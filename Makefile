# TARGET is the executable file
TARGET = app_evs
$(info "=== Start Building $(TARGET) ===")

CURRENT := $(shell pwd)
PROTO_PATH = $(CURRENT)/proto
COMMON_PATH = $(CURRENT)/common
LIBRARY = $(CURRENT)/thirdpart
CAR_PATH = $(CURRENT)/car
DEVICE_PATH = $(CURRENT)/device
MANAGER_PATH = $(CURRENT)/manager
TIMER_PATH = $(CURRENT)/thirdpart/MTimer
JSON_PATH = $(CURRENT)/thirdpart/json

# 自动生成包含路径
DEVICE_SUBDIRS := can adc database gpio meter pmm flash
INCLUDE_DIRS := $(COMMON_PATH) \
                $(PROTO_PATH) \
                $(PROTO_PATH)/include \
                $(CURRENT) \
                $(CAR_PATH) \
                $(DEVICE_PATH) \
                $(MANAGER_PATH) \
                $(TIMER_PATH) \
                $(addprefix $(DEVICE_PATH)/,$(DEVICE_SUBDIRS)) \
                $(LIBRARY)/Sqlite/_sqlite3_install/include \
                $(JSON_PATH)/include

INCLUDE := $(addprefix -I,$(INCLUDE_DIRS))

GIT_DES = $(shell git describe --always)
GIT_TAG = $(shell git describe --tags)
GIT_BRC = $(shell git branch --show)
GIT_VER_INFO = $(GIT_TAG)-$(GIT_DES)-$(GIT_BRC)
CFLAGS += -DGIT_VERSION=\"$(GIT_VER_INFO)\"

SOURCES := $(wildcard \
    ./*.cc \
    manager/*.cc \
    proto/*.cc \
    device/*/*.cc \
    common/*.cc \
    car/*.cc \
    thirdpart/MTimer/*.cc)

OBJECTS = $(patsubst %.cc, build/%.o, $(SOURCES))

# 编译器配置集中管理
define set_toolchain
    CC = $(1)-g++
    AR = $(1)-ar
    STRIP = $(1)-strip
endef

# 通用编译标志
COMMON_FLAGS := -Wall -O2 -std=c++11 -pthread -lrt -g
ARCH_FLAGS = $(if $(filter A7,$(arch)),-mthumb-interwork -mthumb -static,)

# 架构特定配置
ifeq ($(arch),A7)
    TOOLCHAIN_PREFIX = arm-buildroot-linux-gnueabihf
    TOOLCHAIN_PATH = /home/<USER>/work/samaworks/buildroot-at91/output/host/bin
    CC = $(TOOLCHAIN_PATH)/$(TOOLCHAIN_PREFIX)-g++
    AR = $(TOOLCHAIN_PATH)/$(TOOLCHAIN_PREFIX)-ar
    STRIP = $(TOOLCHAIN_PATH)/$(TOOLCHAIN_PREFIX)-strip
    CFLAGS += $(COMMON_FLAGS) $(ARCH_FLAGS)
else ifeq ($(arch),T113)
    TOOLCHAIN_PREFIX = arm-linux-gnueabihf
    TOOLCHAIN_PATH = /home/<USER>/work/t113/host/bin
    CC = $(TOOLCHAIN_PATH)/$(TOOLCHAIN_PREFIX)-g++
    AR = $(TOOLCHAIN_PATH)/$(TOOLCHAIN_PREFIX)-ar
    STRIP = $(TOOLCHAIN_PATH)/$(TOOLCHAIN_PREFIX)-strip
    CFLAGS += $(COMMON_FLAGS) -Wl,-Bdynamic
    LIBS += -L$(PROTO_PATH)/lib -lprotobuf -lprotobuf-lite -lprotoc
    LIBS += -L$(LIBRARY)/Sqlite/_sqlite3_install/lib -lsqlite3
endif

# 使用多核编译加速
CORES := $(shell nproc)
MAKEFLAGS += -j$(CORES)

all: $(TARGET)

$(TARGET): $(OBJECTS)
	@echo "Target: $(TARGET)"
	@echo "Compiling..."
	@mkdir -p out
	@$(CC) $(CFLAGS) -o $(TARGET) $(OBJECTS) $(LIBS)
	@mv $(TARGET) out/
	@echo "Compilation Complete."

build/%.o: %.cc
	@mkdir -p $(dir $@)
	@$(CC) $(CFLAGS) $(INCLUDE) -c $< -o $@

# strip 目标
strip: $(TARGET)
	@echo "$(STRIP) $(TARGET)..."
	@$(STRIP) out/$(TARGET)
	@echo "Strip Complete."

.PHONY: clean
clean:
	@rm -rf $(OBJECTS) out
	# @find . -name '*.o' ! -path './build/*' -delete
	@echo "Clean Complete."
