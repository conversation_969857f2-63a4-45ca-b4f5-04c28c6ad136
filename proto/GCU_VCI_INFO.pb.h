// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GCU_VCI_INFO.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_GCU_5fVCI_5fINFO_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_GCU_5fVCI_5fINFO_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3017000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3017003 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "GCU_AllFaultEnum.pb.h"
#include "GCU_BMS_INFO.pb.h"
#include "GCU_DMC_INFO.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_GCU_5fVCI_5fINFO_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_GCU_5fVCI_5fINFO_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[6]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GCU_5fVCI_5fINFO_2eproto;
namespace VCIinfo {
class ChargerRevive;
struct ChargerReviveDefaultTypeInternal;
extern ChargerReviveDefaultTypeInternal _ChargerRevive_default_instance_;
class ConnectStageSysCtrl;
struct ConnectStageSysCtrlDefaultTypeInternal;
extern ConnectStageSysCtrlDefaultTypeInternal _ConnectStageSysCtrl_default_instance_;
class ContactorState;
struct ContactorStateDefaultTypeInternal;
extern ContactorStateDefaultTypeInternal _ContactorState_default_instance_;
class CurrModAllocState;
struct CurrModAllocStateDefaultTypeInternal;
extern CurrModAllocStateDefaultTypeInternal _CurrModAllocState_default_instance_;
class GunConnectState;
struct GunConnectStateDefaultTypeInternal;
extern GunConnectStateDefaultTypeInternal _GunConnectState_default_instance_;
class VCIsendBMSDemand;
struct VCIsendBMSDemandDefaultTypeInternal;
extern VCIsendBMSDemandDefaultTypeInternal _VCIsendBMSDemand_default_instance_;
}  // namespace VCIinfo
PROTOBUF_NAMESPACE_OPEN
template<> ::VCIinfo::ChargerRevive* Arena::CreateMaybeMessage<::VCIinfo::ChargerRevive>(Arena*);
template<> ::VCIinfo::ConnectStageSysCtrl* Arena::CreateMaybeMessage<::VCIinfo::ConnectStageSysCtrl>(Arena*);
template<> ::VCIinfo::ContactorState* Arena::CreateMaybeMessage<::VCIinfo::ContactorState>(Arena*);
template<> ::VCIinfo::CurrModAllocState* Arena::CreateMaybeMessage<::VCIinfo::CurrModAllocState>(Arena*);
template<> ::VCIinfo::GunConnectState* Arena::CreateMaybeMessage<::VCIinfo::GunConnectState>(Arena*);
template<> ::VCIinfo::VCIsendBMSDemand* Arena::CreateMaybeMessage<::VCIinfo::VCIsendBMSDemand>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace VCIinfo {

enum ContactorStateEnum : int {
  UnKown = 0,
  DriveFailure = 1,
  ContactorAdhesion = 2,
  ContactorReady = 5,
  ContactorCharging = 6,
  Aggregation = 7,
  ContactorStateEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  ContactorStateEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool ContactorStateEnum_IsValid(int value);
constexpr ContactorStateEnum ContactorStateEnum_MIN = UnKown;
constexpr ContactorStateEnum ContactorStateEnum_MAX = Aggregation;
constexpr int ContactorStateEnum_ARRAYSIZE = ContactorStateEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ContactorStateEnum_descriptor();
template<typename T>
inline const std::string& ContactorStateEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ContactorStateEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ContactorStateEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ContactorStateEnum_descriptor(), enum_t_value);
}
inline bool ContactorStateEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ContactorStateEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ContactorStateEnum>(
    ContactorStateEnum_descriptor(), name, value);
}
enum gunMatchState : int {
  UnKownState = 0,
  gunMatchStart = 1,
  OutputVoltOk = 2,
  VoltSampledOk = 3,
  gunMatchOk = 4,
  gunMatchFail = 5,
  gunMatchTimeout = 6,
  gunMatchEndOk = 7,
  gunMatchEndFail = 8,
  gunMatchState_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  gunMatchState_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool gunMatchState_IsValid(int value);
constexpr gunMatchState gunMatchState_MIN = UnKownState;
constexpr gunMatchState gunMatchState_MAX = gunMatchEndFail;
constexpr int gunMatchState_ARRAYSIZE = gunMatchState_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* gunMatchState_descriptor();
template<typename T>
inline const std::string& gunMatchState_Name(T enum_t_value) {
  static_assert(::std::is_same<T, gunMatchState>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function gunMatchState_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    gunMatchState_descriptor(), enum_t_value);
}
inline bool gunMatchState_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, gunMatchState* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<gunMatchState>(
    gunMatchState_descriptor(), name, value);
}
// ===================================================================

class GunConnectState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:VCIinfo.GunConnectState) */ {
 public:
  inline GunConnectState() : GunConnectState(nullptr) {}
  ~GunConnectState() override;
  explicit constexpr GunConnectState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GunConnectState(const GunConnectState& from);
  GunConnectState(GunConnectState&& from) noexcept
    : GunConnectState() {
    *this = ::std::move(from);
  }

  inline GunConnectState& operator=(const GunConnectState& from) {
    CopyFrom(from);
    return *this;
  }
  inline GunConnectState& operator=(GunConnectState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GunConnectState& default_instance() {
    return *internal_default_instance();
  }
  static inline const GunConnectState* internal_default_instance() {
    return reinterpret_cast<const GunConnectState*>(
               &_GunConnectState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GunConnectState& a, GunConnectState& b) {
    a.Swap(&b);
  }
  inline void Swap(GunConnectState* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GunConnectState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GunConnectState* New() const final {
    return new GunConnectState();
  }

  GunConnectState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GunConnectState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GunConnectState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GunConnectState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GunConnectState* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "VCIinfo.GunConnectState";
  }
  protected:
  explicit GunConnectState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTerminalIDFieldNumber = 1,
    kGunIDFieldNumber = 2,
    kAuxDriverFieldNumber = 3,
    kAuxFeedbackFieldNumber = 4,
    kELockDriverFieldNumber = 5,
    kELockFeedbackFieldNumber = 6,
    kAuxRtTypeFieldNumber = 7,
    kTempPositiveFieldNumber = 8,
    kTempNegativeFieldNumber = 9,
    kElockModeFieldNumber = 10,
    kPositionedStateFieldNumber = 11,
    kDualStateFieldNumber = 12,
    kLinkStateFieldNumber = 13,
    kGbtProTypeFieldNumber = 14,
  };
  // uint32 terminalID = 1;
  void clear_terminalid();
  ::PROTOBUF_NAMESPACE_ID::uint32 terminalid() const;
  void set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_terminalid() const;
  void _internal_set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 gunID = 2;
  void clear_gunid();
  ::PROTOBUF_NAMESPACE_ID::uint32 gunid() const;
  void set_gunid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_gunid() const;
  void _internal_set_gunid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 auxDriver = 3;
  void clear_auxdriver();
  ::PROTOBUF_NAMESPACE_ID::uint32 auxdriver() const;
  void set_auxdriver(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_auxdriver() const;
  void _internal_set_auxdriver(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 auxFeedback = 4;
  void clear_auxfeedback();
  ::PROTOBUF_NAMESPACE_ID::uint32 auxfeedback() const;
  void set_auxfeedback(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_auxfeedback() const;
  void _internal_set_auxfeedback(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 eLockDriver = 5;
  void clear_elockdriver();
  ::PROTOBUF_NAMESPACE_ID::uint32 elockdriver() const;
  void set_elockdriver(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_elockdriver() const;
  void _internal_set_elockdriver(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 eLockFeedback = 6;
  void clear_elockfeedback();
  ::PROTOBUF_NAMESPACE_ID::uint32 elockfeedback() const;
  void set_elockfeedback(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_elockfeedback() const;
  void _internal_set_elockfeedback(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 auxRtType = 7;
  void clear_auxrttype();
  ::PROTOBUF_NAMESPACE_ID::uint32 auxrttype() const;
  void set_auxrttype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_auxrttype() const;
  void _internal_set_auxrttype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // float tempPositive = 8;
  void clear_temppositive();
  float temppositive() const;
  void set_temppositive(float value);
  private:
  float _internal_temppositive() const;
  void _internal_set_temppositive(float value);
  public:

  // float tempNegative = 9;
  void clear_tempnegative();
  float tempnegative() const;
  void set_tempnegative(float value);
  private:
  float _internal_tempnegative() const;
  void _internal_set_tempnegative(float value);
  public:

  // uint32 ElockMode = 10;
  void clear_elockmode();
  ::PROTOBUF_NAMESPACE_ID::uint32 elockmode() const;
  void set_elockmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_elockmode() const;
  void _internal_set_elockmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 positionedState = 11;
  void clear_positionedstate();
  ::PROTOBUF_NAMESPACE_ID::uint32 positionedstate() const;
  void set_positionedstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_positionedstate() const;
  void _internal_set_positionedstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 dualState = 12;
  void clear_dualstate();
  ::PROTOBUF_NAMESPACE_ID::uint32 dualstate() const;
  void set_dualstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_dualstate() const;
  void _internal_set_dualstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 linkState = 13;
  void clear_linkstate();
  ::PROTOBUF_NAMESPACE_ID::uint32 linkstate() const;
  void set_linkstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_linkstate() const;
  void _internal_set_linkstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // .DMCinfo.GunProEnum GbtProType = 14;
  void clear_gbtprotype();
  ::DMCinfo::GunProEnum gbtprotype() const;
  void set_gbtprotype(::DMCinfo::GunProEnum value);
  private:
  ::DMCinfo::GunProEnum _internal_gbtprotype() const;
  void _internal_set_gbtprotype(::DMCinfo::GunProEnum value);
  public:

  // @@protoc_insertion_point(class_scope:VCIinfo.GunConnectState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 terminalid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 gunid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 auxdriver_;
  ::PROTOBUF_NAMESPACE_ID::uint32 auxfeedback_;
  ::PROTOBUF_NAMESPACE_ID::uint32 elockdriver_;
  ::PROTOBUF_NAMESPACE_ID::uint32 elockfeedback_;
  ::PROTOBUF_NAMESPACE_ID::uint32 auxrttype_;
  float temppositive_;
  float tempnegative_;
  ::PROTOBUF_NAMESPACE_ID::uint32 elockmode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 positionedstate_;
  ::PROTOBUF_NAMESPACE_ID::uint32 dualstate_;
  ::PROTOBUF_NAMESPACE_ID::uint32 linkstate_;
  int gbtprotype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fVCI_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class ConnectStageSysCtrl final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:VCIinfo.ConnectStageSysCtrl) */ {
 public:
  inline ConnectStageSysCtrl() : ConnectStageSysCtrl(nullptr) {}
  ~ConnectStageSysCtrl() override;
  explicit constexpr ConnectStageSysCtrl(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ConnectStageSysCtrl(const ConnectStageSysCtrl& from);
  ConnectStageSysCtrl(ConnectStageSysCtrl&& from) noexcept
    : ConnectStageSysCtrl() {
    *this = ::std::move(from);
  }

  inline ConnectStageSysCtrl& operator=(const ConnectStageSysCtrl& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConnectStageSysCtrl& operator=(ConnectStageSysCtrl&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ConnectStageSysCtrl& default_instance() {
    return *internal_default_instance();
  }
  static inline const ConnectStageSysCtrl* internal_default_instance() {
    return reinterpret_cast<const ConnectStageSysCtrl*>(
               &_ConnectStageSysCtrl_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ConnectStageSysCtrl& a, ConnectStageSysCtrl& b) {
    a.Swap(&b);
  }
  inline void Swap(ConnectStageSysCtrl* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ConnectStageSysCtrl* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ConnectStageSysCtrl* New() const final {
    return new ConnectStageSysCtrl();
  }

  ConnectStageSysCtrl* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ConnectStageSysCtrl>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ConnectStageSysCtrl& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ConnectStageSysCtrl& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConnectStageSysCtrl* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "VCIinfo.ConnectStageSysCtrl";
  }
  protected:
  explicit ConnectStageSysCtrl(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTerminalIDFieldNumber = 1,
    kGunIDFieldNumber = 2,
    kElockCmdFieldNumber = 3,
    kStartTypeFieldNumber = 4,
    kAuxTypeFieldNumber = 5,
    kElockModeFieldNumber = 6,
  };
  // uint32 terminalID = 1;
  void clear_terminalid();
  ::PROTOBUF_NAMESPACE_ID::uint32 terminalid() const;
  void set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_terminalid() const;
  void _internal_set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 gunID = 2;
  void clear_gunid();
  ::PROTOBUF_NAMESPACE_ID::uint32 gunid() const;
  void set_gunid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_gunid() const;
  void _internal_set_gunid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 elockCmd = 3;
  void clear_elockcmd();
  ::PROTOBUF_NAMESPACE_ID::uint32 elockcmd() const;
  void set_elockcmd(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_elockcmd() const;
  void _internal_set_elockcmd(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 startType = 4;
  void clear_starttype();
  ::PROTOBUF_NAMESPACE_ID::uint32 starttype() const;
  void set_starttype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_starttype() const;
  void _internal_set_starttype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 AuxType = 5;
  void clear_auxtype();
  ::PROTOBUF_NAMESPACE_ID::uint32 auxtype() const;
  void set_auxtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_auxtype() const;
  void _internal_set_auxtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 ElockMode = 6;
  void clear_elockmode();
  ::PROTOBUF_NAMESPACE_ID::uint32 elockmode() const;
  void set_elockmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_elockmode() const;
  void _internal_set_elockmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:VCIinfo.ConnectStageSysCtrl)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 terminalid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 gunid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 elockcmd_;
  ::PROTOBUF_NAMESPACE_ID::uint32 starttype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 auxtype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 elockmode_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fVCI_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class VCIsendBMSDemand final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:VCIinfo.VCIsendBMSDemand) */ {
 public:
  inline VCIsendBMSDemand() : VCIsendBMSDemand(nullptr) {}
  ~VCIsendBMSDemand() override;
  explicit constexpr VCIsendBMSDemand(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  VCIsendBMSDemand(const VCIsendBMSDemand& from);
  VCIsendBMSDemand(VCIsendBMSDemand&& from) noexcept
    : VCIsendBMSDemand() {
    *this = ::std::move(from);
  }

  inline VCIsendBMSDemand& operator=(const VCIsendBMSDemand& from) {
    CopyFrom(from);
    return *this;
  }
  inline VCIsendBMSDemand& operator=(VCIsendBMSDemand&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const VCIsendBMSDemand& default_instance() {
    return *internal_default_instance();
  }
  static inline const VCIsendBMSDemand* internal_default_instance() {
    return reinterpret_cast<const VCIsendBMSDemand*>(
               &_VCIsendBMSDemand_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(VCIsendBMSDemand& a, VCIsendBMSDemand& b) {
    a.Swap(&b);
  }
  inline void Swap(VCIsendBMSDemand* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VCIsendBMSDemand* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline VCIsendBMSDemand* New() const final {
    return new VCIsendBMSDemand();
  }

  VCIsendBMSDemand* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<VCIsendBMSDemand>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const VCIsendBMSDemand& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const VCIsendBMSDemand& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VCIsendBMSDemand* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "VCIinfo.VCIsendBMSDemand";
  }
  protected:
  explicit VCIsendBMSDemand(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kVDemandFieldNumber = 1,
    kIDemandFieldNumber = 2,
    kVPTPDemandFieldNumber = 3,
    kIPTPDemandFieldNumber = 4,
    kBatVoltageFieldNumber = 5,
    kModVoltageFieldNumber = 6,
  };
  // float VDemand = 1;
  void clear_vdemand();
  float vdemand() const;
  void set_vdemand(float value);
  private:
  float _internal_vdemand() const;
  void _internal_set_vdemand(float value);
  public:

  // float IDemand = 2;
  void clear_idemand();
  float idemand() const;
  void set_idemand(float value);
  private:
  float _internal_idemand() const;
  void _internal_set_idemand(float value);
  public:

  // float VPTPDemand = 3;
  void clear_vptpdemand();
  float vptpdemand() const;
  void set_vptpdemand(float value);
  private:
  float _internal_vptpdemand() const;
  void _internal_set_vptpdemand(float value);
  public:

  // float IPTPDemand = 4;
  void clear_iptpdemand();
  float iptpdemand() const;
  void set_iptpdemand(float value);
  private:
  float _internal_iptpdemand() const;
  void _internal_set_iptpdemand(float value);
  public:

  // float batVoltage = 5;
  void clear_batvoltage();
  float batvoltage() const;
  void set_batvoltage(float value);
  private:
  float _internal_batvoltage() const;
  void _internal_set_batvoltage(float value);
  public:

  // float modVoltage = 6;
  void clear_modvoltage();
  float modvoltage() const;
  void set_modvoltage(float value);
  private:
  float _internal_modvoltage() const;
  void _internal_set_modvoltage(float value);
  public:

  // @@protoc_insertion_point(class_scope:VCIinfo.VCIsendBMSDemand)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float vdemand_;
  float idemand_;
  float vptpdemand_;
  float iptpdemand_;
  float batvoltage_;
  float modvoltage_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fVCI_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class ChargerRevive final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:VCIinfo.ChargerRevive) */ {
 public:
  inline ChargerRevive() : ChargerRevive(nullptr) {}
  ~ChargerRevive() override;
  explicit constexpr ChargerRevive(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ChargerRevive(const ChargerRevive& from);
  ChargerRevive(ChargerRevive&& from) noexcept
    : ChargerRevive() {
    *this = ::std::move(from);
  }

  inline ChargerRevive& operator=(const ChargerRevive& from) {
    CopyFrom(from);
    return *this;
  }
  inline ChargerRevive& operator=(ChargerRevive&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ChargerRevive& default_instance() {
    return *internal_default_instance();
  }
  static inline const ChargerRevive* internal_default_instance() {
    return reinterpret_cast<const ChargerRevive*>(
               &_ChargerRevive_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(ChargerRevive& a, ChargerRevive& b) {
    a.Swap(&b);
  }
  inline void Swap(ChargerRevive* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ChargerRevive* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ChargerRevive* New() const final {
    return new ChargerRevive();
  }

  ChargerRevive* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ChargerRevive>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ChargerRevive& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ChargerRevive& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ChargerRevive* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "VCIinfo.ChargerRevive";
  }
  protected:
  explicit ChargerRevive(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIsChargingFieldNumber = 1,
    kIsVINStartFieldNumber = 2,
    kFaultState1FieldNumber = 3,
    kFaultState2FieldNumber = 4,
    kFaultState3FieldNumber = 5,
    kBmsCommStateFieldNumber = 6,
    kBmsRecvStateFieldNumber = 7,
    kBmsTypeFieldNumber = 8,
    kBmsTimeoutCntFieldNumber = 9,
    kElockStateFieldNumber = 10,
    kAuxPowerStateFieldNumber = 11,
    kInsultStateFieldNumber = 12,
    kInsultResultFieldNumber = 13,
    kBmsCurrentMaxFieldNumber = 14,
    kBmsVoltageMaxFieldNumber = 15,
    kCellVoltageMaxFieldNumber = 16,
    kCellTempMaxFieldNumber = 17,
    kInsultVoltageFieldNumber = 18,
  };
  // uint32 isCharging = 1;
  void clear_ischarging();
  ::PROTOBUF_NAMESPACE_ID::uint32 ischarging() const;
  void set_ischarging(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_ischarging() const;
  void _internal_set_ischarging(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 isVINStart = 2;
  void clear_isvinstart();
  ::PROTOBUF_NAMESPACE_ID::uint32 isvinstart() const;
  void set_isvinstart(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_isvinstart() const;
  void _internal_set_isvinstart(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 faultState1 = 3;
  void clear_faultstate1();
  ::PROTOBUF_NAMESPACE_ID::uint32 faultstate1() const;
  void set_faultstate1(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_faultstate1() const;
  void _internal_set_faultstate1(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 faultState2 = 4;
  void clear_faultstate2();
  ::PROTOBUF_NAMESPACE_ID::uint32 faultstate2() const;
  void set_faultstate2(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_faultstate2() const;
  void _internal_set_faultstate2(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 faultState3 = 5;
  void clear_faultstate3();
  ::PROTOBUF_NAMESPACE_ID::uint32 faultstate3() const;
  void set_faultstate3(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_faultstate3() const;
  void _internal_set_faultstate3(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 bmsCommState = 6;
  void clear_bmscommstate();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmscommstate() const;
  void set_bmscommstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmscommstate() const;
  void _internal_set_bmscommstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 bmsRecvState = 7;
  void clear_bmsrecvstate();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsrecvstate() const;
  void set_bmsrecvstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsrecvstate() const;
  void _internal_set_bmsrecvstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 bmsType = 8;
  void clear_bmstype();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmstype() const;
  void set_bmstype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmstype() const;
  void _internal_set_bmstype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 bmsTimeoutCnt = 9;
  void clear_bmstimeoutcnt();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmstimeoutcnt() const;
  void set_bmstimeoutcnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmstimeoutcnt() const;
  void _internal_set_bmstimeoutcnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 elockState = 10;
  void clear_elockstate();
  ::PROTOBUF_NAMESPACE_ID::uint32 elockstate() const;
  void set_elockstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_elockstate() const;
  void _internal_set_elockstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 auxPowerState = 11;
  void clear_auxpowerstate();
  ::PROTOBUF_NAMESPACE_ID::uint32 auxpowerstate() const;
  void set_auxpowerstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_auxpowerstate() const;
  void _internal_set_auxpowerstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 insultState = 12;
  void clear_insultstate();
  ::PROTOBUF_NAMESPACE_ID::uint32 insultstate() const;
  void set_insultstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_insultstate() const;
  void _internal_set_insultstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 insultResult = 13;
  void clear_insultresult();
  ::PROTOBUF_NAMESPACE_ID::uint32 insultresult() const;
  void set_insultresult(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_insultresult() const;
  void _internal_set_insultresult(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // float bmsCurrentMax = 14;
  void clear_bmscurrentmax();
  float bmscurrentmax() const;
  void set_bmscurrentmax(float value);
  private:
  float _internal_bmscurrentmax() const;
  void _internal_set_bmscurrentmax(float value);
  public:

  // float bmsVoltageMax = 15;
  void clear_bmsvoltagemax();
  float bmsvoltagemax() const;
  void set_bmsvoltagemax(float value);
  private:
  float _internal_bmsvoltagemax() const;
  void _internal_set_bmsvoltagemax(float value);
  public:

  // float cellVoltageMax = 16;
  void clear_cellvoltagemax();
  float cellvoltagemax() const;
  void set_cellvoltagemax(float value);
  private:
  float _internal_cellvoltagemax() const;
  void _internal_set_cellvoltagemax(float value);
  public:

  // float cellTempMax = 17;
  void clear_celltempmax();
  float celltempmax() const;
  void set_celltempmax(float value);
  private:
  float _internal_celltempmax() const;
  void _internal_set_celltempmax(float value);
  public:

  // float insultVoltage = 18;
  void clear_insultvoltage();
  float insultvoltage() const;
  void set_insultvoltage(float value);
  private:
  float _internal_insultvoltage() const;
  void _internal_set_insultvoltage(float value);
  public:

  // @@protoc_insertion_point(class_scope:VCIinfo.ChargerRevive)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 ischarging_;
  ::PROTOBUF_NAMESPACE_ID::uint32 isvinstart_;
  ::PROTOBUF_NAMESPACE_ID::uint32 faultstate1_;
  ::PROTOBUF_NAMESPACE_ID::uint32 faultstate2_;
  ::PROTOBUF_NAMESPACE_ID::uint32 faultstate3_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmscommstate_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsrecvstate_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmstype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmstimeoutcnt_;
  ::PROTOBUF_NAMESPACE_ID::uint32 elockstate_;
  ::PROTOBUF_NAMESPACE_ID::uint32 auxpowerstate_;
  ::PROTOBUF_NAMESPACE_ID::uint32 insultstate_;
  ::PROTOBUF_NAMESPACE_ID::uint32 insultresult_;
  float bmscurrentmax_;
  float bmsvoltagemax_;
  float cellvoltagemax_;
  float celltempmax_;
  float insultvoltage_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fVCI_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class ContactorState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:VCIinfo.ContactorState) */ {
 public:
  inline ContactorState() : ContactorState(nullptr) {}
  ~ContactorState() override;
  explicit constexpr ContactorState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ContactorState(const ContactorState& from);
  ContactorState(ContactorState&& from) noexcept
    : ContactorState() {
    *this = ::std::move(from);
  }

  inline ContactorState& operator=(const ContactorState& from) {
    CopyFrom(from);
    return *this;
  }
  inline ContactorState& operator=(ContactorState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ContactorState& default_instance() {
    return *internal_default_instance();
  }
  static inline const ContactorState* internal_default_instance() {
    return reinterpret_cast<const ContactorState*>(
               &_ContactorState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(ContactorState& a, ContactorState& b) {
    a.Swap(&b);
  }
  inline void Swap(ContactorState* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ContactorState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ContactorState* New() const final {
    return new ContactorState();
  }

  ContactorState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ContactorState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ContactorState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ContactorState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ContactorState* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "VCIinfo.ContactorState";
  }
  protected:
  explicit ContactorState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGunNumFieldNumber = 1,
    kContactorStateFieldNumber = 2,
  };
  // uint32 gunNum = 1;
  void clear_gunnum();
  ::PROTOBUF_NAMESPACE_ID::uint32 gunnum() const;
  void set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_gunnum() const;
  void _internal_set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // .VCIinfo.ContactorStateEnum ContactorState = 2;
  void clear_contactorstate();
  ::VCIinfo::ContactorStateEnum contactorstate() const;
  void set_contactorstate(::VCIinfo::ContactorStateEnum value);
  private:
  ::VCIinfo::ContactorStateEnum _internal_contactorstate() const;
  void _internal_set_contactorstate(::VCIinfo::ContactorStateEnum value);
  public:

  // @@protoc_insertion_point(class_scope:VCIinfo.ContactorState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 gunnum_;
  int contactorstate_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fVCI_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class CurrModAllocState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:VCIinfo.CurrModAllocState) */ {
 public:
  inline CurrModAllocState() : CurrModAllocState(nullptr) {}
  ~CurrModAllocState() override;
  explicit constexpr CurrModAllocState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CurrModAllocState(const CurrModAllocState& from);
  CurrModAllocState(CurrModAllocState&& from) noexcept
    : CurrModAllocState() {
    *this = ::std::move(from);
  }

  inline CurrModAllocState& operator=(const CurrModAllocState& from) {
    CopyFrom(from);
    return *this;
  }
  inline CurrModAllocState& operator=(CurrModAllocState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CurrModAllocState& default_instance() {
    return *internal_default_instance();
  }
  static inline const CurrModAllocState* internal_default_instance() {
    return reinterpret_cast<const CurrModAllocState*>(
               &_CurrModAllocState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(CurrModAllocState& a, CurrModAllocState& b) {
    a.Swap(&b);
  }
  inline void Swap(CurrModAllocState* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CurrModAllocState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CurrModAllocState* New() const final {
    return new CurrModAllocState();
  }

  CurrModAllocState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CurrModAllocState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CurrModAllocState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CurrModAllocState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CurrModAllocState* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "VCIinfo.CurrModAllocState";
  }
  protected:
  explicit CurrModAllocState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGunNumFieldNumber = 1,
    kMainSizeFieldNumber = 2,
  };
  // uint32 gunNum = 1;
  void clear_gunnum();
  ::PROTOBUF_NAMESPACE_ID::uint32 gunnum() const;
  void set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_gunnum() const;
  void _internal_set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 MainSize = 2;
  void clear_mainsize();
  ::PROTOBUF_NAMESPACE_ID::uint32 mainsize() const;
  void set_mainsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_mainsize() const;
  void _internal_set_mainsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:VCIinfo.CurrModAllocState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 gunnum_;
  ::PROTOBUF_NAMESPACE_ID::uint32 mainsize_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fVCI_5fINFO_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GunConnectState

// uint32 terminalID = 1;
inline void GunConnectState::clear_terminalid() {
  terminalid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunConnectState::_internal_terminalid() const {
  return terminalid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunConnectState::terminalid() const {
  // @@protoc_insertion_point(field_get:VCIinfo.GunConnectState.terminalID)
  return _internal_terminalid();
}
inline void GunConnectState::_internal_set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  terminalid_ = value;
}
inline void GunConnectState::set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_terminalid(value);
  // @@protoc_insertion_point(field_set:VCIinfo.GunConnectState.terminalID)
}

// uint32 gunID = 2;
inline void GunConnectState::clear_gunid() {
  gunid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunConnectState::_internal_gunid() const {
  return gunid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunConnectState::gunid() const {
  // @@protoc_insertion_point(field_get:VCIinfo.GunConnectState.gunID)
  return _internal_gunid();
}
inline void GunConnectState::_internal_set_gunid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  gunid_ = value;
}
inline void GunConnectState::set_gunid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_gunid(value);
  // @@protoc_insertion_point(field_set:VCIinfo.GunConnectState.gunID)
}

// uint32 auxDriver = 3;
inline void GunConnectState::clear_auxdriver() {
  auxdriver_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunConnectState::_internal_auxdriver() const {
  return auxdriver_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunConnectState::auxdriver() const {
  // @@protoc_insertion_point(field_get:VCIinfo.GunConnectState.auxDriver)
  return _internal_auxdriver();
}
inline void GunConnectState::_internal_set_auxdriver(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  auxdriver_ = value;
}
inline void GunConnectState::set_auxdriver(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_auxdriver(value);
  // @@protoc_insertion_point(field_set:VCIinfo.GunConnectState.auxDriver)
}

// uint32 auxFeedback = 4;
inline void GunConnectState::clear_auxfeedback() {
  auxfeedback_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunConnectState::_internal_auxfeedback() const {
  return auxfeedback_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunConnectState::auxfeedback() const {
  // @@protoc_insertion_point(field_get:VCIinfo.GunConnectState.auxFeedback)
  return _internal_auxfeedback();
}
inline void GunConnectState::_internal_set_auxfeedback(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  auxfeedback_ = value;
}
inline void GunConnectState::set_auxfeedback(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_auxfeedback(value);
  // @@protoc_insertion_point(field_set:VCIinfo.GunConnectState.auxFeedback)
}

// uint32 eLockDriver = 5;
inline void GunConnectState::clear_elockdriver() {
  elockdriver_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunConnectState::_internal_elockdriver() const {
  return elockdriver_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunConnectState::elockdriver() const {
  // @@protoc_insertion_point(field_get:VCIinfo.GunConnectState.eLockDriver)
  return _internal_elockdriver();
}
inline void GunConnectState::_internal_set_elockdriver(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  elockdriver_ = value;
}
inline void GunConnectState::set_elockdriver(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_elockdriver(value);
  // @@protoc_insertion_point(field_set:VCIinfo.GunConnectState.eLockDriver)
}

// uint32 eLockFeedback = 6;
inline void GunConnectState::clear_elockfeedback() {
  elockfeedback_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunConnectState::_internal_elockfeedback() const {
  return elockfeedback_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunConnectState::elockfeedback() const {
  // @@protoc_insertion_point(field_get:VCIinfo.GunConnectState.eLockFeedback)
  return _internal_elockfeedback();
}
inline void GunConnectState::_internal_set_elockfeedback(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  elockfeedback_ = value;
}
inline void GunConnectState::set_elockfeedback(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_elockfeedback(value);
  // @@protoc_insertion_point(field_set:VCIinfo.GunConnectState.eLockFeedback)
}

// uint32 auxRtType = 7;
inline void GunConnectState::clear_auxrttype() {
  auxrttype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunConnectState::_internal_auxrttype() const {
  return auxrttype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunConnectState::auxrttype() const {
  // @@protoc_insertion_point(field_get:VCIinfo.GunConnectState.auxRtType)
  return _internal_auxrttype();
}
inline void GunConnectState::_internal_set_auxrttype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  auxrttype_ = value;
}
inline void GunConnectState::set_auxrttype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_auxrttype(value);
  // @@protoc_insertion_point(field_set:VCIinfo.GunConnectState.auxRtType)
}

// float tempPositive = 8;
inline void GunConnectState::clear_temppositive() {
  temppositive_ = 0;
}
inline float GunConnectState::_internal_temppositive() const {
  return temppositive_;
}
inline float GunConnectState::temppositive() const {
  // @@protoc_insertion_point(field_get:VCIinfo.GunConnectState.tempPositive)
  return _internal_temppositive();
}
inline void GunConnectState::_internal_set_temppositive(float value) {
  
  temppositive_ = value;
}
inline void GunConnectState::set_temppositive(float value) {
  _internal_set_temppositive(value);
  // @@protoc_insertion_point(field_set:VCIinfo.GunConnectState.tempPositive)
}

// float tempNegative = 9;
inline void GunConnectState::clear_tempnegative() {
  tempnegative_ = 0;
}
inline float GunConnectState::_internal_tempnegative() const {
  return tempnegative_;
}
inline float GunConnectState::tempnegative() const {
  // @@protoc_insertion_point(field_get:VCIinfo.GunConnectState.tempNegative)
  return _internal_tempnegative();
}
inline void GunConnectState::_internal_set_tempnegative(float value) {
  
  tempnegative_ = value;
}
inline void GunConnectState::set_tempnegative(float value) {
  _internal_set_tempnegative(value);
  // @@protoc_insertion_point(field_set:VCIinfo.GunConnectState.tempNegative)
}

// uint32 ElockMode = 10;
inline void GunConnectState::clear_elockmode() {
  elockmode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunConnectState::_internal_elockmode() const {
  return elockmode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunConnectState::elockmode() const {
  // @@protoc_insertion_point(field_get:VCIinfo.GunConnectState.ElockMode)
  return _internal_elockmode();
}
inline void GunConnectState::_internal_set_elockmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  elockmode_ = value;
}
inline void GunConnectState::set_elockmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_elockmode(value);
  // @@protoc_insertion_point(field_set:VCIinfo.GunConnectState.ElockMode)
}

// uint32 positionedState = 11;
inline void GunConnectState::clear_positionedstate() {
  positionedstate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunConnectState::_internal_positionedstate() const {
  return positionedstate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunConnectState::positionedstate() const {
  // @@protoc_insertion_point(field_get:VCIinfo.GunConnectState.positionedState)
  return _internal_positionedstate();
}
inline void GunConnectState::_internal_set_positionedstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  positionedstate_ = value;
}
inline void GunConnectState::set_positionedstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_positionedstate(value);
  // @@protoc_insertion_point(field_set:VCIinfo.GunConnectState.positionedState)
}

// uint32 dualState = 12;
inline void GunConnectState::clear_dualstate() {
  dualstate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunConnectState::_internal_dualstate() const {
  return dualstate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunConnectState::dualstate() const {
  // @@protoc_insertion_point(field_get:VCIinfo.GunConnectState.dualState)
  return _internal_dualstate();
}
inline void GunConnectState::_internal_set_dualstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  dualstate_ = value;
}
inline void GunConnectState::set_dualstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_dualstate(value);
  // @@protoc_insertion_point(field_set:VCIinfo.GunConnectState.dualState)
}

// uint32 linkState = 13;
inline void GunConnectState::clear_linkstate() {
  linkstate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunConnectState::_internal_linkstate() const {
  return linkstate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunConnectState::linkstate() const {
  // @@protoc_insertion_point(field_get:VCIinfo.GunConnectState.linkState)
  return _internal_linkstate();
}
inline void GunConnectState::_internal_set_linkstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  linkstate_ = value;
}
inline void GunConnectState::set_linkstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_linkstate(value);
  // @@protoc_insertion_point(field_set:VCIinfo.GunConnectState.linkState)
}

// .DMCinfo.GunProEnum GbtProType = 14;
inline void GunConnectState::clear_gbtprotype() {
  gbtprotype_ = 0;
}
inline ::DMCinfo::GunProEnum GunConnectState::_internal_gbtprotype() const {
  return static_cast< ::DMCinfo::GunProEnum >(gbtprotype_);
}
inline ::DMCinfo::GunProEnum GunConnectState::gbtprotype() const {
  // @@protoc_insertion_point(field_get:VCIinfo.GunConnectState.GbtProType)
  return _internal_gbtprotype();
}
inline void GunConnectState::_internal_set_gbtprotype(::DMCinfo::GunProEnum value) {
  
  gbtprotype_ = value;
}
inline void GunConnectState::set_gbtprotype(::DMCinfo::GunProEnum value) {
  _internal_set_gbtprotype(value);
  // @@protoc_insertion_point(field_set:VCIinfo.GunConnectState.GbtProType)
}

// -------------------------------------------------------------------

// ConnectStageSysCtrl

// uint32 terminalID = 1;
inline void ConnectStageSysCtrl::clear_terminalid() {
  terminalid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ConnectStageSysCtrl::_internal_terminalid() const {
  return terminalid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ConnectStageSysCtrl::terminalid() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ConnectStageSysCtrl.terminalID)
  return _internal_terminalid();
}
inline void ConnectStageSysCtrl::_internal_set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  terminalid_ = value;
}
inline void ConnectStageSysCtrl::set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_terminalid(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ConnectStageSysCtrl.terminalID)
}

// uint32 gunID = 2;
inline void ConnectStageSysCtrl::clear_gunid() {
  gunid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ConnectStageSysCtrl::_internal_gunid() const {
  return gunid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ConnectStageSysCtrl::gunid() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ConnectStageSysCtrl.gunID)
  return _internal_gunid();
}
inline void ConnectStageSysCtrl::_internal_set_gunid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  gunid_ = value;
}
inline void ConnectStageSysCtrl::set_gunid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_gunid(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ConnectStageSysCtrl.gunID)
}

// uint32 elockCmd = 3;
inline void ConnectStageSysCtrl::clear_elockcmd() {
  elockcmd_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ConnectStageSysCtrl::_internal_elockcmd() const {
  return elockcmd_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ConnectStageSysCtrl::elockcmd() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ConnectStageSysCtrl.elockCmd)
  return _internal_elockcmd();
}
inline void ConnectStageSysCtrl::_internal_set_elockcmd(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  elockcmd_ = value;
}
inline void ConnectStageSysCtrl::set_elockcmd(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_elockcmd(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ConnectStageSysCtrl.elockCmd)
}

// uint32 startType = 4;
inline void ConnectStageSysCtrl::clear_starttype() {
  starttype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ConnectStageSysCtrl::_internal_starttype() const {
  return starttype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ConnectStageSysCtrl::starttype() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ConnectStageSysCtrl.startType)
  return _internal_starttype();
}
inline void ConnectStageSysCtrl::_internal_set_starttype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  starttype_ = value;
}
inline void ConnectStageSysCtrl::set_starttype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_starttype(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ConnectStageSysCtrl.startType)
}

// uint32 AuxType = 5;
inline void ConnectStageSysCtrl::clear_auxtype() {
  auxtype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ConnectStageSysCtrl::_internal_auxtype() const {
  return auxtype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ConnectStageSysCtrl::auxtype() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ConnectStageSysCtrl.AuxType)
  return _internal_auxtype();
}
inline void ConnectStageSysCtrl::_internal_set_auxtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  auxtype_ = value;
}
inline void ConnectStageSysCtrl::set_auxtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_auxtype(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ConnectStageSysCtrl.AuxType)
}

// uint32 ElockMode = 6;
inline void ConnectStageSysCtrl::clear_elockmode() {
  elockmode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ConnectStageSysCtrl::_internal_elockmode() const {
  return elockmode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ConnectStageSysCtrl::elockmode() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ConnectStageSysCtrl.ElockMode)
  return _internal_elockmode();
}
inline void ConnectStageSysCtrl::_internal_set_elockmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  elockmode_ = value;
}
inline void ConnectStageSysCtrl::set_elockmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_elockmode(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ConnectStageSysCtrl.ElockMode)
}

// -------------------------------------------------------------------

// VCIsendBMSDemand

// float VDemand = 1;
inline void VCIsendBMSDemand::clear_vdemand() {
  vdemand_ = 0;
}
inline float VCIsendBMSDemand::_internal_vdemand() const {
  return vdemand_;
}
inline float VCIsendBMSDemand::vdemand() const {
  // @@protoc_insertion_point(field_get:VCIinfo.VCIsendBMSDemand.VDemand)
  return _internal_vdemand();
}
inline void VCIsendBMSDemand::_internal_set_vdemand(float value) {
  
  vdemand_ = value;
}
inline void VCIsendBMSDemand::set_vdemand(float value) {
  _internal_set_vdemand(value);
  // @@protoc_insertion_point(field_set:VCIinfo.VCIsendBMSDemand.VDemand)
}

// float IDemand = 2;
inline void VCIsendBMSDemand::clear_idemand() {
  idemand_ = 0;
}
inline float VCIsendBMSDemand::_internal_idemand() const {
  return idemand_;
}
inline float VCIsendBMSDemand::idemand() const {
  // @@protoc_insertion_point(field_get:VCIinfo.VCIsendBMSDemand.IDemand)
  return _internal_idemand();
}
inline void VCIsendBMSDemand::_internal_set_idemand(float value) {
  
  idemand_ = value;
}
inline void VCIsendBMSDemand::set_idemand(float value) {
  _internal_set_idemand(value);
  // @@protoc_insertion_point(field_set:VCIinfo.VCIsendBMSDemand.IDemand)
}

// float VPTPDemand = 3;
inline void VCIsendBMSDemand::clear_vptpdemand() {
  vptpdemand_ = 0;
}
inline float VCIsendBMSDemand::_internal_vptpdemand() const {
  return vptpdemand_;
}
inline float VCIsendBMSDemand::vptpdemand() const {
  // @@protoc_insertion_point(field_get:VCIinfo.VCIsendBMSDemand.VPTPDemand)
  return _internal_vptpdemand();
}
inline void VCIsendBMSDemand::_internal_set_vptpdemand(float value) {
  
  vptpdemand_ = value;
}
inline void VCIsendBMSDemand::set_vptpdemand(float value) {
  _internal_set_vptpdemand(value);
  // @@protoc_insertion_point(field_set:VCIinfo.VCIsendBMSDemand.VPTPDemand)
}

// float IPTPDemand = 4;
inline void VCIsendBMSDemand::clear_iptpdemand() {
  iptpdemand_ = 0;
}
inline float VCIsendBMSDemand::_internal_iptpdemand() const {
  return iptpdemand_;
}
inline float VCIsendBMSDemand::iptpdemand() const {
  // @@protoc_insertion_point(field_get:VCIinfo.VCIsendBMSDemand.IPTPDemand)
  return _internal_iptpdemand();
}
inline void VCIsendBMSDemand::_internal_set_iptpdemand(float value) {
  
  iptpdemand_ = value;
}
inline void VCIsendBMSDemand::set_iptpdemand(float value) {
  _internal_set_iptpdemand(value);
  // @@protoc_insertion_point(field_set:VCIinfo.VCIsendBMSDemand.IPTPDemand)
}

// float batVoltage = 5;
inline void VCIsendBMSDemand::clear_batvoltage() {
  batvoltage_ = 0;
}
inline float VCIsendBMSDemand::_internal_batvoltage() const {
  return batvoltage_;
}
inline float VCIsendBMSDemand::batvoltage() const {
  // @@protoc_insertion_point(field_get:VCIinfo.VCIsendBMSDemand.batVoltage)
  return _internal_batvoltage();
}
inline void VCIsendBMSDemand::_internal_set_batvoltage(float value) {
  
  batvoltage_ = value;
}
inline void VCIsendBMSDemand::set_batvoltage(float value) {
  _internal_set_batvoltage(value);
  // @@protoc_insertion_point(field_set:VCIinfo.VCIsendBMSDemand.batVoltage)
}

// float modVoltage = 6;
inline void VCIsendBMSDemand::clear_modvoltage() {
  modvoltage_ = 0;
}
inline float VCIsendBMSDemand::_internal_modvoltage() const {
  return modvoltage_;
}
inline float VCIsendBMSDemand::modvoltage() const {
  // @@protoc_insertion_point(field_get:VCIinfo.VCIsendBMSDemand.modVoltage)
  return _internal_modvoltage();
}
inline void VCIsendBMSDemand::_internal_set_modvoltage(float value) {
  
  modvoltage_ = value;
}
inline void VCIsendBMSDemand::set_modvoltage(float value) {
  _internal_set_modvoltage(value);
  // @@protoc_insertion_point(field_set:VCIinfo.VCIsendBMSDemand.modVoltage)
}

// -------------------------------------------------------------------

// ChargerRevive

// uint32 isCharging = 1;
inline void ChargerRevive::clear_ischarging() {
  ischarging_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::_internal_ischarging() const {
  return ischarging_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::ischarging() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ChargerRevive.isCharging)
  return _internal_ischarging();
}
inline void ChargerRevive::_internal_set_ischarging(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  ischarging_ = value;
}
inline void ChargerRevive::set_ischarging(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_ischarging(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ChargerRevive.isCharging)
}

// uint32 isVINStart = 2;
inline void ChargerRevive::clear_isvinstart() {
  isvinstart_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::_internal_isvinstart() const {
  return isvinstart_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::isvinstart() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ChargerRevive.isVINStart)
  return _internal_isvinstart();
}
inline void ChargerRevive::_internal_set_isvinstart(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  isvinstart_ = value;
}
inline void ChargerRevive::set_isvinstart(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_isvinstart(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ChargerRevive.isVINStart)
}

// uint32 faultState1 = 3;
inline void ChargerRevive::clear_faultstate1() {
  faultstate1_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::_internal_faultstate1() const {
  return faultstate1_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::faultstate1() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ChargerRevive.faultState1)
  return _internal_faultstate1();
}
inline void ChargerRevive::_internal_set_faultstate1(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  faultstate1_ = value;
}
inline void ChargerRevive::set_faultstate1(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_faultstate1(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ChargerRevive.faultState1)
}

// uint32 faultState2 = 4;
inline void ChargerRevive::clear_faultstate2() {
  faultstate2_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::_internal_faultstate2() const {
  return faultstate2_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::faultstate2() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ChargerRevive.faultState2)
  return _internal_faultstate2();
}
inline void ChargerRevive::_internal_set_faultstate2(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  faultstate2_ = value;
}
inline void ChargerRevive::set_faultstate2(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_faultstate2(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ChargerRevive.faultState2)
}

// uint32 faultState3 = 5;
inline void ChargerRevive::clear_faultstate3() {
  faultstate3_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::_internal_faultstate3() const {
  return faultstate3_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::faultstate3() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ChargerRevive.faultState3)
  return _internal_faultstate3();
}
inline void ChargerRevive::_internal_set_faultstate3(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  faultstate3_ = value;
}
inline void ChargerRevive::set_faultstate3(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_faultstate3(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ChargerRevive.faultState3)
}

// uint32 bmsCommState = 6;
inline void ChargerRevive::clear_bmscommstate() {
  bmscommstate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::_internal_bmscommstate() const {
  return bmscommstate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::bmscommstate() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ChargerRevive.bmsCommState)
  return _internal_bmscommstate();
}
inline void ChargerRevive::_internal_set_bmscommstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmscommstate_ = value;
}
inline void ChargerRevive::set_bmscommstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmscommstate(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ChargerRevive.bmsCommState)
}

// uint32 bmsRecvState = 7;
inline void ChargerRevive::clear_bmsrecvstate() {
  bmsrecvstate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::_internal_bmsrecvstate() const {
  return bmsrecvstate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::bmsrecvstate() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ChargerRevive.bmsRecvState)
  return _internal_bmsrecvstate();
}
inline void ChargerRevive::_internal_set_bmsrecvstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsrecvstate_ = value;
}
inline void ChargerRevive::set_bmsrecvstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsrecvstate(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ChargerRevive.bmsRecvState)
}

// uint32 bmsType = 8;
inline void ChargerRevive::clear_bmstype() {
  bmstype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::_internal_bmstype() const {
  return bmstype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::bmstype() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ChargerRevive.bmsType)
  return _internal_bmstype();
}
inline void ChargerRevive::_internal_set_bmstype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmstype_ = value;
}
inline void ChargerRevive::set_bmstype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmstype(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ChargerRevive.bmsType)
}

// uint32 bmsTimeoutCnt = 9;
inline void ChargerRevive::clear_bmstimeoutcnt() {
  bmstimeoutcnt_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::_internal_bmstimeoutcnt() const {
  return bmstimeoutcnt_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::bmstimeoutcnt() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ChargerRevive.bmsTimeoutCnt)
  return _internal_bmstimeoutcnt();
}
inline void ChargerRevive::_internal_set_bmstimeoutcnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmstimeoutcnt_ = value;
}
inline void ChargerRevive::set_bmstimeoutcnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmstimeoutcnt(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ChargerRevive.bmsTimeoutCnt)
}

// uint32 elockState = 10;
inline void ChargerRevive::clear_elockstate() {
  elockstate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::_internal_elockstate() const {
  return elockstate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::elockstate() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ChargerRevive.elockState)
  return _internal_elockstate();
}
inline void ChargerRevive::_internal_set_elockstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  elockstate_ = value;
}
inline void ChargerRevive::set_elockstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_elockstate(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ChargerRevive.elockState)
}

// uint32 auxPowerState = 11;
inline void ChargerRevive::clear_auxpowerstate() {
  auxpowerstate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::_internal_auxpowerstate() const {
  return auxpowerstate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::auxpowerstate() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ChargerRevive.auxPowerState)
  return _internal_auxpowerstate();
}
inline void ChargerRevive::_internal_set_auxpowerstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  auxpowerstate_ = value;
}
inline void ChargerRevive::set_auxpowerstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_auxpowerstate(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ChargerRevive.auxPowerState)
}

// uint32 insultState = 12;
inline void ChargerRevive::clear_insultstate() {
  insultstate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::_internal_insultstate() const {
  return insultstate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::insultstate() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ChargerRevive.insultState)
  return _internal_insultstate();
}
inline void ChargerRevive::_internal_set_insultstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  insultstate_ = value;
}
inline void ChargerRevive::set_insultstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_insultstate(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ChargerRevive.insultState)
}

// uint32 insultResult = 13;
inline void ChargerRevive::clear_insultresult() {
  insultresult_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::_internal_insultresult() const {
  return insultresult_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ChargerRevive::insultresult() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ChargerRevive.insultResult)
  return _internal_insultresult();
}
inline void ChargerRevive::_internal_set_insultresult(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  insultresult_ = value;
}
inline void ChargerRevive::set_insultresult(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_insultresult(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ChargerRevive.insultResult)
}

// float bmsCurrentMax = 14;
inline void ChargerRevive::clear_bmscurrentmax() {
  bmscurrentmax_ = 0;
}
inline float ChargerRevive::_internal_bmscurrentmax() const {
  return bmscurrentmax_;
}
inline float ChargerRevive::bmscurrentmax() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ChargerRevive.bmsCurrentMax)
  return _internal_bmscurrentmax();
}
inline void ChargerRevive::_internal_set_bmscurrentmax(float value) {
  
  bmscurrentmax_ = value;
}
inline void ChargerRevive::set_bmscurrentmax(float value) {
  _internal_set_bmscurrentmax(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ChargerRevive.bmsCurrentMax)
}

// float bmsVoltageMax = 15;
inline void ChargerRevive::clear_bmsvoltagemax() {
  bmsvoltagemax_ = 0;
}
inline float ChargerRevive::_internal_bmsvoltagemax() const {
  return bmsvoltagemax_;
}
inline float ChargerRevive::bmsvoltagemax() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ChargerRevive.bmsVoltageMax)
  return _internal_bmsvoltagemax();
}
inline void ChargerRevive::_internal_set_bmsvoltagemax(float value) {
  
  bmsvoltagemax_ = value;
}
inline void ChargerRevive::set_bmsvoltagemax(float value) {
  _internal_set_bmsvoltagemax(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ChargerRevive.bmsVoltageMax)
}

// float cellVoltageMax = 16;
inline void ChargerRevive::clear_cellvoltagemax() {
  cellvoltagemax_ = 0;
}
inline float ChargerRevive::_internal_cellvoltagemax() const {
  return cellvoltagemax_;
}
inline float ChargerRevive::cellvoltagemax() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ChargerRevive.cellVoltageMax)
  return _internal_cellvoltagemax();
}
inline void ChargerRevive::_internal_set_cellvoltagemax(float value) {
  
  cellvoltagemax_ = value;
}
inline void ChargerRevive::set_cellvoltagemax(float value) {
  _internal_set_cellvoltagemax(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ChargerRevive.cellVoltageMax)
}

// float cellTempMax = 17;
inline void ChargerRevive::clear_celltempmax() {
  celltempmax_ = 0;
}
inline float ChargerRevive::_internal_celltempmax() const {
  return celltempmax_;
}
inline float ChargerRevive::celltempmax() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ChargerRevive.cellTempMax)
  return _internal_celltempmax();
}
inline void ChargerRevive::_internal_set_celltempmax(float value) {
  
  celltempmax_ = value;
}
inline void ChargerRevive::set_celltempmax(float value) {
  _internal_set_celltempmax(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ChargerRevive.cellTempMax)
}

// float insultVoltage = 18;
inline void ChargerRevive::clear_insultvoltage() {
  insultvoltage_ = 0;
}
inline float ChargerRevive::_internal_insultvoltage() const {
  return insultvoltage_;
}
inline float ChargerRevive::insultvoltage() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ChargerRevive.insultVoltage)
  return _internal_insultvoltage();
}
inline void ChargerRevive::_internal_set_insultvoltage(float value) {
  
  insultvoltage_ = value;
}
inline void ChargerRevive::set_insultvoltage(float value) {
  _internal_set_insultvoltage(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ChargerRevive.insultVoltage)
}

// -------------------------------------------------------------------

// ContactorState

// uint32 gunNum = 1;
inline void ContactorState::clear_gunnum() {
  gunnum_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ContactorState::_internal_gunnum() const {
  return gunnum_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ContactorState::gunnum() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ContactorState.gunNum)
  return _internal_gunnum();
}
inline void ContactorState::_internal_set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  gunnum_ = value;
}
inline void ContactorState::set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_gunnum(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ContactorState.gunNum)
}

// .VCIinfo.ContactorStateEnum ContactorState = 2;
inline void ContactorState::clear_contactorstate() {
  contactorstate_ = 0;
}
inline ::VCIinfo::ContactorStateEnum ContactorState::_internal_contactorstate() const {
  return static_cast< ::VCIinfo::ContactorStateEnum >(contactorstate_);
}
inline ::VCIinfo::ContactorStateEnum ContactorState::contactorstate() const {
  // @@protoc_insertion_point(field_get:VCIinfo.ContactorState.ContactorState)
  return _internal_contactorstate();
}
inline void ContactorState::_internal_set_contactorstate(::VCIinfo::ContactorStateEnum value) {
  
  contactorstate_ = value;
}
inline void ContactorState::set_contactorstate(::VCIinfo::ContactorStateEnum value) {
  _internal_set_contactorstate(value);
  // @@protoc_insertion_point(field_set:VCIinfo.ContactorState.ContactorState)
}

// -------------------------------------------------------------------

// CurrModAllocState

// uint32 gunNum = 1;
inline void CurrModAllocState::clear_gunnum() {
  gunnum_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CurrModAllocState::_internal_gunnum() const {
  return gunnum_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CurrModAllocState::gunnum() const {
  // @@protoc_insertion_point(field_get:VCIinfo.CurrModAllocState.gunNum)
  return _internal_gunnum();
}
inline void CurrModAllocState::_internal_set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  gunnum_ = value;
}
inline void CurrModAllocState::set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_gunnum(value);
  // @@protoc_insertion_point(field_set:VCIinfo.CurrModAllocState.gunNum)
}

// uint32 MainSize = 2;
inline void CurrModAllocState::clear_mainsize() {
  mainsize_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CurrModAllocState::_internal_mainsize() const {
  return mainsize_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CurrModAllocState::mainsize() const {
  // @@protoc_insertion_point(field_get:VCIinfo.CurrModAllocState.MainSize)
  return _internal_mainsize();
}
inline void CurrModAllocState::_internal_set_mainsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  mainsize_ = value;
}
inline void CurrModAllocState::set_mainsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_mainsize(value);
  // @@protoc_insertion_point(field_set:VCIinfo.CurrModAllocState.MainSize)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace VCIinfo

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::VCIinfo::ContactorStateEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::VCIinfo::ContactorStateEnum>() {
  return ::VCIinfo::ContactorStateEnum_descriptor();
}
template <> struct is_proto_enum< ::VCIinfo::gunMatchState> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::VCIinfo::gunMatchState>() {
  return ::VCIinfo::gunMatchState_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_GCU_5fVCI_5fINFO_2eproto
