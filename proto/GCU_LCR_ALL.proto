syntax = "proto3";
import public "GCU_OSC_INFO.proto";
import public "GCU_OHP_INFO.proto";
package gcu_lcr.protobuf;

// log (0x10)
message LogLoad {
	uint32 Module = 1;								//	日志模块
	uint32 Level = 2;								//	日志等级
	int64 Timestamp = 3;							//	发送时间戳
	bytes Sentence = 4;								//	日志负载
}

// log (0x12)
message OrderLogLoad {
	uint32 MeterID = 1;								//	枪编号
	OHPinfo.PipelineStateEnum PipelineState = 2;	//	原LinkState PipelineStateEnum
	string OrderID = 3;								//	订单ID
}

// LCR to OSC记录体返回 (0x92)
message RecordDateGetBack {
	OSCinfo.SettlementModuleEnum ModuleID = 1;		//	发起记录的通讯模块ID
	OHPinfo.UUIDValue RecordID = 2;					//	记录唯一编码
	bytes RecordLoad = 3;							//	记录体
}


