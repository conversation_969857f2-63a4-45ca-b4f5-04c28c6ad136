syntax = "proto3";
import public "GCU_AllFaultEnum.proto";
import public "GCU_BMS_INFO.proto";
import public "GCU_OSC_INFO.proto";
import public "GCU_VCI_INFO.proto";
import public "GCU_DMC_INFO.proto";
import public "GCU_BMS2015P_INFO.proto";
package OHPinfo;

//订单类型枚举
enum OrderTypeEnum {
	DefaultOrderType = 0x00;		//	未知状态缺省值，非法
	LocalFreeOrder = 0x01;			//	本地不计费订单
	LocalChargeOrder = 0x02;		//	本地计费订单
	CloudPlatformOrder = 0x03;		//	订单云参与鉴权或直接发起订单
	DevicePlatformOrder = 0x04;		//	设备管理云参与鉴权或直接发起订单
	SpecialVehicleOrder = 0x05;		//	特殊车辆直接发起订单
	VINPrimingOrder = 0x06;			//	VIN预启动订单
}

//订单子类型枚举，记录可知鉴权方式
enum OrderSubTypeEnum{
	NormalOrderType = 0x00;			//	默认状态缺省值
	VINSubTypeOrder = 0x01;			//	VIN鉴权发起订单
	UIPassWordOrder = 0x02;			//	密码鉴权发起订单
	NFCCardOrder = 0x03;			//	刷卡鉴权发起订单
	ETCRFIDOrder = 0x04;			//	ETC鉴权发起订单
	LicensePlateOrder = 0x05;		//	车牌识别鉴权订单
	VisionRecognitionOrder = 0x06;	//	机器视觉，人脸/姿态/手势识别鉴权订单
	VoiceRecognitionOrder = 0x07;	//	自然语义，声纹/语音识别鉴权订单
}

//订单状态枚举
enum OrderStateEnum {
	DefaultOrderState = 0x00;		//	未知状态缺省值
	AuthenticationOrder = 0x01;		//	订单鉴权
	EstablishOrder = 0x02;			//	订单创建，锁定管线
	RuningState = 0x03;				//	订单运行中，锁定管线
	SettlementState  = 0x04;		//	订单结算中，锁定管线
	HangOrderState = 0x05;			//	订单挂起，释放管线
	CompletedState  = 0x06;			//	订单完成，释放管线
}

//管线状态枚举
enum PipelineStateEnum {
	DefaultPipelineState = 0x00;	//	未知状态缺省值
	DisablePipeline = 0x01;			//	失能管线
	IdleState = 0x02;				//	未插枪，空闲
	InsertedState = 0x03;			//	已插枪，管线进入可启动/可锁定状态
	PrimingState = 0x04;			//	预启动，VIN预启动订单专用，仍为未锁定管线
	StartingState = 0x05;			//	启动中，已锁定管线
	ChargingState = 0x06;			//	充电中，已锁定管线
	SettlementPipeline = 0x07;		//	结算中，已锁定管线
    SubPlugMode = 0x08;             //  辅枪模式 (开启双枪同充模式)
}

//订单停止策略枚举
enum OrderStrategyEnum {
	DefaultStrategy = 0x00;			//	未知状态缺省值，充满
	LocalFullStrategy = 0x01;		//	离线仍充
	LocalDownStrategy = 0x02;		//	离线即停
	SOCStrategy = 0x03;				//	特定SOC停止
	VStrategy = 0x04;				//	特定电压停止
	EnergyStrategy = 0x05;			//	特定电量停止
	BillStrategy = 0x06;			//	特定金额停止
}

//	订单启动权限/优先级枚举
enum OrderJurisdictionEnum {
	DefaultAuthority = 0x00;			//	无订单启动权限
	LowAuthority = 0x01;				//	低订单权限
	NormalAuthority = 0x02;				//	普通订单权限
	ProprietaryAuthority = 0x03;		//	自营订单权限
	EmergencyAuthority = 0x04;			//	紧急订单权限，用于矿山公交等大功率需求
	LocalAuthority = 0x05;				//	最高订单权限，用于现场演示及展出等目的
}

message UUIDValue {
	uint64 value0 = 1;
	uint64 value1 = 2;
}

//订单结算通讯模块状态描述
message SettlementModuleState {
	OSCinfo.SettlementModuleEnum ModuleID = 1;		//	订单结算通讯模块
	OrderStrategyEnum OffLineStrategy = 2;		//	离线策略
	OrderStrategyEnum NormalStrategy= 3;			//	常规策略
	uint32 RegisterState = 4;				//	注册状态，0表示不可用，1表示可用
	uint32 PeriodicCommunication = 5;			//	周期联通性，0表示不可用，1表示可用
	OrderJurisdictionEnum Jurisdiction = 6;		//	订单启动权限
}

//订单费率 by 2023.7.22
message OrderRate {
	float RuningRate = 3;					//	充电费率
	float ServiceRate = 4;					//	服务费率
	uint32 RateEnum = 5;					//	费率参数 1-尖 2-峰 3-平 4-谷
}

//订单分时电量
message OrderBill {
	// uint32 StarTime=1;						//	费率区间开始时间
	// uint32 EndTime=2;						//	费率区间结束时间
	float RuningBill = 3;						//	分时电量
}

//运行订单参数
message RuningOrderState { 
	OSCinfo.SettlementModuleEnum ModuleID = 1;	//	发起订单结算通讯模块
	UUIDValue OrderUUID = 2;					//	订单唯一编码
	double StartMeterReadOut = 3;			    //	初始电表电能读数
	double NowMeterReadOut = 4;			        //	当前电表电能读数
	float volMeasured = 5;						//	当前电表电压读数 by 2023.7.22
    float curMeasured = 6;						//	当前电表电流读数 by 2023.7.22
}

//电表状态及运行读数
message MeterState {
	double MeterWReadOut = 1;			//	实时电能读数
	double MeterIReadOut = 2;			//	实时电表电流读数
	double MeterVReadOut = 3;			//	实时电表电压读数
	uint32 MeterOffLine = 4;			//	电表离线状态，0表示离线，1表示正常
	uint32 MeterCheck = 5;				//	电表校验状态，0表示异常，1表示正常
	uint64 RefreshTime = 6;				//	电表状态刷新时间
}

// 电表信息
message MeterInfo {
    uint32 MeterID = 1;
    string MeterSN = 2;
    string MeterVer = 3;
}

//历史订单描述
message HistoryOrderList {
	UUIDValue OrderUUID = 1;						//	订单唯一标识
	OrderStateEnum OrderStateEnum = 2;				//	订单状态
    uint32 MeterID = 3;								//	当前描述枪头编号，与CAN总线编号一致(0~5)
	string UserID = 4;								//	平台侧用户ID
	string OrderID = 5;								//	平台侧订单ID
	uint32 RuningRateListSize = 6;			        //	本此抢占执行费率
	repeated OrderRate RuningRateList = 7;			//	订单当前执行费率
	uint32 RuningBillListSize = 8;			        //	本此抢占执行费率
	repeated OrderBill RuningBillList = 9;			//	订单当前分时电量
	uint32 StarTime = 10;							//	开始时间戳
	uint32 StopTime = 11;							//	结束时间戳
	double StartMeterReadOut = 12;					//	开始电表电能读数
	double StopMeterReadOut = 13;					//	结束电表电能读数
	bytes StopReason = 14;							//  停机原因
	uint32 OSCReserve0 = 15;						//	OSC保留特征值0
	uint32 OSCReserve1 = 16;						//	OSC保留特征值1
}

//订单流水线状态描述
message OrderPipelineState {
	uint32 MeterID = 1;								//	当前描述流水线编号，与电表485总线编号一致(0~5)
	uint32 ModuleOpen = 2;							//	流水线是否允许本模块发起订单，是否响应
	uint32 LinkState = 3;							//	插枪状态，1表示未插枪，2表示已插枪 
	uint32 OnLineState = 4;							//	流水线可用状态，0表示不可用，1表示可用  不可用表示故障
	uint32 LockState = 5;							//	流水线锁定状态，0表示未锁定，1表示锁定   锁定表示充电中
	uint32 StartType = 6;							//	扫码0，刷卡1
	uint32 ChargingDuration = 7;					//	充电时长(s)
	PipelineStateEnum PipelineState = 8;			//	原LinkState PipelineStateEnum
	float  ChargingPower = 9;						//	充电电能
	double NowMeterReadOut = 10;					//	当前电表电能读数
	OSCinfo.SettlementModuleEnum Preempt = 11;		 //  管线抢占OSC模块枚举值(多平台)
}

//主状态机状态返回描述
message OrderPipelineAns {
	uint32 MeterID = 1;										//	当前描述  流水线编号，与电表485总线编号一致(0~5)
	BMSinfo.ChargeState bmsState = 2;						//	charge线程状态
	BMSinfo.BMSHandShake BmsShakehandsM = 3;				//	BMS返回信息
	BMSinfo.BMSVerification BmsIdentifyM = 4;				//	BMS返回信息
	BMSinfo.BMSConfig BmsConfigM = 5;						//	BMS返回信息
	BMSinfo.BMSCharging BmsChargingM = 6;					//	BMS返回信息
	BMSinfo.BMSChargingEnd BmsChargeFinishM = 7;			//	BMS返回信息
	AllFaultEnum.MatrixStatus ContactorState = 8;			//	阵列接触器返回故障列表
	uint32 OHPFaultSize = 9;								//	OHP返回故障实际数量
	repeated AllFaultEnum.OHPFaultState OHPFault = 10;		//	OHP返回故障列表
	uint32 PMMFaultSize = 11;								//	PMM发生故障实际数量
	repeated AllFaultEnum.PMMFaultState MainAlarmList = 12;	//	PMM返回故障列表
	uint32 VCIFaultSize = 13;				 				//	VCI返回故障障实际数量
	repeated AllFaultEnum.VCIFaultState VCIFault = 14;		//	VCI返回故障列表
	uint32 DMCFaultSize = 15;				 				//	VCI返回故障障实际数量
	repeated AllFaultEnum.DMCFaultState DMCFault = 16;		//	VCI返回故障列表
	uint32 ADModuleParamSize = 17;							//	模块实时参数队列实际数量
	repeated AllFaultEnum.ADModuleAlarm ADModulePList = 18; //  模块返回故障列表
	string MeterSN = 19;									//	电表SN编号
	string MeterVer = 20;									//	电表协议版本
	BMS2015PlusInfo.bms2015pMsg bms2015pM = 21;			    //  2015+ bms信息
}

//订单流水信息()
message OrderPipelineInfo {
	string OrderID = 1;								// 订单流水号
	string UserID = 2;								// 用户信息
	uint32 StartSoc = 3;							//	启动OSC
	uint32 StartTime = 4;							// 启动时间
}

//屏幕配置信息
message hmiConfigInfo {
	uint32 guns = 1;                    //  枪头数量  1:单枪模式 2:双枪模式
	uint32 gunCode = 2;                 //  枪号 0:A  1:B枪
	DMCinfo.GunTypeEnum GunType =3;     //  枪类型
	uint32 VINType = 4;                 //  vin类型  0:vin不使能  2:桩端发起 3:用户发起
	uint32 AuxType = 5;                 //  辅源模式 0:不使能  1:使能
	uint32 adminMode = 6;               //  管理员模式是否使能  0:不使能  1:使能
	uint32 stopType = 7;                //  屏幕停机方式 0:密码停机 1:直接停机
	uint32 RateType =8;                 //  费率是否显示 0:不显示   1:显示
	uint32 standbylogoType=9;           //  待机logo是否显示 0:不显示   1:显示
	uint32 ledType = 10;                //  灯板类型  1:圆灯板  2:方灯板
	uint32 historyType =11;             //  历史订单是否显示 0:不显示 1:显示
	uint32 stopChgSocType = 12;         //  停止充电soc是否维持 0:不维持 1:维持
	uint32 hmiConfigEnable =13;         //  屏幕配置功能 0:不使能 1:使能
	uint32 netOfflineWifiEnable=14;     //  离网wifi充电使能 0:未使能  1:使能
	uint32 VLPREnable = 15;             //  车牌识别使能   0:未使能  1:使能
}
