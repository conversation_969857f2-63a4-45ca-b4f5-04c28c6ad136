/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class loger Definition here.
 */
#include "loger.h"
#include "evs_proto.h"
#include "loger.h"
#include "GCU_EVS_INFO.pb.h"
#include "GCU_EVS_HMI.pb.h"
#include "evs_storage.h"

EVSProto::EVSProto()
{
}

EVSProto::~EVSProto() {}
int32_t EVSProto::ProtoInit(void){
    eVSManageData.EvsDataInit();
    return 0;
}
int32_t EVSProto::ProtoProcess(void)
{
    if(EVS_FAILED == SocketInit())
    {
        Loger(NORMAL,"SocketInit failed\r\n");
        return EVS_FAILED;
    }
    socket_ready = true;
    ProtocolFrameBuff frame_buff={0};
    int32_t frame_len=0,ret = 0; 
    while(1)
    {
        for(int i=0;i<MAX_SERVER_NUM;i++)
        {
            read_socket((DeviceTypeEnum)i);
            ret = get_package_proto(frame_buff.data_buff, &frame_len);
            if(ret > 0)
            {
                frame_terminal_buff[i].push_back(frame_buff);
                memset(&frame_buff, 0, sizeof(frame_buff));
            }
        }
        usleep(10*1000);
    }
    return 0;

}

int32_t EVSProto::SocketInit()
{
    int32_t ret;
    Loger(NORMAL, "EVSProto::SocketInit()");
    while(!socket_initok);
    SocketManage[E_DEV_EVS].target_port = EVS_SOCKET_PORT;
    SocketManage[E_DEV_EVS].ip_addr = serverIP;


    SocketManage[E_DEV_HMI].target_port = HMI_SOCKET_PORT;
    SocketManage[E_DEV_HMI].ip_addr = HMI_SOCKET_IP;

    
    for(int i =0;i<MAX_SERVER_NUM;i++){
        SocketManage[i].Socket_fd = socket(AF_INET, SOCK_DGRAM, 0);
        if (SocketManage[i].Socket_fd < 0)
        {
            Loger(NORMAL, "creat socket[%d] filed!\r",i);
            return EVS_FAILED;
        }

        struct timeval rTimeout = {0, 5000};
        ret = setsockopt(SocketManage[i].Socket_fd, SOL_SOCKET, SO_RCVTIMEO, &rTimeout, sizeof(rTimeout));
        if (ret < 0)
        {
            Loger(NORMAL, "Set Socket[%d] Timeout Failed\r\n",i);
            return EVS_FAILED;
        }

        SocketManage[i].Target_Socket_addr.sin_family = AF_INET;
        SocketManage[i].Target_Socket_addr.sin_port = htons(SocketManage[i].target_port);
        SocketManage[i].Target_Socket_addr.sin_addr.s_addr = inet_addr(SocketManage[i].ip_addr.c_str());
        std::cout << "SocketInit("<<i<<"),ip=="<<SocketManage[i].ip_addr<<"port=="<<SocketManage[i].target_port<<std::endl;
        Loger(NORMAL, "SocketInit(%d) success,fd==%d,IP=%s,port=%d",i,SocketManage[i].Socket_fd,SocketManage[i].ip_addr.c_str(),SocketManage[i].target_port);
    }

    return EVS_SUCCESS;
}

int32_t EVSProto::SendMsg(DeviceTypeEnum  dev,uint8_t cmd, uint8_t *data, uint32_t len)
{
    int32_t ret;
    Protocol_Buff_Fram *protofram;
    protofram = (Protocol_Buff_Fram *)malloc(len + sizeof(Protocol_Buff_Fram) + 4);
    if (protofram == NULL)
    {
        Loger(NORMAL, "malloc protofram failed!\r\n");
        return EVS_FAILED;
    }
    protofram->head = PROTOCOL_FRAM_HEAD;
    protofram->cmd = cmd;
    protofram->len = len;
    memcpy(protofram->data, data, len);

    protofram->data[len] = TAIL[0];
    protofram->data[len + 1] = TAIL[1];
    protofram->data[len + 2] = TAIL[2];
    protofram->data[len + 3] = TAIL[3];

    ret = sendto(SocketManage[dev].Socket_fd, &protofram->head, len + sizeof(Protocol_Buff_Fram) + 4, 0,
                 (struct sockaddr *)&SocketManage[dev].Target_Socket_addr, (socklen_t)sizeof(SocketManage[dev].Target_Socket_addr));
    free(protofram);
    if (ret < 0)
    {
        Loger(NORMAL, "send msg to HMI failed!\r\n");
        return EVS_FAILED;
    }
    return EVS_SUCCESS;
}

/***********************evs通信帧**************************/
/**
 * @brief: EVS注册帧(0x0) evs-> hmi
 *
 * @return {*}
 */

int32_t EVSProto::Send_EvsModuleRegister(DeviceTypeEnum dev)
{
    //std::cout<<"Send_EvsModuleRegister()"<<std::endl;
    int32_t result = EVS_SUCCESS;
    gcu_evs_hmi::protobuf::EVSLogin evsLogin;
   

    evsLogin.set_evsid(evsModuleRegisterbuff.evsID);
    evsLogin.set_interval(evsModuleRegisterbuff.interval);

    uint32_t protocol_size = evsLogin.ByteSizeLong();

    uint8_t protocol_buff[protocol_size];

    evsLogin.SerializePartialToArray(protocol_buff, protocol_size);
    
    //std::cout<<"evsLogin.DebugString().c_str()"<<evsLogin.DebugString().c_str()<<std::endl;
    result = SendMsg(dev,TYPE_EVSMODULE_REGISTER, (uint8_t*)protocol_buff, protocol_size);
    if (result != EVS_SUCCESS)
    {
        Loger(NORMAL, "send dev[%d] register failed!\r\n",dev);
        return EVS_FAILED;
    }

    //Loger(NORMAL, "evsLogin.DebugString:\r\n%s", evsLogin.DebugString().c_str());
    return result;
}

/**
 * @brief: /EVS心跳(0x1)  evs ->hmi
 *
 * @return {*}
 */
int32_t EVSProto::Send_ControlCmdRsp(DeviceTypeEnum dev)
{
    int32_t result = EVS_SUCCESS;

    gcu_evs_hmi::protobuf::EVSHb   eVSHb;

    eVSHb.set_evsid(controlCmdRspBuff.evsID);
    eVSHb.set_heartbeatcnt(controlCmdRspBuff.heartbeatCnt);
    eVSHb.set_onoffstate(controlCmdRspBuff.onOffState);

    
    //EVState 车辆状态
    EVSInfo::EVStateMsg *mutable_EVStateMsg = eVSHb.mutable_evsstate();
    mutable_EVStateMsg->set_batvol(controlCmdRspBuff.evsState.batVol);
    mutable_EVStateMsg->set_batcur(controlCmdRspBuff.evsState.batCur);
    mutable_EVStateMsg->set_nowsoc(controlCmdRspBuff.evsState.nowSOC);
    mutable_EVStateMsg->set_nowfc(controlCmdRspBuff.evsState.nowFC);
    mutable_EVStateMsg->set_nowfdc(controlCmdRspBuff.evsState.nowFDC);
    mutable_EVStateMsg->set_chgmode(controlCmdRspBuff.evsState.chgMode);
    mutable_EVStateMsg->set_insultposr(controlCmdRspBuff.evsState.insultPosR);
    mutable_EVStateMsg->set_insultnegr(controlCmdRspBuff.evsState.insultNegR);
    mutable_EVStateMsg->set_cc1volt(controlCmdRspBuff.evsState.cc1Volt);
    mutable_EVStateMsg->set_workmode(controlCmdRspBuff.evsState.workMode);
    mutable_EVStateMsg->set_stopcode(controlCmdRspBuff.evsState.stopCode);
    mutable_EVStateMsg->set_cc2volt(controlCmdRspBuff.evsState.cc2Volt);
   

    uint32_t protocol_size = eVSHb.ByteSizeLong();

    uint8_t protocol_buff[protocol_size];

    eVSHb.SerializePartialToArray(protocol_buff, protocol_size);
    
    result = SendMsg(dev,TYPE_CONTROL_CMD_RSP, (uint8_t*)protocol_buff, protocol_size);

    if (result != EVS_SUCCESS)
    {
        Loger(NORMAL, "send evs module register failed!\r\n");
        return EVS_FAILED;
    }

    //Loger(NORMAL, "eVSHb.DebugString:\r\n%s", eVSHb.DebugString().c_str());
    return result;
}

/**
 * @brief: EVS报文填充.响应(0x1F)  evs->hmi    报文填充响应（0x02, 0x03, 0x04, 0x05）
 *
 * @return {*}
 */

int32_t EVSProto::Send_MsgFillRsp(DeviceTypeEnum dev)
{
    int32_t result = EVS_SUCCESS;

    gcu_evs_hmi::protobuf::EVSSetReply   eVSSetReply;


    eVSSetReply.set_evsid(msgFillRspBuff.evsID);
    eVSSetReply.set_msgid(msgFillRspBuff.msgID);
    eVSSetReply.set_setack(msgFillRspBuff.setAck);
   


    uint32_t protocol_size = eVSSetReply.ByteSizeLong();

    uint8_t protocol_buff[protocol_size];

    eVSSetReply.SerializePartialToArray(protocol_buff, protocol_size);
    
    result = SendMsg(dev,TYPE_MSG_FILL_ACK, (uint8_t*)protocol_buff, protocol_size);

    if (result != EVS_SUCCESS)
    {
        Loger(NORMAL, "send evs module register failed!\r\n");
        return EVS_FAILED;
    }

    //std::cout<<"eVSSetReply.DebugString().c_str()"<<eVSSetReply.DebugString().c_str()<<std::endl;

    //Loger(NORMAL, "eVSSetReply.DebugString:\r\n%s", eVSSetReply.DebugString().c_str());
    return result;
}


int32_t EVSProto::Send_MessageIntface(DeviceTypeEnum dev,EvsFrameMessageEnum msgType)
{
    int32_t result = EVS_SUCCESS;
    //std::cout<<"Send_MessageIntface()"<<std::endl;
    switch (msgType)
    {
    case TYPE_EVSMODULE_REGISTER:
        getEvsToHmiRegisterData();
        result = Send_EvsModuleRegister(dev);
        break;
    case TYPE_CONTROL_CMD_RSP:
        getEvsToHmiControlCmdRspData();
        result = Send_ControlCmdRsp(dev);
        break;

    case TYPE_MSG_FILL_ACK:
        getEvsToHmiMsgFillRspData();
        result = Send_MsgFillRsp(dev);
        break;

    default:
        Loger(NORMAL, "Send_MessageIntface msgType error!\r\n");
        break;
    }
    return result;
}

/**
 * @brief: EVS发送0x1E查询0x02车辆侧报文信息
 *
 * @return {*}
 */
int32_t EVSProto::Send_VehicleMsgReport(DeviceTypeEnum dev)
{
    int32_t result = EVS_SUCCESS;
    gcu_evs_hmi::protobuf::EVSMsgSet  VehicleMsgPro;

    VehicleMsgPro.set_evsid(eVSManageData.getMsgInfoManageBuff.evsID);
    //get msg form storage
    eVSManageData.MsgInfoUpdate();
    //版本协商信息
    EVSInfo::EVProtoConferMsg *mutable_protoconferm = VehicleMsgPro.mutable_protoconferm();
    mutable_protoconferm->set_cantype(eVSManageData.vehicleSideMsgFillManageBuff.protoConferM.canType);
    mutable_protoconferm->set_gbversion(eVSManageData.vehicleSideMsgFillManageBuff.protoConferM.gbVersion);
    mutable_protoconferm->set_guidanceversion(eVSManageData.vehicleSideMsgFillManageBuff.protoConferM.guidanceVersion);
    mutable_protoconferm->set_transportversion(eVSManageData.vehicleSideMsgFillManageBuff.protoConferM.transportVersion);
    mutable_protoconferm->set_conferres(eVSManageData.vehicleSideMsgFillManageBuff.protoConferM.conferRes);

    //功能协商
    EVSInfo::EVFunConferMsg  *mutable_funconfer = VehicleMsgPro.mutable_funconferm();
    mutable_funconfer->set_configfdc(eVSManageData.vehicleSideMsgFillManageBuff.funConferM.configFDC);
    mutable_funconfer->set_authenfdc(eVSManageData.vehicleSideMsgFillManageBuff.funConferM.authenFDC);
    mutable_funconfer->set_appointfdc(eVSManageData.vehicleSideMsgFillManageBuff.funConferM.appointFDC);
    mutable_funconfer->set_selfcheckfdc(eVSManageData.vehicleSideMsgFillManageBuff.funConferM.selfCheckFDC);
    mutable_funconfer->set_powersupplyfdc(eVSManageData.vehicleSideMsgFillManageBuff.funConferM.powerSupplyFDC);
    mutable_funconfer->set_energytransferfdc(eVSManageData.vehicleSideMsgFillManageBuff.funConferM.energyTransferFDC);
    mutable_funconfer->set_endfdc(eVSManageData.vehicleSideMsgFillManageBuff.funConferM.endFDC);

    //车辆充电参数配置
    EVSInfo::EVChgParaConfigMsg *mutable_paraconfigm = VehicleMsgPro.mutable_paraconfigm();
    mutable_paraconfigm->set_currallowmax(eVSManageData.vehicleSideMsgFillManageBuff.paraConfigM.currAllowMAX);
    mutable_paraconfigm->set_voltallowmax(eVSManageData.vehicleSideMsgFillManageBuff.paraConfigM.voltAllowMAX);
    mutable_paraconfigm->set_energyallowmax(eVSManageData.vehicleSideMsgFillManageBuff.paraConfigM.energyAllowMAX);
    mutable_paraconfigm->set_nowsoc(eVSManageData.vehicleSideMsgFillManageBuff.paraConfigM.nowSOC);
    mutable_paraconfigm->set_cellvoltallowmax(eVSManageData.vehicleSideMsgFillManageBuff.paraConfigM.cellVoltAllowMAX);
    mutable_paraconfigm->set_battempallowmax(eVSManageData.vehicleSideMsgFillManageBuff.paraConfigM.batTempAllowMAX);
    mutable_paraconfigm->set_restarnum(eVSManageData.vehicleSideMsgFillManageBuff.paraConfigM.restarNum);

    // 鉴权信息
    EVSInfo::EVAuthenMsg *mutable_authenm = VehicleMsgPro.mutable_authenm();
    mutable_authenm->set_authenwaittime(eVSManageData.vehicleSideMsgFillManageBuff.authenM.authenWaitTime);
    mutable_authenm->set_evin(eVSManageData.vehicleSideMsgFillManageBuff.authenM.eVIN);
    mutable_authenm->set_nextfdc(eVSManageData.vehicleSideMsgFillManageBuff.authenM.nextFDC);

    //预约充电信息
    EVSInfo::EVReserveMsg *mutable_reservem = VehicleMsgPro.mutable_reservem();
    mutable_reservem->set_bmsdesirestarttime(eVSManageData.vehicleSideMsgFillManageBuff.reserveM.bmsDesireStartTime);
    mutable_reservem->set_bmsdesireleavetime(eVSManageData.vehicleSideMsgFillManageBuff.reserveM.bmsDesireLeaveTime);
    mutable_reservem->set_reserveresult(eVSManageData.vehicleSideMsgFillManageBuff.reserveM.reserveResult);
    mutable_reservem->set_immediatechargesupport(eVSManageData.vehicleSideMsgFillManageBuff.reserveM.immediateChargeSupport);

    // 供电模式 
    EVSInfo::EVPowerSupplyMsg *mutable_powersupplym = VehicleMsgPro.mutable_powersupplym();
    mutable_powersupplym->set_supplystate(eVSManageData.vehicleSideMsgFillManageBuff.powerSupplyM.supplyState);
    mutable_powersupplym->set_supplyvoldesire(eVSManageData.vehicleSideMsgFillManageBuff.powerSupplyM.supplyVolDesire);
    mutable_powersupplym->set_supplycurrdesire(eVSManageData.vehicleSideMsgFillManageBuff.powerSupplyM.supplyCurrDesire);
    mutable_powersupplym->set_supplyend(eVSManageData.vehicleSideMsgFillManageBuff.powerSupplyM.supplyEnd);

    //充电阶段信息
    EVSInfo::EVChargingMsg *mutable_chargingm = VehicleMsgPro.mutable_chargingm();
    mutable_chargingm->set_bmsready(eVSManageData.vehicleSideMsgFillManageBuff.chargingM.bmsReady);
    mutable_chargingm->set_voldemand(eVSManageData.vehicleSideMsgFillManageBuff.chargingM.volDemand);
    mutable_chargingm->set_curdemand(eVSManageData.vehicleSideMsgFillManageBuff.chargingM.curDemand);
    mutable_chargingm->set_chargemode(eVSManageData.vehicleSideMsgFillManageBuff.chargingM.chargeMode);
    mutable_chargingm->set_socnow(eVSManageData.vehicleSideMsgFillManageBuff.chargingM.socNow);
    mutable_chargingm->set_reschgtime(eVSManageData.vehicleSideMsgFillManageBuff.chargingM.resChgTime);
    mutable_chargingm->set_cellbatvolmax(eVSManageData.vehicleSideMsgFillManageBuff.chargingM.cellBatVolMax);
    mutable_chargingm->set_cellbatvolmin(eVSManageData.vehicleSideMsgFillManageBuff.chargingM.cellBatVolMin);
    mutable_chargingm->set_celltempmax(eVSManageData.vehicleSideMsgFillManageBuff.chargingM.celltempMax);
    mutable_chargingm->set_celltempmin(eVSManageData.vehicleSideMsgFillManageBuff.chargingM.celltempMin);


    //充电结束信息
    EVSInfo::EVChargingEndMsg *mutable_chargingendm = VehicleMsgPro.mutable_chargingendm();
    mutable_chargingendm->set_endtype(eVSManageData.vehicleSideMsgFillManageBuff.chargingEndM.endType);
    mutable_chargingendm->set_endcode(eVSManageData.vehicleSideMsgFillManageBuff.chargingEndM.endCode);
    mutable_chargingendm->set_endreason(eVSManageData.vehicleSideMsgFillManageBuff.chargingEndM.endReason);
    mutable_chargingendm->set_repeat(eVSManageData.vehicleSideMsgFillManageBuff.chargingEndM.repeat);
    mutable_chargingendm->set_bmsstickcheckstate(eVSManageData.vehicleSideMsgFillManageBuff.chargingEndM.bmsStickCheckState);

    uint32_t protocol_size = VehicleMsgPro.ByteSizeLong();

    uint8_t protocol_buff[protocol_size];

    VehicleMsgPro.SerializePartialToArray(protocol_buff, protocol_size);

    Loger(NORMAL,"VehicleMsgPro=%s\r\n",VehicleMsgPro.DebugString().c_str());
    
    result = SendMsg(dev,TYPE_VEHICLE_SIDE_MSG_FILL, (uint8_t*)protocol_buff, protocol_size);
    if (result != EVS_SUCCESS)
    {
        Loger(NORMAL, "send module[%d] register failed!\r\n",dev);
        return EVS_FAILED;
    }
    return result;

}

/**
 * @brief: EVS发送0x1E查询0x03报文控制信息
 *
 * @return {*}
 */
int32_t EVSProto::Send_MsgAttrReport(DeviceTypeEnum dev)
{
    int32_t result = EVS_SUCCESS;
    gcu_evs_hmi::protobuf::EVSMsgCtrl  MsgCtrlAttrPro;

    MsgCtrlAttrPro.set_evsid(eVSManageData.getMsgInfoManageBuff.evsID);
    //报文发送控制
    EVSInfo::EVMsgCtrl *mutable_msgctrl = MsgCtrlAttrPro.mutable_msgctrl();
    mutable_msgctrl->set_x2_0x02_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.x2_0x02_State);
    mutable_msgctrl->set_x4_0x04_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.x4_0x04_State);
    mutable_msgctrl->set_x6_0x06_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.x6_0x06_State);
    mutable_msgctrl->set_x9_0x09_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.x9_0x09_State);

    mutable_msgctrl->set_b2_0x12_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.b2_0x12_State);
    mutable_msgctrl->set_c2_0x22_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.c2_0x22_State);
    mutable_msgctrl->set_c4_0x24_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.c4_0x24_State);
    mutable_msgctrl->set_d2_0x32_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.d2_0x32_State);
    mutable_msgctrl->set_d4_0x34_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.d4_0x34_State);
    mutable_msgctrl->set_d6_0x36_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.d6_0x36_State);
    mutable_msgctrl->set_d7_0x37_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.d7_0x37_State);
    mutable_msgctrl->set_d9_0x39_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.d9_0x39_State);
    mutable_msgctrl->set_d10_0x3a_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.d10_0x3A_State);
    mutable_msgctrl->set_e2_0x42_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.e2_0x42_State);
    mutable_msgctrl->set_e4_0x44_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.e4_0x44_State);
    mutable_msgctrl->set_f2_0x52_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.f2_0x52_State);
    mutable_msgctrl->set_g2_0x62_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.g2_0x62_State);
    mutable_msgctrl->set_g3_0x63_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.g3_0x63_State);
    mutable_msgctrl->set_g5_0x65_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.g5_0x65_State);
    mutable_msgctrl->set_h2_0x72_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.h2_0x72_State);
    mutable_msgctrl->set_h3_0x73_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.h3_0x73_State);
    mutable_msgctrl->set_h4_0x74_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.h4_0x74_State);
    mutable_msgctrl->set_h7_0x77_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.h7_0x77_State);
    mutable_msgctrl->set_h9_0x79_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.h9_0x79_State);
    mutable_msgctrl->set_h11_0x82_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.h11_0x82_State);
    mutable_msgctrl->set_h13_0x84_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.h13_0x84_State);
    mutable_msgctrl->set_h14_0x85_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.h14_0x85_State);
    mutable_msgctrl->set_h16_0x87_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.h16_0x87_State);
    mutable_msgctrl->set_h18_0x89_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.h18_0x89_State);
    mutable_msgctrl->set_h20_0x8b_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.h20_0x8B_State);
    mutable_msgctrl->set_i1_0x91_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.i1_0x91_State);
    mutable_msgctrl->set_i4_0x94_state(eVSManageData.msgCtrlInfoManageBuff.msgCtrl.i4_0x94_State);

    //报文发送周期
    EVSInfo::EVMsgCtrl *mutable_msgcycletime = MsgCtrlAttrPro.mutable_msgcycletime();
    mutable_msgcycletime->set_b2_0x12_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.b2_0x12_State);
    mutable_msgcycletime->set_c2_0x22_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.c2_0x22_State);
    mutable_msgcycletime->set_c4_0x24_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.c4_0x24_State);
    mutable_msgcycletime->set_d2_0x32_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.d2_0x32_State);
    mutable_msgcycletime->set_d4_0x34_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.d4_0x34_State);
    mutable_msgcycletime->set_d6_0x36_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.d6_0x36_State);
    mutable_msgcycletime->set_d7_0x37_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.d7_0x37_State);
    mutable_msgcycletime->set_d9_0x39_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.d9_0x39_State);
    mutable_msgcycletime->set_d10_0x3a_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.d10_0x3A_State);
    mutable_msgcycletime->set_e2_0x42_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.e2_0x42_State);
    mutable_msgcycletime->set_e4_0x44_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.e4_0x44_State);
    mutable_msgcycletime->set_f2_0x52_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.f2_0x52_State);
    mutable_msgcycletime->set_g2_0x62_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.g2_0x62_State);
    mutable_msgcycletime->set_g3_0x63_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.g3_0x63_State);
    mutable_msgcycletime->set_g5_0x65_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.g5_0x65_State);
    mutable_msgcycletime->set_h2_0x72_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.h2_0x72_State);
    mutable_msgcycletime->set_h3_0x73_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.h3_0x73_State);
    mutable_msgcycletime->set_h4_0x74_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.h4_0x74_State);
    mutable_msgcycletime->set_h7_0x77_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.h7_0x77_State);
    mutable_msgcycletime->set_h9_0x79_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.h9_0x79_State);
    mutable_msgcycletime->set_h11_0x82_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.h11_0x82_State);
    mutable_msgcycletime->set_h13_0x84_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.h13_0x84_State);
    mutable_msgcycletime->set_h14_0x85_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.h14_0x85_State);
    mutable_msgcycletime->set_h16_0x87_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.h16_0x87_State);
    mutable_msgcycletime->set_h18_0x89_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.h18_0x89_State);
    mutable_msgcycletime->set_h20_0x8b_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.h20_0x8B_State);
    mutable_msgcycletime->set_i1_0x91_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.i1_0x91_State);
    mutable_msgcycletime->set_i4_0x94_state(eVSManageData.msgCtrlInfoManageBuff.msgCycleTime.i4_0x94_State);

     //报文最长发送时间
    EVSInfo::EVMsgCtrl *mutable_msgmaxsendtime = MsgCtrlAttrPro.mutable_msgmaxsendtime();
    mutable_msgmaxsendtime->set_b2_0x12_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.b2_0x12_State);
    mutable_msgmaxsendtime->set_c2_0x22_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.c2_0x22_State);
    mutable_msgmaxsendtime->set_c4_0x24_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.c4_0x24_State);
    mutable_msgmaxsendtime->set_d2_0x32_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.d2_0x32_State);
    mutable_msgmaxsendtime->set_d4_0x34_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.d4_0x34_State);
    mutable_msgmaxsendtime->set_d6_0x36_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.d6_0x36_State);
    mutable_msgmaxsendtime->set_d7_0x37_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.d7_0x37_State);
    mutable_msgmaxsendtime->set_d9_0x39_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.d9_0x39_State);
    mutable_msgmaxsendtime->set_d10_0x3a_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.d10_0x3A_State);
    mutable_msgmaxsendtime->set_e2_0x42_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.e2_0x42_State);
    mutable_msgmaxsendtime->set_e4_0x44_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.e4_0x44_State);
    mutable_msgmaxsendtime->set_f2_0x52_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.f2_0x52_State);
    mutable_msgmaxsendtime->set_g2_0x62_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.g2_0x62_State);
    mutable_msgmaxsendtime->set_g3_0x63_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.g3_0x63_State);
    mutable_msgmaxsendtime->set_g5_0x65_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.g5_0x65_State);
    mutable_msgmaxsendtime->set_h2_0x72_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.h2_0x72_State);
    mutable_msgmaxsendtime->set_h3_0x73_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.h3_0x73_State);
    mutable_msgmaxsendtime->set_h4_0x74_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.h4_0x74_State);
    mutable_msgmaxsendtime->set_h7_0x77_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.h7_0x77_State);
    mutable_msgmaxsendtime->set_h9_0x79_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.h9_0x79_State);
    mutable_msgmaxsendtime->set_h11_0x82_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.h11_0x82_State);
    mutable_msgmaxsendtime->set_h13_0x84_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.h13_0x84_State);
    mutable_msgmaxsendtime->set_h14_0x85_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.h14_0x85_State);
    mutable_msgmaxsendtime->set_h16_0x87_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.h16_0x87_State);
    mutable_msgmaxsendtime->set_h18_0x89_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.h18_0x89_State);
    mutable_msgmaxsendtime->set_h20_0x8b_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.h20_0x8B_State);
    mutable_msgmaxsendtime->set_i1_0x91_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.i1_0x91_State);
    mutable_msgmaxsendtime->set_i4_0x94_state(eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme.i4_0x94_State);



    //阶段确认报文设置
    EVSInfo::EVFunConferAckMsg  *mutable_funconferack = MsgCtrlAttrPro.mutable_funconferack();
    mutable_funconferack->set_funconferack(eVSManageData.msgCtrlInfoManageBuff.funConferAck.funConferAck);
    mutable_funconferack->set_configack(eVSManageData.msgCtrlInfoManageBuff.funConferAck.configAck);
    mutable_funconferack->set_authenack(eVSManageData.msgCtrlInfoManageBuff.funConferAck.authenAck);
    mutable_funconferack->set_appointack(eVSManageData.msgCtrlInfoManageBuff.funConferAck.appointAck);
    mutable_funconferack->set_selfcheckack(eVSManageData.msgCtrlInfoManageBuff.funConferAck.selfCheckAck);
    mutable_funconferack->set_powersupplyack(eVSManageData.msgCtrlInfoManageBuff.funConferAck.powerSupplyAck);
    mutable_funconferack->set_energytransferack(eVSManageData.msgCtrlInfoManageBuff.funConferAck.energyTransferAck);
    mutable_funconferack->set_endack(eVSManageData.msgCtrlInfoManageBuff.funConferAck.endAck);

    uint32_t protocol_size = MsgCtrlAttrPro.ByteSizeLong();

    uint8_t protocol_buff[protocol_size];

    MsgCtrlAttrPro.SerializePartialToArray(protocol_buff, protocol_size);

    Loger(NORMAL,"MsgCtrlAttrPro=%s\r\n",MsgCtrlAttrPro.DebugString().c_str());

    result = SendMsg(dev,TYPE_MSG_ATTR, (uint8_t*)protocol_buff, protocol_size);
    if (result != EVS_SUCCESS)
    {
        Loger(NORMAL, "send module[%d] register failed!\r\n",dev);
        return EVS_FAILED;
    }
    return result;

}

/**
 * @brief: EVS发送0x1E查询0x04电气控制参数
 *
 * @return {*}
 */
int32_t EVSProto::Send_ElecContrlReport(DeviceTypeEnum dev)
{
    int32_t result = EVS_SUCCESS;
    gcu_evs_hmi::protobuf::EVSSysCtrl  ElecCtrlPro;

    ElecCtrlPro.set_evsid(eVSManageData.getMsgInfoManageBuff.evsID);
    //get msg form storage
    eVSManageData.MsgInfoUpdate();
    //车辆电气控制
    EVSInfo::EVElectricCtrl *mutable_electricctrl = ElecCtrlPro.mutable_electricctrl();
    mutable_electricctrl->set_contactk5(eVSManageData.electriCtrlManageBuff.electricCtrl.contactK5);
    mutable_electricctrl->set_contactk6(eVSManageData.electriCtrlManageBuff.electricCtrl.contactK6);
    mutable_electricctrl->set_cc1_s2(eVSManageData.electriCtrlManageBuff.electricCtrl.CC1_S2);
    mutable_electricctrl->set_cc2_s3(eVSManageData.electriCtrlManageBuff.electricCtrl.CC2_S3);
    mutable_electricctrl->set_elockstate(eVSManageData.electriCtrlManageBuff.electricCtrl.elockState);
    mutable_electricctrl->set_canbus(eVSManageData.electriCtrlManageBuff.electricCtrl.canBus);
    mutable_electricctrl->set_ev_pe(eVSManageData.electriCtrlManageBuff.electricCtrl.ev_PE);
    mutable_electricctrl->set_evinsulton(eVSManageData.electriCtrlManageBuff.electricCtrl.evInsultOn);
    mutable_electricctrl->set_evcontactk6(eVSManageData.electriCtrlManageBuff.electricCtrl.evContactK6);
    mutable_electricctrl->set_evpause(eVSManageData.electriCtrlManageBuff.electricCtrl.evPause);
    mutable_electricctrl->set_sysfan(eVSManageData.electriCtrlManageBuff.electricCtrl.sysFan);

    //绝缘参数设置
    EVSInfo::EVInsultCtrl *mutable_insultctrl = ElecCtrlPro.mutable_insultctrl();
    mutable_insultctrl->set_insultposres(eVSManageData.electriCtrlManageBuff.insultCtrl.insultPosRes);
    mutable_insultctrl->set_insultnegres(eVSManageData.electriCtrlManageBuff.insultCtrl.insultNegRes);
    mutable_insultctrl->set_batvol(eVSManageData.electriCtrlManageBuff.insultCtrl.batVol);
    mutable_insultctrl->set_connecttype(eVSManageData.electriCtrlManageBuff.insultCtrl.connectType);

    //IP设置
    EVSInfo::IpMsg *mutable_ipmsg = ElecCtrlPro.mutable_ipmsg();
    mutable_ipmsg->set_selfip(eVSManageData.electriCtrlManageBuff.ipConfig.locolIP);
    mutable_ipmsg->set_selfport(eVSManageData.electriCtrlManageBuff.ipConfig.locolPort);
    mutable_ipmsg->set_serviceip(eVSManageData.electriCtrlManageBuff.ipConfig.serverIP);
    mutable_ipmsg->set_serviceport(eVSManageData.electriCtrlManageBuff.ipConfig.serverPort);

    uint32_t protocol_size = ElecCtrlPro.ByteSizeLong();

    uint8_t protocol_buff[protocol_size];

    ElecCtrlPro.SerializePartialToArray(protocol_buff, protocol_size);

    Loger(NORMAL,"ElecCtrlPro=%s\r\n",ElecCtrlPro.DebugString().c_str());

    result = SendMsg(dev,TYPE_ELECTRI_CTRL, (uint8_t*)protocol_buff, protocol_size);
    if (result != EVS_SUCCESS)
    {
        Loger(NORMAL, "send module[%d] register failed!\r\n",dev);
        return EVS_FAILED;
    }
    return result;

}

/**
 * @brief: EVS发送0x1E查询0x05步进信息
 *
 * @return {*}
 */
int32_t EVSProto::Send_StepInfoReport(DeviceTypeEnum dev)
{
    int32_t result = EVS_SUCCESS;
    gcu_evs_hmi::protobuf::EVSStepMsgSet  StepMsgPro;

    StepMsgPro.set_evsid(eVSManageData.getMsgInfoManageBuff.evsID);
    //步进信息
    EVSInfo::EVSStepMsg *mutable_stepmsg = StepMsgPro.mutable_stepmsg();
    //需求电压步进
    EVSInfo::StepPara *mutable_needvolstep =mutable_stepmsg->mutable_needvolstep();
    mutable_needvolstep->set_starttime(eVSManageData.stepMsgInfoManageBuff.stepMsg.needVolStep.startTime);
    mutable_needvolstep->set_intervaltime(eVSManageData.stepMsgInfoManageBuff.stepMsg.needVolStep.intervalTime);
    mutable_needvolstep->set_startvalue(eVSManageData.stepMsgInfoManageBuff.stepMsg.needVolStep.startValue);
    mutable_needvolstep->set_minvalue(eVSManageData.stepMsgInfoManageBuff.stepMsg.needVolStep.minValue);
    mutable_needvolstep->set_maxvalue(eVSManageData.stepMsgInfoManageBuff.stepMsg.needVolStep.maxValue);
    mutable_needvolstep->set_stepvalue(eVSManageData.stepMsgInfoManageBuff.stepMsg.needVolStep.stepValue);
    mutable_needvolstep->set_cyclemode(eVSManageData.stepMsgInfoManageBuff.stepMsg.needVolStep.cycleMode);
    mutable_needvolstep->set_stepmode(eVSManageData.stepMsgInfoManageBuff.stepMsg.needVolStep.stepMode);

    //需求电流步进
    EVSInfo::StepPara *mutable_needcurrstep =mutable_stepmsg->mutable_needcurrstep();
    mutable_needcurrstep->set_starttime(eVSManageData.stepMsgInfoManageBuff.stepMsg.needCurrStep.startTime);
    mutable_needcurrstep->set_intervaltime(eVSManageData.stepMsgInfoManageBuff.stepMsg.needCurrStep.intervalTime);
    mutable_needcurrstep->set_startvalue(eVSManageData.stepMsgInfoManageBuff.stepMsg.needCurrStep.startValue);
    mutable_needcurrstep->set_minvalue(eVSManageData.stepMsgInfoManageBuff.stepMsg.needCurrStep.minValue);
    mutable_needcurrstep->set_maxvalue(eVSManageData.stepMsgInfoManageBuff.stepMsg.needCurrStep.maxValue);
    mutable_needcurrstep->set_stepvalue(eVSManageData.stepMsgInfoManageBuff.stepMsg.needCurrStep.stepValue);
    mutable_needcurrstep->set_cyclemode(eVSManageData.stepMsgInfoManageBuff.stepMsg.needCurrStep.cycleMode);
    mutable_needcurrstep->set_stepmode(eVSManageData.stepMsgInfoManageBuff.stepMsg.needCurrStep.stepMode);

    //需求SOC步进
    EVSInfo::StepPara *mutable_socstep =mutable_stepmsg->mutable_socstep();
    mutable_socstep->set_starttime(eVSManageData.stepMsgInfoManageBuff.stepMsg.socStep.startTime);
    mutable_socstep->set_intervaltime(eVSManageData.stepMsgInfoManageBuff.stepMsg.socStep.intervalTime);
    mutable_socstep->set_startvalue(eVSManageData.stepMsgInfoManageBuff.stepMsg.socStep.startValue);
    mutable_socstep->set_minvalue(eVSManageData.stepMsgInfoManageBuff.stepMsg.socStep.minValue);
    mutable_socstep->set_maxvalue(eVSManageData.stepMsgInfoManageBuff.stepMsg.socStep.maxValue);
    mutable_socstep->set_stepvalue(eVSManageData.stepMsgInfoManageBuff.stepMsg.socStep.stepValue);
    mutable_socstep->set_cyclemode(eVSManageData.stepMsgInfoManageBuff.stepMsg.socStep.cycleMode);
    mutable_socstep->set_stepmode(eVSManageData.stepMsgInfoManageBuff.stepMsg.socStep.stepMode);

    //单体电压步进
    EVSInfo::StepPara *mutable_cellvolstep =mutable_stepmsg->mutable_cellvolstep();
    mutable_cellvolstep->set_starttime(eVSManageData.stepMsgInfoManageBuff.stepMsg.cellVolStep.startTime);
    mutable_cellvolstep->set_intervaltime(eVSManageData.stepMsgInfoManageBuff.stepMsg.cellVolStep.intervalTime);
    mutable_cellvolstep->set_startvalue(eVSManageData.stepMsgInfoManageBuff.stepMsg.cellVolStep.startValue);
    mutable_cellvolstep->set_minvalue(eVSManageData.stepMsgInfoManageBuff.stepMsg.cellVolStep.minValue);
    mutable_cellvolstep->set_maxvalue(eVSManageData.stepMsgInfoManageBuff.stepMsg.cellVolStep.maxValue);
    mutable_cellvolstep->set_stepvalue(eVSManageData.stepMsgInfoManageBuff.stepMsg.cellVolStep.stepValue);
    mutable_cellvolstep->set_cyclemode(eVSManageData.stepMsgInfoManageBuff.stepMsg.cellVolStep.cycleMode);
    mutable_cellvolstep->set_stepmode(eVSManageData.stepMsgInfoManageBuff.stepMsg.cellVolStep.stepMode);

    //单体温度步进
    EVSInfo::StepPara *mutable_celltempstep =mutable_stepmsg->mutable_celltempstep();
    mutable_celltempstep->set_starttime(eVSManageData.stepMsgInfoManageBuff.stepMsg.cellTempStep.startTime);
    mutable_celltempstep->set_intervaltime(eVSManageData.stepMsgInfoManageBuff.stepMsg.cellTempStep.intervalTime);
    mutable_celltempstep->set_startvalue(eVSManageData.stepMsgInfoManageBuff.stepMsg.cellTempStep.startValue);
    mutable_celltempstep->set_minvalue(eVSManageData.stepMsgInfoManageBuff.stepMsg.cellTempStep.minValue);
    mutable_celltempstep->set_maxvalue(eVSManageData.stepMsgInfoManageBuff.stepMsg.cellTempStep.maxValue);
    mutable_celltempstep->set_stepvalue(eVSManageData.stepMsgInfoManageBuff.stepMsg.cellTempStep.stepValue);
    mutable_celltempstep->set_cyclemode(eVSManageData.stepMsgInfoManageBuff.stepMsg.cellTempStep.cycleMode);
    mutable_celltempstep->set_stepmode(eVSManageData.stepMsgInfoManageBuff.stepMsg.cellTempStep.stepMode);


    uint32_t protocol_size = StepMsgPro.ByteSizeLong();

    uint8_t protocol_buff[protocol_size];

    StepMsgPro.SerializePartialToArray(protocol_buff, protocol_size);

    Loger(NORMAL,"StepMsgPro=%s\r\n",StepMsgPro.DebugString().c_str());

    result = SendMsg(dev,TYPE_STEP_INFO, (uint8_t*)protocol_buff, protocol_size);
    if (result != EVS_SUCCESS)
    {
        Loger(NORMAL, "send module[%d] register failed!\r\n",dev);
        return EVS_FAILED;
    }
    return result;

}

/**
 * @brief: EVS注册帧ack(0x10)  hmi-> evs
 *
 * @return {*}
 */

int32_t EVSProto::Recv_EvsModuleRegisterAck(uint8_t *buff,uint32_t size)
{
    int32_t result = EVS_SUCCESS;
    int8_t ret = 0;
    gcu_evs_hmi::protobuf::EVSLoginAns  eVSLoginAns;
    ret = eVSLoginAns.ParseFromArray(buff, size);
    if (ret == 0)
    {
        Loger(NORMAL, "Recv_EvsModuleRegisterAck  eVSLoginAns ParseFromArray failed!\r\n");
        result =  EVS_FAILED;
    }
    else
    {
        evsModuleRegisterAckBuff.evsID = eVSLoginAns.evsid();
        evsModuleRegisterAckBuff.serverID = eVSLoginAns.serverid();
        evsModuleRegisterAckBuff.interval = eVSLoginAns.interval();

        //Loger(NORMAL, "eVSLoginAns.DebugString:\r\n%s",eVSLoginAns.DebugString().c_str());
        //std::cout<<"eVSLoginAns.DebugString().c_str()"<<eVSLoginAns.DebugString().c_str()<<std::endl;
    }
    return result;
}

/**
 * @brief: EVS控制指令(0x11)  hmi-> evs
 *
 * @return {*}
 */
int32_t EVSProto::Recv_ControlCmd(uint8_t *buff,uint32_t size)
{
    int32_t result = EVS_SUCCESS;
    int8_t ret = 0;
    gcu_evs_hmi::protobuf::EVSHbReply  eVSHbReply;
    ret = eVSHbReply.ParseFromArray(buff, size);
    if (ret == 0)
    {
        Loger(NORMAL, "Recv_ControlCmd eVSHbReply ParseFromArray failed!\r\n");
        result =  EVS_FAILED;
    }
    else
    {
        controlCmdBuff.evsID = eVSHbReply.evsid();
        controlCmdBuff.serverID = eVSHbReply.serverid();
        controlCmdBuff.heartbeatCnt = eVSHbReply.heartbeatcnt();
        controlCmdBuff.evsCmd = eVSHbReply.evscmd();
        controlCmdBuff.offlineMode = eVSHbReply.offlinemode();
        controlCmdBuff.evsReset = eVSHbReply.evsreset();

        //心跳计数清0
        if(controlCmdBuff.serverID-1 == E_DEV_EVS)
        {
            protocolCounter[E_DEV_EVS] = 0; 
        }
        else if (controlCmdBuff.serverID-1 == E_DEV_HMI)
        {
            protocolCounter[E_DEV_HMI] = 0; 
        }
        // std::cout<<"eVSHbReply.DebugString().c_str()"<<eVSHbReply.DebugString().c_str()<<std::endl;
        // Loger(NORMAL, "evsHb.DebugString:\r\n%s",eVSHbReply.DebugString().c_str());
    }
    return result;
}

/**
 * @brief: EVS报文填充(0x2) hmi->evs
 *
 * @return {*}
 */

int32_t EVSProto::Recv_VehicleSideMsgFill(uint8_t *buff,uint32_t size)
{
    int32_t result = EVS_SUCCESS;
    int8_t ret = 0;
    gcu_evs_hmi::protobuf::EVSMsgSet  eVSMsgSet;
    ret = eVSMsgSet.ParseFromArray(buff, size);
    if (ret == 0)
    {
        Loger(NORMAL, "Recv_VehicleSideMsgFill eVSMsgSet ParseFromArray failed!\r\n");
        ret =  EVS_FAILED;
    }
    else
    {
        // 模拟器ID
        vehicleSideMsgFillBuff.evsID = eVSMsgSet.evsid();

        // 版本协商信息
        vehicleSideMsgFillBuff.protoConferM.canType = eVSMsgSet.protoconferm().cantype();
        vehicleSideMsgFillBuff.protoConferM.gbVersion = eVSMsgSet.protoconferm().gbversion();   
        vehicleSideMsgFillBuff.protoConferM.guidanceVersion = eVSMsgSet.protoconferm().guidanceversion();
        vehicleSideMsgFillBuff.protoConferM.transportVersion = eVSMsgSet.protoconferm().transportversion();
        vehicleSideMsgFillBuff.protoConferM.conferRes = eVSMsgSet.protoconferm().conferres();

        // 功能协商
        vehicleSideMsgFillBuff.funConferM.configFDC = eVSMsgSet.funconferm().configfdc();
        vehicleSideMsgFillBuff.funConferM.authenFDC = eVSMsgSet.funconferm().authenfdc();
        vehicleSideMsgFillBuff.funConferM.appointFDC = eVSMsgSet.funconferm().appointfdc();
        vehicleSideMsgFillBuff.funConferM.selfCheckFDC = eVSMsgSet.funconferm().selfcheckfdc();
        vehicleSideMsgFillBuff.funConferM.powerSupplyFDC = eVSMsgSet.funconferm().powersupplyfdc();
        vehicleSideMsgFillBuff.funConferM.energyTransferFDC = eVSMsgSet.funconferm().energytransferfdc();
        vehicleSideMsgFillBuff.funConferM.endFDC = eVSMsgSet.funconferm().endfdc();
        Loger(NORMAL, "User set fun FDC : config = %d,auth = %d,appoint =%d, check = %d, supply = %d,\
             energy = %d,end = %d",vehicleSideMsgFillBuff.funConferM.configFDC,vehicleSideMsgFillBuff.funConferM.authenFDC,
             vehicleSideMsgFillBuff.funConferM.appointFDC, vehicleSideMsgFillBuff.funConferM.selfCheckFDC,
             vehicleSideMsgFillBuff.funConferM.powerSupplyFDC,vehicleSideMsgFillBuff.funConferM.energyTransferFDC,
             vehicleSideMsgFillBuff.funConferM.endFDC);

        // 车辆充电参数配置
        vehicleSideMsgFillBuff.paraConfigM.currAllowMAX = eVSMsgSet.paraconfigm().currallowmax();
        vehicleSideMsgFillBuff.paraConfigM.voltAllowMAX = eVSMsgSet.paraconfigm().voltallowmax();
        vehicleSideMsgFillBuff.paraConfigM.energyAllowMAX = eVSMsgSet.paraconfigm().energyallowmax();
        vehicleSideMsgFillBuff.paraConfigM.nowSOC = eVSMsgSet.paraconfigm().nowsoc();
        vehicleSideMsgFillBuff.paraConfigM.cellVoltAllowMAX = eVSMsgSet.paraconfigm().cellvoltallowmax();
        vehicleSideMsgFillBuff.paraConfigM.batTempAllowMAX = eVSMsgSet.paraconfigm().battempallowmax();
        vehicleSideMsgFillBuff.paraConfigM.restarNum = eVSMsgSet.paraconfigm().restarnum();

        // 鉴权信息
        vehicleSideMsgFillBuff.authenM.authenWaitTime = eVSMsgSet.authenm().authenwaittime();
        vehicleSideMsgFillBuff.authenM.eVIN = eVSMsgSet.authenm().evin();
        vehicleSideMsgFillBuff.authenM.nextFDC = eVSMsgSet.authenm().nextfdc();

        // 预约充电信息 
        vehicleSideMsgFillBuff.reserveM.bmsDesireStartTime = eVSMsgSet.reservem().bmsdesirestarttime();
        vehicleSideMsgFillBuff.reserveM.bmsDesireLeaveTime = eVSMsgSet.reservem().bmsdesireleavetime();
        vehicleSideMsgFillBuff.reserveM.reserveResult = eVSMsgSet.reservem().reserveresult();
        vehicleSideMsgFillBuff.reserveM.immediateChargeSupport = eVSMsgSet.reservem().immediatechargesupport();

        //// 供电模式 
        vehicleSideMsgFillBuff.powerSupplyM.supplyState = eVSMsgSet.powersupplym().supplystate();
        vehicleSideMsgFillBuff.powerSupplyM.supplyVolDesire = eVSMsgSet.powersupplym().supplyvoldesire();
        vehicleSideMsgFillBuff.powerSupplyM.supplyCurrDesire = eVSMsgSet.powersupplym().supplycurrdesire();
        vehicleSideMsgFillBuff.powerSupplyM.supplyEnd = eVSMsgSet.powersupplym().supplyend();

        // 充电阶段信息 
        vehicleSideMsgFillBuff.chargingM.bmsReady = eVSMsgSet.chargingm().bmsready();
        vehicleSideMsgFillBuff.chargingM.volDemand = eVSMsgSet.chargingm().voldemand();
        vehicleSideMsgFillBuff.chargingM.curDemand = eVSMsgSet.chargingm().curdemand();
        vehicleSideMsgFillBuff.chargingM.chargeMode = eVSMsgSet.chargingm().chargemode();
        vehicleSideMsgFillBuff.chargingM.socNow = eVSMsgSet.chargingm().socnow();
        vehicleSideMsgFillBuff.chargingM.resChgTime = eVSMsgSet.chargingm().reschgtime();
        vehicleSideMsgFillBuff.chargingM.cellBatVolMax = eVSMsgSet.chargingm().cellbatvolmax();
        vehicleSideMsgFillBuff.chargingM.cellBatVolMin = eVSMsgSet.chargingm().cellbatvolmin();
        vehicleSideMsgFillBuff.chargingM.celltempMax = eVSMsgSet.chargingm().celltempmax();
        vehicleSideMsgFillBuff.chargingM.celltempMin = eVSMsgSet.chargingm().celltempmin();
      
        // 充电结束信息
        vehicleSideMsgFillBuff.chargingEndM.endType = eVSMsgSet.chargingendm().endtype();
        vehicleSideMsgFillBuff.chargingEndM.endCode = eVSMsgSet.chargingendm().endcode();
        vehicleSideMsgFillBuff.chargingEndM.endReason = eVSMsgSet.chargingendm().endreason();
        vehicleSideMsgFillBuff.chargingEndM.repeat = eVSMsgSet.chargingendm().repeat();
        vehicleSideMsgFillBuff.chargingEndM.bmsStickCheckState = eVSMsgSet.chargingendm().bmsstickcheckstate();

        // std::cout<<"eVSMsgSet.DebugString().c_str()"<<eVSMsgSet.DebugString().c_str()<<std::endl;
        // Loger(NORMAL, "eVSMsgSet.DebugString:\r\n%s",eVSMsgSet.DebugString().c_str());
    }
    return result;
}

/**
 * @brief: EVS报文控制(0x3) hmi->evs
 *
 * @return {*}
 */
int32_t EVSProto::Recv_MsgAttrInfo(uint8_t *buff,uint32_t size)
{
    int32_t result = EVS_SUCCESS;
    int8_t ret = 0;
    gcu_evs_hmi::protobuf::EVSMsgCtrl  eVSMsgCtrl;
    ret = eVSMsgCtrl.ParseFromArray(buff, size);
    if (ret == 0)
    {
        Loger(NORMAL, "Recv_MsgAttrInfo eVSMsgCtrl ParseFromArray failed!\r\n");
        result =  EVS_FAILED;
    }
    else
    {
        msgAttrInfoBuff.evsID = eVSMsgCtrl.evsid();

        //msgCtrl
        //x
        msgAttrInfoBuff.msgCtrl.x2_0x02_State = eVSMsgCtrl.msgctrl().x2_0x02_state();
        msgAttrInfoBuff.msgCtrl.x4_0x04_State = eVSMsgCtrl.msgctrl().x4_0x04_state();
        msgAttrInfoBuff.msgCtrl.x6_0x06_State = eVSMsgCtrl.msgctrl().x6_0x06_state();
        msgAttrInfoBuff.msgCtrl.x9_0x09_State = eVSMsgCtrl.msgctrl().x9_0x09_state();
        //b
        msgAttrInfoBuff.msgCtrl.b2_0x12_State = eVSMsgCtrl.msgctrl().b2_0x12_state();
        //c
        msgAttrInfoBuff.msgCtrl.c2_0x22_State = eVSMsgCtrl.msgctrl().c2_0x22_state();
        msgAttrInfoBuff.msgCtrl.c4_0x24_State = eVSMsgCtrl.msgctrl().c4_0x24_state();
        //d
        msgAttrInfoBuff.msgCtrl.d2_0x32_State = eVSMsgCtrl.msgctrl().d2_0x32_state();
        msgAttrInfoBuff.msgCtrl.d4_0x34_State = eVSMsgCtrl.msgctrl().d4_0x34_state();
        msgAttrInfoBuff.msgCtrl.d6_0x36_State = eVSMsgCtrl.msgctrl().d6_0x36_state();
        msgAttrInfoBuff.msgCtrl.d7_0x37_State = eVSMsgCtrl.msgctrl().d7_0x37_state();
        msgAttrInfoBuff.msgCtrl.d9_0x39_State = eVSMsgCtrl.msgctrl().d9_0x39_state();
        msgAttrInfoBuff.msgCtrl.d10_0x3A_State = eVSMsgCtrl.msgctrl().d10_0x3a_state();
        //e
        msgAttrInfoBuff.msgCtrl.e2_0x42_State = eVSMsgCtrl.msgctrl().e2_0x42_state();
        msgAttrInfoBuff.msgCtrl.e4_0x44_State = eVSMsgCtrl.msgctrl().e4_0x44_state();
        //f
        msgAttrInfoBuff.msgCtrl.f2_0x52_State = eVSMsgCtrl.msgctrl().f2_0x52_state();
        //g
        msgAttrInfoBuff.msgCtrl.g2_0x62_State = eVSMsgCtrl.msgctrl().g2_0x62_state();
        msgAttrInfoBuff.msgCtrl.g3_0x63_State = eVSMsgCtrl.msgctrl().g3_0x63_state();
        msgAttrInfoBuff.msgCtrl.g5_0x65_State = eVSMsgCtrl.msgctrl().g5_0x65_state();
        //h
        msgAttrInfoBuff.msgCtrl.h2_0x72_State = eVSMsgCtrl.msgctrl().h2_0x72_state();
        msgAttrInfoBuff.msgCtrl.h3_0x73_State = eVSMsgCtrl.msgctrl().h3_0x73_state();
        msgAttrInfoBuff.msgCtrl.h4_0x74_State = eVSMsgCtrl.msgctrl().h4_0x74_state();
        msgAttrInfoBuff.msgCtrl.h7_0x77_State = eVSMsgCtrl.msgctrl().h7_0x77_state();
        msgAttrInfoBuff.msgCtrl.h9_0x79_State = eVSMsgCtrl.msgctrl().h9_0x79_state();
        msgAttrInfoBuff.msgCtrl.h11_0x82_State = eVSMsgCtrl.msgctrl().h11_0x82_state();
        msgAttrInfoBuff.msgCtrl.h13_0x84_State = eVSMsgCtrl.msgctrl().h13_0x84_state();
        msgAttrInfoBuff.msgCtrl.h14_0x85_State = eVSMsgCtrl.msgctrl().h14_0x85_state();
        msgAttrInfoBuff.msgCtrl.h16_0x87_State = eVSMsgCtrl.msgctrl().h16_0x87_state();
        msgAttrInfoBuff.msgCtrl.h18_0x89_State = eVSMsgCtrl.msgctrl().h18_0x89_state();
        msgAttrInfoBuff.msgCtrl.h20_0x8B_State = eVSMsgCtrl.msgctrl().h20_0x8b_state();
        //i
        msgAttrInfoBuff.msgCtrl.i1_0x91_State = eVSMsgCtrl.msgctrl().i1_0x91_state();
        msgAttrInfoBuff.msgCtrl.i4_0x94_State = eVSMsgCtrl.msgctrl().i4_0x94_state();


        //msgCycleTime
        msgAttrInfoBuff.msgCycleTime.x2_0x02_State = eVSMsgCtrl.msgcycletime().x2_0x02_state();
        msgAttrInfoBuff.msgCycleTime.x4_0x04_State = eVSMsgCtrl.msgcycletime().x4_0x04_state();
        msgAttrInfoBuff.msgCycleTime.x6_0x06_State = eVSMsgCtrl.msgcycletime().x6_0x06_state();
        msgAttrInfoBuff.msgCycleTime.x9_0x09_State = eVSMsgCtrl.msgcycletime().x9_0x09_state();
        //b
        msgAttrInfoBuff.msgCycleTime.b2_0x12_State = eVSMsgCtrl.msgcycletime().b2_0x12_state();
        //c
        msgAttrInfoBuff.msgCycleTime.c2_0x22_State = eVSMsgCtrl.msgcycletime().c2_0x22_state();
        msgAttrInfoBuff.msgCycleTime.c4_0x24_State = eVSMsgCtrl.msgcycletime().c4_0x24_state();
        //d
        msgAttrInfoBuff.msgCycleTime.d2_0x32_State = eVSMsgCtrl.msgcycletime().d2_0x32_state();
        msgAttrInfoBuff.msgCycleTime.d4_0x34_State = eVSMsgCtrl.msgcycletime().d4_0x34_state();
        msgAttrInfoBuff.msgCycleTime.d6_0x36_State = eVSMsgCtrl.msgcycletime().d6_0x36_state();
        msgAttrInfoBuff.msgCycleTime.d7_0x37_State = eVSMsgCtrl.msgcycletime().d7_0x37_state();
        msgAttrInfoBuff.msgCycleTime.d9_0x39_State = eVSMsgCtrl.msgcycletime().d9_0x39_state();
        msgAttrInfoBuff.msgCycleTime.d10_0x3A_State = eVSMsgCtrl.msgcycletime().d10_0x3a_state();
        //e
        msgAttrInfoBuff.msgCycleTime.e2_0x42_State = eVSMsgCtrl.msgcycletime().e2_0x42_state();
        msgAttrInfoBuff.msgCycleTime.e4_0x44_State = eVSMsgCtrl.msgcycletime().e4_0x44_state();
        //f
        msgAttrInfoBuff.msgCycleTime.f2_0x52_State = eVSMsgCtrl.msgcycletime().f2_0x52_state();
        //g
        msgAttrInfoBuff.msgCycleTime.g2_0x62_State = eVSMsgCtrl.msgcycletime().g2_0x62_state();
        msgAttrInfoBuff.msgCycleTime.g3_0x63_State = eVSMsgCtrl.msgcycletime().g3_0x63_state();
        msgAttrInfoBuff.msgCycleTime.g5_0x65_State = eVSMsgCtrl.msgcycletime().g5_0x65_state();
        //h
        msgAttrInfoBuff.msgCycleTime.h2_0x72_State = eVSMsgCtrl.msgcycletime().h2_0x72_state();
        msgAttrInfoBuff.msgCycleTime.h3_0x73_State = eVSMsgCtrl.msgcycletime().h3_0x73_state();
        msgAttrInfoBuff.msgCycleTime.h4_0x74_State = eVSMsgCtrl.msgcycletime().h4_0x74_state();
        msgAttrInfoBuff.msgCycleTime.h7_0x77_State = eVSMsgCtrl.msgcycletime().h7_0x77_state();
        msgAttrInfoBuff.msgCycleTime.h9_0x79_State = eVSMsgCtrl.msgcycletime().h9_0x79_state();
        msgAttrInfoBuff.msgCycleTime.h11_0x82_State = eVSMsgCtrl.msgcycletime().h11_0x82_state();
        msgAttrInfoBuff.msgCycleTime.h13_0x84_State = eVSMsgCtrl.msgcycletime().h13_0x84_state();
        msgAttrInfoBuff.msgCycleTime.h14_0x85_State = eVSMsgCtrl.msgcycletime().h14_0x85_state();
        msgAttrInfoBuff.msgCycleTime.h16_0x87_State = eVSMsgCtrl.msgcycletime().h16_0x87_state();
        msgAttrInfoBuff.msgCycleTime.h18_0x89_State = eVSMsgCtrl.msgcycletime().h18_0x89_state();
        msgAttrInfoBuff.msgCycleTime.h20_0x8B_State = eVSMsgCtrl.msgcycletime().h20_0x8b_state();
        //i
        msgAttrInfoBuff.msgCycleTime.i1_0x91_State = eVSMsgCtrl.msgcycletime().i1_0x91_state();
        msgAttrInfoBuff.msgCycleTime.i4_0x94_State = eVSMsgCtrl.msgcycletime().i4_0x94_state();


        //msgMaxSendTIme
        msgAttrInfoBuff.msgMaxSendTIme.x2_0x02_State = eVSMsgCtrl.msgmaxsendtime().x2_0x02_state();
        msgAttrInfoBuff.msgMaxSendTIme.x4_0x04_State = eVSMsgCtrl.msgmaxsendtime().x4_0x04_state();
        msgAttrInfoBuff.msgMaxSendTIme.x6_0x06_State = eVSMsgCtrl.msgmaxsendtime().x6_0x06_state();
        msgAttrInfoBuff.msgMaxSendTIme.x9_0x09_State = eVSMsgCtrl.msgmaxsendtime().x9_0x09_state();
        //b
        msgAttrInfoBuff.msgMaxSendTIme.b2_0x12_State = eVSMsgCtrl.msgmaxsendtime().b2_0x12_state();
        //c
        msgAttrInfoBuff.msgMaxSendTIme.c2_0x22_State = eVSMsgCtrl.msgmaxsendtime().c2_0x22_state();
        msgAttrInfoBuff.msgMaxSendTIme.c4_0x24_State = eVSMsgCtrl.msgmaxsendtime().c4_0x24_state();
        //d
        msgAttrInfoBuff.msgMaxSendTIme.d2_0x32_State = eVSMsgCtrl.msgmaxsendtime().d2_0x32_state();
        msgAttrInfoBuff.msgMaxSendTIme.d4_0x34_State = eVSMsgCtrl.msgmaxsendtime().d4_0x34_state();
        msgAttrInfoBuff.msgMaxSendTIme.d6_0x36_State = eVSMsgCtrl.msgmaxsendtime().d6_0x36_state();
        msgAttrInfoBuff.msgMaxSendTIme.d7_0x37_State = eVSMsgCtrl.msgmaxsendtime().d7_0x37_state();
        msgAttrInfoBuff.msgMaxSendTIme.d9_0x39_State = eVSMsgCtrl.msgmaxsendtime().d9_0x39_state();
        msgAttrInfoBuff.msgMaxSendTIme.d10_0x3A_State = eVSMsgCtrl.msgmaxsendtime().d10_0x3a_state();
        //e
        msgAttrInfoBuff.msgMaxSendTIme.e2_0x42_State = eVSMsgCtrl.msgmaxsendtime().e2_0x42_state();
        msgAttrInfoBuff.msgMaxSendTIme.e4_0x44_State = eVSMsgCtrl.msgmaxsendtime().e4_0x44_state();
        //f
        msgAttrInfoBuff.msgMaxSendTIme.f2_0x52_State = eVSMsgCtrl.msgmaxsendtime().f2_0x52_state();
        //g
        msgAttrInfoBuff.msgMaxSendTIme.g2_0x62_State = eVSMsgCtrl.msgmaxsendtime().g2_0x62_state();
        msgAttrInfoBuff.msgMaxSendTIme.g3_0x63_State = eVSMsgCtrl.msgmaxsendtime().g3_0x63_state();
        msgAttrInfoBuff.msgMaxSendTIme.g5_0x65_State = eVSMsgCtrl.msgmaxsendtime().g5_0x65_state();
        //h
        msgAttrInfoBuff.msgMaxSendTIme.h2_0x72_State = eVSMsgCtrl.msgmaxsendtime().h2_0x72_state();
        msgAttrInfoBuff.msgMaxSendTIme.h3_0x73_State = eVSMsgCtrl.msgmaxsendtime().h3_0x73_state();
        msgAttrInfoBuff.msgMaxSendTIme.h4_0x74_State = eVSMsgCtrl.msgmaxsendtime().h4_0x74_state();
        msgAttrInfoBuff.msgMaxSendTIme.h7_0x77_State = eVSMsgCtrl.msgmaxsendtime().h7_0x77_state();
        msgAttrInfoBuff.msgMaxSendTIme.h9_0x79_State = eVSMsgCtrl.msgmaxsendtime().h9_0x79_state();
        msgAttrInfoBuff.msgMaxSendTIme.h11_0x82_State = eVSMsgCtrl.msgmaxsendtime().h11_0x82_state();
        msgAttrInfoBuff.msgMaxSendTIme.h13_0x84_State = eVSMsgCtrl.msgmaxsendtime().h13_0x84_state();
        msgAttrInfoBuff.msgMaxSendTIme.h14_0x85_State = eVSMsgCtrl.msgmaxsendtime().h14_0x85_state();
        msgAttrInfoBuff.msgMaxSendTIme.h16_0x87_State = eVSMsgCtrl.msgmaxsendtime().h16_0x87_state();
        msgAttrInfoBuff.msgMaxSendTIme.h18_0x89_State = eVSMsgCtrl.msgmaxsendtime().h18_0x89_state();
        msgAttrInfoBuff.msgMaxSendTIme.h20_0x8B_State = eVSMsgCtrl.msgmaxsendtime().h20_0x8b_state();
        //i
        msgAttrInfoBuff.msgMaxSendTIme.i1_0x91_State = eVSMsgCtrl.msgmaxsendtime().i1_0x91_state();
        msgAttrInfoBuff.msgMaxSendTIme.i4_0x94_State = eVSMsgCtrl.msgmaxsendtime().i4_0x94_state();


        // 阶段确认报文设置
        msgAttrInfoBuff.funConferAck.funConferAck = eVSMsgCtrl.funconferack().funconferack();
        msgAttrInfoBuff.funConferAck.configAck = eVSMsgCtrl.funconferack().configack();
        msgAttrInfoBuff.funConferAck.authenAck = eVSMsgCtrl.funconferack().authenack();
        msgAttrInfoBuff.funConferAck.appointAck = eVSMsgCtrl.funconferack().appointack();
        msgAttrInfoBuff.funConferAck.selfCheckAck = eVSMsgCtrl.funconferack().selfcheckack();
        msgAttrInfoBuff.funConferAck.powerSupplyAck = eVSMsgCtrl.funconferack().powersupplyack();
        msgAttrInfoBuff.funConferAck.energyTransferAck = eVSMsgCtrl.funconferack().energytransferack();
        msgAttrInfoBuff.funConferAck.endAck = eVSMsgCtrl.funconferack().endack();
     
        // Loger(NORMAL, "eVSMsgCtrl.DebugString:\r\n%s",eVSMsgCtrl.DebugString().c_str());
        // std::cout<<"eVSMsgCtrl.DebugString().c_str()"<<eVSMsgCtrl.DebugString().c_str()<<std::endl;
    }
    return result;
}

/**
 * @brief: EVS电气状态控制(0x4) hmi->evs
 *
 * @return {*}
 */
int32_t EVSProto::Recv_ElectriCtrl(uint8_t *buff,uint32_t size)
{
    int32_t result = EVS_SUCCESS;
    int8_t ret = 0;
    gcu_evs_hmi::protobuf::EVSSysCtrl  eVSElectricCtrl;
    ret = eVSElectricCtrl.ParseFromArray(buff, size);
    if (ret == 0)
    {
        Loger(NORMAL, "Recv_ElectriCtrl eVSElectricCtrl ParseFromArray failed!\r\n");
        result =  EVS_FAILED;
    }
    else
    {
        electriCtrlBuff.evsID = eVSElectricCtrl.evsid();
        // 车辆电气控制
        electriCtrlBuff.electricCtrl.contactK5 = eVSElectricCtrl.electricctrl().contactk5();
        electriCtrlBuff.electricCtrl.contactK6 = eVSElectricCtrl.electricctrl().contactk6();
        electriCtrlBuff.electricCtrl.CC1_S2 = eVSElectricCtrl.electricctrl().cc1_s2();
        electriCtrlBuff.electricCtrl.CC2_S3 = eVSElectricCtrl.electricctrl().cc2_s3();
        electriCtrlBuff.electricCtrl.elockState = eVSElectricCtrl.electricctrl().elockstate();
        electriCtrlBuff.electricCtrl.canBus = eVSElectricCtrl.electricctrl().canbus();
        electriCtrlBuff.electricCtrl.ev_PE = eVSElectricCtrl.electricctrl().ev_pe();
        electriCtrlBuff.electricCtrl.evInsultOn = eVSElectricCtrl.electricctrl().evinsulton();
        electriCtrlBuff.electricCtrl.evContactK6 = eVSElectricCtrl.electricctrl().evcontactk6();
        electriCtrlBuff.electricCtrl.evPause = eVSElectricCtrl.electricctrl().evpause();
        electriCtrlBuff.electricCtrl.sysFan = eVSElectricCtrl.electricctrl().sysfan();

        // 绝缘参数设置
        electriCtrlBuff.insultCtrl.insultPosRes = eVSElectricCtrl.insultctrl().insultposres();
        electriCtrlBuff.insultCtrl.insultNegRes = eVSElectricCtrl.insultctrl().insultnegres();
        electriCtrlBuff.insultCtrl.batVol = eVSElectricCtrl.insultctrl().batvol();
        electriCtrlBuff.insultCtrl.connectType = eVSElectricCtrl.insultctrl().connecttype();

        //IP参数设置
        electriCtrlBuff.ipConfig.locolIP = eVSElectricCtrl.ipmsg().selfip();
        electriCtrlBuff.ipConfig.locolPort = eVSElectricCtrl.ipmsg().selfport();
        electriCtrlBuff.ipConfig.serverIP = eVSElectricCtrl.ipmsg().serviceip();
        electriCtrlBuff.ipConfig.serverPort = eVSElectricCtrl.ipmsg().serviceport();
      
        // std::cout<<"eVSElectricCtrl.DebugString().c_str()"<<eVSElectricCtrl.DebugString().c_str()<<std::endl;
        Loger(NORMAL, "eVSElectricCtrl.DebugString:\r\n%s",eVSElectricCtrl.DebugString().c_str());
    }
    return result;
}

/**
 * @brief: EVS步进信息设置(0x5) hmi->evs
 *
 * @return {*}
 */

int32_t EVSProto::Recv_StepMsgInfo(uint8_t *buff,uint32_t size)
{
    int32_t result = EVS_SUCCESS;
    int8_t ret = 0;
    gcu_evs_hmi::protobuf::EVSStepMsgSet  eVSStepMsgSet;
    ret = eVSStepMsgSet.ParseFromArray(buff, size);
    if (ret == 0)
    {
        Loger(NORMAL, "Recv_StepMsgInfo  eVSStepMsgSet ParseFromArray failed!\r\n");
        result =  EVS_FAILED;
    }
    else
    {
        stepMsgInfoBuff.evsID = eVSStepMsgSet.evsid();
        // 步进信息
        stepMsgInfoBuff.stepMsg.needVolStep.startTime = eVSStepMsgSet.stepmsg().needvolstep().starttime();
        stepMsgInfoBuff.stepMsg.needVolStep.intervalTime = eVSStepMsgSet.stepmsg().needvolstep().intervaltime();
        stepMsgInfoBuff.stepMsg.needVolStep.startValue = eVSStepMsgSet.stepmsg().needvolstep().startvalue();
        stepMsgInfoBuff.stepMsg.needVolStep.minValue = eVSStepMsgSet.stepmsg().needvolstep().minvalue();
        stepMsgInfoBuff.stepMsg.needVolStep.maxValue = eVSStepMsgSet.stepmsg().needvolstep().maxvalue();
        stepMsgInfoBuff.stepMsg.needVolStep.stepValue = eVSStepMsgSet.stepmsg().needvolstep().stepvalue();
        stepMsgInfoBuff.stepMsg.needVolStep.cycleMode = eVSStepMsgSet.stepmsg().needvolstep().cyclemode();
        stepMsgInfoBuff.stepMsg.needVolStep.stepMode = eVSStepMsgSet.stepmsg().needvolstep().stepmode();

        // 需求电流步进
        stepMsgInfoBuff.stepMsg.needCurrStep.startTime = eVSStepMsgSet.stepmsg().needcurrstep().starttime();
        stepMsgInfoBuff.stepMsg.needCurrStep.intervalTime = eVSStepMsgSet.stepmsg().needcurrstep().intervaltime();
        stepMsgInfoBuff.stepMsg.needCurrStep.startValue = eVSStepMsgSet.stepmsg().needcurrstep().startvalue();
        stepMsgInfoBuff.stepMsg.needCurrStep.minValue = eVSStepMsgSet.stepmsg().needcurrstep().minvalue();
        stepMsgInfoBuff.stepMsg.needCurrStep.maxValue = eVSStepMsgSet.stepmsg().needcurrstep().maxvalue();
        stepMsgInfoBuff.stepMsg.needCurrStep.stepValue = eVSStepMsgSet.stepmsg().needcurrstep().stepvalue();
        stepMsgInfoBuff.stepMsg.needCurrStep.cycleMode = eVSStepMsgSet.stepmsg().needcurrstep().cyclemode();
        stepMsgInfoBuff.stepMsg.needCurrStep.stepMode = eVSStepMsgSet.stepmsg().needcurrstep().stepmode();

        // soc步进
        stepMsgInfoBuff.stepMsg.socStep.startTime = eVSStepMsgSet.stepmsg().socstep().starttime();
        stepMsgInfoBuff.stepMsg.socStep.intervalTime = eVSStepMsgSet.stepmsg().socstep().intervaltime();
        stepMsgInfoBuff.stepMsg.socStep.startValue = eVSStepMsgSet.stepmsg().socstep().startvalue();
        stepMsgInfoBuff.stepMsg.socStep.minValue = eVSStepMsgSet.stepmsg().socstep().minvalue();
        stepMsgInfoBuff.stepMsg.socStep.maxValue = eVSStepMsgSet.stepmsg().socstep().maxvalue();
        stepMsgInfoBuff.stepMsg.socStep.stepValue = eVSStepMsgSet.stepmsg().socstep().stepvalue();
        stepMsgInfoBuff.stepMsg.socStep.cycleMode = eVSStepMsgSet.stepmsg().socstep().cyclemode();
        stepMsgInfoBuff.stepMsg.socStep.stepMode = eVSStepMsgSet.stepmsg().socstep().stepmode();

        // 单体电压步进
        stepMsgInfoBuff.stepMsg.cellVolStep.startTime = eVSStepMsgSet.stepmsg().cellvolstep().starttime();
        stepMsgInfoBuff.stepMsg.cellVolStep.intervalTime = eVSStepMsgSet.stepmsg().cellvolstep().intervaltime();
        stepMsgInfoBuff.stepMsg.cellVolStep.startValue = eVSStepMsgSet.stepmsg().cellvolstep().startvalue();
        stepMsgInfoBuff.stepMsg.cellVolStep.minValue = eVSStepMsgSet.stepmsg().cellvolstep().minvalue();
        stepMsgInfoBuff.stepMsg.cellVolStep.maxValue = eVSStepMsgSet.stepmsg().cellvolstep().maxvalue();
        stepMsgInfoBuff.stepMsg.cellVolStep.stepValue = eVSStepMsgSet.stepmsg().cellvolstep().stepvalue();
        stepMsgInfoBuff.stepMsg.cellVolStep.cycleMode = eVSStepMsgSet.stepmsg().cellvolstep().cyclemode();
        stepMsgInfoBuff.stepMsg.cellVolStep.stepMode = eVSStepMsgSet.stepmsg().cellvolstep().stepmode();

        // 单体电压步进
        stepMsgInfoBuff.stepMsg.cellTempStep.startTime = eVSStepMsgSet.stepmsg().celltempstep().starttime();
        stepMsgInfoBuff.stepMsg.cellTempStep.intervalTime = eVSStepMsgSet.stepmsg().celltempstep().intervaltime();
        stepMsgInfoBuff.stepMsg.cellTempStep.startValue = eVSStepMsgSet.stepmsg().celltempstep().startvalue();
        stepMsgInfoBuff.stepMsg.cellTempStep.minValue = eVSStepMsgSet.stepmsg().celltempstep().minvalue();
        stepMsgInfoBuff.stepMsg.cellTempStep.maxValue = eVSStepMsgSet.stepmsg().celltempstep().maxvalue();
        stepMsgInfoBuff.stepMsg.cellTempStep.stepValue = eVSStepMsgSet.stepmsg().celltempstep().stepvalue();
        stepMsgInfoBuff.stepMsg.cellTempStep.cycleMode = eVSStepMsgSet.stepmsg().celltempstep().cyclemode();
         stepMsgInfoBuff.stepMsg.cellTempStep.stepMode = eVSStepMsgSet.stepmsg().celltempstep().stepmode();

        // Loger(NORMAL, "eVSLoginAns.DebugString:\r\n%s", eVSStepMsgSet.DebugString().c_str());
        // std::cout<<"eVSStepMsgSet.DebugString().c_str()"<<eVSStepMsgSet.DebugString().c_str()<<std::endl;
    }
    return result;
}

/**
 * @brief: EVS报文查询命令(0x1E) hmi->evs
 *
 * @return {*}
 */

int32_t EVSProto::Recv_getMsgInfo(uint8_t *buff,uint32_t size)
{
     int32_t result = EVS_SUCCESS;
    int8_t ret = 0;
    gcu_evs_hmi::protobuf::EVSGetMsg  eVSMsgGet;
    ret = eVSMsgGet.ParseFromArray(buff, size);
    if (ret == 0)
    {
        Loger(NORMAL, "Recv_getMsgInfo  eVSMsgGet ParseFromArray failed!\r\n");
        result =  EVS_FAILED;
    }
    else
    {
         getMsgInfoBuff.evsID = eVSMsgGet.evsid();
         getMsgInfoBuff.MsgID = eVSMsgGet.msgid();
         getMsgInfoBuff.getTime = eVSMsgGet.gettime();
    }

    return result;
}

 /*****************************数据处理******************************/

// 0x00 报文设置数据赋值
int32_t EVSProto::getEvsToHmiRegisterData()
{
    //  std::cout<< "EVSProto::getEvsToHmiRegisterData()==>"<<std::endl;
    evsModuleRegisterbuff.evsID = eVSManageData.evsModuleRegisterManageBuff.evsID;
    evsModuleRegisterbuff.interval = eVSManageData.evsModuleRegisterManageBuff.interval;
    evsModuleRegisterbuff.protoConferM = eVSManageData.evsModuleRegisterManageBuff.protoConferM;
    evsModuleRegisterbuff.funConferM = eVSManageData.evsModuleRegisterManageBuff.funConferM;
    evsModuleRegisterbuff.paraConfigM = eVSManageData.evsModuleRegisterManageBuff.paraConfigM;
    evsModuleRegisterbuff.authenM = eVSManageData.evsModuleRegisterManageBuff.authenM;
    evsModuleRegisterbuff.reserveM = eVSManageData.evsModuleRegisterManageBuff.reserveM;
    evsModuleRegisterbuff.powerSupplyM = eVSManageData.evsModuleRegisterManageBuff.powerSupplyM;
    evsModuleRegisterbuff.chargingM = eVSManageData.evsModuleRegisterManageBuff.chargingM;
    evsModuleRegisterbuff.chargingEndM = eVSManageData.evsModuleRegisterManageBuff.chargingEndM;
   
    return EVS_SUCCESS;

}

// 0x01充电状态信息数据设置
int32_t EVSProto::getEvsToHmiControlCmdRspData()
{
    controlCmdRspBuff.evsID = eVSManageData.controlCmdRspManageBuff.evsID;
    controlCmdRspBuff.heartbeatCnt = eVSManageData.controlCmdRspManageBuff.heartbeatCnt;
    controlCmdRspBuff.onOffState = eVSManageData.controlCmdRspManageBuff.onOffState;
    controlCmdRspBuff.evsState = eVSManageData.controlCmdRspManageBuff.evsState;

    return EVS_SUCCESS;
}

// 0x1f ack数据设置
int32_t EVSProto::getEvsToHmiMsgFillRspData()
{
    msgFillRspBuff.evsID = eVSManageData.msgFillRspManageBuff.evsID;
    msgFillRspBuff.msgID = eVSManageData.msgFillRspManageBuff.msgID;
    msgFillRspBuff.setAck = eVSManageData.msgFillRspManageBuff.setAck;

    return EVS_SUCCESS;
}


// 0x10 数据设置响应数据接收
int32_t EVSProto::setHmiToEvsRegisterAckData()
{
    eVSManageData.evsModuleRegisterAckManageBuff.evsID = evsModuleRegisterAckBuff.evsID;
    eVSManageData.evsModuleRegisterAckManageBuff.serverID = evsModuleRegisterAckBuff.serverID;
    eVSManageData.evsModuleRegisterAckManageBuff.interval = evsModuleRegisterAckBuff.interval;
    //注册状态更新
    if(eVSManageData.evsModuleRegisterAckManageBuff.serverID-1 == E_DEV_EVS)
    {
        protocolWorkState[E_DEV_EVS] = 1; 
        Loger(NORMAL, "E_DEV_EVS reg success!\r\n");

    }
    else if (eVSManageData.evsModuleRegisterAckManageBuff.serverID-1 == E_DEV_HMI)
    {
        protocolWorkState[E_DEV_HMI] = 1; 
        Loger(NORMAL, "E_DEV_HMI reg success!\r\n");
    }

    return EVS_SUCCESS;

}

// 0x11 控制指令数据接收
int32_t EVSProto::setHmiToEvsControlCmdData()
{
    eVSManageData.controlCmdManageBuff.evsID = controlCmdBuff.evsID;
    eVSManageData.controlCmdManageBuff.serverID = controlCmdBuff.serverID;
    eVSManageData.controlCmdManageBuff.heartbeatCnt = controlCmdBuff.heartbeatCnt;
    eVSManageData.controlCmdManageBuff.evsCmd = controlCmdBuff.evsCmd;
    eVSManageData.controlCmdManageBuff.offlineMode = controlCmdBuff.offlineMode;
    eVSManageData.controlCmdManageBuff.evsReset = controlCmdBuff.evsReset;


    return EVS_SUCCESS;

}

// 0x02 车辆报文填充数据接收
int32_t EVSProto::setHmiToEvsVehicleSideMsgFillData()
{
    eVSManageData.vehicleSideMsgFillManageBuff.evsID =  vehicleSideMsgFillBuff.evsID;
    eVSManageData.vehicleSideMsgFillManageBuff.protoConferM = vehicleSideMsgFillBuff.protoConferM;
    eVSManageData.vehicleSideMsgFillManageBuff.funConferM = vehicleSideMsgFillBuff.funConferM;
    eVSManageData.vehicleSideMsgFillManageBuff.paraConfigM = vehicleSideMsgFillBuff.paraConfigM;
    eVSManageData.vehicleSideMsgFillManageBuff.authenM = vehicleSideMsgFillBuff.authenM;
    eVSManageData.vehicleSideMsgFillManageBuff.reserveM = vehicleSideMsgFillBuff.reserveM;
    eVSManageData.vehicleSideMsgFillManageBuff.powerSupplyM = vehicleSideMsgFillBuff.powerSupplyM;
    eVSManageData.vehicleSideMsgFillManageBuff.chargingM = vehicleSideMsgFillBuff.chargingM;
    eVSManageData.vehicleSideMsgFillManageBuff.chargingEndM = vehicleSideMsgFillBuff.chargingEndM;

    return EVS_SUCCESS;
}

// 0x03 报文属性数据接收
int32_t EVSProto::setHmiToEvsMsgAttrInfoData()
{
    
    eVSManageData.msgCtrlInfoManageBuff.evsID = msgAttrInfoBuff.evsID;
    eVSManageData.msgCtrlInfoManageBuff.msgCtrl = msgAttrInfoBuff.msgCtrl;
    eVSManageData.msgCtrlInfoManageBuff.msgCycleTime = msgAttrInfoBuff.msgCycleTime;
    eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme = msgAttrInfoBuff.msgMaxSendTIme;
    eVSManageData.msgCtrlInfoManageBuff.funConferAck = msgAttrInfoBuff.funConferAck;

    return EVS_SUCCESS;
}

// 0x04 电器控制数据接收
int32_t EVSProto::setHmiToEvsElectriCtrlData()
{
    eVSManageData.electriCtrlManageBuff.evsID = electriCtrlBuff.evsID;
    eVSManageData.electriCtrlManageBuff.electricCtrl = electriCtrlBuff.electricCtrl;
    eVSManageData.electriCtrlManageBuff.insultCtrl = electriCtrlBuff.insultCtrl;
    eVSManageData.electriCtrlManageBuff.ipConfig = electriCtrlBuff.ipConfig;
    return EVS_SUCCESS;
}

// 0x05 步进数据接收
int32_t EVSProto::setHmiToEvsStepMsgInfoData()
{
    eVSManageData.stepMsgInfoManageBuff.evsID = stepMsgInfoBuff.evsID;
    eVSManageData.stepMsgInfoManageBuff.stepMsg = stepMsgInfoBuff.stepMsg;
    return EVS_SUCCESS;
}

// 0x1E 查询状态数据信息接收
int32_t EVSProto::setHmiToEvsGetMsgInfoData()
{
    eVSManageData.getMsgInfoManageBuff.evsID = getMsgInfoBuff.evsID;
    eVSManageData.getMsgInfoManageBuff.MsgID = getMsgInfoBuff.MsgID;
    eVSManageData.getMsgInfoManageBuff.getTime = getMsgInfoBuff.getTime;
    return EVS_SUCCESS;
}






int32_t EVSProto::read_socket(DeviceTypeEnum dev)
{
    int32_t ret = 0;
    socklen_t  socket_addr_len = sizeof(SocketManage[dev].Target_Socket_addr);
    ret = recvfrom(SocketManage[dev].Socket_fd,  receive_frame, FRAME_BUFF_SIZE, 0, 
                    (struct sockaddr *)&SocketManage[dev].Target_Socket_addr, &socket_addr_len);
    if (ret < 0)
    {
        //Loger(NORMAL, "read_socket error");
        return EVS_FAILED;
    }

    //缓冲区
    if((receive_offset + ret) >= FRAME_BUFF_SIZE)
    {
        memcpy(&receive_frame_buff[receive_offset], receive_frame, (FRAME_BUFF_SIZE - receive_offset));
        memcpy(&receive_frame_buff[0], &receive_frame[FRAME_BUFF_SIZE-receive_offset], 
                                (ret - (FRAME_BUFF_SIZE - receive_offset)));
        receive_offset = ret - (FRAME_BUFF_SIZE - receive_offset);
        receive_cnt ++;
        if(receive_cnt >= 0xffffffffffffffff)
        {
            receive_cnt = 0;
        }
    }
    else
    {
        memcpy(&receive_frame_buff[receive_offset], receive_frame, ret);
        receive_offset += ret;
    }


    if(scan_offset <= receive_offset )
    {
        scan_max_cnt = receive_offset;
    }
    else
    {
        scan_max_cnt = receive_offset + FRAME_BUFF_SIZE;
    }

    return EVS_SUCCESS;
}


int32_t EVSProto::get_package_proto(uint8_t *frame_data, int32_t *len)
{
    uint32_t i = 0, offset_len = 0;
    uint32_t frame_len = 0;

    if(scan_offset <= receive_offset)
    {
        scan_max_cnt = receive_offset;
    }
    else
    {
        scan_max_cnt = receive_offset + FRAME_BUFF_SIZE;
    }

    for(uint32_t id=scan_offset ;id<scan_max_cnt; id++)
    {
        i=id%FRAME_BUFF_SIZE;
        if((receive_frame_buff[i]==0xfa))
        {
            frame_len = 0;
            frame_len = receive_frame_buff[(i+1)%FRAME_BUFF_SIZE];
            frame_len |= (receive_frame_buff[(i+2)%FRAME_BUFF_SIZE]<<8);
            if((receive_frame_buff[(i+4+frame_len)%FRAME_BUFF_SIZE] == TAIL[0])&&
                (receive_frame_buff[(i+4+frame_len+1)%FRAME_BUFF_SIZE] == TAIL[1])&&
                (receive_frame_buff[(i+4+frame_len+2)%FRAME_BUFF_SIZE] == TAIL[2])&&
                (receive_frame_buff[(i+4+frame_len+3)%FRAME_BUFF_SIZE] == TAIL[3]))
            {
                offset_len = (frame_len+4+4+id);
                if(offset_len > scan_max_cnt)
                {
                    return 0;
                }
                *len = frame_len+4+4;
                if(frame_len+4+4+i >= FRAME_BUFF_SIZE)
                {
                    get_cnt++;
                }
                if(get_cnt >= receive_cnt)
                {
                    return 0;
                }
                scan_offset  = (frame_len+4+4+i)%FRAME_BUFF_SIZE;

                for(uint32_t j=0; j<(4+frame_len+4); j++)
                {
                    frame_data[j] = receive_frame_buff[(i+j) % FRAME_BUFF_SIZE];
                    sprintf(&str_buff[j*2],"%02x",frame_data[j]);
                }
                return 1;
            }
        }
    }
    return 0;
}



int32_t EVSProto::get_frame_data(DeviceTypeEnum *dev,ProtocolFrameBuff *data)
{
    
    for(int i=0;i<MAX_SERVER_NUM;i++){
    if(frame_terminal_buff[i].empty() == false)
    {
        std::vector<ProtocolFrameBuff>::iterator it = frame_terminal_buff[i].begin();
        *data = frame_terminal_buff[i].front();
        *dev =(DeviceTypeEnum)i;
        frame_terminal_buff[i].erase(it); 
        return 0;
    }
    }
    return -1;
}




int32_t EVSProto::ReceiveMsg(DeviceTypeEnum *dev)
{
    
    ProtocolFrameBuff frame_buff;
    //memcpy(frame_buff.data_buff, receive_frame, FRAME_BUFF_SIZE);

    if(-1 == get_frame_data(dev,&frame_buff))
    {
        return EVS_FAILED;
    } 

    if(frame_buff.fram_buff.Fram.head == 0xFA)
    {
        switch (frame_buff.fram_buff.Fram.cmd)
        {
        case TYPE_EVSMODULE_REGISTER_ACK:
            Recv_EvsModuleRegisterAck(frame_buff.fram_buff.Fram.data, frame_buff.fram_buff.Fram.len);
            setHmiToEvsRegisterAckData();
            eVSManageData.upDateFlag |= (0x1 << eVSManageData.upDateFlagMap.at(TYPE_EVSMODULE_REGISTER_ACK));
            break;

        case TYPE_CONTROL_CMD:
            Recv_ControlCmd(frame_buff.fram_buff.Fram.data, frame_buff.fram_buff.Fram.len);
            setHmiToEvsControlCmdData();
            eVSManageData.upDateFlag |= (0x1 << eVSManageData.upDateFlagMap.at(TYPE_CONTROL_CMD));
            break;

        case TYPE_VEHICLE_SIDE_MSG_FILL:
            Recv_VehicleSideMsgFill(frame_buff.fram_buff.Fram.data, frame_buff.fram_buff.Fram.len);
            setHmiToEvsVehicleSideMsgFillData();
            eVSManageData.storeRegisterResetManageData(eVSManageData.vehicleSideMsgFillManageBuff);
            eVSManageData.upDateFlag |= (0x1 << eVSManageData.upDateFlagMap.at(TYPE_VEHICLE_SIDE_MSG_FILL));
            break;

        case TYPE_MSG_ATTR:
            Recv_MsgAttrInfo(frame_buff.fram_buff.Fram.data, frame_buff.fram_buff.Fram.len);
            setHmiToEvsMsgAttrInfoData();
            eVSManageData.upDateFlag |= (0x1 << eVSManageData.upDateFlagMap.at(TYPE_MSG_ATTR));
            break;

        case TYPE_ELECTRI_CTRL:
            Recv_ElectriCtrl(frame_buff.fram_buff.Fram.data, frame_buff.fram_buff.Fram.len);
            setHmiToEvsElectriCtrlData();
            eVSManageData.upDateFlag |=  (0x1 << eVSManageData.upDateFlagMap.at(TYPE_ELECTRI_CTRL));
            break;

        case TYPE_STEP_INFO:
            Recv_StepMsgInfo(frame_buff.fram_buff.Fram.data, frame_buff.fram_buff.Fram.len);
            setHmiToEvsStepMsgInfoData();
            eVSManageData.upDateFlag |= (0x1 << eVSManageData.upDateFlagMap.at(TYPE_STEP_INFO));
            break;
        case TYPE_GET_MSG:
            Recv_getMsgInfo(frame_buff.fram_buff.Fram.data, frame_buff.fram_buff.Fram.len);
            setHmiToEvsGetMsgInfoData();
            eVSManageData.upDateFlag |= (0x1 << eVSManageData.upDateFlagMap.at(TYPE_GET_MSG));
            break;

        default:
            break;
        }
    }

    return EVS_SUCCESS;
}





EVSManageData::EVSManageData(){
    
};
EVSManageData::~EVSManageData(){};

int32_t EVSManageData::MsgStateInit(void){
    EVMsgCtrlInfo* pctrl = &(msgCtrlInfoManageBuff.msgCtrl);
    pctrl->x2_0x02_State = 1;  // 车辆确认结果报文
    pctrl->x4_0x04_State = 1;  // 车辆中止报文
    pctrl->x6_0x06_State = 1;  // 车辆充电回路接触器状态报文
    pctrl->x9_0x09_State = 1;  // 车辆唤醒报文
    pctrl->b2_0x12_State = 1;  // 车辆功能协商确认结果报文
    pctrl->c2_0x22_State = 1;  // 车辆充电参数报文（FDC = 1）
    pctrl->c4_0x24_State = 1;  // 车辆充放电参数报文（FDC = 2）
    pctrl->d2_0x32_State = 1;  // 车辆鉴权等待报文（FDC = 1）
    pctrl->d4_0x34_State = 1;  // 车辆鉴权参数报文（FDC = 2）
    pctrl->d6_0x36_State = 1;  // 重新鉴权请求报文（FDC = 2）
    pctrl->d7_0x37_State = 1;  // 车辆鉴权参数报文（FDC = 3）
    pctrl->d9_0x39_State = 1;  // 鉴权结果报文（FDC = 3）
    pctrl->d10_0x3A_State = 1; // 重新鉴权请求报文（FDC = 3）
    pctrl->e2_0x42_State = 1;  // 车辆预约充电信息报文（FDC )
    pctrl->e4_0x44_State = 1;  // 车辆预约充电协商报文（FDC = 1）
    pctrl->f2_0x52_State = 1;  // 检测确认报文（FDC = 1）
    pctrl->g2_0x62_State = 1;  // 车辆供电状态报文（FDC = 1）
    pctrl->g3_0x63_State = 1;  // 车辆供电需求报文（FDC = 1）
    pctrl->g5_0x65_State = 1;  // 车辆供电完成报文
    pctrl->h2_0x72_State = 1;  // 车辆就绪状态报文（FDC = 1）
    pctrl->h3_0x73_State = 1;  // 车辆充电需求报文（FDC = 1）
    pctrl->h4_0x74_State = 1;  // 车辆充电基本信息报文（FDC = 1）
    pctrl->h7_0x77_State = 1;  // 车辆充电电池基本信息（FDC = 1）
    pctrl->h9_0x79_State = 1;  // 车辆暂停报文（FDC = 1）
    pctrl->h11_0x82_State = 1; // 车辆就绪状态报文（FDC = 2）
    pctrl->h13_0x84_State = 1; // 车辆动态输出能力报文（FDC = 2）
    pctrl->h14_0x85_State = 1; // 车辆充电需求报文（FDC = 2）
    pctrl->h16_0x87_State = 1; // 车辆充放电基本信息报文（fDC = 2）
    pctrl->h18_0x89_State = 1; // 车辆充放电电池基本信息（FDC = 2）
    pctrl->h20_0x8B_State = 1; // 车辆暂停报文（FDC = 2）
    pctrl->i1_0x91_State = 1;  // 车辆粘连检测报文（FDC = 1）
    pctrl->i4_0x94_State = 1;  // 车辆统计报文（FDC = 1）
    EVFunConferAckMsgInfo *pAck = &(msgCtrlInfoManageBuff.funConferAck);
    pAck->funConferAck = 0xaa;      // 功能协商功能确认结果
    pAck->configAck = 0xaa;         // 配置功能确认结果
    pAck->authenAck = 0xaa;         // 鉴权功能确认结果
    pAck->appointAck = 0xaa;        // 预约功能确认结果
    pAck->selfCheckAck = 0xaa;      // 输出回路检测功能确认结果
    pAck->powerSupplyAck = 0xaa;    // 供电模式功能确认结果
    pAck->energyTransferAck = 0xaa; // 预充及能量传输功能确认结果
    pAck->endAck = 0xaa;            // 结束功能确认结果
    return 0;
}

int32_t EVSManageData::ElectStateInit(void){
    EVElectricCtrlInfo* elect = &(electriCtrlManageBuff.electricCtrl);
    elect->contactK5 = 0x00;   // K5状态  默认0x00:断开， 0xAA:闭合
    elect->contactK6 = 0x00;   // K6状态  默认0x00:断开， 0xAA:闭合
    elect->CC1_S2 = 0xaa;      // S2状态  默认0x00:断开， 0xAA:闭合
    elect->CC2_S3 = 0xaa;      // S3状态  默认0x00:断开， 0xAA:闭合
    elect->elockState = 0x00;  // 车辆电子锁状态 默认未锁止0x00,锁止0xAA
    elect->canBus = 0x00;      // CANBus状态  默认 0xAA:闭合， 0x00:断开
    elect->ev_PE = 0x00;       // PE断线状态  默认 0xAA:闭合， 0x00:断开
    elect->evInsultOn = 0x00;  // 车辆绝缘开启  默认0x00:关闭, 0xAA:打开
    elect->evContactK6 = 0x00; // 车辆绝缘检测周期  min 0:连续做，0~200min
    elect->evPause = 0x00;     // 车辆暂停:0xAA 暂停，0x00 恢复
    elect->sysFan = 0x00;        //  系统风机: 0 关机，0x1 开机

    EVInsultCtrlInfo *insult = &(electriCtrlManageBuff.insultCtrl);
    insult->insultPosRes = 0;; // DC+ 阻抗 kΩ
    insult->insultNegRes = 0; // DC- 阻抗 kΩ
    insult->batVol = 200;          // 电池电压 0.0 - 6500.0V
    insult->connectType = 0;  // 连接方式：默认 0x00 正接； 0x01 反接； 0x02 开路
    return 0;
}

int32_t EVSManageData::MsgInfoUpdate(void){
    StorageParaDef  user_data = GetUserData();   //读取现有数据，然后更新，避免IP信息被覆盖

    vehicleSideMsgFillManageBuff.protoConferM = user_data.protoConferM;  // 版本协商信息
    vehicleSideMsgFillManageBuff.funConferM = user_data.funConferM;      // 功能协商
    vehicleSideMsgFillManageBuff.paraConfigM = user_data.paraConfigM; // 车辆充电参数配置
    vehicleSideMsgFillManageBuff.authenM = user_data.authenM;            // 鉴权信息
    vehicleSideMsgFillManageBuff.reserveM = user_data.reserveM;          // 预约充电信息
    vehicleSideMsgFillManageBuff.powerSupplyM = user_data.powerSupplyM;  // 供电模式
    vehicleSideMsgFillManageBuff.chargingM = user_data.chargingM;        // 充电阶段信息
    vehicleSideMsgFillManageBuff.chargingEndM = user_data.chargingEndM;  // 充电结束信息
    electriCtrlManageBuff.ipConfig = user_data.ipConfig;         //IP配置信息
    electriCtrlManageBuff.ipConfig.locolIP = getlocalIP("eth0");
    return 0;
    
}
int32_t EVSManageData::EvsDataInit(void){
    MsgStateInit();
    ElectStateInit();
    MsgInfoUpdate();
    return 0;
}

void EVSManageData::setEvsModuleRegisterManageData(EvsModuleRegister_t  registerData)
{
    evsModuleRegisterManageBuff = registerData;
}

void EVSManageData::setControlCmdRspManageData(ControlCmdRsp_t cmdData)
{
    controlCmdRspManageBuff = cmdData;
}

void EVSManageData::setMsgFillRspManageData(MsgFillRsp_t  rspData)
{
    msgFillRspManageBuff = rspData;
}



void EVSManageData::getEvsModuleRegisterAckManageData(EvsModuleRegisterAck_t & ackData)
{
    ackData = evsModuleRegisterAckManageBuff;
    upDateFlag &=  ~(0x1 << upDateFlagMap.at(TYPE_EVSMODULE_REGISTER_ACK));

}

void EVSManageData::getControlCmdManageData(ControlCmd_t &cmdData)
{
    cmdData = controlCmdManageBuff;
    upDateFlag &=  ~(0x1 << upDateFlagMap.at(TYPE_CONTROL_CMD));
}

void EVSManageData::getVehicleSideMsgFillManageData(VehicleSideMsgFill_t &fillData)
{
    fillData = vehicleSideMsgFillManageBuff;
    upDateFlag &=  ~(0x1 << upDateFlagMap.at(TYPE_VEHICLE_SIDE_MSG_FILL));
}


void EVSManageData::getMsgAttrInfoManageData(MsgCtrl_t & attrInfoData)
{
    attrInfoData = msgCtrlInfoManageBuff;
    upDateFlag &=  ~(0x1 << upDateFlagMap.at(TYPE_MSG_ATTR));
}

void EVSManageData::getElectriCtrlManageData(ElectriCtrl_t &electData)
{
    electData = electriCtrlManageBuff;
    upDateFlag &=  ~(0x1 << upDateFlagMap.at(TYPE_ELECTRI_CTRL));
}

void EVSManageData::getStepMsgInfoManageData(StepMsgInfo_t &stepData)
{
    stepData = stepMsgInfoManageBuff;
    upDateFlag &=  ~(0x1 << upDateFlagMap.at(TYPE_STEP_INFO));
}


void EVSManageData::storeRegisterResetManageData(VehicleSideMsgFill_t&  registInfo)
{
    registerResetManageBuff = registInfo;
}

void EVSManageData::getRegisterResetManageData(VehicleSideMsgFill_t & registInfo)
{
    registInfo = registerResetManageBuff;
}

void EVSManageData::setEvsModuleRegisterManageData(uint32_t interval)
{
    evsModuleRegisterManageBuff.evsID = registerResetManageBuff.evsID;
    evsModuleRegisterManageBuff.interval = 1000;
    if(interval)
    {
        evsModuleRegisterManageBuff.interval = interval;
    }
    evsModuleRegisterManageBuff.protoConferM = registerResetManageBuff.protoConferM;
    evsModuleRegisterManageBuff.funConferM = registerResetManageBuff.funConferM;
    evsModuleRegisterManageBuff.paraConfigM = registerResetManageBuff.paraConfigM;
    evsModuleRegisterManageBuff.authenM = registerResetManageBuff.authenM;
    evsModuleRegisterManageBuff.reserveM = registerResetManageBuff.reserveM;
    evsModuleRegisterManageBuff.powerSupplyM = registerResetManageBuff.powerSupplyM;
    evsModuleRegisterManageBuff.chargingM = registerResetManageBuff.chargingM;
    evsModuleRegisterManageBuff.chargingEndM = registerResetManageBuff.chargingEndM;
}

uint8_t  EVSManageData::getUpdateUpDateFlag()
{
    return upDateFlag;
}


void EVSManageData::updataEvsId()
{
    evsModuleRegisterManageBuff.evsID = evsModuleRegisterAckManageBuff.evsID;
    controlCmdRspManageBuff.evsID = evsModuleRegisterAckManageBuff.evsID;
    msgFillRspManageBuff.evsID = evsModuleRegisterAckManageBuff.evsID;

    controlCmdManageBuff.evsID = evsModuleRegisterAckManageBuff.evsID;
    vehicleSideMsgFillManageBuff.evsID = evsModuleRegisterAckManageBuff.evsID;
    msgCtrlInfoManageBuff.evsID = evsModuleRegisterAckManageBuff.evsID;
    electriCtrlManageBuff.evsID = evsModuleRegisterAckManageBuff.evsID;
    stepMsgInfoManageBuff.evsID = evsModuleRegisterAckManageBuff.evsID;

    registerResetManageBuff.evsID = evsModuleRegisterAckManageBuff.evsID;
}

//心跳数据帧 msg 回填
void EVSManageData::updataHeartBeatData(bool heartbeatCntFlag, bool onOffStateFlag)
{
  
    controlCmdRspManageBuff.heartbeatCnt = 1;  //第一次发送心跳
    if(heartbeatCntFlag)                       //非第一次发送心跳
    {
        controlCmdRspManageBuff.heartbeatCnt = controlCmdManageBuff.heartbeatCnt + 1;
    }

    if(controlCmdRspManageBuff.heartbeatCnt > 0xFFFFFFFF)
    {
        controlCmdRspManageBuff.heartbeatCnt = 0;
    }


    controlCmdRspManageBuff.onOffState = 0x02;
    if(onOffStateFlag)
    {
        controlCmdRspManageBuff.onOffState = controlCmdManageBuff.evsCmd;
    }

    //需要获取
    //controlCmdRspManageBuff.evsState;
}



//0x02 ~ 0x05 ack msg Data回填
void EVSManageData::setAckMsgData(EvsFrameMessageEnum  msgType, uint32_t data)
{
    switch(msgType)
    {
        case TYPE_VEHICLE_SIDE_MSG_FILL:
            msgFillRspManageBuff.msgID = (uint8_t)TYPE_VEHICLE_SIDE_MSG_FILL;
            break;
        case TYPE_MSG_ATTR:
            msgFillRspManageBuff.msgID = (uint8_t)TYPE_MSG_ATTR;
            
            break;
        case TYPE_ELECTRI_CTRL:
            msgFillRspManageBuff.msgID = (uint8_t)TYPE_ELECTRI_CTRL;
            break;
        case TYPE_STEP_INFO:
            msgFillRspManageBuff.msgID = (uint8_t)TYPE_STEP_INFO;
            break;
        case TYPE_GET_MSG:
            msgFillRspManageBuff.msgID = (uint8_t)TYPE_GET_MSG;
            break;
        default:
            break;
    }
    msgFillRspManageBuff.setAck = data;

}


 bool EVSManageData::isAckMsgUpdata(EvsFrameMessageEnum  msgType)
 {
    bool flag = false;
    switch (msgType)
    {
    case TYPE_EVSMODULE_REGISTER_ACK:
        if(upDateFlag >> upDateFlagMap[TYPE_EVSMODULE_REGISTER_ACK] & 0x01)
        {
            flag = true;
        }
        break;
    
    case TYPE_CONTROL_CMD:
        if(upDateFlag >> upDateFlagMap[TYPE_CONTROL_CMD] & 0x01)
        {
            flag = true;
        }
        break;
    
    case TYPE_VEHICLE_SIDE_MSG_FILL:
        if(upDateFlag >> upDateFlagMap[TYPE_VEHICLE_SIDE_MSG_FILL] & 0x01)
        {
            flag = true;
        }
        break;

    case TYPE_MSG_ATTR:
        if(upDateFlag >> upDateFlagMap[TYPE_MSG_ATTR] & 0x01)
        {
            flag = true;
        }
        break;
    
    case TYPE_ELECTRI_CTRL:
        if(upDateFlag >> upDateFlagMap[TYPE_ELECTRI_CTRL] & 0x01)
        {
            flag = true;
        }
        break;
        
    case TYPE_STEP_INFO:
        if(upDateFlag >> upDateFlagMap[TYPE_STEP_INFO] & 0x01)
        {
            flag = true;
        }
        break;
    case TYPE_GET_MSG:
        if(upDateFlag >> upDateFlagMap[TYPE_GET_MSG] & 0x01)
        {
            flag = true;
        }
        break;

    
    default:
        break;
    }
    return flag;
    
 }

