// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GCU_BMS_INFO.proto

#include "GCU_BMS_INFO.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace BMSinfo {
constexpr BMSHandShake::BMSHandShake(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : gbtprotoversion_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , bmsvolmaxallowed_(0){}
struct BMSHandShakeDefaultTypeInternal {
  constexpr BMSHandShakeDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~BMSHandShakeDefaultTypeInternal() {}
  union {
    BMSHandShake _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT BMSHandShakeDefaultTypeInternal _BMSHandShake_default_instance_;
constexpr BMSVerification::BMSVerification(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : batproducer_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , batproducedate_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , bmsversion_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , bmsvin_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , chargerarea_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , batterytype_(0u)
  , batterysn_(0u)
  , propertyright_(0u)
  , machineverifyresult_(0u)
  , machinenumber_(0u)
  , batterchargecnt_(0u)
  , capacityrated_(0)
  , voltagerated_(0){}
struct BMSVerificationDefaultTypeInternal {
  constexpr BMSVerificationDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~BMSVerificationDefaultTypeInternal() {}
  union {
    BMSVerification _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT BMSVerificationDefaultTypeInternal _BMSVerification_default_instance_;
constexpr BMSConfig::BMSConfig(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : monovolmaxallowed_(0)
  , curallowedmax_(0)
  , totalnominalenergy_(0)
  , volallowedmax_(0)
  , tempallowedmax_(0)
  , startsoc_(0)
  , volbatnow_(0)
  , volchargermax_(0)
  , volchargermin_(0)
  , curchargermax_(0)
  , curchargermin_(0)
  , bmsready_(0u)
  , chargerready_(0u){}
struct BMSConfigDefaultTypeInternal {
  constexpr BMSConfigDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~BMSConfigDefaultTypeInternal() {}
  union {
    BMSConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT BMSConfigDefaultTypeInternal _BMSConfig_default_instance_;
constexpr BMSChargingEnd::BMSChargingEnd(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : endsoc_(0)
  , monobatvolmin_(0)
  , monobatvolmax_(0)
  , battempmin_(0)
  , battempmax_(0)
  , bmsstopreason_(0u)
  , bmsfaultreason_(0u)
  , bmserrorreason_(0u)
  , chargerstopreason_(0u)
  , chargerfaultreason_(0u)
  , chargererrorreason_(0u){}
struct BMSChargingEndDefaultTypeInternal {
  constexpr BMSChargingEndDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~BMSChargingEndDefaultTypeInternal() {}
  union {
    BMSChargingEnd _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT BMSChargingEndDefaultTypeInternal _BMSChargingEnd_default_instance_;
constexpr BMSCharging::BMSCharging(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : chargemode_(0)

  , heatmode_(0u)
  , totalchgtime_(0u)
  , remainchgtime_(0u)
  , monobatvolmaxcode_(0u)
  , monobatvolmincode_(0u)
  , tempmaxcode_(0u)
  , tempmincode_(0u)
  , volmaxgroupnum_(0u)
  , monobatvolover_(0u)
  , monobatvolunder_(0u)
  , socover_(0u)
  , socunder_(0u)
  , batcurover_(0u)
  , battempover_(0u)
  , insulationabnormal_(0u)
  , outconnectabnormal_(0u)
  , bmsallow_(0u)
  , chargerallow_(0u)
  , socnow_(0)
  , voldemand_(0)
  , curdemand_(0)
  , volmeasured_(0)
  , curmeasured_(0)
  , monobatvolmax_(0)
  , monobatvolmin_(0)
  , tempmax_(0)
  , tempmin_(0){}
struct BMSChargingDefaultTypeInternal {
  constexpr BMSChargingDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~BMSChargingDefaultTypeInternal() {}
  union {
    BMSCharging _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT BMSChargingDefaultTypeInternal _BMSCharging_default_instance_;
constexpr BMSTimeout::BMSTimeout(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : bmserrorframe_(0u)
  , chargererrorframe_(0u){}
struct BMSTimeoutDefaultTypeInternal {
  constexpr BMSTimeoutDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~BMSTimeoutDefaultTypeInternal() {}
  union {
    BMSTimeout _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT BMSTimeoutDefaultTypeInternal _BMSTimeout_default_instance_;
constexpr BMSReconnectEvent::BMSReconnectEvent(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : timeoutstate_(0u)
  , bmstimeouttype_(0)

  , reconnectcnt_(0u)
  , nextstate_(0u){}
struct BMSReconnectEventDefaultTypeInternal {
  constexpr BMSReconnectEventDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~BMSReconnectEventDefaultTypeInternal() {}
  union {
    BMSReconnectEvent _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT BMSReconnectEventDefaultTypeInternal _BMSReconnectEvent_default_instance_;
constexpr bms2015Msg::bms2015Msg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : bmshandshakem_(nullptr)
  , bmsverifym_(nullptr)
  , bmsconfigm_(nullptr)
  , bmschargingm_(nullptr)
  , bmschargefinishm_(nullptr)
  , bmsstate_(0)
{}
struct bms2015MsgDefaultTypeInternal {
  constexpr bms2015MsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~bms2015MsgDefaultTypeInternal() {}
  union {
    bms2015Msg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT bms2015MsgDefaultTypeInternal _bms2015Msg_default_instance_;
}  // namespace BMSinfo
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_GCU_5fBMS_5fINFO_2eproto[8];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_GCU_5fBMS_5fINFO_2eproto[3];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_GCU_5fBMS_5fINFO_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_GCU_5fBMS_5fINFO_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSHandShake, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSHandShake, bmsvolmaxallowed_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSHandShake, gbtprotoversion_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSVerification, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSVerification, batterytype_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSVerification, batterysn_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSVerification, propertyright_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSVerification, machineverifyresult_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSVerification, machinenumber_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSVerification, batterchargecnt_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSVerification, capacityrated_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSVerification, voltagerated_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSVerification, batproducer_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSVerification, batproducedate_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSVerification, bmsversion_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSVerification, bmsvin_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSVerification, chargerarea_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSConfig, monovolmaxallowed_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSConfig, curallowedmax_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSConfig, totalnominalenergy_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSConfig, volallowedmax_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSConfig, tempallowedmax_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSConfig, startsoc_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSConfig, volbatnow_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSConfig, volchargermax_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSConfig, volchargermin_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSConfig, curchargermax_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSConfig, curchargermin_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSConfig, bmsready_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSConfig, chargerready_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSChargingEnd, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSChargingEnd, endsoc_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSChargingEnd, monobatvolmin_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSChargingEnd, monobatvolmax_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSChargingEnd, battempmin_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSChargingEnd, battempmax_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSChargingEnd, bmsstopreason_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSChargingEnd, bmsfaultreason_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSChargingEnd, bmserrorreason_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSChargingEnd, chargerstopreason_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSChargingEnd, chargerfaultreason_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSChargingEnd, chargererrorreason_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, chargemode_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, heatmode_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, totalchgtime_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, remainchgtime_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, monobatvolmaxcode_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, monobatvolmincode_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, tempmaxcode_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, tempmincode_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, volmaxgroupnum_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, monobatvolover_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, monobatvolunder_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, socover_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, socunder_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, batcurover_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, battempover_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, insulationabnormal_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, outconnectabnormal_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, bmsallow_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, chargerallow_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, socnow_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, voldemand_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, curdemand_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, volmeasured_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, curmeasured_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, monobatvolmax_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, monobatvolmin_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, tempmax_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSCharging, tempmin_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSTimeout, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSTimeout, bmserrorframe_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSTimeout, chargererrorframe_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSReconnectEvent, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSReconnectEvent, timeoutstate_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSReconnectEvent, bmstimeouttype_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSReconnectEvent, reconnectcnt_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::BMSReconnectEvent, nextstate_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::BMSinfo::bms2015Msg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::BMSinfo::bms2015Msg, bmsstate_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::bms2015Msg, bmshandshakem_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::bms2015Msg, bmsverifym_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::bms2015Msg, bmsconfigm_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::bms2015Msg, bmschargingm_),
  PROTOBUF_FIELD_OFFSET(::BMSinfo::bms2015Msg, bmschargefinishm_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::BMSinfo::BMSHandShake)},
  { 7, -1, sizeof(::BMSinfo::BMSVerification)},
  { 25, -1, sizeof(::BMSinfo::BMSConfig)},
  { 43, -1, sizeof(::BMSinfo::BMSChargingEnd)},
  { 59, -1, sizeof(::BMSinfo::BMSCharging)},
  { 92, -1, sizeof(::BMSinfo::BMSTimeout)},
  { 99, -1, sizeof(::BMSinfo::BMSReconnectEvent)},
  { 108, -1, sizeof(::BMSinfo::bms2015Msg)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::BMSinfo::_BMSHandShake_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::BMSinfo::_BMSVerification_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::BMSinfo::_BMSConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::BMSinfo::_BMSChargingEnd_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::BMSinfo::_BMSCharging_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::BMSinfo::_BMSTimeout_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::BMSinfo::_BMSReconnectEvent_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::BMSinfo::_bms2015Msg_default_instance_),
};

const char descriptor_table_protodef_GCU_5fBMS_5fINFO_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\022GCU_BMS_INFO.proto\022\007BMSinfo\"A\n\014BMSHand"
  "Shake\022\030\n\020bmsVolMaxAllowed\030\001 \001(\002\022\027\n\017gbtPr"
  "otoVersion\030\002 \001(\014\"\260\002\n\017BMSVerification\022\023\n\013"
  "batteryType\030\001 \001(\r\022\021\n\tbatterySN\030\002 \001(\r\022\025\n\r"
  "propertyRight\030\003 \001(\r\022\033\n\023machineVerifyResu"
  "lt\030\004 \001(\r\022\025\n\rmachineNumber\030\005 \001(\r\022\027\n\017batte"
  "rChargeCnt\030\006 \001(\r\022\025\n\rcapacityRated\030\007 \001(\002\022"
  "\024\n\014voltageRated\030\010 \001(\002\022\023\n\013batProducer\030\t \001"
  "(\014\022\026\n\016batProduceDate\030\n \001(\014\022\022\n\nbmsVersion"
  "\030\013 \001(\014\022\016\n\006bmsVIN\030\014 \001(\014\022\023\n\013chargerArea\030\r "
  "\001(\014\"\261\002\n\tBMSConfig\022\031\n\021monoVolMaxAllowed\030\001"
  " \001(\002\022\025\n\rcurAllowedMax\030\002 \001(\002\022\032\n\022totalNomi"
  "nalEnergy\030\003 \001(\002\022\025\n\rvolAllowedMax\030\004 \001(\002\022\026"
  "\n\016tempAllowedMax\030\005 \001(\002\022\020\n\010startSOC\030\006 \001(\002"
  "\022\021\n\tvolBatNow\030\007 \001(\002\022\025\n\rvolChargerMax\030\010 \001"
  "(\002\022\025\n\rvolChargerMin\030\t \001(\002\022\025\n\rcurChargerM"
  "ax\030\n \001(\002\022\025\n\rcurChargerMin\030\013 \001(\002\022\020\n\010bmsRe"
  "ady\030\014 \001(\r\022\024\n\014chargerReady\030\r \001(\r\"\220\002\n\016BMSC"
  "hargingEnd\022\016\n\006endSOC\030\001 \001(\002\022\025\n\rmonoBatVol"
  "Min\030\002 \001(\002\022\025\n\rmonoBatVolMax\030\003 \001(\002\022\022\n\nbatT"
  "empMin\030\004 \001(\002\022\022\n\nbatTempMax\030\005 \001(\002\022\025\n\rbmsS"
  "topReason\030\006 \001(\r\022\026\n\016bmsFaultReason\030\007 \001(\r\022"
  "\026\n\016bmsErrorReason\030\010 \001(\r\022\031\n\021chargerStopRe"
  "ason\030\t \001(\r\022\032\n\022chargerFaultReason\030\n \001(\r\022\032"
  "\n\022chargerErrorReason\030\013 \001(\r\"\374\004\n\013BMSChargi"
  "ng\022)\n\nchargeMode\030\001 \001(\0162\025.BMSinfo.Chargin"
  "gMode\022\020\n\010heatMode\030\002 \001(\r\022\024\n\014totalChgTime\030"
  "\003 \001(\r\022\025\n\rremainChgTime\030\004 \001(\r\022\031\n\021monoBatV"
  "olMaxCode\030\005 \001(\r\022\031\n\021monoBatVolMinCode\030\006 \001"
  "(\r\022\023\n\013tempMaxCode\030\007 \001(\r\022\023\n\013tempMinCode\030\010"
  " \001(\r\022\026\n\016volMaxGroupNum\030\t \001(\r\022\026\n\016monoBatV"
  "olOver\030\n \001(\r\022\027\n\017monoBatVolUnder\030\013 \001(\r\022\017\n"
  "\007socOver\030\014 \001(\r\022\020\n\010socUnder\030\r \001(\r\022\022\n\nbatC"
  "urOver\030\016 \001(\r\022\023\n\013batTempOver\030\017 \001(\r\022\032\n\022ins"
  "ulationAbnormal\030\020 \001(\r\022\032\n\022outConnectAbnor"
  "mal\030\021 \001(\r\022\020\n\010bmsAllow\030\022 \001(\r\022\024\n\014chargerAl"
  "low\030\023 \001(\r\022\016\n\006socNow\030\024 \001(\002\022\021\n\tvolDemand\030\025"
  " \001(\002\022\021\n\tcurDemand\030\026 \001(\002\022\023\n\013volMeasured\030\027"
  " \001(\002\022\023\n\013curMeasured\030\030 \001(\002\022\025\n\rmonoBatVolM"
  "ax\030\031 \001(\002\022\025\n\rmonoBatVolMin\030\032 \001(\002\022\017\n\007tempM"
  "ax\030\033 \001(\002\022\017\n\007tempMin\030\034 \001(\002\">\n\nBMSTimeout\022"
  "\025\n\rbmsErrorFrame\030\001 \001(\r\022\031\n\021chargerErrorFr"
  "ame\030\002 \001(\r\"\203\001\n\021BMSReconnectEvent\022\024\n\014timeo"
  "utState\030\001 \001(\r\022/\n\016bmsTimeoutType\030\002 \001(\0162\027."
  "BMSinfo.BMSTimeoutEnum\022\024\n\014reconnectCnt\030\003"
  " \001(\r\022\021\n\tnextState\030\004 \001(\r\"\227\002\n\nbms2015Msg\022&"
  "\n\010bmsState\030\003 \001(\0162\024.BMSinfo.ChargeState\022,"
  "\n\rBmshandShakeM\030\004 \001(\0132\025.BMSinfo.BMSHandS"
  "hake\022,\n\nBmsVerifyM\030\005 \001(\0132\030.BMSinfo.BMSVe"
  "rification\022&\n\nBmsConfigM\030\006 \001(\0132\022.BMSinfo"
  ".BMSConfig\022*\n\014BmsChargingM\030\007 \001(\0132\024.BMSin"
  "fo.BMSCharging\0221\n\020BmsChargeFinishM\030\010 \001(\013"
  "2\027.BMSinfo.BMSChargingEnd*M\n\014ChargingMod"
  "e\022\027\n\023DefaultChargingMode\020\000\022\021\n\rVoltageSta"
  "ble\020\001\022\021\n\rCurrentStable\020\002*c\n\016BMSTimeoutEn"
  "um\022\022\n\016DefaultTimeout\020\000\022\007\n\003BHM\020\001\022\007\n\003BRM\020\002"
  "\022\007\n\003BCP\020\003\022\007\n\003BCS\020\004\022\007\n\003BCL\020\005\022\007\n\003BST\020\006\022\007\n\003"
  "BSD\020\007*\247\001\n\013ChargeState\022\020\n\014DefaultState\020\000\022"
  "\020\n\014ChargeCreate\020\001\022\r\n\tHandshake\020\002\022\014\n\010Iden"
  "tify\020\003\022\017\n\013ParamConfig\020\004\022\013\n\007Charing\020\005\022\r\n\t"
  "ChgStatic\020\006\022\016\n\nChgTimeout\020\007\022\n\n\006ChgEnd\020\010\022"
  "\016\n\nChgDestory\020\tb\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_GCU_5fBMS_5fINFO_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GCU_5fBMS_5fINFO_2eproto = {
  false, false, 2463, descriptor_table_protodef_GCU_5fBMS_5fINFO_2eproto, "GCU_BMS_INFO.proto", 
  &descriptor_table_GCU_5fBMS_5fINFO_2eproto_once, nullptr, 0, 8,
  schemas, file_default_instances, TableStruct_GCU_5fBMS_5fINFO_2eproto::offsets,
  file_level_metadata_GCU_5fBMS_5fINFO_2eproto, file_level_enum_descriptors_GCU_5fBMS_5fINFO_2eproto, file_level_service_descriptors_GCU_5fBMS_5fINFO_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_GCU_5fBMS_5fINFO_2eproto_getter() {
  return &descriptor_table_GCU_5fBMS_5fINFO_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_GCU_5fBMS_5fINFO_2eproto(&descriptor_table_GCU_5fBMS_5fINFO_2eproto);
namespace BMSinfo {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ChargingMode_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fBMS_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fBMS_5fINFO_2eproto[0];
}
bool ChargingMode_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* BMSTimeoutEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fBMS_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fBMS_5fINFO_2eproto[1];
}
bool BMSTimeoutEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ChargeState_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fBMS_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fBMS_5fINFO_2eproto[2];
}
bool ChargeState_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class BMSHandShake::_Internal {
 public:
};

BMSHandShake::BMSHandShake(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:BMSinfo.BMSHandShake)
}
BMSHandShake::BMSHandShake(const BMSHandShake& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  gbtprotoversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_gbtprotoversion().empty()) {
    gbtprotoversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_gbtprotoversion(), 
      GetArenaForAllocation());
  }
  bmsvolmaxallowed_ = from.bmsvolmaxallowed_;
  // @@protoc_insertion_point(copy_constructor:BMSinfo.BMSHandShake)
}

inline void BMSHandShake::SharedCtor() {
gbtprotoversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
bmsvolmaxallowed_ = 0;
}

BMSHandShake::~BMSHandShake() {
  // @@protoc_insertion_point(destructor:BMSinfo.BMSHandShake)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void BMSHandShake::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  gbtprotoversion_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void BMSHandShake::ArenaDtor(void* object) {
  BMSHandShake* _this = reinterpret_cast< BMSHandShake* >(object);
  (void)_this;
}
void BMSHandShake::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void BMSHandShake::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void BMSHandShake::Clear() {
// @@protoc_insertion_point(message_clear_start:BMSinfo.BMSHandShake)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  gbtprotoversion_.ClearToEmpty();
  bmsvolmaxallowed_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* BMSHandShake::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float bmsVolMaxAllowed = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          bmsvolmaxallowed_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // bytes gbtProtoVersion = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_gbtprotoversion();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* BMSHandShake::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:BMSinfo.BMSHandShake)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float bmsVolMaxAllowed = 1;
  if (!(this->_internal_bmsvolmaxallowed() <= 0 && this->_internal_bmsvolmaxallowed() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_bmsvolmaxallowed(), target);
  }

  // bytes gbtProtoVersion = 2;
  if (!this->_internal_gbtprotoversion().empty()) {
    target = stream->WriteBytesMaybeAliased(
        2, this->_internal_gbtprotoversion(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:BMSinfo.BMSHandShake)
  return target;
}

size_t BMSHandShake::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:BMSinfo.BMSHandShake)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes gbtProtoVersion = 2;
  if (!this->_internal_gbtprotoversion().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_gbtprotoversion());
  }

  // float bmsVolMaxAllowed = 1;
  if (!(this->_internal_bmsvolmaxallowed() <= 0 && this->_internal_bmsvolmaxallowed() >= 0)) {
    total_size += 1 + 4;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData BMSHandShake::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    BMSHandShake::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*BMSHandShake::GetClassData() const { return &_class_data_; }

void BMSHandShake::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<BMSHandShake *>(to)->MergeFrom(
      static_cast<const BMSHandShake &>(from));
}


void BMSHandShake::MergeFrom(const BMSHandShake& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:BMSinfo.BMSHandShake)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_gbtprotoversion().empty()) {
    _internal_set_gbtprotoversion(from._internal_gbtprotoversion());
  }
  if (!(from._internal_bmsvolmaxallowed() <= 0 && from._internal_bmsvolmaxallowed() >= 0)) {
    _internal_set_bmsvolmaxallowed(from._internal_bmsvolmaxallowed());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void BMSHandShake::CopyFrom(const BMSHandShake& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:BMSinfo.BMSHandShake)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BMSHandShake::IsInitialized() const {
  return true;
}

void BMSHandShake::InternalSwap(BMSHandShake* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &gbtprotoversion_, GetArenaForAllocation(),
      &other->gbtprotoversion_, other->GetArenaForAllocation()
  );
  swap(bmsvolmaxallowed_, other->bmsvolmaxallowed_);
}

::PROTOBUF_NAMESPACE_ID::Metadata BMSHandShake::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fBMS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fBMS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fBMS_5fINFO_2eproto[0]);
}

// ===================================================================

class BMSVerification::_Internal {
 public:
};

BMSVerification::BMSVerification(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:BMSinfo.BMSVerification)
}
BMSVerification::BMSVerification(const BMSVerification& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  batproducer_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_batproducer().empty()) {
    batproducer_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_batproducer(), 
      GetArenaForAllocation());
  }
  batproducedate_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_batproducedate().empty()) {
    batproducedate_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_batproducedate(), 
      GetArenaForAllocation());
  }
  bmsversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_bmsversion().empty()) {
    bmsversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_bmsversion(), 
      GetArenaForAllocation());
  }
  bmsvin_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_bmsvin().empty()) {
    bmsvin_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_bmsvin(), 
      GetArenaForAllocation());
  }
  chargerarea_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_chargerarea().empty()) {
    chargerarea_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_chargerarea(), 
      GetArenaForAllocation());
  }
  ::memcpy(&batterytype_, &from.batterytype_,
    static_cast<size_t>(reinterpret_cast<char*>(&voltagerated_) -
    reinterpret_cast<char*>(&batterytype_)) + sizeof(voltagerated_));
  // @@protoc_insertion_point(copy_constructor:BMSinfo.BMSVerification)
}

inline void BMSVerification::SharedCtor() {
batproducer_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
batproducedate_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
bmsversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
bmsvin_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
chargerarea_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&batterytype_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&voltagerated_) -
    reinterpret_cast<char*>(&batterytype_)) + sizeof(voltagerated_));
}

BMSVerification::~BMSVerification() {
  // @@protoc_insertion_point(destructor:BMSinfo.BMSVerification)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void BMSVerification::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  batproducer_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  batproducedate_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  bmsversion_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  bmsvin_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  chargerarea_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void BMSVerification::ArenaDtor(void* object) {
  BMSVerification* _this = reinterpret_cast< BMSVerification* >(object);
  (void)_this;
}
void BMSVerification::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void BMSVerification::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void BMSVerification::Clear() {
// @@protoc_insertion_point(message_clear_start:BMSinfo.BMSVerification)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  batproducer_.ClearToEmpty();
  batproducedate_.ClearToEmpty();
  bmsversion_.ClearToEmpty();
  bmsvin_.ClearToEmpty();
  chargerarea_.ClearToEmpty();
  ::memset(&batterytype_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&voltagerated_) -
      reinterpret_cast<char*>(&batterytype_)) + sizeof(voltagerated_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* BMSVerification::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 batteryType = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          batterytype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 batterySN = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          batterysn_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 propertyRight = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          propertyright_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 machineVerifyResult = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          machineverifyresult_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 machineNumber = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          machinenumber_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 batterChargeCnt = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          batterchargecnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float capacityRated = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 61)) {
          capacityrated_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float voltageRated = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 69)) {
          voltagerated_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // bytes batProducer = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          auto str = _internal_mutable_batproducer();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes batProduceDate = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 82)) {
          auto str = _internal_mutable_batproducedate();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes bmsVersion = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 90)) {
          auto str = _internal_mutable_bmsversion();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes bmsVIN = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 98)) {
          auto str = _internal_mutable_bmsvin();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes chargerArea = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 106)) {
          auto str = _internal_mutable_chargerarea();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* BMSVerification::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:BMSinfo.BMSVerification)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 batteryType = 1;
  if (this->_internal_batterytype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_batterytype(), target);
  }

  // uint32 batterySN = 2;
  if (this->_internal_batterysn() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_batterysn(), target);
  }

  // uint32 propertyRight = 3;
  if (this->_internal_propertyright() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_propertyright(), target);
  }

  // uint32 machineVerifyResult = 4;
  if (this->_internal_machineverifyresult() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_machineverifyresult(), target);
  }

  // uint32 machineNumber = 5;
  if (this->_internal_machinenumber() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_machinenumber(), target);
  }

  // uint32 batterChargeCnt = 6;
  if (this->_internal_batterchargecnt() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_batterchargecnt(), target);
  }

  // float capacityRated = 7;
  if (!(this->_internal_capacityrated() <= 0 && this->_internal_capacityrated() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(7, this->_internal_capacityrated(), target);
  }

  // float voltageRated = 8;
  if (!(this->_internal_voltagerated() <= 0 && this->_internal_voltagerated() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(8, this->_internal_voltagerated(), target);
  }

  // bytes batProducer = 9;
  if (!this->_internal_batproducer().empty()) {
    target = stream->WriteBytesMaybeAliased(
        9, this->_internal_batproducer(), target);
  }

  // bytes batProduceDate = 10;
  if (!this->_internal_batproducedate().empty()) {
    target = stream->WriteBytesMaybeAliased(
        10, this->_internal_batproducedate(), target);
  }

  // bytes bmsVersion = 11;
  if (!this->_internal_bmsversion().empty()) {
    target = stream->WriteBytesMaybeAliased(
        11, this->_internal_bmsversion(), target);
  }

  // bytes bmsVIN = 12;
  if (!this->_internal_bmsvin().empty()) {
    target = stream->WriteBytesMaybeAliased(
        12, this->_internal_bmsvin(), target);
  }

  // bytes chargerArea = 13;
  if (!this->_internal_chargerarea().empty()) {
    target = stream->WriteBytesMaybeAliased(
        13, this->_internal_chargerarea(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:BMSinfo.BMSVerification)
  return target;
}

size_t BMSVerification::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:BMSinfo.BMSVerification)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes batProducer = 9;
  if (!this->_internal_batproducer().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_batproducer());
  }

  // bytes batProduceDate = 10;
  if (!this->_internal_batproducedate().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_batproducedate());
  }

  // bytes bmsVersion = 11;
  if (!this->_internal_bmsversion().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_bmsversion());
  }

  // bytes bmsVIN = 12;
  if (!this->_internal_bmsvin().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_bmsvin());
  }

  // bytes chargerArea = 13;
  if (!this->_internal_chargerarea().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_chargerarea());
  }

  // uint32 batteryType = 1;
  if (this->_internal_batterytype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_batterytype());
  }

  // uint32 batterySN = 2;
  if (this->_internal_batterysn() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_batterysn());
  }

  // uint32 propertyRight = 3;
  if (this->_internal_propertyright() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_propertyright());
  }

  // uint32 machineVerifyResult = 4;
  if (this->_internal_machineverifyresult() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_machineverifyresult());
  }

  // uint32 machineNumber = 5;
  if (this->_internal_machinenumber() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_machinenumber());
  }

  // uint32 batterChargeCnt = 6;
  if (this->_internal_batterchargecnt() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_batterchargecnt());
  }

  // float capacityRated = 7;
  if (!(this->_internal_capacityrated() <= 0 && this->_internal_capacityrated() >= 0)) {
    total_size += 1 + 4;
  }

  // float voltageRated = 8;
  if (!(this->_internal_voltagerated() <= 0 && this->_internal_voltagerated() >= 0)) {
    total_size += 1 + 4;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData BMSVerification::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    BMSVerification::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*BMSVerification::GetClassData() const { return &_class_data_; }

void BMSVerification::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<BMSVerification *>(to)->MergeFrom(
      static_cast<const BMSVerification &>(from));
}


void BMSVerification::MergeFrom(const BMSVerification& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:BMSinfo.BMSVerification)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_batproducer().empty()) {
    _internal_set_batproducer(from._internal_batproducer());
  }
  if (!from._internal_batproducedate().empty()) {
    _internal_set_batproducedate(from._internal_batproducedate());
  }
  if (!from._internal_bmsversion().empty()) {
    _internal_set_bmsversion(from._internal_bmsversion());
  }
  if (!from._internal_bmsvin().empty()) {
    _internal_set_bmsvin(from._internal_bmsvin());
  }
  if (!from._internal_chargerarea().empty()) {
    _internal_set_chargerarea(from._internal_chargerarea());
  }
  if (from._internal_batterytype() != 0) {
    _internal_set_batterytype(from._internal_batterytype());
  }
  if (from._internal_batterysn() != 0) {
    _internal_set_batterysn(from._internal_batterysn());
  }
  if (from._internal_propertyright() != 0) {
    _internal_set_propertyright(from._internal_propertyright());
  }
  if (from._internal_machineverifyresult() != 0) {
    _internal_set_machineverifyresult(from._internal_machineverifyresult());
  }
  if (from._internal_machinenumber() != 0) {
    _internal_set_machinenumber(from._internal_machinenumber());
  }
  if (from._internal_batterchargecnt() != 0) {
    _internal_set_batterchargecnt(from._internal_batterchargecnt());
  }
  if (!(from._internal_capacityrated() <= 0 && from._internal_capacityrated() >= 0)) {
    _internal_set_capacityrated(from._internal_capacityrated());
  }
  if (!(from._internal_voltagerated() <= 0 && from._internal_voltagerated() >= 0)) {
    _internal_set_voltagerated(from._internal_voltagerated());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void BMSVerification::CopyFrom(const BMSVerification& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:BMSinfo.BMSVerification)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BMSVerification::IsInitialized() const {
  return true;
}

void BMSVerification::InternalSwap(BMSVerification* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &batproducer_, GetArenaForAllocation(),
      &other->batproducer_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &batproducedate_, GetArenaForAllocation(),
      &other->batproducedate_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &bmsversion_, GetArenaForAllocation(),
      &other->bmsversion_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &bmsvin_, GetArenaForAllocation(),
      &other->bmsvin_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &chargerarea_, GetArenaForAllocation(),
      &other->chargerarea_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(BMSVerification, voltagerated_)
      + sizeof(BMSVerification::voltagerated_)
      - PROTOBUF_FIELD_OFFSET(BMSVerification, batterytype_)>(
          reinterpret_cast<char*>(&batterytype_),
          reinterpret_cast<char*>(&other->batterytype_));
}

::PROTOBUF_NAMESPACE_ID::Metadata BMSVerification::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fBMS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fBMS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fBMS_5fINFO_2eproto[1]);
}

// ===================================================================

class BMSConfig::_Internal {
 public:
};

BMSConfig::BMSConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:BMSinfo.BMSConfig)
}
BMSConfig::BMSConfig(const BMSConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&monovolmaxallowed_, &from.monovolmaxallowed_,
    static_cast<size_t>(reinterpret_cast<char*>(&chargerready_) -
    reinterpret_cast<char*>(&monovolmaxallowed_)) + sizeof(chargerready_));
  // @@protoc_insertion_point(copy_constructor:BMSinfo.BMSConfig)
}

inline void BMSConfig::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&monovolmaxallowed_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&chargerready_) -
    reinterpret_cast<char*>(&monovolmaxallowed_)) + sizeof(chargerready_));
}

BMSConfig::~BMSConfig() {
  // @@protoc_insertion_point(destructor:BMSinfo.BMSConfig)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void BMSConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void BMSConfig::ArenaDtor(void* object) {
  BMSConfig* _this = reinterpret_cast< BMSConfig* >(object);
  (void)_this;
}
void BMSConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void BMSConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void BMSConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:BMSinfo.BMSConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&monovolmaxallowed_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&chargerready_) -
      reinterpret_cast<char*>(&monovolmaxallowed_)) + sizeof(chargerready_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* BMSConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float monoVolMaxAllowed = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          monovolmaxallowed_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float curAllowedMax = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 21)) {
          curallowedmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float totalNominalEnergy = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          totalnominalenergy_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float volAllowedMax = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 37)) {
          volallowedmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float tempAllowedMax = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 45)) {
          tempallowedmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float startSOC = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 53)) {
          startsoc_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float volBatNow = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 61)) {
          volbatnow_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float volChargerMax = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 69)) {
          volchargermax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float volChargerMin = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 77)) {
          volchargermin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float curChargerMax = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 85)) {
          curchargermax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float curChargerMin = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 93)) {
          curchargermin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 bmsReady = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 96)) {
          bmsready_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 chargerReady = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 104)) {
          chargerready_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* BMSConfig::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:BMSinfo.BMSConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float monoVolMaxAllowed = 1;
  if (!(this->_internal_monovolmaxallowed() <= 0 && this->_internal_monovolmaxallowed() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_monovolmaxallowed(), target);
  }

  // float curAllowedMax = 2;
  if (!(this->_internal_curallowedmax() <= 0 && this->_internal_curallowedmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_curallowedmax(), target);
  }

  // float totalNominalEnergy = 3;
  if (!(this->_internal_totalnominalenergy() <= 0 && this->_internal_totalnominalenergy() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_totalnominalenergy(), target);
  }

  // float volAllowedMax = 4;
  if (!(this->_internal_volallowedmax() <= 0 && this->_internal_volallowedmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_volallowedmax(), target);
  }

  // float tempAllowedMax = 5;
  if (!(this->_internal_tempallowedmax() <= 0 && this->_internal_tempallowedmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(5, this->_internal_tempallowedmax(), target);
  }

  // float startSOC = 6;
  if (!(this->_internal_startsoc() <= 0 && this->_internal_startsoc() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(6, this->_internal_startsoc(), target);
  }

  // float volBatNow = 7;
  if (!(this->_internal_volbatnow() <= 0 && this->_internal_volbatnow() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(7, this->_internal_volbatnow(), target);
  }

  // float volChargerMax = 8;
  if (!(this->_internal_volchargermax() <= 0 && this->_internal_volchargermax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(8, this->_internal_volchargermax(), target);
  }

  // float volChargerMin = 9;
  if (!(this->_internal_volchargermin() <= 0 && this->_internal_volchargermin() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(9, this->_internal_volchargermin(), target);
  }

  // float curChargerMax = 10;
  if (!(this->_internal_curchargermax() <= 0 && this->_internal_curchargermax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(10, this->_internal_curchargermax(), target);
  }

  // float curChargerMin = 11;
  if (!(this->_internal_curchargermin() <= 0 && this->_internal_curchargermin() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(11, this->_internal_curchargermin(), target);
  }

  // uint32 bmsReady = 12;
  if (this->_internal_bmsready() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(12, this->_internal_bmsready(), target);
  }

  // uint32 chargerReady = 13;
  if (this->_internal_chargerready() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(13, this->_internal_chargerready(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:BMSinfo.BMSConfig)
  return target;
}

size_t BMSConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:BMSinfo.BMSConfig)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float monoVolMaxAllowed = 1;
  if (!(this->_internal_monovolmaxallowed() <= 0 && this->_internal_monovolmaxallowed() >= 0)) {
    total_size += 1 + 4;
  }

  // float curAllowedMax = 2;
  if (!(this->_internal_curallowedmax() <= 0 && this->_internal_curallowedmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float totalNominalEnergy = 3;
  if (!(this->_internal_totalnominalenergy() <= 0 && this->_internal_totalnominalenergy() >= 0)) {
    total_size += 1 + 4;
  }

  // float volAllowedMax = 4;
  if (!(this->_internal_volallowedmax() <= 0 && this->_internal_volallowedmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float tempAllowedMax = 5;
  if (!(this->_internal_tempallowedmax() <= 0 && this->_internal_tempallowedmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float startSOC = 6;
  if (!(this->_internal_startsoc() <= 0 && this->_internal_startsoc() >= 0)) {
    total_size += 1 + 4;
  }

  // float volBatNow = 7;
  if (!(this->_internal_volbatnow() <= 0 && this->_internal_volbatnow() >= 0)) {
    total_size += 1 + 4;
  }

  // float volChargerMax = 8;
  if (!(this->_internal_volchargermax() <= 0 && this->_internal_volchargermax() >= 0)) {
    total_size += 1 + 4;
  }

  // float volChargerMin = 9;
  if (!(this->_internal_volchargermin() <= 0 && this->_internal_volchargermin() >= 0)) {
    total_size += 1 + 4;
  }

  // float curChargerMax = 10;
  if (!(this->_internal_curchargermax() <= 0 && this->_internal_curchargermax() >= 0)) {
    total_size += 1 + 4;
  }

  // float curChargerMin = 11;
  if (!(this->_internal_curchargermin() <= 0 && this->_internal_curchargermin() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 bmsReady = 12;
  if (this->_internal_bmsready() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsready());
  }

  // uint32 chargerReady = 13;
  if (this->_internal_chargerready() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chargerready());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData BMSConfig::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    BMSConfig::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*BMSConfig::GetClassData() const { return &_class_data_; }

void BMSConfig::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<BMSConfig *>(to)->MergeFrom(
      static_cast<const BMSConfig &>(from));
}


void BMSConfig::MergeFrom(const BMSConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:BMSinfo.BMSConfig)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from._internal_monovolmaxallowed() <= 0 && from._internal_monovolmaxallowed() >= 0)) {
    _internal_set_monovolmaxallowed(from._internal_monovolmaxallowed());
  }
  if (!(from._internal_curallowedmax() <= 0 && from._internal_curallowedmax() >= 0)) {
    _internal_set_curallowedmax(from._internal_curallowedmax());
  }
  if (!(from._internal_totalnominalenergy() <= 0 && from._internal_totalnominalenergy() >= 0)) {
    _internal_set_totalnominalenergy(from._internal_totalnominalenergy());
  }
  if (!(from._internal_volallowedmax() <= 0 && from._internal_volallowedmax() >= 0)) {
    _internal_set_volallowedmax(from._internal_volallowedmax());
  }
  if (!(from._internal_tempallowedmax() <= 0 && from._internal_tempallowedmax() >= 0)) {
    _internal_set_tempallowedmax(from._internal_tempallowedmax());
  }
  if (!(from._internal_startsoc() <= 0 && from._internal_startsoc() >= 0)) {
    _internal_set_startsoc(from._internal_startsoc());
  }
  if (!(from._internal_volbatnow() <= 0 && from._internal_volbatnow() >= 0)) {
    _internal_set_volbatnow(from._internal_volbatnow());
  }
  if (!(from._internal_volchargermax() <= 0 && from._internal_volchargermax() >= 0)) {
    _internal_set_volchargermax(from._internal_volchargermax());
  }
  if (!(from._internal_volchargermin() <= 0 && from._internal_volchargermin() >= 0)) {
    _internal_set_volchargermin(from._internal_volchargermin());
  }
  if (!(from._internal_curchargermax() <= 0 && from._internal_curchargermax() >= 0)) {
    _internal_set_curchargermax(from._internal_curchargermax());
  }
  if (!(from._internal_curchargermin() <= 0 && from._internal_curchargermin() >= 0)) {
    _internal_set_curchargermin(from._internal_curchargermin());
  }
  if (from._internal_bmsready() != 0) {
    _internal_set_bmsready(from._internal_bmsready());
  }
  if (from._internal_chargerready() != 0) {
    _internal_set_chargerready(from._internal_chargerready());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void BMSConfig::CopyFrom(const BMSConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:BMSinfo.BMSConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BMSConfig::IsInitialized() const {
  return true;
}

void BMSConfig::InternalSwap(BMSConfig* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(BMSConfig, chargerready_)
      + sizeof(BMSConfig::chargerready_)
      - PROTOBUF_FIELD_OFFSET(BMSConfig, monovolmaxallowed_)>(
          reinterpret_cast<char*>(&monovolmaxallowed_),
          reinterpret_cast<char*>(&other->monovolmaxallowed_));
}

::PROTOBUF_NAMESPACE_ID::Metadata BMSConfig::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fBMS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fBMS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fBMS_5fINFO_2eproto[2]);
}

// ===================================================================

class BMSChargingEnd::_Internal {
 public:
};

BMSChargingEnd::BMSChargingEnd(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:BMSinfo.BMSChargingEnd)
}
BMSChargingEnd::BMSChargingEnd(const BMSChargingEnd& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&endsoc_, &from.endsoc_,
    static_cast<size_t>(reinterpret_cast<char*>(&chargererrorreason_) -
    reinterpret_cast<char*>(&endsoc_)) + sizeof(chargererrorreason_));
  // @@protoc_insertion_point(copy_constructor:BMSinfo.BMSChargingEnd)
}

inline void BMSChargingEnd::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&endsoc_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&chargererrorreason_) -
    reinterpret_cast<char*>(&endsoc_)) + sizeof(chargererrorreason_));
}

BMSChargingEnd::~BMSChargingEnd() {
  // @@protoc_insertion_point(destructor:BMSinfo.BMSChargingEnd)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void BMSChargingEnd::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void BMSChargingEnd::ArenaDtor(void* object) {
  BMSChargingEnd* _this = reinterpret_cast< BMSChargingEnd* >(object);
  (void)_this;
}
void BMSChargingEnd::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void BMSChargingEnd::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void BMSChargingEnd::Clear() {
// @@protoc_insertion_point(message_clear_start:BMSinfo.BMSChargingEnd)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&endsoc_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&chargererrorreason_) -
      reinterpret_cast<char*>(&endsoc_)) + sizeof(chargererrorreason_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* BMSChargingEnd::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float endSOC = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          endsoc_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float monoBatVolMin = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 21)) {
          monobatvolmin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float monoBatVolMax = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          monobatvolmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float batTempMin = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 37)) {
          battempmin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float batTempMax = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 45)) {
          battempmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 bmsStopReason = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          bmsstopreason_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 bmsFaultReason = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          bmsfaultreason_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 bmsErrorReason = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          bmserrorreason_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 chargerStopReason = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72)) {
          chargerstopreason_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 chargerFaultReason = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          chargerfaultreason_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 chargerErrorReason = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          chargererrorreason_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* BMSChargingEnd::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:BMSinfo.BMSChargingEnd)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float endSOC = 1;
  if (!(this->_internal_endsoc() <= 0 && this->_internal_endsoc() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_endsoc(), target);
  }

  // float monoBatVolMin = 2;
  if (!(this->_internal_monobatvolmin() <= 0 && this->_internal_monobatvolmin() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_monobatvolmin(), target);
  }

  // float monoBatVolMax = 3;
  if (!(this->_internal_monobatvolmax() <= 0 && this->_internal_monobatvolmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_monobatvolmax(), target);
  }

  // float batTempMin = 4;
  if (!(this->_internal_battempmin() <= 0 && this->_internal_battempmin() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_battempmin(), target);
  }

  // float batTempMax = 5;
  if (!(this->_internal_battempmax() <= 0 && this->_internal_battempmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(5, this->_internal_battempmax(), target);
  }

  // uint32 bmsStopReason = 6;
  if (this->_internal_bmsstopreason() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_bmsstopreason(), target);
  }

  // uint32 bmsFaultReason = 7;
  if (this->_internal_bmsfaultreason() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(7, this->_internal_bmsfaultreason(), target);
  }

  // uint32 bmsErrorReason = 8;
  if (this->_internal_bmserrorreason() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_bmserrorreason(), target);
  }

  // uint32 chargerStopReason = 9;
  if (this->_internal_chargerstopreason() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(9, this->_internal_chargerstopreason(), target);
  }

  // uint32 chargerFaultReason = 10;
  if (this->_internal_chargerfaultreason() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(10, this->_internal_chargerfaultreason(), target);
  }

  // uint32 chargerErrorReason = 11;
  if (this->_internal_chargererrorreason() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(11, this->_internal_chargererrorreason(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:BMSinfo.BMSChargingEnd)
  return target;
}

size_t BMSChargingEnd::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:BMSinfo.BMSChargingEnd)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float endSOC = 1;
  if (!(this->_internal_endsoc() <= 0 && this->_internal_endsoc() >= 0)) {
    total_size += 1 + 4;
  }

  // float monoBatVolMin = 2;
  if (!(this->_internal_monobatvolmin() <= 0 && this->_internal_monobatvolmin() >= 0)) {
    total_size += 1 + 4;
  }

  // float monoBatVolMax = 3;
  if (!(this->_internal_monobatvolmax() <= 0 && this->_internal_monobatvolmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float batTempMin = 4;
  if (!(this->_internal_battempmin() <= 0 && this->_internal_battempmin() >= 0)) {
    total_size += 1 + 4;
  }

  // float batTempMax = 5;
  if (!(this->_internal_battempmax() <= 0 && this->_internal_battempmax() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 bmsStopReason = 6;
  if (this->_internal_bmsstopreason() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsstopreason());
  }

  // uint32 bmsFaultReason = 7;
  if (this->_internal_bmsfaultreason() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsfaultreason());
  }

  // uint32 bmsErrorReason = 8;
  if (this->_internal_bmserrorreason() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmserrorreason());
  }

  // uint32 chargerStopReason = 9;
  if (this->_internal_chargerstopreason() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chargerstopreason());
  }

  // uint32 chargerFaultReason = 10;
  if (this->_internal_chargerfaultreason() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chargerfaultreason());
  }

  // uint32 chargerErrorReason = 11;
  if (this->_internal_chargererrorreason() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chargererrorreason());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData BMSChargingEnd::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    BMSChargingEnd::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*BMSChargingEnd::GetClassData() const { return &_class_data_; }

void BMSChargingEnd::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<BMSChargingEnd *>(to)->MergeFrom(
      static_cast<const BMSChargingEnd &>(from));
}


void BMSChargingEnd::MergeFrom(const BMSChargingEnd& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:BMSinfo.BMSChargingEnd)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from._internal_endsoc() <= 0 && from._internal_endsoc() >= 0)) {
    _internal_set_endsoc(from._internal_endsoc());
  }
  if (!(from._internal_monobatvolmin() <= 0 && from._internal_monobatvolmin() >= 0)) {
    _internal_set_monobatvolmin(from._internal_monobatvolmin());
  }
  if (!(from._internal_monobatvolmax() <= 0 && from._internal_monobatvolmax() >= 0)) {
    _internal_set_monobatvolmax(from._internal_monobatvolmax());
  }
  if (!(from._internal_battempmin() <= 0 && from._internal_battempmin() >= 0)) {
    _internal_set_battempmin(from._internal_battempmin());
  }
  if (!(from._internal_battempmax() <= 0 && from._internal_battempmax() >= 0)) {
    _internal_set_battempmax(from._internal_battempmax());
  }
  if (from._internal_bmsstopreason() != 0) {
    _internal_set_bmsstopreason(from._internal_bmsstopreason());
  }
  if (from._internal_bmsfaultreason() != 0) {
    _internal_set_bmsfaultreason(from._internal_bmsfaultreason());
  }
  if (from._internal_bmserrorreason() != 0) {
    _internal_set_bmserrorreason(from._internal_bmserrorreason());
  }
  if (from._internal_chargerstopreason() != 0) {
    _internal_set_chargerstopreason(from._internal_chargerstopreason());
  }
  if (from._internal_chargerfaultreason() != 0) {
    _internal_set_chargerfaultreason(from._internal_chargerfaultreason());
  }
  if (from._internal_chargererrorreason() != 0) {
    _internal_set_chargererrorreason(from._internal_chargererrorreason());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void BMSChargingEnd::CopyFrom(const BMSChargingEnd& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:BMSinfo.BMSChargingEnd)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BMSChargingEnd::IsInitialized() const {
  return true;
}

void BMSChargingEnd::InternalSwap(BMSChargingEnd* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(BMSChargingEnd, chargererrorreason_)
      + sizeof(BMSChargingEnd::chargererrorreason_)
      - PROTOBUF_FIELD_OFFSET(BMSChargingEnd, endsoc_)>(
          reinterpret_cast<char*>(&endsoc_),
          reinterpret_cast<char*>(&other->endsoc_));
}

::PROTOBUF_NAMESPACE_ID::Metadata BMSChargingEnd::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fBMS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fBMS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fBMS_5fINFO_2eproto[3]);
}

// ===================================================================

class BMSCharging::_Internal {
 public:
};

BMSCharging::BMSCharging(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:BMSinfo.BMSCharging)
}
BMSCharging::BMSCharging(const BMSCharging& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&chargemode_, &from.chargemode_,
    static_cast<size_t>(reinterpret_cast<char*>(&tempmin_) -
    reinterpret_cast<char*>(&chargemode_)) + sizeof(tempmin_));
  // @@protoc_insertion_point(copy_constructor:BMSinfo.BMSCharging)
}

inline void BMSCharging::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&chargemode_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&tempmin_) -
    reinterpret_cast<char*>(&chargemode_)) + sizeof(tempmin_));
}

BMSCharging::~BMSCharging() {
  // @@protoc_insertion_point(destructor:BMSinfo.BMSCharging)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void BMSCharging::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void BMSCharging::ArenaDtor(void* object) {
  BMSCharging* _this = reinterpret_cast< BMSCharging* >(object);
  (void)_this;
}
void BMSCharging::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void BMSCharging::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void BMSCharging::Clear() {
// @@protoc_insertion_point(message_clear_start:BMSinfo.BMSCharging)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&chargemode_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&tempmin_) -
      reinterpret_cast<char*>(&chargemode_)) + sizeof(tempmin_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* BMSCharging::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .BMSinfo.ChargingMode chargeMode = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_chargemode(static_cast<::BMSinfo::ChargingMode>(val));
        } else goto handle_unusual;
        continue;
      // uint32 heatMode = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          heatmode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 totalChgTime = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          totalchgtime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 remainChgTime = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          remainchgtime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 monoBatVolMaxCode = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          monobatvolmaxcode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 monoBatVolMinCode = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          monobatvolmincode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 tempMaxCode = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          tempmaxcode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 tempMinCode = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          tempmincode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 volMaxGroupNum = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72)) {
          volmaxgroupnum_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 monoBatVolOver = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          monobatvolover_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 monoBatVolUnder = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          monobatvolunder_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 socOver = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 96)) {
          socover_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 socUnder = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 104)) {
          socunder_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 batCurOver = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 112)) {
          batcurover_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 batTempOver = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 120)) {
          battempover_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 insulationAbnormal = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 128)) {
          insulationabnormal_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 outConnectAbnormal = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 136)) {
          outconnectabnormal_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 bmsAllow = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 144)) {
          bmsallow_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 chargerAllow = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 152)) {
          chargerallow_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float socNow = 20;
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 165)) {
          socnow_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float volDemand = 21;
      case 21:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 173)) {
          voldemand_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float curDemand = 22;
      case 22:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 181)) {
          curdemand_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float volMeasured = 23;
      case 23:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 189)) {
          volmeasured_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float curMeasured = 24;
      case 24:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 197)) {
          curmeasured_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float monoBatVolMax = 25;
      case 25:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 205)) {
          monobatvolmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float monoBatVolMin = 26;
      case 26:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 213)) {
          monobatvolmin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float tempMax = 27;
      case 27:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 221)) {
          tempmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float tempMin = 28;
      case 28:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 229)) {
          tempmin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* BMSCharging::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:BMSinfo.BMSCharging)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .BMSinfo.ChargingMode chargeMode = 1;
  if (this->_internal_chargemode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_chargemode(), target);
  }

  // uint32 heatMode = 2;
  if (this->_internal_heatmode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_heatmode(), target);
  }

  // uint32 totalChgTime = 3;
  if (this->_internal_totalchgtime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_totalchgtime(), target);
  }

  // uint32 remainChgTime = 4;
  if (this->_internal_remainchgtime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_remainchgtime(), target);
  }

  // uint32 monoBatVolMaxCode = 5;
  if (this->_internal_monobatvolmaxcode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_monobatvolmaxcode(), target);
  }

  // uint32 monoBatVolMinCode = 6;
  if (this->_internal_monobatvolmincode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_monobatvolmincode(), target);
  }

  // uint32 tempMaxCode = 7;
  if (this->_internal_tempmaxcode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(7, this->_internal_tempmaxcode(), target);
  }

  // uint32 tempMinCode = 8;
  if (this->_internal_tempmincode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_tempmincode(), target);
  }

  // uint32 volMaxGroupNum = 9;
  if (this->_internal_volmaxgroupnum() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(9, this->_internal_volmaxgroupnum(), target);
  }

  // uint32 monoBatVolOver = 10;
  if (this->_internal_monobatvolover() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(10, this->_internal_monobatvolover(), target);
  }

  // uint32 monoBatVolUnder = 11;
  if (this->_internal_monobatvolunder() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(11, this->_internal_monobatvolunder(), target);
  }

  // uint32 socOver = 12;
  if (this->_internal_socover() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(12, this->_internal_socover(), target);
  }

  // uint32 socUnder = 13;
  if (this->_internal_socunder() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(13, this->_internal_socunder(), target);
  }

  // uint32 batCurOver = 14;
  if (this->_internal_batcurover() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(14, this->_internal_batcurover(), target);
  }

  // uint32 batTempOver = 15;
  if (this->_internal_battempover() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(15, this->_internal_battempover(), target);
  }

  // uint32 insulationAbnormal = 16;
  if (this->_internal_insulationabnormal() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(16, this->_internal_insulationabnormal(), target);
  }

  // uint32 outConnectAbnormal = 17;
  if (this->_internal_outconnectabnormal() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(17, this->_internal_outconnectabnormal(), target);
  }

  // uint32 bmsAllow = 18;
  if (this->_internal_bmsallow() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(18, this->_internal_bmsallow(), target);
  }

  // uint32 chargerAllow = 19;
  if (this->_internal_chargerallow() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(19, this->_internal_chargerallow(), target);
  }

  // float socNow = 20;
  if (!(this->_internal_socnow() <= 0 && this->_internal_socnow() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(20, this->_internal_socnow(), target);
  }

  // float volDemand = 21;
  if (!(this->_internal_voldemand() <= 0 && this->_internal_voldemand() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(21, this->_internal_voldemand(), target);
  }

  // float curDemand = 22;
  if (!(this->_internal_curdemand() <= 0 && this->_internal_curdemand() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(22, this->_internal_curdemand(), target);
  }

  // float volMeasured = 23;
  if (!(this->_internal_volmeasured() <= 0 && this->_internal_volmeasured() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(23, this->_internal_volmeasured(), target);
  }

  // float curMeasured = 24;
  if (!(this->_internal_curmeasured() <= 0 && this->_internal_curmeasured() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(24, this->_internal_curmeasured(), target);
  }

  // float monoBatVolMax = 25;
  if (!(this->_internal_monobatvolmax() <= 0 && this->_internal_monobatvolmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(25, this->_internal_monobatvolmax(), target);
  }

  // float monoBatVolMin = 26;
  if (!(this->_internal_monobatvolmin() <= 0 && this->_internal_monobatvolmin() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(26, this->_internal_monobatvolmin(), target);
  }

  // float tempMax = 27;
  if (!(this->_internal_tempmax() <= 0 && this->_internal_tempmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(27, this->_internal_tempmax(), target);
  }

  // float tempMin = 28;
  if (!(this->_internal_tempmin() <= 0 && this->_internal_tempmin() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(28, this->_internal_tempmin(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:BMSinfo.BMSCharging)
  return target;
}

size_t BMSCharging::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:BMSinfo.BMSCharging)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .BMSinfo.ChargingMode chargeMode = 1;
  if (this->_internal_chargemode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_chargemode());
  }

  // uint32 heatMode = 2;
  if (this->_internal_heatmode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_heatmode());
  }

  // uint32 totalChgTime = 3;
  if (this->_internal_totalchgtime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_totalchgtime());
  }

  // uint32 remainChgTime = 4;
  if (this->_internal_remainchgtime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_remainchgtime());
  }

  // uint32 monoBatVolMaxCode = 5;
  if (this->_internal_monobatvolmaxcode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_monobatvolmaxcode());
  }

  // uint32 monoBatVolMinCode = 6;
  if (this->_internal_monobatvolmincode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_monobatvolmincode());
  }

  // uint32 tempMaxCode = 7;
  if (this->_internal_tempmaxcode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_tempmaxcode());
  }

  // uint32 tempMinCode = 8;
  if (this->_internal_tempmincode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_tempmincode());
  }

  // uint32 volMaxGroupNum = 9;
  if (this->_internal_volmaxgroupnum() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_volmaxgroupnum());
  }

  // uint32 monoBatVolOver = 10;
  if (this->_internal_monobatvolover() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_monobatvolover());
  }

  // uint32 monoBatVolUnder = 11;
  if (this->_internal_monobatvolunder() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_monobatvolunder());
  }

  // uint32 socOver = 12;
  if (this->_internal_socover() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_socover());
  }

  // uint32 socUnder = 13;
  if (this->_internal_socunder() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_socunder());
  }

  // uint32 batCurOver = 14;
  if (this->_internal_batcurover() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_batcurover());
  }

  // uint32 batTempOver = 15;
  if (this->_internal_battempover() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_battempover());
  }

  // uint32 insulationAbnormal = 16;
  if (this->_internal_insulationabnormal() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_insulationabnormal());
  }

  // uint32 outConnectAbnormal = 17;
  if (this->_internal_outconnectabnormal() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_outconnectabnormal());
  }

  // uint32 bmsAllow = 18;
  if (this->_internal_bmsallow() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsallow());
  }

  // uint32 chargerAllow = 19;
  if (this->_internal_chargerallow() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chargerallow());
  }

  // float socNow = 20;
  if (!(this->_internal_socnow() <= 0 && this->_internal_socnow() >= 0)) {
    total_size += 2 + 4;
  }

  // float volDemand = 21;
  if (!(this->_internal_voldemand() <= 0 && this->_internal_voldemand() >= 0)) {
    total_size += 2 + 4;
  }

  // float curDemand = 22;
  if (!(this->_internal_curdemand() <= 0 && this->_internal_curdemand() >= 0)) {
    total_size += 2 + 4;
  }

  // float volMeasured = 23;
  if (!(this->_internal_volmeasured() <= 0 && this->_internal_volmeasured() >= 0)) {
    total_size += 2 + 4;
  }

  // float curMeasured = 24;
  if (!(this->_internal_curmeasured() <= 0 && this->_internal_curmeasured() >= 0)) {
    total_size += 2 + 4;
  }

  // float monoBatVolMax = 25;
  if (!(this->_internal_monobatvolmax() <= 0 && this->_internal_monobatvolmax() >= 0)) {
    total_size += 2 + 4;
  }

  // float monoBatVolMin = 26;
  if (!(this->_internal_monobatvolmin() <= 0 && this->_internal_monobatvolmin() >= 0)) {
    total_size += 2 + 4;
  }

  // float tempMax = 27;
  if (!(this->_internal_tempmax() <= 0 && this->_internal_tempmax() >= 0)) {
    total_size += 2 + 4;
  }

  // float tempMin = 28;
  if (!(this->_internal_tempmin() <= 0 && this->_internal_tempmin() >= 0)) {
    total_size += 2 + 4;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData BMSCharging::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    BMSCharging::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*BMSCharging::GetClassData() const { return &_class_data_; }

void BMSCharging::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<BMSCharging *>(to)->MergeFrom(
      static_cast<const BMSCharging &>(from));
}


void BMSCharging::MergeFrom(const BMSCharging& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:BMSinfo.BMSCharging)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_chargemode() != 0) {
    _internal_set_chargemode(from._internal_chargemode());
  }
  if (from._internal_heatmode() != 0) {
    _internal_set_heatmode(from._internal_heatmode());
  }
  if (from._internal_totalchgtime() != 0) {
    _internal_set_totalchgtime(from._internal_totalchgtime());
  }
  if (from._internal_remainchgtime() != 0) {
    _internal_set_remainchgtime(from._internal_remainchgtime());
  }
  if (from._internal_monobatvolmaxcode() != 0) {
    _internal_set_monobatvolmaxcode(from._internal_monobatvolmaxcode());
  }
  if (from._internal_monobatvolmincode() != 0) {
    _internal_set_monobatvolmincode(from._internal_monobatvolmincode());
  }
  if (from._internal_tempmaxcode() != 0) {
    _internal_set_tempmaxcode(from._internal_tempmaxcode());
  }
  if (from._internal_tempmincode() != 0) {
    _internal_set_tempmincode(from._internal_tempmincode());
  }
  if (from._internal_volmaxgroupnum() != 0) {
    _internal_set_volmaxgroupnum(from._internal_volmaxgroupnum());
  }
  if (from._internal_monobatvolover() != 0) {
    _internal_set_monobatvolover(from._internal_monobatvolover());
  }
  if (from._internal_monobatvolunder() != 0) {
    _internal_set_monobatvolunder(from._internal_monobatvolunder());
  }
  if (from._internal_socover() != 0) {
    _internal_set_socover(from._internal_socover());
  }
  if (from._internal_socunder() != 0) {
    _internal_set_socunder(from._internal_socunder());
  }
  if (from._internal_batcurover() != 0) {
    _internal_set_batcurover(from._internal_batcurover());
  }
  if (from._internal_battempover() != 0) {
    _internal_set_battempover(from._internal_battempover());
  }
  if (from._internal_insulationabnormal() != 0) {
    _internal_set_insulationabnormal(from._internal_insulationabnormal());
  }
  if (from._internal_outconnectabnormal() != 0) {
    _internal_set_outconnectabnormal(from._internal_outconnectabnormal());
  }
  if (from._internal_bmsallow() != 0) {
    _internal_set_bmsallow(from._internal_bmsallow());
  }
  if (from._internal_chargerallow() != 0) {
    _internal_set_chargerallow(from._internal_chargerallow());
  }
  if (!(from._internal_socnow() <= 0 && from._internal_socnow() >= 0)) {
    _internal_set_socnow(from._internal_socnow());
  }
  if (!(from._internal_voldemand() <= 0 && from._internal_voldemand() >= 0)) {
    _internal_set_voldemand(from._internal_voldemand());
  }
  if (!(from._internal_curdemand() <= 0 && from._internal_curdemand() >= 0)) {
    _internal_set_curdemand(from._internal_curdemand());
  }
  if (!(from._internal_volmeasured() <= 0 && from._internal_volmeasured() >= 0)) {
    _internal_set_volmeasured(from._internal_volmeasured());
  }
  if (!(from._internal_curmeasured() <= 0 && from._internal_curmeasured() >= 0)) {
    _internal_set_curmeasured(from._internal_curmeasured());
  }
  if (!(from._internal_monobatvolmax() <= 0 && from._internal_monobatvolmax() >= 0)) {
    _internal_set_monobatvolmax(from._internal_monobatvolmax());
  }
  if (!(from._internal_monobatvolmin() <= 0 && from._internal_monobatvolmin() >= 0)) {
    _internal_set_monobatvolmin(from._internal_monobatvolmin());
  }
  if (!(from._internal_tempmax() <= 0 && from._internal_tempmax() >= 0)) {
    _internal_set_tempmax(from._internal_tempmax());
  }
  if (!(from._internal_tempmin() <= 0 && from._internal_tempmin() >= 0)) {
    _internal_set_tempmin(from._internal_tempmin());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void BMSCharging::CopyFrom(const BMSCharging& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:BMSinfo.BMSCharging)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BMSCharging::IsInitialized() const {
  return true;
}

void BMSCharging::InternalSwap(BMSCharging* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(BMSCharging, tempmin_)
      + sizeof(BMSCharging::tempmin_)
      - PROTOBUF_FIELD_OFFSET(BMSCharging, chargemode_)>(
          reinterpret_cast<char*>(&chargemode_),
          reinterpret_cast<char*>(&other->chargemode_));
}

::PROTOBUF_NAMESPACE_ID::Metadata BMSCharging::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fBMS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fBMS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fBMS_5fINFO_2eproto[4]);
}

// ===================================================================

class BMSTimeout::_Internal {
 public:
};

BMSTimeout::BMSTimeout(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:BMSinfo.BMSTimeout)
}
BMSTimeout::BMSTimeout(const BMSTimeout& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&bmserrorframe_, &from.bmserrorframe_,
    static_cast<size_t>(reinterpret_cast<char*>(&chargererrorframe_) -
    reinterpret_cast<char*>(&bmserrorframe_)) + sizeof(chargererrorframe_));
  // @@protoc_insertion_point(copy_constructor:BMSinfo.BMSTimeout)
}

inline void BMSTimeout::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&bmserrorframe_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&chargererrorframe_) -
    reinterpret_cast<char*>(&bmserrorframe_)) + sizeof(chargererrorframe_));
}

BMSTimeout::~BMSTimeout() {
  // @@protoc_insertion_point(destructor:BMSinfo.BMSTimeout)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void BMSTimeout::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void BMSTimeout::ArenaDtor(void* object) {
  BMSTimeout* _this = reinterpret_cast< BMSTimeout* >(object);
  (void)_this;
}
void BMSTimeout::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void BMSTimeout::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void BMSTimeout::Clear() {
// @@protoc_insertion_point(message_clear_start:BMSinfo.BMSTimeout)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&bmserrorframe_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&chargererrorframe_) -
      reinterpret_cast<char*>(&bmserrorframe_)) + sizeof(chargererrorframe_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* BMSTimeout::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 bmsErrorFrame = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          bmserrorframe_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 chargerErrorFrame = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          chargererrorframe_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* BMSTimeout::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:BMSinfo.BMSTimeout)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 bmsErrorFrame = 1;
  if (this->_internal_bmserrorframe() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_bmserrorframe(), target);
  }

  // uint32 chargerErrorFrame = 2;
  if (this->_internal_chargererrorframe() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_chargererrorframe(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:BMSinfo.BMSTimeout)
  return target;
}

size_t BMSTimeout::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:BMSinfo.BMSTimeout)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 bmsErrorFrame = 1;
  if (this->_internal_bmserrorframe() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmserrorframe());
  }

  // uint32 chargerErrorFrame = 2;
  if (this->_internal_chargererrorframe() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chargererrorframe());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData BMSTimeout::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    BMSTimeout::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*BMSTimeout::GetClassData() const { return &_class_data_; }

void BMSTimeout::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<BMSTimeout *>(to)->MergeFrom(
      static_cast<const BMSTimeout &>(from));
}


void BMSTimeout::MergeFrom(const BMSTimeout& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:BMSinfo.BMSTimeout)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_bmserrorframe() != 0) {
    _internal_set_bmserrorframe(from._internal_bmserrorframe());
  }
  if (from._internal_chargererrorframe() != 0) {
    _internal_set_chargererrorframe(from._internal_chargererrorframe());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void BMSTimeout::CopyFrom(const BMSTimeout& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:BMSinfo.BMSTimeout)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BMSTimeout::IsInitialized() const {
  return true;
}

void BMSTimeout::InternalSwap(BMSTimeout* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(BMSTimeout, chargererrorframe_)
      + sizeof(BMSTimeout::chargererrorframe_)
      - PROTOBUF_FIELD_OFFSET(BMSTimeout, bmserrorframe_)>(
          reinterpret_cast<char*>(&bmserrorframe_),
          reinterpret_cast<char*>(&other->bmserrorframe_));
}

::PROTOBUF_NAMESPACE_ID::Metadata BMSTimeout::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fBMS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fBMS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fBMS_5fINFO_2eproto[5]);
}

// ===================================================================

class BMSReconnectEvent::_Internal {
 public:
};

BMSReconnectEvent::BMSReconnectEvent(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:BMSinfo.BMSReconnectEvent)
}
BMSReconnectEvent::BMSReconnectEvent(const BMSReconnectEvent& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&timeoutstate_, &from.timeoutstate_,
    static_cast<size_t>(reinterpret_cast<char*>(&nextstate_) -
    reinterpret_cast<char*>(&timeoutstate_)) + sizeof(nextstate_));
  // @@protoc_insertion_point(copy_constructor:BMSinfo.BMSReconnectEvent)
}

inline void BMSReconnectEvent::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&timeoutstate_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&nextstate_) -
    reinterpret_cast<char*>(&timeoutstate_)) + sizeof(nextstate_));
}

BMSReconnectEvent::~BMSReconnectEvent() {
  // @@protoc_insertion_point(destructor:BMSinfo.BMSReconnectEvent)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void BMSReconnectEvent::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void BMSReconnectEvent::ArenaDtor(void* object) {
  BMSReconnectEvent* _this = reinterpret_cast< BMSReconnectEvent* >(object);
  (void)_this;
}
void BMSReconnectEvent::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void BMSReconnectEvent::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void BMSReconnectEvent::Clear() {
// @@protoc_insertion_point(message_clear_start:BMSinfo.BMSReconnectEvent)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&timeoutstate_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&nextstate_) -
      reinterpret_cast<char*>(&timeoutstate_)) + sizeof(nextstate_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* BMSReconnectEvent::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 timeoutState = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          timeoutstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .BMSinfo.BMSTimeoutEnum bmsTimeoutType = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_bmstimeouttype(static_cast<::BMSinfo::BMSTimeoutEnum>(val));
        } else goto handle_unusual;
        continue;
      // uint32 reconnectCnt = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          reconnectcnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 nextState = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          nextstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* BMSReconnectEvent::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:BMSinfo.BMSReconnectEvent)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 timeoutState = 1;
  if (this->_internal_timeoutstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_timeoutstate(), target);
  }

  // .BMSinfo.BMSTimeoutEnum bmsTimeoutType = 2;
  if (this->_internal_bmstimeouttype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_bmstimeouttype(), target);
  }

  // uint32 reconnectCnt = 3;
  if (this->_internal_reconnectcnt() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_reconnectcnt(), target);
  }

  // uint32 nextState = 4;
  if (this->_internal_nextstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_nextstate(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:BMSinfo.BMSReconnectEvent)
  return target;
}

size_t BMSReconnectEvent::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:BMSinfo.BMSReconnectEvent)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 timeoutState = 1;
  if (this->_internal_timeoutstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_timeoutstate());
  }

  // .BMSinfo.BMSTimeoutEnum bmsTimeoutType = 2;
  if (this->_internal_bmstimeouttype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_bmstimeouttype());
  }

  // uint32 reconnectCnt = 3;
  if (this->_internal_reconnectcnt() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_reconnectcnt());
  }

  // uint32 nextState = 4;
  if (this->_internal_nextstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_nextstate());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData BMSReconnectEvent::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    BMSReconnectEvent::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*BMSReconnectEvent::GetClassData() const { return &_class_data_; }

void BMSReconnectEvent::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<BMSReconnectEvent *>(to)->MergeFrom(
      static_cast<const BMSReconnectEvent &>(from));
}


void BMSReconnectEvent::MergeFrom(const BMSReconnectEvent& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:BMSinfo.BMSReconnectEvent)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_timeoutstate() != 0) {
    _internal_set_timeoutstate(from._internal_timeoutstate());
  }
  if (from._internal_bmstimeouttype() != 0) {
    _internal_set_bmstimeouttype(from._internal_bmstimeouttype());
  }
  if (from._internal_reconnectcnt() != 0) {
    _internal_set_reconnectcnt(from._internal_reconnectcnt());
  }
  if (from._internal_nextstate() != 0) {
    _internal_set_nextstate(from._internal_nextstate());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void BMSReconnectEvent::CopyFrom(const BMSReconnectEvent& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:BMSinfo.BMSReconnectEvent)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BMSReconnectEvent::IsInitialized() const {
  return true;
}

void BMSReconnectEvent::InternalSwap(BMSReconnectEvent* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(BMSReconnectEvent, nextstate_)
      + sizeof(BMSReconnectEvent::nextstate_)
      - PROTOBUF_FIELD_OFFSET(BMSReconnectEvent, timeoutstate_)>(
          reinterpret_cast<char*>(&timeoutstate_),
          reinterpret_cast<char*>(&other->timeoutstate_));
}

::PROTOBUF_NAMESPACE_ID::Metadata BMSReconnectEvent::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fBMS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fBMS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fBMS_5fINFO_2eproto[6]);
}

// ===================================================================

class bms2015Msg::_Internal {
 public:
  static const ::BMSinfo::BMSHandShake& bmshandshakem(const bms2015Msg* msg);
  static const ::BMSinfo::BMSVerification& bmsverifym(const bms2015Msg* msg);
  static const ::BMSinfo::BMSConfig& bmsconfigm(const bms2015Msg* msg);
  static const ::BMSinfo::BMSCharging& bmschargingm(const bms2015Msg* msg);
  static const ::BMSinfo::BMSChargingEnd& bmschargefinishm(const bms2015Msg* msg);
};

const ::BMSinfo::BMSHandShake&
bms2015Msg::_Internal::bmshandshakem(const bms2015Msg* msg) {
  return *msg->bmshandshakem_;
}
const ::BMSinfo::BMSVerification&
bms2015Msg::_Internal::bmsverifym(const bms2015Msg* msg) {
  return *msg->bmsverifym_;
}
const ::BMSinfo::BMSConfig&
bms2015Msg::_Internal::bmsconfigm(const bms2015Msg* msg) {
  return *msg->bmsconfigm_;
}
const ::BMSinfo::BMSCharging&
bms2015Msg::_Internal::bmschargingm(const bms2015Msg* msg) {
  return *msg->bmschargingm_;
}
const ::BMSinfo::BMSChargingEnd&
bms2015Msg::_Internal::bmschargefinishm(const bms2015Msg* msg) {
  return *msg->bmschargefinishm_;
}
bms2015Msg::bms2015Msg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:BMSinfo.bms2015Msg)
}
bms2015Msg::bms2015Msg(const bms2015Msg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_bmshandshakem()) {
    bmshandshakem_ = new ::BMSinfo::BMSHandShake(*from.bmshandshakem_);
  } else {
    bmshandshakem_ = nullptr;
  }
  if (from._internal_has_bmsverifym()) {
    bmsverifym_ = new ::BMSinfo::BMSVerification(*from.bmsverifym_);
  } else {
    bmsverifym_ = nullptr;
  }
  if (from._internal_has_bmsconfigm()) {
    bmsconfigm_ = new ::BMSinfo::BMSConfig(*from.bmsconfigm_);
  } else {
    bmsconfigm_ = nullptr;
  }
  if (from._internal_has_bmschargingm()) {
    bmschargingm_ = new ::BMSinfo::BMSCharging(*from.bmschargingm_);
  } else {
    bmschargingm_ = nullptr;
  }
  if (from._internal_has_bmschargefinishm()) {
    bmschargefinishm_ = new ::BMSinfo::BMSChargingEnd(*from.bmschargefinishm_);
  } else {
    bmschargefinishm_ = nullptr;
  }
  bmsstate_ = from.bmsstate_;
  // @@protoc_insertion_point(copy_constructor:BMSinfo.bms2015Msg)
}

inline void bms2015Msg::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&bmshandshakem_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&bmsstate_) -
    reinterpret_cast<char*>(&bmshandshakem_)) + sizeof(bmsstate_));
}

bms2015Msg::~bms2015Msg() {
  // @@protoc_insertion_point(destructor:BMSinfo.bms2015Msg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void bms2015Msg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete bmshandshakem_;
  if (this != internal_default_instance()) delete bmsverifym_;
  if (this != internal_default_instance()) delete bmsconfigm_;
  if (this != internal_default_instance()) delete bmschargingm_;
  if (this != internal_default_instance()) delete bmschargefinishm_;
}

void bms2015Msg::ArenaDtor(void* object) {
  bms2015Msg* _this = reinterpret_cast< bms2015Msg* >(object);
  (void)_this;
}
void bms2015Msg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void bms2015Msg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void bms2015Msg::Clear() {
// @@protoc_insertion_point(message_clear_start:BMSinfo.bms2015Msg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && bmshandshakem_ != nullptr) {
    delete bmshandshakem_;
  }
  bmshandshakem_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bmsverifym_ != nullptr) {
    delete bmsverifym_;
  }
  bmsverifym_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bmsconfigm_ != nullptr) {
    delete bmsconfigm_;
  }
  bmsconfigm_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bmschargingm_ != nullptr) {
    delete bmschargingm_;
  }
  bmschargingm_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bmschargefinishm_ != nullptr) {
    delete bmschargefinishm_;
  }
  bmschargefinishm_ = nullptr;
  bmsstate_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* bms2015Msg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .BMSinfo.ChargeState bmsState = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_bmsstate(static_cast<::BMSinfo::ChargeState>(val));
        } else goto handle_unusual;
        continue;
      // .BMSinfo.BMSHandShake BmshandShakeM = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_bmshandshakem(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .BMSinfo.BMSVerification BmsVerifyM = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_bmsverifym(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .BMSinfo.BMSConfig BmsConfigM = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_bmsconfigm(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .BMSinfo.BMSCharging BmsChargingM = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_bmschargingm(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .BMSinfo.BMSChargingEnd BmsChargeFinishM = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          ptr = ctx->ParseMessage(_internal_mutable_bmschargefinishm(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* bms2015Msg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:BMSinfo.bms2015Msg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .BMSinfo.ChargeState bmsState = 3;
  if (this->_internal_bmsstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_bmsstate(), target);
  }

  // .BMSinfo.BMSHandShake BmshandShakeM = 4;
  if (this->_internal_has_bmshandshakem()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::bmshandshakem(this), target, stream);
  }

  // .BMSinfo.BMSVerification BmsVerifyM = 5;
  if (this->_internal_has_bmsverifym()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::bmsverifym(this), target, stream);
  }

  // .BMSinfo.BMSConfig BmsConfigM = 6;
  if (this->_internal_has_bmsconfigm()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::bmsconfigm(this), target, stream);
  }

  // .BMSinfo.BMSCharging BmsChargingM = 7;
  if (this->_internal_has_bmschargingm()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::bmschargingm(this), target, stream);
  }

  // .BMSinfo.BMSChargingEnd BmsChargeFinishM = 8;
  if (this->_internal_has_bmschargefinishm()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        8, _Internal::bmschargefinishm(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:BMSinfo.bms2015Msg)
  return target;
}

size_t bms2015Msg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:BMSinfo.bms2015Msg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .BMSinfo.BMSHandShake BmshandShakeM = 4;
  if (this->_internal_has_bmshandshakem()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bmshandshakem_);
  }

  // .BMSinfo.BMSVerification BmsVerifyM = 5;
  if (this->_internal_has_bmsverifym()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bmsverifym_);
  }

  // .BMSinfo.BMSConfig BmsConfigM = 6;
  if (this->_internal_has_bmsconfigm()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bmsconfigm_);
  }

  // .BMSinfo.BMSCharging BmsChargingM = 7;
  if (this->_internal_has_bmschargingm()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bmschargingm_);
  }

  // .BMSinfo.BMSChargingEnd BmsChargeFinishM = 8;
  if (this->_internal_has_bmschargefinishm()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bmschargefinishm_);
  }

  // .BMSinfo.ChargeState bmsState = 3;
  if (this->_internal_bmsstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_bmsstate());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData bms2015Msg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    bms2015Msg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*bms2015Msg::GetClassData() const { return &_class_data_; }

void bms2015Msg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<bms2015Msg *>(to)->MergeFrom(
      static_cast<const bms2015Msg &>(from));
}


void bms2015Msg::MergeFrom(const bms2015Msg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:BMSinfo.bms2015Msg)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_bmshandshakem()) {
    _internal_mutable_bmshandshakem()->::BMSinfo::BMSHandShake::MergeFrom(from._internal_bmshandshakem());
  }
  if (from._internal_has_bmsverifym()) {
    _internal_mutable_bmsverifym()->::BMSinfo::BMSVerification::MergeFrom(from._internal_bmsverifym());
  }
  if (from._internal_has_bmsconfigm()) {
    _internal_mutable_bmsconfigm()->::BMSinfo::BMSConfig::MergeFrom(from._internal_bmsconfigm());
  }
  if (from._internal_has_bmschargingm()) {
    _internal_mutable_bmschargingm()->::BMSinfo::BMSCharging::MergeFrom(from._internal_bmschargingm());
  }
  if (from._internal_has_bmschargefinishm()) {
    _internal_mutable_bmschargefinishm()->::BMSinfo::BMSChargingEnd::MergeFrom(from._internal_bmschargefinishm());
  }
  if (from._internal_bmsstate() != 0) {
    _internal_set_bmsstate(from._internal_bmsstate());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void bms2015Msg::CopyFrom(const bms2015Msg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:BMSinfo.bms2015Msg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool bms2015Msg::IsInitialized() const {
  return true;
}

void bms2015Msg::InternalSwap(bms2015Msg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(bms2015Msg, bmsstate_)
      + sizeof(bms2015Msg::bmsstate_)
      - PROTOBUF_FIELD_OFFSET(bms2015Msg, bmshandshakem_)>(
          reinterpret_cast<char*>(&bmshandshakem_),
          reinterpret_cast<char*>(&other->bmshandshakem_));
}

::PROTOBUF_NAMESPACE_ID::Metadata bms2015Msg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fBMS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fBMS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fBMS_5fINFO_2eproto[7]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace BMSinfo
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::BMSinfo::BMSHandShake* Arena::CreateMaybeMessage< ::BMSinfo::BMSHandShake >(Arena* arena) {
  return Arena::CreateMessageInternal< ::BMSinfo::BMSHandShake >(arena);
}
template<> PROTOBUF_NOINLINE ::BMSinfo::BMSVerification* Arena::CreateMaybeMessage< ::BMSinfo::BMSVerification >(Arena* arena) {
  return Arena::CreateMessageInternal< ::BMSinfo::BMSVerification >(arena);
}
template<> PROTOBUF_NOINLINE ::BMSinfo::BMSConfig* Arena::CreateMaybeMessage< ::BMSinfo::BMSConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::BMSinfo::BMSConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::BMSinfo::BMSChargingEnd* Arena::CreateMaybeMessage< ::BMSinfo::BMSChargingEnd >(Arena* arena) {
  return Arena::CreateMessageInternal< ::BMSinfo::BMSChargingEnd >(arena);
}
template<> PROTOBUF_NOINLINE ::BMSinfo::BMSCharging* Arena::CreateMaybeMessage< ::BMSinfo::BMSCharging >(Arena* arena) {
  return Arena::CreateMessageInternal< ::BMSinfo::BMSCharging >(arena);
}
template<> PROTOBUF_NOINLINE ::BMSinfo::BMSTimeout* Arena::CreateMaybeMessage< ::BMSinfo::BMSTimeout >(Arena* arena) {
  return Arena::CreateMessageInternal< ::BMSinfo::BMSTimeout >(arena);
}
template<> PROTOBUF_NOINLINE ::BMSinfo::BMSReconnectEvent* Arena::CreateMaybeMessage< ::BMSinfo::BMSReconnectEvent >(Arena* arena) {
  return Arena::CreateMessageInternal< ::BMSinfo::BMSReconnectEvent >(arena);
}
template<> PROTOBUF_NOINLINE ::BMSinfo::bms2015Msg* Arena::CreateMaybeMessage< ::BMSinfo::bms2015Msg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::BMSinfo::bms2015Msg >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
