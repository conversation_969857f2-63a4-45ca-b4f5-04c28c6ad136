// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GCU_LCR_ALL.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_GCU_5fLCR_5fALL_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_GCU_5fLCR_5fALL_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3017000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3017003 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "GCU_OSC_INFO.pb.h"
#include "GCU_OHP_INFO.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_GCU_5fLCR_5fALL_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_GCU_5fLCR_5fALL_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[3]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GCU_5fLCR_5fALL_2eproto;
namespace gcu_lcr {
namespace protobuf {
class LogLoad;
struct LogLoadDefaultTypeInternal;
extern LogLoadDefaultTypeInternal _LogLoad_default_instance_;
class OrderLogLoad;
struct OrderLogLoadDefaultTypeInternal;
extern OrderLogLoadDefaultTypeInternal _OrderLogLoad_default_instance_;
class RecordDateGetBack;
struct RecordDateGetBackDefaultTypeInternal;
extern RecordDateGetBackDefaultTypeInternal _RecordDateGetBack_default_instance_;
}  // namespace protobuf
}  // namespace gcu_lcr
PROTOBUF_NAMESPACE_OPEN
template<> ::gcu_lcr::protobuf::LogLoad* Arena::CreateMaybeMessage<::gcu_lcr::protobuf::LogLoad>(Arena*);
template<> ::gcu_lcr::protobuf::OrderLogLoad* Arena::CreateMaybeMessage<::gcu_lcr::protobuf::OrderLogLoad>(Arena*);
template<> ::gcu_lcr::protobuf::RecordDateGetBack* Arena::CreateMaybeMessage<::gcu_lcr::protobuf::RecordDateGetBack>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace gcu_lcr {
namespace protobuf {

// ===================================================================

class LogLoad final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gcu_lcr.protobuf.LogLoad) */ {
 public:
  inline LogLoad() : LogLoad(nullptr) {}
  ~LogLoad() override;
  explicit constexpr LogLoad(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LogLoad(const LogLoad& from);
  LogLoad(LogLoad&& from) noexcept
    : LogLoad() {
    *this = ::std::move(from);
  }

  inline LogLoad& operator=(const LogLoad& from) {
    CopyFrom(from);
    return *this;
  }
  inline LogLoad& operator=(LogLoad&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LogLoad& default_instance() {
    return *internal_default_instance();
  }
  static inline const LogLoad* internal_default_instance() {
    return reinterpret_cast<const LogLoad*>(
               &_LogLoad_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(LogLoad& a, LogLoad& b) {
    a.Swap(&b);
  }
  inline void Swap(LogLoad* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LogLoad* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline LogLoad* New() const final {
    return new LogLoad();
  }

  LogLoad* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<LogLoad>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LogLoad& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LogLoad& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LogLoad* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gcu_lcr.protobuf.LogLoad";
  }
  protected:
  explicit LogLoad(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSentenceFieldNumber = 4,
    kModuleFieldNumber = 1,
    kLevelFieldNumber = 2,
    kTimestampFieldNumber = 3,
  };
  // bytes Sentence = 4;
  void clear_sentence();
  const std::string& sentence() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_sentence(ArgT0&& arg0, ArgT... args);
  std::string* mutable_sentence();
  PROTOBUF_MUST_USE_RESULT std::string* release_sentence();
  void set_allocated_sentence(std::string* sentence);
  private:
  const std::string& _internal_sentence() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_sentence(const std::string& value);
  std::string* _internal_mutable_sentence();
  public:

  // uint32 Module = 1;
  void clear_module();
  ::PROTOBUF_NAMESPACE_ID::uint32 module() const;
  void set_module(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_module() const;
  void _internal_set_module(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 Level = 2;
  void clear_level();
  ::PROTOBUF_NAMESPACE_ID::uint32 level() const;
  void set_level(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_level() const;
  void _internal_set_level(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // int64 Timestamp = 3;
  void clear_timestamp();
  ::PROTOBUF_NAMESPACE_ID::int64 timestamp() const;
  void set_timestamp(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_timestamp() const;
  void _internal_set_timestamp(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // @@protoc_insertion_point(class_scope:gcu_lcr.protobuf.LogLoad)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr sentence_;
  ::PROTOBUF_NAMESPACE_ID::uint32 module_;
  ::PROTOBUF_NAMESPACE_ID::uint32 level_;
  ::PROTOBUF_NAMESPACE_ID::int64 timestamp_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fLCR_5fALL_2eproto;
};
// -------------------------------------------------------------------

class OrderLogLoad final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gcu_lcr.protobuf.OrderLogLoad) */ {
 public:
  inline OrderLogLoad() : OrderLogLoad(nullptr) {}
  ~OrderLogLoad() override;
  explicit constexpr OrderLogLoad(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OrderLogLoad(const OrderLogLoad& from);
  OrderLogLoad(OrderLogLoad&& from) noexcept
    : OrderLogLoad() {
    *this = ::std::move(from);
  }

  inline OrderLogLoad& operator=(const OrderLogLoad& from) {
    CopyFrom(from);
    return *this;
  }
  inline OrderLogLoad& operator=(OrderLogLoad&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OrderLogLoad& default_instance() {
    return *internal_default_instance();
  }
  static inline const OrderLogLoad* internal_default_instance() {
    return reinterpret_cast<const OrderLogLoad*>(
               &_OrderLogLoad_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(OrderLogLoad& a, OrderLogLoad& b) {
    a.Swap(&b);
  }
  inline void Swap(OrderLogLoad* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OrderLogLoad* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OrderLogLoad* New() const final {
    return new OrderLogLoad();
  }

  OrderLogLoad* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OrderLogLoad>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OrderLogLoad& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const OrderLogLoad& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OrderLogLoad* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gcu_lcr.protobuf.OrderLogLoad";
  }
  protected:
  explicit OrderLogLoad(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOrderIDFieldNumber = 3,
    kMeterIDFieldNumber = 1,
    kPipelineStateFieldNumber = 2,
  };
  // string OrderID = 3;
  void clear_orderid();
  const std::string& orderid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_orderid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_orderid();
  PROTOBUF_MUST_USE_RESULT std::string* release_orderid();
  void set_allocated_orderid(std::string* orderid);
  private:
  const std::string& _internal_orderid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_orderid(const std::string& value);
  std::string* _internal_mutable_orderid();
  public:

  // uint32 MeterID = 1;
  void clear_meterid();
  ::PROTOBUF_NAMESPACE_ID::uint32 meterid() const;
  void set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_meterid() const;
  void _internal_set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // .OHPinfo.PipelineStateEnum PipelineState = 2;
  void clear_pipelinestate();
  ::OHPinfo::PipelineStateEnum pipelinestate() const;
  void set_pipelinestate(::OHPinfo::PipelineStateEnum value);
  private:
  ::OHPinfo::PipelineStateEnum _internal_pipelinestate() const;
  void _internal_set_pipelinestate(::OHPinfo::PipelineStateEnum value);
  public:

  // @@protoc_insertion_point(class_scope:gcu_lcr.protobuf.OrderLogLoad)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr orderid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 meterid_;
  int pipelinestate_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fLCR_5fALL_2eproto;
};
// -------------------------------------------------------------------

class RecordDateGetBack final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gcu_lcr.protobuf.RecordDateGetBack) */ {
 public:
  inline RecordDateGetBack() : RecordDateGetBack(nullptr) {}
  ~RecordDateGetBack() override;
  explicit constexpr RecordDateGetBack(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RecordDateGetBack(const RecordDateGetBack& from);
  RecordDateGetBack(RecordDateGetBack&& from) noexcept
    : RecordDateGetBack() {
    *this = ::std::move(from);
  }

  inline RecordDateGetBack& operator=(const RecordDateGetBack& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecordDateGetBack& operator=(RecordDateGetBack&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RecordDateGetBack& default_instance() {
    return *internal_default_instance();
  }
  static inline const RecordDateGetBack* internal_default_instance() {
    return reinterpret_cast<const RecordDateGetBack*>(
               &_RecordDateGetBack_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(RecordDateGetBack& a, RecordDateGetBack& b) {
    a.Swap(&b);
  }
  inline void Swap(RecordDateGetBack* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RecordDateGetBack* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RecordDateGetBack* New() const final {
    return new RecordDateGetBack();
  }

  RecordDateGetBack* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RecordDateGetBack>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RecordDateGetBack& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RecordDateGetBack& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecordDateGetBack* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gcu_lcr.protobuf.RecordDateGetBack";
  }
  protected:
  explicit RecordDateGetBack(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRecordLoadFieldNumber = 3,
    kRecordIDFieldNumber = 2,
    kModuleIDFieldNumber = 1,
  };
  // bytes RecordLoad = 3;
  void clear_recordload();
  const std::string& recordload() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_recordload(ArgT0&& arg0, ArgT... args);
  std::string* mutable_recordload();
  PROTOBUF_MUST_USE_RESULT std::string* release_recordload();
  void set_allocated_recordload(std::string* recordload);
  private:
  const std::string& _internal_recordload() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_recordload(const std::string& value);
  std::string* _internal_mutable_recordload();
  public:

  // .OHPinfo.UUIDValue RecordID = 2;
  bool has_recordid() const;
  private:
  bool _internal_has_recordid() const;
  public:
  void clear_recordid();
  const ::OHPinfo::UUIDValue& recordid() const;
  PROTOBUF_MUST_USE_RESULT ::OHPinfo::UUIDValue* release_recordid();
  ::OHPinfo::UUIDValue* mutable_recordid();
  void set_allocated_recordid(::OHPinfo::UUIDValue* recordid);
  private:
  const ::OHPinfo::UUIDValue& _internal_recordid() const;
  ::OHPinfo::UUIDValue* _internal_mutable_recordid();
  public:
  void unsafe_arena_set_allocated_recordid(
      ::OHPinfo::UUIDValue* recordid);
  ::OHPinfo::UUIDValue* unsafe_arena_release_recordid();

  // .OSCinfo.SettlementModuleEnum ModuleID = 1;
  void clear_moduleid();
  ::OSCinfo::SettlementModuleEnum moduleid() const;
  void set_moduleid(::OSCinfo::SettlementModuleEnum value);
  private:
  ::OSCinfo::SettlementModuleEnum _internal_moduleid() const;
  void _internal_set_moduleid(::OSCinfo::SettlementModuleEnum value);
  public:

  // @@protoc_insertion_point(class_scope:gcu_lcr.protobuf.RecordDateGetBack)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr recordload_;
  ::OHPinfo::UUIDValue* recordid_;
  int moduleid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fLCR_5fALL_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// LogLoad

// uint32 Module = 1;
inline void LogLoad::clear_module() {
  module_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 LogLoad::_internal_module() const {
  return module_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 LogLoad::module() const {
  // @@protoc_insertion_point(field_get:gcu_lcr.protobuf.LogLoad.Module)
  return _internal_module();
}
inline void LogLoad::_internal_set_module(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  module_ = value;
}
inline void LogLoad::set_module(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_module(value);
  // @@protoc_insertion_point(field_set:gcu_lcr.protobuf.LogLoad.Module)
}

// uint32 Level = 2;
inline void LogLoad::clear_level() {
  level_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 LogLoad::_internal_level() const {
  return level_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 LogLoad::level() const {
  // @@protoc_insertion_point(field_get:gcu_lcr.protobuf.LogLoad.Level)
  return _internal_level();
}
inline void LogLoad::_internal_set_level(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  level_ = value;
}
inline void LogLoad::set_level(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_level(value);
  // @@protoc_insertion_point(field_set:gcu_lcr.protobuf.LogLoad.Level)
}

// int64 Timestamp = 3;
inline void LogLoad::clear_timestamp() {
  timestamp_ = int64_t{0};
}
inline ::PROTOBUF_NAMESPACE_ID::int64 LogLoad::_internal_timestamp() const {
  return timestamp_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 LogLoad::timestamp() const {
  // @@protoc_insertion_point(field_get:gcu_lcr.protobuf.LogLoad.Timestamp)
  return _internal_timestamp();
}
inline void LogLoad::_internal_set_timestamp(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  timestamp_ = value;
}
inline void LogLoad::set_timestamp(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_timestamp(value);
  // @@protoc_insertion_point(field_set:gcu_lcr.protobuf.LogLoad.Timestamp)
}

// bytes Sentence = 4;
inline void LogLoad::clear_sentence() {
  sentence_.ClearToEmpty();
}
inline const std::string& LogLoad::sentence() const {
  // @@protoc_insertion_point(field_get:gcu_lcr.protobuf.LogLoad.Sentence)
  return _internal_sentence();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void LogLoad::set_sentence(ArgT0&& arg0, ArgT... args) {
 
 sentence_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:gcu_lcr.protobuf.LogLoad.Sentence)
}
inline std::string* LogLoad::mutable_sentence() {
  std::string* _s = _internal_mutable_sentence();
  // @@protoc_insertion_point(field_mutable:gcu_lcr.protobuf.LogLoad.Sentence)
  return _s;
}
inline const std::string& LogLoad::_internal_sentence() const {
  return sentence_.Get();
}
inline void LogLoad::_internal_set_sentence(const std::string& value) {
  
  sentence_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* LogLoad::_internal_mutable_sentence() {
  
  return sentence_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* LogLoad::release_sentence() {
  // @@protoc_insertion_point(field_release:gcu_lcr.protobuf.LogLoad.Sentence)
  return sentence_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void LogLoad::set_allocated_sentence(std::string* sentence) {
  if (sentence != nullptr) {
    
  } else {
    
  }
  sentence_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), sentence,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:gcu_lcr.protobuf.LogLoad.Sentence)
}

// -------------------------------------------------------------------

// OrderLogLoad

// uint32 MeterID = 1;
inline void OrderLogLoad::clear_meterid() {
  meterid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderLogLoad::_internal_meterid() const {
  return meterid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderLogLoad::meterid() const {
  // @@protoc_insertion_point(field_get:gcu_lcr.protobuf.OrderLogLoad.MeterID)
  return _internal_meterid();
}
inline void OrderLogLoad::_internal_set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  meterid_ = value;
}
inline void OrderLogLoad::set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_meterid(value);
  // @@protoc_insertion_point(field_set:gcu_lcr.protobuf.OrderLogLoad.MeterID)
}

// .OHPinfo.PipelineStateEnum PipelineState = 2;
inline void OrderLogLoad::clear_pipelinestate() {
  pipelinestate_ = 0;
}
inline ::OHPinfo::PipelineStateEnum OrderLogLoad::_internal_pipelinestate() const {
  return static_cast< ::OHPinfo::PipelineStateEnum >(pipelinestate_);
}
inline ::OHPinfo::PipelineStateEnum OrderLogLoad::pipelinestate() const {
  // @@protoc_insertion_point(field_get:gcu_lcr.protobuf.OrderLogLoad.PipelineState)
  return _internal_pipelinestate();
}
inline void OrderLogLoad::_internal_set_pipelinestate(::OHPinfo::PipelineStateEnum value) {
  
  pipelinestate_ = value;
}
inline void OrderLogLoad::set_pipelinestate(::OHPinfo::PipelineStateEnum value) {
  _internal_set_pipelinestate(value);
  // @@protoc_insertion_point(field_set:gcu_lcr.protobuf.OrderLogLoad.PipelineState)
}

// string OrderID = 3;
inline void OrderLogLoad::clear_orderid() {
  orderid_.ClearToEmpty();
}
inline const std::string& OrderLogLoad::orderid() const {
  // @@protoc_insertion_point(field_get:gcu_lcr.protobuf.OrderLogLoad.OrderID)
  return _internal_orderid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OrderLogLoad::set_orderid(ArgT0&& arg0, ArgT... args) {
 
 orderid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:gcu_lcr.protobuf.OrderLogLoad.OrderID)
}
inline std::string* OrderLogLoad::mutable_orderid() {
  std::string* _s = _internal_mutable_orderid();
  // @@protoc_insertion_point(field_mutable:gcu_lcr.protobuf.OrderLogLoad.OrderID)
  return _s;
}
inline const std::string& OrderLogLoad::_internal_orderid() const {
  return orderid_.Get();
}
inline void OrderLogLoad::_internal_set_orderid(const std::string& value) {
  
  orderid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* OrderLogLoad::_internal_mutable_orderid() {
  
  return orderid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* OrderLogLoad::release_orderid() {
  // @@protoc_insertion_point(field_release:gcu_lcr.protobuf.OrderLogLoad.OrderID)
  return orderid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void OrderLogLoad::set_allocated_orderid(std::string* orderid) {
  if (orderid != nullptr) {
    
  } else {
    
  }
  orderid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), orderid,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:gcu_lcr.protobuf.OrderLogLoad.OrderID)
}

// -------------------------------------------------------------------

// RecordDateGetBack

// .OSCinfo.SettlementModuleEnum ModuleID = 1;
inline void RecordDateGetBack::clear_moduleid() {
  moduleid_ = 0;
}
inline ::OSCinfo::SettlementModuleEnum RecordDateGetBack::_internal_moduleid() const {
  return static_cast< ::OSCinfo::SettlementModuleEnum >(moduleid_);
}
inline ::OSCinfo::SettlementModuleEnum RecordDateGetBack::moduleid() const {
  // @@protoc_insertion_point(field_get:gcu_lcr.protobuf.RecordDateGetBack.ModuleID)
  return _internal_moduleid();
}
inline void RecordDateGetBack::_internal_set_moduleid(::OSCinfo::SettlementModuleEnum value) {
  
  moduleid_ = value;
}
inline void RecordDateGetBack::set_moduleid(::OSCinfo::SettlementModuleEnum value) {
  _internal_set_moduleid(value);
  // @@protoc_insertion_point(field_set:gcu_lcr.protobuf.RecordDateGetBack.ModuleID)
}

// .OHPinfo.UUIDValue RecordID = 2;
inline bool RecordDateGetBack::_internal_has_recordid() const {
  return this != internal_default_instance() && recordid_ != nullptr;
}
inline bool RecordDateGetBack::has_recordid() const {
  return _internal_has_recordid();
}
inline const ::OHPinfo::UUIDValue& RecordDateGetBack::_internal_recordid() const {
  const ::OHPinfo::UUIDValue* p = recordid_;
  return p != nullptr ? *p : reinterpret_cast<const ::OHPinfo::UUIDValue&>(
      ::OHPinfo::_UUIDValue_default_instance_);
}
inline const ::OHPinfo::UUIDValue& RecordDateGetBack::recordid() const {
  // @@protoc_insertion_point(field_get:gcu_lcr.protobuf.RecordDateGetBack.RecordID)
  return _internal_recordid();
}
inline void RecordDateGetBack::unsafe_arena_set_allocated_recordid(
    ::OHPinfo::UUIDValue* recordid) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(recordid_);
  }
  recordid_ = recordid;
  if (recordid) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gcu_lcr.protobuf.RecordDateGetBack.RecordID)
}
inline ::OHPinfo::UUIDValue* RecordDateGetBack::release_recordid() {
  
  ::OHPinfo::UUIDValue* temp = recordid_;
  recordid_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::OHPinfo::UUIDValue* RecordDateGetBack::unsafe_arena_release_recordid() {
  // @@protoc_insertion_point(field_release:gcu_lcr.protobuf.RecordDateGetBack.RecordID)
  
  ::OHPinfo::UUIDValue* temp = recordid_;
  recordid_ = nullptr;
  return temp;
}
inline ::OHPinfo::UUIDValue* RecordDateGetBack::_internal_mutable_recordid() {
  
  if (recordid_ == nullptr) {
    auto* p = CreateMaybeMessage<::OHPinfo::UUIDValue>(GetArenaForAllocation());
    recordid_ = p;
  }
  return recordid_;
}
inline ::OHPinfo::UUIDValue* RecordDateGetBack::mutable_recordid() {
  ::OHPinfo::UUIDValue* _msg = _internal_mutable_recordid();
  // @@protoc_insertion_point(field_mutable:gcu_lcr.protobuf.RecordDateGetBack.RecordID)
  return _msg;
}
inline void RecordDateGetBack::set_allocated_recordid(::OHPinfo::UUIDValue* recordid) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(recordid_);
  }
  if (recordid) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(recordid));
    if (message_arena != submessage_arena) {
      recordid = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, recordid, submessage_arena);
    }
    
  } else {
    
  }
  recordid_ = recordid;
  // @@protoc_insertion_point(field_set_allocated:gcu_lcr.protobuf.RecordDateGetBack.RecordID)
}

// bytes RecordLoad = 3;
inline void RecordDateGetBack::clear_recordload() {
  recordload_.ClearToEmpty();
}
inline const std::string& RecordDateGetBack::recordload() const {
  // @@protoc_insertion_point(field_get:gcu_lcr.protobuf.RecordDateGetBack.RecordLoad)
  return _internal_recordload();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RecordDateGetBack::set_recordload(ArgT0&& arg0, ArgT... args) {
 
 recordload_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:gcu_lcr.protobuf.RecordDateGetBack.RecordLoad)
}
inline std::string* RecordDateGetBack::mutable_recordload() {
  std::string* _s = _internal_mutable_recordload();
  // @@protoc_insertion_point(field_mutable:gcu_lcr.protobuf.RecordDateGetBack.RecordLoad)
  return _s;
}
inline const std::string& RecordDateGetBack::_internal_recordload() const {
  return recordload_.Get();
}
inline void RecordDateGetBack::_internal_set_recordload(const std::string& value) {
  
  recordload_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RecordDateGetBack::_internal_mutable_recordload() {
  
  return recordload_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RecordDateGetBack::release_recordload() {
  // @@protoc_insertion_point(field_release:gcu_lcr.protobuf.RecordDateGetBack.RecordLoad)
  return recordload_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RecordDateGetBack::set_allocated_recordload(std::string* recordload) {
  if (recordload != nullptr) {
    
  } else {
    
  }
  recordload_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), recordload,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:gcu_lcr.protobuf.RecordDateGetBack.RecordLoad)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf
}  // namespace gcu_lcr

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_GCU_5fLCR_5fALL_2eproto
