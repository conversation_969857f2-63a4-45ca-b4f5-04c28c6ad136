# libprotoc.la - a libtool library file
# Generated by libtool (GNU libtool) 2.4.6 Debian-2.4.6-14
#
# Please DO NOT delete this file!
# It is necessary for linking the library.

# The name that we can dlopen(3).
dlname='libprotoc.so.28'

# Names of this library.
library_names='libprotoc.so.28.0.3 libprotoc.so.28 libprotoc.so'

# The name of the static archive.
old_library='libprotoc.a'

# Linker flags that cannot go in dependency_libs.
inherited_linker_flags=' -pthread'

# Libraries that this one depends upon.
dependency_libs=' /home/<USER>/protobuf_3.17.3/protobuf-3.17.3/build/lib/libprotobuf.la -lpthread -lz /home/<USER>/work/microchip/buildroot-at91/output/host/arm-buildroot-linux-gnueabihf/lib/libstdc++.la'

# Names of additional weak libraries provided by this library
weak_library_names=''

# Version information for libprotoc.
current=28
age=0
revision=3

# Is this an already installed library?
installed=yes

# Should we warn about portability when linking against -modules?
shouldnotlink=no

# Files to dlopen/dlpreopen
dlopen=''
dlpreopen=''

# Directory that this library needs to be installed in:
libdir='/home/<USER>/protobuf_3.17.3/protobuf-3.17.3/build/lib'
