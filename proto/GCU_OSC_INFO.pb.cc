// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GCU_OSC_INFO.proto

#include "GCU_OSC_INFO.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace OSCinfo {
constexpr CardMaininfo::CardMaininfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : key_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , cputxload_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , moduleid_(0)

  , cardtypeid_(0)

  , sector_num_(0u)
  , key_length_(0u)
  , cputxsize_(0u)
  , cardstatus_(0)

  , balance_(0){}
struct CardMaininfoDefaultTypeInternal {
  constexpr CardMaininfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CardMaininfoDefaultTypeInternal() {}
  union {
    CardMaininfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CardMaininfoDefaultTypeInternal _CardMaininfo_default_instance_;
constexpr CardMesginfo::CardMesginfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : dateload_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , cpurxload_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , moduleid_(0)

  , cardtypeid_(0)

  , sectorsize_(0u)
  , sectorlist_(0u)
  , cpurxsize_(0u)
  , cardreignflag_(0u)
  , opttype_(0)
{}
struct CardMesginfoDefaultTypeInternal {
  constexpr CardMesginfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CardMesginfoDefaultTypeInternal() {}
  union {
    CardMesginfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CardMesginfoDefaultTypeInternal _CardMesginfo_default_instance_;
constexpr SettlementServer::SettlementServer(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : weburl_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , stationnum_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , centreip_(0u)
  , centreport_(0u)
  , ip_(0u)
  , port_(0u)
  , multisettlementpull_(0u){}
struct SettlementServerDefaultTypeInternal {
  constexpr SettlementServerDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SettlementServerDefaultTypeInternal() {}
  union {
    SettlementServer _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SettlementServerDefaultTypeInternal _SettlementServer_default_instance_;
constexpr IdleFault::IdleFault(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ohpfault_()
  , mainalarmlist_()
  , vcifault_()
  , dmcfault_()
  , meterid_(0u)
  , ohpfaultsize_(0u)
  , mainalarmlistsize_(0u)
  , vcifaultsize_(0u)
  , dmcfaultsize_(0u){}
struct IdleFaultDefaultTypeInternal {
  constexpr IdleFaultDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~IdleFaultDefaultTypeInternal() {}
  union {
    IdleFault _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT IdleFaultDefaultTypeInternal _IdleFault_default_instance_;
}  // namespace OSCinfo
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_GCU_5fOSC_5fINFO_2eproto[4];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_GCU_5fOSC_5fINFO_2eproto[6];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_GCU_5fOSC_5fINFO_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_GCU_5fOSC_5fINFO_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::OSCinfo::CardMaininfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::OSCinfo::CardMaininfo, moduleid_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::CardMaininfo, cardtypeid_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::CardMaininfo, sector_num_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::CardMaininfo, key_length_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::CardMaininfo, key_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::CardMaininfo, cputxsize_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::CardMaininfo, cputxload_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::CardMaininfo, cardstatus_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::CardMaininfo, balance_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::OSCinfo::CardMesginfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::OSCinfo::CardMesginfo, moduleid_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::CardMesginfo, cardtypeid_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::CardMesginfo, sectorsize_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::CardMesginfo, sectorlist_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::CardMesginfo, dateload_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::CardMesginfo, cpurxsize_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::CardMesginfo, cpurxload_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::CardMesginfo, cardreignflag_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::CardMesginfo, opttype_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::OSCinfo::SettlementServer, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::OSCinfo::SettlementServer, centreip_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::SettlementServer, centreport_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::SettlementServer, ip_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::SettlementServer, port_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::SettlementServer, weburl_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::SettlementServer, stationnum_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::SettlementServer, multisettlementpull_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::OSCinfo::IdleFault, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::OSCinfo::IdleFault, meterid_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::IdleFault, ohpfaultsize_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::IdleFault, ohpfault_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::IdleFault, mainalarmlistsize_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::IdleFault, mainalarmlist_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::IdleFault, vcifaultsize_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::IdleFault, vcifault_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::IdleFault, dmcfaultsize_),
  PROTOBUF_FIELD_OFFSET(::OSCinfo::IdleFault, dmcfault_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::OSCinfo::CardMaininfo)},
  { 14, -1, sizeof(::OSCinfo::CardMesginfo)},
  { 28, -1, sizeof(::OSCinfo::SettlementServer)},
  { 40, -1, sizeof(::OSCinfo::IdleFault)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::OSCinfo::_CardMaininfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::OSCinfo::_CardMesginfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::OSCinfo::_SettlementServer_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::OSCinfo::_IdleFault_default_instance_),
};

const char descriptor_table_protodef_GCU_5fOSC_5fINFO_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\022GCU_OSC_INFO.proto\022\007OSCinfo\032\026GCU_AllFa"
  "ultEnum.proto\032\022GCU_BMS_INFO.proto\"\206\002\n\014Ca"
  "rdMaininfo\022/\n\010ModuleID\030\001 \001(\0162\035.OSCinfo.S"
  "ettlementModuleEnum\022)\n\nCardTypeID\030\002 \001(\0162"
  "\025.OSCinfo.CardTypeEnum\022\022\n\nSector_Num\030\003 \001"
  "(\r\022\022\n\nKey_Length\030\004 \001(\r\022\013\n\003Key\030\005 \001(\014\022\021\n\tC"
  "puTxSize\030\006 \001(\r\022\021\n\tCpuTxLoad\030\007 \001(\014\022.\n\nCar"
  "dstatus\030\010 \001(\0162\032.OSCinfo.CardOperationEnu"
  "m\022\017\n\007balance\030\t \001(\001\"\213\002\n\014CardMesginfo\022/\n\010M"
  "oduleID\030\001 \001(\0162\035.OSCinfo.SettlementModule"
  "Enum\022)\n\nCardTypeID\030\002 \001(\0162\025.OSCinfo.CardT"
  "ypeEnum\022\022\n\nSectorSize\030\003 \001(\r\022\022\n\nSectorLis"
  "t\030\004 \001(\r\022\020\n\010DateLoad\030\005 \001(\014\022\021\n\tCpuRxSize\030\006"
  " \001(\r\022\021\n\tCpuRxLoad\030\007 \001(\014\022\025\n\rCardReignFlag"
  "\030\010 \001(\r\022(\n\007OptType\030\t \001(\0162\027.OSCinfo.Option"
  "TypeEnum\"\223\001\n\020SettlementServer\022\020\n\010CentreI"
  "P\030\001 \001(\r\022\022\n\nCentrePort\030\002 \001(\r\022\n\n\002IP\030\003 \001(\r\022"
  "\014\n\004Port\030\004 \001(\r\022\016\n\006WebURL\030\005 \001(\t\022\022\n\nStation"
  "Num\030\006 \001(\t\022\033\n\023MultiSettlementPull\030\007 \001(\r\"\272"
  "\002\n\tIdleFault\022\017\n\007MeterID\030\001 \001(\r\022\024\n\014OHPFaul"
  "tSize\030\002 \001(\r\022-\n\010OHPFault\030\003 \003(\0132\033.AllFault"
  "Enum.OHPFaultState\022\031\n\021MainAlarmListSize\030"
  "\004 \001(\r\0222\n\rMainAlarmList\030\005 \003(\0132\033.AllFaultE"
  "num.PMMFaultState\022\024\n\014VCIFaultSize\030\006 \001(\r\022"
  "-\n\010VCIFault\030\007 \003(\0132\033.AllFaultEnum.VCIFaul"
  "tState\022\024\n\014DMCFaultSize\030\010 \001(\r\022-\n\010DMCFault"
  "\030\t \003(\0132\033.AllFaultEnum.DMCFaultState*\357\001\n\024"
  "SettlementModuleEnum\022\020\n\014UnKownModule\020\000\022\007"
  "\n\003HMC\020\001\022\007\n\003LOS\020\002\022\007\n\003DMC\020\003\022\007\n\003ICC\020\004\022\007\n\003XJ"
  "C\020\005\022\007\n\003GWC\020\006\022\007\n\003AMP\020\007\022\007\n\003CJC\020\010\022\007\n\003SHE\020\t\022"
  "\007\n\003SXC\020\n\022\007\n\003OCP\020\013\022\007\n\003SPI\020\014\022\007\n\003NDC\020\r\022\007\n\003T"
  "ED\020\016\022\007\n\003STC\020\017\022\007\n\003XJT\020\020\022\007\n\003RAI\020\021\022\007\n\003CNC\020\022"
  "\022\007\n\003HKW\020\023\022\007\n\003EOE\020\024\022\007\n\003GWT\020\025\022\010\n\003LPR\020\360\001*8\n"
  "\014CardTypeEnum\022\016\n\nUnKownCard\020\000\022\006\n\002M1\020\001\022\007\n"
  "\003CPU\020\002\022\007\n\003VIN\020\003*S\n\016OptionTypeEnum\022\017\n\013Cha"
  "rgeStart\020\000\022\016\n\nChargeStop\020\001\022\016\n\nCardUnlock"
  "\020\002\022\020\n\014BalanceQuery\020\003*\353\001\n\021CardOperationEn"
  "um\022\023\n\017UnKownOperation\020\000\022\t\n\005Doing\020\001\022\010\n\004Do"
  "ne\020\002\022\014\n\010CrcFault\020\003\022\013\n\007OpFault\020\004\022\016\n\nLocke"
  "dCard\020\005\022\r\n\tSumsFault\020\006\022\020\n\014UnlockedCard\020\007"
  "\022\021\n\rWaitUserInput\020\010\022\023\n\017QueryNotSupport\020\t"
  "\022\024\n\020DontAllowStartup\020\n\022\017\n\013ConfigError\020\013\022"
  "\021\n\rAuthentFailed\020\014*F\n\017HMCTipsTypeEnum\022\013\n"
  "\007VinAuth\020\000\022\006\n\002Ad\020\001\022\007\n\003OTA\020\002\022\t\n\005Voice\020\003\022\n"
  "\n\006AddOil\020\004*\300\002\n\017HMCTipsRespEnum\022\016\n\nUnKown"
  "Tips\020\000\022\r\n\tTipsDoing\020\001\022\014\n\010TipsDone\020\002\022\016\n\nV"
  "inTimeout\020\003\022\014\n\010VinFault\020\004\022\023\n\017PlatAuthTim"
  "eout\020\005\022\025\n\021PlatAuthSumsFault\020\006\022\026\n\022PlatAut"
  "hExistFault\020\007\022\021\n\rPlatAuthFault\020\010\022\030\n\024Plat"
  "VinTimeIntercept\020\t\022\014\n\010OtaFault\020\020\022\r\n\tAdTi"
  "meout\020 \022\022\n\016ADInsertCanNot\020!\022\021\n\rADVciNoIn"
  "sert\020\"\022\022\n\016InsertGunAgain\0200\022\031\n\025NoEntryFor"
  "OilVehicles\0201P\000P\001b\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_GCU_5fOSC_5fINFO_2eproto_deps[2] = {
  &::descriptor_table_GCU_5fAllFaultEnum_2eproto,
  &::descriptor_table_GCU_5fBMS_5fINFO_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_GCU_5fOSC_5fINFO_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GCU_5fOSC_5fINFO_2eproto = {
  false, false, 2105, descriptor_table_protodef_GCU_5fOSC_5fINFO_2eproto, "GCU_OSC_INFO.proto", 
  &descriptor_table_GCU_5fOSC_5fINFO_2eproto_once, descriptor_table_GCU_5fOSC_5fINFO_2eproto_deps, 2, 4,
  schemas, file_default_instances, TableStruct_GCU_5fOSC_5fINFO_2eproto::offsets,
  file_level_metadata_GCU_5fOSC_5fINFO_2eproto, file_level_enum_descriptors_GCU_5fOSC_5fINFO_2eproto, file_level_service_descriptors_GCU_5fOSC_5fINFO_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_GCU_5fOSC_5fINFO_2eproto_getter() {
  return &descriptor_table_GCU_5fOSC_5fINFO_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_GCU_5fOSC_5fINFO_2eproto(&descriptor_table_GCU_5fOSC_5fINFO_2eproto);
namespace OSCinfo {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SettlementModuleEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fOSC_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fOSC_5fINFO_2eproto[0];
}
bool SettlementModuleEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    case 14:
    case 15:
    case 16:
    case 17:
    case 18:
    case 19:
    case 20:
    case 21:
    case 240:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CardTypeEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fOSC_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fOSC_5fINFO_2eproto[1];
}
bool CardTypeEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OptionTypeEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fOSC_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fOSC_5fINFO_2eproto[2];
}
bool OptionTypeEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CardOperationEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fOSC_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fOSC_5fINFO_2eproto[3];
}
bool CardOperationEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* HMCTipsTypeEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fOSC_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fOSC_5fINFO_2eproto[4];
}
bool HMCTipsTypeEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* HMCTipsRespEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fOSC_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fOSC_5fINFO_2eproto[5];
}
bool HMCTipsRespEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 16:
    case 32:
    case 33:
    case 34:
    case 48:
    case 49:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class CardMaininfo::_Internal {
 public:
};

CardMaininfo::CardMaininfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:OSCinfo.CardMaininfo)
}
CardMaininfo::CardMaininfo(const CardMaininfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_key().empty()) {
    key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_key(), 
      GetArenaForAllocation());
  }
  cputxload_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_cputxload().empty()) {
    cputxload_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_cputxload(), 
      GetArenaForAllocation());
  }
  ::memcpy(&moduleid_, &from.moduleid_,
    static_cast<size_t>(reinterpret_cast<char*>(&balance_) -
    reinterpret_cast<char*>(&moduleid_)) + sizeof(balance_));
  // @@protoc_insertion_point(copy_constructor:OSCinfo.CardMaininfo)
}

inline void CardMaininfo::SharedCtor() {
key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
cputxload_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&moduleid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&balance_) -
    reinterpret_cast<char*>(&moduleid_)) + sizeof(balance_));
}

CardMaininfo::~CardMaininfo() {
  // @@protoc_insertion_point(destructor:OSCinfo.CardMaininfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CardMaininfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  cputxload_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void CardMaininfo::ArenaDtor(void* object) {
  CardMaininfo* _this = reinterpret_cast< CardMaininfo* >(object);
  (void)_this;
}
void CardMaininfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CardMaininfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CardMaininfo::Clear() {
// @@protoc_insertion_point(message_clear_start:OSCinfo.CardMaininfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  key_.ClearToEmpty();
  cputxload_.ClearToEmpty();
  ::memset(&moduleid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&balance_) -
      reinterpret_cast<char*>(&moduleid_)) + sizeof(balance_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CardMaininfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .OSCinfo.SettlementModuleEnum ModuleID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_moduleid(static_cast<::OSCinfo::SettlementModuleEnum>(val));
        } else goto handle_unusual;
        continue;
      // .OSCinfo.CardTypeEnum CardTypeID = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_cardtypeid(static_cast<::OSCinfo::CardTypeEnum>(val));
        } else goto handle_unusual;
        continue;
      // uint32 Sector_Num = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          sector_num_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 Key_Length = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          key_length_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes Key = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          auto str = _internal_mutable_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 CpuTxSize = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          cputxsize_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes CpuTxLoad = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          auto str = _internal_mutable_cputxload();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .OSCinfo.CardOperationEnum Cardstatus = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_cardstatus(static_cast<::OSCinfo::CardOperationEnum>(val));
        } else goto handle_unusual;
        continue;
      // double balance = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 73)) {
          balance_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* CardMaininfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:OSCinfo.CardMaininfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .OSCinfo.SettlementModuleEnum ModuleID = 1;
  if (this->_internal_moduleid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_moduleid(), target);
  }

  // .OSCinfo.CardTypeEnum CardTypeID = 2;
  if (this->_internal_cardtypeid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_cardtypeid(), target);
  }

  // uint32 Sector_Num = 3;
  if (this->_internal_sector_num() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_sector_num(), target);
  }

  // uint32 Key_Length = 4;
  if (this->_internal_key_length() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_key_length(), target);
  }

  // bytes Key = 5;
  if (!this->_internal_key().empty()) {
    target = stream->WriteBytesMaybeAliased(
        5, this->_internal_key(), target);
  }

  // uint32 CpuTxSize = 6;
  if (this->_internal_cputxsize() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_cputxsize(), target);
  }

  // bytes CpuTxLoad = 7;
  if (!this->_internal_cputxload().empty()) {
    target = stream->WriteBytesMaybeAliased(
        7, this->_internal_cputxload(), target);
  }

  // .OSCinfo.CardOperationEnum Cardstatus = 8;
  if (this->_internal_cardstatus() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      8, this->_internal_cardstatus(), target);
  }

  // double balance = 9;
  if (!(this->_internal_balance() <= 0 && this->_internal_balance() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(9, this->_internal_balance(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:OSCinfo.CardMaininfo)
  return target;
}

size_t CardMaininfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:OSCinfo.CardMaininfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes Key = 5;
  if (!this->_internal_key().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_key());
  }

  // bytes CpuTxLoad = 7;
  if (!this->_internal_cputxload().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_cputxload());
  }

  // .OSCinfo.SettlementModuleEnum ModuleID = 1;
  if (this->_internal_moduleid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_moduleid());
  }

  // .OSCinfo.CardTypeEnum CardTypeID = 2;
  if (this->_internal_cardtypeid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_cardtypeid());
  }

  // uint32 Sector_Num = 3;
  if (this->_internal_sector_num() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_sector_num());
  }

  // uint32 Key_Length = 4;
  if (this->_internal_key_length() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_key_length());
  }

  // uint32 CpuTxSize = 6;
  if (this->_internal_cputxsize() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_cputxsize());
  }

  // .OSCinfo.CardOperationEnum Cardstatus = 8;
  if (this->_internal_cardstatus() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_cardstatus());
  }

  // double balance = 9;
  if (!(this->_internal_balance() <= 0 && this->_internal_balance() >= 0)) {
    total_size += 1 + 8;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CardMaininfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CardMaininfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CardMaininfo::GetClassData() const { return &_class_data_; }

void CardMaininfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<CardMaininfo *>(to)->MergeFrom(
      static_cast<const CardMaininfo &>(from));
}


void CardMaininfo::MergeFrom(const CardMaininfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:OSCinfo.CardMaininfo)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_key().empty()) {
    _internal_set_key(from._internal_key());
  }
  if (!from._internal_cputxload().empty()) {
    _internal_set_cputxload(from._internal_cputxload());
  }
  if (from._internal_moduleid() != 0) {
    _internal_set_moduleid(from._internal_moduleid());
  }
  if (from._internal_cardtypeid() != 0) {
    _internal_set_cardtypeid(from._internal_cardtypeid());
  }
  if (from._internal_sector_num() != 0) {
    _internal_set_sector_num(from._internal_sector_num());
  }
  if (from._internal_key_length() != 0) {
    _internal_set_key_length(from._internal_key_length());
  }
  if (from._internal_cputxsize() != 0) {
    _internal_set_cputxsize(from._internal_cputxsize());
  }
  if (from._internal_cardstatus() != 0) {
    _internal_set_cardstatus(from._internal_cardstatus());
  }
  if (!(from._internal_balance() <= 0 && from._internal_balance() >= 0)) {
    _internal_set_balance(from._internal_balance());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CardMaininfo::CopyFrom(const CardMaininfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:OSCinfo.CardMaininfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CardMaininfo::IsInitialized() const {
  return true;
}

void CardMaininfo::InternalSwap(CardMaininfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &key_, GetArenaForAllocation(),
      &other->key_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &cputxload_, GetArenaForAllocation(),
      &other->cputxload_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CardMaininfo, balance_)
      + sizeof(CardMaininfo::balance_)
      - PROTOBUF_FIELD_OFFSET(CardMaininfo, moduleid_)>(
          reinterpret_cast<char*>(&moduleid_),
          reinterpret_cast<char*>(&other->moduleid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CardMaininfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fOSC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fOSC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fOSC_5fINFO_2eproto[0]);
}

// ===================================================================

class CardMesginfo::_Internal {
 public:
};

CardMesginfo::CardMesginfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:OSCinfo.CardMesginfo)
}
CardMesginfo::CardMesginfo(const CardMesginfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  dateload_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_dateload().empty()) {
    dateload_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_dateload(), 
      GetArenaForAllocation());
  }
  cpurxload_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_cpurxload().empty()) {
    cpurxload_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_cpurxload(), 
      GetArenaForAllocation());
  }
  ::memcpy(&moduleid_, &from.moduleid_,
    static_cast<size_t>(reinterpret_cast<char*>(&opttype_) -
    reinterpret_cast<char*>(&moduleid_)) + sizeof(opttype_));
  // @@protoc_insertion_point(copy_constructor:OSCinfo.CardMesginfo)
}

inline void CardMesginfo::SharedCtor() {
dateload_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
cpurxload_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&moduleid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&opttype_) -
    reinterpret_cast<char*>(&moduleid_)) + sizeof(opttype_));
}

CardMesginfo::~CardMesginfo() {
  // @@protoc_insertion_point(destructor:OSCinfo.CardMesginfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CardMesginfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  dateload_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  cpurxload_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void CardMesginfo::ArenaDtor(void* object) {
  CardMesginfo* _this = reinterpret_cast< CardMesginfo* >(object);
  (void)_this;
}
void CardMesginfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CardMesginfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CardMesginfo::Clear() {
// @@protoc_insertion_point(message_clear_start:OSCinfo.CardMesginfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  dateload_.ClearToEmpty();
  cpurxload_.ClearToEmpty();
  ::memset(&moduleid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&opttype_) -
      reinterpret_cast<char*>(&moduleid_)) + sizeof(opttype_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CardMesginfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .OSCinfo.SettlementModuleEnum ModuleID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_moduleid(static_cast<::OSCinfo::SettlementModuleEnum>(val));
        } else goto handle_unusual;
        continue;
      // .OSCinfo.CardTypeEnum CardTypeID = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_cardtypeid(static_cast<::OSCinfo::CardTypeEnum>(val));
        } else goto handle_unusual;
        continue;
      // uint32 SectorSize = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          sectorsize_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 SectorList = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          sectorlist_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes DateLoad = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          auto str = _internal_mutable_dateload();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 CpuRxSize = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          cpurxsize_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes CpuRxLoad = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          auto str = _internal_mutable_cpurxload();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 CardReignFlag = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          cardreignflag_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .OSCinfo.OptionTypeEnum OptType = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_opttype(static_cast<::OSCinfo::OptionTypeEnum>(val));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* CardMesginfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:OSCinfo.CardMesginfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .OSCinfo.SettlementModuleEnum ModuleID = 1;
  if (this->_internal_moduleid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_moduleid(), target);
  }

  // .OSCinfo.CardTypeEnum CardTypeID = 2;
  if (this->_internal_cardtypeid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_cardtypeid(), target);
  }

  // uint32 SectorSize = 3;
  if (this->_internal_sectorsize() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_sectorsize(), target);
  }

  // uint32 SectorList = 4;
  if (this->_internal_sectorlist() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_sectorlist(), target);
  }

  // bytes DateLoad = 5;
  if (!this->_internal_dateload().empty()) {
    target = stream->WriteBytesMaybeAliased(
        5, this->_internal_dateload(), target);
  }

  // uint32 CpuRxSize = 6;
  if (this->_internal_cpurxsize() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_cpurxsize(), target);
  }

  // bytes CpuRxLoad = 7;
  if (!this->_internal_cpurxload().empty()) {
    target = stream->WriteBytesMaybeAliased(
        7, this->_internal_cpurxload(), target);
  }

  // uint32 CardReignFlag = 8;
  if (this->_internal_cardreignflag() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_cardreignflag(), target);
  }

  // .OSCinfo.OptionTypeEnum OptType = 9;
  if (this->_internal_opttype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      9, this->_internal_opttype(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:OSCinfo.CardMesginfo)
  return target;
}

size_t CardMesginfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:OSCinfo.CardMesginfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes DateLoad = 5;
  if (!this->_internal_dateload().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_dateload());
  }

  // bytes CpuRxLoad = 7;
  if (!this->_internal_cpurxload().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_cpurxload());
  }

  // .OSCinfo.SettlementModuleEnum ModuleID = 1;
  if (this->_internal_moduleid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_moduleid());
  }

  // .OSCinfo.CardTypeEnum CardTypeID = 2;
  if (this->_internal_cardtypeid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_cardtypeid());
  }

  // uint32 SectorSize = 3;
  if (this->_internal_sectorsize() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_sectorsize());
  }

  // uint32 SectorList = 4;
  if (this->_internal_sectorlist() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_sectorlist());
  }

  // uint32 CpuRxSize = 6;
  if (this->_internal_cpurxsize() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_cpurxsize());
  }

  // uint32 CardReignFlag = 8;
  if (this->_internal_cardreignflag() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_cardreignflag());
  }

  // .OSCinfo.OptionTypeEnum OptType = 9;
  if (this->_internal_opttype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_opttype());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CardMesginfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CardMesginfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CardMesginfo::GetClassData() const { return &_class_data_; }

void CardMesginfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<CardMesginfo *>(to)->MergeFrom(
      static_cast<const CardMesginfo &>(from));
}


void CardMesginfo::MergeFrom(const CardMesginfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:OSCinfo.CardMesginfo)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_dateload().empty()) {
    _internal_set_dateload(from._internal_dateload());
  }
  if (!from._internal_cpurxload().empty()) {
    _internal_set_cpurxload(from._internal_cpurxload());
  }
  if (from._internal_moduleid() != 0) {
    _internal_set_moduleid(from._internal_moduleid());
  }
  if (from._internal_cardtypeid() != 0) {
    _internal_set_cardtypeid(from._internal_cardtypeid());
  }
  if (from._internal_sectorsize() != 0) {
    _internal_set_sectorsize(from._internal_sectorsize());
  }
  if (from._internal_sectorlist() != 0) {
    _internal_set_sectorlist(from._internal_sectorlist());
  }
  if (from._internal_cpurxsize() != 0) {
    _internal_set_cpurxsize(from._internal_cpurxsize());
  }
  if (from._internal_cardreignflag() != 0) {
    _internal_set_cardreignflag(from._internal_cardreignflag());
  }
  if (from._internal_opttype() != 0) {
    _internal_set_opttype(from._internal_opttype());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CardMesginfo::CopyFrom(const CardMesginfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:OSCinfo.CardMesginfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CardMesginfo::IsInitialized() const {
  return true;
}

void CardMesginfo::InternalSwap(CardMesginfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &dateload_, GetArenaForAllocation(),
      &other->dateload_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &cpurxload_, GetArenaForAllocation(),
      &other->cpurxload_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CardMesginfo, opttype_)
      + sizeof(CardMesginfo::opttype_)
      - PROTOBUF_FIELD_OFFSET(CardMesginfo, moduleid_)>(
          reinterpret_cast<char*>(&moduleid_),
          reinterpret_cast<char*>(&other->moduleid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CardMesginfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fOSC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fOSC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fOSC_5fINFO_2eproto[1]);
}

// ===================================================================

class SettlementServer::_Internal {
 public:
};

SettlementServer::SettlementServer(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:OSCinfo.SettlementServer)
}
SettlementServer::SettlementServer(const SettlementServer& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  weburl_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_weburl().empty()) {
    weburl_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_weburl(), 
      GetArenaForAllocation());
  }
  stationnum_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_stationnum().empty()) {
    stationnum_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_stationnum(), 
      GetArenaForAllocation());
  }
  ::memcpy(&centreip_, &from.centreip_,
    static_cast<size_t>(reinterpret_cast<char*>(&multisettlementpull_) -
    reinterpret_cast<char*>(&centreip_)) + sizeof(multisettlementpull_));
  // @@protoc_insertion_point(copy_constructor:OSCinfo.SettlementServer)
}

inline void SettlementServer::SharedCtor() {
weburl_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
stationnum_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&centreip_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&multisettlementpull_) -
    reinterpret_cast<char*>(&centreip_)) + sizeof(multisettlementpull_));
}

SettlementServer::~SettlementServer() {
  // @@protoc_insertion_point(destructor:OSCinfo.SettlementServer)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SettlementServer::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  weburl_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  stationnum_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SettlementServer::ArenaDtor(void* object) {
  SettlementServer* _this = reinterpret_cast< SettlementServer* >(object);
  (void)_this;
}
void SettlementServer::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SettlementServer::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SettlementServer::Clear() {
// @@protoc_insertion_point(message_clear_start:OSCinfo.SettlementServer)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  weburl_.ClearToEmpty();
  stationnum_.ClearToEmpty();
  ::memset(&centreip_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&multisettlementpull_) -
      reinterpret_cast<char*>(&centreip_)) + sizeof(multisettlementpull_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SettlementServer::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 CentreIP = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          centreip_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 CentrePort = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          centreport_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 IP = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          ip_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 Port = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          port_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string WebURL = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          auto str = _internal_mutable_weburl();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "OSCinfo.SettlementServer.WebURL"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string StationNum = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          auto str = _internal_mutable_stationnum();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "OSCinfo.SettlementServer.StationNum"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 MultiSettlementPull = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          multisettlementpull_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* SettlementServer::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:OSCinfo.SettlementServer)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 CentreIP = 1;
  if (this->_internal_centreip() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_centreip(), target);
  }

  // uint32 CentrePort = 2;
  if (this->_internal_centreport() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_centreport(), target);
  }

  // uint32 IP = 3;
  if (this->_internal_ip() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_ip(), target);
  }

  // uint32 Port = 4;
  if (this->_internal_port() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_port(), target);
  }

  // string WebURL = 5;
  if (!this->_internal_weburl().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_weburl().data(), static_cast<int>(this->_internal_weburl().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "OSCinfo.SettlementServer.WebURL");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_weburl(), target);
  }

  // string StationNum = 6;
  if (!this->_internal_stationnum().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_stationnum().data(), static_cast<int>(this->_internal_stationnum().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "OSCinfo.SettlementServer.StationNum");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_stationnum(), target);
  }

  // uint32 MultiSettlementPull = 7;
  if (this->_internal_multisettlementpull() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(7, this->_internal_multisettlementpull(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:OSCinfo.SettlementServer)
  return target;
}

size_t SettlementServer::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:OSCinfo.SettlementServer)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string WebURL = 5;
  if (!this->_internal_weburl().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_weburl());
  }

  // string StationNum = 6;
  if (!this->_internal_stationnum().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_stationnum());
  }

  // uint32 CentreIP = 1;
  if (this->_internal_centreip() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_centreip());
  }

  // uint32 CentrePort = 2;
  if (this->_internal_centreport() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_centreport());
  }

  // uint32 IP = 3;
  if (this->_internal_ip() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_ip());
  }

  // uint32 Port = 4;
  if (this->_internal_port() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_port());
  }

  // uint32 MultiSettlementPull = 7;
  if (this->_internal_multisettlementpull() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_multisettlementpull());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SettlementServer::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SettlementServer::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SettlementServer::GetClassData() const { return &_class_data_; }

void SettlementServer::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<SettlementServer *>(to)->MergeFrom(
      static_cast<const SettlementServer &>(from));
}


void SettlementServer::MergeFrom(const SettlementServer& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:OSCinfo.SettlementServer)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_weburl().empty()) {
    _internal_set_weburl(from._internal_weburl());
  }
  if (!from._internal_stationnum().empty()) {
    _internal_set_stationnum(from._internal_stationnum());
  }
  if (from._internal_centreip() != 0) {
    _internal_set_centreip(from._internal_centreip());
  }
  if (from._internal_centreport() != 0) {
    _internal_set_centreport(from._internal_centreport());
  }
  if (from._internal_ip() != 0) {
    _internal_set_ip(from._internal_ip());
  }
  if (from._internal_port() != 0) {
    _internal_set_port(from._internal_port());
  }
  if (from._internal_multisettlementpull() != 0) {
    _internal_set_multisettlementpull(from._internal_multisettlementpull());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SettlementServer::CopyFrom(const SettlementServer& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:OSCinfo.SettlementServer)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SettlementServer::IsInitialized() const {
  return true;
}

void SettlementServer::InternalSwap(SettlementServer* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &weburl_, GetArenaForAllocation(),
      &other->weburl_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &stationnum_, GetArenaForAllocation(),
      &other->stationnum_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SettlementServer, multisettlementpull_)
      + sizeof(SettlementServer::multisettlementpull_)
      - PROTOBUF_FIELD_OFFSET(SettlementServer, centreip_)>(
          reinterpret_cast<char*>(&centreip_),
          reinterpret_cast<char*>(&other->centreip_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SettlementServer::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fOSC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fOSC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fOSC_5fINFO_2eproto[2]);
}

// ===================================================================

class IdleFault::_Internal {
 public:
};

void IdleFault::clear_ohpfault() {
  ohpfault_.Clear();
}
void IdleFault::clear_mainalarmlist() {
  mainalarmlist_.Clear();
}
void IdleFault::clear_vcifault() {
  vcifault_.Clear();
}
void IdleFault::clear_dmcfault() {
  dmcfault_.Clear();
}
IdleFault::IdleFault(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  ohpfault_(arena),
  mainalarmlist_(arena),
  vcifault_(arena),
  dmcfault_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:OSCinfo.IdleFault)
}
IdleFault::IdleFault(const IdleFault& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      ohpfault_(from.ohpfault_),
      mainalarmlist_(from.mainalarmlist_),
      vcifault_(from.vcifault_),
      dmcfault_(from.dmcfault_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&meterid_, &from.meterid_,
    static_cast<size_t>(reinterpret_cast<char*>(&dmcfaultsize_) -
    reinterpret_cast<char*>(&meterid_)) + sizeof(dmcfaultsize_));
  // @@protoc_insertion_point(copy_constructor:OSCinfo.IdleFault)
}

inline void IdleFault::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&meterid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&dmcfaultsize_) -
    reinterpret_cast<char*>(&meterid_)) + sizeof(dmcfaultsize_));
}

IdleFault::~IdleFault() {
  // @@protoc_insertion_point(destructor:OSCinfo.IdleFault)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void IdleFault::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void IdleFault::ArenaDtor(void* object) {
  IdleFault* _this = reinterpret_cast< IdleFault* >(object);
  (void)_this;
}
void IdleFault::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void IdleFault::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void IdleFault::Clear() {
// @@protoc_insertion_point(message_clear_start:OSCinfo.IdleFault)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ohpfault_.Clear();
  mainalarmlist_.Clear();
  vcifault_.Clear();
  dmcfault_.Clear();
  ::memset(&meterid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&dmcfaultsize_) -
      reinterpret_cast<char*>(&meterid_)) + sizeof(dmcfaultsize_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* IdleFault::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 MeterID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          meterid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 OHPFaultSize = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ohpfaultsize_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .AllFaultEnum.OHPFaultState OHPFault = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_ohpfault(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else goto handle_unusual;
        continue;
      // uint32 MainAlarmListSize = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          mainalarmlistsize_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .AllFaultEnum.PMMFaultState MainAlarmList = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_mainalarmlist(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else goto handle_unusual;
        continue;
      // uint32 VCIFaultSize = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          vcifaultsize_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .AllFaultEnum.VCIFaultState VCIFault = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_vcifault(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<58>(ptr));
        } else goto handle_unusual;
        continue;
      // uint32 DMCFaultSize = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          dmcfaultsize_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .AllFaultEnum.DMCFaultState DMCFault = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_dmcfault(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<74>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* IdleFault::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:OSCinfo.IdleFault)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 MeterID = 1;
  if (this->_internal_meterid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_meterid(), target);
  }

  // uint32 OHPFaultSize = 2;
  if (this->_internal_ohpfaultsize() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_ohpfaultsize(), target);
  }

  // repeated .AllFaultEnum.OHPFaultState OHPFault = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_ohpfault_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, this->_internal_ohpfault(i), target, stream);
  }

  // uint32 MainAlarmListSize = 4;
  if (this->_internal_mainalarmlistsize() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_mainalarmlistsize(), target);
  }

  // repeated .AllFaultEnum.PMMFaultState MainAlarmList = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_mainalarmlist_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, this->_internal_mainalarmlist(i), target, stream);
  }

  // uint32 VCIFaultSize = 6;
  if (this->_internal_vcifaultsize() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_vcifaultsize(), target);
  }

  // repeated .AllFaultEnum.VCIFaultState VCIFault = 7;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_vcifault_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(7, this->_internal_vcifault(i), target, stream);
  }

  // uint32 DMCFaultSize = 8;
  if (this->_internal_dmcfaultsize() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_dmcfaultsize(), target);
  }

  // repeated .AllFaultEnum.DMCFaultState DMCFault = 9;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_dmcfault_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(9, this->_internal_dmcfault(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:OSCinfo.IdleFault)
  return target;
}

size_t IdleFault::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:OSCinfo.IdleFault)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .AllFaultEnum.OHPFaultState OHPFault = 3;
  total_size += 1UL * this->_internal_ohpfault_size();
  for (const auto& msg : this->ohpfault_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .AllFaultEnum.PMMFaultState MainAlarmList = 5;
  total_size += 1UL * this->_internal_mainalarmlist_size();
  for (const auto& msg : this->mainalarmlist_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .AllFaultEnum.VCIFaultState VCIFault = 7;
  total_size += 1UL * this->_internal_vcifault_size();
  for (const auto& msg : this->vcifault_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .AllFaultEnum.DMCFaultState DMCFault = 9;
  total_size += 1UL * this->_internal_dmcfault_size();
  for (const auto& msg : this->dmcfault_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // uint32 MeterID = 1;
  if (this->_internal_meterid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_meterid());
  }

  // uint32 OHPFaultSize = 2;
  if (this->_internal_ohpfaultsize() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_ohpfaultsize());
  }

  // uint32 MainAlarmListSize = 4;
  if (this->_internal_mainalarmlistsize() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_mainalarmlistsize());
  }

  // uint32 VCIFaultSize = 6;
  if (this->_internal_vcifaultsize() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_vcifaultsize());
  }

  // uint32 DMCFaultSize = 8;
  if (this->_internal_dmcfaultsize() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_dmcfaultsize());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData IdleFault::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    IdleFault::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*IdleFault::GetClassData() const { return &_class_data_; }

void IdleFault::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<IdleFault *>(to)->MergeFrom(
      static_cast<const IdleFault &>(from));
}


void IdleFault::MergeFrom(const IdleFault& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:OSCinfo.IdleFault)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  ohpfault_.MergeFrom(from.ohpfault_);
  mainalarmlist_.MergeFrom(from.mainalarmlist_);
  vcifault_.MergeFrom(from.vcifault_);
  dmcfault_.MergeFrom(from.dmcfault_);
  if (from._internal_meterid() != 0) {
    _internal_set_meterid(from._internal_meterid());
  }
  if (from._internal_ohpfaultsize() != 0) {
    _internal_set_ohpfaultsize(from._internal_ohpfaultsize());
  }
  if (from._internal_mainalarmlistsize() != 0) {
    _internal_set_mainalarmlistsize(from._internal_mainalarmlistsize());
  }
  if (from._internal_vcifaultsize() != 0) {
    _internal_set_vcifaultsize(from._internal_vcifaultsize());
  }
  if (from._internal_dmcfaultsize() != 0) {
    _internal_set_dmcfaultsize(from._internal_dmcfaultsize());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void IdleFault::CopyFrom(const IdleFault& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:OSCinfo.IdleFault)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool IdleFault::IsInitialized() const {
  return true;
}

void IdleFault::InternalSwap(IdleFault* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ohpfault_.InternalSwap(&other->ohpfault_);
  mainalarmlist_.InternalSwap(&other->mainalarmlist_);
  vcifault_.InternalSwap(&other->vcifault_);
  dmcfault_.InternalSwap(&other->dmcfault_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(IdleFault, dmcfaultsize_)
      + sizeof(IdleFault::dmcfaultsize_)
      - PROTOBUF_FIELD_OFFSET(IdleFault, meterid_)>(
          reinterpret_cast<char*>(&meterid_),
          reinterpret_cast<char*>(&other->meterid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata IdleFault::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fOSC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fOSC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fOSC_5fINFO_2eproto[3]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace OSCinfo
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::OSCinfo::CardMaininfo* Arena::CreateMaybeMessage< ::OSCinfo::CardMaininfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::OSCinfo::CardMaininfo >(arena);
}
template<> PROTOBUF_NOINLINE ::OSCinfo::CardMesginfo* Arena::CreateMaybeMessage< ::OSCinfo::CardMesginfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::OSCinfo::CardMesginfo >(arena);
}
template<> PROTOBUF_NOINLINE ::OSCinfo::SettlementServer* Arena::CreateMaybeMessage< ::OSCinfo::SettlementServer >(Arena* arena) {
  return Arena::CreateMessageInternal< ::OSCinfo::SettlementServer >(arena);
}
template<> PROTOBUF_NOINLINE ::OSCinfo::IdleFault* Arena::CreateMaybeMessage< ::OSCinfo::IdleFault >(Arena* arena) {
  return Arena::CreateMessageInternal< ::OSCinfo::IdleFault >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
