syntax = "proto3";
import public "GCU_EVS_INFO.proto";
package gcu_evs_hmi.protobuf;

//EVS注册帧(0x0)
message EVSLogin {
	uint64 evsID = 1;							// 模拟器ID
	uint32 interval = 2;						// 心跳信息间隔（毫秒）
}

//上位机注册回复帧.响应(0x10)
message EVSLoginAns {
	uint64 evsID = 1;							// 模拟器ID
	uint32  serverID =2; 					 //  服务器ID  1:上位机   2:HMI
	uint32 interval = 3;						// 心跳信息间隔（毫秒）
}

//EVS心跳(0x1)
message EVSHb {
	uint64 evsID = 1;					//  模拟器ID
	uint32 heartbeatCnt = 2;	  		//  心跳计数
	uint32 onOffState = 3;				//  EVS 状态: 0 关机 1 开机
	EVSInfo.EVStateMsg evsState = 4;    //  车辆实时状态
}

//EVS心跳.响应(0x11)
message EVSHbReply {
	uint64 evsID = 1;			//  模拟器ID
	uint32 serverID=2;                //  服务器ID  1:上位机   2:HMI
	uint32 heartbeatCnt = 3;	//	心跳计数
	uint32 evsCmd = 4; 			//  控制指令: 0x00 自动 0x01 开机 0x02 关机(默认)
	uint32 offlineMode = 5;		//  控制指令: 0x00 离线模式  0xAA 联网模式(默认)
	uint32 evsReset = 6;        //  复位指令: 0xAA 参数重置，其他无效
}

//EVS报文填充(0x2) HMI<-->EVS
message EVSMsgSet {
	uint64 evsID = 1;							// 模拟器ID
	EVSInfo.EVProtoConferMsg protoConferM = 2; 	// 版本协商信息
    EVSInfo.EVFunConferMsg funConferM = 3; 		// 功能协商
	EVSInfo.EVChgParaConfigMsg paraConfigM = 4;	// 车辆充电参数配置
	EVSInfo.EVAuthenMsg authenM = 5;			// 鉴权信息
	EVSInfo.EVReserveMsg reserveM = 6;			// 预约充电信息 
	EVSInfo.EVPowerSupplyMsg powerSupplyM = 7;	// 供电模式 
	EVSInfo.EVChargingMsg chargingM = 8;		// 充电阶段信息 
	EVSInfo.EVChargingEndMsg chargingEndM = 9; // 充电结束信息
}
//EVS报文控制(0x3) HMI<-->EVS
message EVSMsgCtrl {
	uint64 evsID = 1;						// 模拟器ID
	EVSInfo.EVMsgCtrl msgCtrl = 2; 			// 是否发送(默认 1) 0：不发送，1 发送
	EVSInfo.EVMsgCtrl msgCycleTime = 3; 	// 报文发送周期 ms
	EVSInfo.EVMsgCtrl msgMaxSendTIme = 4; 	// 报文最长发送时间 ms
	EVSInfo.EVFunConferAckMsg funConferAck = 5; // 阶段确认报文设置
}

//EVS电气状态控制(0x4) HMI<-->EVS
message EVSSysCtrl {
	uint64 evsID = 1;							// 模拟器ID
	EVSInfo.EVElectricCtrl electricCtrl = 2; 	// 车辆电气控制
	EVSInfo.EVInsultCtrl  insultCtrl = 3;       // 绝缘参数设置
	EVSInfo.IpMsg ipMsg = 4;					// ip信息
}


//EVS步进信息设置(0x5)HMI-->EVS
message EVSStepMsgSet {
	uint64 evsID = 1;					// 模拟器ID
	EVSInfo.EVSStepMsg stepMsg = 2;     // 步进信息 
}
//EVS报文查询命令(0x1E) HMI-->EVS
message EVSGetMsg {
	uint64 evsID = 1;			//  模拟器ID
	uint32 msgID = 2;	  		//  消息ID
	uint64 getTime = 3;	  		//  查询计数	
}
//EVS报文设置结果(0x1F) EVS-->HMI 
message EVSSetReply {
	uint64 evsID = 1;			//  模拟器ID
	uint32 msgID = 2;	  		//  消息ID
	uint32 setAck = 3;	  		//  设置结果 0xAA 成功，0x00 失败	
}