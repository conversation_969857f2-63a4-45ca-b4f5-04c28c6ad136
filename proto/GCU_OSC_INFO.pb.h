// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GCU_OSC_INFO.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_GCU_5fOSC_5fINFO_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_GCU_5fOSC_5fINFO_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3017000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3017003 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "GCU_AllFaultEnum.pb.h"
#include "GCU_BMS_INFO.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_GCU_5fOSC_5fINFO_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_GCU_5fOSC_5fINFO_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[4]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GCU_5fOSC_5fINFO_2eproto;
namespace OSCinfo {
class CardMaininfo;
struct CardMaininfoDefaultTypeInternal;
extern CardMaininfoDefaultTypeInternal _CardMaininfo_default_instance_;
class CardMesginfo;
struct CardMesginfoDefaultTypeInternal;
extern CardMesginfoDefaultTypeInternal _CardMesginfo_default_instance_;
class IdleFault;
struct IdleFaultDefaultTypeInternal;
extern IdleFaultDefaultTypeInternal _IdleFault_default_instance_;
class SettlementServer;
struct SettlementServerDefaultTypeInternal;
extern SettlementServerDefaultTypeInternal _SettlementServer_default_instance_;
}  // namespace OSCinfo
PROTOBUF_NAMESPACE_OPEN
template<> ::OSCinfo::CardMaininfo* Arena::CreateMaybeMessage<::OSCinfo::CardMaininfo>(Arena*);
template<> ::OSCinfo::CardMesginfo* Arena::CreateMaybeMessage<::OSCinfo::CardMesginfo>(Arena*);
template<> ::OSCinfo::IdleFault* Arena::CreateMaybeMessage<::OSCinfo::IdleFault>(Arena*);
template<> ::OSCinfo::SettlementServer* Arena::CreateMaybeMessage<::OSCinfo::SettlementServer>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace OSCinfo {

enum SettlementModuleEnum : int {
  UnKownModule = 0,
  HMC = 1,
  LOS = 2,
  DMC = 3,
  ICC = 4,
  XJC = 5,
  GWC = 6,
  AMP = 7,
  CJC = 8,
  SHE = 9,
  SXC = 10,
  OCP = 11,
  SPI = 12,
  NDC = 13,
  TED = 14,
  STC = 15,
  XJT = 16,
  RAI = 17,
  CNC = 18,
  HKW = 19,
  EOE = 20,
  GWT = 21,
  LPR = 240,
  SettlementModuleEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  SettlementModuleEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool SettlementModuleEnum_IsValid(int value);
constexpr SettlementModuleEnum SettlementModuleEnum_MIN = UnKownModule;
constexpr SettlementModuleEnum SettlementModuleEnum_MAX = LPR;
constexpr int SettlementModuleEnum_ARRAYSIZE = SettlementModuleEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SettlementModuleEnum_descriptor();
template<typename T>
inline const std::string& SettlementModuleEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SettlementModuleEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SettlementModuleEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SettlementModuleEnum_descriptor(), enum_t_value);
}
inline bool SettlementModuleEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SettlementModuleEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SettlementModuleEnum>(
    SettlementModuleEnum_descriptor(), name, value);
}
enum CardTypeEnum : int {
  UnKownCard = 0,
  M1 = 1,
  CPU = 2,
  VIN = 3,
  CardTypeEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  CardTypeEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool CardTypeEnum_IsValid(int value);
constexpr CardTypeEnum CardTypeEnum_MIN = UnKownCard;
constexpr CardTypeEnum CardTypeEnum_MAX = VIN;
constexpr int CardTypeEnum_ARRAYSIZE = CardTypeEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CardTypeEnum_descriptor();
template<typename T>
inline const std::string& CardTypeEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, CardTypeEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function CardTypeEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    CardTypeEnum_descriptor(), enum_t_value);
}
inline bool CardTypeEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, CardTypeEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<CardTypeEnum>(
    CardTypeEnum_descriptor(), name, value);
}
enum OptionTypeEnum : int {
  ChargeStart = 0,
  ChargeStop = 1,
  CardUnlock = 2,
  BalanceQuery = 3,
  OptionTypeEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  OptionTypeEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool OptionTypeEnum_IsValid(int value);
constexpr OptionTypeEnum OptionTypeEnum_MIN = ChargeStart;
constexpr OptionTypeEnum OptionTypeEnum_MAX = BalanceQuery;
constexpr int OptionTypeEnum_ARRAYSIZE = OptionTypeEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OptionTypeEnum_descriptor();
template<typename T>
inline const std::string& OptionTypeEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, OptionTypeEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function OptionTypeEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    OptionTypeEnum_descriptor(), enum_t_value);
}
inline bool OptionTypeEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, OptionTypeEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<OptionTypeEnum>(
    OptionTypeEnum_descriptor(), name, value);
}
enum CardOperationEnum : int {
  UnKownOperation = 0,
  Doing = 1,
  Done = 2,
  CrcFault = 3,
  OpFault = 4,
  LockedCard = 5,
  SumsFault = 6,
  UnlockedCard = 7,
  WaitUserInput = 8,
  QueryNotSupport = 9,
  DontAllowStartup = 10,
  ConfigError = 11,
  AuthentFailed = 12,
  CardOperationEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  CardOperationEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool CardOperationEnum_IsValid(int value);
constexpr CardOperationEnum CardOperationEnum_MIN = UnKownOperation;
constexpr CardOperationEnum CardOperationEnum_MAX = AuthentFailed;
constexpr int CardOperationEnum_ARRAYSIZE = CardOperationEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CardOperationEnum_descriptor();
template<typename T>
inline const std::string& CardOperationEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, CardOperationEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function CardOperationEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    CardOperationEnum_descriptor(), enum_t_value);
}
inline bool CardOperationEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, CardOperationEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<CardOperationEnum>(
    CardOperationEnum_descriptor(), name, value);
}
enum HMCTipsTypeEnum : int {
  VinAuth = 0,
  Ad = 1,
  OTA = 2,
  Voice = 3,
  AddOil = 4,
  HMCTipsTypeEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  HMCTipsTypeEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool HMCTipsTypeEnum_IsValid(int value);
constexpr HMCTipsTypeEnum HMCTipsTypeEnum_MIN = VinAuth;
constexpr HMCTipsTypeEnum HMCTipsTypeEnum_MAX = AddOil;
constexpr int HMCTipsTypeEnum_ARRAYSIZE = HMCTipsTypeEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* HMCTipsTypeEnum_descriptor();
template<typename T>
inline const std::string& HMCTipsTypeEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, HMCTipsTypeEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function HMCTipsTypeEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    HMCTipsTypeEnum_descriptor(), enum_t_value);
}
inline bool HMCTipsTypeEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, HMCTipsTypeEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<HMCTipsTypeEnum>(
    HMCTipsTypeEnum_descriptor(), name, value);
}
enum HMCTipsRespEnum : int {
  UnKownTips = 0,
  TipsDoing = 1,
  TipsDone = 2,
  VinTimeout = 3,
  VinFault = 4,
  PlatAuthTimeout = 5,
  PlatAuthSumsFault = 6,
  PlatAuthExistFault = 7,
  PlatAuthFault = 8,
  PlatVinTimeIntercept = 9,
  OtaFault = 16,
  AdTimeout = 32,
  ADInsertCanNot = 33,
  ADVciNoInsert = 34,
  InsertGunAgain = 48,
  NoEntryForOilVehicles = 49,
  HMCTipsRespEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  HMCTipsRespEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool HMCTipsRespEnum_IsValid(int value);
constexpr HMCTipsRespEnum HMCTipsRespEnum_MIN = UnKownTips;
constexpr HMCTipsRespEnum HMCTipsRespEnum_MAX = NoEntryForOilVehicles;
constexpr int HMCTipsRespEnum_ARRAYSIZE = HMCTipsRespEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* HMCTipsRespEnum_descriptor();
template<typename T>
inline const std::string& HMCTipsRespEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, HMCTipsRespEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function HMCTipsRespEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    HMCTipsRespEnum_descriptor(), enum_t_value);
}
inline bool HMCTipsRespEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, HMCTipsRespEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<HMCTipsRespEnum>(
    HMCTipsRespEnum_descriptor(), name, value);
}
// ===================================================================

class CardMaininfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:OSCinfo.CardMaininfo) */ {
 public:
  inline CardMaininfo() : CardMaininfo(nullptr) {}
  ~CardMaininfo() override;
  explicit constexpr CardMaininfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CardMaininfo(const CardMaininfo& from);
  CardMaininfo(CardMaininfo&& from) noexcept
    : CardMaininfo() {
    *this = ::std::move(from);
  }

  inline CardMaininfo& operator=(const CardMaininfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline CardMaininfo& operator=(CardMaininfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CardMaininfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const CardMaininfo* internal_default_instance() {
    return reinterpret_cast<const CardMaininfo*>(
               &_CardMaininfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CardMaininfo& a, CardMaininfo& b) {
    a.Swap(&b);
  }
  inline void Swap(CardMaininfo* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CardMaininfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CardMaininfo* New() const final {
    return new CardMaininfo();
  }

  CardMaininfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CardMaininfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CardMaininfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CardMaininfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CardMaininfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "OSCinfo.CardMaininfo";
  }
  protected:
  explicit CardMaininfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeyFieldNumber = 5,
    kCpuTxLoadFieldNumber = 7,
    kModuleIDFieldNumber = 1,
    kCardTypeIDFieldNumber = 2,
    kSectorNumFieldNumber = 3,
    kKeyLengthFieldNumber = 4,
    kCpuTxSizeFieldNumber = 6,
    kCardstatusFieldNumber = 8,
    kBalanceFieldNumber = 9,
  };
  // bytes Key = 5;
  void clear_key();
  const std::string& key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_key();
  PROTOBUF_MUST_USE_RESULT std::string* release_key();
  void set_allocated_key(std::string* key);
  private:
  const std::string& _internal_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_key(const std::string& value);
  std::string* _internal_mutable_key();
  public:

  // bytes CpuTxLoad = 7;
  void clear_cputxload();
  const std::string& cputxload() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_cputxload(ArgT0&& arg0, ArgT... args);
  std::string* mutable_cputxload();
  PROTOBUF_MUST_USE_RESULT std::string* release_cputxload();
  void set_allocated_cputxload(std::string* cputxload);
  private:
  const std::string& _internal_cputxload() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_cputxload(const std::string& value);
  std::string* _internal_mutable_cputxload();
  public:

  // .OSCinfo.SettlementModuleEnum ModuleID = 1;
  void clear_moduleid();
  ::OSCinfo::SettlementModuleEnum moduleid() const;
  void set_moduleid(::OSCinfo::SettlementModuleEnum value);
  private:
  ::OSCinfo::SettlementModuleEnum _internal_moduleid() const;
  void _internal_set_moduleid(::OSCinfo::SettlementModuleEnum value);
  public:

  // .OSCinfo.CardTypeEnum CardTypeID = 2;
  void clear_cardtypeid();
  ::OSCinfo::CardTypeEnum cardtypeid() const;
  void set_cardtypeid(::OSCinfo::CardTypeEnum value);
  private:
  ::OSCinfo::CardTypeEnum _internal_cardtypeid() const;
  void _internal_set_cardtypeid(::OSCinfo::CardTypeEnum value);
  public:

  // uint32 Sector_Num = 3;
  void clear_sector_num();
  ::PROTOBUF_NAMESPACE_ID::uint32 sector_num() const;
  void set_sector_num(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_sector_num() const;
  void _internal_set_sector_num(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 Key_Length = 4;
  void clear_key_length();
  ::PROTOBUF_NAMESPACE_ID::uint32 key_length() const;
  void set_key_length(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_key_length() const;
  void _internal_set_key_length(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 CpuTxSize = 6;
  void clear_cputxsize();
  ::PROTOBUF_NAMESPACE_ID::uint32 cputxsize() const;
  void set_cputxsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_cputxsize() const;
  void _internal_set_cputxsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // .OSCinfo.CardOperationEnum Cardstatus = 8;
  void clear_cardstatus();
  ::OSCinfo::CardOperationEnum cardstatus() const;
  void set_cardstatus(::OSCinfo::CardOperationEnum value);
  private:
  ::OSCinfo::CardOperationEnum _internal_cardstatus() const;
  void _internal_set_cardstatus(::OSCinfo::CardOperationEnum value);
  public:

  // double balance = 9;
  void clear_balance();
  double balance() const;
  void set_balance(double value);
  private:
  double _internal_balance() const;
  void _internal_set_balance(double value);
  public:

  // @@protoc_insertion_point(class_scope:OSCinfo.CardMaininfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr key_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cputxload_;
  int moduleid_;
  int cardtypeid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 sector_num_;
  ::PROTOBUF_NAMESPACE_ID::uint32 key_length_;
  ::PROTOBUF_NAMESPACE_ID::uint32 cputxsize_;
  int cardstatus_;
  double balance_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fOSC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class CardMesginfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:OSCinfo.CardMesginfo) */ {
 public:
  inline CardMesginfo() : CardMesginfo(nullptr) {}
  ~CardMesginfo() override;
  explicit constexpr CardMesginfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CardMesginfo(const CardMesginfo& from);
  CardMesginfo(CardMesginfo&& from) noexcept
    : CardMesginfo() {
    *this = ::std::move(from);
  }

  inline CardMesginfo& operator=(const CardMesginfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline CardMesginfo& operator=(CardMesginfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CardMesginfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const CardMesginfo* internal_default_instance() {
    return reinterpret_cast<const CardMesginfo*>(
               &_CardMesginfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(CardMesginfo& a, CardMesginfo& b) {
    a.Swap(&b);
  }
  inline void Swap(CardMesginfo* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CardMesginfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CardMesginfo* New() const final {
    return new CardMesginfo();
  }

  CardMesginfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CardMesginfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CardMesginfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CardMesginfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CardMesginfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "OSCinfo.CardMesginfo";
  }
  protected:
  explicit CardMesginfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDateLoadFieldNumber = 5,
    kCpuRxLoadFieldNumber = 7,
    kModuleIDFieldNumber = 1,
    kCardTypeIDFieldNumber = 2,
    kSectorSizeFieldNumber = 3,
    kSectorListFieldNumber = 4,
    kCpuRxSizeFieldNumber = 6,
    kCardReignFlagFieldNumber = 8,
    kOptTypeFieldNumber = 9,
  };
  // bytes DateLoad = 5;
  void clear_dateload();
  const std::string& dateload() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_dateload(ArgT0&& arg0, ArgT... args);
  std::string* mutable_dateload();
  PROTOBUF_MUST_USE_RESULT std::string* release_dateload();
  void set_allocated_dateload(std::string* dateload);
  private:
  const std::string& _internal_dateload() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_dateload(const std::string& value);
  std::string* _internal_mutable_dateload();
  public:

  // bytes CpuRxLoad = 7;
  void clear_cpurxload();
  const std::string& cpurxload() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_cpurxload(ArgT0&& arg0, ArgT... args);
  std::string* mutable_cpurxload();
  PROTOBUF_MUST_USE_RESULT std::string* release_cpurxload();
  void set_allocated_cpurxload(std::string* cpurxload);
  private:
  const std::string& _internal_cpurxload() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_cpurxload(const std::string& value);
  std::string* _internal_mutable_cpurxload();
  public:

  // .OSCinfo.SettlementModuleEnum ModuleID = 1;
  void clear_moduleid();
  ::OSCinfo::SettlementModuleEnum moduleid() const;
  void set_moduleid(::OSCinfo::SettlementModuleEnum value);
  private:
  ::OSCinfo::SettlementModuleEnum _internal_moduleid() const;
  void _internal_set_moduleid(::OSCinfo::SettlementModuleEnum value);
  public:

  // .OSCinfo.CardTypeEnum CardTypeID = 2;
  void clear_cardtypeid();
  ::OSCinfo::CardTypeEnum cardtypeid() const;
  void set_cardtypeid(::OSCinfo::CardTypeEnum value);
  private:
  ::OSCinfo::CardTypeEnum _internal_cardtypeid() const;
  void _internal_set_cardtypeid(::OSCinfo::CardTypeEnum value);
  public:

  // uint32 SectorSize = 3;
  void clear_sectorsize();
  ::PROTOBUF_NAMESPACE_ID::uint32 sectorsize() const;
  void set_sectorsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_sectorsize() const;
  void _internal_set_sectorsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 SectorList = 4;
  void clear_sectorlist();
  ::PROTOBUF_NAMESPACE_ID::uint32 sectorlist() const;
  void set_sectorlist(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_sectorlist() const;
  void _internal_set_sectorlist(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 CpuRxSize = 6;
  void clear_cpurxsize();
  ::PROTOBUF_NAMESPACE_ID::uint32 cpurxsize() const;
  void set_cpurxsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_cpurxsize() const;
  void _internal_set_cpurxsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 CardReignFlag = 8;
  void clear_cardreignflag();
  ::PROTOBUF_NAMESPACE_ID::uint32 cardreignflag() const;
  void set_cardreignflag(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_cardreignflag() const;
  void _internal_set_cardreignflag(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // .OSCinfo.OptionTypeEnum OptType = 9;
  void clear_opttype();
  ::OSCinfo::OptionTypeEnum opttype() const;
  void set_opttype(::OSCinfo::OptionTypeEnum value);
  private:
  ::OSCinfo::OptionTypeEnum _internal_opttype() const;
  void _internal_set_opttype(::OSCinfo::OptionTypeEnum value);
  public:

  // @@protoc_insertion_point(class_scope:OSCinfo.CardMesginfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr dateload_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cpurxload_;
  int moduleid_;
  int cardtypeid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 sectorsize_;
  ::PROTOBUF_NAMESPACE_ID::uint32 sectorlist_;
  ::PROTOBUF_NAMESPACE_ID::uint32 cpurxsize_;
  ::PROTOBUF_NAMESPACE_ID::uint32 cardreignflag_;
  int opttype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fOSC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class SettlementServer final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:OSCinfo.SettlementServer) */ {
 public:
  inline SettlementServer() : SettlementServer(nullptr) {}
  ~SettlementServer() override;
  explicit constexpr SettlementServer(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SettlementServer(const SettlementServer& from);
  SettlementServer(SettlementServer&& from) noexcept
    : SettlementServer() {
    *this = ::std::move(from);
  }

  inline SettlementServer& operator=(const SettlementServer& from) {
    CopyFrom(from);
    return *this;
  }
  inline SettlementServer& operator=(SettlementServer&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SettlementServer& default_instance() {
    return *internal_default_instance();
  }
  static inline const SettlementServer* internal_default_instance() {
    return reinterpret_cast<const SettlementServer*>(
               &_SettlementServer_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(SettlementServer& a, SettlementServer& b) {
    a.Swap(&b);
  }
  inline void Swap(SettlementServer* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SettlementServer* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SettlementServer* New() const final {
    return new SettlementServer();
  }

  SettlementServer* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SettlementServer>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SettlementServer& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SettlementServer& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SettlementServer* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "OSCinfo.SettlementServer";
  }
  protected:
  explicit SettlementServer(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWebURLFieldNumber = 5,
    kStationNumFieldNumber = 6,
    kCentreIPFieldNumber = 1,
    kCentrePortFieldNumber = 2,
    kIPFieldNumber = 3,
    kPortFieldNumber = 4,
    kMultiSettlementPullFieldNumber = 7,
  };
  // string WebURL = 5;
  void clear_weburl();
  const std::string& weburl() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_weburl(ArgT0&& arg0, ArgT... args);
  std::string* mutable_weburl();
  PROTOBUF_MUST_USE_RESULT std::string* release_weburl();
  void set_allocated_weburl(std::string* weburl);
  private:
  const std::string& _internal_weburl() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_weburl(const std::string& value);
  std::string* _internal_mutable_weburl();
  public:

  // string StationNum = 6;
  void clear_stationnum();
  const std::string& stationnum() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_stationnum(ArgT0&& arg0, ArgT... args);
  std::string* mutable_stationnum();
  PROTOBUF_MUST_USE_RESULT std::string* release_stationnum();
  void set_allocated_stationnum(std::string* stationnum);
  private:
  const std::string& _internal_stationnum() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_stationnum(const std::string& value);
  std::string* _internal_mutable_stationnum();
  public:

  // uint32 CentreIP = 1;
  void clear_centreip();
  ::PROTOBUF_NAMESPACE_ID::uint32 centreip() const;
  void set_centreip(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_centreip() const;
  void _internal_set_centreip(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 CentrePort = 2;
  void clear_centreport();
  ::PROTOBUF_NAMESPACE_ID::uint32 centreport() const;
  void set_centreport(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_centreport() const;
  void _internal_set_centreport(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 IP = 3;
  void clear_ip();
  ::PROTOBUF_NAMESPACE_ID::uint32 ip() const;
  void set_ip(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_ip() const;
  void _internal_set_ip(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 Port = 4;
  void clear_port();
  ::PROTOBUF_NAMESPACE_ID::uint32 port() const;
  void set_port(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_port() const;
  void _internal_set_port(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 MultiSettlementPull = 7;
  void clear_multisettlementpull();
  ::PROTOBUF_NAMESPACE_ID::uint32 multisettlementpull() const;
  void set_multisettlementpull(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_multisettlementpull() const;
  void _internal_set_multisettlementpull(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:OSCinfo.SettlementServer)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr weburl_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr stationnum_;
  ::PROTOBUF_NAMESPACE_ID::uint32 centreip_;
  ::PROTOBUF_NAMESPACE_ID::uint32 centreport_;
  ::PROTOBUF_NAMESPACE_ID::uint32 ip_;
  ::PROTOBUF_NAMESPACE_ID::uint32 port_;
  ::PROTOBUF_NAMESPACE_ID::uint32 multisettlementpull_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fOSC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class IdleFault final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:OSCinfo.IdleFault) */ {
 public:
  inline IdleFault() : IdleFault(nullptr) {}
  ~IdleFault() override;
  explicit constexpr IdleFault(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  IdleFault(const IdleFault& from);
  IdleFault(IdleFault&& from) noexcept
    : IdleFault() {
    *this = ::std::move(from);
  }

  inline IdleFault& operator=(const IdleFault& from) {
    CopyFrom(from);
    return *this;
  }
  inline IdleFault& operator=(IdleFault&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const IdleFault& default_instance() {
    return *internal_default_instance();
  }
  static inline const IdleFault* internal_default_instance() {
    return reinterpret_cast<const IdleFault*>(
               &_IdleFault_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(IdleFault& a, IdleFault& b) {
    a.Swap(&b);
  }
  inline void Swap(IdleFault* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(IdleFault* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline IdleFault* New() const final {
    return new IdleFault();
  }

  IdleFault* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<IdleFault>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const IdleFault& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const IdleFault& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(IdleFault* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "OSCinfo.IdleFault";
  }
  protected:
  explicit IdleFault(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOHPFaultFieldNumber = 3,
    kMainAlarmListFieldNumber = 5,
    kVCIFaultFieldNumber = 7,
    kDMCFaultFieldNumber = 9,
    kMeterIDFieldNumber = 1,
    kOHPFaultSizeFieldNumber = 2,
    kMainAlarmListSizeFieldNumber = 4,
    kVCIFaultSizeFieldNumber = 6,
    kDMCFaultSizeFieldNumber = 8,
  };
  // repeated .AllFaultEnum.OHPFaultState OHPFault = 3;
  int ohpfault_size() const;
  private:
  int _internal_ohpfault_size() const;
  public:
  void clear_ohpfault();
  ::AllFaultEnum::OHPFaultState* mutable_ohpfault(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::OHPFaultState >*
      mutable_ohpfault();
  private:
  const ::AllFaultEnum::OHPFaultState& _internal_ohpfault(int index) const;
  ::AllFaultEnum::OHPFaultState* _internal_add_ohpfault();
  public:
  const ::AllFaultEnum::OHPFaultState& ohpfault(int index) const;
  ::AllFaultEnum::OHPFaultState* add_ohpfault();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::OHPFaultState >&
      ohpfault() const;

  // repeated .AllFaultEnum.PMMFaultState MainAlarmList = 5;
  int mainalarmlist_size() const;
  private:
  int _internal_mainalarmlist_size() const;
  public:
  void clear_mainalarmlist();
  ::AllFaultEnum::PMMFaultState* mutable_mainalarmlist(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::PMMFaultState >*
      mutable_mainalarmlist();
  private:
  const ::AllFaultEnum::PMMFaultState& _internal_mainalarmlist(int index) const;
  ::AllFaultEnum::PMMFaultState* _internal_add_mainalarmlist();
  public:
  const ::AllFaultEnum::PMMFaultState& mainalarmlist(int index) const;
  ::AllFaultEnum::PMMFaultState* add_mainalarmlist();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::PMMFaultState >&
      mainalarmlist() const;

  // repeated .AllFaultEnum.VCIFaultState VCIFault = 7;
  int vcifault_size() const;
  private:
  int _internal_vcifault_size() const;
  public:
  void clear_vcifault();
  ::AllFaultEnum::VCIFaultState* mutable_vcifault(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::VCIFaultState >*
      mutable_vcifault();
  private:
  const ::AllFaultEnum::VCIFaultState& _internal_vcifault(int index) const;
  ::AllFaultEnum::VCIFaultState* _internal_add_vcifault();
  public:
  const ::AllFaultEnum::VCIFaultState& vcifault(int index) const;
  ::AllFaultEnum::VCIFaultState* add_vcifault();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::VCIFaultState >&
      vcifault() const;

  // repeated .AllFaultEnum.DMCFaultState DMCFault = 9;
  int dmcfault_size() const;
  private:
  int _internal_dmcfault_size() const;
  public:
  void clear_dmcfault();
  ::AllFaultEnum::DMCFaultState* mutable_dmcfault(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::DMCFaultState >*
      mutable_dmcfault();
  private:
  const ::AllFaultEnum::DMCFaultState& _internal_dmcfault(int index) const;
  ::AllFaultEnum::DMCFaultState* _internal_add_dmcfault();
  public:
  const ::AllFaultEnum::DMCFaultState& dmcfault(int index) const;
  ::AllFaultEnum::DMCFaultState* add_dmcfault();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::DMCFaultState >&
      dmcfault() const;

  // uint32 MeterID = 1;
  void clear_meterid();
  ::PROTOBUF_NAMESPACE_ID::uint32 meterid() const;
  void set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_meterid() const;
  void _internal_set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 OHPFaultSize = 2;
  void clear_ohpfaultsize();
  ::PROTOBUF_NAMESPACE_ID::uint32 ohpfaultsize() const;
  void set_ohpfaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_ohpfaultsize() const;
  void _internal_set_ohpfaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 MainAlarmListSize = 4;
  void clear_mainalarmlistsize();
  ::PROTOBUF_NAMESPACE_ID::uint32 mainalarmlistsize() const;
  void set_mainalarmlistsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_mainalarmlistsize() const;
  void _internal_set_mainalarmlistsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 VCIFaultSize = 6;
  void clear_vcifaultsize();
  ::PROTOBUF_NAMESPACE_ID::uint32 vcifaultsize() const;
  void set_vcifaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_vcifaultsize() const;
  void _internal_set_vcifaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 DMCFaultSize = 8;
  void clear_dmcfaultsize();
  ::PROTOBUF_NAMESPACE_ID::uint32 dmcfaultsize() const;
  void set_dmcfaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_dmcfaultsize() const;
  void _internal_set_dmcfaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:OSCinfo.IdleFault)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::OHPFaultState > ohpfault_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::PMMFaultState > mainalarmlist_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::VCIFaultState > vcifault_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::DMCFaultState > dmcfault_;
  ::PROTOBUF_NAMESPACE_ID::uint32 meterid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 ohpfaultsize_;
  ::PROTOBUF_NAMESPACE_ID::uint32 mainalarmlistsize_;
  ::PROTOBUF_NAMESPACE_ID::uint32 vcifaultsize_;
  ::PROTOBUF_NAMESPACE_ID::uint32 dmcfaultsize_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fOSC_5fINFO_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CardMaininfo

// .OSCinfo.SettlementModuleEnum ModuleID = 1;
inline void CardMaininfo::clear_moduleid() {
  moduleid_ = 0;
}
inline ::OSCinfo::SettlementModuleEnum CardMaininfo::_internal_moduleid() const {
  return static_cast< ::OSCinfo::SettlementModuleEnum >(moduleid_);
}
inline ::OSCinfo::SettlementModuleEnum CardMaininfo::moduleid() const {
  // @@protoc_insertion_point(field_get:OSCinfo.CardMaininfo.ModuleID)
  return _internal_moduleid();
}
inline void CardMaininfo::_internal_set_moduleid(::OSCinfo::SettlementModuleEnum value) {
  
  moduleid_ = value;
}
inline void CardMaininfo::set_moduleid(::OSCinfo::SettlementModuleEnum value) {
  _internal_set_moduleid(value);
  // @@protoc_insertion_point(field_set:OSCinfo.CardMaininfo.ModuleID)
}

// .OSCinfo.CardTypeEnum CardTypeID = 2;
inline void CardMaininfo::clear_cardtypeid() {
  cardtypeid_ = 0;
}
inline ::OSCinfo::CardTypeEnum CardMaininfo::_internal_cardtypeid() const {
  return static_cast< ::OSCinfo::CardTypeEnum >(cardtypeid_);
}
inline ::OSCinfo::CardTypeEnum CardMaininfo::cardtypeid() const {
  // @@protoc_insertion_point(field_get:OSCinfo.CardMaininfo.CardTypeID)
  return _internal_cardtypeid();
}
inline void CardMaininfo::_internal_set_cardtypeid(::OSCinfo::CardTypeEnum value) {
  
  cardtypeid_ = value;
}
inline void CardMaininfo::set_cardtypeid(::OSCinfo::CardTypeEnum value) {
  _internal_set_cardtypeid(value);
  // @@protoc_insertion_point(field_set:OSCinfo.CardMaininfo.CardTypeID)
}

// uint32 Sector_Num = 3;
inline void CardMaininfo::clear_sector_num() {
  sector_num_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CardMaininfo::_internal_sector_num() const {
  return sector_num_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CardMaininfo::sector_num() const {
  // @@protoc_insertion_point(field_get:OSCinfo.CardMaininfo.Sector_Num)
  return _internal_sector_num();
}
inline void CardMaininfo::_internal_set_sector_num(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  sector_num_ = value;
}
inline void CardMaininfo::set_sector_num(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_sector_num(value);
  // @@protoc_insertion_point(field_set:OSCinfo.CardMaininfo.Sector_Num)
}

// uint32 Key_Length = 4;
inline void CardMaininfo::clear_key_length() {
  key_length_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CardMaininfo::_internal_key_length() const {
  return key_length_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CardMaininfo::key_length() const {
  // @@protoc_insertion_point(field_get:OSCinfo.CardMaininfo.Key_Length)
  return _internal_key_length();
}
inline void CardMaininfo::_internal_set_key_length(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  key_length_ = value;
}
inline void CardMaininfo::set_key_length(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_key_length(value);
  // @@protoc_insertion_point(field_set:OSCinfo.CardMaininfo.Key_Length)
}

// bytes Key = 5;
inline void CardMaininfo::clear_key() {
  key_.ClearToEmpty();
}
inline const std::string& CardMaininfo::key() const {
  // @@protoc_insertion_point(field_get:OSCinfo.CardMaininfo.Key)
  return _internal_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CardMaininfo::set_key(ArgT0&& arg0, ArgT... args) {
 
 key_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:OSCinfo.CardMaininfo.Key)
}
inline std::string* CardMaininfo::mutable_key() {
  std::string* _s = _internal_mutable_key();
  // @@protoc_insertion_point(field_mutable:OSCinfo.CardMaininfo.Key)
  return _s;
}
inline const std::string& CardMaininfo::_internal_key() const {
  return key_.Get();
}
inline void CardMaininfo::_internal_set_key(const std::string& value) {
  
  key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CardMaininfo::_internal_mutable_key() {
  
  return key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CardMaininfo::release_key() {
  // @@protoc_insertion_point(field_release:OSCinfo.CardMaininfo.Key)
  return key_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CardMaininfo::set_allocated_key(std::string* key) {
  if (key != nullptr) {
    
  } else {
    
  }
  key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), key,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:OSCinfo.CardMaininfo.Key)
}

// uint32 CpuTxSize = 6;
inline void CardMaininfo::clear_cputxsize() {
  cputxsize_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CardMaininfo::_internal_cputxsize() const {
  return cputxsize_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CardMaininfo::cputxsize() const {
  // @@protoc_insertion_point(field_get:OSCinfo.CardMaininfo.CpuTxSize)
  return _internal_cputxsize();
}
inline void CardMaininfo::_internal_set_cputxsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  cputxsize_ = value;
}
inline void CardMaininfo::set_cputxsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_cputxsize(value);
  // @@protoc_insertion_point(field_set:OSCinfo.CardMaininfo.CpuTxSize)
}

// bytes CpuTxLoad = 7;
inline void CardMaininfo::clear_cputxload() {
  cputxload_.ClearToEmpty();
}
inline const std::string& CardMaininfo::cputxload() const {
  // @@protoc_insertion_point(field_get:OSCinfo.CardMaininfo.CpuTxLoad)
  return _internal_cputxload();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CardMaininfo::set_cputxload(ArgT0&& arg0, ArgT... args) {
 
 cputxload_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:OSCinfo.CardMaininfo.CpuTxLoad)
}
inline std::string* CardMaininfo::mutable_cputxload() {
  std::string* _s = _internal_mutable_cputxload();
  // @@protoc_insertion_point(field_mutable:OSCinfo.CardMaininfo.CpuTxLoad)
  return _s;
}
inline const std::string& CardMaininfo::_internal_cputxload() const {
  return cputxload_.Get();
}
inline void CardMaininfo::_internal_set_cputxload(const std::string& value) {
  
  cputxload_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CardMaininfo::_internal_mutable_cputxload() {
  
  return cputxload_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CardMaininfo::release_cputxload() {
  // @@protoc_insertion_point(field_release:OSCinfo.CardMaininfo.CpuTxLoad)
  return cputxload_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CardMaininfo::set_allocated_cputxload(std::string* cputxload) {
  if (cputxload != nullptr) {
    
  } else {
    
  }
  cputxload_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), cputxload,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:OSCinfo.CardMaininfo.CpuTxLoad)
}

// .OSCinfo.CardOperationEnum Cardstatus = 8;
inline void CardMaininfo::clear_cardstatus() {
  cardstatus_ = 0;
}
inline ::OSCinfo::CardOperationEnum CardMaininfo::_internal_cardstatus() const {
  return static_cast< ::OSCinfo::CardOperationEnum >(cardstatus_);
}
inline ::OSCinfo::CardOperationEnum CardMaininfo::cardstatus() const {
  // @@protoc_insertion_point(field_get:OSCinfo.CardMaininfo.Cardstatus)
  return _internal_cardstatus();
}
inline void CardMaininfo::_internal_set_cardstatus(::OSCinfo::CardOperationEnum value) {
  
  cardstatus_ = value;
}
inline void CardMaininfo::set_cardstatus(::OSCinfo::CardOperationEnum value) {
  _internal_set_cardstatus(value);
  // @@protoc_insertion_point(field_set:OSCinfo.CardMaininfo.Cardstatus)
}

// double balance = 9;
inline void CardMaininfo::clear_balance() {
  balance_ = 0;
}
inline double CardMaininfo::_internal_balance() const {
  return balance_;
}
inline double CardMaininfo::balance() const {
  // @@protoc_insertion_point(field_get:OSCinfo.CardMaininfo.balance)
  return _internal_balance();
}
inline void CardMaininfo::_internal_set_balance(double value) {
  
  balance_ = value;
}
inline void CardMaininfo::set_balance(double value) {
  _internal_set_balance(value);
  // @@protoc_insertion_point(field_set:OSCinfo.CardMaininfo.balance)
}

// -------------------------------------------------------------------

// CardMesginfo

// .OSCinfo.SettlementModuleEnum ModuleID = 1;
inline void CardMesginfo::clear_moduleid() {
  moduleid_ = 0;
}
inline ::OSCinfo::SettlementModuleEnum CardMesginfo::_internal_moduleid() const {
  return static_cast< ::OSCinfo::SettlementModuleEnum >(moduleid_);
}
inline ::OSCinfo::SettlementModuleEnum CardMesginfo::moduleid() const {
  // @@protoc_insertion_point(field_get:OSCinfo.CardMesginfo.ModuleID)
  return _internal_moduleid();
}
inline void CardMesginfo::_internal_set_moduleid(::OSCinfo::SettlementModuleEnum value) {
  
  moduleid_ = value;
}
inline void CardMesginfo::set_moduleid(::OSCinfo::SettlementModuleEnum value) {
  _internal_set_moduleid(value);
  // @@protoc_insertion_point(field_set:OSCinfo.CardMesginfo.ModuleID)
}

// .OSCinfo.CardTypeEnum CardTypeID = 2;
inline void CardMesginfo::clear_cardtypeid() {
  cardtypeid_ = 0;
}
inline ::OSCinfo::CardTypeEnum CardMesginfo::_internal_cardtypeid() const {
  return static_cast< ::OSCinfo::CardTypeEnum >(cardtypeid_);
}
inline ::OSCinfo::CardTypeEnum CardMesginfo::cardtypeid() const {
  // @@protoc_insertion_point(field_get:OSCinfo.CardMesginfo.CardTypeID)
  return _internal_cardtypeid();
}
inline void CardMesginfo::_internal_set_cardtypeid(::OSCinfo::CardTypeEnum value) {
  
  cardtypeid_ = value;
}
inline void CardMesginfo::set_cardtypeid(::OSCinfo::CardTypeEnum value) {
  _internal_set_cardtypeid(value);
  // @@protoc_insertion_point(field_set:OSCinfo.CardMesginfo.CardTypeID)
}

// uint32 SectorSize = 3;
inline void CardMesginfo::clear_sectorsize() {
  sectorsize_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CardMesginfo::_internal_sectorsize() const {
  return sectorsize_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CardMesginfo::sectorsize() const {
  // @@protoc_insertion_point(field_get:OSCinfo.CardMesginfo.SectorSize)
  return _internal_sectorsize();
}
inline void CardMesginfo::_internal_set_sectorsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  sectorsize_ = value;
}
inline void CardMesginfo::set_sectorsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_sectorsize(value);
  // @@protoc_insertion_point(field_set:OSCinfo.CardMesginfo.SectorSize)
}

// uint32 SectorList = 4;
inline void CardMesginfo::clear_sectorlist() {
  sectorlist_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CardMesginfo::_internal_sectorlist() const {
  return sectorlist_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CardMesginfo::sectorlist() const {
  // @@protoc_insertion_point(field_get:OSCinfo.CardMesginfo.SectorList)
  return _internal_sectorlist();
}
inline void CardMesginfo::_internal_set_sectorlist(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  sectorlist_ = value;
}
inline void CardMesginfo::set_sectorlist(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_sectorlist(value);
  // @@protoc_insertion_point(field_set:OSCinfo.CardMesginfo.SectorList)
}

// bytes DateLoad = 5;
inline void CardMesginfo::clear_dateload() {
  dateload_.ClearToEmpty();
}
inline const std::string& CardMesginfo::dateload() const {
  // @@protoc_insertion_point(field_get:OSCinfo.CardMesginfo.DateLoad)
  return _internal_dateload();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CardMesginfo::set_dateload(ArgT0&& arg0, ArgT... args) {
 
 dateload_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:OSCinfo.CardMesginfo.DateLoad)
}
inline std::string* CardMesginfo::mutable_dateload() {
  std::string* _s = _internal_mutable_dateload();
  // @@protoc_insertion_point(field_mutable:OSCinfo.CardMesginfo.DateLoad)
  return _s;
}
inline const std::string& CardMesginfo::_internal_dateload() const {
  return dateload_.Get();
}
inline void CardMesginfo::_internal_set_dateload(const std::string& value) {
  
  dateload_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CardMesginfo::_internal_mutable_dateload() {
  
  return dateload_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CardMesginfo::release_dateload() {
  // @@protoc_insertion_point(field_release:OSCinfo.CardMesginfo.DateLoad)
  return dateload_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CardMesginfo::set_allocated_dateload(std::string* dateload) {
  if (dateload != nullptr) {
    
  } else {
    
  }
  dateload_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), dateload,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:OSCinfo.CardMesginfo.DateLoad)
}

// uint32 CpuRxSize = 6;
inline void CardMesginfo::clear_cpurxsize() {
  cpurxsize_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CardMesginfo::_internal_cpurxsize() const {
  return cpurxsize_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CardMesginfo::cpurxsize() const {
  // @@protoc_insertion_point(field_get:OSCinfo.CardMesginfo.CpuRxSize)
  return _internal_cpurxsize();
}
inline void CardMesginfo::_internal_set_cpurxsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  cpurxsize_ = value;
}
inline void CardMesginfo::set_cpurxsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_cpurxsize(value);
  // @@protoc_insertion_point(field_set:OSCinfo.CardMesginfo.CpuRxSize)
}

// bytes CpuRxLoad = 7;
inline void CardMesginfo::clear_cpurxload() {
  cpurxload_.ClearToEmpty();
}
inline const std::string& CardMesginfo::cpurxload() const {
  // @@protoc_insertion_point(field_get:OSCinfo.CardMesginfo.CpuRxLoad)
  return _internal_cpurxload();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CardMesginfo::set_cpurxload(ArgT0&& arg0, ArgT... args) {
 
 cpurxload_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:OSCinfo.CardMesginfo.CpuRxLoad)
}
inline std::string* CardMesginfo::mutable_cpurxload() {
  std::string* _s = _internal_mutable_cpurxload();
  // @@protoc_insertion_point(field_mutable:OSCinfo.CardMesginfo.CpuRxLoad)
  return _s;
}
inline const std::string& CardMesginfo::_internal_cpurxload() const {
  return cpurxload_.Get();
}
inline void CardMesginfo::_internal_set_cpurxload(const std::string& value) {
  
  cpurxload_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CardMesginfo::_internal_mutable_cpurxload() {
  
  return cpurxload_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CardMesginfo::release_cpurxload() {
  // @@protoc_insertion_point(field_release:OSCinfo.CardMesginfo.CpuRxLoad)
  return cpurxload_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CardMesginfo::set_allocated_cpurxload(std::string* cpurxload) {
  if (cpurxload != nullptr) {
    
  } else {
    
  }
  cpurxload_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), cpurxload,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:OSCinfo.CardMesginfo.CpuRxLoad)
}

// uint32 CardReignFlag = 8;
inline void CardMesginfo::clear_cardreignflag() {
  cardreignflag_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CardMesginfo::_internal_cardreignflag() const {
  return cardreignflag_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CardMesginfo::cardreignflag() const {
  // @@protoc_insertion_point(field_get:OSCinfo.CardMesginfo.CardReignFlag)
  return _internal_cardreignflag();
}
inline void CardMesginfo::_internal_set_cardreignflag(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  cardreignflag_ = value;
}
inline void CardMesginfo::set_cardreignflag(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_cardreignflag(value);
  // @@protoc_insertion_point(field_set:OSCinfo.CardMesginfo.CardReignFlag)
}

// .OSCinfo.OptionTypeEnum OptType = 9;
inline void CardMesginfo::clear_opttype() {
  opttype_ = 0;
}
inline ::OSCinfo::OptionTypeEnum CardMesginfo::_internal_opttype() const {
  return static_cast< ::OSCinfo::OptionTypeEnum >(opttype_);
}
inline ::OSCinfo::OptionTypeEnum CardMesginfo::opttype() const {
  // @@protoc_insertion_point(field_get:OSCinfo.CardMesginfo.OptType)
  return _internal_opttype();
}
inline void CardMesginfo::_internal_set_opttype(::OSCinfo::OptionTypeEnum value) {
  
  opttype_ = value;
}
inline void CardMesginfo::set_opttype(::OSCinfo::OptionTypeEnum value) {
  _internal_set_opttype(value);
  // @@protoc_insertion_point(field_set:OSCinfo.CardMesginfo.OptType)
}

// -------------------------------------------------------------------

// SettlementServer

// uint32 CentreIP = 1;
inline void SettlementServer::clear_centreip() {
  centreip_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SettlementServer::_internal_centreip() const {
  return centreip_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SettlementServer::centreip() const {
  // @@protoc_insertion_point(field_get:OSCinfo.SettlementServer.CentreIP)
  return _internal_centreip();
}
inline void SettlementServer::_internal_set_centreip(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  centreip_ = value;
}
inline void SettlementServer::set_centreip(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_centreip(value);
  // @@protoc_insertion_point(field_set:OSCinfo.SettlementServer.CentreIP)
}

// uint32 CentrePort = 2;
inline void SettlementServer::clear_centreport() {
  centreport_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SettlementServer::_internal_centreport() const {
  return centreport_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SettlementServer::centreport() const {
  // @@protoc_insertion_point(field_get:OSCinfo.SettlementServer.CentrePort)
  return _internal_centreport();
}
inline void SettlementServer::_internal_set_centreport(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  centreport_ = value;
}
inline void SettlementServer::set_centreport(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_centreport(value);
  // @@protoc_insertion_point(field_set:OSCinfo.SettlementServer.CentrePort)
}

// uint32 IP = 3;
inline void SettlementServer::clear_ip() {
  ip_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SettlementServer::_internal_ip() const {
  return ip_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SettlementServer::ip() const {
  // @@protoc_insertion_point(field_get:OSCinfo.SettlementServer.IP)
  return _internal_ip();
}
inline void SettlementServer::_internal_set_ip(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  ip_ = value;
}
inline void SettlementServer::set_ip(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_ip(value);
  // @@protoc_insertion_point(field_set:OSCinfo.SettlementServer.IP)
}

// uint32 Port = 4;
inline void SettlementServer::clear_port() {
  port_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SettlementServer::_internal_port() const {
  return port_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SettlementServer::port() const {
  // @@protoc_insertion_point(field_get:OSCinfo.SettlementServer.Port)
  return _internal_port();
}
inline void SettlementServer::_internal_set_port(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  port_ = value;
}
inline void SettlementServer::set_port(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_port(value);
  // @@protoc_insertion_point(field_set:OSCinfo.SettlementServer.Port)
}

// string WebURL = 5;
inline void SettlementServer::clear_weburl() {
  weburl_.ClearToEmpty();
}
inline const std::string& SettlementServer::weburl() const {
  // @@protoc_insertion_point(field_get:OSCinfo.SettlementServer.WebURL)
  return _internal_weburl();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SettlementServer::set_weburl(ArgT0&& arg0, ArgT... args) {
 
 weburl_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:OSCinfo.SettlementServer.WebURL)
}
inline std::string* SettlementServer::mutable_weburl() {
  std::string* _s = _internal_mutable_weburl();
  // @@protoc_insertion_point(field_mutable:OSCinfo.SettlementServer.WebURL)
  return _s;
}
inline const std::string& SettlementServer::_internal_weburl() const {
  return weburl_.Get();
}
inline void SettlementServer::_internal_set_weburl(const std::string& value) {
  
  weburl_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SettlementServer::_internal_mutable_weburl() {
  
  return weburl_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SettlementServer::release_weburl() {
  // @@protoc_insertion_point(field_release:OSCinfo.SettlementServer.WebURL)
  return weburl_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SettlementServer::set_allocated_weburl(std::string* weburl) {
  if (weburl != nullptr) {
    
  } else {
    
  }
  weburl_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), weburl,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:OSCinfo.SettlementServer.WebURL)
}

// string StationNum = 6;
inline void SettlementServer::clear_stationnum() {
  stationnum_.ClearToEmpty();
}
inline const std::string& SettlementServer::stationnum() const {
  // @@protoc_insertion_point(field_get:OSCinfo.SettlementServer.StationNum)
  return _internal_stationnum();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SettlementServer::set_stationnum(ArgT0&& arg0, ArgT... args) {
 
 stationnum_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:OSCinfo.SettlementServer.StationNum)
}
inline std::string* SettlementServer::mutable_stationnum() {
  std::string* _s = _internal_mutable_stationnum();
  // @@protoc_insertion_point(field_mutable:OSCinfo.SettlementServer.StationNum)
  return _s;
}
inline const std::string& SettlementServer::_internal_stationnum() const {
  return stationnum_.Get();
}
inline void SettlementServer::_internal_set_stationnum(const std::string& value) {
  
  stationnum_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SettlementServer::_internal_mutable_stationnum() {
  
  return stationnum_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SettlementServer::release_stationnum() {
  // @@protoc_insertion_point(field_release:OSCinfo.SettlementServer.StationNum)
  return stationnum_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SettlementServer::set_allocated_stationnum(std::string* stationnum) {
  if (stationnum != nullptr) {
    
  } else {
    
  }
  stationnum_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), stationnum,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:OSCinfo.SettlementServer.StationNum)
}

// uint32 MultiSettlementPull = 7;
inline void SettlementServer::clear_multisettlementpull() {
  multisettlementpull_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SettlementServer::_internal_multisettlementpull() const {
  return multisettlementpull_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SettlementServer::multisettlementpull() const {
  // @@protoc_insertion_point(field_get:OSCinfo.SettlementServer.MultiSettlementPull)
  return _internal_multisettlementpull();
}
inline void SettlementServer::_internal_set_multisettlementpull(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  multisettlementpull_ = value;
}
inline void SettlementServer::set_multisettlementpull(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_multisettlementpull(value);
  // @@protoc_insertion_point(field_set:OSCinfo.SettlementServer.MultiSettlementPull)
}

// -------------------------------------------------------------------

// IdleFault

// uint32 MeterID = 1;
inline void IdleFault::clear_meterid() {
  meterid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 IdleFault::_internal_meterid() const {
  return meterid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 IdleFault::meterid() const {
  // @@protoc_insertion_point(field_get:OSCinfo.IdleFault.MeterID)
  return _internal_meterid();
}
inline void IdleFault::_internal_set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  meterid_ = value;
}
inline void IdleFault::set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_meterid(value);
  // @@protoc_insertion_point(field_set:OSCinfo.IdleFault.MeterID)
}

// uint32 OHPFaultSize = 2;
inline void IdleFault::clear_ohpfaultsize() {
  ohpfaultsize_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 IdleFault::_internal_ohpfaultsize() const {
  return ohpfaultsize_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 IdleFault::ohpfaultsize() const {
  // @@protoc_insertion_point(field_get:OSCinfo.IdleFault.OHPFaultSize)
  return _internal_ohpfaultsize();
}
inline void IdleFault::_internal_set_ohpfaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  ohpfaultsize_ = value;
}
inline void IdleFault::set_ohpfaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_ohpfaultsize(value);
  // @@protoc_insertion_point(field_set:OSCinfo.IdleFault.OHPFaultSize)
}

// repeated .AllFaultEnum.OHPFaultState OHPFault = 3;
inline int IdleFault::_internal_ohpfault_size() const {
  return ohpfault_.size();
}
inline int IdleFault::ohpfault_size() const {
  return _internal_ohpfault_size();
}
inline ::AllFaultEnum::OHPFaultState* IdleFault::mutable_ohpfault(int index) {
  // @@protoc_insertion_point(field_mutable:OSCinfo.IdleFault.OHPFault)
  return ohpfault_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::OHPFaultState >*
IdleFault::mutable_ohpfault() {
  // @@protoc_insertion_point(field_mutable_list:OSCinfo.IdleFault.OHPFault)
  return &ohpfault_;
}
inline const ::AllFaultEnum::OHPFaultState& IdleFault::_internal_ohpfault(int index) const {
  return ohpfault_.Get(index);
}
inline const ::AllFaultEnum::OHPFaultState& IdleFault::ohpfault(int index) const {
  // @@protoc_insertion_point(field_get:OSCinfo.IdleFault.OHPFault)
  return _internal_ohpfault(index);
}
inline ::AllFaultEnum::OHPFaultState* IdleFault::_internal_add_ohpfault() {
  return ohpfault_.Add();
}
inline ::AllFaultEnum::OHPFaultState* IdleFault::add_ohpfault() {
  ::AllFaultEnum::OHPFaultState* _add = _internal_add_ohpfault();
  // @@protoc_insertion_point(field_add:OSCinfo.IdleFault.OHPFault)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::OHPFaultState >&
IdleFault::ohpfault() const {
  // @@protoc_insertion_point(field_list:OSCinfo.IdleFault.OHPFault)
  return ohpfault_;
}

// uint32 MainAlarmListSize = 4;
inline void IdleFault::clear_mainalarmlistsize() {
  mainalarmlistsize_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 IdleFault::_internal_mainalarmlistsize() const {
  return mainalarmlistsize_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 IdleFault::mainalarmlistsize() const {
  // @@protoc_insertion_point(field_get:OSCinfo.IdleFault.MainAlarmListSize)
  return _internal_mainalarmlistsize();
}
inline void IdleFault::_internal_set_mainalarmlistsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  mainalarmlistsize_ = value;
}
inline void IdleFault::set_mainalarmlistsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_mainalarmlistsize(value);
  // @@protoc_insertion_point(field_set:OSCinfo.IdleFault.MainAlarmListSize)
}

// repeated .AllFaultEnum.PMMFaultState MainAlarmList = 5;
inline int IdleFault::_internal_mainalarmlist_size() const {
  return mainalarmlist_.size();
}
inline int IdleFault::mainalarmlist_size() const {
  return _internal_mainalarmlist_size();
}
inline ::AllFaultEnum::PMMFaultState* IdleFault::mutable_mainalarmlist(int index) {
  // @@protoc_insertion_point(field_mutable:OSCinfo.IdleFault.MainAlarmList)
  return mainalarmlist_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::PMMFaultState >*
IdleFault::mutable_mainalarmlist() {
  // @@protoc_insertion_point(field_mutable_list:OSCinfo.IdleFault.MainAlarmList)
  return &mainalarmlist_;
}
inline const ::AllFaultEnum::PMMFaultState& IdleFault::_internal_mainalarmlist(int index) const {
  return mainalarmlist_.Get(index);
}
inline const ::AllFaultEnum::PMMFaultState& IdleFault::mainalarmlist(int index) const {
  // @@protoc_insertion_point(field_get:OSCinfo.IdleFault.MainAlarmList)
  return _internal_mainalarmlist(index);
}
inline ::AllFaultEnum::PMMFaultState* IdleFault::_internal_add_mainalarmlist() {
  return mainalarmlist_.Add();
}
inline ::AllFaultEnum::PMMFaultState* IdleFault::add_mainalarmlist() {
  ::AllFaultEnum::PMMFaultState* _add = _internal_add_mainalarmlist();
  // @@protoc_insertion_point(field_add:OSCinfo.IdleFault.MainAlarmList)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::PMMFaultState >&
IdleFault::mainalarmlist() const {
  // @@protoc_insertion_point(field_list:OSCinfo.IdleFault.MainAlarmList)
  return mainalarmlist_;
}

// uint32 VCIFaultSize = 6;
inline void IdleFault::clear_vcifaultsize() {
  vcifaultsize_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 IdleFault::_internal_vcifaultsize() const {
  return vcifaultsize_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 IdleFault::vcifaultsize() const {
  // @@protoc_insertion_point(field_get:OSCinfo.IdleFault.VCIFaultSize)
  return _internal_vcifaultsize();
}
inline void IdleFault::_internal_set_vcifaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  vcifaultsize_ = value;
}
inline void IdleFault::set_vcifaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_vcifaultsize(value);
  // @@protoc_insertion_point(field_set:OSCinfo.IdleFault.VCIFaultSize)
}

// repeated .AllFaultEnum.VCIFaultState VCIFault = 7;
inline int IdleFault::_internal_vcifault_size() const {
  return vcifault_.size();
}
inline int IdleFault::vcifault_size() const {
  return _internal_vcifault_size();
}
inline ::AllFaultEnum::VCIFaultState* IdleFault::mutable_vcifault(int index) {
  // @@protoc_insertion_point(field_mutable:OSCinfo.IdleFault.VCIFault)
  return vcifault_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::VCIFaultState >*
IdleFault::mutable_vcifault() {
  // @@protoc_insertion_point(field_mutable_list:OSCinfo.IdleFault.VCIFault)
  return &vcifault_;
}
inline const ::AllFaultEnum::VCIFaultState& IdleFault::_internal_vcifault(int index) const {
  return vcifault_.Get(index);
}
inline const ::AllFaultEnum::VCIFaultState& IdleFault::vcifault(int index) const {
  // @@protoc_insertion_point(field_get:OSCinfo.IdleFault.VCIFault)
  return _internal_vcifault(index);
}
inline ::AllFaultEnum::VCIFaultState* IdleFault::_internal_add_vcifault() {
  return vcifault_.Add();
}
inline ::AllFaultEnum::VCIFaultState* IdleFault::add_vcifault() {
  ::AllFaultEnum::VCIFaultState* _add = _internal_add_vcifault();
  // @@protoc_insertion_point(field_add:OSCinfo.IdleFault.VCIFault)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::VCIFaultState >&
IdleFault::vcifault() const {
  // @@protoc_insertion_point(field_list:OSCinfo.IdleFault.VCIFault)
  return vcifault_;
}

// uint32 DMCFaultSize = 8;
inline void IdleFault::clear_dmcfaultsize() {
  dmcfaultsize_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 IdleFault::_internal_dmcfaultsize() const {
  return dmcfaultsize_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 IdleFault::dmcfaultsize() const {
  // @@protoc_insertion_point(field_get:OSCinfo.IdleFault.DMCFaultSize)
  return _internal_dmcfaultsize();
}
inline void IdleFault::_internal_set_dmcfaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  dmcfaultsize_ = value;
}
inline void IdleFault::set_dmcfaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_dmcfaultsize(value);
  // @@protoc_insertion_point(field_set:OSCinfo.IdleFault.DMCFaultSize)
}

// repeated .AllFaultEnum.DMCFaultState DMCFault = 9;
inline int IdleFault::_internal_dmcfault_size() const {
  return dmcfault_.size();
}
inline int IdleFault::dmcfault_size() const {
  return _internal_dmcfault_size();
}
inline ::AllFaultEnum::DMCFaultState* IdleFault::mutable_dmcfault(int index) {
  // @@protoc_insertion_point(field_mutable:OSCinfo.IdleFault.DMCFault)
  return dmcfault_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::DMCFaultState >*
IdleFault::mutable_dmcfault() {
  // @@protoc_insertion_point(field_mutable_list:OSCinfo.IdleFault.DMCFault)
  return &dmcfault_;
}
inline const ::AllFaultEnum::DMCFaultState& IdleFault::_internal_dmcfault(int index) const {
  return dmcfault_.Get(index);
}
inline const ::AllFaultEnum::DMCFaultState& IdleFault::dmcfault(int index) const {
  // @@protoc_insertion_point(field_get:OSCinfo.IdleFault.DMCFault)
  return _internal_dmcfault(index);
}
inline ::AllFaultEnum::DMCFaultState* IdleFault::_internal_add_dmcfault() {
  return dmcfault_.Add();
}
inline ::AllFaultEnum::DMCFaultState* IdleFault::add_dmcfault() {
  ::AllFaultEnum::DMCFaultState* _add = _internal_add_dmcfault();
  // @@protoc_insertion_point(field_add:OSCinfo.IdleFault.DMCFault)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::DMCFaultState >&
IdleFault::dmcfault() const {
  // @@protoc_insertion_point(field_list:OSCinfo.IdleFault.DMCFault)
  return dmcfault_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace OSCinfo

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::OSCinfo::SettlementModuleEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::OSCinfo::SettlementModuleEnum>() {
  return ::OSCinfo::SettlementModuleEnum_descriptor();
}
template <> struct is_proto_enum< ::OSCinfo::CardTypeEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::OSCinfo::CardTypeEnum>() {
  return ::OSCinfo::CardTypeEnum_descriptor();
}
template <> struct is_proto_enum< ::OSCinfo::OptionTypeEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::OSCinfo::OptionTypeEnum>() {
  return ::OSCinfo::OptionTypeEnum_descriptor();
}
template <> struct is_proto_enum< ::OSCinfo::CardOperationEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::OSCinfo::CardOperationEnum>() {
  return ::OSCinfo::CardOperationEnum_descriptor();
}
template <> struct is_proto_enum< ::OSCinfo::HMCTipsTypeEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::OSCinfo::HMCTipsTypeEnum>() {
  return ::OSCinfo::HMCTipsTypeEnum_descriptor();
}
template <> struct is_proto_enum< ::OSCinfo::HMCTipsRespEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::OSCinfo::HMCTipsRespEnum>() {
  return ::OSCinfo::HMCTipsRespEnum_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_GCU_5fOSC_5fINFO_2eproto
