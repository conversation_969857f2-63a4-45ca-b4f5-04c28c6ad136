syntax = "proto3";
package BMS2015PlusInfo;

//ProtoConferMsg-版本协商信息 22
message ProtoConferMsg {
    uint32 chargerCanType = 1;        // 充电机CAN类型
    uint32 bmsCanType = 2;            // 车辆CAN类型
    uint32 chargerConferResult = 3;   // 充电机版本协商结果:0x00:继续协商;0x01:协商成功;0x02:协商失败
    uint32 EVConferResult = 4;        // 充电机版本协商结果:0x00:继续协商;0x01:协商成功;0x02:协商失败
    bytes chgGbVersion = 5;           // 充机期望或者协商一致的版本号
    bytes EvGbVersion = 6;            // 车辆期望或协商一致的版本号
    bytes chargerGuidanceVersion = 7; // 控制导引版本 1
    bytes chargerTransportVersion = 8;// 传输层版本  1
    bytes bmsGuidanceVersion = 9;     // 车辆控制导引版本 1
    bytes bmsTransportVersion = 10;   // 车辆传输层版本 1   
}

//FunConfer-功能协商
message FunConferMsg {
    uint32 chgConfigFDC = 1;      //充电机对配置功能支持情况 （按位表示，bit0 ~bit7 ，对应FDC1~FDC8）
    uint32 chgAuthenFDC = 2;      //充电机对鉴权功能支持情况
    uint32 chgAppointFDC = 3;     //充电机对预约功能支持情况
    uint32 chgSelfCheckFDC = 4;   //充电机对输出回路检测功能支持情况
    uint32 chgPowerSupplyFDC = 5; //充电机对供电模式功能支持情况
    uint32 chgEnergyTransferFDC = 6;//充电机对预充及能量传输功能支持情况
    uint32 chgEndFDC = 7;         //充电机对结束功能支持情况

    uint32 EVConfigFDC = 8;      //车辆对配置功能支持情况 （按位表示，bit0 ~bit7 ，对应FDC1~FDC8）
    uint32 EVAuthenFDC = 9;      //车辆对鉴权功能支持情况
    uint32 EVAppointFDC = 10;    //车辆对预约功能支持情况
    uint32 EVSelfCheckFDC = 11;  //车辆对输出回路检测功能支持情况
    uint32 EVPowerSupplyFDC = 12;//车辆对供电模式功能支持情况
    uint32 EVEnergyTransferFDC = 13;//车辆对预充及能量传输功能支持情况
    uint32 EVEndFDC = 14;        //车辆对结束功能支持情况
}

//BmsConfig-参数配置阶段BMS信息 17
message BMSConfig {
    float monoVolMaxAllowed = 1;      // 单体动力蓄电池最高允许充电电压
    float curAllowedMax = 2;          // 最高允许充电电流
    float energyAllowdMax = 3;        // 电池允许输入总能量
    float volAllowedMax = 4;          // 最高允许充电总电压
    float tempAllowedMax = 5;         // 最高允许温度
    float startSOC = 6;               // 整车动力蓄电池荷电状态(SOC)
    float volBatNow = 7;              // 整车动力蓄电池当前电池电压
    float volChargerMax = 8;          // 充电机最高输出电压
    float volChargerMin = 9;          // 充电机最低输出电压
    float curChargerMax = 10;         // 充电机最大输出电流
    float curChargerMin = 11;         // 充电机最小输出电流

    // Daniel add
    uint32 currUpRate = 12;           //电流上调速率
    uint32 currDownRate = 13;         // 电流下调速率
    uint32 chargerAllowedRestartNum = 14;// 电桩允许重启次数
    uint32 bmsAllowedRestartNum = 15; // 车辆允许重启次数
}

// 放电配置信息 16
message DischargeConfig{
    float volDischargeMax = 1;        // 充电机最高放电电压
    float volDischargeMin = 2;        // 充电机最低输出电压
    float curDischargeMax = 3;        // 充电机最大输出电流
    float curDischargeMin = 4;        // 充电机最小输出电流

    float volBmsAllowedMin = 5;       // 车辆最低允许放电电压
    float curBmsAllowedMax = 6;       // 车辆最大允许放电电流
    float curBmsAllowedMin = 7;       // 车辆最小允许放电电流
    float volCellAllowedMax = 8;      // 动力电池单元最高允许电压
    float volCellAllowedMin = 9;      // 动力电池单元最低允许电压
    float socAllowedMin = 10;         // 车辆最低允许荷电状态
    
    float totalBatteryCycleNum = 11;  // 已进行的充放电循环次数
    float allowedBatteryCycleNum = 12;// 本次可用的剩余充放电循环次数
    float desireResidueRange = 13;    // 期望剩余续航里程
    float tempAllowedMax = 14;        // 最高允许温度
    uint32 chargerAllowedRestartNum = 15;// 电桩允许重启次数
    uint32 bmsAllowedRestartNum = 16;  // 车辆允许重启次数
}
// 鉴权信息 66
message AuthenMsg{
  uint32 nowFDC = 1;             // 当前鉴权的FDC: 0:无鉴权；1~8:FDC功能模块
  uint32 authenWaitTime = 2;     // 总鉴权等待时间
  uint32 bmsWaitAuthenState = 3; // 车辆当前等待鉴权状态 0x00:继续等待； 0xDD: 不同意等待
  uint32 authenResult = 4;       // 鉴权结果 0x00,鉴权失败，0xAA: 鉴权成功
  uint32 succeedAuthenFDC = 5;   // 成功鉴权的FDC
  bytes  eVIN = 6;               // 车辆识别码EVIN
  bytes  batProducer = 7;        // 电池生产厂商名称（VIN中节选Byte1～Byte9）
  bytes  chargerOperators = 8;   // 充电运营商编码 9 字节
  bytes  chargerNumber = 9;      // 充电设备接口编码 26字节
}
//预约充电信息 5
message ReserveMsg {
    uint32 bmsDesireStartTime = 1;    // 车辆期望开始充电时间
    uint32 bmsDesireLeaveTime = 2;    // 车辆期望出发时间
    float  chargerOutPowerMax = 3;    // 充电机最大输出功率
    uint32 immediateChargeSupport = 4;// 立即充电支持 0x00 都不支持, 0x01:都支持;0x02:仅电桩支持;0x03:仅车辆支持
    uint32 reserveResult = 5;         // 预约充电结果  0x00:未知，0x01:预约协商成功;0x02:立即充电； 0x03 预约协商失败; 0x04: 不进行立即充电
}

//充电机自检信息 4
message SelfcheckMsg {
    uint32 stickCheckState = 1;         // 接触器粘连检测 0x00 待检;0x01：检测中;0x02 成功;0x03:失败
    uint32 shortCircuitCheckState = 2;  // 短路检测
    uint32 insultCheckState = 3;        // 绝缘检测状态
    uint32 dischargeState  = 4;         // 泄放状态 
}
//车辆外设状态 3
message VehicelState {
    uint32 vehicleC5State = 1;      //车辆输出接触器正极状态 0x00 断开；0xAA 闭合； 0xFF 不可信
    uint32 vehicleC6State = 2;      //车辆输出接触器正极状态 0x00 断开；0xAA 闭合； 0xFF 不可信
    uint32 vehicelElockState = 3;   //车辆电子锁状态 0x00 未锁止；0xAA 锁止； 0xFF 不可信
}

// 供电模式 66
message PowerSupplyMsg{
    uint32 chargerSupplyState = 1;   // 充电机供电状态
    uint32 EvSupplyState = 2;        // 车辆供电状态
    float volDesire = 3;             // 供电电压需求
    float currDesire = 4;            // 供电电流需求
    float outVol = 5;                // 供电电压
    float outCurr = 6;               //  供电电流
    float chgOutCurrMax = 7;         // 充电机最大输出电流能力
    float resonForCapacityChange = 8; //  充电机当前输出能力变化原因
}

//BmsChargeFinish-充电结束阶段BMS信息 17
message BMSChargingEnd {
    uint32 bmsStickCheckState = 1;   // 车辆粘连检测状态0:待检测；1:检测中;2:异常中止;3:检测通过;4:检测失败;0xFF:本次不检测
    uint32 chgStickCheckEnable = 2;  // 充电机允许粘连检测状态
    float energyChg = 3;             // 本次充电电能
    float energyDischarge = 4;       // 本次放电电能
    float endSOC = 5;                // 中止荷电状态SOC(%)

    uint32 chargerStopType = 6;     // 充电机中止类型
    uint32 chargerStopCode = 7;     // 充电机中止原因代码
    uint32 bmsStopType = 8;         // 车辆中止类型
    uint32 bmsStopCode = 9;         // 车辆中止原因代码
    uint32 chgReconnectEnable = 10; // 充电机重连请求：0x00 不请求； 0xAA 请求重连； 0xFF 无效
    uint32 reconnectEnable = 11;    // 车辆重连请求：0x00 不请求； 0xAA 请求重连； 0xFF 无效
}

//BmsCharging-充电阶段BMS信息 30
message BMSCharging {
    uint32 bmsReady = 1;             // BMS就绪状态
    uint32 chargerReady = 2;         // 充电机就绪状态
    uint32 chargeMode = 3;           // 充电模式(恒压充/恒流充)
    uint32 resonForCapacityChange = 4; // 充电机输出能力变化原因
    uint32 bmsPause = 5;             // BMS暂停:0xAA 暂停，0x00 恢复
    uint32 chgPause = 6;             // 充电机暂停:0xAA 暂停，0x00 恢复
    float  bmsBatVol = 7;            // 车辆上报电池电压
    float volDemand = 8;             // 需求电压
    float curDemand = 9;             // 需求电流
    float socNow = 10;                // 当前SOC
    float currOutCapacity = 11;       //充电机当前最大输出电流能力
    float volMeasured = 12;           // 充电电压测量值
    float curMeasured = 13;           // 充电电流测量值
    float monoBatVolMax = 14;         // 单体电池最高电压
    float monoBatVolMin = 15;         // 单体电池最低电压
    float tempMax = 16;               // 电池最高温度
    float tempMin = 17;               // 电池最低温度
}

//车桩交互状态机 2015+
enum ChargeState{
    DefaultState = 0x00;   // 缺省值
    ChargeCreate = 0x1;    // 充电线程创建
    ProtoConfer = 0x2;     // 版本协商
    FunctionConfer = 0x3;  // 功能协商
    Authentication = 0x4;  // 鉴权功能
    AppointStage  = 0x5;   // 预约充电
    SelfCheck     = 0x6;   // 输出回路检测
    PowerSupply = 0x7;     // 供电模式
    PreCharge = 0x8;       // 预充
    EnergyTransfer = 0x9;  // 能量传输
    TransferEnd = 0xA;     // 充电结束
    ChgDestory = 0xB;      // 充电线程销毁
}

//  2015+ bms信息
message bms2015pMsg{
    ChargeState bmsState = 1;			    //	BMS交互状态
    ProtoConferMsg bmsProtoConfer = 2;	    //	版本协商
    FunConferMsg bmsFunConfer = 3;		    //	功能协商
    BMSConfig BmsConfgM = 4;				//	参数配置
    BMSCharging BmsChargingM = 5;			//	预充与充电
    BMSChargingEnd BmsChargeFinishM = 6;	//	充电结束

    AuthenMsg BmsAuthenM = 7;              // 鉴权信息
	ReserveMsg BmsReserveM = 8;            // 预约充电信息
	SelfcheckMsg BmsSelfCheckM = 9;        // 充电机自检信息
	VehicelState  BmsVehicelStateM= 10;     // 车辆外设状态
	PowerSupplyMsg PowerSupplyM = 11;       // 供电模式状态
}