// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GCU_OHP_INFO.proto

#include "GCU_OHP_INFO.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace OHPinfo {
constexpr UUIDValue::UUIDValue(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : value0_(uint64_t{0u})
  , value1_(uint64_t{0u}){}
struct UUIDValueDefaultTypeInternal {
  constexpr UUIDValueDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~UUIDValueDefaultTypeInternal() {}
  union {
    UUIDValue _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT UUIDValueDefaultTypeInternal _UUIDValue_default_instance_;
constexpr SettlementModuleState::SettlementModuleState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : moduleid_(0)

  , offlinestrategy_(0)

  , normalstrategy_(0)

  , registerstate_(0u)
  , periodiccommunication_(0u)
  , jurisdiction_(0)
{}
struct SettlementModuleStateDefaultTypeInternal {
  constexpr SettlementModuleStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SettlementModuleStateDefaultTypeInternal() {}
  union {
    SettlementModuleState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SettlementModuleStateDefaultTypeInternal _SettlementModuleState_default_instance_;
constexpr OrderRate::OrderRate(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : runingrate_(0)
  , servicerate_(0)
  , rateenum_(0u){}
struct OrderRateDefaultTypeInternal {
  constexpr OrderRateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~OrderRateDefaultTypeInternal() {}
  union {
    OrderRate _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT OrderRateDefaultTypeInternal _OrderRate_default_instance_;
constexpr OrderBill::OrderBill(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : runingbill_(0){}
struct OrderBillDefaultTypeInternal {
  constexpr OrderBillDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~OrderBillDefaultTypeInternal() {}
  union {
    OrderBill _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT OrderBillDefaultTypeInternal _OrderBill_default_instance_;
constexpr RuningOrderState::RuningOrderState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : orderuuid_(nullptr)
  , startmeterreadout_(0)
  , moduleid_(0)

  , volmeasured_(0)
  , nowmeterreadout_(0)
  , curmeasured_(0){}
struct RuningOrderStateDefaultTypeInternal {
  constexpr RuningOrderStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RuningOrderStateDefaultTypeInternal() {}
  union {
    RuningOrderState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RuningOrderStateDefaultTypeInternal _RuningOrderState_default_instance_;
constexpr MeterState::MeterState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : meterwreadout_(0)
  , meterireadout_(0)
  , metervreadout_(0)
  , meteroffline_(0u)
  , metercheck_(0u)
  , refreshtime_(uint64_t{0u}){}
struct MeterStateDefaultTypeInternal {
  constexpr MeterStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MeterStateDefaultTypeInternal() {}
  union {
    MeterState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MeterStateDefaultTypeInternal _MeterState_default_instance_;
constexpr MeterInfo::MeterInfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : metersn_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , meterver_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , meterid_(0u){}
struct MeterInfoDefaultTypeInternal {
  constexpr MeterInfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MeterInfoDefaultTypeInternal() {}
  union {
    MeterInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MeterInfoDefaultTypeInternal _MeterInfo_default_instance_;
constexpr HistoryOrderList::HistoryOrderList(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : runingratelist_()
  , runingbilllist_()
  , userid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , orderid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , stopreason_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , orderuuid_(nullptr)
  , orderstateenum_(0)

  , meterid_(0u)
  , runingratelistsize_(0u)
  , runingbilllistsize_(0u)
  , startime_(0u)
  , stoptime_(0u)
  , startmeterreadout_(0)
  , stopmeterreadout_(0)
  , oscreserve0_(0u)
  , oscreserve1_(0u){}
struct HistoryOrderListDefaultTypeInternal {
  constexpr HistoryOrderListDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~HistoryOrderListDefaultTypeInternal() {}
  union {
    HistoryOrderList _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT HistoryOrderListDefaultTypeInternal _HistoryOrderList_default_instance_;
constexpr OrderPipelineState::OrderPipelineState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : meterid_(0u)
  , moduleopen_(0u)
  , linkstate_(0u)
  , onlinestate_(0u)
  , lockstate_(0u)
  , starttype_(0u)
  , chargingduration_(0u)
  , pipelinestate_(0)

  , nowmeterreadout_(0)
  , chargingpower_(0)
  , preempt_(0)
{}
struct OrderPipelineStateDefaultTypeInternal {
  constexpr OrderPipelineStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~OrderPipelineStateDefaultTypeInternal() {}
  union {
    OrderPipelineState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT OrderPipelineStateDefaultTypeInternal _OrderPipelineState_default_instance_;
constexpr OrderPipelineAns::OrderPipelineAns(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ohpfault_()
  , mainalarmlist_()
  , vcifault_()
  , dmcfault_()
  , admoduleplist_()
  , metersn_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , meterver_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , bmsshakehandsm_(nullptr)
  , bmsidentifym_(nullptr)
  , bmsconfigm_(nullptr)
  , bmschargingm_(nullptr)
  , bmschargefinishm_(nullptr)
  , contactorstate_(nullptr)
  , bms2015pm_(nullptr)
  , meterid_(0u)
  , bmsstate_(0)

  , ohpfaultsize_(0u)
  , pmmfaultsize_(0u)
  , vcifaultsize_(0u)
  , dmcfaultsize_(0u)
  , admoduleparamsize_(0u){}
struct OrderPipelineAnsDefaultTypeInternal {
  constexpr OrderPipelineAnsDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~OrderPipelineAnsDefaultTypeInternal() {}
  union {
    OrderPipelineAns _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT OrderPipelineAnsDefaultTypeInternal _OrderPipelineAns_default_instance_;
constexpr OrderPipelineInfo::OrderPipelineInfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : orderid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , userid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , startsoc_(0u)
  , starttime_(0u){}
struct OrderPipelineInfoDefaultTypeInternal {
  constexpr OrderPipelineInfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~OrderPipelineInfoDefaultTypeInternal() {}
  union {
    OrderPipelineInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT OrderPipelineInfoDefaultTypeInternal _OrderPipelineInfo_default_instance_;
constexpr hmiConfigInfo::hmiConfigInfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : guns_(0u)
  , guncode_(0u)
  , guntype_(0)

  , vintype_(0u)
  , auxtype_(0u)
  , adminmode_(0u)
  , stoptype_(0u)
  , ratetype_(0u)
  , standbylogotype_(0u)
  , ledtype_(0u)
  , historytype_(0u)
  , stopchgsoctype_(0u)
  , hmiconfigenable_(0u)
  , netofflinewifienable_(0u)
  , vlprenable_(0u){}
struct hmiConfigInfoDefaultTypeInternal {
  constexpr hmiConfigInfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~hmiConfigInfoDefaultTypeInternal() {}
  union {
    hmiConfigInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT hmiConfigInfoDefaultTypeInternal _hmiConfigInfo_default_instance_;
}  // namespace OHPinfo
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_GCU_5fOHP_5fINFO_2eproto[12];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_GCU_5fOHP_5fINFO_2eproto[6];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_GCU_5fOHP_5fINFO_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_GCU_5fOHP_5fINFO_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::UUIDValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::UUIDValue, value0_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::UUIDValue, value1_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::SettlementModuleState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::SettlementModuleState, moduleid_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::SettlementModuleState, offlinestrategy_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::SettlementModuleState, normalstrategy_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::SettlementModuleState, registerstate_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::SettlementModuleState, periodiccommunication_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::SettlementModuleState, jurisdiction_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderRate, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderRate, runingrate_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderRate, servicerate_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderRate, rateenum_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderBill, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderBill, runingbill_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::RuningOrderState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::RuningOrderState, moduleid_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::RuningOrderState, orderuuid_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::RuningOrderState, startmeterreadout_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::RuningOrderState, nowmeterreadout_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::RuningOrderState, volmeasured_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::RuningOrderState, curmeasured_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::MeterState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::MeterState, meterwreadout_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::MeterState, meterireadout_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::MeterState, metervreadout_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::MeterState, meteroffline_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::MeterState, metercheck_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::MeterState, refreshtime_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::MeterInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::MeterInfo, meterid_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::MeterInfo, metersn_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::MeterInfo, meterver_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::HistoryOrderList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::HistoryOrderList, orderuuid_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::HistoryOrderList, orderstateenum_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::HistoryOrderList, meterid_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::HistoryOrderList, userid_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::HistoryOrderList, orderid_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::HistoryOrderList, runingratelistsize_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::HistoryOrderList, runingratelist_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::HistoryOrderList, runingbilllistsize_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::HistoryOrderList, runingbilllist_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::HistoryOrderList, startime_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::HistoryOrderList, stoptime_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::HistoryOrderList, startmeterreadout_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::HistoryOrderList, stopmeterreadout_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::HistoryOrderList, stopreason_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::HistoryOrderList, oscreserve0_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::HistoryOrderList, oscreserve1_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineState, meterid_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineState, moduleopen_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineState, linkstate_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineState, onlinestate_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineState, lockstate_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineState, starttype_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineState, chargingduration_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineState, pipelinestate_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineState, chargingpower_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineState, nowmeterreadout_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineState, preempt_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineAns, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineAns, meterid_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineAns, bmsstate_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineAns, bmsshakehandsm_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineAns, bmsidentifym_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineAns, bmsconfigm_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineAns, bmschargingm_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineAns, bmschargefinishm_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineAns, contactorstate_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineAns, ohpfaultsize_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineAns, ohpfault_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineAns, pmmfaultsize_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineAns, mainalarmlist_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineAns, vcifaultsize_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineAns, vcifault_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineAns, dmcfaultsize_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineAns, dmcfault_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineAns, admoduleparamsize_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineAns, admoduleplist_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineAns, metersn_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineAns, meterver_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineAns, bms2015pm_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineInfo, orderid_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineInfo, userid_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineInfo, startsoc_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::OrderPipelineInfo, starttime_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::hmiConfigInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::OHPinfo::hmiConfigInfo, guns_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::hmiConfigInfo, guncode_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::hmiConfigInfo, guntype_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::hmiConfigInfo, vintype_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::hmiConfigInfo, auxtype_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::hmiConfigInfo, adminmode_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::hmiConfigInfo, stoptype_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::hmiConfigInfo, ratetype_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::hmiConfigInfo, standbylogotype_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::hmiConfigInfo, ledtype_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::hmiConfigInfo, historytype_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::hmiConfigInfo, stopchgsoctype_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::hmiConfigInfo, hmiconfigenable_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::hmiConfigInfo, netofflinewifienable_),
  PROTOBUF_FIELD_OFFSET(::OHPinfo::hmiConfigInfo, vlprenable_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::OHPinfo::UUIDValue)},
  { 7, -1, sizeof(::OHPinfo::SettlementModuleState)},
  { 18, -1, sizeof(::OHPinfo::OrderRate)},
  { 26, -1, sizeof(::OHPinfo::OrderBill)},
  { 32, -1, sizeof(::OHPinfo::RuningOrderState)},
  { 43, -1, sizeof(::OHPinfo::MeterState)},
  { 54, -1, sizeof(::OHPinfo::MeterInfo)},
  { 62, -1, sizeof(::OHPinfo::HistoryOrderList)},
  { 83, -1, sizeof(::OHPinfo::OrderPipelineState)},
  { 99, -1, sizeof(::OHPinfo::OrderPipelineAns)},
  { 125, -1, sizeof(::OHPinfo::OrderPipelineInfo)},
  { 134, -1, sizeof(::OHPinfo::hmiConfigInfo)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::OHPinfo::_UUIDValue_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::OHPinfo::_SettlementModuleState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::OHPinfo::_OrderRate_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::OHPinfo::_OrderBill_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::OHPinfo::_RuningOrderState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::OHPinfo::_MeterState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::OHPinfo::_MeterInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::OHPinfo::_HistoryOrderList_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::OHPinfo::_OrderPipelineState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::OHPinfo::_OrderPipelineAns_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::OHPinfo::_OrderPipelineInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::OHPinfo::_hmiConfigInfo_default_instance_),
};

const char descriptor_table_protodef_GCU_5fOHP_5fINFO_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\022GCU_OHP_INFO.proto\022\007OHPinfo\032\026GCU_AllFa"
  "ultEnum.proto\032\022GCU_BMS_INFO.proto\032\022GCU_O"
  "SC_INFO.proto\032\022GCU_VCI_INFO.proto\032\022GCU_D"
  "MC_INFO.proto\032\027GCU_BMS2015P_INFO.proto\"+"
  "\n\tUUIDValue\022\016\n\006value0\030\001 \001(\004\022\016\n\006value1\030\002 "
  "\001(\004\"\235\002\n\025SettlementModuleState\022/\n\010ModuleI"
  "D\030\001 \001(\0162\035.OSCinfo.SettlementModuleEnum\0223"
  "\n\017OffLineStrategy\030\002 \001(\0162\032.OHPinfo.OrderS"
  "trategyEnum\0222\n\016NormalStrategy\030\003 \001(\0162\032.OH"
  "Pinfo.OrderStrategyEnum\022\025\n\rRegisterState"
  "\030\004 \001(\r\022\035\n\025PeriodicCommunication\030\005 \001(\r\0224\n"
  "\014Jurisdiction\030\006 \001(\0162\036.OHPinfo.OrderJuris"
  "dictionEnum\"F\n\tOrderRate\022\022\n\nRuningRate\030\003"
  " \001(\002\022\023\n\013ServiceRate\030\004 \001(\002\022\020\n\010RateEnum\030\005 "
  "\001(\r\"\037\n\tOrderBill\022\022\n\nRuningBill\030\003 \001(\002\"\310\001\n"
  "\020RuningOrderState\022/\n\010ModuleID\030\001 \001(\0162\035.OS"
  "Cinfo.SettlementModuleEnum\022%\n\tOrderUUID\030"
  "\002 \001(\0132\022.OHPinfo.UUIDValue\022\031\n\021StartMeterR"
  "eadOut\030\003 \001(\001\022\027\n\017NowMeterReadOut\030\004 \001(\001\022\023\n"
  "\013volMeasured\030\005 \001(\002\022\023\n\013curMeasured\030\006 \001(\002\""
  "\220\001\n\nMeterState\022\025\n\rMeterWReadOut\030\001 \001(\001\022\025\n"
  "\rMeterIReadOut\030\002 \001(\001\022\025\n\rMeterVReadOut\030\003 "
  "\001(\001\022\024\n\014MeterOffLine\030\004 \001(\r\022\022\n\nMeterCheck\030"
  "\005 \001(\r\022\023\n\013RefreshTime\030\006 \001(\004\"\?\n\tMeterInfo\022"
  "\017\n\007MeterID\030\001 \001(\r\022\017\n\007MeterSN\030\002 \001(\t\022\020\n\010Met"
  "erVer\030\003 \001(\t\"\303\003\n\020HistoryOrderList\022%\n\tOrde"
  "rUUID\030\001 \001(\0132\022.OHPinfo.UUIDValue\022/\n\016Order"
  "StateEnum\030\002 \001(\0162\027.OHPinfo.OrderStateEnum"
  "\022\017\n\007MeterID\030\003 \001(\r\022\016\n\006UserID\030\004 \001(\t\022\017\n\007Ord"
  "erID\030\005 \001(\t\022\032\n\022RuningRateListSize\030\006 \001(\r\022*"
  "\n\016RuningRateList\030\007 \003(\0132\022.OHPinfo.OrderRa"
  "te\022\032\n\022RuningBillListSize\030\010 \001(\r\022*\n\016Runing"
  "BillList\030\t \003(\0132\022.OHPinfo.OrderBill\022\020\n\010St"
  "arTime\030\n \001(\r\022\020\n\010StopTime\030\013 \001(\r\022\031\n\021StartM"
  "eterReadOut\030\014 \001(\001\022\030\n\020StopMeterReadOut\030\r "
  "\001(\001\022\022\n\nStopReason\030\016 \001(\014\022\023\n\013OSCReserve0\030\017"
  " \001(\r\022\023\n\013OSCReserve1\030\020 \001(\r\"\264\002\n\022OrderPipel"
  "ineState\022\017\n\007MeterID\030\001 \001(\r\022\022\n\nModuleOpen\030"
  "\002 \001(\r\022\021\n\tLinkState\030\003 \001(\r\022\023\n\013OnLineState\030"
  "\004 \001(\r\022\021\n\tLockState\030\005 \001(\r\022\021\n\tStartType\030\006 "
  "\001(\r\022\030\n\020ChargingDuration\030\007 \001(\r\0221\n\rPipelin"
  "eState\030\010 \001(\0162\032.OHPinfo.PipelineStateEnum"
  "\022\025\n\rChargingPower\030\t \001(\002\022\027\n\017NowMeterReadO"
  "ut\030\n \001(\001\022.\n\007Preempt\030\013 \001(\0162\035.OSCinfo.Sett"
  "lementModuleEnum\"\241\006\n\020OrderPipelineAns\022\017\n"
  "\007MeterID\030\001 \001(\r\022&\n\010bmsState\030\002 \001(\0162\024.BMSin"
  "fo.ChargeState\022-\n\016BmsShakehandsM\030\003 \001(\0132\025"
  ".BMSinfo.BMSHandShake\022.\n\014BmsIdentifyM\030\004 "
  "\001(\0132\030.BMSinfo.BMSVerification\022&\n\nBmsConf"
  "igM\030\005 \001(\0132\022.BMSinfo.BMSConfig\022*\n\014BmsChar"
  "gingM\030\006 \001(\0132\024.BMSinfo.BMSCharging\0221\n\020Bms"
  "ChargeFinishM\030\007 \001(\0132\027.BMSinfo.BMSChargin"
  "gEnd\0222\n\016ContactorState\030\010 \001(\0132\032.AllFaultE"
  "num.MatrixStatus\022\024\n\014OHPFaultSize\030\t \001(\r\022-"
  "\n\010OHPFault\030\n \003(\0132\033.AllFaultEnum.OHPFault"
  "State\022\024\n\014PMMFaultSize\030\013 \001(\r\0222\n\rMainAlarm"
  "List\030\014 \003(\0132\033.AllFaultEnum.PMMFaultState\022"
  "\024\n\014VCIFaultSize\030\r \001(\r\022-\n\010VCIFault\030\016 \003(\0132"
  "\033.AllFaultEnum.VCIFaultState\022\024\n\014DMCFault"
  "Size\030\017 \001(\r\022-\n\010DMCFault\030\020 \003(\0132\033.AllFaultE"
  "num.DMCFaultState\022\031\n\021ADModuleParamSize\030\021"
  " \001(\r\0222\n\rADModulePList\030\022 \003(\0132\033.AllFaultEn"
  "um.ADModuleAlarm\022\017\n\007MeterSN\030\023 \001(\t\022\020\n\010Met"
  "erVer\030\024 \001(\t\022/\n\tbms2015pM\030\025 \001(\0132\034.BMS2015"
  "PlusInfo.bms2015pMsg\"Y\n\021OrderPipelineInf"
  "o\022\017\n\007OrderID\030\001 \001(\t\022\016\n\006UserID\030\002 \001(\t\022\020\n\010St"
  "artSoc\030\003 \001(\r\022\021\n\tStartTime\030\004 \001(\r\"\320\002\n\rhmiC"
  "onfigInfo\022\014\n\004guns\030\001 \001(\r\022\017\n\007gunCode\030\002 \001(\r"
  "\022%\n\007GunType\030\003 \001(\0162\024.DMCinfo.GunTypeEnum\022"
  "\017\n\007VINType\030\004 \001(\r\022\017\n\007AuxType\030\005 \001(\r\022\021\n\tadm"
  "inMode\030\006 \001(\r\022\020\n\010stopType\030\007 \001(\r\022\020\n\010RateTy"
  "pe\030\010 \001(\r\022\027\n\017standbylogoType\030\t \001(\r\022\017\n\007led"
  "Type\030\n \001(\r\022\023\n\013historyType\030\013 \001(\r\022\026\n\016stopC"
  "hgSocType\030\014 \001(\r\022\027\n\017hmiConfigEnable\030\r \001(\r"
  "\022\034\n\024netOfflineWifiEnable\030\016 \001(\r\022\022\n\nVLPREn"
  "able\030\017 \001(\r*\256\001\n\rOrderTypeEnum\022\024\n\020DefaultO"
  "rderType\020\000\022\022\n\016LocalFreeOrder\020\001\022\024\n\020LocalC"
  "hargeOrder\020\002\022\026\n\022CloudPlatformOrder\020\003\022\027\n\023"
  "DevicePlatformOrder\020\004\022\027\n\023SpecialVehicleO"
  "rder\020\005\022\023\n\017VINPrimingOrder\020\006*\303\001\n\020OrderSub"
  "TypeEnum\022\023\n\017NormalOrderType\020\000\022\023\n\017VINSubT"
  "ypeOrder\020\001\022\023\n\017UIPassWordOrder\020\002\022\020\n\014NFCCa"
  "rdOrder\020\003\022\020\n\014ETCRFIDOrder\020\004\022\025\n\021LicensePl"
  "ateOrder\020\005\022\032\n\026VisionRecognitionOrder\020\006\022\031"
  "\n\025VoiceRecognitionOrder\020\007*\242\001\n\016OrderState"
  "Enum\022\025\n\021DefaultOrderState\020\000\022\027\n\023Authentic"
  "ationOrder\020\001\022\022\n\016EstablishOrder\020\002\022\017\n\013Runi"
  "ngState\020\003\022\023\n\017SettlementState\020\004\022\022\n\016HangOr"
  "derState\020\005\022\022\n\016CompletedState\020\006*\305\001\n\021Pipel"
  "ineStateEnum\022\030\n\024DefaultPipelineState\020\000\022\023"
  "\n\017DisablePipeline\020\001\022\r\n\tIdleState\020\002\022\021\n\rIn"
  "sertedState\020\003\022\020\n\014PrimingState\020\004\022\021\n\rStart"
  "ingState\020\005\022\021\n\rChargingState\020\006\022\026\n\022Settlem"
  "entPipeline\020\007\022\017\n\013SubPlugMode\020\010*\234\001\n\021Order"
  "StrategyEnum\022\023\n\017DefaultStrategy\020\000\022\025\n\021Loc"
  "alFullStrategy\020\001\022\025\n\021LocalDownStrategy\020\002\022"
  "\017\n\013SOCStrategy\020\003\022\r\n\tVStrategy\020\004\022\022\n\016Energ"
  "yStrategy\020\005\022\020\n\014BillStrategy\020\006*\232\001\n\025OrderJ"
  "urisdictionEnum\022\024\n\020DefaultAuthority\020\000\022\020\n"
  "\014LowAuthority\020\001\022\023\n\017NormalAuthority\020\002\022\030\n\024"
  "ProprietaryAuthority\020\003\022\026\n\022EmergencyAutho"
  "rity\020\004\022\022\n\016LocalAuthority\020\005P\000P\001P\002P\003P\004P\005b\006"
  "proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_GCU_5fOHP_5fINFO_2eproto_deps[6] = {
  &::descriptor_table_GCU_5fAllFaultEnum_2eproto,
  &::descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto,
  &::descriptor_table_GCU_5fBMS_5fINFO_2eproto,
  &::descriptor_table_GCU_5fDMC_5fINFO_2eproto,
  &::descriptor_table_GCU_5fOSC_5fINFO_2eproto,
  &::descriptor_table_GCU_5fVCI_5fINFO_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_GCU_5fOHP_5fINFO_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GCU_5fOHP_5fINFO_2eproto = {
  false, false, 4086, descriptor_table_protodef_GCU_5fOHP_5fINFO_2eproto, "GCU_OHP_INFO.proto", 
  &descriptor_table_GCU_5fOHP_5fINFO_2eproto_once, descriptor_table_GCU_5fOHP_5fINFO_2eproto_deps, 6, 12,
  schemas, file_default_instances, TableStruct_GCU_5fOHP_5fINFO_2eproto::offsets,
  file_level_metadata_GCU_5fOHP_5fINFO_2eproto, file_level_enum_descriptors_GCU_5fOHP_5fINFO_2eproto, file_level_service_descriptors_GCU_5fOHP_5fINFO_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_GCU_5fOHP_5fINFO_2eproto_getter() {
  return &descriptor_table_GCU_5fOHP_5fINFO_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_GCU_5fOHP_5fINFO_2eproto(&descriptor_table_GCU_5fOHP_5fINFO_2eproto);
namespace OHPinfo {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OrderTypeEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fOHP_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fOHP_5fINFO_2eproto[0];
}
bool OrderTypeEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OrderSubTypeEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fOHP_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fOHP_5fINFO_2eproto[1];
}
bool OrderSubTypeEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OrderStateEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fOHP_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fOHP_5fINFO_2eproto[2];
}
bool OrderStateEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PipelineStateEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fOHP_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fOHP_5fINFO_2eproto[3];
}
bool PipelineStateEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OrderStrategyEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fOHP_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fOHP_5fINFO_2eproto[4];
}
bool OrderStrategyEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OrderJurisdictionEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fOHP_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fOHP_5fINFO_2eproto[5];
}
bool OrderJurisdictionEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class UUIDValue::_Internal {
 public:
};

UUIDValue::UUIDValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:OHPinfo.UUIDValue)
}
UUIDValue::UUIDValue(const UUIDValue& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&value0_, &from.value0_,
    static_cast<size_t>(reinterpret_cast<char*>(&value1_) -
    reinterpret_cast<char*>(&value0_)) + sizeof(value1_));
  // @@protoc_insertion_point(copy_constructor:OHPinfo.UUIDValue)
}

inline void UUIDValue::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&value0_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&value1_) -
    reinterpret_cast<char*>(&value0_)) + sizeof(value1_));
}

UUIDValue::~UUIDValue() {
  // @@protoc_insertion_point(destructor:OHPinfo.UUIDValue)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void UUIDValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void UUIDValue::ArenaDtor(void* object) {
  UUIDValue* _this = reinterpret_cast< UUIDValue* >(object);
  (void)_this;
}
void UUIDValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UUIDValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void UUIDValue::Clear() {
// @@protoc_insertion_point(message_clear_start:OHPinfo.UUIDValue)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&value0_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&value1_) -
      reinterpret_cast<char*>(&value0_)) + sizeof(value1_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UUIDValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 value0 = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          value0_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint64 value1 = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          value1_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* UUIDValue::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:OHPinfo.UUIDValue)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 value0 = 1;
  if (this->_internal_value0() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_value0(), target);
  }

  // uint64 value1 = 2;
  if (this->_internal_value1() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(2, this->_internal_value1(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:OHPinfo.UUIDValue)
  return target;
}

size_t UUIDValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:OHPinfo.UUIDValue)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint64 value0 = 1;
  if (this->_internal_value0() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_value0());
  }

  // uint64 value1 = 2;
  if (this->_internal_value1() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_value1());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UUIDValue::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    UUIDValue::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UUIDValue::GetClassData() const { return &_class_data_; }

void UUIDValue::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<UUIDValue *>(to)->MergeFrom(
      static_cast<const UUIDValue &>(from));
}


void UUIDValue::MergeFrom(const UUIDValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:OHPinfo.UUIDValue)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_value0() != 0) {
    _internal_set_value0(from._internal_value0());
  }
  if (from._internal_value1() != 0) {
    _internal_set_value1(from._internal_value1());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void UUIDValue::CopyFrom(const UUIDValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:OHPinfo.UUIDValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UUIDValue::IsInitialized() const {
  return true;
}

void UUIDValue::InternalSwap(UUIDValue* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(UUIDValue, value1_)
      + sizeof(UUIDValue::value1_)
      - PROTOBUF_FIELD_OFFSET(UUIDValue, value0_)>(
          reinterpret_cast<char*>(&value0_),
          reinterpret_cast<char*>(&other->value0_));
}

::PROTOBUF_NAMESPACE_ID::Metadata UUIDValue::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fOHP_5fINFO_2eproto_getter, &descriptor_table_GCU_5fOHP_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fOHP_5fINFO_2eproto[0]);
}

// ===================================================================

class SettlementModuleState::_Internal {
 public:
};

SettlementModuleState::SettlementModuleState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:OHPinfo.SettlementModuleState)
}
SettlementModuleState::SettlementModuleState(const SettlementModuleState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&moduleid_, &from.moduleid_,
    static_cast<size_t>(reinterpret_cast<char*>(&jurisdiction_) -
    reinterpret_cast<char*>(&moduleid_)) + sizeof(jurisdiction_));
  // @@protoc_insertion_point(copy_constructor:OHPinfo.SettlementModuleState)
}

inline void SettlementModuleState::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&moduleid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&jurisdiction_) -
    reinterpret_cast<char*>(&moduleid_)) + sizeof(jurisdiction_));
}

SettlementModuleState::~SettlementModuleState() {
  // @@protoc_insertion_point(destructor:OHPinfo.SettlementModuleState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SettlementModuleState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SettlementModuleState::ArenaDtor(void* object) {
  SettlementModuleState* _this = reinterpret_cast< SettlementModuleState* >(object);
  (void)_this;
}
void SettlementModuleState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SettlementModuleState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SettlementModuleState::Clear() {
// @@protoc_insertion_point(message_clear_start:OHPinfo.SettlementModuleState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&moduleid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&jurisdiction_) -
      reinterpret_cast<char*>(&moduleid_)) + sizeof(jurisdiction_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SettlementModuleState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .OSCinfo.SettlementModuleEnum ModuleID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_moduleid(static_cast<::OSCinfo::SettlementModuleEnum>(val));
        } else goto handle_unusual;
        continue;
      // .OHPinfo.OrderStrategyEnum OffLineStrategy = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_offlinestrategy(static_cast<::OHPinfo::OrderStrategyEnum>(val));
        } else goto handle_unusual;
        continue;
      // .OHPinfo.OrderStrategyEnum NormalStrategy = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_normalstrategy(static_cast<::OHPinfo::OrderStrategyEnum>(val));
        } else goto handle_unusual;
        continue;
      // uint32 RegisterState = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          registerstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 PeriodicCommunication = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          periodiccommunication_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .OHPinfo.OrderJurisdictionEnum Jurisdiction = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_jurisdiction(static_cast<::OHPinfo::OrderJurisdictionEnum>(val));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* SettlementModuleState::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:OHPinfo.SettlementModuleState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .OSCinfo.SettlementModuleEnum ModuleID = 1;
  if (this->_internal_moduleid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_moduleid(), target);
  }

  // .OHPinfo.OrderStrategyEnum OffLineStrategy = 2;
  if (this->_internal_offlinestrategy() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_offlinestrategy(), target);
  }

  // .OHPinfo.OrderStrategyEnum NormalStrategy = 3;
  if (this->_internal_normalstrategy() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_normalstrategy(), target);
  }

  // uint32 RegisterState = 4;
  if (this->_internal_registerstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_registerstate(), target);
  }

  // uint32 PeriodicCommunication = 5;
  if (this->_internal_periodiccommunication() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_periodiccommunication(), target);
  }

  // .OHPinfo.OrderJurisdictionEnum Jurisdiction = 6;
  if (this->_internal_jurisdiction() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      6, this->_internal_jurisdiction(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:OHPinfo.SettlementModuleState)
  return target;
}

size_t SettlementModuleState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:OHPinfo.SettlementModuleState)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .OSCinfo.SettlementModuleEnum ModuleID = 1;
  if (this->_internal_moduleid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_moduleid());
  }

  // .OHPinfo.OrderStrategyEnum OffLineStrategy = 2;
  if (this->_internal_offlinestrategy() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_offlinestrategy());
  }

  // .OHPinfo.OrderStrategyEnum NormalStrategy = 3;
  if (this->_internal_normalstrategy() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_normalstrategy());
  }

  // uint32 RegisterState = 4;
  if (this->_internal_registerstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_registerstate());
  }

  // uint32 PeriodicCommunication = 5;
  if (this->_internal_periodiccommunication() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_periodiccommunication());
  }

  // .OHPinfo.OrderJurisdictionEnum Jurisdiction = 6;
  if (this->_internal_jurisdiction() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_jurisdiction());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SettlementModuleState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SettlementModuleState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SettlementModuleState::GetClassData() const { return &_class_data_; }

void SettlementModuleState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<SettlementModuleState *>(to)->MergeFrom(
      static_cast<const SettlementModuleState &>(from));
}


void SettlementModuleState::MergeFrom(const SettlementModuleState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:OHPinfo.SettlementModuleState)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_moduleid() != 0) {
    _internal_set_moduleid(from._internal_moduleid());
  }
  if (from._internal_offlinestrategy() != 0) {
    _internal_set_offlinestrategy(from._internal_offlinestrategy());
  }
  if (from._internal_normalstrategy() != 0) {
    _internal_set_normalstrategy(from._internal_normalstrategy());
  }
  if (from._internal_registerstate() != 0) {
    _internal_set_registerstate(from._internal_registerstate());
  }
  if (from._internal_periodiccommunication() != 0) {
    _internal_set_periodiccommunication(from._internal_periodiccommunication());
  }
  if (from._internal_jurisdiction() != 0) {
    _internal_set_jurisdiction(from._internal_jurisdiction());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SettlementModuleState::CopyFrom(const SettlementModuleState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:OHPinfo.SettlementModuleState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SettlementModuleState::IsInitialized() const {
  return true;
}

void SettlementModuleState::InternalSwap(SettlementModuleState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SettlementModuleState, jurisdiction_)
      + sizeof(SettlementModuleState::jurisdiction_)
      - PROTOBUF_FIELD_OFFSET(SettlementModuleState, moduleid_)>(
          reinterpret_cast<char*>(&moduleid_),
          reinterpret_cast<char*>(&other->moduleid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SettlementModuleState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fOHP_5fINFO_2eproto_getter, &descriptor_table_GCU_5fOHP_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fOHP_5fINFO_2eproto[1]);
}

// ===================================================================

class OrderRate::_Internal {
 public:
};

OrderRate::OrderRate(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:OHPinfo.OrderRate)
}
OrderRate::OrderRate(const OrderRate& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&runingrate_, &from.runingrate_,
    static_cast<size_t>(reinterpret_cast<char*>(&rateenum_) -
    reinterpret_cast<char*>(&runingrate_)) + sizeof(rateenum_));
  // @@protoc_insertion_point(copy_constructor:OHPinfo.OrderRate)
}

inline void OrderRate::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&runingrate_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&rateenum_) -
    reinterpret_cast<char*>(&runingrate_)) + sizeof(rateenum_));
}

OrderRate::~OrderRate() {
  // @@protoc_insertion_point(destructor:OHPinfo.OrderRate)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void OrderRate::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void OrderRate::ArenaDtor(void* object) {
  OrderRate* _this = reinterpret_cast< OrderRate* >(object);
  (void)_this;
}
void OrderRate::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void OrderRate::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void OrderRate::Clear() {
// @@protoc_insertion_point(message_clear_start:OHPinfo.OrderRate)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&runingrate_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&rateenum_) -
      reinterpret_cast<char*>(&runingrate_)) + sizeof(rateenum_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* OrderRate::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float RuningRate = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          runingrate_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float ServiceRate = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 37)) {
          servicerate_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 RateEnum = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          rateenum_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* OrderRate::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:OHPinfo.OrderRate)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float RuningRate = 3;
  if (!(this->_internal_runingrate() <= 0 && this->_internal_runingrate() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_runingrate(), target);
  }

  // float ServiceRate = 4;
  if (!(this->_internal_servicerate() <= 0 && this->_internal_servicerate() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_servicerate(), target);
  }

  // uint32 RateEnum = 5;
  if (this->_internal_rateenum() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_rateenum(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:OHPinfo.OrderRate)
  return target;
}

size_t OrderRate::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:OHPinfo.OrderRate)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float RuningRate = 3;
  if (!(this->_internal_runingrate() <= 0 && this->_internal_runingrate() >= 0)) {
    total_size += 1 + 4;
  }

  // float ServiceRate = 4;
  if (!(this->_internal_servicerate() <= 0 && this->_internal_servicerate() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 RateEnum = 5;
  if (this->_internal_rateenum() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_rateenum());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData OrderRate::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    OrderRate::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*OrderRate::GetClassData() const { return &_class_data_; }

void OrderRate::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<OrderRate *>(to)->MergeFrom(
      static_cast<const OrderRate &>(from));
}


void OrderRate::MergeFrom(const OrderRate& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:OHPinfo.OrderRate)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from._internal_runingrate() <= 0 && from._internal_runingrate() >= 0)) {
    _internal_set_runingrate(from._internal_runingrate());
  }
  if (!(from._internal_servicerate() <= 0 && from._internal_servicerate() >= 0)) {
    _internal_set_servicerate(from._internal_servicerate());
  }
  if (from._internal_rateenum() != 0) {
    _internal_set_rateenum(from._internal_rateenum());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void OrderRate::CopyFrom(const OrderRate& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:OHPinfo.OrderRate)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OrderRate::IsInitialized() const {
  return true;
}

void OrderRate::InternalSwap(OrderRate* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(OrderRate, rateenum_)
      + sizeof(OrderRate::rateenum_)
      - PROTOBUF_FIELD_OFFSET(OrderRate, runingrate_)>(
          reinterpret_cast<char*>(&runingrate_),
          reinterpret_cast<char*>(&other->runingrate_));
}

::PROTOBUF_NAMESPACE_ID::Metadata OrderRate::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fOHP_5fINFO_2eproto_getter, &descriptor_table_GCU_5fOHP_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fOHP_5fINFO_2eproto[2]);
}

// ===================================================================

class OrderBill::_Internal {
 public:
};

OrderBill::OrderBill(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:OHPinfo.OrderBill)
}
OrderBill::OrderBill(const OrderBill& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  runingbill_ = from.runingbill_;
  // @@protoc_insertion_point(copy_constructor:OHPinfo.OrderBill)
}

inline void OrderBill::SharedCtor() {
runingbill_ = 0;
}

OrderBill::~OrderBill() {
  // @@protoc_insertion_point(destructor:OHPinfo.OrderBill)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void OrderBill::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void OrderBill::ArenaDtor(void* object) {
  OrderBill* _this = reinterpret_cast< OrderBill* >(object);
  (void)_this;
}
void OrderBill::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void OrderBill::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void OrderBill::Clear() {
// @@protoc_insertion_point(message_clear_start:OHPinfo.OrderBill)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  runingbill_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* OrderBill::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float RuningBill = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          runingbill_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* OrderBill::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:OHPinfo.OrderBill)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float RuningBill = 3;
  if (!(this->_internal_runingbill() <= 0 && this->_internal_runingbill() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_runingbill(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:OHPinfo.OrderBill)
  return target;
}

size_t OrderBill::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:OHPinfo.OrderBill)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float RuningBill = 3;
  if (!(this->_internal_runingbill() <= 0 && this->_internal_runingbill() >= 0)) {
    total_size += 1 + 4;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData OrderBill::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    OrderBill::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*OrderBill::GetClassData() const { return &_class_data_; }

void OrderBill::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<OrderBill *>(to)->MergeFrom(
      static_cast<const OrderBill &>(from));
}


void OrderBill::MergeFrom(const OrderBill& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:OHPinfo.OrderBill)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from._internal_runingbill() <= 0 && from._internal_runingbill() >= 0)) {
    _internal_set_runingbill(from._internal_runingbill());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void OrderBill::CopyFrom(const OrderBill& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:OHPinfo.OrderBill)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OrderBill::IsInitialized() const {
  return true;
}

void OrderBill::InternalSwap(OrderBill* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(runingbill_, other->runingbill_);
}

::PROTOBUF_NAMESPACE_ID::Metadata OrderBill::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fOHP_5fINFO_2eproto_getter, &descriptor_table_GCU_5fOHP_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fOHP_5fINFO_2eproto[3]);
}

// ===================================================================

class RuningOrderState::_Internal {
 public:
  static const ::OHPinfo::UUIDValue& orderuuid(const RuningOrderState* msg);
};

const ::OHPinfo::UUIDValue&
RuningOrderState::_Internal::orderuuid(const RuningOrderState* msg) {
  return *msg->orderuuid_;
}
RuningOrderState::RuningOrderState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:OHPinfo.RuningOrderState)
}
RuningOrderState::RuningOrderState(const RuningOrderState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_orderuuid()) {
    orderuuid_ = new ::OHPinfo::UUIDValue(*from.orderuuid_);
  } else {
    orderuuid_ = nullptr;
  }
  ::memcpy(&startmeterreadout_, &from.startmeterreadout_,
    static_cast<size_t>(reinterpret_cast<char*>(&curmeasured_) -
    reinterpret_cast<char*>(&startmeterreadout_)) + sizeof(curmeasured_));
  // @@protoc_insertion_point(copy_constructor:OHPinfo.RuningOrderState)
}

inline void RuningOrderState::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&orderuuid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&curmeasured_) -
    reinterpret_cast<char*>(&orderuuid_)) + sizeof(curmeasured_));
}

RuningOrderState::~RuningOrderState() {
  // @@protoc_insertion_point(destructor:OHPinfo.RuningOrderState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RuningOrderState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete orderuuid_;
}

void RuningOrderState::ArenaDtor(void* object) {
  RuningOrderState* _this = reinterpret_cast< RuningOrderState* >(object);
  (void)_this;
}
void RuningOrderState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RuningOrderState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RuningOrderState::Clear() {
// @@protoc_insertion_point(message_clear_start:OHPinfo.RuningOrderState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && orderuuid_ != nullptr) {
    delete orderuuid_;
  }
  orderuuid_ = nullptr;
  ::memset(&startmeterreadout_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&curmeasured_) -
      reinterpret_cast<char*>(&startmeterreadout_)) + sizeof(curmeasured_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RuningOrderState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .OSCinfo.SettlementModuleEnum ModuleID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_moduleid(static_cast<::OSCinfo::SettlementModuleEnum>(val));
        } else goto handle_unusual;
        continue;
      // .OHPinfo.UUIDValue OrderUUID = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_orderuuid(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // double StartMeterReadOut = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 25)) {
          startmeterreadout_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double NowMeterReadOut = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 33)) {
          nowmeterreadout_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // float volMeasured = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 45)) {
          volmeasured_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float curMeasured = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 53)) {
          curmeasured_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RuningOrderState::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:OHPinfo.RuningOrderState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .OSCinfo.SettlementModuleEnum ModuleID = 1;
  if (this->_internal_moduleid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_moduleid(), target);
  }

  // .OHPinfo.UUIDValue OrderUUID = 2;
  if (this->_internal_has_orderuuid()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::orderuuid(this), target, stream);
  }

  // double StartMeterReadOut = 3;
  if (!(this->_internal_startmeterreadout() <= 0 && this->_internal_startmeterreadout() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_startmeterreadout(), target);
  }

  // double NowMeterReadOut = 4;
  if (!(this->_internal_nowmeterreadout() <= 0 && this->_internal_nowmeterreadout() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(4, this->_internal_nowmeterreadout(), target);
  }

  // float volMeasured = 5;
  if (!(this->_internal_volmeasured() <= 0 && this->_internal_volmeasured() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(5, this->_internal_volmeasured(), target);
  }

  // float curMeasured = 6;
  if (!(this->_internal_curmeasured() <= 0 && this->_internal_curmeasured() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(6, this->_internal_curmeasured(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:OHPinfo.RuningOrderState)
  return target;
}

size_t RuningOrderState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:OHPinfo.RuningOrderState)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .OHPinfo.UUIDValue OrderUUID = 2;
  if (this->_internal_has_orderuuid()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *orderuuid_);
  }

  // double StartMeterReadOut = 3;
  if (!(this->_internal_startmeterreadout() <= 0 && this->_internal_startmeterreadout() >= 0)) {
    total_size += 1 + 8;
  }

  // .OSCinfo.SettlementModuleEnum ModuleID = 1;
  if (this->_internal_moduleid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_moduleid());
  }

  // float volMeasured = 5;
  if (!(this->_internal_volmeasured() <= 0 && this->_internal_volmeasured() >= 0)) {
    total_size += 1 + 4;
  }

  // double NowMeterReadOut = 4;
  if (!(this->_internal_nowmeterreadout() <= 0 && this->_internal_nowmeterreadout() >= 0)) {
    total_size += 1 + 8;
  }

  // float curMeasured = 6;
  if (!(this->_internal_curmeasured() <= 0 && this->_internal_curmeasured() >= 0)) {
    total_size += 1 + 4;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RuningOrderState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RuningOrderState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RuningOrderState::GetClassData() const { return &_class_data_; }

void RuningOrderState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<RuningOrderState *>(to)->MergeFrom(
      static_cast<const RuningOrderState &>(from));
}


void RuningOrderState::MergeFrom(const RuningOrderState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:OHPinfo.RuningOrderState)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_orderuuid()) {
    _internal_mutable_orderuuid()->::OHPinfo::UUIDValue::MergeFrom(from._internal_orderuuid());
  }
  if (!(from._internal_startmeterreadout() <= 0 && from._internal_startmeterreadout() >= 0)) {
    _internal_set_startmeterreadout(from._internal_startmeterreadout());
  }
  if (from._internal_moduleid() != 0) {
    _internal_set_moduleid(from._internal_moduleid());
  }
  if (!(from._internal_volmeasured() <= 0 && from._internal_volmeasured() >= 0)) {
    _internal_set_volmeasured(from._internal_volmeasured());
  }
  if (!(from._internal_nowmeterreadout() <= 0 && from._internal_nowmeterreadout() >= 0)) {
    _internal_set_nowmeterreadout(from._internal_nowmeterreadout());
  }
  if (!(from._internal_curmeasured() <= 0 && from._internal_curmeasured() >= 0)) {
    _internal_set_curmeasured(from._internal_curmeasured());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RuningOrderState::CopyFrom(const RuningOrderState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:OHPinfo.RuningOrderState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RuningOrderState::IsInitialized() const {
  return true;
}

void RuningOrderState::InternalSwap(RuningOrderState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RuningOrderState, curmeasured_)
      + sizeof(RuningOrderState::curmeasured_)
      - PROTOBUF_FIELD_OFFSET(RuningOrderState, orderuuid_)>(
          reinterpret_cast<char*>(&orderuuid_),
          reinterpret_cast<char*>(&other->orderuuid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RuningOrderState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fOHP_5fINFO_2eproto_getter, &descriptor_table_GCU_5fOHP_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fOHP_5fINFO_2eproto[4]);
}

// ===================================================================

class MeterState::_Internal {
 public:
};

MeterState::MeterState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:OHPinfo.MeterState)
}
MeterState::MeterState(const MeterState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&meterwreadout_, &from.meterwreadout_,
    static_cast<size_t>(reinterpret_cast<char*>(&refreshtime_) -
    reinterpret_cast<char*>(&meterwreadout_)) + sizeof(refreshtime_));
  // @@protoc_insertion_point(copy_constructor:OHPinfo.MeterState)
}

inline void MeterState::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&meterwreadout_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&refreshtime_) -
    reinterpret_cast<char*>(&meterwreadout_)) + sizeof(refreshtime_));
}

MeterState::~MeterState() {
  // @@protoc_insertion_point(destructor:OHPinfo.MeterState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MeterState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void MeterState::ArenaDtor(void* object) {
  MeterState* _this = reinterpret_cast< MeterState* >(object);
  (void)_this;
}
void MeterState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MeterState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MeterState::Clear() {
// @@protoc_insertion_point(message_clear_start:OHPinfo.MeterState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&meterwreadout_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&refreshtime_) -
      reinterpret_cast<char*>(&meterwreadout_)) + sizeof(refreshtime_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MeterState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double MeterWReadOut = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 9)) {
          meterwreadout_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double MeterIReadOut = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 17)) {
          meterireadout_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double MeterVReadOut = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 25)) {
          metervreadout_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // uint32 MeterOffLine = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          meteroffline_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 MeterCheck = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          metercheck_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint64 RefreshTime = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          refreshtime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* MeterState::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:OHPinfo.MeterState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double MeterWReadOut = 1;
  if (!(this->_internal_meterwreadout() <= 0 && this->_internal_meterwreadout() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_meterwreadout(), target);
  }

  // double MeterIReadOut = 2;
  if (!(this->_internal_meterireadout() <= 0 && this->_internal_meterireadout() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_meterireadout(), target);
  }

  // double MeterVReadOut = 3;
  if (!(this->_internal_metervreadout() <= 0 && this->_internal_metervreadout() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_metervreadout(), target);
  }

  // uint32 MeterOffLine = 4;
  if (this->_internal_meteroffline() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_meteroffline(), target);
  }

  // uint32 MeterCheck = 5;
  if (this->_internal_metercheck() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_metercheck(), target);
  }

  // uint64 RefreshTime = 6;
  if (this->_internal_refreshtime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(6, this->_internal_refreshtime(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:OHPinfo.MeterState)
  return target;
}

size_t MeterState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:OHPinfo.MeterState)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double MeterWReadOut = 1;
  if (!(this->_internal_meterwreadout() <= 0 && this->_internal_meterwreadout() >= 0)) {
    total_size += 1 + 8;
  }

  // double MeterIReadOut = 2;
  if (!(this->_internal_meterireadout() <= 0 && this->_internal_meterireadout() >= 0)) {
    total_size += 1 + 8;
  }

  // double MeterVReadOut = 3;
  if (!(this->_internal_metervreadout() <= 0 && this->_internal_metervreadout() >= 0)) {
    total_size += 1 + 8;
  }

  // uint32 MeterOffLine = 4;
  if (this->_internal_meteroffline() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_meteroffline());
  }

  // uint32 MeterCheck = 5;
  if (this->_internal_metercheck() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_metercheck());
  }

  // uint64 RefreshTime = 6;
  if (this->_internal_refreshtime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_refreshtime());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MeterState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MeterState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MeterState::GetClassData() const { return &_class_data_; }

void MeterState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<MeterState *>(to)->MergeFrom(
      static_cast<const MeterState &>(from));
}


void MeterState::MergeFrom(const MeterState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:OHPinfo.MeterState)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from._internal_meterwreadout() <= 0 && from._internal_meterwreadout() >= 0)) {
    _internal_set_meterwreadout(from._internal_meterwreadout());
  }
  if (!(from._internal_meterireadout() <= 0 && from._internal_meterireadout() >= 0)) {
    _internal_set_meterireadout(from._internal_meterireadout());
  }
  if (!(from._internal_metervreadout() <= 0 && from._internal_metervreadout() >= 0)) {
    _internal_set_metervreadout(from._internal_metervreadout());
  }
  if (from._internal_meteroffline() != 0) {
    _internal_set_meteroffline(from._internal_meteroffline());
  }
  if (from._internal_metercheck() != 0) {
    _internal_set_metercheck(from._internal_metercheck());
  }
  if (from._internal_refreshtime() != 0) {
    _internal_set_refreshtime(from._internal_refreshtime());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MeterState::CopyFrom(const MeterState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:OHPinfo.MeterState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MeterState::IsInitialized() const {
  return true;
}

void MeterState::InternalSwap(MeterState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(MeterState, refreshtime_)
      + sizeof(MeterState::refreshtime_)
      - PROTOBUF_FIELD_OFFSET(MeterState, meterwreadout_)>(
          reinterpret_cast<char*>(&meterwreadout_),
          reinterpret_cast<char*>(&other->meterwreadout_));
}

::PROTOBUF_NAMESPACE_ID::Metadata MeterState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fOHP_5fINFO_2eproto_getter, &descriptor_table_GCU_5fOHP_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fOHP_5fINFO_2eproto[5]);
}

// ===================================================================

class MeterInfo::_Internal {
 public:
};

MeterInfo::MeterInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:OHPinfo.MeterInfo)
}
MeterInfo::MeterInfo(const MeterInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  metersn_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_metersn().empty()) {
    metersn_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_metersn(), 
      GetArenaForAllocation());
  }
  meterver_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_meterver().empty()) {
    meterver_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_meterver(), 
      GetArenaForAllocation());
  }
  meterid_ = from.meterid_;
  // @@protoc_insertion_point(copy_constructor:OHPinfo.MeterInfo)
}

inline void MeterInfo::SharedCtor() {
metersn_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
meterver_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
meterid_ = 0u;
}

MeterInfo::~MeterInfo() {
  // @@protoc_insertion_point(destructor:OHPinfo.MeterInfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MeterInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  metersn_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  meterver_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void MeterInfo::ArenaDtor(void* object) {
  MeterInfo* _this = reinterpret_cast< MeterInfo* >(object);
  (void)_this;
}
void MeterInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MeterInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MeterInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:OHPinfo.MeterInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  metersn_.ClearToEmpty();
  meterver_.ClearToEmpty();
  meterid_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MeterInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 MeterID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          meterid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string MeterSN = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_metersn();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "OHPinfo.MeterInfo.MeterSN"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string MeterVer = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_meterver();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "OHPinfo.MeterInfo.MeterVer"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* MeterInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:OHPinfo.MeterInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 MeterID = 1;
  if (this->_internal_meterid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_meterid(), target);
  }

  // string MeterSN = 2;
  if (!this->_internal_metersn().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_metersn().data(), static_cast<int>(this->_internal_metersn().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "OHPinfo.MeterInfo.MeterSN");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_metersn(), target);
  }

  // string MeterVer = 3;
  if (!this->_internal_meterver().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_meterver().data(), static_cast<int>(this->_internal_meterver().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "OHPinfo.MeterInfo.MeterVer");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_meterver(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:OHPinfo.MeterInfo)
  return target;
}

size_t MeterInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:OHPinfo.MeterInfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string MeterSN = 2;
  if (!this->_internal_metersn().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_metersn());
  }

  // string MeterVer = 3;
  if (!this->_internal_meterver().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_meterver());
  }

  // uint32 MeterID = 1;
  if (this->_internal_meterid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_meterid());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MeterInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MeterInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MeterInfo::GetClassData() const { return &_class_data_; }

void MeterInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<MeterInfo *>(to)->MergeFrom(
      static_cast<const MeterInfo &>(from));
}


void MeterInfo::MergeFrom(const MeterInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:OHPinfo.MeterInfo)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_metersn().empty()) {
    _internal_set_metersn(from._internal_metersn());
  }
  if (!from._internal_meterver().empty()) {
    _internal_set_meterver(from._internal_meterver());
  }
  if (from._internal_meterid() != 0) {
    _internal_set_meterid(from._internal_meterid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MeterInfo::CopyFrom(const MeterInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:OHPinfo.MeterInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MeterInfo::IsInitialized() const {
  return true;
}

void MeterInfo::InternalSwap(MeterInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &metersn_, GetArenaForAllocation(),
      &other->metersn_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &meterver_, GetArenaForAllocation(),
      &other->meterver_, other->GetArenaForAllocation()
  );
  swap(meterid_, other->meterid_);
}

::PROTOBUF_NAMESPACE_ID::Metadata MeterInfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fOHP_5fINFO_2eproto_getter, &descriptor_table_GCU_5fOHP_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fOHP_5fINFO_2eproto[6]);
}

// ===================================================================

class HistoryOrderList::_Internal {
 public:
  static const ::OHPinfo::UUIDValue& orderuuid(const HistoryOrderList* msg);
};

const ::OHPinfo::UUIDValue&
HistoryOrderList::_Internal::orderuuid(const HistoryOrderList* msg) {
  return *msg->orderuuid_;
}
HistoryOrderList::HistoryOrderList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  runingratelist_(arena),
  runingbilllist_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:OHPinfo.HistoryOrderList)
}
HistoryOrderList::HistoryOrderList(const HistoryOrderList& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      runingratelist_(from.runingratelist_),
      runingbilllist_(from.runingbilllist_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  userid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_userid().empty()) {
    userid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_userid(), 
      GetArenaForAllocation());
  }
  orderid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_orderid().empty()) {
    orderid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_orderid(), 
      GetArenaForAllocation());
  }
  stopreason_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_stopreason().empty()) {
    stopreason_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_stopreason(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_orderuuid()) {
    orderuuid_ = new ::OHPinfo::UUIDValue(*from.orderuuid_);
  } else {
    orderuuid_ = nullptr;
  }
  ::memcpy(&orderstateenum_, &from.orderstateenum_,
    static_cast<size_t>(reinterpret_cast<char*>(&oscreserve1_) -
    reinterpret_cast<char*>(&orderstateenum_)) + sizeof(oscreserve1_));
  // @@protoc_insertion_point(copy_constructor:OHPinfo.HistoryOrderList)
}

inline void HistoryOrderList::SharedCtor() {
userid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
orderid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
stopreason_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&orderuuid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&oscreserve1_) -
    reinterpret_cast<char*>(&orderuuid_)) + sizeof(oscreserve1_));
}

HistoryOrderList::~HistoryOrderList() {
  // @@protoc_insertion_point(destructor:OHPinfo.HistoryOrderList)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void HistoryOrderList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  userid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  orderid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  stopreason_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete orderuuid_;
}

void HistoryOrderList::ArenaDtor(void* object) {
  HistoryOrderList* _this = reinterpret_cast< HistoryOrderList* >(object);
  (void)_this;
}
void HistoryOrderList::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void HistoryOrderList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void HistoryOrderList::Clear() {
// @@protoc_insertion_point(message_clear_start:OHPinfo.HistoryOrderList)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  runingratelist_.Clear();
  runingbilllist_.Clear();
  userid_.ClearToEmpty();
  orderid_.ClearToEmpty();
  stopreason_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && orderuuid_ != nullptr) {
    delete orderuuid_;
  }
  orderuuid_ = nullptr;
  ::memset(&orderstateenum_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&oscreserve1_) -
      reinterpret_cast<char*>(&orderstateenum_)) + sizeof(oscreserve1_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* HistoryOrderList::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .OHPinfo.UUIDValue OrderUUID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_orderuuid(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .OHPinfo.OrderStateEnum OrderStateEnum = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_orderstateenum(static_cast<::OHPinfo::OrderStateEnum>(val));
        } else goto handle_unusual;
        continue;
      // uint32 MeterID = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          meterid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string UserID = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          auto str = _internal_mutable_userid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "OHPinfo.HistoryOrderList.UserID"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string OrderID = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          auto str = _internal_mutable_orderid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "OHPinfo.HistoryOrderList.OrderID"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 RuningRateListSize = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          runingratelistsize_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .OHPinfo.OrderRate RuningRateList = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_runingratelist(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<58>(ptr));
        } else goto handle_unusual;
        continue;
      // uint32 RuningBillListSize = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          runingbilllistsize_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .OHPinfo.OrderBill RuningBillList = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_runingbilllist(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<74>(ptr));
        } else goto handle_unusual;
        continue;
      // uint32 StarTime = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          startime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 StopTime = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          stoptime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // double StartMeterReadOut = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 97)) {
          startmeterreadout_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double StopMeterReadOut = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 105)) {
          stopmeterreadout_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // bytes StopReason = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 114)) {
          auto str = _internal_mutable_stopreason();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 OSCReserve0 = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 120)) {
          oscreserve0_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 OSCReserve1 = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 128)) {
          oscreserve1_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* HistoryOrderList::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:OHPinfo.HistoryOrderList)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .OHPinfo.UUIDValue OrderUUID = 1;
  if (this->_internal_has_orderuuid()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::orderuuid(this), target, stream);
  }

  // .OHPinfo.OrderStateEnum OrderStateEnum = 2;
  if (this->_internal_orderstateenum() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_orderstateenum(), target);
  }

  // uint32 MeterID = 3;
  if (this->_internal_meterid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_meterid(), target);
  }

  // string UserID = 4;
  if (!this->_internal_userid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_userid().data(), static_cast<int>(this->_internal_userid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "OHPinfo.HistoryOrderList.UserID");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_userid(), target);
  }

  // string OrderID = 5;
  if (!this->_internal_orderid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_orderid().data(), static_cast<int>(this->_internal_orderid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "OHPinfo.HistoryOrderList.OrderID");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_orderid(), target);
  }

  // uint32 RuningRateListSize = 6;
  if (this->_internal_runingratelistsize() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_runingratelistsize(), target);
  }

  // repeated .OHPinfo.OrderRate RuningRateList = 7;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_runingratelist_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(7, this->_internal_runingratelist(i), target, stream);
  }

  // uint32 RuningBillListSize = 8;
  if (this->_internal_runingbilllistsize() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_runingbilllistsize(), target);
  }

  // repeated .OHPinfo.OrderBill RuningBillList = 9;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_runingbilllist_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(9, this->_internal_runingbilllist(i), target, stream);
  }

  // uint32 StarTime = 10;
  if (this->_internal_startime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(10, this->_internal_startime(), target);
  }

  // uint32 StopTime = 11;
  if (this->_internal_stoptime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(11, this->_internal_stoptime(), target);
  }

  // double StartMeterReadOut = 12;
  if (!(this->_internal_startmeterreadout() <= 0 && this->_internal_startmeterreadout() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(12, this->_internal_startmeterreadout(), target);
  }

  // double StopMeterReadOut = 13;
  if (!(this->_internal_stopmeterreadout() <= 0 && this->_internal_stopmeterreadout() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(13, this->_internal_stopmeterreadout(), target);
  }

  // bytes StopReason = 14;
  if (!this->_internal_stopreason().empty()) {
    target = stream->WriteBytesMaybeAliased(
        14, this->_internal_stopreason(), target);
  }

  // uint32 OSCReserve0 = 15;
  if (this->_internal_oscreserve0() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(15, this->_internal_oscreserve0(), target);
  }

  // uint32 OSCReserve1 = 16;
  if (this->_internal_oscreserve1() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(16, this->_internal_oscreserve1(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:OHPinfo.HistoryOrderList)
  return target;
}

size_t HistoryOrderList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:OHPinfo.HistoryOrderList)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .OHPinfo.OrderRate RuningRateList = 7;
  total_size += 1UL * this->_internal_runingratelist_size();
  for (const auto& msg : this->runingratelist_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .OHPinfo.OrderBill RuningBillList = 9;
  total_size += 1UL * this->_internal_runingbilllist_size();
  for (const auto& msg : this->runingbilllist_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string UserID = 4;
  if (!this->_internal_userid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_userid());
  }

  // string OrderID = 5;
  if (!this->_internal_orderid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_orderid());
  }

  // bytes StopReason = 14;
  if (!this->_internal_stopreason().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_stopreason());
  }

  // .OHPinfo.UUIDValue OrderUUID = 1;
  if (this->_internal_has_orderuuid()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *orderuuid_);
  }

  // .OHPinfo.OrderStateEnum OrderStateEnum = 2;
  if (this->_internal_orderstateenum() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_orderstateenum());
  }

  // uint32 MeterID = 3;
  if (this->_internal_meterid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_meterid());
  }

  // uint32 RuningRateListSize = 6;
  if (this->_internal_runingratelistsize() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_runingratelistsize());
  }

  // uint32 RuningBillListSize = 8;
  if (this->_internal_runingbilllistsize() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_runingbilllistsize());
  }

  // uint32 StarTime = 10;
  if (this->_internal_startime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_startime());
  }

  // uint32 StopTime = 11;
  if (this->_internal_stoptime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_stoptime());
  }

  // double StartMeterReadOut = 12;
  if (!(this->_internal_startmeterreadout() <= 0 && this->_internal_startmeterreadout() >= 0)) {
    total_size += 1 + 8;
  }

  // double StopMeterReadOut = 13;
  if (!(this->_internal_stopmeterreadout() <= 0 && this->_internal_stopmeterreadout() >= 0)) {
    total_size += 1 + 8;
  }

  // uint32 OSCReserve0 = 15;
  if (this->_internal_oscreserve0() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_oscreserve0());
  }

  // uint32 OSCReserve1 = 16;
  if (this->_internal_oscreserve1() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_oscreserve1());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData HistoryOrderList::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    HistoryOrderList::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*HistoryOrderList::GetClassData() const { return &_class_data_; }

void HistoryOrderList::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<HistoryOrderList *>(to)->MergeFrom(
      static_cast<const HistoryOrderList &>(from));
}


void HistoryOrderList::MergeFrom(const HistoryOrderList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:OHPinfo.HistoryOrderList)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  runingratelist_.MergeFrom(from.runingratelist_);
  runingbilllist_.MergeFrom(from.runingbilllist_);
  if (!from._internal_userid().empty()) {
    _internal_set_userid(from._internal_userid());
  }
  if (!from._internal_orderid().empty()) {
    _internal_set_orderid(from._internal_orderid());
  }
  if (!from._internal_stopreason().empty()) {
    _internal_set_stopreason(from._internal_stopreason());
  }
  if (from._internal_has_orderuuid()) {
    _internal_mutable_orderuuid()->::OHPinfo::UUIDValue::MergeFrom(from._internal_orderuuid());
  }
  if (from._internal_orderstateenum() != 0) {
    _internal_set_orderstateenum(from._internal_orderstateenum());
  }
  if (from._internal_meterid() != 0) {
    _internal_set_meterid(from._internal_meterid());
  }
  if (from._internal_runingratelistsize() != 0) {
    _internal_set_runingratelistsize(from._internal_runingratelistsize());
  }
  if (from._internal_runingbilllistsize() != 0) {
    _internal_set_runingbilllistsize(from._internal_runingbilllistsize());
  }
  if (from._internal_startime() != 0) {
    _internal_set_startime(from._internal_startime());
  }
  if (from._internal_stoptime() != 0) {
    _internal_set_stoptime(from._internal_stoptime());
  }
  if (!(from._internal_startmeterreadout() <= 0 && from._internal_startmeterreadout() >= 0)) {
    _internal_set_startmeterreadout(from._internal_startmeterreadout());
  }
  if (!(from._internal_stopmeterreadout() <= 0 && from._internal_stopmeterreadout() >= 0)) {
    _internal_set_stopmeterreadout(from._internal_stopmeterreadout());
  }
  if (from._internal_oscreserve0() != 0) {
    _internal_set_oscreserve0(from._internal_oscreserve0());
  }
  if (from._internal_oscreserve1() != 0) {
    _internal_set_oscreserve1(from._internal_oscreserve1());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void HistoryOrderList::CopyFrom(const HistoryOrderList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:OHPinfo.HistoryOrderList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HistoryOrderList::IsInitialized() const {
  return true;
}

void HistoryOrderList::InternalSwap(HistoryOrderList* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  runingratelist_.InternalSwap(&other->runingratelist_);
  runingbilllist_.InternalSwap(&other->runingbilllist_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &userid_, GetArenaForAllocation(),
      &other->userid_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &orderid_, GetArenaForAllocation(),
      &other->orderid_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &stopreason_, GetArenaForAllocation(),
      &other->stopreason_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(HistoryOrderList, oscreserve1_)
      + sizeof(HistoryOrderList::oscreserve1_)
      - PROTOBUF_FIELD_OFFSET(HistoryOrderList, orderuuid_)>(
          reinterpret_cast<char*>(&orderuuid_),
          reinterpret_cast<char*>(&other->orderuuid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata HistoryOrderList::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fOHP_5fINFO_2eproto_getter, &descriptor_table_GCU_5fOHP_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fOHP_5fINFO_2eproto[7]);
}

// ===================================================================

class OrderPipelineState::_Internal {
 public:
};

OrderPipelineState::OrderPipelineState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:OHPinfo.OrderPipelineState)
}
OrderPipelineState::OrderPipelineState(const OrderPipelineState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&meterid_, &from.meterid_,
    static_cast<size_t>(reinterpret_cast<char*>(&preempt_) -
    reinterpret_cast<char*>(&meterid_)) + sizeof(preempt_));
  // @@protoc_insertion_point(copy_constructor:OHPinfo.OrderPipelineState)
}

inline void OrderPipelineState::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&meterid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&preempt_) -
    reinterpret_cast<char*>(&meterid_)) + sizeof(preempt_));
}

OrderPipelineState::~OrderPipelineState() {
  // @@protoc_insertion_point(destructor:OHPinfo.OrderPipelineState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void OrderPipelineState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void OrderPipelineState::ArenaDtor(void* object) {
  OrderPipelineState* _this = reinterpret_cast< OrderPipelineState* >(object);
  (void)_this;
}
void OrderPipelineState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void OrderPipelineState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void OrderPipelineState::Clear() {
// @@protoc_insertion_point(message_clear_start:OHPinfo.OrderPipelineState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&meterid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&preempt_) -
      reinterpret_cast<char*>(&meterid_)) + sizeof(preempt_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* OrderPipelineState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 MeterID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          meterid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 ModuleOpen = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          moduleopen_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 LinkState = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          linkstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 OnLineState = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          onlinestate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 LockState = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          lockstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 StartType = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          starttype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 ChargingDuration = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          chargingduration_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .OHPinfo.PipelineStateEnum PipelineState = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_pipelinestate(static_cast<::OHPinfo::PipelineStateEnum>(val));
        } else goto handle_unusual;
        continue;
      // float ChargingPower = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 77)) {
          chargingpower_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // double NowMeterReadOut = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 81)) {
          nowmeterreadout_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // .OSCinfo.SettlementModuleEnum Preempt = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_preempt(static_cast<::OSCinfo::SettlementModuleEnum>(val));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* OrderPipelineState::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:OHPinfo.OrderPipelineState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 MeterID = 1;
  if (this->_internal_meterid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_meterid(), target);
  }

  // uint32 ModuleOpen = 2;
  if (this->_internal_moduleopen() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_moduleopen(), target);
  }

  // uint32 LinkState = 3;
  if (this->_internal_linkstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_linkstate(), target);
  }

  // uint32 OnLineState = 4;
  if (this->_internal_onlinestate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_onlinestate(), target);
  }

  // uint32 LockState = 5;
  if (this->_internal_lockstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_lockstate(), target);
  }

  // uint32 StartType = 6;
  if (this->_internal_starttype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_starttype(), target);
  }

  // uint32 ChargingDuration = 7;
  if (this->_internal_chargingduration() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(7, this->_internal_chargingduration(), target);
  }

  // .OHPinfo.PipelineStateEnum PipelineState = 8;
  if (this->_internal_pipelinestate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      8, this->_internal_pipelinestate(), target);
  }

  // float ChargingPower = 9;
  if (!(this->_internal_chargingpower() <= 0 && this->_internal_chargingpower() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(9, this->_internal_chargingpower(), target);
  }

  // double NowMeterReadOut = 10;
  if (!(this->_internal_nowmeterreadout() <= 0 && this->_internal_nowmeterreadout() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(10, this->_internal_nowmeterreadout(), target);
  }

  // .OSCinfo.SettlementModuleEnum Preempt = 11;
  if (this->_internal_preempt() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      11, this->_internal_preempt(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:OHPinfo.OrderPipelineState)
  return target;
}

size_t OrderPipelineState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:OHPinfo.OrderPipelineState)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 MeterID = 1;
  if (this->_internal_meterid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_meterid());
  }

  // uint32 ModuleOpen = 2;
  if (this->_internal_moduleopen() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_moduleopen());
  }

  // uint32 LinkState = 3;
  if (this->_internal_linkstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_linkstate());
  }

  // uint32 OnLineState = 4;
  if (this->_internal_onlinestate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_onlinestate());
  }

  // uint32 LockState = 5;
  if (this->_internal_lockstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_lockstate());
  }

  // uint32 StartType = 6;
  if (this->_internal_starttype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_starttype());
  }

  // uint32 ChargingDuration = 7;
  if (this->_internal_chargingduration() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chargingduration());
  }

  // .OHPinfo.PipelineStateEnum PipelineState = 8;
  if (this->_internal_pipelinestate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_pipelinestate());
  }

  // double NowMeterReadOut = 10;
  if (!(this->_internal_nowmeterreadout() <= 0 && this->_internal_nowmeterreadout() >= 0)) {
    total_size += 1 + 8;
  }

  // float ChargingPower = 9;
  if (!(this->_internal_chargingpower() <= 0 && this->_internal_chargingpower() >= 0)) {
    total_size += 1 + 4;
  }

  // .OSCinfo.SettlementModuleEnum Preempt = 11;
  if (this->_internal_preempt() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_preempt());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData OrderPipelineState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    OrderPipelineState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*OrderPipelineState::GetClassData() const { return &_class_data_; }

void OrderPipelineState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<OrderPipelineState *>(to)->MergeFrom(
      static_cast<const OrderPipelineState &>(from));
}


void OrderPipelineState::MergeFrom(const OrderPipelineState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:OHPinfo.OrderPipelineState)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_meterid() != 0) {
    _internal_set_meterid(from._internal_meterid());
  }
  if (from._internal_moduleopen() != 0) {
    _internal_set_moduleopen(from._internal_moduleopen());
  }
  if (from._internal_linkstate() != 0) {
    _internal_set_linkstate(from._internal_linkstate());
  }
  if (from._internal_onlinestate() != 0) {
    _internal_set_onlinestate(from._internal_onlinestate());
  }
  if (from._internal_lockstate() != 0) {
    _internal_set_lockstate(from._internal_lockstate());
  }
  if (from._internal_starttype() != 0) {
    _internal_set_starttype(from._internal_starttype());
  }
  if (from._internal_chargingduration() != 0) {
    _internal_set_chargingduration(from._internal_chargingduration());
  }
  if (from._internal_pipelinestate() != 0) {
    _internal_set_pipelinestate(from._internal_pipelinestate());
  }
  if (!(from._internal_nowmeterreadout() <= 0 && from._internal_nowmeterreadout() >= 0)) {
    _internal_set_nowmeterreadout(from._internal_nowmeterreadout());
  }
  if (!(from._internal_chargingpower() <= 0 && from._internal_chargingpower() >= 0)) {
    _internal_set_chargingpower(from._internal_chargingpower());
  }
  if (from._internal_preempt() != 0) {
    _internal_set_preempt(from._internal_preempt());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void OrderPipelineState::CopyFrom(const OrderPipelineState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:OHPinfo.OrderPipelineState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OrderPipelineState::IsInitialized() const {
  return true;
}

void OrderPipelineState::InternalSwap(OrderPipelineState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(OrderPipelineState, preempt_)
      + sizeof(OrderPipelineState::preempt_)
      - PROTOBUF_FIELD_OFFSET(OrderPipelineState, meterid_)>(
          reinterpret_cast<char*>(&meterid_),
          reinterpret_cast<char*>(&other->meterid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata OrderPipelineState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fOHP_5fINFO_2eproto_getter, &descriptor_table_GCU_5fOHP_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fOHP_5fINFO_2eproto[8]);
}

// ===================================================================

class OrderPipelineAns::_Internal {
 public:
  static const ::BMSinfo::BMSHandShake& bmsshakehandsm(const OrderPipelineAns* msg);
  static const ::BMSinfo::BMSVerification& bmsidentifym(const OrderPipelineAns* msg);
  static const ::BMSinfo::BMSConfig& bmsconfigm(const OrderPipelineAns* msg);
  static const ::BMSinfo::BMSCharging& bmschargingm(const OrderPipelineAns* msg);
  static const ::BMSinfo::BMSChargingEnd& bmschargefinishm(const OrderPipelineAns* msg);
  static const ::AllFaultEnum::MatrixStatus& contactorstate(const OrderPipelineAns* msg);
  static const ::BMS2015PlusInfo::bms2015pMsg& bms2015pm(const OrderPipelineAns* msg);
};

const ::BMSinfo::BMSHandShake&
OrderPipelineAns::_Internal::bmsshakehandsm(const OrderPipelineAns* msg) {
  return *msg->bmsshakehandsm_;
}
const ::BMSinfo::BMSVerification&
OrderPipelineAns::_Internal::bmsidentifym(const OrderPipelineAns* msg) {
  return *msg->bmsidentifym_;
}
const ::BMSinfo::BMSConfig&
OrderPipelineAns::_Internal::bmsconfigm(const OrderPipelineAns* msg) {
  return *msg->bmsconfigm_;
}
const ::BMSinfo::BMSCharging&
OrderPipelineAns::_Internal::bmschargingm(const OrderPipelineAns* msg) {
  return *msg->bmschargingm_;
}
const ::BMSinfo::BMSChargingEnd&
OrderPipelineAns::_Internal::bmschargefinishm(const OrderPipelineAns* msg) {
  return *msg->bmschargefinishm_;
}
const ::AllFaultEnum::MatrixStatus&
OrderPipelineAns::_Internal::contactorstate(const OrderPipelineAns* msg) {
  return *msg->contactorstate_;
}
const ::BMS2015PlusInfo::bms2015pMsg&
OrderPipelineAns::_Internal::bms2015pm(const OrderPipelineAns* msg) {
  return *msg->bms2015pm_;
}
void OrderPipelineAns::clear_bmsshakehandsm() {
  if (GetArenaForAllocation() == nullptr && bmsshakehandsm_ != nullptr) {
    delete bmsshakehandsm_;
  }
  bmsshakehandsm_ = nullptr;
}
void OrderPipelineAns::clear_bmsidentifym() {
  if (GetArenaForAllocation() == nullptr && bmsidentifym_ != nullptr) {
    delete bmsidentifym_;
  }
  bmsidentifym_ = nullptr;
}
void OrderPipelineAns::clear_bmsconfigm() {
  if (GetArenaForAllocation() == nullptr && bmsconfigm_ != nullptr) {
    delete bmsconfigm_;
  }
  bmsconfigm_ = nullptr;
}
void OrderPipelineAns::clear_bmschargingm() {
  if (GetArenaForAllocation() == nullptr && bmschargingm_ != nullptr) {
    delete bmschargingm_;
  }
  bmschargingm_ = nullptr;
}
void OrderPipelineAns::clear_bmschargefinishm() {
  if (GetArenaForAllocation() == nullptr && bmschargefinishm_ != nullptr) {
    delete bmschargefinishm_;
  }
  bmschargefinishm_ = nullptr;
}
void OrderPipelineAns::clear_contactorstate() {
  if (GetArenaForAllocation() == nullptr && contactorstate_ != nullptr) {
    delete contactorstate_;
  }
  contactorstate_ = nullptr;
}
void OrderPipelineAns::clear_ohpfault() {
  ohpfault_.Clear();
}
void OrderPipelineAns::clear_mainalarmlist() {
  mainalarmlist_.Clear();
}
void OrderPipelineAns::clear_vcifault() {
  vcifault_.Clear();
}
void OrderPipelineAns::clear_dmcfault() {
  dmcfault_.Clear();
}
void OrderPipelineAns::clear_admoduleplist() {
  admoduleplist_.Clear();
}
void OrderPipelineAns::clear_bms2015pm() {
  if (GetArenaForAllocation() == nullptr && bms2015pm_ != nullptr) {
    delete bms2015pm_;
  }
  bms2015pm_ = nullptr;
}
OrderPipelineAns::OrderPipelineAns(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  ohpfault_(arena),
  mainalarmlist_(arena),
  vcifault_(arena),
  dmcfault_(arena),
  admoduleplist_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:OHPinfo.OrderPipelineAns)
}
OrderPipelineAns::OrderPipelineAns(const OrderPipelineAns& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      ohpfault_(from.ohpfault_),
      mainalarmlist_(from.mainalarmlist_),
      vcifault_(from.vcifault_),
      dmcfault_(from.dmcfault_),
      admoduleplist_(from.admoduleplist_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  metersn_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_metersn().empty()) {
    metersn_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_metersn(), 
      GetArenaForAllocation());
  }
  meterver_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_meterver().empty()) {
    meterver_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_meterver(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_bmsshakehandsm()) {
    bmsshakehandsm_ = new ::BMSinfo::BMSHandShake(*from.bmsshakehandsm_);
  } else {
    bmsshakehandsm_ = nullptr;
  }
  if (from._internal_has_bmsidentifym()) {
    bmsidentifym_ = new ::BMSinfo::BMSVerification(*from.bmsidentifym_);
  } else {
    bmsidentifym_ = nullptr;
  }
  if (from._internal_has_bmsconfigm()) {
    bmsconfigm_ = new ::BMSinfo::BMSConfig(*from.bmsconfigm_);
  } else {
    bmsconfigm_ = nullptr;
  }
  if (from._internal_has_bmschargingm()) {
    bmschargingm_ = new ::BMSinfo::BMSCharging(*from.bmschargingm_);
  } else {
    bmschargingm_ = nullptr;
  }
  if (from._internal_has_bmschargefinishm()) {
    bmschargefinishm_ = new ::BMSinfo::BMSChargingEnd(*from.bmschargefinishm_);
  } else {
    bmschargefinishm_ = nullptr;
  }
  if (from._internal_has_contactorstate()) {
    contactorstate_ = new ::AllFaultEnum::MatrixStatus(*from.contactorstate_);
  } else {
    contactorstate_ = nullptr;
  }
  if (from._internal_has_bms2015pm()) {
    bms2015pm_ = new ::BMS2015PlusInfo::bms2015pMsg(*from.bms2015pm_);
  } else {
    bms2015pm_ = nullptr;
  }
  ::memcpy(&meterid_, &from.meterid_,
    static_cast<size_t>(reinterpret_cast<char*>(&admoduleparamsize_) -
    reinterpret_cast<char*>(&meterid_)) + sizeof(admoduleparamsize_));
  // @@protoc_insertion_point(copy_constructor:OHPinfo.OrderPipelineAns)
}

inline void OrderPipelineAns::SharedCtor() {
metersn_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
meterver_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&bmsshakehandsm_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&admoduleparamsize_) -
    reinterpret_cast<char*>(&bmsshakehandsm_)) + sizeof(admoduleparamsize_));
}

OrderPipelineAns::~OrderPipelineAns() {
  // @@protoc_insertion_point(destructor:OHPinfo.OrderPipelineAns)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void OrderPipelineAns::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  metersn_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  meterver_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete bmsshakehandsm_;
  if (this != internal_default_instance()) delete bmsidentifym_;
  if (this != internal_default_instance()) delete bmsconfigm_;
  if (this != internal_default_instance()) delete bmschargingm_;
  if (this != internal_default_instance()) delete bmschargefinishm_;
  if (this != internal_default_instance()) delete contactorstate_;
  if (this != internal_default_instance()) delete bms2015pm_;
}

void OrderPipelineAns::ArenaDtor(void* object) {
  OrderPipelineAns* _this = reinterpret_cast< OrderPipelineAns* >(object);
  (void)_this;
}
void OrderPipelineAns::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void OrderPipelineAns::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void OrderPipelineAns::Clear() {
// @@protoc_insertion_point(message_clear_start:OHPinfo.OrderPipelineAns)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ohpfault_.Clear();
  mainalarmlist_.Clear();
  vcifault_.Clear();
  dmcfault_.Clear();
  admoduleplist_.Clear();
  metersn_.ClearToEmpty();
  meterver_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && bmsshakehandsm_ != nullptr) {
    delete bmsshakehandsm_;
  }
  bmsshakehandsm_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bmsidentifym_ != nullptr) {
    delete bmsidentifym_;
  }
  bmsidentifym_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bmsconfigm_ != nullptr) {
    delete bmsconfigm_;
  }
  bmsconfigm_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bmschargingm_ != nullptr) {
    delete bmschargingm_;
  }
  bmschargingm_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bmschargefinishm_ != nullptr) {
    delete bmschargefinishm_;
  }
  bmschargefinishm_ = nullptr;
  if (GetArenaForAllocation() == nullptr && contactorstate_ != nullptr) {
    delete contactorstate_;
  }
  contactorstate_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bms2015pm_ != nullptr) {
    delete bms2015pm_;
  }
  bms2015pm_ = nullptr;
  ::memset(&meterid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&admoduleparamsize_) -
      reinterpret_cast<char*>(&meterid_)) + sizeof(admoduleparamsize_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* OrderPipelineAns::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 MeterID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          meterid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .BMSinfo.ChargeState bmsState = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_bmsstate(static_cast<::BMSinfo::ChargeState>(val));
        } else goto handle_unusual;
        continue;
      // .BMSinfo.BMSHandShake BmsShakehandsM = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_bmsshakehandsm(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .BMSinfo.BMSVerification BmsIdentifyM = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_bmsidentifym(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .BMSinfo.BMSConfig BmsConfigM = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_bmsconfigm(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .BMSinfo.BMSCharging BmsChargingM = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_bmschargingm(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .BMSinfo.BMSChargingEnd BmsChargeFinishM = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_bmschargefinishm(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .AllFaultEnum.MatrixStatus ContactorState = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          ptr = ctx->ParseMessage(_internal_mutable_contactorstate(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 OHPFaultSize = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72)) {
          ohpfaultsize_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .AllFaultEnum.OHPFaultState OHPFault = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 82)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_ohpfault(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<82>(ptr));
        } else goto handle_unusual;
        continue;
      // uint32 PMMFaultSize = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          pmmfaultsize_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .AllFaultEnum.PMMFaultState MainAlarmList = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 98)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_mainalarmlist(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<98>(ptr));
        } else goto handle_unusual;
        continue;
      // uint32 VCIFaultSize = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 104)) {
          vcifaultsize_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .AllFaultEnum.VCIFaultState VCIFault = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 114)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_vcifault(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<114>(ptr));
        } else goto handle_unusual;
        continue;
      // uint32 DMCFaultSize = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 120)) {
          dmcfaultsize_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .AllFaultEnum.DMCFaultState DMCFault = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 130)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_dmcfault(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<130>(ptr));
        } else goto handle_unusual;
        continue;
      // uint32 ADModuleParamSize = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 136)) {
          admoduleparamsize_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .AllFaultEnum.ADModuleAlarm ADModulePList = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 146)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_admoduleplist(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<146>(ptr));
        } else goto handle_unusual;
        continue;
      // string MeterSN = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 154)) {
          auto str = _internal_mutable_metersn();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "OHPinfo.OrderPipelineAns.MeterSN"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string MeterVer = 20;
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 162)) {
          auto str = _internal_mutable_meterver();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "OHPinfo.OrderPipelineAns.MeterVer"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .BMS2015PlusInfo.bms2015pMsg bms2015pM = 21;
      case 21:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 170)) {
          ptr = ctx->ParseMessage(_internal_mutable_bms2015pm(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* OrderPipelineAns::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:OHPinfo.OrderPipelineAns)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 MeterID = 1;
  if (this->_internal_meterid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_meterid(), target);
  }

  // .BMSinfo.ChargeState bmsState = 2;
  if (this->_internal_bmsstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_bmsstate(), target);
  }

  // .BMSinfo.BMSHandShake BmsShakehandsM = 3;
  if (this->_internal_has_bmsshakehandsm()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::bmsshakehandsm(this), target, stream);
  }

  // .BMSinfo.BMSVerification BmsIdentifyM = 4;
  if (this->_internal_has_bmsidentifym()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::bmsidentifym(this), target, stream);
  }

  // .BMSinfo.BMSConfig BmsConfigM = 5;
  if (this->_internal_has_bmsconfigm()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::bmsconfigm(this), target, stream);
  }

  // .BMSinfo.BMSCharging BmsChargingM = 6;
  if (this->_internal_has_bmschargingm()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::bmschargingm(this), target, stream);
  }

  // .BMSinfo.BMSChargingEnd BmsChargeFinishM = 7;
  if (this->_internal_has_bmschargefinishm()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::bmschargefinishm(this), target, stream);
  }

  // .AllFaultEnum.MatrixStatus ContactorState = 8;
  if (this->_internal_has_contactorstate()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        8, _Internal::contactorstate(this), target, stream);
  }

  // uint32 OHPFaultSize = 9;
  if (this->_internal_ohpfaultsize() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(9, this->_internal_ohpfaultsize(), target);
  }

  // repeated .AllFaultEnum.OHPFaultState OHPFault = 10;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_ohpfault_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(10, this->_internal_ohpfault(i), target, stream);
  }

  // uint32 PMMFaultSize = 11;
  if (this->_internal_pmmfaultsize() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(11, this->_internal_pmmfaultsize(), target);
  }

  // repeated .AllFaultEnum.PMMFaultState MainAlarmList = 12;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_mainalarmlist_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(12, this->_internal_mainalarmlist(i), target, stream);
  }

  // uint32 VCIFaultSize = 13;
  if (this->_internal_vcifaultsize() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(13, this->_internal_vcifaultsize(), target);
  }

  // repeated .AllFaultEnum.VCIFaultState VCIFault = 14;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_vcifault_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(14, this->_internal_vcifault(i), target, stream);
  }

  // uint32 DMCFaultSize = 15;
  if (this->_internal_dmcfaultsize() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(15, this->_internal_dmcfaultsize(), target);
  }

  // repeated .AllFaultEnum.DMCFaultState DMCFault = 16;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_dmcfault_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(16, this->_internal_dmcfault(i), target, stream);
  }

  // uint32 ADModuleParamSize = 17;
  if (this->_internal_admoduleparamsize() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(17, this->_internal_admoduleparamsize(), target);
  }

  // repeated .AllFaultEnum.ADModuleAlarm ADModulePList = 18;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_admoduleplist_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(18, this->_internal_admoduleplist(i), target, stream);
  }

  // string MeterSN = 19;
  if (!this->_internal_metersn().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_metersn().data(), static_cast<int>(this->_internal_metersn().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "OHPinfo.OrderPipelineAns.MeterSN");
    target = stream->WriteStringMaybeAliased(
        19, this->_internal_metersn(), target);
  }

  // string MeterVer = 20;
  if (!this->_internal_meterver().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_meterver().data(), static_cast<int>(this->_internal_meterver().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "OHPinfo.OrderPipelineAns.MeterVer");
    target = stream->WriteStringMaybeAliased(
        20, this->_internal_meterver(), target);
  }

  // .BMS2015PlusInfo.bms2015pMsg bms2015pM = 21;
  if (this->_internal_has_bms2015pm()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        21, _Internal::bms2015pm(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:OHPinfo.OrderPipelineAns)
  return target;
}

size_t OrderPipelineAns::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:OHPinfo.OrderPipelineAns)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .AllFaultEnum.OHPFaultState OHPFault = 10;
  total_size += 1UL * this->_internal_ohpfault_size();
  for (const auto& msg : this->ohpfault_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .AllFaultEnum.PMMFaultState MainAlarmList = 12;
  total_size += 1UL * this->_internal_mainalarmlist_size();
  for (const auto& msg : this->mainalarmlist_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .AllFaultEnum.VCIFaultState VCIFault = 14;
  total_size += 1UL * this->_internal_vcifault_size();
  for (const auto& msg : this->vcifault_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .AllFaultEnum.DMCFaultState DMCFault = 16;
  total_size += 2UL * this->_internal_dmcfault_size();
  for (const auto& msg : this->dmcfault_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .AllFaultEnum.ADModuleAlarm ADModulePList = 18;
  total_size += 2UL * this->_internal_admoduleplist_size();
  for (const auto& msg : this->admoduleplist_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string MeterSN = 19;
  if (!this->_internal_metersn().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_metersn());
  }

  // string MeterVer = 20;
  if (!this->_internal_meterver().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_meterver());
  }

  // .BMSinfo.BMSHandShake BmsShakehandsM = 3;
  if (this->_internal_has_bmsshakehandsm()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bmsshakehandsm_);
  }

  // .BMSinfo.BMSVerification BmsIdentifyM = 4;
  if (this->_internal_has_bmsidentifym()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bmsidentifym_);
  }

  // .BMSinfo.BMSConfig BmsConfigM = 5;
  if (this->_internal_has_bmsconfigm()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bmsconfigm_);
  }

  // .BMSinfo.BMSCharging BmsChargingM = 6;
  if (this->_internal_has_bmschargingm()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bmschargingm_);
  }

  // .BMSinfo.BMSChargingEnd BmsChargeFinishM = 7;
  if (this->_internal_has_bmschargefinishm()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bmschargefinishm_);
  }

  // .AllFaultEnum.MatrixStatus ContactorState = 8;
  if (this->_internal_has_contactorstate()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *contactorstate_);
  }

  // .BMS2015PlusInfo.bms2015pMsg bms2015pM = 21;
  if (this->_internal_has_bms2015pm()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bms2015pm_);
  }

  // uint32 MeterID = 1;
  if (this->_internal_meterid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_meterid());
  }

  // .BMSinfo.ChargeState bmsState = 2;
  if (this->_internal_bmsstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_bmsstate());
  }

  // uint32 OHPFaultSize = 9;
  if (this->_internal_ohpfaultsize() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_ohpfaultsize());
  }

  // uint32 PMMFaultSize = 11;
  if (this->_internal_pmmfaultsize() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_pmmfaultsize());
  }

  // uint32 VCIFaultSize = 13;
  if (this->_internal_vcifaultsize() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_vcifaultsize());
  }

  // uint32 DMCFaultSize = 15;
  if (this->_internal_dmcfaultsize() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_dmcfaultsize());
  }

  // uint32 ADModuleParamSize = 17;
  if (this->_internal_admoduleparamsize() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_admoduleparamsize());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData OrderPipelineAns::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    OrderPipelineAns::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*OrderPipelineAns::GetClassData() const { return &_class_data_; }

void OrderPipelineAns::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<OrderPipelineAns *>(to)->MergeFrom(
      static_cast<const OrderPipelineAns &>(from));
}


void OrderPipelineAns::MergeFrom(const OrderPipelineAns& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:OHPinfo.OrderPipelineAns)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  ohpfault_.MergeFrom(from.ohpfault_);
  mainalarmlist_.MergeFrom(from.mainalarmlist_);
  vcifault_.MergeFrom(from.vcifault_);
  dmcfault_.MergeFrom(from.dmcfault_);
  admoduleplist_.MergeFrom(from.admoduleplist_);
  if (!from._internal_metersn().empty()) {
    _internal_set_metersn(from._internal_metersn());
  }
  if (!from._internal_meterver().empty()) {
    _internal_set_meterver(from._internal_meterver());
  }
  if (from._internal_has_bmsshakehandsm()) {
    _internal_mutable_bmsshakehandsm()->::BMSinfo::BMSHandShake::MergeFrom(from._internal_bmsshakehandsm());
  }
  if (from._internal_has_bmsidentifym()) {
    _internal_mutable_bmsidentifym()->::BMSinfo::BMSVerification::MergeFrom(from._internal_bmsidentifym());
  }
  if (from._internal_has_bmsconfigm()) {
    _internal_mutable_bmsconfigm()->::BMSinfo::BMSConfig::MergeFrom(from._internal_bmsconfigm());
  }
  if (from._internal_has_bmschargingm()) {
    _internal_mutable_bmschargingm()->::BMSinfo::BMSCharging::MergeFrom(from._internal_bmschargingm());
  }
  if (from._internal_has_bmschargefinishm()) {
    _internal_mutable_bmschargefinishm()->::BMSinfo::BMSChargingEnd::MergeFrom(from._internal_bmschargefinishm());
  }
  if (from._internal_has_contactorstate()) {
    _internal_mutable_contactorstate()->::AllFaultEnum::MatrixStatus::MergeFrom(from._internal_contactorstate());
  }
  if (from._internal_has_bms2015pm()) {
    _internal_mutable_bms2015pm()->::BMS2015PlusInfo::bms2015pMsg::MergeFrom(from._internal_bms2015pm());
  }
  if (from._internal_meterid() != 0) {
    _internal_set_meterid(from._internal_meterid());
  }
  if (from._internal_bmsstate() != 0) {
    _internal_set_bmsstate(from._internal_bmsstate());
  }
  if (from._internal_ohpfaultsize() != 0) {
    _internal_set_ohpfaultsize(from._internal_ohpfaultsize());
  }
  if (from._internal_pmmfaultsize() != 0) {
    _internal_set_pmmfaultsize(from._internal_pmmfaultsize());
  }
  if (from._internal_vcifaultsize() != 0) {
    _internal_set_vcifaultsize(from._internal_vcifaultsize());
  }
  if (from._internal_dmcfaultsize() != 0) {
    _internal_set_dmcfaultsize(from._internal_dmcfaultsize());
  }
  if (from._internal_admoduleparamsize() != 0) {
    _internal_set_admoduleparamsize(from._internal_admoduleparamsize());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void OrderPipelineAns::CopyFrom(const OrderPipelineAns& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:OHPinfo.OrderPipelineAns)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OrderPipelineAns::IsInitialized() const {
  return true;
}

void OrderPipelineAns::InternalSwap(OrderPipelineAns* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ohpfault_.InternalSwap(&other->ohpfault_);
  mainalarmlist_.InternalSwap(&other->mainalarmlist_);
  vcifault_.InternalSwap(&other->vcifault_);
  dmcfault_.InternalSwap(&other->dmcfault_);
  admoduleplist_.InternalSwap(&other->admoduleplist_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &metersn_, GetArenaForAllocation(),
      &other->metersn_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &meterver_, GetArenaForAllocation(),
      &other->meterver_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(OrderPipelineAns, admoduleparamsize_)
      + sizeof(OrderPipelineAns::admoduleparamsize_)
      - PROTOBUF_FIELD_OFFSET(OrderPipelineAns, bmsshakehandsm_)>(
          reinterpret_cast<char*>(&bmsshakehandsm_),
          reinterpret_cast<char*>(&other->bmsshakehandsm_));
}

::PROTOBUF_NAMESPACE_ID::Metadata OrderPipelineAns::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fOHP_5fINFO_2eproto_getter, &descriptor_table_GCU_5fOHP_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fOHP_5fINFO_2eproto[9]);
}

// ===================================================================

class OrderPipelineInfo::_Internal {
 public:
};

OrderPipelineInfo::OrderPipelineInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:OHPinfo.OrderPipelineInfo)
}
OrderPipelineInfo::OrderPipelineInfo(const OrderPipelineInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  orderid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_orderid().empty()) {
    orderid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_orderid(), 
      GetArenaForAllocation());
  }
  userid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_userid().empty()) {
    userid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_userid(), 
      GetArenaForAllocation());
  }
  ::memcpy(&startsoc_, &from.startsoc_,
    static_cast<size_t>(reinterpret_cast<char*>(&starttime_) -
    reinterpret_cast<char*>(&startsoc_)) + sizeof(starttime_));
  // @@protoc_insertion_point(copy_constructor:OHPinfo.OrderPipelineInfo)
}

inline void OrderPipelineInfo::SharedCtor() {
orderid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
userid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&startsoc_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&starttime_) -
    reinterpret_cast<char*>(&startsoc_)) + sizeof(starttime_));
}

OrderPipelineInfo::~OrderPipelineInfo() {
  // @@protoc_insertion_point(destructor:OHPinfo.OrderPipelineInfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void OrderPipelineInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  orderid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  userid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void OrderPipelineInfo::ArenaDtor(void* object) {
  OrderPipelineInfo* _this = reinterpret_cast< OrderPipelineInfo* >(object);
  (void)_this;
}
void OrderPipelineInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void OrderPipelineInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void OrderPipelineInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:OHPinfo.OrderPipelineInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  orderid_.ClearToEmpty();
  userid_.ClearToEmpty();
  ::memset(&startsoc_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&starttime_) -
      reinterpret_cast<char*>(&startsoc_)) + sizeof(starttime_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* OrderPipelineInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string OrderID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_orderid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "OHPinfo.OrderPipelineInfo.OrderID"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string UserID = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_userid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "OHPinfo.OrderPipelineInfo.UserID"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 StartSoc = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          startsoc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 StartTime = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          starttime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* OrderPipelineInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:OHPinfo.OrderPipelineInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string OrderID = 1;
  if (!this->_internal_orderid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_orderid().data(), static_cast<int>(this->_internal_orderid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "OHPinfo.OrderPipelineInfo.OrderID");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_orderid(), target);
  }

  // string UserID = 2;
  if (!this->_internal_userid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_userid().data(), static_cast<int>(this->_internal_userid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "OHPinfo.OrderPipelineInfo.UserID");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_userid(), target);
  }

  // uint32 StartSoc = 3;
  if (this->_internal_startsoc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_startsoc(), target);
  }

  // uint32 StartTime = 4;
  if (this->_internal_starttime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_starttime(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:OHPinfo.OrderPipelineInfo)
  return target;
}

size_t OrderPipelineInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:OHPinfo.OrderPipelineInfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string OrderID = 1;
  if (!this->_internal_orderid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_orderid());
  }

  // string UserID = 2;
  if (!this->_internal_userid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_userid());
  }

  // uint32 StartSoc = 3;
  if (this->_internal_startsoc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_startsoc());
  }

  // uint32 StartTime = 4;
  if (this->_internal_starttime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_starttime());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData OrderPipelineInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    OrderPipelineInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*OrderPipelineInfo::GetClassData() const { return &_class_data_; }

void OrderPipelineInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<OrderPipelineInfo *>(to)->MergeFrom(
      static_cast<const OrderPipelineInfo &>(from));
}


void OrderPipelineInfo::MergeFrom(const OrderPipelineInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:OHPinfo.OrderPipelineInfo)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_orderid().empty()) {
    _internal_set_orderid(from._internal_orderid());
  }
  if (!from._internal_userid().empty()) {
    _internal_set_userid(from._internal_userid());
  }
  if (from._internal_startsoc() != 0) {
    _internal_set_startsoc(from._internal_startsoc());
  }
  if (from._internal_starttime() != 0) {
    _internal_set_starttime(from._internal_starttime());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void OrderPipelineInfo::CopyFrom(const OrderPipelineInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:OHPinfo.OrderPipelineInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OrderPipelineInfo::IsInitialized() const {
  return true;
}

void OrderPipelineInfo::InternalSwap(OrderPipelineInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &orderid_, GetArenaForAllocation(),
      &other->orderid_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &userid_, GetArenaForAllocation(),
      &other->userid_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(OrderPipelineInfo, starttime_)
      + sizeof(OrderPipelineInfo::starttime_)
      - PROTOBUF_FIELD_OFFSET(OrderPipelineInfo, startsoc_)>(
          reinterpret_cast<char*>(&startsoc_),
          reinterpret_cast<char*>(&other->startsoc_));
}

::PROTOBUF_NAMESPACE_ID::Metadata OrderPipelineInfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fOHP_5fINFO_2eproto_getter, &descriptor_table_GCU_5fOHP_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fOHP_5fINFO_2eproto[10]);
}

// ===================================================================

class hmiConfigInfo::_Internal {
 public:
};

hmiConfigInfo::hmiConfigInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:OHPinfo.hmiConfigInfo)
}
hmiConfigInfo::hmiConfigInfo(const hmiConfigInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&guns_, &from.guns_,
    static_cast<size_t>(reinterpret_cast<char*>(&vlprenable_) -
    reinterpret_cast<char*>(&guns_)) + sizeof(vlprenable_));
  // @@protoc_insertion_point(copy_constructor:OHPinfo.hmiConfigInfo)
}

inline void hmiConfigInfo::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&guns_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&vlprenable_) -
    reinterpret_cast<char*>(&guns_)) + sizeof(vlprenable_));
}

hmiConfigInfo::~hmiConfigInfo() {
  // @@protoc_insertion_point(destructor:OHPinfo.hmiConfigInfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void hmiConfigInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void hmiConfigInfo::ArenaDtor(void* object) {
  hmiConfigInfo* _this = reinterpret_cast< hmiConfigInfo* >(object);
  (void)_this;
}
void hmiConfigInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void hmiConfigInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void hmiConfigInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:OHPinfo.hmiConfigInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&guns_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&vlprenable_) -
      reinterpret_cast<char*>(&guns_)) + sizeof(vlprenable_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* hmiConfigInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 guns = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          guns_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 gunCode = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          guncode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DMCinfo.GunTypeEnum GunType = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_guntype(static_cast<::DMCinfo::GunTypeEnum>(val));
        } else goto handle_unusual;
        continue;
      // uint32 VINType = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          vintype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 AuxType = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          auxtype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 adminMode = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          adminmode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 stopType = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          stoptype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 RateType = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          ratetype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 standbylogoType = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72)) {
          standbylogotype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 ledType = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          ledtype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 historyType = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          historytype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 stopChgSocType = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 96)) {
          stopchgsoctype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 hmiConfigEnable = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 104)) {
          hmiconfigenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 netOfflineWifiEnable = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 112)) {
          netofflinewifienable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 VLPREnable = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 120)) {
          vlprenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* hmiConfigInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:OHPinfo.hmiConfigInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 guns = 1;
  if (this->_internal_guns() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_guns(), target);
  }

  // uint32 gunCode = 2;
  if (this->_internal_guncode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_guncode(), target);
  }

  // .DMCinfo.GunTypeEnum GunType = 3;
  if (this->_internal_guntype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_guntype(), target);
  }

  // uint32 VINType = 4;
  if (this->_internal_vintype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_vintype(), target);
  }

  // uint32 AuxType = 5;
  if (this->_internal_auxtype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_auxtype(), target);
  }

  // uint32 adminMode = 6;
  if (this->_internal_adminmode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_adminmode(), target);
  }

  // uint32 stopType = 7;
  if (this->_internal_stoptype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(7, this->_internal_stoptype(), target);
  }

  // uint32 RateType = 8;
  if (this->_internal_ratetype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_ratetype(), target);
  }

  // uint32 standbylogoType = 9;
  if (this->_internal_standbylogotype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(9, this->_internal_standbylogotype(), target);
  }

  // uint32 ledType = 10;
  if (this->_internal_ledtype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(10, this->_internal_ledtype(), target);
  }

  // uint32 historyType = 11;
  if (this->_internal_historytype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(11, this->_internal_historytype(), target);
  }

  // uint32 stopChgSocType = 12;
  if (this->_internal_stopchgsoctype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(12, this->_internal_stopchgsoctype(), target);
  }

  // uint32 hmiConfigEnable = 13;
  if (this->_internal_hmiconfigenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(13, this->_internal_hmiconfigenable(), target);
  }

  // uint32 netOfflineWifiEnable = 14;
  if (this->_internal_netofflinewifienable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(14, this->_internal_netofflinewifienable(), target);
  }

  // uint32 VLPREnable = 15;
  if (this->_internal_vlprenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(15, this->_internal_vlprenable(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:OHPinfo.hmiConfigInfo)
  return target;
}

size_t hmiConfigInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:OHPinfo.hmiConfigInfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 guns = 1;
  if (this->_internal_guns() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_guns());
  }

  // uint32 gunCode = 2;
  if (this->_internal_guncode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_guncode());
  }

  // .DMCinfo.GunTypeEnum GunType = 3;
  if (this->_internal_guntype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_guntype());
  }

  // uint32 VINType = 4;
  if (this->_internal_vintype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_vintype());
  }

  // uint32 AuxType = 5;
  if (this->_internal_auxtype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_auxtype());
  }

  // uint32 adminMode = 6;
  if (this->_internal_adminmode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_adminmode());
  }

  // uint32 stopType = 7;
  if (this->_internal_stoptype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_stoptype());
  }

  // uint32 RateType = 8;
  if (this->_internal_ratetype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_ratetype());
  }

  // uint32 standbylogoType = 9;
  if (this->_internal_standbylogotype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_standbylogotype());
  }

  // uint32 ledType = 10;
  if (this->_internal_ledtype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_ledtype());
  }

  // uint32 historyType = 11;
  if (this->_internal_historytype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_historytype());
  }

  // uint32 stopChgSocType = 12;
  if (this->_internal_stopchgsoctype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_stopchgsoctype());
  }

  // uint32 hmiConfigEnable = 13;
  if (this->_internal_hmiconfigenable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_hmiconfigenable());
  }

  // uint32 netOfflineWifiEnable = 14;
  if (this->_internal_netofflinewifienable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_netofflinewifienable());
  }

  // uint32 VLPREnable = 15;
  if (this->_internal_vlprenable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_vlprenable());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData hmiConfigInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    hmiConfigInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*hmiConfigInfo::GetClassData() const { return &_class_data_; }

void hmiConfigInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<hmiConfigInfo *>(to)->MergeFrom(
      static_cast<const hmiConfigInfo &>(from));
}


void hmiConfigInfo::MergeFrom(const hmiConfigInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:OHPinfo.hmiConfigInfo)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_guns() != 0) {
    _internal_set_guns(from._internal_guns());
  }
  if (from._internal_guncode() != 0) {
    _internal_set_guncode(from._internal_guncode());
  }
  if (from._internal_guntype() != 0) {
    _internal_set_guntype(from._internal_guntype());
  }
  if (from._internal_vintype() != 0) {
    _internal_set_vintype(from._internal_vintype());
  }
  if (from._internal_auxtype() != 0) {
    _internal_set_auxtype(from._internal_auxtype());
  }
  if (from._internal_adminmode() != 0) {
    _internal_set_adminmode(from._internal_adminmode());
  }
  if (from._internal_stoptype() != 0) {
    _internal_set_stoptype(from._internal_stoptype());
  }
  if (from._internal_ratetype() != 0) {
    _internal_set_ratetype(from._internal_ratetype());
  }
  if (from._internal_standbylogotype() != 0) {
    _internal_set_standbylogotype(from._internal_standbylogotype());
  }
  if (from._internal_ledtype() != 0) {
    _internal_set_ledtype(from._internal_ledtype());
  }
  if (from._internal_historytype() != 0) {
    _internal_set_historytype(from._internal_historytype());
  }
  if (from._internal_stopchgsoctype() != 0) {
    _internal_set_stopchgsoctype(from._internal_stopchgsoctype());
  }
  if (from._internal_hmiconfigenable() != 0) {
    _internal_set_hmiconfigenable(from._internal_hmiconfigenable());
  }
  if (from._internal_netofflinewifienable() != 0) {
    _internal_set_netofflinewifienable(from._internal_netofflinewifienable());
  }
  if (from._internal_vlprenable() != 0) {
    _internal_set_vlprenable(from._internal_vlprenable());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void hmiConfigInfo::CopyFrom(const hmiConfigInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:OHPinfo.hmiConfigInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool hmiConfigInfo::IsInitialized() const {
  return true;
}

void hmiConfigInfo::InternalSwap(hmiConfigInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(hmiConfigInfo, vlprenable_)
      + sizeof(hmiConfigInfo::vlprenable_)
      - PROTOBUF_FIELD_OFFSET(hmiConfigInfo, guns_)>(
          reinterpret_cast<char*>(&guns_),
          reinterpret_cast<char*>(&other->guns_));
}

::PROTOBUF_NAMESPACE_ID::Metadata hmiConfigInfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fOHP_5fINFO_2eproto_getter, &descriptor_table_GCU_5fOHP_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fOHP_5fINFO_2eproto[11]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace OHPinfo
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::OHPinfo::UUIDValue* Arena::CreateMaybeMessage< ::OHPinfo::UUIDValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::OHPinfo::UUIDValue >(arena);
}
template<> PROTOBUF_NOINLINE ::OHPinfo::SettlementModuleState* Arena::CreateMaybeMessage< ::OHPinfo::SettlementModuleState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::OHPinfo::SettlementModuleState >(arena);
}
template<> PROTOBUF_NOINLINE ::OHPinfo::OrderRate* Arena::CreateMaybeMessage< ::OHPinfo::OrderRate >(Arena* arena) {
  return Arena::CreateMessageInternal< ::OHPinfo::OrderRate >(arena);
}
template<> PROTOBUF_NOINLINE ::OHPinfo::OrderBill* Arena::CreateMaybeMessage< ::OHPinfo::OrderBill >(Arena* arena) {
  return Arena::CreateMessageInternal< ::OHPinfo::OrderBill >(arena);
}
template<> PROTOBUF_NOINLINE ::OHPinfo::RuningOrderState* Arena::CreateMaybeMessage< ::OHPinfo::RuningOrderState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::OHPinfo::RuningOrderState >(arena);
}
template<> PROTOBUF_NOINLINE ::OHPinfo::MeterState* Arena::CreateMaybeMessage< ::OHPinfo::MeterState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::OHPinfo::MeterState >(arena);
}
template<> PROTOBUF_NOINLINE ::OHPinfo::MeterInfo* Arena::CreateMaybeMessage< ::OHPinfo::MeterInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::OHPinfo::MeterInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::OHPinfo::HistoryOrderList* Arena::CreateMaybeMessage< ::OHPinfo::HistoryOrderList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::OHPinfo::HistoryOrderList >(arena);
}
template<> PROTOBUF_NOINLINE ::OHPinfo::OrderPipelineState* Arena::CreateMaybeMessage< ::OHPinfo::OrderPipelineState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::OHPinfo::OrderPipelineState >(arena);
}
template<> PROTOBUF_NOINLINE ::OHPinfo::OrderPipelineAns* Arena::CreateMaybeMessage< ::OHPinfo::OrderPipelineAns >(Arena* arena) {
  return Arena::CreateMessageInternal< ::OHPinfo::OrderPipelineAns >(arena);
}
template<> PROTOBUF_NOINLINE ::OHPinfo::OrderPipelineInfo* Arena::CreateMaybeMessage< ::OHPinfo::OrderPipelineInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::OHPinfo::OrderPipelineInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::OHPinfo::hmiConfigInfo* Arena::CreateMaybeMessage< ::OHPinfo::hmiConfigInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::OHPinfo::hmiConfigInfo >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
