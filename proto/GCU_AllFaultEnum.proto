syntax = "proto3";
package AllFaultEnum;

/* 20220312 v0.1
 *统一全局故障:
**/

//VCI故障上报
enum VCIFaultEnum {
    DefaultVCIFault = 0;				//	缺省值

    //BMS故障 13
    gunShortCircuit = 0x01;            // 车辆输出短路故障
    BMSCellVolOver =  0x02;            // 主动防护,电池单体过压
    BMSPackVolOver =  0x03;            // 主动防护,电池包过压(预充时,电池包电压大于BCP最大允许电压)
    BMSPackCurrOver =  0x04;           // 主动防护,过流(输出电流 > BCP最大允许电流)
    BMSCellTempOver =  0x05;           // 主动防护,过温
    BMSTempOutControl =  0x06;         // 主动防护,热失控
    BMSRelayAdhesion =  0x07;          // 主动防护,车辆继电器粘连

    BMSOverCharge =  0x08;             // 主动防护,过充保护
    BMSAuxPowerAbnormal =  0x09;       // 主动防护,辅助电源异常
    BMSInnerRelayOpenCircuit =  0x0a;  // 主动防护,继电器开路
    BMSDemandCurrAbnormal =  0x0b;     // BMS需求电流大于最高允许电流
    BMSDemandVolAbnormal =  0x0c;      // BMS需求电流大于最高允许电压
    BMSBatVolAbnormal = 0x0d;          // 启动充电前输出接触器外侧电压大于50V
    BMSDataUpdateABNORMAL = 0x0e;      // BMS数据不刷新
    BMSBCSCurrABNORMAL = 0x0f;         // BMS电流采样异常

    //Charger 故障 13
    ServiceOffline =  0x10;            // 必要软件服务模块离线
    CC1VoltAbnormal =  0x11;           // CC1电压异常
    GunStateAbnormal =  0x12;          // 控制导引异常
    ElockAbnormal =  0x13;             // 电子锁异常
    GunReserveConnect =  0x14;         // 枪头反接
    AuxPowerAbnormal =  0x15;          // 辅助电源异常
    InsultVolHigh =  0x16;             // 绝缘检测电压高
    InsultVolLow = 0x17;               // 绝缘检测阶段模块电压低
    InsultWarn =  0x18;                // 绝缘检测告警
    InsultAbnormal =  0x19;            // 绝缘检测异常
    DisChgFail =  0x1a;                // 泄放失败
    StartTimeout = 0x1b;               // 启动超时
    BatAllowVolLow =  0x1c;            // BMS最高允许电压小于充电机最小输出电压
    StartVoltHigh = 0x1d;              // 绝缘检测前模块侧电压高
    ElockUnlock = 0x1e;                // 电子锁解锁失败
	    
    //预充 16+4
    BatVolHigh =  0x20;                // 电池电压大于充电机最大电压
    BatVolLow =  0x21;                 // 电池电压小于充电机最小电压
    BatVolDiffFromBCP = 0x22;          // 电池电压与BCP电压不一致
    PreChgVolHigh =  0x23;             // 预充阶段模块电压高
    PreChgVolLow =  0x24;              // 预充阶段模块电压低
    //充电中
    FuseBreakFault =  0x25;            // 熔断器故障
    OutOverCurr =  0x26;               // 输出过流
    OutOverVol = 0x27;                 // 充电电压大于电池包最大允许电压
    MeterCurrAbnormal = 0x28;          // 直流电表电流异常
    MeterVolAbnormal = 0x29;           // 直流电表电压异常
    GunTempWarn =  0x2a;               // 枪头过温告警
    GunOverTemp =  0x2b;               // 枪头过温故障
    NoActiveCurr =  0x2c;              // 无有效电流
    SOCFull =  0x2d;                   // SOC满中止
    ChgForbidTimeout =  0x2e;          // 禁止充电超时
    ChgSysAbnormal =  0x2f;            // 充电机系统故障

    BCSCurrDiff =  0x30;               // 输出电流与BCS报文电流不匹配
    BCSVolDiff =  0x31;                // 输出电压与BCS报文电压不匹配
    //结束充电
    GunNotAtHome = 0x32;               // 充电枪归位告警
    GunLifeWarn = 0x33;                // 枪插拔次数告警
    VINStartTimeOut = 0x34;            // VIN启动超时 
    BATVOLsimpleABNORMAL = 0x35;       // 电池电压采样异常
    OutOverVOLT = 0x36;                // 输出过压
    OutLowVOLT = 0x37;                 // 输出欠压
    OutCurrHIGH = 0x38;                // 输出电流大于需求电流 
	GunTempSensorFault = 0x39;         // 枪头传感器故障
	BmsDemandVolLow = 0x3a;            // BMS需求电压小于充电中最小电压
	InnerCommFault = 0x3b;             // 内部通信异常
    ElockErrDrawGun = 0x3c;           // 电子锁异常用户拔枪终止
    UserDrawGun = 0x3d;                  // 用户拔枪中止
    BSMTimeout = 0x3e;                    // BSM报文超时
    BSTParaMisMatch = 0x3f;           // BST报充电参数不匹配

    //GB27930协议 16 + 16 +1
    BMSProtoVerUnMatch =  0x40;        // BMS通信协议版本不匹配
    BRMTimeout =  0x41;                // BRM报文接收超时
    BRMAbnormal =  0x42;               // BRM报文数据项异常
    BCPTimeout =  0x43;                // BCP报文接收超时
    BCPAbnormal =  0x44;               // BCP报文数据项异常
    BROTimeout =  0x45;                // BRO报文(0x00)接收超时
    BROaaTimeout =  0x46;              // BRO报文(0xAA)接收超时
    BCLTimeout =  0x47;                // BCL报文接收超时
    BCSTimeout =  0x48;                // BCS报文接收超时
    BSMCellVolLow =  0x49;             // BSM报文中单体动力蓄电池电压过低
    BSMSocHigh =  0x4a;                // BSM报文中SOC过高
    BSMSocLow =  0x4b;                 // BSM报文中SOC过低
    BSMPackOverCurr =  0x4c;           // BSM报文中充电过流
    BSMBatTempHigh =  0x4d;            // BSM报文中动力蓄电池温度过高
    BSMInsultAbnormal =  0x4e;         // BSM报文中动力蓄电池绝缘状态异常
    BSMContactorAbnormal =  0x4f;      // BSM报文中连接器连接状态异常

    BSMCellVolHigh =  0x50;            // BSM报文中单体动力蓄电池电压过高
    BSTTimeout =  0x51;                // BST报文接收超时
    BSTMeetSOC =  0x52;                // BST报达到SOC目标值
    BSTMeetVol =  0x53;                // BST报达到总电压设定值
    BSTMeetCellvol =  0x54;            // BST报达到单体电压设定值
    BSTInsultFault =  0x55;            // BST报BMS绝缘故障
    BSTOutConnectorOverTemp =  0x56;   // BST报BMS输出连接器过温故障
    BSTCommponentOverTemp =  0x57;     // BST报BMS元件,输出连接器故障
    BSTChgConnectorAbnormal =  0x58;   // BST报BMS充电连接器故障
    BSTBatOverTemp =  0x59;            // BST报BMS电池组温度过高故障
    BSTHighVolRelayFault =  0x5a;      // BST报BMS高压继电器故障
    BSTcc2Fault =  0x5b;               // BST报BMS检测点2电压检测故障
    BSTOtherFault =  0x5c;             // BST报BMS其他故障
    BSTOverCurr =  0x5d;               // BST报BMS电流过大
    BSTVolAbnormal =  0x5e;            // BST报BMS电压异常
    BSTNoReason =  0x5f;               // BST填充为0
    BSDTimeout =  0x60;                // BSD报文超时
    DoorSensor =  0x61;                // 门磁故障
    EmergencyStopV =  0x62;            // 急停故障
    ContactorOpenFault= 0x63;          // 输出接触器释放故障
    ContactorCloseFault = 0x64;        // 输出接触器吸合故障
    FANerror = 0x65;                   // 终端风机故障
    PMMlockFault = 0x66;               // PMM互锁故障
    ContactorPEDriver= 0x67;           // 输出接触器正极驱动失效故障
    ContactorNEDriver = 0x68;          // 输出接触器负极驱动失效故障
    ContactorPEStick = 0x69;           // 输出接触器正极粘连故障
    ContactorNEStick = 0x6a;           // 输出接触器负极粘连故障
    CanSendFault = 0x6b;               // CAN发送失败故障
    CanBufferFull = 0x6c;              // CAN总线buffer满
    BMSNotReply  = 0x6d;               // BMS不回复故障
    PMMnoADmodle = 0x6e;               // pmm无模块可用故障
    BMSDemandLow3A = 0x6f;             // 车辆需求电流小于3A故障
    InsultLow700k = 0x70;              // 绝缘检测值小于700K故障
    ADFixing = 0x71;                   // AD校正中采样异常
    ADnoFix = 0x72;                    // 未进行AD校正
    MccbBreakFault = 0x73;             // 塑壳故障
    PreChargeBatVolLow = 0x74;    // 预充阶段电池电压低
    BCSCurrBigBCL = 0x76;               // 车辆采样电流大于车辆需求电流
    BCSCurrLow = 0x77;                 // BMS采样电流小
    BCSCurrHigh = 0x78;                // BMS采样电流大
    CooperDifferTerminal = 0x79;       // 双枪充电应用场景错误（不同终端）
    CooperStepStart = 0x7a;            // 双枪充电应用场景错误（先后启机）
    SOCHighStop = 0x7b;                // SOC高车辆主动中止
    BMSstateErr = 0x7c;                  // BMS初始状态异常（注:收到BSD、BEM）
    BSTNullErr = 0x7d;                     // BST为0异常中止（注:BST= 0，结束SOC < 90, 充电时长小于3min）
    BSTotherFaultStop = 0x7e;          // BST其他故障异常中止（注:BST其他故障，结束SOC < 90, 充电时长小于3min）
    InsultSampErr= 0x7f;               // 绝缘结果不可信 （注:绝缘检测采样值正负偏差过大，采样结果不可信）
    
    Smoke = 0x80;			           // 烟感传感器故障
    Water = 0x81;			           // 水浸传感器故障
    ToppleFall = 0x82;			       // 倾倒传感器故障
    Lightning = 0x83;			       // 防雷传感器故障
    Dustproof = 0x84;			       // 防尘网传感器故障
    CooperGunNumErr = 0x85;     // 双枪同充枪个数异常
    CurrOffsetErr = 0x86;               // 电流偏移量异常 
    BCSCurrOverBCP = 0x87;        // BCS电流大于BCP最大允许电流
    
    LC_OverVolt = 0x90;		               // 液冷过压故障
    LC_LowVolt = 0x91;		      	       // 液冷低压故障
    LC_LowLiquidLevel = 0x92;	               // 液冷低液位故障
    LC_PumpOverCurr = 0x93;	               // 液冷泵过流故障
    LC_PumpLowPress = 0x94;	               // 液冷泵欠压故障
    LC_PumpOverPress = 0x95;	               // 液冷泵过压故障
    LC_PumpOverTemp = 0x96;	               // 液冷泵过温故障(回液进液传感器)
    LC_PumpLockDrotor = 0x97;	               // 液冷泵堵转故障
    LC_PumpCommErr = 0x98;	               // 液冷泵通讯故障
    LC_LowFlow = 0x99;	                       // 液冷低流量故障
    LC_HighFlow = 0x9A;	               	       // 液冷高流量故障
    LC_FanOverCurr = 0x9B;	   	       // 液冷风机过流故障
    LC_DevCommuErr = 0x9C;		       // 液冷离线故障	
    LC_ParamSetErr = 0x9D;                     // 液冷参数配置错误

    CtAppointPlanChange = 0xA0;	      // 充电机预约计划变更
    CtAppointTimeNotCome = 0xA1;      // 充电机预约时间未到
    CtChgLeakage = 0xA2;              // 充电机漏电	
    CtConnectErr = 0xA3;              // 充电机报车辆接口断开
    CtVehiElockOpen = 0xA4;           // 车辆电子锁解锁
    CtVehiS2Open = 0xA5;              // 车辆S2 断开
    CtChgFuncConferTimeout = 0xA6;    // 功能协商超时
    CtChgParaConfigTimeout = 0xA7;    // 参数配置超时
    CtChgAuthenTimeout = 0xA8;        // 鉴权超时
    CtChgAppointTimeout = 0xA9;       // 预约超时
    CtChgSelfCheckTimeout = 0xAA;     // 输出回路自检超时
    CtChgPowerSupplyTimeout = 0xAB;   // 供电模式超时
    CtChgPreChgTimeout = 0xAC;        // 预充超时
    CtChgEnergyTransTimeout = 0xAD;   // 能量传输阶段超时
    CtChgEndTimeout = 0xAE;           // 结束阶段超时
    CtBmsNecessFunLack = 0xAF;        // 车辆必需功能模块缺失

    CtBmsAuthenFunLack = 0xB0;        // 车辆鉴权功能缺失
    CtBmsParaNoMatch  = 0xB1;         // 车辆充电参数不匹配
    CtChgAuthenFail   = 0xB2;         // 充电机鉴权失败
    CtBmsNotAllowAppoint = 0xB3;      // 车辆预约不允许
    CtBmsWakeUpFail = 0xB4;           // 唤醒车辆失败 
    CtPauseTimeout = 0xB5;            // 暂停总时间超限
    CtPauseCntOver = 0xB6;            // 暂停次数超限
    CtPauseRepeat = 0xB7;             // 暂停冲突 


    BtNormalStop = 0xC0;	         // 达到设定中止条件中止
    BtUserStop = 0xC1;	             // 车辆端用户中止
    BtAppointPlanChange = 0xC2;	     // 车辆预约计划变更
    BtInsultFail = 0xC3;	         // 车辆绝缘故障
    BtConnectErr = 0xC4;             // 车辆报接口断开连接
    BtPEOpen = 0xC5;                 // 车辆报PE断线
    BtCC2Err = 0xC6;                 // 车辆报CC2电压异常
    BtCC3Err = 0xC7;                 // 车辆报CC3电压异常
    BtElockErr = 0xC8;               // 车辆报电子锁异常
    BtFaultStop = 0xC9;              // 车辆故障停机
    BtEmergencyStop = 0xCA;          // 车辆紧急停机
    BtS1OpenStop = 0xCB;             // 车辆报S1断开停机
    BtChargerStop = 0xCC;            // 车辆报充电机主动中止
    BtChgFuncConferTimeout = 0xCD;   // 车辆报功能协商超时
    BtChgParaConfigTimeout = 0xCE;   // 车辆报参数配置超时
    BtChgAuthenTimeout = 0xCF;       // 车辆报鉴权超时

    BtChgAppointTimeout = 0xD0;       // 车辆报预约超时
    BtChgSelfCheckTimeout = 0xD1;     // 车辆报输出回路自检超时
    BtChgPowerSupplyTimeout = 0xD2;   // 车辆报供电模式超时
    BtChgPreChgTimeout = 0xD3;        // 车辆报预充超时
    BtChgEnergyTransTimeout = 0xD4;   // 车辆报能量传输阶段超时
    BtChgEndTimeout = 0xD5;           // 车辆报结束阶段超时
    BtNecessFunLack = 0xD6;           // 车辆报必需功能模块缺失
    BtFunConferFail = 0xD7;           // 车辆报功能协商失败
    BtParaNoMatch  = 0xD8;            // 车辆报充电参数不匹配
    BtAuthenFail   = 0xD9;            // 车辆报鉴权失败
    BtNotAllowAppoint = 0xDA;         // 车辆报预约不允许
    BtWakeUpFail = 0xDB;              // 车辆报唤醒失败 
    BtSupplyVolHigh = 0xDC;           // 车辆报供电电压过高
    BtSupplyCurrHigh = 0xDD;          // 车辆报供电电流过大
    BtSupplyVolErr = 0xDE;            // 车辆报供电电压异常
    BtSupplyCurrErr = 0xDF;           // 车辆报供电电流异常

    BtSupplyModSwitchFail = 0xE0;     // 车辆报供电模块投切失败
    BtTransVolHigh = 0xE1;            // 车辆报能量传输阶段电压高
    BtTransCurrHigh = 0xE2;           // 车辆报能量传输阶段电流过大
    BtTransVolErr = 0xE3;             // 车辆报能量传输阶段电压异常
    BtTransCurrErr = 0xE4;            // 车辆报能量传输阶段电流异常
    BtGunOverTemp = 0xE5;             // 车辆报枪座过温
    BtChargerStopFail = 0xE6;         // 车辆报充电机无法中止
    BtPauseTimeout = 0xE7;            // 车辆报暂停总时间超限
    BtPauseCntOver = 0xE8;            // 车辆报暂停次数超限
    BtPauseRepeat = 0xE9;             // 暂停冲突 
    
    BtStageEnsureFail = 0xEA;         // 车辆报阶段确认失败
    BtStageRequestTimeout = 0xEB;     // 车辆报阶段请求报文超时
    StageConfirmTimeout = 0xEC;       // 0x02车辆阶段确认超时 1s
    EvStopTimeout = 0xED;             // 0x03车辆中止报文超时 1s
    EvWakeupTimeout = 0xEE;           // 0x09车辆唤醒报文超时 10s
    FunConferTimeout = 0xEF;          // 0x12车辆功能协商结果报文超时 1s
    EvConfigTimeout = 0xF0;           // 车辆充电参数配置报文超时（0x22 0x24）5s
    EvAuthenTimeout = 0xF1;           // 车辆鉴权结果报文超时(0x33 0x35 0x39) 1s 
    EvAppointMsgTimeout = 0xF2;       // 0x42车辆预约充电信息报文超时 1s
    EvAppointConferTimeout = 0xF3;    // 0x44车辆预约充电确认报文超时 1s

    EvBCLTimeout = 0xF4;              // 0x73车辆充电需求报文超时 5s
    EvPauseTimeout = 0xF5;            // 0x79车辆暂停报文超时 5s
    EvBSDTimeout = 0xF6;              // 0x94车辆统计报文超时
    
}

//PMM故障上报
enum PMMFaultEnum {
	DefaultPMMFault = 0x00;		//	缺省值

	//模块相关
	//监控加工的故障
	HvdcStartFail = 0x01;			//	充电模块开机超时 *
	HvdcGroupFail = 0x02;			//	模块响应配组失败 *
	HvdcOffline = 0x03;			//	模块离线 *
	HvdcStopFail = 0x04;                    //      充电模块关机超时 *

	//40帧故障告警状态
	OutOverVolt = 0x10;				//	输出过压 *
	OverTemp = 0x11;				//	过温 *
	FanFault = 0x12;				//	风扇故障 *
	EEPROMFault = 0x13;				//	模块EEPROM故障（校准失败）
	CANErr = 0x14;					//	模块CAN错误状态
	ACLowVolt = 0x15;				//	模块交流欠压告警 *
	ACLackPhase = 0x16;				//	模块交流缺相告警 *
	SerUnblncdCurr = 0x17;			//	模块严重不均流 *
	IDRepeat = 0x18;				//	模块ID重复 *
	ACOverVolt = 0x19;				//	模块交流过压 *
	PFCProtect = 0x1a;				//	模块PFC保护 *
	SlightUnblncdCurr = 0x1b;		//	模块轻微不均流
	DischgAlarm = 0x1c;				//	模块放电电路故障 *
	SCFault = 0x1d;					//	模块短路锁死告警 *
	InnerComAlarm = 0x1e;			//	模块内部通信异常告警

	//43帧故障告警状态
	ElectGridAbnor = 0x20;			//	电网异常检测预告警
	ModRelayFault = 0x21;			//	模块继电器故障（安规故障）
	OutCaplifeWarn = 0x22;			//	输出电解电容寿命预告警 *
	ACCut = 0x23;					//	ACCUT输入掉电告警 *
	DCOCP = 0x24;					//	DCOCP
	BoardcorrectFail = 0x25;		//	单板校准失败
	DiodeTempWarn = 0x26;			//	DC 二极管散热器温度预告警
	MOSTempWarn = 0x27;				//	DC MOS管散热器温度预告警
	PFCTempWarn = 0x28;				//	PFC散热器温度预告警

	//接触器相关
	NoChgMod = 0x31;					//	无模块可用
	ModStartFail = 0x32;				//	模块开机超时
	OutShortCircuit = 0x33;             //  输出短路
	ContactPosStick = 0x34;			    //	输出接触器正极粘连故障
	ContactNegStick = 0x35;				//	输出接触器负极粘连故障
	ContactPosDiscnct = 0x36;			//	输出接触器正极驱动失效故障
	ContactNegDiscnct = 0x37;			//	输出接触器负极驱动失效故障
	InputACOverVolt = 0x38;             //  输入过压
	InputACLowVolt = 0x39;              //  输入欠压
	InputACLakePhase = 0x3A;            //  输入缺相
	ArrayContactorCmdAbnormal = 0x3B;   //  阵列接触器指令异常
	OutContactorCmdAbnormal   = 0x3C;   //  输出接触器指令异常
	ArrayContactorStick = 0x3D;         //  阵列接触器粘连
	ArrayContactorDiscnct = 0x3E;       //  阵列接触器驱动失效

    EmergencyStopP = 0x40;				//	急停
	GateMagnetP = 0x41;					//	门磁
	ADModuleHybrid = 0x42;				//	模块混插
	SPDFaultP = 0x43;     				//	防雷器故障
 	SmokeSensorP = 0x44;    			//	烟感
 	WaterSensorP = 0x45;    			//	水浸
 	TiltSensorP = 0x46;     			//	倾倒
    InContactorCmdAbnormal   = 0x47;    //  塑壳断路器反馈异常
    ACContactorStick   = 0x48;          //  交流接触器粘连故障
    ACContactorDiscnct   = 0x49;        //  交流接触器驱动失效故障
	Systerm_DC_FanFault = 0x4A;			//	系统直流风机故障     
	Systerm_AC_FanFault = 0x4B;			//  系统交流风机故障 
	Systerm_Meter_Fault = 0x4C;			//  系统电表故障    
	PDUOffline = 0x4D;                  //  PDU离线
    LinkArrayContactorStick = 0x4E;     //  枪关联的阵列接触器粘连  (自动恢复)
    Gun_NoChgModule = 0x4F;             //  枪无模块可用
    

    HvdcAllMissing = 0x50;			    //	系统无可用模块 *
    HvdcOutOfRange = 0x51;			    //	模块地址超范围 *
    HvdcAddrTrample = 0x52;			    //	模块地址重复 *
    HvdcAddrErr = 0x53;				    //	模块地址与槽位不匹配 *
    HvdcAddrNull = 0x54;			    //	模块地址缺失
    SystermConfigFault = 0x55;			    //  系统初始化失败
    MatrixFault = 0x56;                                     //  功率矩阵失效(无法跨枪生长)
    GunLinkModulePowerOffFailed = 0x57;        //        枪关联模块关机失败
    SocketVciOffline = 0x58;                          //  VCI离线
    SocketOhpOffline = 0x59;                        //  OHP离线
    SocketDmcOffline = 0x5A;                       //  DMC离线
    SocketFsmOffline = 0x5B;                        //  FSM离线
    DustproofBlocked   = 0x60;                      //  防尘网堵塞告警

    PMMFaultMax = 0xFF;                             // 最大值，新增故障只能加到这个前边
}

//OHP故障上报
enum OHPFaultEnum {
	DefaultOHPFault = 0x00;				//	缺省值

	//网络相关
	LTEFault = 0x01;					//	蜂窝网络故障 *
	WiFiFault = 0x02;					//	wifi网络故障 *
	GmacFault = 0x03;					//	有线网络故障 *
	NetFault = 0x04;					//	完全网络故障 *
	SettlementCloudFault = 0x05;		//	当前订单云网络故障 *
	SettlementCloudStop = 0x06;			//	当前订单云主动终止 *

	//订单及电表相关
	StartUpTimeOut = 0x10;				//	订单启动超时
	PeriodicCommunicationFault = 0x11;	//	当前订单云周期通讯超时
	OrderSettlementFault = 0x12;		//	订单结算失败
    OrderVinTimeStop = 0x13;            //  vin定时检测停机
	SerialNumberFault = 0x20;			//	桩编码校验失败
	UUIDFault = 0x21;					//	UUID生成异常
	FRAMFault = 0x22;					//	热数据初始化失败
    MeterPowerFault = 0x23;				//	电表电量读数校验异常或故障
	MeterCurrFault = 0x24;				//	电表电流读数校验异常或故障
	MeterOffLine = 0x25;				//	电表离线 *
    MeterVolFault = 0x26;				//	电表电压读数校验异常或故障
    MeterDevStartFail = 0x27;			//	电表设备启动失败
    DBFault = 0x28;                     //  DB写入失败
    MeterPowerForwardJump = 0x29;       //  【0,0】电表电量正向跳变(超过10度)
    MeterPowerReverseJump = 0x2a;       //  【0,0】电表电量反向跳变

	//整机及人机交互相关
	GateMagnet = 0x30;					//	门磁
	EmergencyStop = 0x31;				//	急停
	SPDFault = 0x32;					//	防雷器异常
	SysFanFault = 0x33;					//	系统风机
	SysOverTemperature = 0x34;			//	系统过温
	CtrlOverTemperature = 0x35;			//	监控板过温
	SmokeSensor = 0x36;					//	烟感
	WaterSensor = 0x37;					//	水浸
	TiltSensor = 0x38;					//	倾倒
	HMIOffLine = 0x39;					//	HMI离线
	SOCOffLine = 0x3A;					//	SOC灯板故障
	NFCOffLine = 0x3B;					//	刷卡器故障
	SpeakerFault = 0x3C;				//	speaker故障
	MicphoneFault = 0x3D;				//	micphone故障
	CameraFault = 0x3E;                 //	摄像头故障
    LPROffline = 0x3F;                    //   LPR离线

    FSMnoService = 0x40;               	//	FSM离线
    VCInoService = 0x41;               	//	VCI离线
    PMMnoService = 0x42;               	//	PMM离线
    LCRnoService = 0x43;               	//	LCR离线
    DMCnoService = 0x44;               	//	DMC离线
    LOSnoService = 0x45;               	//	LOS离线
    OscnoService = 0x46;               	//	OSC离线
    OrderStartFault = 0x47;             //	订单启动异常

    // by 2023.8.4 新增
    StartUpCheckOhpEnableFail = 0x60;   // 【1,0】订单启动检查OHP状态失败
    StartUpCheckSysEnableFail = 0x61;   // 【1,0】订单启动检查系统状态失败
    StartUpGetOrderFault = 0x62;        // 【1,0】订单启动数据错误             【report_startCmd写入】
    StartUpFramWriteFail = 0x63;        // 【1,0】订单启动写入FRAM失败         【report_startCmd写入】
    StartUpDbWriteFail = 0x64;          // 【1,0】订单启动写入DB失败           【report_startCmd写入】
    StartTimeOut = 0x65;                // 【1,0】订单启动超时(110s)
    AuthTimeOut = 0x66;                // 【1,0】订单鉴权超时(110s)
    PreStartTimeOut = 0x67;            // 【1,0】订单预启动超时(50s)
    PreStartGetVinFail = 0x68;         // 【1,0】订单预启动获取VIN失败
    AuthFail = 0x69;                    // 【1,0】订单鉴权失败
    AdFixTimeOut = 0x70;                // 【1,0】AD矫正超时(9m)
    DrawGunAbnormal = 0x71;             // 【1,0】异常拔枪
    SystemTimeFault = 0x72;             // 【1,0】检测到系统时间跳变（充电中触发）

    // 启动中检测到故障说明
    StartCheckOSCRateParamFail = 0x81;  // 启动费率自检失败
    StartCheckOSCParamEmpty = 0x82;     // 启动参数自检失败
    StartCheckSysTimeFail = 0x83;       // 设备对时自检失败
    StartSelfErrPipelineState = 0x84;   // 订单管线不允许启机
    StartCheckPipelineStart = 0x85;     // 订单管线已占用(多平台)
    StartCheckIdle = 0x86;              // OHP未插抢启动
    StartCheckFault = 0x87;             // 设备故障无法启机


    // 告警，会向平台展示推送出来，范围：0x90以上
    StartFailNotFoundOrder = 0x90;  // 启动失败扭转时检测不到订单
    FRAMReadFFFFFail = 0x91;        // fram读取的所有订单都是ffff告警
    MultiOscOffline = 0x92;         // 多平台时单个osc的离线
    PanicOhpCreate = 0x93;          // ohp出现panic

}

//DMC故障上报
enum DMCFaultEnum {
	DefaultDMCFault = 0x00;				//	缺省值

	PendingFault = 0x01;				//	挂起停机 *
	DisputeFault = 0x02;				//	纠纷停机 *
}

//故障告警发生状态枚举
enum AlarmStateEnum {
	DefaultAlarm = 0;				//	缺省
	ADModuleNormal = 1;				//	故障告警恢复
	ADModuleFaultNo = 2;			//	故障告警发生,故障
}

//VCI故障上报
message VCIFaultState {
    VCIFaultEnum faultType = 1;         // 故障告警名称枚举值
    AlarmStateEnum faultState = 2;      // 故障告警状态
    uint64 faultRaiseTime = 3;          // 故障告警发生时间
    uint64 faultDownTime = 4;           // 故障告警恢复时间
    uint32 ShutDownType = 5;            // 是否停机标志位
    uint32 ShowType = 6;                // 是否显示标志位
}

//PMM故障上报
message PMMFaultState {
    PMMFaultEnum faultType = 1;         // 故障告警名称枚举值
    AlarmStateEnum faultState = 2;      // 故障告警状态
    uint64 faultRaiseTime = 3;          // 故障告警发生时间
    uint64 faultDownTime = 4;           // 故障告警恢复时间
    uint32 ShutDownType = 5;            // 是否停机标志位
    uint32 ShowType = 6;                // 是否显示标志位
}

//OHP故障上报
message OHPFaultState {
    OHPFaultEnum faultType = 1;         // 故障告警名称枚举值
    AlarmStateEnum faultState = 2;      // 故障告警状态
    uint64 faultRaiseTime = 3;          // 故障告警发生时间
    uint64 faultDownTime = 4;           // 故障告警恢复时间
    uint32 ShutDownType = 5;            // 是否停机标志位
    uint32 ShowType = 6;                // 是否显示标志位
}

//DMC故障上报
message DMCFaultState {
    DMCFaultEnum faultType = 1;         // 故障告警名称枚举值
    AlarmStateEnum faultState = 2;      // 故障告警状态
    uint64 faultRaiseTime = 3;          // 故障告警发生时间
    uint64 faultDownTime = 4;           // 故障告警恢复时间
    uint32 ShutDownType = 5;            // 是否停机标志位
    uint32 ShowType = 6;                // 是否显示标志位
}

//PMM阵列接触器状态描述
message MatrixStatus {
	uint32 MatrixContactorID = 1;					//	编号，0枪视角
	AllFaultEnum.PMMFaultEnum AlarmAnsList = 2;		//	故障告警类型
    uint32 AlarmAttr = 3;                                    //   故障告警属性  0:真实故障  1:虚拟故障
    uint32 realFaultContID = 4;                        //    真实故障编号  1: 正级接触器故障   2:负级接触器故障
}

//PMM模块故障告警描述
message ADModuleAlarm {
	uint32 ADModuleID = 1;				        			//	模块编号/位号
	repeated AllFaultEnum.PMMFaultState ADModuleList = 2;	//	PMM故障告警属性
}

message VCIContactorFaultSend {
	uint32 gunNum = 1;                                                                  //  真实枪号
	repeated AllFaultEnum.VCIFaultState VCIFault = 2;		//	VCI返回故障列表
}

message PMMContactorFaultSend {
	uint32 gunNum = 1;                                                                  //  真实枪号
	repeated AllFaultEnum.PMMFaultState PMMFault = 2;		//	PMM故障告警属性
}

message OHPContactorFaultSend {
	uint32 gunNum = 1;                                                                  //  真实枪号
	repeated AllFaultEnum.OHPFaultState OHPFault = 2;		//	OHP故障列表
}

message DMCContactorFaultSend {
	uint32 gunNum = 1;                                                                         //  真实枪号
	repeated AllFaultEnum.DMCFaultState DMCFault = 2;		//	DMC故障列表
}

