// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GCU_LCR_ALL.proto

#include "GCU_LCR_ALL.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace gcu_lcr {
namespace protobuf {
constexpr LogLoad::LogLoad(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : sentence_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , module_(0u)
  , level_(0u)
  , timestamp_(int64_t{0}){}
struct LogLoadDefaultTypeInternal {
  constexpr LogLoadDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LogLoadDefaultTypeInternal() {}
  union {
    LogLoad _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LogLoadDefaultTypeInternal _LogLoad_default_instance_;
constexpr OrderLogLoad::OrderLogLoad(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : orderid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , meterid_(0u)
  , pipelinestate_(0)
{}
struct OrderLogLoadDefaultTypeInternal {
  constexpr OrderLogLoadDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~OrderLogLoadDefaultTypeInternal() {}
  union {
    OrderLogLoad _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT OrderLogLoadDefaultTypeInternal _OrderLogLoad_default_instance_;
constexpr RecordDateGetBack::RecordDateGetBack(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : recordload_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , recordid_(nullptr)
  , moduleid_(0)
{}
struct RecordDateGetBackDefaultTypeInternal {
  constexpr RecordDateGetBackDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RecordDateGetBackDefaultTypeInternal() {}
  union {
    RecordDateGetBack _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RecordDateGetBackDefaultTypeInternal _RecordDateGetBack_default_instance_;
}  // namespace protobuf
}  // namespace gcu_lcr
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_GCU_5fLCR_5fALL_2eproto[3];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_GCU_5fLCR_5fALL_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_GCU_5fLCR_5fALL_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_GCU_5fLCR_5fALL_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gcu_lcr::protobuf::LogLoad, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gcu_lcr::protobuf::LogLoad, module_),
  PROTOBUF_FIELD_OFFSET(::gcu_lcr::protobuf::LogLoad, level_),
  PROTOBUF_FIELD_OFFSET(::gcu_lcr::protobuf::LogLoad, timestamp_),
  PROTOBUF_FIELD_OFFSET(::gcu_lcr::protobuf::LogLoad, sentence_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gcu_lcr::protobuf::OrderLogLoad, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gcu_lcr::protobuf::OrderLogLoad, meterid_),
  PROTOBUF_FIELD_OFFSET(::gcu_lcr::protobuf::OrderLogLoad, pipelinestate_),
  PROTOBUF_FIELD_OFFSET(::gcu_lcr::protobuf::OrderLogLoad, orderid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gcu_lcr::protobuf::RecordDateGetBack, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gcu_lcr::protobuf::RecordDateGetBack, moduleid_),
  PROTOBUF_FIELD_OFFSET(::gcu_lcr::protobuf::RecordDateGetBack, recordid_),
  PROTOBUF_FIELD_OFFSET(::gcu_lcr::protobuf::RecordDateGetBack, recordload_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::gcu_lcr::protobuf::LogLoad)},
  { 9, -1, sizeof(::gcu_lcr::protobuf::OrderLogLoad)},
  { 17, -1, sizeof(::gcu_lcr::protobuf::RecordDateGetBack)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gcu_lcr::protobuf::_LogLoad_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gcu_lcr::protobuf::_OrderLogLoad_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gcu_lcr::protobuf::_RecordDateGetBack_default_instance_),
};

const char descriptor_table_protodef_GCU_5fLCR_5fALL_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\021GCU_LCR_ALL.proto\022\020gcu_lcr.protobuf\032\022G"
  "CU_OSC_INFO.proto\032\022GCU_OHP_INFO.proto\"M\n"
  "\007LogLoad\022\016\n\006Module\030\001 \001(\r\022\r\n\005Level\030\002 \001(\r\022"
  "\021\n\tTimestamp\030\003 \001(\003\022\020\n\010Sentence\030\004 \001(\014\"c\n\014"
  "OrderLogLoad\022\017\n\007MeterID\030\001 \001(\r\0221\n\rPipelin"
  "eState\030\002 \001(\0162\032.OHPinfo.PipelineStateEnum"
  "\022\017\n\007OrderID\030\003 \001(\t\"~\n\021RecordDateGetBack\022/"
  "\n\010ModuleID\030\001 \001(\0162\035.OSCinfo.SettlementMod"
  "uleEnum\022$\n\010RecordID\030\002 \001(\0132\022.OHPinfo.UUID"
  "Value\022\022\n\nRecordLoad\030\003 \001(\014P\000P\001b\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_GCU_5fLCR_5fALL_2eproto_deps[2] = {
  &::descriptor_table_GCU_5fOHP_5fINFO_2eproto,
  &::descriptor_table_GCU_5fOSC_5fINFO_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_GCU_5fLCR_5fALL_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GCU_5fLCR_5fALL_2eproto = {
  false, false, 397, descriptor_table_protodef_GCU_5fLCR_5fALL_2eproto, "GCU_LCR_ALL.proto", 
  &descriptor_table_GCU_5fLCR_5fALL_2eproto_once, descriptor_table_GCU_5fLCR_5fALL_2eproto_deps, 2, 3,
  schemas, file_default_instances, TableStruct_GCU_5fLCR_5fALL_2eproto::offsets,
  file_level_metadata_GCU_5fLCR_5fALL_2eproto, file_level_enum_descriptors_GCU_5fLCR_5fALL_2eproto, file_level_service_descriptors_GCU_5fLCR_5fALL_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_GCU_5fLCR_5fALL_2eproto_getter() {
  return &descriptor_table_GCU_5fLCR_5fALL_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_GCU_5fLCR_5fALL_2eproto(&descriptor_table_GCU_5fLCR_5fALL_2eproto);
namespace gcu_lcr {
namespace protobuf {

// ===================================================================

class LogLoad::_Internal {
 public:
};

LogLoad::LogLoad(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:gcu_lcr.protobuf.LogLoad)
}
LogLoad::LogLoad(const LogLoad& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  sentence_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_sentence().empty()) {
    sentence_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_sentence(), 
      GetArenaForAllocation());
  }
  ::memcpy(&module_, &from.module_,
    static_cast<size_t>(reinterpret_cast<char*>(&timestamp_) -
    reinterpret_cast<char*>(&module_)) + sizeof(timestamp_));
  // @@protoc_insertion_point(copy_constructor:gcu_lcr.protobuf.LogLoad)
}

inline void LogLoad::SharedCtor() {
sentence_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&module_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&timestamp_) -
    reinterpret_cast<char*>(&module_)) + sizeof(timestamp_));
}

LogLoad::~LogLoad() {
  // @@protoc_insertion_point(destructor:gcu_lcr.protobuf.LogLoad)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LogLoad::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  sentence_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void LogLoad::ArenaDtor(void* object) {
  LogLoad* _this = reinterpret_cast< LogLoad* >(object);
  (void)_this;
}
void LogLoad::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LogLoad::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LogLoad::Clear() {
// @@protoc_insertion_point(message_clear_start:gcu_lcr.protobuf.LogLoad)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  sentence_.ClearToEmpty();
  ::memset(&module_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&timestamp_) -
      reinterpret_cast<char*>(&module_)) + sizeof(timestamp_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LogLoad::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 Module = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          module_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 Level = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          level_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int64 Timestamp = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          timestamp_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes Sentence = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          auto str = _internal_mutable_sentence();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* LogLoad::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gcu_lcr.protobuf.LogLoad)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 Module = 1;
  if (this->_internal_module() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_module(), target);
  }

  // uint32 Level = 2;
  if (this->_internal_level() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_level(), target);
  }

  // int64 Timestamp = 3;
  if (this->_internal_timestamp() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(3, this->_internal_timestamp(), target);
  }

  // bytes Sentence = 4;
  if (!this->_internal_sentence().empty()) {
    target = stream->WriteBytesMaybeAliased(
        4, this->_internal_sentence(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gcu_lcr.protobuf.LogLoad)
  return target;
}

size_t LogLoad::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gcu_lcr.protobuf.LogLoad)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes Sentence = 4;
  if (!this->_internal_sentence().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_sentence());
  }

  // uint32 Module = 1;
  if (this->_internal_module() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_module());
  }

  // uint32 Level = 2;
  if (this->_internal_level() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_level());
  }

  // int64 Timestamp = 3;
  if (this->_internal_timestamp() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
        this->_internal_timestamp());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LogLoad::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LogLoad::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LogLoad::GetClassData() const { return &_class_data_; }

void LogLoad::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<LogLoad *>(to)->MergeFrom(
      static_cast<const LogLoad &>(from));
}


void LogLoad::MergeFrom(const LogLoad& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gcu_lcr.protobuf.LogLoad)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_sentence().empty()) {
    _internal_set_sentence(from._internal_sentence());
  }
  if (from._internal_module() != 0) {
    _internal_set_module(from._internal_module());
  }
  if (from._internal_level() != 0) {
    _internal_set_level(from._internal_level());
  }
  if (from._internal_timestamp() != 0) {
    _internal_set_timestamp(from._internal_timestamp());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LogLoad::CopyFrom(const LogLoad& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gcu_lcr.protobuf.LogLoad)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LogLoad::IsInitialized() const {
  return true;
}

void LogLoad::InternalSwap(LogLoad* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &sentence_, GetArenaForAllocation(),
      &other->sentence_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LogLoad, timestamp_)
      + sizeof(LogLoad::timestamp_)
      - PROTOBUF_FIELD_OFFSET(LogLoad, module_)>(
          reinterpret_cast<char*>(&module_),
          reinterpret_cast<char*>(&other->module_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LogLoad::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fLCR_5fALL_2eproto_getter, &descriptor_table_GCU_5fLCR_5fALL_2eproto_once,
      file_level_metadata_GCU_5fLCR_5fALL_2eproto[0]);
}

// ===================================================================

class OrderLogLoad::_Internal {
 public:
};

OrderLogLoad::OrderLogLoad(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:gcu_lcr.protobuf.OrderLogLoad)
}
OrderLogLoad::OrderLogLoad(const OrderLogLoad& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  orderid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_orderid().empty()) {
    orderid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_orderid(), 
      GetArenaForAllocation());
  }
  ::memcpy(&meterid_, &from.meterid_,
    static_cast<size_t>(reinterpret_cast<char*>(&pipelinestate_) -
    reinterpret_cast<char*>(&meterid_)) + sizeof(pipelinestate_));
  // @@protoc_insertion_point(copy_constructor:gcu_lcr.protobuf.OrderLogLoad)
}

inline void OrderLogLoad::SharedCtor() {
orderid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&meterid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&pipelinestate_) -
    reinterpret_cast<char*>(&meterid_)) + sizeof(pipelinestate_));
}

OrderLogLoad::~OrderLogLoad() {
  // @@protoc_insertion_point(destructor:gcu_lcr.protobuf.OrderLogLoad)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void OrderLogLoad::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  orderid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void OrderLogLoad::ArenaDtor(void* object) {
  OrderLogLoad* _this = reinterpret_cast< OrderLogLoad* >(object);
  (void)_this;
}
void OrderLogLoad::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void OrderLogLoad::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void OrderLogLoad::Clear() {
// @@protoc_insertion_point(message_clear_start:gcu_lcr.protobuf.OrderLogLoad)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  orderid_.ClearToEmpty();
  ::memset(&meterid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&pipelinestate_) -
      reinterpret_cast<char*>(&meterid_)) + sizeof(pipelinestate_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* OrderLogLoad::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 MeterID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          meterid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .OHPinfo.PipelineStateEnum PipelineState = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_pipelinestate(static_cast<::OHPinfo::PipelineStateEnum>(val));
        } else goto handle_unusual;
        continue;
      // string OrderID = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_orderid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "gcu_lcr.protobuf.OrderLogLoad.OrderID"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* OrderLogLoad::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gcu_lcr.protobuf.OrderLogLoad)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 MeterID = 1;
  if (this->_internal_meterid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_meterid(), target);
  }

  // .OHPinfo.PipelineStateEnum PipelineState = 2;
  if (this->_internal_pipelinestate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_pipelinestate(), target);
  }

  // string OrderID = 3;
  if (!this->_internal_orderid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_orderid().data(), static_cast<int>(this->_internal_orderid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "gcu_lcr.protobuf.OrderLogLoad.OrderID");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_orderid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gcu_lcr.protobuf.OrderLogLoad)
  return target;
}

size_t OrderLogLoad::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gcu_lcr.protobuf.OrderLogLoad)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string OrderID = 3;
  if (!this->_internal_orderid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_orderid());
  }

  // uint32 MeterID = 1;
  if (this->_internal_meterid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_meterid());
  }

  // .OHPinfo.PipelineStateEnum PipelineState = 2;
  if (this->_internal_pipelinestate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_pipelinestate());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData OrderLogLoad::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    OrderLogLoad::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*OrderLogLoad::GetClassData() const { return &_class_data_; }

void OrderLogLoad::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<OrderLogLoad *>(to)->MergeFrom(
      static_cast<const OrderLogLoad &>(from));
}


void OrderLogLoad::MergeFrom(const OrderLogLoad& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gcu_lcr.protobuf.OrderLogLoad)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_orderid().empty()) {
    _internal_set_orderid(from._internal_orderid());
  }
  if (from._internal_meterid() != 0) {
    _internal_set_meterid(from._internal_meterid());
  }
  if (from._internal_pipelinestate() != 0) {
    _internal_set_pipelinestate(from._internal_pipelinestate());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void OrderLogLoad::CopyFrom(const OrderLogLoad& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gcu_lcr.protobuf.OrderLogLoad)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OrderLogLoad::IsInitialized() const {
  return true;
}

void OrderLogLoad::InternalSwap(OrderLogLoad* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &orderid_, GetArenaForAllocation(),
      &other->orderid_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(OrderLogLoad, pipelinestate_)
      + sizeof(OrderLogLoad::pipelinestate_)
      - PROTOBUF_FIELD_OFFSET(OrderLogLoad, meterid_)>(
          reinterpret_cast<char*>(&meterid_),
          reinterpret_cast<char*>(&other->meterid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata OrderLogLoad::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fLCR_5fALL_2eproto_getter, &descriptor_table_GCU_5fLCR_5fALL_2eproto_once,
      file_level_metadata_GCU_5fLCR_5fALL_2eproto[1]);
}

// ===================================================================

class RecordDateGetBack::_Internal {
 public:
  static const ::OHPinfo::UUIDValue& recordid(const RecordDateGetBack* msg);
};

const ::OHPinfo::UUIDValue&
RecordDateGetBack::_Internal::recordid(const RecordDateGetBack* msg) {
  return *msg->recordid_;
}
void RecordDateGetBack::clear_recordid() {
  if (GetArenaForAllocation() == nullptr && recordid_ != nullptr) {
    delete recordid_;
  }
  recordid_ = nullptr;
}
RecordDateGetBack::RecordDateGetBack(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:gcu_lcr.protobuf.RecordDateGetBack)
}
RecordDateGetBack::RecordDateGetBack(const RecordDateGetBack& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  recordload_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_recordload().empty()) {
    recordload_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_recordload(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_recordid()) {
    recordid_ = new ::OHPinfo::UUIDValue(*from.recordid_);
  } else {
    recordid_ = nullptr;
  }
  moduleid_ = from.moduleid_;
  // @@protoc_insertion_point(copy_constructor:gcu_lcr.protobuf.RecordDateGetBack)
}

inline void RecordDateGetBack::SharedCtor() {
recordload_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&recordid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&moduleid_) -
    reinterpret_cast<char*>(&recordid_)) + sizeof(moduleid_));
}

RecordDateGetBack::~RecordDateGetBack() {
  // @@protoc_insertion_point(destructor:gcu_lcr.protobuf.RecordDateGetBack)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RecordDateGetBack::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  recordload_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete recordid_;
}

void RecordDateGetBack::ArenaDtor(void* object) {
  RecordDateGetBack* _this = reinterpret_cast< RecordDateGetBack* >(object);
  (void)_this;
}
void RecordDateGetBack::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RecordDateGetBack::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RecordDateGetBack::Clear() {
// @@protoc_insertion_point(message_clear_start:gcu_lcr.protobuf.RecordDateGetBack)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  recordload_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && recordid_ != nullptr) {
    delete recordid_;
  }
  recordid_ = nullptr;
  moduleid_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RecordDateGetBack::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .OSCinfo.SettlementModuleEnum ModuleID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_moduleid(static_cast<::OSCinfo::SettlementModuleEnum>(val));
        } else goto handle_unusual;
        continue;
      // .OHPinfo.UUIDValue RecordID = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_recordid(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes RecordLoad = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_recordload();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RecordDateGetBack::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gcu_lcr.protobuf.RecordDateGetBack)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .OSCinfo.SettlementModuleEnum ModuleID = 1;
  if (this->_internal_moduleid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_moduleid(), target);
  }

  // .OHPinfo.UUIDValue RecordID = 2;
  if (this->_internal_has_recordid()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::recordid(this), target, stream);
  }

  // bytes RecordLoad = 3;
  if (!this->_internal_recordload().empty()) {
    target = stream->WriteBytesMaybeAliased(
        3, this->_internal_recordload(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gcu_lcr.protobuf.RecordDateGetBack)
  return target;
}

size_t RecordDateGetBack::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gcu_lcr.protobuf.RecordDateGetBack)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes RecordLoad = 3;
  if (!this->_internal_recordload().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_recordload());
  }

  // .OHPinfo.UUIDValue RecordID = 2;
  if (this->_internal_has_recordid()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *recordid_);
  }

  // .OSCinfo.SettlementModuleEnum ModuleID = 1;
  if (this->_internal_moduleid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_moduleid());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RecordDateGetBack::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RecordDateGetBack::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RecordDateGetBack::GetClassData() const { return &_class_data_; }

void RecordDateGetBack::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<RecordDateGetBack *>(to)->MergeFrom(
      static_cast<const RecordDateGetBack &>(from));
}


void RecordDateGetBack::MergeFrom(const RecordDateGetBack& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gcu_lcr.protobuf.RecordDateGetBack)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_recordload().empty()) {
    _internal_set_recordload(from._internal_recordload());
  }
  if (from._internal_has_recordid()) {
    _internal_mutable_recordid()->::OHPinfo::UUIDValue::MergeFrom(from._internal_recordid());
  }
  if (from._internal_moduleid() != 0) {
    _internal_set_moduleid(from._internal_moduleid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RecordDateGetBack::CopyFrom(const RecordDateGetBack& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gcu_lcr.protobuf.RecordDateGetBack)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RecordDateGetBack::IsInitialized() const {
  return true;
}

void RecordDateGetBack::InternalSwap(RecordDateGetBack* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &recordload_, GetArenaForAllocation(),
      &other->recordload_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RecordDateGetBack, moduleid_)
      + sizeof(RecordDateGetBack::moduleid_)
      - PROTOBUF_FIELD_OFFSET(RecordDateGetBack, recordid_)>(
          reinterpret_cast<char*>(&recordid_),
          reinterpret_cast<char*>(&other->recordid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RecordDateGetBack::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fLCR_5fALL_2eproto_getter, &descriptor_table_GCU_5fLCR_5fALL_2eproto_once,
      file_level_metadata_GCU_5fLCR_5fALL_2eproto[2]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace protobuf
}  // namespace gcu_lcr
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::gcu_lcr::protobuf::LogLoad* Arena::CreateMaybeMessage< ::gcu_lcr::protobuf::LogLoad >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gcu_lcr::protobuf::LogLoad >(arena);
}
template<> PROTOBUF_NOINLINE ::gcu_lcr::protobuf::OrderLogLoad* Arena::CreateMaybeMessage< ::gcu_lcr::protobuf::OrderLogLoad >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gcu_lcr::protobuf::OrderLogLoad >(arena);
}
template<> PROTOBUF_NOINLINE ::gcu_lcr::protobuf::RecordDateGetBack* Arena::CreateMaybeMessage< ::gcu_lcr::protobuf::RecordDateGetBack >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gcu_lcr::protobuf::RecordDateGetBack >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
