syntax = "proto3";
import public "GCU_AllFaultEnum.proto";
import public "GCU_BMS_INFO.proto";
package OSCinfo;

//订单结算通讯模块枚举
enum SettlementModuleEnum {
	UnKownModule = 0x00;			//	未知状态缺省值，非法
	HMC = 0x01;						//	HMC
	LOS = 0x02;						//	本地订单结算模块
	DMC = 0x03;						//	设备管理云订单服务模块
	ICC = 0x04;						//	爱充平台服务模块
	XJC = 0x05;						//	小桔平台服务模块(小桔SDK版本)
	GWC = 0x06;						//	国网及类国网协议平台服务模块(标准对外协议)
	AMP = 0x07;						//	安培平台服务模块
	CJC = 0x08;						//	成都交投平台服务模块
	SHE = 0x09;						//	壳牌平台服务模块
	SXC = 0x0A;						//	三峡平台服务模块
	OCP = 0x0B;						//	OCPP平台服务模块
	SPI = 0x0C;						//	国投平台服务模块
	NDC = 0x0D;						//	宁德交投服务模块
	TED = 0x0E;						//	特来电服务模块
	STC = 0x0F;						//	铁塔服务模块
	XJT = 0x10;						//	小桔平台服务模块(小桔TCU版本)
	RAI = 0x11;						//	杭州智联互充平台
	CNC = 0x12;						//	中核平台
	HKW = 0x13;						//  	海康威视平台
	EOE = 0x14;						//  	天涯行
	GWT = 0x15;						//	国网协议TCU版本
	LPR = 0xF0;                     			//  	LPR摄像头模块
}

//订单结算通讯模块枚举
enum CardTypeEnum {
	UnKownCard = 0x00;				//	未知状态缺省值，非法
	M1 = 0x01;						//	HMC
	CPU = 0x02;						//	HMC
	VIN = 0x03;						//	HMC
}

// 刷卡操作类型枚举
enum OptionTypeEnum {
	ChargeStart = 0x00;				//  启动充电类型
	ChargeStop = 0x01;				//  停止充电类型
	CardUnlock = 0x02;				//  解锁控制类型
	BalanceQuery = 0x03;				//  余额查询类型
}

//操作响应枚举
enum CardOperationEnum {
	UnKownOperation = 0x00;				//	未知状态缺省值
	Doing = 0x01;					//	操作中
	Done = 0x02;					//	操作成功
	CrcFault = 0x03;				//	校验错误
	OpFault = 0x04;					//	操作失败
	LockedCard = 0x05;				//	卡已锁定,请解锁后使用
	SumsFault = 0x06;				//	金额不足
	UnlockedCard = 0x07;				//	卡片未锁定
	WaitUserInput = 0x08;				//	等待用户输入
	QueryNotSupport = 0x09 ;			//	不支持余额查询
	DontAllowStartup = 0x0A;                        //	自检失败，不允许启机
	ConfigError  = 0x0B;				//	配置错误，请检查配置
	AuthentFailed = 0x0C;				//	鉴权不通过

}

// HMC屏幕提示交互控制类型枚举
enum HMCTipsTypeEnum {
	VinAuth = 0x00;					//  VIN鉴权交互
	Ad = 0x01;							 //  AD矫正交互
	OTA = 0x02;						   //  OTA升级交互
	Voice = 0x03;                     //  语音提示交互
	AddOil = 0x04;                  //   液冷终端添加冷却液
}

enum HMCTipsRespEnum {
	UnKownTips = 0x00;				//	未知状态缺省值
	TipsDoing = 0x01;				//  操作中：向平台发起鉴权中
	TipsDone = 0x02;				//	成功：	鉴权成功,等待启机
	VinTimeout = 0x03;				//  操作失败：车端获取VIN超时
	VinFault = 0x04;				//  操作失败：车端获取VIN错误
	PlatAuthTimeout = 0x05;			//  鉴权失败：平台鉴权超时
	PlatAuthSumsFault = 0x06;			//  鉴权失败：余额不足
	PlatAuthExistFault = 0x07;			//  鉴权失败：账户错误
	PlatAuthFault = 0x08;				//  鉴权失败：平台鉴权失败(其他未知原因)
	PlatVinTimeIntercept = 0x09;	// 	鉴权失败：平台vin禁止时间段拦截
	OtaFault = 0x10;				//  操作失败：OTA升级失败
	AdTimeout = 0x20;				//	操作失败：AD校准超时
	ADInsertCanNot = 0x21;			//  操作失败：AD校准时，桩处于插枪态
	ADVciNoInsert = 0x22;			//  操作失败：AD校准时，空闲态下vci没有上报插枪，无法进入校准
	InsertGunAgain = 0x30;			//  语音提示：“枪未插好，请重新插枪”
	NoEntryForOilVehicles = 0x31;		//  语音提示：“充电车位，油车毋入”
	
}

//卡控制信息
message CardMaininfo {
	SettlementModuleEnum ModuleID = 1;		//	通讯模块
	CardTypeEnum CardTypeID = 2;			//	卡类型
	uint32 Sector_Num = 3;					//	扇区
	uint32 Key_Length = 4;					//	密钥长度
	bytes Key = 5;							//	密钥
	uint32 CpuTxSize = 6;					//	Cpu卡发送数据长度
	bytes CpuTxLoad = 7;					//	Cpu卡发送数据负载
	CardOperationEnum Cardstatus = 8;		//	操作状态
	double balance = 9;						//  余额
}

//卡信息
message CardMesginfo {
	SettlementModuleEnum ModuleID = 1;		//	通讯模块
	CardTypeEnum CardTypeID = 2;			//	卡类型
	uint32 SectorSize = 3;					//	扇区数量
	uint32  SectorList = 4;					//	扇区号
	bytes DateLoad = 5;						//	卡信息负载
	uint32 CpuRxSize = 6;					//	Cpu卡接收数据长度
	bytes CpuRxLoad = 7;					//	Cpu卡接收数据负载
	uint32 CardReignFlag = 8;				//	卡意外移除标记,0在位,1移除
	OptionTypeEnum OptType = 9;				//	卡操作类型
}

//结算平台信息
message SettlementServer {
	uint32 CentreIP = 1;				//	中心服务器地址
	uint32 CentrePort = 2;				//	中心服务器端口
	uint32 IP = 3;						//	散列服务器地址
	uint32 Port = 4;					//	散列服务器端口
	string WebURL = 5;					//	URL地址
	string StationNum = 6;				//	平台侧注册桩编码/二维码
	uint32 MultiSettlementPull = 7;		//	是否支持多结算平台拉取 0表示不获取，1获取0x86，2表示获取0x88和0x8c(多平台)
}

//主状态机状态返回描述
message IdleFault {
	uint32 MeterID = 1;										//	当前描述  流水线编号，与电表485总线编号一致(0~5)
	uint32 OHPFaultSize = 2;								//	OHP返回故障实际数量
	repeated AllFaultEnum.OHPFaultState OHPFault = 3;		//	OHP返回故障列表,仅整机故障
	uint32 MainAlarmListSize = 4;							//	PMM发生故障实际数量
	repeated AllFaultEnum.PMMFaultState MainAlarmList = 5;	//	PMM故障告警属性
	uint32 VCIFaultSize = 6;								//	VCI返回故障障实际数量
	repeated AllFaultEnum.VCIFaultState VCIFault = 7;		//	VCI返回故障列表
	uint32 DMCFaultSize = 8;								//	DMC返回故障障实际数量
	repeated AllFaultEnum.DMCFaultState DMCFault= 9;		//	DMC返回故障列表
}
