syntax = "proto3";
package DMCinfo;

//国标协议类型枚举
enum GunProEnum {
	GBT11 = 0x00;					//	新老兼容国标枪
	GBT15 = 0x01;					//	新国标枪
	GB2015P = 0x02;					//	GB2015+
    GB2023A = 0x03;					//	GB2023A
    GB2023B = 0x04;					//	GB2023B
}

//运行模式枚举
enum RunModeEnum {
	operateMode = 0x00;        //  运营模式
	authenMode = 0x01;         //   认证模式
	DCsource = 0x02;			  //	直流源模式
}

//枪类型枚举
enum GunTypeEnum {
	StandGun = 0x00;                                //      标准枪（GB2011,GB2015,GB2023A)
	Chaoji   = 0x01;                                //      超级枪(液冷）
	GBT2023B = 0x02;                                //      国标2023B超级枪
	CCS1     = 0x03;                                //      美标枪
	CHAdeMO  = 0x04;                                //      日标枪
	CCS2     = 0x05;                                //      欧标枪
	Bow      = 0x06;                                //      充电弓
	SCD      = 0x07;                                //      自动充电枪
	FanHP    = 0x08;                                //	风冷大功率（400A）
	GunGB2015p = 0x09;                              //      GB2015+
}


//模块型枚举
enum ADModuleTypeEnum {
	XYBR30KW = 0x00;				//	星愿博瑞30kw
	XYBR40KW = 0x01;				//	星愿博瑞40kw
	YFY20KW = 0x02;					//	英飞源20kw
	YFY30KW = 0x03;					//	英飞源30kw
	YFY40KW = 0x04;					//	英飞源40kw
	megmeet30KW = 0x05;				//	麦格米特30kw
	megmeet40KW = 0x06;				//	麦格米特40kw
	TonHe30KW = 0x07;				//	通合30kw
	TonHe40KW = 0x08;				//	通合40kw
	uugreen30KW = 0x09;				//	优优绿能30kw
	uugreen40KW = 0x0A;				//	优优绿能40kw
}

// 屏幕配置充电策略枚举
enum HmcChargeStrategyEnum{
    hmccs_default = 0x00;        // 缺省
    hmccs_allow = 0x01;          // 允许
    hmccs_off = 0x02;            // 禁止
}
// 屏幕配置充电方式枚举(测试）
enum HmcChargeModeEnum {
    hmccm_default = 0x00;         // 全部
    hmccm_vin = 0x01;             // VIN
    hmccm_card = 0x02;            // 刷卡
}

// 屏幕配置修改项枚举
enum HmcLcrEnum {
	hmclcr_default = 0x00;      // 缺省
	hmclcr_soc = 0x01;			// SOC配置
	hmclcr_station = 0x02;		// 运营平台站地址(包括桩编码，IP及端口)
	hmclcr_termcode= 0x03;		// 终端编号修改
	hmclcr_timingStrategy=0x04;	// 定时充电策略
}

// 模块工作模式枚举
enum HvdcSilenceMode {
	e_nomal = 0x00;      		// 缺省(正常模式)
	e_lowNoise = 0x01;      // 低噪模式
}

message PUBInfo{
	uint32 terminalID = 1;				//	终端ID
	uint32 GunAmount = 2;			//	枪头数量
	GunTypeEnum GunType =3;                //   枪类型
	float sysVolMax = 4;				  //	系统(模块)最大输出电压
	float sysCurMax = 5;				 //	系统(模块)最大输出电流
	float sysCurMin = 6;				   //	系统(模块)最小输出电流        
	float sysMinVolCV = 7;				//	系统(模块)恒压下最小输出电压(CV = constant voltage)
	float sysMinVolCC = 8;				//	系统(模块)恒流下最小输出电压(CC = constant current)
	uint32 ErrorAsWarning = 9;			//	故障降级模式
	uint32 ADFixMode = 10;				   //	是否执行AD矫正
	uint32 gunMatchMode = 11;         //   是否执行电枪匹配
	uint32 lqdCoolOilAddCmd = 12;  //   液冷机加液指令  0:终端加液停止  1:终端加液开始 
	RunModeEnum  runMode = 13;   //   运行模式
	uint32 GunCode =14;						 //    使能枪号(单枪时配置)   0：A枪  1:B枪
	repeated GunMatchInfo gunMachInfo = 15;     // 电枪匹配关系
	uint32 multGunParallMode =16;						   //	多枪直接并联模式  0:未使能（独立，1枪1接触器独立）1:使能（并联,多枪1接触器并联）
}

message VCIInfo{
	uint32 terminalID = 1;						//	终端ID
	uint32 CooperateMode = 2;			//	双枪模式
	uint32 DCFanEnable = 3;					//	直流风机使能 0:未使能 1:使能 默认:使能
	uint32 SmokeEnable = 4;					//	烟感传感器使能 0:未使能 1:使能 默认:不使能
	uint32 ToppleAndFallEnable = 5;		//	倾倒传感器使能 0:未使能 1:使能 默认:不使能
	uint32 WaterEnable = 6;			//	水浸传感器使能 0:未使能 1:使能 默认:不使能
	uint32 LightningEnable = 7;			//	防雷传感器使能 0:未使能 1:使能 默认:不使能
	uint32 DustproofEnable = 8;			//	防尘传感器使能 0:未使能 1:使能 默认:不使能
	uint32 EmergencyStopType = 9;		//	急停模式 (带交接急停逻辑需反转)
	repeated GUNInfo gunInfo = 10;      //  枪配置信息
}

message GUNInfo{
	uint32 gunID = 1;                                      //  枪ID 0:A 1:B
	GunProEnum GbtProtoType = 2;		//	 枪国标协议类型
	uint32 EmeterType = 3;						   //   电表类型,645/698
	uint32 ElockMode = 4;							//	 电子锁工作模式
	uint32 TemperatureMode = 5;			  //    温度传感器类型
	uint32 GunCurrent = 6;							//	 枪线限流
	uint32 MainContactorCurrent = 7;	 //	 主接触器限流
	uint32 gunTempEnable = 8;				  //   枪温传感器使能 0:未使能  1:使能 默认:使能
	uint32 AuxType = 9;								  //	辅源类型
	ActiveProtectParam ActiveProtPara = 10;   //主动防护参数
}

//模块注册参数描述
message ADModuleInfo {
	uint32 ID = 1;					//	模块编号/位号
	float currMax = 2;				//	模块最大输出电流
	float limitPower = 3;				//	模块限功率点
	float volMax = 4;				//	模块最大输出电压
	float volMin = 5;				//	模块限流输出最低电压
	float ratedVol = 6;				//	模块额定/缺省输出电压
	float ratedPower = 7;				//      模块额定输出功率
	float ratedCurr = 8;				//	模块额定输出电流
	float ratedInputVol = 9;			//	模块额定输入电压
	bytes DCModuleSN = 10;				//	直流模块SN码(32位条码)
	bytes softVersion = 11;			//	直流模块软件版本号
	bytes hardVersion = 12;			//	直流模块硬件版本号
}

message PMMInfo{
	uint32 MatrixType = 1;				//	功率矩阵类型
	uint32 MatrixContactorCurrent = 2;		//	阵列接触器限流
	uint32 ContactorAc = 3;			//	交流接触器使能 0:未使能 1:使能  默认:不使能
	uint32 EmergencyStopType = 4;			//	急停模式
	uint32 limitPower = 5;				//	整机限功率
	ADModuleTypeEnum ADModuleType = 6;		//	模块类型
	repeated ADModuleInfo ADModule = 7;		//	模块信息
	uint32 TempEnable = 8;								//	 温度传感器使能 0:未使能  1:使能 默认:不使能
	uint32 ACFanEnable = 9;								//	 交流风机使能 0:未使能 1:使能 默认:使能
	uint32 SmokeEnable = 10;						 //	   烟感传感器使能 0:未使能 1:使能 默认:不使能
	uint32 ToppleAndFallEnable = 11;		  //	倾倒传感器使能 0:未使能 1:使能 默认:不使能
	uint32 WaterEnable = 12;			              //	水浸传感器使能 0:未使能 1:使能 默认:不使能
	uint32 LightningEnable = 13;			       //	 防雷传感器使能 0:未使能 1:使能 默认:不使能
	uint32 BreakerEnable = 14;						//	  塑壳断路器使能 0:未使能 1:使能 默认:不使能
	uint32 DustproofEnable = 15;				 //	   防尘传感器使能 0:未使能 1:使能 默认:不使能
	uint32 ACFanNum = 16;							  //	交流风机数量 1代120:2 1代180:3 1代240/360:6 二代120:3 群充360:6 群充480:8 群充960:14
	HvdcSilenceMode ModuleSilenceMode = 17;     //    设置模块工作模式  e_nomal:正常模式  e_lowNoise:低噪模式
	uint32 PduType = 18;												  //	PDU类型设置      0:通信型扩展PDU    1:IO型内置PDU  (默认为0:通信型扩展PDU)
	uint32 coolingType = 19;                                           //	 冷却方式  0:风冷  1:液冷
	uint32 liquidDeviceType = 20;								  //	液冷设备机型  0:剑湖
	uint32 liquidCoolAddLiquid = 21;						   //	 液冷设备加液   0:停止加液  1:开始加液
}

message ServerInfo {
	uint32 platform = 1;			       //	平台类型及协议编号(如：小桔SDK、爱充、云快充...)
	uint32 qrcode = 2;						 //   二维码开关 0:使能  1:未使能
	uint32 vin = 3;								  //	VIN启机开关
	uint32 card = 4;					  		//    刷卡启动开关 0:未使能  1:使能
	string platName = 5;            	  //    平台名称(多平台) 最多可配置三个汉字
	uint32 wifiofflineChgMode = 6;    //	wifi离网充电模式  0:离网充满  1:离网即停
	string oscProcess = 7;            		//  平台进程名称，如：icc,amp (小写) C83
}

// Platform Server information（多平台）
message OHPInfo {
	uint32 terminalID = 1;				//	终端ID
	uint32 NetType = 2;					//	网络类型,LTE/GMAC/WiFi
	uint32 Vin = 3;							  //	VIN启机开关 0:vin不使能 1:vin使能  2:桩端发起 3:用户发起 
	uint32 AdminMode = 4;				   //	管理员模式
	uint32 AuxVisiable = 5;					  //	辅源控制按钮
	uint32 CooperateMode = 6;			//	双枪模式按钮
	bytes MACip = 7;				//	以太网IP
	bytes WiFiip = 8;				//	WiFi IP
	repeated ServerInfo servers = 9;		//	云服务器信息(by2024.3.7 多平台)
	uint32 OrderHistory = 10;			//	历史订单是否显示  0 关闭 1 打开
	uint32 FaultsHistory =11;           	  //	历史故障显示  0 关闭 1 打开
	uint32 stopType = 12;                        //   屏幕停机方式 
	uint32 RateType =13;                        //    费率类型
	uint32 standbylogoType =14;        //    待机logo类型
	uint32 ledType = 15;                         //    灯板类型  1:圆灯板  2:方灯板
	uint32 stopChgSocType = 16;        //    停止充电soc是否维持 0:不维持 1:维持
    uint32 hmiConfigEnable =17;        //    屏幕配置功能 0:不使能 1:使能
	uint32  netOfflineWifiEnable=18; //  离网wifi充电使能 0:未使能  1:使能
	uint32  VLPREnable = 19;                 //  车牌识别使能   0:未使能  1:使能
	repeated GUNInfo gunInfo = 20;   //  枪配置信息
	uint32 ratedPower = 21;                  //  易充电 额定功率（SP3G1）
	string productModel = 22;              //  易充电 产品型号（SP3G1）
	string ccid = 23;                                   //  SIM卡号（SP3G1）
	uint32 maxCurrent =24;                   //  最大输出电流（SP3G1）
}

// 屏幕配置定时充电时段
message HmcChargingStrategyM {
    HmcChargeStrategyEnum 	chargeStrategy = 1;    // 充电策略
    HmcChargeModeEnum 		chargeStar = 2;        // 充电方式
    uint32 					startTime = 3;         // 开始时间(分钟0-1440)
    uint32 					endTime = 4;           // 结束时间(分钟0-1440)
}

// 屏幕配置信息
message HMCInfo {
	uint32 terminalID = 1;									//	终端ID
    HmcLcrEnum lcrType = 2;									//  配置类型
    string terminalCode =3;                                 //  更改终端编号
    uint32 soc =4;										    //  SOC阈值
    repeated ServerInfo platInfo =5;								    //  运营平台信息
    repeated HmcChargingStrategyM strategy = 6;				//  定时/禁止充电策略
}

message GunLoadConstraint {
	uint32 terminalID = 1;												// 	 所属终端号
	uint32 gunID = 2;                                                  		//   枪ID 0:A 1:B
	repeated uint32 gunNum = 3;                               //   真实枪号
	uint32 GunlimitPower = 4;									   //	枪功率限制
}
//真实枪与BMS枪匹配关系
message GunMatchInfo {
	uint32 gunID = 1;                                                        //    枪ID 0:A 1:B
	repeated uint32 gunNum = 2;                                //    真实枪号
	uint32 isAdjacent =3;												 //		是否相邻枪   0x60填0, 0x62填真实值    0:缺省 1:不相邻   2:相邻
}

/*****************************主动防护参数配置***************************/
//电流均衡
message CurrentBalance {
	float EqualChgCurrCoef = 1;         // 均衡阶段的充电电流系数    数据范围：0-1000，分辨率：0.01，偏移量：0；0xFFFF：不设置；
	uint32 AllowEqualChgTime = 2;  //   电池均衡时间  数据范围：0~250min，分辨率：1min，偏移量：0；0xFF：不设置；
}

//热失控
message HotRunaway {
	float HotRunThreshold = 1;                        //  热失控温升阈值  数据范围：-50~200℃，分辨率：1℃，-50℃偏移量；0xFF：不设置；
	uint32 HotRunConfireTime = 2;                //  热失控确认时间  数据范围：0~250秒，分辨率：1秒，偏移量：0；0xFF：不设置；
	uint32 HotRunProtDisabled = 3;               //  禁止热失控防护  0x01：是；0x00：否；0xFF：不设置；
}

//单体过压
message CellOverVolt {
	float LiFePO4CellAllowedChgVoltMax = 1;    	  //  磷酸铁锂单体过压阈值   数据范围：0~24V，分辨率：0.01V，偏移量：0；0xFFFF：不设置；
	float LiMnNiCoCellAllowedChgVoltMax = 2;     //  镍钴锰三元锂单体过压阈值   数据范围：0~24V，分辨率：0.01V，偏移量：0；0xFFFF：不设置；
	float LiTitanateCellAllowedChgVoltMax = 3;    //   钛酸锂单体过压阈值    数据范围：0~24V，分辨率：0.01V，偏移量：0；0xFFFF：不设置；
	float LiManganateCellAllowedChgVoltMax = 4;  // 锰酸锂单体过压阈值   数据范围：0~24V，分辨率：0.01V，偏移量：0；0xFFFF：不设置；
	uint32 CellOverVoltConfireTime = 5;       			 //  单体过压确认时间  数据范围：0~250秒，分辨率：1秒，偏移量：0；0xFF：不设置；
	uint32 CellOverVoltProtDisabled = 6;      			//   禁止单体过压防护                0x01：是；0x00：否；0xFF：不设置；
}

//电池包过压
message PackOverVolt {
	float PackAllowedChgVoltMax = 1;   			//  整体过压阈值   数据范围：0~750V，分辨率：0.1V，偏移量：0；0xFFFF：不设置；
	uint32 PackOverVoltConfireTime = 2;  			//  整体过压确认时间  数据范围：0~250秒，分辨率：1秒，偏移量：0；0xFF：不设置；
	uint32 PackOverVoltProtDisabled = 3; 	       //   禁止整体过压防护  0x01:是；0x00:否；0xFF：不设置；
}

//过流
message OverCurrent {
	float OverCurrThreshold = 1;               //  过流阈值   数据范围：0~2000A，分辨率：0.1A，偏移量：-2000A；0xFFFF：不设置；
    uint32 OverCurrConfireTime = 2;       // 过流确认时间   数据范围：0~250秒，分辨率：1秒，偏移量：0；0xFF：不设置；
	uint32 OverCurrProtDisabled = 3;     //  禁止过流防护   0x01：是；0x00：否；0xFF：不设置；
}

//单体过温
message CellOverTemp {
	float LiFePO4OverTempThreshold = 1;        		// 磷酸铁锂单体过温阈值  数据范围：-50~200℃，分辨率：1℃，偏移量：-50℃；0xFF：不设置；
    float LiMnNiCoOverTempThreshold =2;           // 镍钴锰三元锂过温阈值        数据范围：-50~200℃，分辨率：1℃，偏移量：-50℃；0xFF：不设置；
	float LiTitanateOverTempThreshold = 3;         // 钛酸锂过温阈值 数据范围：-50~200℃，分辨率：1℃，偏移量：-50℃；0xFF：不设置；
	float LiManganateOverTempThreshold = 4;   // 锰酸锂过温阈值  数据范围：-50~200℃，分辨率：1℃，偏移量：-50℃；0xFF：不设置；
	uint32 OverTempConfireTime = 5;        //  过温确认时间  数据范围：0~250秒，分辨率：1秒，偏移量：0；0xFF：不设置；
	uint32 OverTempProtDisabled = 6;       //  禁止过温防护  0x01：是；0x00:否；0xFF：不设置；
}

//低温
message LowTemp {
	float LowTempThreshold = 1;        		// 低温阈值  数据范围：-50~200℃，分辨率：1℃，偏移量：-50℃；0xFF：不设置；
    uint32 LowTempConfireTime = 2;        //  低温确认时间  数据范围：0~250秒，分辨率：1秒，偏移量：0；0xFF：不设置；
	uint32 LowTempProtDisabled = 3;       //  禁止低温防护  0x01：是；0x00:否；0xFF：不设置；
}

//BMS继电器粘连
message BMSRlyStick {
	float    BMSRlyStickVoltThreshold = 1;     //   BMS继电器粘连电压阈值   数据范围：0~750V，分辨率：0.1V，偏移量：0；0xFFFF：不设置；
    uint32 BMSRlyStickConfireTime = 2;        //   BMS继电器粘连确认时间   数据范围：0~250秒，分辨率：1秒，偏移量：0；0xFF：不设置；
    uint32 BMSRlyStickDisabled = 3;               //   禁止BMS继电器粘连检测    0x01：是；0x00：否；0xFF：不设置；
}

//BMS继电器开路
message BMSRlyOC {
	float    BMSRlyOCVoltThreshold = 1;         //   BMS继电器开路电压阈值   数据范围：0~750V，分辨率：0.1V，偏移量：0；0xFFFF：不设置；
    float    BMSRlyOCCurrentThreshold = 2;    //   BMS继电器开路电流阈值     数据范围：0~2000A，分辨率：0.1A，偏移量：-2000A；0xFFFF：不设置；
	uint32 BMSRlyOCConfireTime = 3;        //   BMS继电器开路确认时间   数据范围：0~250秒，分辨率：1秒，偏移量：0；0xFF：不设置；
    uint32 BMSRlyOCDisabled = 4;               //   禁止BMS继电器开路检测    0x01：是；0x00：否；0xFF：不设置；
}


//过充
message  OverCharge {
	    float OverChgCoef = 1;                            // 过充判断系数设置     数据范围：1~10，分辨率：0.1，偏移量：0；0xFF：不设置；
        float OverChgAH = 2;                               //  过充判断电量参考值 数据范围：1~10，分辨率：0.1，偏移量：0；0xFF：不设置；
        uint32 OverChgConfireTime = 3;          //  过充判断确认时间    数据范围：0~250秒，分辨率：1秒，偏移量：0；0xFF：不设置；
        uint32 OverChgProtDisabled = 4;         //  禁止过充检测主动防护   0x01：是；0x00：否；0xFF：不设置；
}

//BMS数据重复
message BMSDataRepeat {
	uint32 BMSDataRepeatConfireTime = 1;        //   BMS数据重复确认时间       数据范围：15~120min，分辨率：1min，偏移量：0；0xFF：不设置；
	uint32 BMBMSDataErrorProtDisabled = 2;    //   禁止BMS数据校验告警检测    0x01：是；0x00：否；0xFF：不设置；SDataRepeatProtDisabled = 2;   //  禁止BMS数据重复异常防护   0x01：是；0x00：否；0xFF：不设置；
	uint32 BMSDataErrorProtDisabled =3;            //   禁止BMS数据校验告警检测    0x01：是；0x00：否；0xFF：不设置；
}


//主动防护参数配置
message ActiveProtectParam {
	CurrentBalance currBalance = 1;               // 电流均衡参数
	HotRunaway  hotRunaway = 2;                  // 热失控参数
	CellOverVolt  cellOverVolt = 3;                  // 单体过压参数
	PackOverVolt packOverVolt = 4;               //  整包过压参数
	OverCurrent  overCurrent = 5;                  //  过流
	CellOverTemp  cellOverTemp = 6;            //  单体过温
	LowTemp  lowTemp = 7;                              //  低温
	BMSRlyStick  bmsRlyStick = 8;                   //  BMS继电器粘连
	BMSRlyOC  bmsRlyOC = 9;                  		 //  BMS继电器开路
	OverCharge overCharge = 10;                    //  过充
	BMSDataRepeat bmsDataRepeat = 11;  //   BMS 数据重复
}



