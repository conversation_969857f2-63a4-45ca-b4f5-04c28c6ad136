syntax = "proto3";
package EVSInfo;

//ProtoConferMsg-版本协商信息
message EVProtoConferMsg {
    uint32 canType = 1;            // CAN类型 (默认0x00): CAN2.0: 0x00; CAN FD:0x01; CANXL:0x02
    bytes  gbVersion = 2;          // 车辆期望或协商一致的版本号(默认V2.0.0) GB2015(V1.0.0),GB2015p(V2.0.0)
                                   // 协议版本号 BYTE1:主版本号，BYTE2：次版本号，BYTE3：临时版本号 
    uint32 guidanceVersion = 3;    // 车辆控制导引版本 默认0x01:GB2015p ; 0x02:GB2015 
    uint32 transportVersion = 4;   // 车辆传输层版本 默认 0x01: 2015+; 0xFF 其他
    uint32 conferRes = 5;          // 预留 默认 0xFF
}

//FunConfer-功能协商
message EVFunConferMsg {  //（按位表示，bit0 ~bit7 ，对应FDC1~FDC8）
    uint32 configFDC = 1;      //车辆对配置功能支持情况 (默认0x01) FDC1:充电;  FDC2: 充放电
    uint32 authenFDC = 2;      //车辆对鉴权功能支持情况 (默认0x01) FDC1:扫码/刷卡; FDC2 : EVIN鉴权;  FDC3: 云端鉴权-桩识别码
    uint32 appointFDC = 3;     //车辆对预约功能支持情况 (默认0x00)
    uint32 selfCheckFDC = 4;   //车辆对输出回路检测功能支持情况 (默认0x01)
    uint32 powerSupplyFDC = 5; //车辆对供电模式功能支持情况 (默认0x01)
    uint32 energyTransferFDC = 6;//车辆对预充及能量传输功能支持情况 (默认0x01) FDC1:充电模式 FDC2:充放电模式
    uint32 endFDC = 7;         //车辆对结束功能支持情况 (默认0x01)
}

//车辆充电参数配置
message EVChgParaConfigMsg {
    float currAllowMAX = 1;     // 车辆最大允许充电电流(默认250.0A)   0A - 6500.0A
    float voltAllowMAX = 2;     // 车辆最高允许充电电压(默认1000.0V)   0V - 6500.0V
    float energyAllowMAX = 3;   // 车辆最高允许输入总能量(默认100.0Kw.h) 0.1kw.h/位 0Kwh - 6500.0Kwh -256.0:参数无效 -255.0：参数异常
    float nowSOC = 4;           // 车辆荷电状态(默认49.0)  0% - 100.0%
    float cellVoltAllowMAX = 5; // 电池最小并联单元最高允许电压(默认3.80V)  0.01V/位 0V - 650.00V -256.00:参数无效 -255.0：参数异常
    float batTempAllowMAX = 6; // 电池单体最高允许温度(默认25 ℃) 1℃/位 -50℃ ~ 200℃ -256：参数无效 -255：数据异常
    uint32 restarNum = 7;       // 重新启动次数(默认 1次) 0 - 200次 0xFE：次数不限， 0xFF：参数无效
}


// 鉴权信息
message EVAuthenMsg{
  uint32 authenWaitTime = 1;     // 总鉴权等待时间(默认600s)  
  bytes  eVIN = 2;               // 车辆识别码EVIN(默认 XIANWANMAPRODUCE7)  
  uint32 nextFDC = 3;            // 重新鉴权的FDC(默认0): 默认 0:无重新鉴权；1~8:FDC功能模块
}

//预约充电信息 
message EVReserveMsg {
    uint32 bmsDesireStartTime = 1;    // 车辆期望开始充电时间(默认0) min 0~65000min, 0xFFFF：充电机对此内容不做判定
    uint32 bmsDesireLeaveTime = 2;    // 车辆期望出发时间(默认0) min 0~65000min, 0xFFFF：充电机对此内容不做判定
    uint32 reserveResult = 3;         // 预约协商充电结果(默认0xFF) 0xAA:预约协商成功; 0xFF 预约协商失败; 
    uint32 immediateChargeSupport = 4;// 立即充电支持(默认0x00) 0x00 不支持, 0xAA:支持;
}

// 供电模式 
message EVPowerSupplyMsg{
    uint32 supplyState = 1;        // 车辆供电状态(默认0x01) 0x00:未就绪，0x01 :就绪
    float supplyVolDesire = 2;     // 供电电压需求(默认300.0V) 0.0 - 6500.0V
    float supplyCurrDesire = 3;    // 供电电流需求(默认100.0A) 0.0 - 6500.0A
    uint32 supplyEnd = 4;          // 供电模式结束请求(默认 0x00):0xAA,结束供电;其他，继续供电 
}

//充电阶段信息 
message EVChargingMsg {
    uint32 bmsReady = 1;          // BMS就绪状态(默认0xAA): 0x00未就绪；0xAA就绪
    float volDemand = 2;          // 需求电压(默认450.0V):0.0 - 6500.0V
    float curDemand = 3;          // 需求电流(默认200.0A):0.0 - 6500.0A
    uint32 chargeMode = 4;        // 充电模式(默认0x01)：0x01恒流，0x02恒压
    float socNow = 5;             // 当前SOC:(默认49.0)
    uint32 resChgTime = 6;         // 剩余估算时间:(默认100)，0~65000min
    float cellBatVolMax = 7;     // 单体电池最高电压:(默认3.10V) 0.01 V/位 ：0 V～650.00 V -256.00：参数无效 -255.00：数据异常
    float cellBatVolMin = 8;     // 单体电池最低电压:(默认2.90V) 0.01 V/位 ：0 V～650.00 V -256.00：参数无效 -255.00：数据异常
    float celltempMax = 9;       // 电池最高温度:(默认44 ℃) 1℃/位 -50℃ ~ 200℃  -256.0：参数无效 -255.0：数据异常
    float celltempMin = 10;      // 电池最低温度:(默认34 ℃) 1℃/位 -50℃ ~ 200℃  -256.0：参数无效 -255.0：数据异常
}

//充电结束信息
message EVChargingEndMsg {
    uint32 endType = 1;     // 中止类型(默认0) 
    uint32 endCode = 2;     // 中止码(默认0) 
    uint32 endReason = 3;   // 中止原因(默认0) 
    uint32 repeat = 4;      // 请求重连(默认0) ：不请求重连，0xAA请求重连，0xFF无效
    uint32 bmsStickCheckState = 5;  // 车辆粘连检测状态(默认0) 0:自动；1:检测中;2:异常中止;3:检测通过;4:检测失败;0xFF:本次不检测
}

//PhaseACK-阶段确认
message EVFunConferAckMsg {  //（0x00:确认失败， 0xAA:确认成功, 0x55:不回复, 0x66 自动）(默认0x66)
    uint32 funConferAck = 1;   //功能协商功能确认结果 (默认0x66)
    uint32 configAck = 2;      //配置功能确认结果 (默认0x66)
    uint32 authenAck = 3;      //鉴权功能确认结果 (默认0x66)
    uint32 appointAck = 4;     //预约功能确认结果 (默认0x66)
    uint32 selfCheckAck = 5;   //输出回路检测功能确认结果 (默认0x66)
    uint32 powerSupplyAck = 6; //供电模式功能确认结果 (默认0x66)
    uint32 energyTransferAck = 7;//预充及能量传输功能确认结果 (默认0x66)
    uint32 endAck = 8;         //结束功能确认结果 (默认0x66)
}

//EVDeviceMsg 车辆电气控制
message EVElectricCtrl {
    uint32 contactK5 = 1;    // K5状态  默认0x00:断开， 0xAA:闭合 
    uint32 contactK6 = 2;    // K6状态  默认0x00:断开， 0xAA:闭合
    uint32 CC1_S2 = 3;       // S2状态  默认0x00:断开， 0xAA:闭合 
    uint32 CC2_S3 = 4;       // S3状态  默认0x00:断开， 0xAA:闭合 
    uint32 elockState = 5;   // 车辆电子锁状态 默认未锁止0x00,锁止0xAA
    uint32 canBus = 6;       // CANBus状态  默认 0xAA:闭合， 0x00:断开
    uint32 ev_PE = 7;        // PE断线状态  默认 0xAA:闭合， 0x00:断开 
    uint32 evInsultOn = 8;   // 车辆绝缘开启  默认0x00:关闭, 0xAA:打开 
    uint32 evContactK6 = 9;  // 车辆绝缘检测周期(默认0)  min 0:连续做，0~200min
    uint32 evPause = 10;     // 车辆暂停(默认0x00):0xAA 暂停，0x00 恢复
    uint32 sysFan = 11;      // 系统风机: 0 关机，0x1 开机
}

//EVInsult 绝缘检测控制
message EVInsultCtrl {
    uint32 insultPosRes = 1; // DC+ 阻抗 kΩ (默认 1000)
    uint32 insultNegRes = 2; // DC- 阻抗 kΩ (默认 1000)
    float  batVol = 3;       // 电池电压(默认 300.0V) 0.0 - 6500.0V
    uint32 connectType = 4;  // 连接方式(默认 0x00)： 0x00 正接； 0x01 反接； 0x02 开路
}

//EVState 车辆状态
message EVStateMsg {
    float batVol = 1;    // 电池电压
    float batCur = 2;    // 电池电流
    float nowSOC = 3;    // 当前SOC
    uint32 nowFC = 4;    // 当前FC 0x0: 版本协商; 0x10:功能协商; 0x20 参数配置;  0x30鉴权; 0x40 预约; 0x50 输出回路检测 0x60 供电模式; 0x70 预充及能量传输; 0x80 结束;
    uint32 nowFDC = 5;   // 当前FDC (0~8)
    uint32 chgMode = 6;  // 充电模式 （恒压 恒流）
    float insultPosR = 7;// DC+ 阻抗 kΩ
    float insultNegR = 8;// DC- 阻抗 kΩ
    float cc1Volt = 9;   // CC1 电压
    uint32 workMode = 10;// 工作模式： 1 正常模式；2 步进模式
    uint32 stopCode = 11;// 中止码
    float cc2Volt = 12;   // CC2 电压
}


//EVStepMsg 车辆步进参数
message StepPara {
    uint64 startTime = 1;       // 开始时间 *s ( 0:立马生效，10: 10s后生效) (默认 0x00)
    uint64 intervalTime = 2;    // 间隔时间 *s (默认 0x00)
    float startValue = 3;       // 起始值 (默认 0x00)
    float minValue = 4;         // 最小值 (默认 0x00)
    float maxValue = 5;         // 最大值 (默认 0x00)
    float stepValue = 6;        // 步进值 (默认 0x00)
    uint32 cycleMode = 7;       // 循环模式(默认 0x00)： 0x00 关闭 0xAA 打开
    uint32 stepMode = 8;        // 步进模式(默认 0x00)： 0x00 关闭 0xAA 打开
}

message EVSStepMsg{
    StepPara needVolStep = 1;   // 需求电压步进
    StepPara needCurrStep = 2;  // 需求电流步进
    StepPara socStep = 3;       // soc步进
    StepPara cellVolStep = 4;   // 单体电压步进
    StepPara cellTempStep = 5;  // 单体温度步进
}

//报文状态设置
message EVMsgCtrl { // 周期 -- 最大时间
    uint32 x2_0x02_State = 1;  // 车辆确认结果报文 0xFFFF 1s
    uint32 x4_0x04_State = 2;  // 车辆中止报文 0xFFFF 1s
    uint32 x6_0x06_State = 3;  // 车辆充电回路接触器状态报文 1s 0xFFFF
    uint32 x9_0x09_State = 4;  // 车辆唤醒报文 0xFFFF 10s
    uint32 b2_0x12_State = 5;  // 车辆功能协商确认结果报文 0xFFFF 1s
    uint32 c2_0x22_State = 6;  // 车辆充电参数报文（FDC = 1）0xFFFF 5s
    uint32 c4_0x24_State = 7;  // 车辆充放电参数报文（FDC = 2） 0xFFFF 5s
    uint32 d2_0x32_State = 8;  // 车辆鉴权等待报文（FDC = 1）0xFFFF 0xFFFF
    uint32 d4_0x34_State = 9;  // 车辆鉴权参数报文（FDC = 2）0xFFFF 1s
    uint32 d6_0x36_State = 10; // 重新鉴权请求报文（FDC = 2）0xFFFF 1s
    uint32 d7_0x37_State = 11; // 车辆鉴权参数报文（FDC = 3）0xFFFF 1s
    uint32 d9_0x39_State = 12; // 鉴权结果报文（FDC = 3） 0xFFFF 1s
    uint32 d10_0x3A_State = 13;// 重新鉴权请求报文（FDC = 3）0xFFFF 1s
    uint32 e2_0x42_State = 14; // 车辆预约充电信息报文（FDC = 1) 0xFFFF 10s
    uint32 e4_0x44_State = 15; // 车辆预约充电协商报文（FDC = 1）0xFFFF 1s
    uint32 f2_0x52_State = 16; // 检测确认报文（FDC = 1） 0xFFFF 1s
    uint32 g2_0x62_State = 17; // 车辆供电状态报文（FDC = 1）0xFFFF 0xFFFF
    uint32 g3_0x63_State = 18; // 车辆供电需求报文（FDC = 1）1s 0xFFFF
    uint32 g5_0x65_State = 19; // 车辆供电完成报文           0xFFFF 1s
    uint32 h2_0x72_State = 20; // 车辆就绪状态报文（FDC = 1）0xFFFF 0xFFFF
    uint32 h3_0x73_State = 21; // 车辆充电需求报文（FDC = 1）1s 0xFFFF
    uint32 h4_0x74_State = 22; // 车辆充电基本信息报文（FDC = 1）1s 0xFFFF
    uint32 h7_0x77_State = 23; // 车辆充电电池基本信息（FDC = 1）1s 0xFFFF
    uint32 h9_0x79_State = 24; // 车辆暂停报文（FDC = 1）       0xFFFF 5s
    uint32 h11_0x82_State = 25; // 车辆就绪状态报文（FDC = 2） 0xFFFF 0xFFFF
    uint32 h13_0x84_State = 26; // 车辆动态输出能力报文（FDC = 2）250ms 0xFFFF
    uint32 h14_0x85_State = 27; // 车辆充电需求报文（FDC = 2）    1s 0xFFFF
    uint32 h16_0x87_State = 28; // 车辆充放电基本信息报文（fDC = 2）1s 0xFFFF
    uint32 h18_0x89_State = 29; // 车辆充放电电池基本信息（FDC = 2）1s 0xFFFF
    uint32 h20_0x8B_State = 30; // 车辆暂停报文（FDC = 2）          0xFFFF 5s
    uint32 i1_0x91_State = 31;  // 车辆粘连检测报文（FDC = 1） 250ms 0xFFFF
    uint32 i4_0x94_State = 32;  // 车辆统计报文（FDC = 1）     0xFFFF 1s
}
// 网络设置
message IpMsg{
    bytes selfIP = 1;       // 本机ip
    uint32 selfPort = 2;    // 本机端口
    bytes serviceIP = 3;       // service ip
    uint32 servicePort = 4;    // service端口
}