// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GCU_BMS2015P_INFO.proto

#include "GCU_BMS2015P_INFO.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace BMS2015PlusInfo {
constexpr ProtoConferMsg::ProtoConferMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : chggbversion_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , evgbversion_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , chargerguidanceversion_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , chargertransportversion_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , bmsguidanceversion_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , bmstransportversion_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , chargercantype_(0u)
  , bmscantype_(0u)
  , chargerconferresult_(0u)
  , evconferresult_(0u){}
struct ProtoConferMsgDefaultTypeInternal {
  constexpr ProtoConferMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ProtoConferMsgDefaultTypeInternal() {}
  union {
    ProtoConferMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ProtoConferMsgDefaultTypeInternal _ProtoConferMsg_default_instance_;
constexpr FunConferMsg::FunConferMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : chgconfigfdc_(0u)
  , chgauthenfdc_(0u)
  , chgappointfdc_(0u)
  , chgselfcheckfdc_(0u)
  , chgpowersupplyfdc_(0u)
  , chgenergytransferfdc_(0u)
  , chgendfdc_(0u)
  , evconfigfdc_(0u)
  , evauthenfdc_(0u)
  , evappointfdc_(0u)
  , evselfcheckfdc_(0u)
  , evpowersupplyfdc_(0u)
  , evenergytransferfdc_(0u)
  , evendfdc_(0u){}
struct FunConferMsgDefaultTypeInternal {
  constexpr FunConferMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~FunConferMsgDefaultTypeInternal() {}
  union {
    FunConferMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT FunConferMsgDefaultTypeInternal _FunConferMsg_default_instance_;
constexpr BMSConfig::BMSConfig(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : monovolmaxallowed_(0)
  , curallowedmax_(0)
  , energyallowdmax_(0)
  , volallowedmax_(0)
  , tempallowedmax_(0)
  , startsoc_(0)
  , volbatnow_(0)
  , volchargermax_(0)
  , volchargermin_(0)
  , curchargermax_(0)
  , curchargermin_(0)
  , curruprate_(0u)
  , currdownrate_(0u)
  , chargerallowedrestartnum_(0u)
  , bmsallowedrestartnum_(0u){}
struct BMSConfigDefaultTypeInternal {
  constexpr BMSConfigDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~BMSConfigDefaultTypeInternal() {}
  union {
    BMSConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT BMSConfigDefaultTypeInternal _BMSConfig_default_instance_;
constexpr DischargeConfig::DischargeConfig(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : voldischargemax_(0)
  , voldischargemin_(0)
  , curdischargemax_(0)
  , curdischargemin_(0)
  , volbmsallowedmin_(0)
  , curbmsallowedmax_(0)
  , curbmsallowedmin_(0)
  , volcellallowedmax_(0)
  , volcellallowedmin_(0)
  , socallowedmin_(0)
  , totalbatterycyclenum_(0)
  , allowedbatterycyclenum_(0)
  , desireresiduerange_(0)
  , tempallowedmax_(0)
  , chargerallowedrestartnum_(0u)
  , bmsallowedrestartnum_(0u){}
struct DischargeConfigDefaultTypeInternal {
  constexpr DischargeConfigDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DischargeConfigDefaultTypeInternal() {}
  union {
    DischargeConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DischargeConfigDefaultTypeInternal _DischargeConfig_default_instance_;
constexpr AuthenMsg::AuthenMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : evin_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , batproducer_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , chargeroperators_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , chargernumber_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , nowfdc_(0u)
  , authenwaittime_(0u)
  , bmswaitauthenstate_(0u)
  , authenresult_(0u)
  , succeedauthenfdc_(0u){}
struct AuthenMsgDefaultTypeInternal {
  constexpr AuthenMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AuthenMsgDefaultTypeInternal() {}
  union {
    AuthenMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AuthenMsgDefaultTypeInternal _AuthenMsg_default_instance_;
constexpr ReserveMsg::ReserveMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : bmsdesirestarttime_(0u)
  , bmsdesireleavetime_(0u)
  , chargeroutpowermax_(0)
  , immediatechargesupport_(0u)
  , reserveresult_(0u){}
struct ReserveMsgDefaultTypeInternal {
  constexpr ReserveMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ReserveMsgDefaultTypeInternal() {}
  union {
    ReserveMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ReserveMsgDefaultTypeInternal _ReserveMsg_default_instance_;
constexpr SelfcheckMsg::SelfcheckMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : stickcheckstate_(0u)
  , shortcircuitcheckstate_(0u)
  , insultcheckstate_(0u)
  , dischargestate_(0u){}
struct SelfcheckMsgDefaultTypeInternal {
  constexpr SelfcheckMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SelfcheckMsgDefaultTypeInternal() {}
  union {
    SelfcheckMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SelfcheckMsgDefaultTypeInternal _SelfcheckMsg_default_instance_;
constexpr VehicelState::VehicelState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : vehiclec5state_(0u)
  , vehiclec6state_(0u)
  , vehicelelockstate_(0u){}
struct VehicelStateDefaultTypeInternal {
  constexpr VehicelStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~VehicelStateDefaultTypeInternal() {}
  union {
    VehicelState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT VehicelStateDefaultTypeInternal _VehicelState_default_instance_;
constexpr PowerSupplyMsg::PowerSupplyMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : chargersupplystate_(0u)
  , evsupplystate_(0u)
  , voldesire_(0)
  , currdesire_(0)
  , outvol_(0)
  , outcurr_(0)
  , chgoutcurrmax_(0)
  , resonforcapacitychange_(0){}
struct PowerSupplyMsgDefaultTypeInternal {
  constexpr PowerSupplyMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PowerSupplyMsgDefaultTypeInternal() {}
  union {
    PowerSupplyMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PowerSupplyMsgDefaultTypeInternal _PowerSupplyMsg_default_instance_;
constexpr BMSChargingEnd::BMSChargingEnd(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : bmsstickcheckstate_(0u)
  , chgstickcheckenable_(0u)
  , energychg_(0)
  , energydischarge_(0)
  , endsoc_(0)
  , chargerstoptype_(0u)
  , chargerstopcode_(0u)
  , bmsstoptype_(0u)
  , bmsstopcode_(0u)
  , chgreconnectenable_(0u)
  , reconnectenable_(0u){}
struct BMSChargingEndDefaultTypeInternal {
  constexpr BMSChargingEndDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~BMSChargingEndDefaultTypeInternal() {}
  union {
    BMSChargingEnd _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT BMSChargingEndDefaultTypeInternal _BMSChargingEnd_default_instance_;
constexpr BMSCharging::BMSCharging(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : bmsready_(0u)
  , chargerready_(0u)
  , chargemode_(0u)
  , resonforcapacitychange_(0u)
  , bmspause_(0u)
  , chgpause_(0u)
  , bmsbatvol_(0)
  , voldemand_(0)
  , curdemand_(0)
  , socnow_(0)
  , curroutcapacity_(0)
  , volmeasured_(0)
  , curmeasured_(0)
  , monobatvolmax_(0)
  , monobatvolmin_(0)
  , tempmax_(0)
  , tempmin_(0){}
struct BMSChargingDefaultTypeInternal {
  constexpr BMSChargingDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~BMSChargingDefaultTypeInternal() {}
  union {
    BMSCharging _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT BMSChargingDefaultTypeInternal _BMSCharging_default_instance_;
constexpr bms2015pMsg::bms2015pMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : bmsprotoconfer_(nullptr)
  , bmsfunconfer_(nullptr)
  , bmsconfgm_(nullptr)
  , bmschargingm_(nullptr)
  , bmschargefinishm_(nullptr)
  , bmsauthenm_(nullptr)
  , bmsreservem_(nullptr)
  , bmsselfcheckm_(nullptr)
  , bmsvehicelstatem_(nullptr)
  , powersupplym_(nullptr)
  , bmsstate_(0)
{}
struct bms2015pMsgDefaultTypeInternal {
  constexpr bms2015pMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~bms2015pMsgDefaultTypeInternal() {}
  union {
    bms2015pMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT bms2015pMsgDefaultTypeInternal _bms2015pMsg_default_instance_;
}  // namespace BMS2015PlusInfo
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_GCU_5fBMS2015P_5fINFO_2eproto[12];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_GCU_5fBMS2015P_5fINFO_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_GCU_5fBMS2015P_5fINFO_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_GCU_5fBMS2015P_5fINFO_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::ProtoConferMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::ProtoConferMsg, chargercantype_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::ProtoConferMsg, bmscantype_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::ProtoConferMsg, chargerconferresult_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::ProtoConferMsg, evconferresult_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::ProtoConferMsg, chggbversion_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::ProtoConferMsg, evgbversion_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::ProtoConferMsg, chargerguidanceversion_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::ProtoConferMsg, chargertransportversion_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::ProtoConferMsg, bmsguidanceversion_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::ProtoConferMsg, bmstransportversion_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::FunConferMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::FunConferMsg, chgconfigfdc_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::FunConferMsg, chgauthenfdc_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::FunConferMsg, chgappointfdc_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::FunConferMsg, chgselfcheckfdc_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::FunConferMsg, chgpowersupplyfdc_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::FunConferMsg, chgenergytransferfdc_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::FunConferMsg, chgendfdc_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::FunConferMsg, evconfigfdc_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::FunConferMsg, evauthenfdc_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::FunConferMsg, evappointfdc_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::FunConferMsg, evselfcheckfdc_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::FunConferMsg, evpowersupplyfdc_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::FunConferMsg, evenergytransferfdc_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::FunConferMsg, evendfdc_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSConfig, monovolmaxallowed_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSConfig, curallowedmax_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSConfig, energyallowdmax_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSConfig, volallowedmax_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSConfig, tempallowedmax_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSConfig, startsoc_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSConfig, volbatnow_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSConfig, volchargermax_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSConfig, volchargermin_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSConfig, curchargermax_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSConfig, curchargermin_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSConfig, curruprate_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSConfig, currdownrate_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSConfig, chargerallowedrestartnum_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSConfig, bmsallowedrestartnum_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::DischargeConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::DischargeConfig, voldischargemax_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::DischargeConfig, voldischargemin_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::DischargeConfig, curdischargemax_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::DischargeConfig, curdischargemin_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::DischargeConfig, volbmsallowedmin_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::DischargeConfig, curbmsallowedmax_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::DischargeConfig, curbmsallowedmin_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::DischargeConfig, volcellallowedmax_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::DischargeConfig, volcellallowedmin_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::DischargeConfig, socallowedmin_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::DischargeConfig, totalbatterycyclenum_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::DischargeConfig, allowedbatterycyclenum_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::DischargeConfig, desireresiduerange_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::DischargeConfig, tempallowedmax_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::DischargeConfig, chargerallowedrestartnum_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::DischargeConfig, bmsallowedrestartnum_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::AuthenMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::AuthenMsg, nowfdc_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::AuthenMsg, authenwaittime_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::AuthenMsg, bmswaitauthenstate_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::AuthenMsg, authenresult_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::AuthenMsg, succeedauthenfdc_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::AuthenMsg, evin_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::AuthenMsg, batproducer_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::AuthenMsg, chargeroperators_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::AuthenMsg, chargernumber_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::ReserveMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::ReserveMsg, bmsdesirestarttime_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::ReserveMsg, bmsdesireleavetime_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::ReserveMsg, chargeroutpowermax_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::ReserveMsg, immediatechargesupport_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::ReserveMsg, reserveresult_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::SelfcheckMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::SelfcheckMsg, stickcheckstate_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::SelfcheckMsg, shortcircuitcheckstate_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::SelfcheckMsg, insultcheckstate_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::SelfcheckMsg, dischargestate_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::VehicelState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::VehicelState, vehiclec5state_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::VehicelState, vehiclec6state_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::VehicelState, vehicelelockstate_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::PowerSupplyMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::PowerSupplyMsg, chargersupplystate_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::PowerSupplyMsg, evsupplystate_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::PowerSupplyMsg, voldesire_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::PowerSupplyMsg, currdesire_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::PowerSupplyMsg, outvol_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::PowerSupplyMsg, outcurr_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::PowerSupplyMsg, chgoutcurrmax_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::PowerSupplyMsg, resonforcapacitychange_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSChargingEnd, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSChargingEnd, bmsstickcheckstate_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSChargingEnd, chgstickcheckenable_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSChargingEnd, energychg_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSChargingEnd, energydischarge_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSChargingEnd, endsoc_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSChargingEnd, chargerstoptype_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSChargingEnd, chargerstopcode_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSChargingEnd, bmsstoptype_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSChargingEnd, bmsstopcode_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSChargingEnd, chgreconnectenable_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSChargingEnd, reconnectenable_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSCharging, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSCharging, bmsready_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSCharging, chargerready_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSCharging, chargemode_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSCharging, resonforcapacitychange_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSCharging, bmspause_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSCharging, chgpause_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSCharging, bmsbatvol_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSCharging, voldemand_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSCharging, curdemand_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSCharging, socnow_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSCharging, curroutcapacity_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSCharging, volmeasured_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSCharging, curmeasured_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSCharging, monobatvolmax_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSCharging, monobatvolmin_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSCharging, tempmax_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::BMSCharging, tempmin_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::bms2015pMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::bms2015pMsg, bmsstate_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::bms2015pMsg, bmsprotoconfer_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::bms2015pMsg, bmsfunconfer_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::bms2015pMsg, bmsconfgm_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::bms2015pMsg, bmschargingm_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::bms2015pMsg, bmschargefinishm_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::bms2015pMsg, bmsauthenm_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::bms2015pMsg, bmsreservem_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::bms2015pMsg, bmsselfcheckm_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::bms2015pMsg, bmsvehicelstatem_),
  PROTOBUF_FIELD_OFFSET(::BMS2015PlusInfo::bms2015pMsg, powersupplym_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::BMS2015PlusInfo::ProtoConferMsg)},
  { 15, -1, sizeof(::BMS2015PlusInfo::FunConferMsg)},
  { 34, -1, sizeof(::BMS2015PlusInfo::BMSConfig)},
  { 54, -1, sizeof(::BMS2015PlusInfo::DischargeConfig)},
  { 75, -1, sizeof(::BMS2015PlusInfo::AuthenMsg)},
  { 89, -1, sizeof(::BMS2015PlusInfo::ReserveMsg)},
  { 99, -1, sizeof(::BMS2015PlusInfo::SelfcheckMsg)},
  { 108, -1, sizeof(::BMS2015PlusInfo::VehicelState)},
  { 116, -1, sizeof(::BMS2015PlusInfo::PowerSupplyMsg)},
  { 129, -1, sizeof(::BMS2015PlusInfo::BMSChargingEnd)},
  { 145, -1, sizeof(::BMS2015PlusInfo::BMSCharging)},
  { 167, -1, sizeof(::BMS2015PlusInfo::bms2015pMsg)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::BMS2015PlusInfo::_ProtoConferMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::BMS2015PlusInfo::_FunConferMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::BMS2015PlusInfo::_BMSConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::BMS2015PlusInfo::_DischargeConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::BMS2015PlusInfo::_AuthenMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::BMS2015PlusInfo::_ReserveMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::BMS2015PlusInfo::_SelfcheckMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::BMS2015PlusInfo::_VehicelState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::BMS2015PlusInfo::_PowerSupplyMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::BMS2015PlusInfo::_BMSChargingEnd_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::BMS2015PlusInfo::_BMSCharging_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::BMS2015PlusInfo::_bms2015pMsg_default_instance_),
};

const char descriptor_table_protodef_GCU_5fBMS2015P_5fINFO_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\027GCU_BMS2015P_INFO.proto\022\017BMS2015PlusIn"
  "fo\"\226\002\n\016ProtoConferMsg\022\026\n\016chargerCanType\030"
  "\001 \001(\r\022\022\n\nbmsCanType\030\002 \001(\r\022\033\n\023chargerConf"
  "erResult\030\003 \001(\r\022\026\n\016EVConferResult\030\004 \001(\r\022\024"
  "\n\014chgGbVersion\030\005 \001(\014\022\023\n\013EvGbVersion\030\006 \001("
  "\014\022\036\n\026chargerGuidanceVersion\030\007 \001(\014\022\037\n\027cha"
  "rgerTransportVersion\030\010 \001(\014\022\032\n\022bmsGuidanc"
  "eVersion\030\t \001(\014\022\033\n\023bmsTransportVersion\030\n "
  "\001(\014\"\327\002\n\014FunConferMsg\022\024\n\014chgConfigFDC\030\001 \001"
  "(\r\022\024\n\014chgAuthenFDC\030\002 \001(\r\022\025\n\rchgAppointFD"
  "C\030\003 \001(\r\022\027\n\017chgSelfCheckFDC\030\004 \001(\r\022\031\n\021chgP"
  "owerSupplyFDC\030\005 \001(\r\022\034\n\024chgEnergyTransfer"
  "FDC\030\006 \001(\r\022\021\n\tchgEndFDC\030\007 \001(\r\022\023\n\013EVConfig"
  "FDC\030\010 \001(\r\022\023\n\013EVAuthenFDC\030\t \001(\r\022\024\n\014EVAppo"
  "intFDC\030\n \001(\r\022\026\n\016EVSelfCheckFDC\030\013 \001(\r\022\030\n\020"
  "EVPowerSupplyFDC\030\014 \001(\r\022\033\n\023EVEnergyTransf"
  "erFDC\030\r \001(\r\022\020\n\010EVEndFDC\030\016 \001(\r\"\360\002\n\tBMSCon"
  "fig\022\031\n\021monoVolMaxAllowed\030\001 \001(\002\022\025\n\rcurAll"
  "owedMax\030\002 \001(\002\022\027\n\017energyAllowdMax\030\003 \001(\002\022\025"
  "\n\rvolAllowedMax\030\004 \001(\002\022\026\n\016tempAllowedMax\030"
  "\005 \001(\002\022\020\n\010startSOC\030\006 \001(\002\022\021\n\tvolBatNow\030\007 \001"
  "(\002\022\025\n\rvolChargerMax\030\010 \001(\002\022\025\n\rvolChargerM"
  "in\030\t \001(\002\022\025\n\rcurChargerMax\030\n \001(\002\022\025\n\rcurCh"
  "argerMin\030\013 \001(\002\022\022\n\ncurrUpRate\030\014 \001(\r\022\024\n\014cu"
  "rrDownRate\030\r \001(\r\022 \n\030chargerAllowedRestar"
  "tNum\030\016 \001(\r\022\034\n\024bmsAllowedRestartNum\030\017 \001(\r"
  "\"\302\003\n\017DischargeConfig\022\027\n\017volDischargeMax\030"
  "\001 \001(\002\022\027\n\017volDischargeMin\030\002 \001(\002\022\027\n\017curDis"
  "chargeMax\030\003 \001(\002\022\027\n\017curDischargeMin\030\004 \001(\002"
  "\022\030\n\020volBmsAllowedMin\030\005 \001(\002\022\030\n\020curBmsAllo"
  "wedMax\030\006 \001(\002\022\030\n\020curBmsAllowedMin\030\007 \001(\002\022\031"
  "\n\021volCellAllowedMax\030\010 \001(\002\022\031\n\021volCellAllo"
  "wedMin\030\t \001(\002\022\025\n\rsocAllowedMin\030\n \001(\002\022\034\n\024t"
  "otalBatteryCycleNum\030\013 \001(\002\022\036\n\026allowedBatt"
  "eryCycleNum\030\014 \001(\002\022\032\n\022desireResidueRange\030"
  "\r \001(\002\022\026\n\016tempAllowedMax\030\016 \001(\002\022 \n\030charger"
  "AllowedRestartNum\030\017 \001(\r\022\034\n\024bmsAllowedRes"
  "tartNum\030\020 \001(\r\"\323\001\n\tAuthenMsg\022\016\n\006nowFDC\030\001 "
  "\001(\r\022\026\n\016authenWaitTime\030\002 \001(\r\022\032\n\022bmsWaitAu"
  "thenState\030\003 \001(\r\022\024\n\014authenResult\030\004 \001(\r\022\030\n"
  "\020succeedAuthenFDC\030\005 \001(\r\022\014\n\004eVIN\030\006 \001(\014\022\023\n"
  "\013batProducer\030\007 \001(\014\022\030\n\020chargerOperators\030\010"
  " \001(\014\022\025\n\rchargerNumber\030\t \001(\014\"\227\001\n\nReserveM"
  "sg\022\032\n\022bmsDesireStartTime\030\001 \001(\r\022\032\n\022bmsDes"
  "ireLeaveTime\030\002 \001(\r\022\032\n\022chargerOutPowerMax"
  "\030\003 \001(\002\022\036\n\026immediateChargeSupport\030\004 \001(\r\022\025"
  "\n\rreserveResult\030\005 \001(\r\"y\n\014SelfcheckMsg\022\027\n"
  "\017stickCheckState\030\001 \001(\r\022\036\n\026shortCircuitCh"
  "eckState\030\002 \001(\r\022\030\n\020insultCheckState\030\003 \001(\r"
  "\022\026\n\016dischargeState\030\004 \001(\r\"Y\n\014VehicelState"
  "\022\026\n\016vehicleC5State\030\001 \001(\r\022\026\n\016vehicleC6Sta"
  "te\030\002 \001(\r\022\031\n\021vehicelElockState\030\003 \001(\r\"\302\001\n\016"
  "PowerSupplyMsg\022\032\n\022chargerSupplyState\030\001 \001"
  "(\r\022\025\n\rEvSupplyState\030\002 \001(\r\022\021\n\tvolDesire\030\003"
  " \001(\002\022\022\n\ncurrDesire\030\004 \001(\002\022\016\n\006outVol\030\005 \001(\002"
  "\022\017\n\007outCurr\030\006 \001(\002\022\025\n\rchgOutCurrMax\030\007 \001(\002"
  "\022\036\n\026resonForCapacityChange\030\010 \001(\002\"\226\002\n\016BMS"
  "ChargingEnd\022\032\n\022bmsStickCheckState\030\001 \001(\r\022"
  "\033\n\023chgStickCheckEnable\030\002 \001(\r\022\021\n\tenergyCh"
  "g\030\003 \001(\002\022\027\n\017energyDischarge\030\004 \001(\002\022\016\n\006endS"
  "OC\030\005 \001(\002\022\027\n\017chargerStopType\030\006 \001(\r\022\027\n\017cha"
  "rgerStopCode\030\007 \001(\r\022\023\n\013bmsStopType\030\010 \001(\r\022"
  "\023\n\013bmsStopCode\030\t \001(\r\022\032\n\022chgReconnectEnab"
  "le\030\n \001(\r\022\027\n\017reconnectEnable\030\013 \001(\r\"\351\002\n\013BM"
  "SCharging\022\020\n\010bmsReady\030\001 \001(\r\022\024\n\014chargerRe"
  "ady\030\002 \001(\r\022\022\n\nchargeMode\030\003 \001(\r\022\036\n\026resonFo"
  "rCapacityChange\030\004 \001(\r\022\020\n\010bmsPause\030\005 \001(\r\022"
  "\020\n\010chgPause\030\006 \001(\r\022\021\n\tbmsBatVol\030\007 \001(\002\022\021\n\t"
  "volDemand\030\010 \001(\002\022\021\n\tcurDemand\030\t \001(\002\022\016\n\006so"
  "cNow\030\n \001(\002\022\027\n\017currOutCapacity\030\013 \001(\002\022\023\n\013v"
  "olMeasured\030\014 \001(\002\022\023\n\013curMeasured\030\r \001(\002\022\025\n"
  "\rmonoBatVolMax\030\016 \001(\002\022\025\n\rmonoBatVolMin\030\017 "
  "\001(\002\022\017\n\007tempMax\030\020 \001(\002\022\017\n\007tempMin\030\021 \001(\002\"\321\004"
  "\n\013bms2015pMsg\022.\n\010bmsState\030\001 \001(\0162\034.BMS201"
  "5PlusInfo.ChargeState\0227\n\016bmsProtoConfer\030"
  "\002 \001(\0132\037.BMS2015PlusInfo.ProtoConferMsg\0223"
  "\n\014bmsFunConfer\030\003 \001(\0132\035.BMS2015PlusInfo.F"
  "unConferMsg\022-\n\tBmsConfgM\030\004 \001(\0132\032.BMS2015"
  "PlusInfo.BMSConfig\0222\n\014BmsChargingM\030\005 \001(\013"
  "2\034.BMS2015PlusInfo.BMSCharging\0229\n\020BmsCha"
  "rgeFinishM\030\006 \001(\0132\037.BMS2015PlusInfo.BMSCh"
  "argingEnd\022.\n\nBmsAuthenM\030\007 \001(\0132\032.BMS2015P"
  "lusInfo.AuthenMsg\0220\n\013BmsReserveM\030\010 \001(\0132\033"
  ".BMS2015PlusInfo.ReserveMsg\0224\n\rBmsSelfCh"
  "eckM\030\t \001(\0132\035.BMS2015PlusInfo.SelfcheckMs"
  "g\0227\n\020BmsVehicelStateM\030\n \001(\0132\035.BMS2015Plu"
  "sInfo.VehicelState\0225\n\014PowerSupplyM\030\013 \001(\013"
  "2\037.BMS2015PlusInfo.PowerSupplyMsg*\340\001\n\013Ch"
  "argeState\022\020\n\014DefaultState\020\000\022\020\n\014ChargeCre"
  "ate\020\001\022\017\n\013ProtoConfer\020\002\022\022\n\016FunctionConfer"
  "\020\003\022\022\n\016Authentication\020\004\022\020\n\014AppointStage\020\005"
  "\022\r\n\tSelfCheck\020\006\022\017\n\013PowerSupply\020\007\022\r\n\tPreC"
  "harge\020\010\022\022\n\016EnergyTransfer\020\t\022\017\n\013TransferE"
  "nd\020\n\022\016\n\nChgDestory\020\013b\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto = {
  false, false, 3748, descriptor_table_protodef_GCU_5fBMS2015P_5fINFO_2eproto, "GCU_BMS2015P_INFO.proto", 
  &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_once, nullptr, 0, 12,
  schemas, file_default_instances, TableStruct_GCU_5fBMS2015P_5fINFO_2eproto::offsets,
  file_level_metadata_GCU_5fBMS2015P_5fINFO_2eproto, file_level_enum_descriptors_GCU_5fBMS2015P_5fINFO_2eproto, file_level_service_descriptors_GCU_5fBMS2015P_5fINFO_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_getter() {
  return &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_GCU_5fBMS2015P_5fINFO_2eproto(&descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto);
namespace BMS2015PlusInfo {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ChargeState_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fBMS2015P_5fINFO_2eproto[0];
}
bool ChargeState_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class ProtoConferMsg::_Internal {
 public:
};

ProtoConferMsg::ProtoConferMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:BMS2015PlusInfo.ProtoConferMsg)
}
ProtoConferMsg::ProtoConferMsg(const ProtoConferMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  chggbversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_chggbversion().empty()) {
    chggbversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_chggbversion(), 
      GetArenaForAllocation());
  }
  evgbversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_evgbversion().empty()) {
    evgbversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_evgbversion(), 
      GetArenaForAllocation());
  }
  chargerguidanceversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_chargerguidanceversion().empty()) {
    chargerguidanceversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_chargerguidanceversion(), 
      GetArenaForAllocation());
  }
  chargertransportversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_chargertransportversion().empty()) {
    chargertransportversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_chargertransportversion(), 
      GetArenaForAllocation());
  }
  bmsguidanceversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_bmsguidanceversion().empty()) {
    bmsguidanceversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_bmsguidanceversion(), 
      GetArenaForAllocation());
  }
  bmstransportversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_bmstransportversion().empty()) {
    bmstransportversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_bmstransportversion(), 
      GetArenaForAllocation());
  }
  ::memcpy(&chargercantype_, &from.chargercantype_,
    static_cast<size_t>(reinterpret_cast<char*>(&evconferresult_) -
    reinterpret_cast<char*>(&chargercantype_)) + sizeof(evconferresult_));
  // @@protoc_insertion_point(copy_constructor:BMS2015PlusInfo.ProtoConferMsg)
}

inline void ProtoConferMsg::SharedCtor() {
chggbversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
evgbversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
chargerguidanceversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
chargertransportversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
bmsguidanceversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
bmstransportversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&chargercantype_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&evconferresult_) -
    reinterpret_cast<char*>(&chargercantype_)) + sizeof(evconferresult_));
}

ProtoConferMsg::~ProtoConferMsg() {
  // @@protoc_insertion_point(destructor:BMS2015PlusInfo.ProtoConferMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ProtoConferMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  chggbversion_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  evgbversion_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  chargerguidanceversion_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  chargertransportversion_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  bmsguidanceversion_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  bmstransportversion_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ProtoConferMsg::ArenaDtor(void* object) {
  ProtoConferMsg* _this = reinterpret_cast< ProtoConferMsg* >(object);
  (void)_this;
}
void ProtoConferMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ProtoConferMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ProtoConferMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:BMS2015PlusInfo.ProtoConferMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  chggbversion_.ClearToEmpty();
  evgbversion_.ClearToEmpty();
  chargerguidanceversion_.ClearToEmpty();
  chargertransportversion_.ClearToEmpty();
  bmsguidanceversion_.ClearToEmpty();
  bmstransportversion_.ClearToEmpty();
  ::memset(&chargercantype_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&evconferresult_) -
      reinterpret_cast<char*>(&chargercantype_)) + sizeof(evconferresult_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ProtoConferMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 chargerCanType = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          chargercantype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 bmsCanType = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          bmscantype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 chargerConferResult = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          chargerconferresult_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 EVConferResult = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          evconferresult_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes chgGbVersion = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          auto str = _internal_mutable_chggbversion();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes EvGbVersion = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          auto str = _internal_mutable_evgbversion();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes chargerGuidanceVersion = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          auto str = _internal_mutable_chargerguidanceversion();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes chargerTransportVersion = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          auto str = _internal_mutable_chargertransportversion();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes bmsGuidanceVersion = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          auto str = _internal_mutable_bmsguidanceversion();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes bmsTransportVersion = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 82)) {
          auto str = _internal_mutable_bmstransportversion();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ProtoConferMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:BMS2015PlusInfo.ProtoConferMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 chargerCanType = 1;
  if (this->_internal_chargercantype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_chargercantype(), target);
  }

  // uint32 bmsCanType = 2;
  if (this->_internal_bmscantype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_bmscantype(), target);
  }

  // uint32 chargerConferResult = 3;
  if (this->_internal_chargerconferresult() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_chargerconferresult(), target);
  }

  // uint32 EVConferResult = 4;
  if (this->_internal_evconferresult() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_evconferresult(), target);
  }

  // bytes chgGbVersion = 5;
  if (!this->_internal_chggbversion().empty()) {
    target = stream->WriteBytesMaybeAliased(
        5, this->_internal_chggbversion(), target);
  }

  // bytes EvGbVersion = 6;
  if (!this->_internal_evgbversion().empty()) {
    target = stream->WriteBytesMaybeAliased(
        6, this->_internal_evgbversion(), target);
  }

  // bytes chargerGuidanceVersion = 7;
  if (!this->_internal_chargerguidanceversion().empty()) {
    target = stream->WriteBytesMaybeAliased(
        7, this->_internal_chargerguidanceversion(), target);
  }

  // bytes chargerTransportVersion = 8;
  if (!this->_internal_chargertransportversion().empty()) {
    target = stream->WriteBytesMaybeAliased(
        8, this->_internal_chargertransportversion(), target);
  }

  // bytes bmsGuidanceVersion = 9;
  if (!this->_internal_bmsguidanceversion().empty()) {
    target = stream->WriteBytesMaybeAliased(
        9, this->_internal_bmsguidanceversion(), target);
  }

  // bytes bmsTransportVersion = 10;
  if (!this->_internal_bmstransportversion().empty()) {
    target = stream->WriteBytesMaybeAliased(
        10, this->_internal_bmstransportversion(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:BMS2015PlusInfo.ProtoConferMsg)
  return target;
}

size_t ProtoConferMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:BMS2015PlusInfo.ProtoConferMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes chgGbVersion = 5;
  if (!this->_internal_chggbversion().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_chggbversion());
  }

  // bytes EvGbVersion = 6;
  if (!this->_internal_evgbversion().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_evgbversion());
  }

  // bytes chargerGuidanceVersion = 7;
  if (!this->_internal_chargerguidanceversion().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_chargerguidanceversion());
  }

  // bytes chargerTransportVersion = 8;
  if (!this->_internal_chargertransportversion().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_chargertransportversion());
  }

  // bytes bmsGuidanceVersion = 9;
  if (!this->_internal_bmsguidanceversion().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_bmsguidanceversion());
  }

  // bytes bmsTransportVersion = 10;
  if (!this->_internal_bmstransportversion().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_bmstransportversion());
  }

  // uint32 chargerCanType = 1;
  if (this->_internal_chargercantype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chargercantype());
  }

  // uint32 bmsCanType = 2;
  if (this->_internal_bmscantype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmscantype());
  }

  // uint32 chargerConferResult = 3;
  if (this->_internal_chargerconferresult() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chargerconferresult());
  }

  // uint32 EVConferResult = 4;
  if (this->_internal_evconferresult() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_evconferresult());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ProtoConferMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ProtoConferMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ProtoConferMsg::GetClassData() const { return &_class_data_; }

void ProtoConferMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<ProtoConferMsg *>(to)->MergeFrom(
      static_cast<const ProtoConferMsg &>(from));
}


void ProtoConferMsg::MergeFrom(const ProtoConferMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:BMS2015PlusInfo.ProtoConferMsg)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_chggbversion().empty()) {
    _internal_set_chggbversion(from._internal_chggbversion());
  }
  if (!from._internal_evgbversion().empty()) {
    _internal_set_evgbversion(from._internal_evgbversion());
  }
  if (!from._internal_chargerguidanceversion().empty()) {
    _internal_set_chargerguidanceversion(from._internal_chargerguidanceversion());
  }
  if (!from._internal_chargertransportversion().empty()) {
    _internal_set_chargertransportversion(from._internal_chargertransportversion());
  }
  if (!from._internal_bmsguidanceversion().empty()) {
    _internal_set_bmsguidanceversion(from._internal_bmsguidanceversion());
  }
  if (!from._internal_bmstransportversion().empty()) {
    _internal_set_bmstransportversion(from._internal_bmstransportversion());
  }
  if (from._internal_chargercantype() != 0) {
    _internal_set_chargercantype(from._internal_chargercantype());
  }
  if (from._internal_bmscantype() != 0) {
    _internal_set_bmscantype(from._internal_bmscantype());
  }
  if (from._internal_chargerconferresult() != 0) {
    _internal_set_chargerconferresult(from._internal_chargerconferresult());
  }
  if (from._internal_evconferresult() != 0) {
    _internal_set_evconferresult(from._internal_evconferresult());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ProtoConferMsg::CopyFrom(const ProtoConferMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:BMS2015PlusInfo.ProtoConferMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ProtoConferMsg::IsInitialized() const {
  return true;
}

void ProtoConferMsg::InternalSwap(ProtoConferMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &chggbversion_, GetArenaForAllocation(),
      &other->chggbversion_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &evgbversion_, GetArenaForAllocation(),
      &other->evgbversion_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &chargerguidanceversion_, GetArenaForAllocation(),
      &other->chargerguidanceversion_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &chargertransportversion_, GetArenaForAllocation(),
      &other->chargertransportversion_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &bmsguidanceversion_, GetArenaForAllocation(),
      &other->bmsguidanceversion_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &bmstransportversion_, GetArenaForAllocation(),
      &other->bmstransportversion_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ProtoConferMsg, evconferresult_)
      + sizeof(ProtoConferMsg::evconferresult_)
      - PROTOBUF_FIELD_OFFSET(ProtoConferMsg, chargercantype_)>(
          reinterpret_cast<char*>(&chargercantype_),
          reinterpret_cast<char*>(&other->chargercantype_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ProtoConferMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_getter, &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fBMS2015P_5fINFO_2eproto[0]);
}

// ===================================================================

class FunConferMsg::_Internal {
 public:
};

FunConferMsg::FunConferMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:BMS2015PlusInfo.FunConferMsg)
}
FunConferMsg::FunConferMsg(const FunConferMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&chgconfigfdc_, &from.chgconfigfdc_,
    static_cast<size_t>(reinterpret_cast<char*>(&evendfdc_) -
    reinterpret_cast<char*>(&chgconfigfdc_)) + sizeof(evendfdc_));
  // @@protoc_insertion_point(copy_constructor:BMS2015PlusInfo.FunConferMsg)
}

inline void FunConferMsg::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&chgconfigfdc_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&evendfdc_) -
    reinterpret_cast<char*>(&chgconfigfdc_)) + sizeof(evendfdc_));
}

FunConferMsg::~FunConferMsg() {
  // @@protoc_insertion_point(destructor:BMS2015PlusInfo.FunConferMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void FunConferMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void FunConferMsg::ArenaDtor(void* object) {
  FunConferMsg* _this = reinterpret_cast< FunConferMsg* >(object);
  (void)_this;
}
void FunConferMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void FunConferMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void FunConferMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:BMS2015PlusInfo.FunConferMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&chgconfigfdc_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&evendfdc_) -
      reinterpret_cast<char*>(&chgconfigfdc_)) + sizeof(evendfdc_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* FunConferMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 chgConfigFDC = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          chgconfigfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 chgAuthenFDC = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          chgauthenfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 chgAppointFDC = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          chgappointfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 chgSelfCheckFDC = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          chgselfcheckfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 chgPowerSupplyFDC = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          chgpowersupplyfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 chgEnergyTransferFDC = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          chgenergytransferfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 chgEndFDC = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          chgendfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 EVConfigFDC = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          evconfigfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 EVAuthenFDC = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72)) {
          evauthenfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 EVAppointFDC = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          evappointfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 EVSelfCheckFDC = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          evselfcheckfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 EVPowerSupplyFDC = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 96)) {
          evpowersupplyfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 EVEnergyTransferFDC = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 104)) {
          evenergytransferfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 EVEndFDC = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 112)) {
          evendfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* FunConferMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:BMS2015PlusInfo.FunConferMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 chgConfigFDC = 1;
  if (this->_internal_chgconfigfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_chgconfigfdc(), target);
  }

  // uint32 chgAuthenFDC = 2;
  if (this->_internal_chgauthenfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_chgauthenfdc(), target);
  }

  // uint32 chgAppointFDC = 3;
  if (this->_internal_chgappointfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_chgappointfdc(), target);
  }

  // uint32 chgSelfCheckFDC = 4;
  if (this->_internal_chgselfcheckfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_chgselfcheckfdc(), target);
  }

  // uint32 chgPowerSupplyFDC = 5;
  if (this->_internal_chgpowersupplyfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_chgpowersupplyfdc(), target);
  }

  // uint32 chgEnergyTransferFDC = 6;
  if (this->_internal_chgenergytransferfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_chgenergytransferfdc(), target);
  }

  // uint32 chgEndFDC = 7;
  if (this->_internal_chgendfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(7, this->_internal_chgendfdc(), target);
  }

  // uint32 EVConfigFDC = 8;
  if (this->_internal_evconfigfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_evconfigfdc(), target);
  }

  // uint32 EVAuthenFDC = 9;
  if (this->_internal_evauthenfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(9, this->_internal_evauthenfdc(), target);
  }

  // uint32 EVAppointFDC = 10;
  if (this->_internal_evappointfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(10, this->_internal_evappointfdc(), target);
  }

  // uint32 EVSelfCheckFDC = 11;
  if (this->_internal_evselfcheckfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(11, this->_internal_evselfcheckfdc(), target);
  }

  // uint32 EVPowerSupplyFDC = 12;
  if (this->_internal_evpowersupplyfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(12, this->_internal_evpowersupplyfdc(), target);
  }

  // uint32 EVEnergyTransferFDC = 13;
  if (this->_internal_evenergytransferfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(13, this->_internal_evenergytransferfdc(), target);
  }

  // uint32 EVEndFDC = 14;
  if (this->_internal_evendfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(14, this->_internal_evendfdc(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:BMS2015PlusInfo.FunConferMsg)
  return target;
}

size_t FunConferMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:BMS2015PlusInfo.FunConferMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 chgConfigFDC = 1;
  if (this->_internal_chgconfigfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chgconfigfdc());
  }

  // uint32 chgAuthenFDC = 2;
  if (this->_internal_chgauthenfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chgauthenfdc());
  }

  // uint32 chgAppointFDC = 3;
  if (this->_internal_chgappointfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chgappointfdc());
  }

  // uint32 chgSelfCheckFDC = 4;
  if (this->_internal_chgselfcheckfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chgselfcheckfdc());
  }

  // uint32 chgPowerSupplyFDC = 5;
  if (this->_internal_chgpowersupplyfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chgpowersupplyfdc());
  }

  // uint32 chgEnergyTransferFDC = 6;
  if (this->_internal_chgenergytransferfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chgenergytransferfdc());
  }

  // uint32 chgEndFDC = 7;
  if (this->_internal_chgendfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chgendfdc());
  }

  // uint32 EVConfigFDC = 8;
  if (this->_internal_evconfigfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_evconfigfdc());
  }

  // uint32 EVAuthenFDC = 9;
  if (this->_internal_evauthenfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_evauthenfdc());
  }

  // uint32 EVAppointFDC = 10;
  if (this->_internal_evappointfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_evappointfdc());
  }

  // uint32 EVSelfCheckFDC = 11;
  if (this->_internal_evselfcheckfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_evselfcheckfdc());
  }

  // uint32 EVPowerSupplyFDC = 12;
  if (this->_internal_evpowersupplyfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_evpowersupplyfdc());
  }

  // uint32 EVEnergyTransferFDC = 13;
  if (this->_internal_evenergytransferfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_evenergytransferfdc());
  }

  // uint32 EVEndFDC = 14;
  if (this->_internal_evendfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_evendfdc());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData FunConferMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    FunConferMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*FunConferMsg::GetClassData() const { return &_class_data_; }

void FunConferMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<FunConferMsg *>(to)->MergeFrom(
      static_cast<const FunConferMsg &>(from));
}


void FunConferMsg::MergeFrom(const FunConferMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:BMS2015PlusInfo.FunConferMsg)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_chgconfigfdc() != 0) {
    _internal_set_chgconfigfdc(from._internal_chgconfigfdc());
  }
  if (from._internal_chgauthenfdc() != 0) {
    _internal_set_chgauthenfdc(from._internal_chgauthenfdc());
  }
  if (from._internal_chgappointfdc() != 0) {
    _internal_set_chgappointfdc(from._internal_chgappointfdc());
  }
  if (from._internal_chgselfcheckfdc() != 0) {
    _internal_set_chgselfcheckfdc(from._internal_chgselfcheckfdc());
  }
  if (from._internal_chgpowersupplyfdc() != 0) {
    _internal_set_chgpowersupplyfdc(from._internal_chgpowersupplyfdc());
  }
  if (from._internal_chgenergytransferfdc() != 0) {
    _internal_set_chgenergytransferfdc(from._internal_chgenergytransferfdc());
  }
  if (from._internal_chgendfdc() != 0) {
    _internal_set_chgendfdc(from._internal_chgendfdc());
  }
  if (from._internal_evconfigfdc() != 0) {
    _internal_set_evconfigfdc(from._internal_evconfigfdc());
  }
  if (from._internal_evauthenfdc() != 0) {
    _internal_set_evauthenfdc(from._internal_evauthenfdc());
  }
  if (from._internal_evappointfdc() != 0) {
    _internal_set_evappointfdc(from._internal_evappointfdc());
  }
  if (from._internal_evselfcheckfdc() != 0) {
    _internal_set_evselfcheckfdc(from._internal_evselfcheckfdc());
  }
  if (from._internal_evpowersupplyfdc() != 0) {
    _internal_set_evpowersupplyfdc(from._internal_evpowersupplyfdc());
  }
  if (from._internal_evenergytransferfdc() != 0) {
    _internal_set_evenergytransferfdc(from._internal_evenergytransferfdc());
  }
  if (from._internal_evendfdc() != 0) {
    _internal_set_evendfdc(from._internal_evendfdc());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void FunConferMsg::CopyFrom(const FunConferMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:BMS2015PlusInfo.FunConferMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FunConferMsg::IsInitialized() const {
  return true;
}

void FunConferMsg::InternalSwap(FunConferMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(FunConferMsg, evendfdc_)
      + sizeof(FunConferMsg::evendfdc_)
      - PROTOBUF_FIELD_OFFSET(FunConferMsg, chgconfigfdc_)>(
          reinterpret_cast<char*>(&chgconfigfdc_),
          reinterpret_cast<char*>(&other->chgconfigfdc_));
}

::PROTOBUF_NAMESPACE_ID::Metadata FunConferMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_getter, &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fBMS2015P_5fINFO_2eproto[1]);
}

// ===================================================================

class BMSConfig::_Internal {
 public:
};

BMSConfig::BMSConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:BMS2015PlusInfo.BMSConfig)
}
BMSConfig::BMSConfig(const BMSConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&monovolmaxallowed_, &from.monovolmaxallowed_,
    static_cast<size_t>(reinterpret_cast<char*>(&bmsallowedrestartnum_) -
    reinterpret_cast<char*>(&monovolmaxallowed_)) + sizeof(bmsallowedrestartnum_));
  // @@protoc_insertion_point(copy_constructor:BMS2015PlusInfo.BMSConfig)
}

inline void BMSConfig::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&monovolmaxallowed_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&bmsallowedrestartnum_) -
    reinterpret_cast<char*>(&monovolmaxallowed_)) + sizeof(bmsallowedrestartnum_));
}

BMSConfig::~BMSConfig() {
  // @@protoc_insertion_point(destructor:BMS2015PlusInfo.BMSConfig)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void BMSConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void BMSConfig::ArenaDtor(void* object) {
  BMSConfig* _this = reinterpret_cast< BMSConfig* >(object);
  (void)_this;
}
void BMSConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void BMSConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void BMSConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:BMS2015PlusInfo.BMSConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&monovolmaxallowed_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&bmsallowedrestartnum_) -
      reinterpret_cast<char*>(&monovolmaxallowed_)) + sizeof(bmsallowedrestartnum_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* BMSConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float monoVolMaxAllowed = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          monovolmaxallowed_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float curAllowedMax = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 21)) {
          curallowedmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float energyAllowdMax = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          energyallowdmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float volAllowedMax = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 37)) {
          volallowedmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float tempAllowedMax = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 45)) {
          tempallowedmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float startSOC = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 53)) {
          startsoc_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float volBatNow = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 61)) {
          volbatnow_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float volChargerMax = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 69)) {
          volchargermax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float volChargerMin = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 77)) {
          volchargermin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float curChargerMax = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 85)) {
          curchargermax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float curChargerMin = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 93)) {
          curchargermin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 currUpRate = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 96)) {
          curruprate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 currDownRate = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 104)) {
          currdownrate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 chargerAllowedRestartNum = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 112)) {
          chargerallowedrestartnum_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 bmsAllowedRestartNum = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 120)) {
          bmsallowedrestartnum_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* BMSConfig::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:BMS2015PlusInfo.BMSConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float monoVolMaxAllowed = 1;
  if (!(this->_internal_monovolmaxallowed() <= 0 && this->_internal_monovolmaxallowed() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_monovolmaxallowed(), target);
  }

  // float curAllowedMax = 2;
  if (!(this->_internal_curallowedmax() <= 0 && this->_internal_curallowedmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_curallowedmax(), target);
  }

  // float energyAllowdMax = 3;
  if (!(this->_internal_energyallowdmax() <= 0 && this->_internal_energyallowdmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_energyallowdmax(), target);
  }

  // float volAllowedMax = 4;
  if (!(this->_internal_volallowedmax() <= 0 && this->_internal_volallowedmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_volallowedmax(), target);
  }

  // float tempAllowedMax = 5;
  if (!(this->_internal_tempallowedmax() <= 0 && this->_internal_tempallowedmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(5, this->_internal_tempallowedmax(), target);
  }

  // float startSOC = 6;
  if (!(this->_internal_startsoc() <= 0 && this->_internal_startsoc() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(6, this->_internal_startsoc(), target);
  }

  // float volBatNow = 7;
  if (!(this->_internal_volbatnow() <= 0 && this->_internal_volbatnow() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(7, this->_internal_volbatnow(), target);
  }

  // float volChargerMax = 8;
  if (!(this->_internal_volchargermax() <= 0 && this->_internal_volchargermax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(8, this->_internal_volchargermax(), target);
  }

  // float volChargerMin = 9;
  if (!(this->_internal_volchargermin() <= 0 && this->_internal_volchargermin() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(9, this->_internal_volchargermin(), target);
  }

  // float curChargerMax = 10;
  if (!(this->_internal_curchargermax() <= 0 && this->_internal_curchargermax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(10, this->_internal_curchargermax(), target);
  }

  // float curChargerMin = 11;
  if (!(this->_internal_curchargermin() <= 0 && this->_internal_curchargermin() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(11, this->_internal_curchargermin(), target);
  }

  // uint32 currUpRate = 12;
  if (this->_internal_curruprate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(12, this->_internal_curruprate(), target);
  }

  // uint32 currDownRate = 13;
  if (this->_internal_currdownrate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(13, this->_internal_currdownrate(), target);
  }

  // uint32 chargerAllowedRestartNum = 14;
  if (this->_internal_chargerallowedrestartnum() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(14, this->_internal_chargerallowedrestartnum(), target);
  }

  // uint32 bmsAllowedRestartNum = 15;
  if (this->_internal_bmsallowedrestartnum() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(15, this->_internal_bmsallowedrestartnum(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:BMS2015PlusInfo.BMSConfig)
  return target;
}

size_t BMSConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:BMS2015PlusInfo.BMSConfig)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float monoVolMaxAllowed = 1;
  if (!(this->_internal_monovolmaxallowed() <= 0 && this->_internal_monovolmaxallowed() >= 0)) {
    total_size += 1 + 4;
  }

  // float curAllowedMax = 2;
  if (!(this->_internal_curallowedmax() <= 0 && this->_internal_curallowedmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float energyAllowdMax = 3;
  if (!(this->_internal_energyallowdmax() <= 0 && this->_internal_energyallowdmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float volAllowedMax = 4;
  if (!(this->_internal_volallowedmax() <= 0 && this->_internal_volallowedmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float tempAllowedMax = 5;
  if (!(this->_internal_tempallowedmax() <= 0 && this->_internal_tempallowedmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float startSOC = 6;
  if (!(this->_internal_startsoc() <= 0 && this->_internal_startsoc() >= 0)) {
    total_size += 1 + 4;
  }

  // float volBatNow = 7;
  if (!(this->_internal_volbatnow() <= 0 && this->_internal_volbatnow() >= 0)) {
    total_size += 1 + 4;
  }

  // float volChargerMax = 8;
  if (!(this->_internal_volchargermax() <= 0 && this->_internal_volchargermax() >= 0)) {
    total_size += 1 + 4;
  }

  // float volChargerMin = 9;
  if (!(this->_internal_volchargermin() <= 0 && this->_internal_volchargermin() >= 0)) {
    total_size += 1 + 4;
  }

  // float curChargerMax = 10;
  if (!(this->_internal_curchargermax() <= 0 && this->_internal_curchargermax() >= 0)) {
    total_size += 1 + 4;
  }

  // float curChargerMin = 11;
  if (!(this->_internal_curchargermin() <= 0 && this->_internal_curchargermin() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 currUpRate = 12;
  if (this->_internal_curruprate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_curruprate());
  }

  // uint32 currDownRate = 13;
  if (this->_internal_currdownrate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_currdownrate());
  }

  // uint32 chargerAllowedRestartNum = 14;
  if (this->_internal_chargerallowedrestartnum() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chargerallowedrestartnum());
  }

  // uint32 bmsAllowedRestartNum = 15;
  if (this->_internal_bmsallowedrestartnum() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsallowedrestartnum());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData BMSConfig::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    BMSConfig::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*BMSConfig::GetClassData() const { return &_class_data_; }

void BMSConfig::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<BMSConfig *>(to)->MergeFrom(
      static_cast<const BMSConfig &>(from));
}


void BMSConfig::MergeFrom(const BMSConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:BMS2015PlusInfo.BMSConfig)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from._internal_monovolmaxallowed() <= 0 && from._internal_monovolmaxallowed() >= 0)) {
    _internal_set_monovolmaxallowed(from._internal_monovolmaxallowed());
  }
  if (!(from._internal_curallowedmax() <= 0 && from._internal_curallowedmax() >= 0)) {
    _internal_set_curallowedmax(from._internal_curallowedmax());
  }
  if (!(from._internal_energyallowdmax() <= 0 && from._internal_energyallowdmax() >= 0)) {
    _internal_set_energyallowdmax(from._internal_energyallowdmax());
  }
  if (!(from._internal_volallowedmax() <= 0 && from._internal_volallowedmax() >= 0)) {
    _internal_set_volallowedmax(from._internal_volallowedmax());
  }
  if (!(from._internal_tempallowedmax() <= 0 && from._internal_tempallowedmax() >= 0)) {
    _internal_set_tempallowedmax(from._internal_tempallowedmax());
  }
  if (!(from._internal_startsoc() <= 0 && from._internal_startsoc() >= 0)) {
    _internal_set_startsoc(from._internal_startsoc());
  }
  if (!(from._internal_volbatnow() <= 0 && from._internal_volbatnow() >= 0)) {
    _internal_set_volbatnow(from._internal_volbatnow());
  }
  if (!(from._internal_volchargermax() <= 0 && from._internal_volchargermax() >= 0)) {
    _internal_set_volchargermax(from._internal_volchargermax());
  }
  if (!(from._internal_volchargermin() <= 0 && from._internal_volchargermin() >= 0)) {
    _internal_set_volchargermin(from._internal_volchargermin());
  }
  if (!(from._internal_curchargermax() <= 0 && from._internal_curchargermax() >= 0)) {
    _internal_set_curchargermax(from._internal_curchargermax());
  }
  if (!(from._internal_curchargermin() <= 0 && from._internal_curchargermin() >= 0)) {
    _internal_set_curchargermin(from._internal_curchargermin());
  }
  if (from._internal_curruprate() != 0) {
    _internal_set_curruprate(from._internal_curruprate());
  }
  if (from._internal_currdownrate() != 0) {
    _internal_set_currdownrate(from._internal_currdownrate());
  }
  if (from._internal_chargerallowedrestartnum() != 0) {
    _internal_set_chargerallowedrestartnum(from._internal_chargerallowedrestartnum());
  }
  if (from._internal_bmsallowedrestartnum() != 0) {
    _internal_set_bmsallowedrestartnum(from._internal_bmsallowedrestartnum());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void BMSConfig::CopyFrom(const BMSConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:BMS2015PlusInfo.BMSConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BMSConfig::IsInitialized() const {
  return true;
}

void BMSConfig::InternalSwap(BMSConfig* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(BMSConfig, bmsallowedrestartnum_)
      + sizeof(BMSConfig::bmsallowedrestartnum_)
      - PROTOBUF_FIELD_OFFSET(BMSConfig, monovolmaxallowed_)>(
          reinterpret_cast<char*>(&monovolmaxallowed_),
          reinterpret_cast<char*>(&other->monovolmaxallowed_));
}

::PROTOBUF_NAMESPACE_ID::Metadata BMSConfig::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_getter, &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fBMS2015P_5fINFO_2eproto[2]);
}

// ===================================================================

class DischargeConfig::_Internal {
 public:
};

DischargeConfig::DischargeConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:BMS2015PlusInfo.DischargeConfig)
}
DischargeConfig::DischargeConfig(const DischargeConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&voldischargemax_, &from.voldischargemax_,
    static_cast<size_t>(reinterpret_cast<char*>(&bmsallowedrestartnum_) -
    reinterpret_cast<char*>(&voldischargemax_)) + sizeof(bmsallowedrestartnum_));
  // @@protoc_insertion_point(copy_constructor:BMS2015PlusInfo.DischargeConfig)
}

inline void DischargeConfig::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&voldischargemax_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&bmsallowedrestartnum_) -
    reinterpret_cast<char*>(&voldischargemax_)) + sizeof(bmsallowedrestartnum_));
}

DischargeConfig::~DischargeConfig() {
  // @@protoc_insertion_point(destructor:BMS2015PlusInfo.DischargeConfig)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DischargeConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void DischargeConfig::ArenaDtor(void* object) {
  DischargeConfig* _this = reinterpret_cast< DischargeConfig* >(object);
  (void)_this;
}
void DischargeConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DischargeConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DischargeConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:BMS2015PlusInfo.DischargeConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&voldischargemax_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&bmsallowedrestartnum_) -
      reinterpret_cast<char*>(&voldischargemax_)) + sizeof(bmsallowedrestartnum_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DischargeConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float volDischargeMax = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          voldischargemax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float volDischargeMin = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 21)) {
          voldischargemin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float curDischargeMax = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          curdischargemax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float curDischargeMin = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 37)) {
          curdischargemin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float volBmsAllowedMin = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 45)) {
          volbmsallowedmin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float curBmsAllowedMax = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 53)) {
          curbmsallowedmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float curBmsAllowedMin = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 61)) {
          curbmsallowedmin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float volCellAllowedMax = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 69)) {
          volcellallowedmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float volCellAllowedMin = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 77)) {
          volcellallowedmin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float socAllowedMin = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 85)) {
          socallowedmin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float totalBatteryCycleNum = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 93)) {
          totalbatterycyclenum_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float allowedBatteryCycleNum = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 101)) {
          allowedbatterycyclenum_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float desireResidueRange = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 109)) {
          desireresiduerange_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float tempAllowedMax = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 117)) {
          tempallowedmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 chargerAllowedRestartNum = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 120)) {
          chargerallowedrestartnum_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 bmsAllowedRestartNum = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 128)) {
          bmsallowedrestartnum_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* DischargeConfig::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:BMS2015PlusInfo.DischargeConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float volDischargeMax = 1;
  if (!(this->_internal_voldischargemax() <= 0 && this->_internal_voldischargemax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_voldischargemax(), target);
  }

  // float volDischargeMin = 2;
  if (!(this->_internal_voldischargemin() <= 0 && this->_internal_voldischargemin() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_voldischargemin(), target);
  }

  // float curDischargeMax = 3;
  if (!(this->_internal_curdischargemax() <= 0 && this->_internal_curdischargemax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_curdischargemax(), target);
  }

  // float curDischargeMin = 4;
  if (!(this->_internal_curdischargemin() <= 0 && this->_internal_curdischargemin() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_curdischargemin(), target);
  }

  // float volBmsAllowedMin = 5;
  if (!(this->_internal_volbmsallowedmin() <= 0 && this->_internal_volbmsallowedmin() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(5, this->_internal_volbmsallowedmin(), target);
  }

  // float curBmsAllowedMax = 6;
  if (!(this->_internal_curbmsallowedmax() <= 0 && this->_internal_curbmsallowedmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(6, this->_internal_curbmsallowedmax(), target);
  }

  // float curBmsAllowedMin = 7;
  if (!(this->_internal_curbmsallowedmin() <= 0 && this->_internal_curbmsallowedmin() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(7, this->_internal_curbmsallowedmin(), target);
  }

  // float volCellAllowedMax = 8;
  if (!(this->_internal_volcellallowedmax() <= 0 && this->_internal_volcellallowedmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(8, this->_internal_volcellallowedmax(), target);
  }

  // float volCellAllowedMin = 9;
  if (!(this->_internal_volcellallowedmin() <= 0 && this->_internal_volcellallowedmin() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(9, this->_internal_volcellallowedmin(), target);
  }

  // float socAllowedMin = 10;
  if (!(this->_internal_socallowedmin() <= 0 && this->_internal_socallowedmin() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(10, this->_internal_socallowedmin(), target);
  }

  // float totalBatteryCycleNum = 11;
  if (!(this->_internal_totalbatterycyclenum() <= 0 && this->_internal_totalbatterycyclenum() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(11, this->_internal_totalbatterycyclenum(), target);
  }

  // float allowedBatteryCycleNum = 12;
  if (!(this->_internal_allowedbatterycyclenum() <= 0 && this->_internal_allowedbatterycyclenum() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(12, this->_internal_allowedbatterycyclenum(), target);
  }

  // float desireResidueRange = 13;
  if (!(this->_internal_desireresiduerange() <= 0 && this->_internal_desireresiduerange() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(13, this->_internal_desireresiduerange(), target);
  }

  // float tempAllowedMax = 14;
  if (!(this->_internal_tempallowedmax() <= 0 && this->_internal_tempallowedmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(14, this->_internal_tempallowedmax(), target);
  }

  // uint32 chargerAllowedRestartNum = 15;
  if (this->_internal_chargerallowedrestartnum() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(15, this->_internal_chargerallowedrestartnum(), target);
  }

  // uint32 bmsAllowedRestartNum = 16;
  if (this->_internal_bmsallowedrestartnum() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(16, this->_internal_bmsallowedrestartnum(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:BMS2015PlusInfo.DischargeConfig)
  return target;
}

size_t DischargeConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:BMS2015PlusInfo.DischargeConfig)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float volDischargeMax = 1;
  if (!(this->_internal_voldischargemax() <= 0 && this->_internal_voldischargemax() >= 0)) {
    total_size += 1 + 4;
  }

  // float volDischargeMin = 2;
  if (!(this->_internal_voldischargemin() <= 0 && this->_internal_voldischargemin() >= 0)) {
    total_size += 1 + 4;
  }

  // float curDischargeMax = 3;
  if (!(this->_internal_curdischargemax() <= 0 && this->_internal_curdischargemax() >= 0)) {
    total_size += 1 + 4;
  }

  // float curDischargeMin = 4;
  if (!(this->_internal_curdischargemin() <= 0 && this->_internal_curdischargemin() >= 0)) {
    total_size += 1 + 4;
  }

  // float volBmsAllowedMin = 5;
  if (!(this->_internal_volbmsallowedmin() <= 0 && this->_internal_volbmsallowedmin() >= 0)) {
    total_size += 1 + 4;
  }

  // float curBmsAllowedMax = 6;
  if (!(this->_internal_curbmsallowedmax() <= 0 && this->_internal_curbmsallowedmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float curBmsAllowedMin = 7;
  if (!(this->_internal_curbmsallowedmin() <= 0 && this->_internal_curbmsallowedmin() >= 0)) {
    total_size += 1 + 4;
  }

  // float volCellAllowedMax = 8;
  if (!(this->_internal_volcellallowedmax() <= 0 && this->_internal_volcellallowedmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float volCellAllowedMin = 9;
  if (!(this->_internal_volcellallowedmin() <= 0 && this->_internal_volcellallowedmin() >= 0)) {
    total_size += 1 + 4;
  }

  // float socAllowedMin = 10;
  if (!(this->_internal_socallowedmin() <= 0 && this->_internal_socallowedmin() >= 0)) {
    total_size += 1 + 4;
  }

  // float totalBatteryCycleNum = 11;
  if (!(this->_internal_totalbatterycyclenum() <= 0 && this->_internal_totalbatterycyclenum() >= 0)) {
    total_size += 1 + 4;
  }

  // float allowedBatteryCycleNum = 12;
  if (!(this->_internal_allowedbatterycyclenum() <= 0 && this->_internal_allowedbatterycyclenum() >= 0)) {
    total_size += 1 + 4;
  }

  // float desireResidueRange = 13;
  if (!(this->_internal_desireresiduerange() <= 0 && this->_internal_desireresiduerange() >= 0)) {
    total_size += 1 + 4;
  }

  // float tempAllowedMax = 14;
  if (!(this->_internal_tempallowedmax() <= 0 && this->_internal_tempallowedmax() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 chargerAllowedRestartNum = 15;
  if (this->_internal_chargerallowedrestartnum() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chargerallowedrestartnum());
  }

  // uint32 bmsAllowedRestartNum = 16;
  if (this->_internal_bmsallowedrestartnum() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsallowedrestartnum());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DischargeConfig::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DischargeConfig::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DischargeConfig::GetClassData() const { return &_class_data_; }

void DischargeConfig::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<DischargeConfig *>(to)->MergeFrom(
      static_cast<const DischargeConfig &>(from));
}


void DischargeConfig::MergeFrom(const DischargeConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:BMS2015PlusInfo.DischargeConfig)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from._internal_voldischargemax() <= 0 && from._internal_voldischargemax() >= 0)) {
    _internal_set_voldischargemax(from._internal_voldischargemax());
  }
  if (!(from._internal_voldischargemin() <= 0 && from._internal_voldischargemin() >= 0)) {
    _internal_set_voldischargemin(from._internal_voldischargemin());
  }
  if (!(from._internal_curdischargemax() <= 0 && from._internal_curdischargemax() >= 0)) {
    _internal_set_curdischargemax(from._internal_curdischargemax());
  }
  if (!(from._internal_curdischargemin() <= 0 && from._internal_curdischargemin() >= 0)) {
    _internal_set_curdischargemin(from._internal_curdischargemin());
  }
  if (!(from._internal_volbmsallowedmin() <= 0 && from._internal_volbmsallowedmin() >= 0)) {
    _internal_set_volbmsallowedmin(from._internal_volbmsallowedmin());
  }
  if (!(from._internal_curbmsallowedmax() <= 0 && from._internal_curbmsallowedmax() >= 0)) {
    _internal_set_curbmsallowedmax(from._internal_curbmsallowedmax());
  }
  if (!(from._internal_curbmsallowedmin() <= 0 && from._internal_curbmsallowedmin() >= 0)) {
    _internal_set_curbmsallowedmin(from._internal_curbmsallowedmin());
  }
  if (!(from._internal_volcellallowedmax() <= 0 && from._internal_volcellallowedmax() >= 0)) {
    _internal_set_volcellallowedmax(from._internal_volcellallowedmax());
  }
  if (!(from._internal_volcellallowedmin() <= 0 && from._internal_volcellallowedmin() >= 0)) {
    _internal_set_volcellallowedmin(from._internal_volcellallowedmin());
  }
  if (!(from._internal_socallowedmin() <= 0 && from._internal_socallowedmin() >= 0)) {
    _internal_set_socallowedmin(from._internal_socallowedmin());
  }
  if (!(from._internal_totalbatterycyclenum() <= 0 && from._internal_totalbatterycyclenum() >= 0)) {
    _internal_set_totalbatterycyclenum(from._internal_totalbatterycyclenum());
  }
  if (!(from._internal_allowedbatterycyclenum() <= 0 && from._internal_allowedbatterycyclenum() >= 0)) {
    _internal_set_allowedbatterycyclenum(from._internal_allowedbatterycyclenum());
  }
  if (!(from._internal_desireresiduerange() <= 0 && from._internal_desireresiduerange() >= 0)) {
    _internal_set_desireresiduerange(from._internal_desireresiduerange());
  }
  if (!(from._internal_tempallowedmax() <= 0 && from._internal_tempallowedmax() >= 0)) {
    _internal_set_tempallowedmax(from._internal_tempallowedmax());
  }
  if (from._internal_chargerallowedrestartnum() != 0) {
    _internal_set_chargerallowedrestartnum(from._internal_chargerallowedrestartnum());
  }
  if (from._internal_bmsallowedrestartnum() != 0) {
    _internal_set_bmsallowedrestartnum(from._internal_bmsallowedrestartnum());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DischargeConfig::CopyFrom(const DischargeConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:BMS2015PlusInfo.DischargeConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DischargeConfig::IsInitialized() const {
  return true;
}

void DischargeConfig::InternalSwap(DischargeConfig* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DischargeConfig, bmsallowedrestartnum_)
      + sizeof(DischargeConfig::bmsallowedrestartnum_)
      - PROTOBUF_FIELD_OFFSET(DischargeConfig, voldischargemax_)>(
          reinterpret_cast<char*>(&voldischargemax_),
          reinterpret_cast<char*>(&other->voldischargemax_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DischargeConfig::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_getter, &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fBMS2015P_5fINFO_2eproto[3]);
}

// ===================================================================

class AuthenMsg::_Internal {
 public:
};

AuthenMsg::AuthenMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:BMS2015PlusInfo.AuthenMsg)
}
AuthenMsg::AuthenMsg(const AuthenMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  evin_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_evin().empty()) {
    evin_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_evin(), 
      GetArenaForAllocation());
  }
  batproducer_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_batproducer().empty()) {
    batproducer_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_batproducer(), 
      GetArenaForAllocation());
  }
  chargeroperators_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_chargeroperators().empty()) {
    chargeroperators_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_chargeroperators(), 
      GetArenaForAllocation());
  }
  chargernumber_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_chargernumber().empty()) {
    chargernumber_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_chargernumber(), 
      GetArenaForAllocation());
  }
  ::memcpy(&nowfdc_, &from.nowfdc_,
    static_cast<size_t>(reinterpret_cast<char*>(&succeedauthenfdc_) -
    reinterpret_cast<char*>(&nowfdc_)) + sizeof(succeedauthenfdc_));
  // @@protoc_insertion_point(copy_constructor:BMS2015PlusInfo.AuthenMsg)
}

inline void AuthenMsg::SharedCtor() {
evin_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
batproducer_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
chargeroperators_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
chargernumber_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&nowfdc_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&succeedauthenfdc_) -
    reinterpret_cast<char*>(&nowfdc_)) + sizeof(succeedauthenfdc_));
}

AuthenMsg::~AuthenMsg() {
  // @@protoc_insertion_point(destructor:BMS2015PlusInfo.AuthenMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void AuthenMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  evin_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  batproducer_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  chargeroperators_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  chargernumber_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void AuthenMsg::ArenaDtor(void* object) {
  AuthenMsg* _this = reinterpret_cast< AuthenMsg* >(object);
  (void)_this;
}
void AuthenMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void AuthenMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AuthenMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:BMS2015PlusInfo.AuthenMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  evin_.ClearToEmpty();
  batproducer_.ClearToEmpty();
  chargeroperators_.ClearToEmpty();
  chargernumber_.ClearToEmpty();
  ::memset(&nowfdc_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&succeedauthenfdc_) -
      reinterpret_cast<char*>(&nowfdc_)) + sizeof(succeedauthenfdc_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AuthenMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 nowFDC = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          nowfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 authenWaitTime = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          authenwaittime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 bmsWaitAuthenState = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          bmswaitauthenstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 authenResult = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          authenresult_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 succeedAuthenFDC = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          succeedauthenfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes eVIN = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          auto str = _internal_mutable_evin();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes batProducer = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          auto str = _internal_mutable_batproducer();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes chargerOperators = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          auto str = _internal_mutable_chargeroperators();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes chargerNumber = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          auto str = _internal_mutable_chargernumber();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* AuthenMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:BMS2015PlusInfo.AuthenMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 nowFDC = 1;
  if (this->_internal_nowfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_nowfdc(), target);
  }

  // uint32 authenWaitTime = 2;
  if (this->_internal_authenwaittime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_authenwaittime(), target);
  }

  // uint32 bmsWaitAuthenState = 3;
  if (this->_internal_bmswaitauthenstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_bmswaitauthenstate(), target);
  }

  // uint32 authenResult = 4;
  if (this->_internal_authenresult() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_authenresult(), target);
  }

  // uint32 succeedAuthenFDC = 5;
  if (this->_internal_succeedauthenfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_succeedauthenfdc(), target);
  }

  // bytes eVIN = 6;
  if (!this->_internal_evin().empty()) {
    target = stream->WriteBytesMaybeAliased(
        6, this->_internal_evin(), target);
  }

  // bytes batProducer = 7;
  if (!this->_internal_batproducer().empty()) {
    target = stream->WriteBytesMaybeAliased(
        7, this->_internal_batproducer(), target);
  }

  // bytes chargerOperators = 8;
  if (!this->_internal_chargeroperators().empty()) {
    target = stream->WriteBytesMaybeAliased(
        8, this->_internal_chargeroperators(), target);
  }

  // bytes chargerNumber = 9;
  if (!this->_internal_chargernumber().empty()) {
    target = stream->WriteBytesMaybeAliased(
        9, this->_internal_chargernumber(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:BMS2015PlusInfo.AuthenMsg)
  return target;
}

size_t AuthenMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:BMS2015PlusInfo.AuthenMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes eVIN = 6;
  if (!this->_internal_evin().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_evin());
  }

  // bytes batProducer = 7;
  if (!this->_internal_batproducer().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_batproducer());
  }

  // bytes chargerOperators = 8;
  if (!this->_internal_chargeroperators().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_chargeroperators());
  }

  // bytes chargerNumber = 9;
  if (!this->_internal_chargernumber().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_chargernumber());
  }

  // uint32 nowFDC = 1;
  if (this->_internal_nowfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_nowfdc());
  }

  // uint32 authenWaitTime = 2;
  if (this->_internal_authenwaittime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_authenwaittime());
  }

  // uint32 bmsWaitAuthenState = 3;
  if (this->_internal_bmswaitauthenstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmswaitauthenstate());
  }

  // uint32 authenResult = 4;
  if (this->_internal_authenresult() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_authenresult());
  }

  // uint32 succeedAuthenFDC = 5;
  if (this->_internal_succeedauthenfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_succeedauthenfdc());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AuthenMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    AuthenMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AuthenMsg::GetClassData() const { return &_class_data_; }

void AuthenMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<AuthenMsg *>(to)->MergeFrom(
      static_cast<const AuthenMsg &>(from));
}


void AuthenMsg::MergeFrom(const AuthenMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:BMS2015PlusInfo.AuthenMsg)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_evin().empty()) {
    _internal_set_evin(from._internal_evin());
  }
  if (!from._internal_batproducer().empty()) {
    _internal_set_batproducer(from._internal_batproducer());
  }
  if (!from._internal_chargeroperators().empty()) {
    _internal_set_chargeroperators(from._internal_chargeroperators());
  }
  if (!from._internal_chargernumber().empty()) {
    _internal_set_chargernumber(from._internal_chargernumber());
  }
  if (from._internal_nowfdc() != 0) {
    _internal_set_nowfdc(from._internal_nowfdc());
  }
  if (from._internal_authenwaittime() != 0) {
    _internal_set_authenwaittime(from._internal_authenwaittime());
  }
  if (from._internal_bmswaitauthenstate() != 0) {
    _internal_set_bmswaitauthenstate(from._internal_bmswaitauthenstate());
  }
  if (from._internal_authenresult() != 0) {
    _internal_set_authenresult(from._internal_authenresult());
  }
  if (from._internal_succeedauthenfdc() != 0) {
    _internal_set_succeedauthenfdc(from._internal_succeedauthenfdc());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AuthenMsg::CopyFrom(const AuthenMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:BMS2015PlusInfo.AuthenMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AuthenMsg::IsInitialized() const {
  return true;
}

void AuthenMsg::InternalSwap(AuthenMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &evin_, GetArenaForAllocation(),
      &other->evin_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &batproducer_, GetArenaForAllocation(),
      &other->batproducer_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &chargeroperators_, GetArenaForAllocation(),
      &other->chargeroperators_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &chargernumber_, GetArenaForAllocation(),
      &other->chargernumber_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(AuthenMsg, succeedauthenfdc_)
      + sizeof(AuthenMsg::succeedauthenfdc_)
      - PROTOBUF_FIELD_OFFSET(AuthenMsg, nowfdc_)>(
          reinterpret_cast<char*>(&nowfdc_),
          reinterpret_cast<char*>(&other->nowfdc_));
}

::PROTOBUF_NAMESPACE_ID::Metadata AuthenMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_getter, &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fBMS2015P_5fINFO_2eproto[4]);
}

// ===================================================================

class ReserveMsg::_Internal {
 public:
};

ReserveMsg::ReserveMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:BMS2015PlusInfo.ReserveMsg)
}
ReserveMsg::ReserveMsg(const ReserveMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&bmsdesirestarttime_, &from.bmsdesirestarttime_,
    static_cast<size_t>(reinterpret_cast<char*>(&reserveresult_) -
    reinterpret_cast<char*>(&bmsdesirestarttime_)) + sizeof(reserveresult_));
  // @@protoc_insertion_point(copy_constructor:BMS2015PlusInfo.ReserveMsg)
}

inline void ReserveMsg::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&bmsdesirestarttime_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&reserveresult_) -
    reinterpret_cast<char*>(&bmsdesirestarttime_)) + sizeof(reserveresult_));
}

ReserveMsg::~ReserveMsg() {
  // @@protoc_insertion_point(destructor:BMS2015PlusInfo.ReserveMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ReserveMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void ReserveMsg::ArenaDtor(void* object) {
  ReserveMsg* _this = reinterpret_cast< ReserveMsg* >(object);
  (void)_this;
}
void ReserveMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ReserveMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ReserveMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:BMS2015PlusInfo.ReserveMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&bmsdesirestarttime_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&reserveresult_) -
      reinterpret_cast<char*>(&bmsdesirestarttime_)) + sizeof(reserveresult_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ReserveMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 bmsDesireStartTime = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          bmsdesirestarttime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 bmsDesireLeaveTime = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          bmsdesireleavetime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float chargerOutPowerMax = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          chargeroutpowermax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 immediateChargeSupport = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          immediatechargesupport_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 reserveResult = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          reserveresult_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ReserveMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:BMS2015PlusInfo.ReserveMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 bmsDesireStartTime = 1;
  if (this->_internal_bmsdesirestarttime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_bmsdesirestarttime(), target);
  }

  // uint32 bmsDesireLeaveTime = 2;
  if (this->_internal_bmsdesireleavetime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_bmsdesireleavetime(), target);
  }

  // float chargerOutPowerMax = 3;
  if (!(this->_internal_chargeroutpowermax() <= 0 && this->_internal_chargeroutpowermax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_chargeroutpowermax(), target);
  }

  // uint32 immediateChargeSupport = 4;
  if (this->_internal_immediatechargesupport() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_immediatechargesupport(), target);
  }

  // uint32 reserveResult = 5;
  if (this->_internal_reserveresult() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_reserveresult(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:BMS2015PlusInfo.ReserveMsg)
  return target;
}

size_t ReserveMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:BMS2015PlusInfo.ReserveMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 bmsDesireStartTime = 1;
  if (this->_internal_bmsdesirestarttime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsdesirestarttime());
  }

  // uint32 bmsDesireLeaveTime = 2;
  if (this->_internal_bmsdesireleavetime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsdesireleavetime());
  }

  // float chargerOutPowerMax = 3;
  if (!(this->_internal_chargeroutpowermax() <= 0 && this->_internal_chargeroutpowermax() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 immediateChargeSupport = 4;
  if (this->_internal_immediatechargesupport() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_immediatechargesupport());
  }

  // uint32 reserveResult = 5;
  if (this->_internal_reserveresult() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_reserveresult());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ReserveMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ReserveMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ReserveMsg::GetClassData() const { return &_class_data_; }

void ReserveMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<ReserveMsg *>(to)->MergeFrom(
      static_cast<const ReserveMsg &>(from));
}


void ReserveMsg::MergeFrom(const ReserveMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:BMS2015PlusInfo.ReserveMsg)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_bmsdesirestarttime() != 0) {
    _internal_set_bmsdesirestarttime(from._internal_bmsdesirestarttime());
  }
  if (from._internal_bmsdesireleavetime() != 0) {
    _internal_set_bmsdesireleavetime(from._internal_bmsdesireleavetime());
  }
  if (!(from._internal_chargeroutpowermax() <= 0 && from._internal_chargeroutpowermax() >= 0)) {
    _internal_set_chargeroutpowermax(from._internal_chargeroutpowermax());
  }
  if (from._internal_immediatechargesupport() != 0) {
    _internal_set_immediatechargesupport(from._internal_immediatechargesupport());
  }
  if (from._internal_reserveresult() != 0) {
    _internal_set_reserveresult(from._internal_reserveresult());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ReserveMsg::CopyFrom(const ReserveMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:BMS2015PlusInfo.ReserveMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ReserveMsg::IsInitialized() const {
  return true;
}

void ReserveMsg::InternalSwap(ReserveMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ReserveMsg, reserveresult_)
      + sizeof(ReserveMsg::reserveresult_)
      - PROTOBUF_FIELD_OFFSET(ReserveMsg, bmsdesirestarttime_)>(
          reinterpret_cast<char*>(&bmsdesirestarttime_),
          reinterpret_cast<char*>(&other->bmsdesirestarttime_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ReserveMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_getter, &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fBMS2015P_5fINFO_2eproto[5]);
}

// ===================================================================

class SelfcheckMsg::_Internal {
 public:
};

SelfcheckMsg::SelfcheckMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:BMS2015PlusInfo.SelfcheckMsg)
}
SelfcheckMsg::SelfcheckMsg(const SelfcheckMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&stickcheckstate_, &from.stickcheckstate_,
    static_cast<size_t>(reinterpret_cast<char*>(&dischargestate_) -
    reinterpret_cast<char*>(&stickcheckstate_)) + sizeof(dischargestate_));
  // @@protoc_insertion_point(copy_constructor:BMS2015PlusInfo.SelfcheckMsg)
}

inline void SelfcheckMsg::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&stickcheckstate_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&dischargestate_) -
    reinterpret_cast<char*>(&stickcheckstate_)) + sizeof(dischargestate_));
}

SelfcheckMsg::~SelfcheckMsg() {
  // @@protoc_insertion_point(destructor:BMS2015PlusInfo.SelfcheckMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SelfcheckMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SelfcheckMsg::ArenaDtor(void* object) {
  SelfcheckMsg* _this = reinterpret_cast< SelfcheckMsg* >(object);
  (void)_this;
}
void SelfcheckMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SelfcheckMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SelfcheckMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:BMS2015PlusInfo.SelfcheckMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&stickcheckstate_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&dischargestate_) -
      reinterpret_cast<char*>(&stickcheckstate_)) + sizeof(dischargestate_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SelfcheckMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 stickCheckState = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          stickcheckstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 shortCircuitCheckState = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          shortcircuitcheckstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 insultCheckState = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          insultcheckstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 dischargeState = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          dischargestate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* SelfcheckMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:BMS2015PlusInfo.SelfcheckMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 stickCheckState = 1;
  if (this->_internal_stickcheckstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_stickcheckstate(), target);
  }

  // uint32 shortCircuitCheckState = 2;
  if (this->_internal_shortcircuitcheckstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_shortcircuitcheckstate(), target);
  }

  // uint32 insultCheckState = 3;
  if (this->_internal_insultcheckstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_insultcheckstate(), target);
  }

  // uint32 dischargeState = 4;
  if (this->_internal_dischargestate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_dischargestate(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:BMS2015PlusInfo.SelfcheckMsg)
  return target;
}

size_t SelfcheckMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:BMS2015PlusInfo.SelfcheckMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 stickCheckState = 1;
  if (this->_internal_stickcheckstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_stickcheckstate());
  }

  // uint32 shortCircuitCheckState = 2;
  if (this->_internal_shortcircuitcheckstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_shortcircuitcheckstate());
  }

  // uint32 insultCheckState = 3;
  if (this->_internal_insultcheckstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_insultcheckstate());
  }

  // uint32 dischargeState = 4;
  if (this->_internal_dischargestate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_dischargestate());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SelfcheckMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SelfcheckMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SelfcheckMsg::GetClassData() const { return &_class_data_; }

void SelfcheckMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<SelfcheckMsg *>(to)->MergeFrom(
      static_cast<const SelfcheckMsg &>(from));
}


void SelfcheckMsg::MergeFrom(const SelfcheckMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:BMS2015PlusInfo.SelfcheckMsg)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_stickcheckstate() != 0) {
    _internal_set_stickcheckstate(from._internal_stickcheckstate());
  }
  if (from._internal_shortcircuitcheckstate() != 0) {
    _internal_set_shortcircuitcheckstate(from._internal_shortcircuitcheckstate());
  }
  if (from._internal_insultcheckstate() != 0) {
    _internal_set_insultcheckstate(from._internal_insultcheckstate());
  }
  if (from._internal_dischargestate() != 0) {
    _internal_set_dischargestate(from._internal_dischargestate());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SelfcheckMsg::CopyFrom(const SelfcheckMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:BMS2015PlusInfo.SelfcheckMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SelfcheckMsg::IsInitialized() const {
  return true;
}

void SelfcheckMsg::InternalSwap(SelfcheckMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SelfcheckMsg, dischargestate_)
      + sizeof(SelfcheckMsg::dischargestate_)
      - PROTOBUF_FIELD_OFFSET(SelfcheckMsg, stickcheckstate_)>(
          reinterpret_cast<char*>(&stickcheckstate_),
          reinterpret_cast<char*>(&other->stickcheckstate_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SelfcheckMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_getter, &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fBMS2015P_5fINFO_2eproto[6]);
}

// ===================================================================

class VehicelState::_Internal {
 public:
};

VehicelState::VehicelState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:BMS2015PlusInfo.VehicelState)
}
VehicelState::VehicelState(const VehicelState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&vehiclec5state_, &from.vehiclec5state_,
    static_cast<size_t>(reinterpret_cast<char*>(&vehicelelockstate_) -
    reinterpret_cast<char*>(&vehiclec5state_)) + sizeof(vehicelelockstate_));
  // @@protoc_insertion_point(copy_constructor:BMS2015PlusInfo.VehicelState)
}

inline void VehicelState::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&vehiclec5state_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&vehicelelockstate_) -
    reinterpret_cast<char*>(&vehiclec5state_)) + sizeof(vehicelelockstate_));
}

VehicelState::~VehicelState() {
  // @@protoc_insertion_point(destructor:BMS2015PlusInfo.VehicelState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void VehicelState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void VehicelState::ArenaDtor(void* object) {
  VehicelState* _this = reinterpret_cast< VehicelState* >(object);
  (void)_this;
}
void VehicelState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void VehicelState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void VehicelState::Clear() {
// @@protoc_insertion_point(message_clear_start:BMS2015PlusInfo.VehicelState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&vehiclec5state_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&vehicelelockstate_) -
      reinterpret_cast<char*>(&vehiclec5state_)) + sizeof(vehicelelockstate_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* VehicelState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 vehicleC5State = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          vehiclec5state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 vehicleC6State = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          vehiclec6state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 vehicelElockState = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          vehicelelockstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* VehicelState::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:BMS2015PlusInfo.VehicelState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 vehicleC5State = 1;
  if (this->_internal_vehiclec5state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_vehiclec5state(), target);
  }

  // uint32 vehicleC6State = 2;
  if (this->_internal_vehiclec6state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_vehiclec6state(), target);
  }

  // uint32 vehicelElockState = 3;
  if (this->_internal_vehicelelockstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_vehicelelockstate(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:BMS2015PlusInfo.VehicelState)
  return target;
}

size_t VehicelState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:BMS2015PlusInfo.VehicelState)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 vehicleC5State = 1;
  if (this->_internal_vehiclec5state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_vehiclec5state());
  }

  // uint32 vehicleC6State = 2;
  if (this->_internal_vehiclec6state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_vehiclec6state());
  }

  // uint32 vehicelElockState = 3;
  if (this->_internal_vehicelelockstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_vehicelelockstate());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData VehicelState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    VehicelState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*VehicelState::GetClassData() const { return &_class_data_; }

void VehicelState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<VehicelState *>(to)->MergeFrom(
      static_cast<const VehicelState &>(from));
}


void VehicelState::MergeFrom(const VehicelState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:BMS2015PlusInfo.VehicelState)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_vehiclec5state() != 0) {
    _internal_set_vehiclec5state(from._internal_vehiclec5state());
  }
  if (from._internal_vehiclec6state() != 0) {
    _internal_set_vehiclec6state(from._internal_vehiclec6state());
  }
  if (from._internal_vehicelelockstate() != 0) {
    _internal_set_vehicelelockstate(from._internal_vehicelelockstate());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void VehicelState::CopyFrom(const VehicelState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:BMS2015PlusInfo.VehicelState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VehicelState::IsInitialized() const {
  return true;
}

void VehicelState::InternalSwap(VehicelState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(VehicelState, vehicelelockstate_)
      + sizeof(VehicelState::vehicelelockstate_)
      - PROTOBUF_FIELD_OFFSET(VehicelState, vehiclec5state_)>(
          reinterpret_cast<char*>(&vehiclec5state_),
          reinterpret_cast<char*>(&other->vehiclec5state_));
}

::PROTOBUF_NAMESPACE_ID::Metadata VehicelState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_getter, &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fBMS2015P_5fINFO_2eproto[7]);
}

// ===================================================================

class PowerSupplyMsg::_Internal {
 public:
};

PowerSupplyMsg::PowerSupplyMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:BMS2015PlusInfo.PowerSupplyMsg)
}
PowerSupplyMsg::PowerSupplyMsg(const PowerSupplyMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&chargersupplystate_, &from.chargersupplystate_,
    static_cast<size_t>(reinterpret_cast<char*>(&resonforcapacitychange_) -
    reinterpret_cast<char*>(&chargersupplystate_)) + sizeof(resonforcapacitychange_));
  // @@protoc_insertion_point(copy_constructor:BMS2015PlusInfo.PowerSupplyMsg)
}

inline void PowerSupplyMsg::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&chargersupplystate_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&resonforcapacitychange_) -
    reinterpret_cast<char*>(&chargersupplystate_)) + sizeof(resonforcapacitychange_));
}

PowerSupplyMsg::~PowerSupplyMsg() {
  // @@protoc_insertion_point(destructor:BMS2015PlusInfo.PowerSupplyMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PowerSupplyMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void PowerSupplyMsg::ArenaDtor(void* object) {
  PowerSupplyMsg* _this = reinterpret_cast< PowerSupplyMsg* >(object);
  (void)_this;
}
void PowerSupplyMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PowerSupplyMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PowerSupplyMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:BMS2015PlusInfo.PowerSupplyMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&chargersupplystate_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&resonforcapacitychange_) -
      reinterpret_cast<char*>(&chargersupplystate_)) + sizeof(resonforcapacitychange_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PowerSupplyMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 chargerSupplyState = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          chargersupplystate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 EvSupplyState = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          evsupplystate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float volDesire = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          voldesire_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float currDesire = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 37)) {
          currdesire_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float outVol = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 45)) {
          outvol_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float outCurr = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 53)) {
          outcurr_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float chgOutCurrMax = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 61)) {
          chgoutcurrmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float resonForCapacityChange = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 69)) {
          resonforcapacitychange_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* PowerSupplyMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:BMS2015PlusInfo.PowerSupplyMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 chargerSupplyState = 1;
  if (this->_internal_chargersupplystate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_chargersupplystate(), target);
  }

  // uint32 EvSupplyState = 2;
  if (this->_internal_evsupplystate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_evsupplystate(), target);
  }

  // float volDesire = 3;
  if (!(this->_internal_voldesire() <= 0 && this->_internal_voldesire() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_voldesire(), target);
  }

  // float currDesire = 4;
  if (!(this->_internal_currdesire() <= 0 && this->_internal_currdesire() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_currdesire(), target);
  }

  // float outVol = 5;
  if (!(this->_internal_outvol() <= 0 && this->_internal_outvol() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(5, this->_internal_outvol(), target);
  }

  // float outCurr = 6;
  if (!(this->_internal_outcurr() <= 0 && this->_internal_outcurr() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(6, this->_internal_outcurr(), target);
  }

  // float chgOutCurrMax = 7;
  if (!(this->_internal_chgoutcurrmax() <= 0 && this->_internal_chgoutcurrmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(7, this->_internal_chgoutcurrmax(), target);
  }

  // float resonForCapacityChange = 8;
  if (!(this->_internal_resonforcapacitychange() <= 0 && this->_internal_resonforcapacitychange() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(8, this->_internal_resonforcapacitychange(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:BMS2015PlusInfo.PowerSupplyMsg)
  return target;
}

size_t PowerSupplyMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:BMS2015PlusInfo.PowerSupplyMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 chargerSupplyState = 1;
  if (this->_internal_chargersupplystate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chargersupplystate());
  }

  // uint32 EvSupplyState = 2;
  if (this->_internal_evsupplystate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_evsupplystate());
  }

  // float volDesire = 3;
  if (!(this->_internal_voldesire() <= 0 && this->_internal_voldesire() >= 0)) {
    total_size += 1 + 4;
  }

  // float currDesire = 4;
  if (!(this->_internal_currdesire() <= 0 && this->_internal_currdesire() >= 0)) {
    total_size += 1 + 4;
  }

  // float outVol = 5;
  if (!(this->_internal_outvol() <= 0 && this->_internal_outvol() >= 0)) {
    total_size += 1 + 4;
  }

  // float outCurr = 6;
  if (!(this->_internal_outcurr() <= 0 && this->_internal_outcurr() >= 0)) {
    total_size += 1 + 4;
  }

  // float chgOutCurrMax = 7;
  if (!(this->_internal_chgoutcurrmax() <= 0 && this->_internal_chgoutcurrmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float resonForCapacityChange = 8;
  if (!(this->_internal_resonforcapacitychange() <= 0 && this->_internal_resonforcapacitychange() >= 0)) {
    total_size += 1 + 4;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PowerSupplyMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PowerSupplyMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PowerSupplyMsg::GetClassData() const { return &_class_data_; }

void PowerSupplyMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<PowerSupplyMsg *>(to)->MergeFrom(
      static_cast<const PowerSupplyMsg &>(from));
}


void PowerSupplyMsg::MergeFrom(const PowerSupplyMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:BMS2015PlusInfo.PowerSupplyMsg)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_chargersupplystate() != 0) {
    _internal_set_chargersupplystate(from._internal_chargersupplystate());
  }
  if (from._internal_evsupplystate() != 0) {
    _internal_set_evsupplystate(from._internal_evsupplystate());
  }
  if (!(from._internal_voldesire() <= 0 && from._internal_voldesire() >= 0)) {
    _internal_set_voldesire(from._internal_voldesire());
  }
  if (!(from._internal_currdesire() <= 0 && from._internal_currdesire() >= 0)) {
    _internal_set_currdesire(from._internal_currdesire());
  }
  if (!(from._internal_outvol() <= 0 && from._internal_outvol() >= 0)) {
    _internal_set_outvol(from._internal_outvol());
  }
  if (!(from._internal_outcurr() <= 0 && from._internal_outcurr() >= 0)) {
    _internal_set_outcurr(from._internal_outcurr());
  }
  if (!(from._internal_chgoutcurrmax() <= 0 && from._internal_chgoutcurrmax() >= 0)) {
    _internal_set_chgoutcurrmax(from._internal_chgoutcurrmax());
  }
  if (!(from._internal_resonforcapacitychange() <= 0 && from._internal_resonforcapacitychange() >= 0)) {
    _internal_set_resonforcapacitychange(from._internal_resonforcapacitychange());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PowerSupplyMsg::CopyFrom(const PowerSupplyMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:BMS2015PlusInfo.PowerSupplyMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PowerSupplyMsg::IsInitialized() const {
  return true;
}

void PowerSupplyMsg::InternalSwap(PowerSupplyMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PowerSupplyMsg, resonforcapacitychange_)
      + sizeof(PowerSupplyMsg::resonforcapacitychange_)
      - PROTOBUF_FIELD_OFFSET(PowerSupplyMsg, chargersupplystate_)>(
          reinterpret_cast<char*>(&chargersupplystate_),
          reinterpret_cast<char*>(&other->chargersupplystate_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PowerSupplyMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_getter, &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fBMS2015P_5fINFO_2eproto[8]);
}

// ===================================================================

class BMSChargingEnd::_Internal {
 public:
};

BMSChargingEnd::BMSChargingEnd(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:BMS2015PlusInfo.BMSChargingEnd)
}
BMSChargingEnd::BMSChargingEnd(const BMSChargingEnd& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&bmsstickcheckstate_, &from.bmsstickcheckstate_,
    static_cast<size_t>(reinterpret_cast<char*>(&reconnectenable_) -
    reinterpret_cast<char*>(&bmsstickcheckstate_)) + sizeof(reconnectenable_));
  // @@protoc_insertion_point(copy_constructor:BMS2015PlusInfo.BMSChargingEnd)
}

inline void BMSChargingEnd::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&bmsstickcheckstate_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&reconnectenable_) -
    reinterpret_cast<char*>(&bmsstickcheckstate_)) + sizeof(reconnectenable_));
}

BMSChargingEnd::~BMSChargingEnd() {
  // @@protoc_insertion_point(destructor:BMS2015PlusInfo.BMSChargingEnd)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void BMSChargingEnd::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void BMSChargingEnd::ArenaDtor(void* object) {
  BMSChargingEnd* _this = reinterpret_cast< BMSChargingEnd* >(object);
  (void)_this;
}
void BMSChargingEnd::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void BMSChargingEnd::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void BMSChargingEnd::Clear() {
// @@protoc_insertion_point(message_clear_start:BMS2015PlusInfo.BMSChargingEnd)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&bmsstickcheckstate_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&reconnectenable_) -
      reinterpret_cast<char*>(&bmsstickcheckstate_)) + sizeof(reconnectenable_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* BMSChargingEnd::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 bmsStickCheckState = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          bmsstickcheckstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 chgStickCheckEnable = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          chgstickcheckenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float energyChg = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          energychg_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float energyDischarge = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 37)) {
          energydischarge_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float endSOC = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 45)) {
          endsoc_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 chargerStopType = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          chargerstoptype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 chargerStopCode = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          chargerstopcode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 bmsStopType = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          bmsstoptype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 bmsStopCode = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72)) {
          bmsstopcode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 chgReconnectEnable = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          chgreconnectenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 reconnectEnable = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          reconnectenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* BMSChargingEnd::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:BMS2015PlusInfo.BMSChargingEnd)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 bmsStickCheckState = 1;
  if (this->_internal_bmsstickcheckstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_bmsstickcheckstate(), target);
  }

  // uint32 chgStickCheckEnable = 2;
  if (this->_internal_chgstickcheckenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_chgstickcheckenable(), target);
  }

  // float energyChg = 3;
  if (!(this->_internal_energychg() <= 0 && this->_internal_energychg() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_energychg(), target);
  }

  // float energyDischarge = 4;
  if (!(this->_internal_energydischarge() <= 0 && this->_internal_energydischarge() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_energydischarge(), target);
  }

  // float endSOC = 5;
  if (!(this->_internal_endsoc() <= 0 && this->_internal_endsoc() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(5, this->_internal_endsoc(), target);
  }

  // uint32 chargerStopType = 6;
  if (this->_internal_chargerstoptype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_chargerstoptype(), target);
  }

  // uint32 chargerStopCode = 7;
  if (this->_internal_chargerstopcode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(7, this->_internal_chargerstopcode(), target);
  }

  // uint32 bmsStopType = 8;
  if (this->_internal_bmsstoptype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_bmsstoptype(), target);
  }

  // uint32 bmsStopCode = 9;
  if (this->_internal_bmsstopcode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(9, this->_internal_bmsstopcode(), target);
  }

  // uint32 chgReconnectEnable = 10;
  if (this->_internal_chgreconnectenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(10, this->_internal_chgreconnectenable(), target);
  }

  // uint32 reconnectEnable = 11;
  if (this->_internal_reconnectenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(11, this->_internal_reconnectenable(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:BMS2015PlusInfo.BMSChargingEnd)
  return target;
}

size_t BMSChargingEnd::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:BMS2015PlusInfo.BMSChargingEnd)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 bmsStickCheckState = 1;
  if (this->_internal_bmsstickcheckstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsstickcheckstate());
  }

  // uint32 chgStickCheckEnable = 2;
  if (this->_internal_chgstickcheckenable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chgstickcheckenable());
  }

  // float energyChg = 3;
  if (!(this->_internal_energychg() <= 0 && this->_internal_energychg() >= 0)) {
    total_size += 1 + 4;
  }

  // float energyDischarge = 4;
  if (!(this->_internal_energydischarge() <= 0 && this->_internal_energydischarge() >= 0)) {
    total_size += 1 + 4;
  }

  // float endSOC = 5;
  if (!(this->_internal_endsoc() <= 0 && this->_internal_endsoc() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 chargerStopType = 6;
  if (this->_internal_chargerstoptype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chargerstoptype());
  }

  // uint32 chargerStopCode = 7;
  if (this->_internal_chargerstopcode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chargerstopcode());
  }

  // uint32 bmsStopType = 8;
  if (this->_internal_bmsstoptype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsstoptype());
  }

  // uint32 bmsStopCode = 9;
  if (this->_internal_bmsstopcode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsstopcode());
  }

  // uint32 chgReconnectEnable = 10;
  if (this->_internal_chgreconnectenable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chgreconnectenable());
  }

  // uint32 reconnectEnable = 11;
  if (this->_internal_reconnectenable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_reconnectenable());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData BMSChargingEnd::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    BMSChargingEnd::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*BMSChargingEnd::GetClassData() const { return &_class_data_; }

void BMSChargingEnd::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<BMSChargingEnd *>(to)->MergeFrom(
      static_cast<const BMSChargingEnd &>(from));
}


void BMSChargingEnd::MergeFrom(const BMSChargingEnd& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:BMS2015PlusInfo.BMSChargingEnd)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_bmsstickcheckstate() != 0) {
    _internal_set_bmsstickcheckstate(from._internal_bmsstickcheckstate());
  }
  if (from._internal_chgstickcheckenable() != 0) {
    _internal_set_chgstickcheckenable(from._internal_chgstickcheckenable());
  }
  if (!(from._internal_energychg() <= 0 && from._internal_energychg() >= 0)) {
    _internal_set_energychg(from._internal_energychg());
  }
  if (!(from._internal_energydischarge() <= 0 && from._internal_energydischarge() >= 0)) {
    _internal_set_energydischarge(from._internal_energydischarge());
  }
  if (!(from._internal_endsoc() <= 0 && from._internal_endsoc() >= 0)) {
    _internal_set_endsoc(from._internal_endsoc());
  }
  if (from._internal_chargerstoptype() != 0) {
    _internal_set_chargerstoptype(from._internal_chargerstoptype());
  }
  if (from._internal_chargerstopcode() != 0) {
    _internal_set_chargerstopcode(from._internal_chargerstopcode());
  }
  if (from._internal_bmsstoptype() != 0) {
    _internal_set_bmsstoptype(from._internal_bmsstoptype());
  }
  if (from._internal_bmsstopcode() != 0) {
    _internal_set_bmsstopcode(from._internal_bmsstopcode());
  }
  if (from._internal_chgreconnectenable() != 0) {
    _internal_set_chgreconnectenable(from._internal_chgreconnectenable());
  }
  if (from._internal_reconnectenable() != 0) {
    _internal_set_reconnectenable(from._internal_reconnectenable());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void BMSChargingEnd::CopyFrom(const BMSChargingEnd& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:BMS2015PlusInfo.BMSChargingEnd)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BMSChargingEnd::IsInitialized() const {
  return true;
}

void BMSChargingEnd::InternalSwap(BMSChargingEnd* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(BMSChargingEnd, reconnectenable_)
      + sizeof(BMSChargingEnd::reconnectenable_)
      - PROTOBUF_FIELD_OFFSET(BMSChargingEnd, bmsstickcheckstate_)>(
          reinterpret_cast<char*>(&bmsstickcheckstate_),
          reinterpret_cast<char*>(&other->bmsstickcheckstate_));
}

::PROTOBUF_NAMESPACE_ID::Metadata BMSChargingEnd::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_getter, &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fBMS2015P_5fINFO_2eproto[9]);
}

// ===================================================================

class BMSCharging::_Internal {
 public:
};

BMSCharging::BMSCharging(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:BMS2015PlusInfo.BMSCharging)
}
BMSCharging::BMSCharging(const BMSCharging& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&bmsready_, &from.bmsready_,
    static_cast<size_t>(reinterpret_cast<char*>(&tempmin_) -
    reinterpret_cast<char*>(&bmsready_)) + sizeof(tempmin_));
  // @@protoc_insertion_point(copy_constructor:BMS2015PlusInfo.BMSCharging)
}

inline void BMSCharging::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&bmsready_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&tempmin_) -
    reinterpret_cast<char*>(&bmsready_)) + sizeof(tempmin_));
}

BMSCharging::~BMSCharging() {
  // @@protoc_insertion_point(destructor:BMS2015PlusInfo.BMSCharging)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void BMSCharging::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void BMSCharging::ArenaDtor(void* object) {
  BMSCharging* _this = reinterpret_cast< BMSCharging* >(object);
  (void)_this;
}
void BMSCharging::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void BMSCharging::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void BMSCharging::Clear() {
// @@protoc_insertion_point(message_clear_start:BMS2015PlusInfo.BMSCharging)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&bmsready_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&tempmin_) -
      reinterpret_cast<char*>(&bmsready_)) + sizeof(tempmin_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* BMSCharging::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 bmsReady = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          bmsready_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 chargerReady = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          chargerready_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 chargeMode = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          chargemode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 resonForCapacityChange = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          resonforcapacitychange_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 bmsPause = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          bmspause_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 chgPause = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          chgpause_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float bmsBatVol = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 61)) {
          bmsbatvol_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float volDemand = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 69)) {
          voldemand_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float curDemand = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 77)) {
          curdemand_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float socNow = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 85)) {
          socnow_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float currOutCapacity = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 93)) {
          curroutcapacity_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float volMeasured = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 101)) {
          volmeasured_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float curMeasured = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 109)) {
          curmeasured_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float monoBatVolMax = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 117)) {
          monobatvolmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float monoBatVolMin = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 125)) {
          monobatvolmin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float tempMax = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 133)) {
          tempmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float tempMin = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 141)) {
          tempmin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* BMSCharging::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:BMS2015PlusInfo.BMSCharging)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 bmsReady = 1;
  if (this->_internal_bmsready() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_bmsready(), target);
  }

  // uint32 chargerReady = 2;
  if (this->_internal_chargerready() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_chargerready(), target);
  }

  // uint32 chargeMode = 3;
  if (this->_internal_chargemode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_chargemode(), target);
  }

  // uint32 resonForCapacityChange = 4;
  if (this->_internal_resonforcapacitychange() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_resonforcapacitychange(), target);
  }

  // uint32 bmsPause = 5;
  if (this->_internal_bmspause() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_bmspause(), target);
  }

  // uint32 chgPause = 6;
  if (this->_internal_chgpause() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_chgpause(), target);
  }

  // float bmsBatVol = 7;
  if (!(this->_internal_bmsbatvol() <= 0 && this->_internal_bmsbatvol() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(7, this->_internal_bmsbatvol(), target);
  }

  // float volDemand = 8;
  if (!(this->_internal_voldemand() <= 0 && this->_internal_voldemand() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(8, this->_internal_voldemand(), target);
  }

  // float curDemand = 9;
  if (!(this->_internal_curdemand() <= 0 && this->_internal_curdemand() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(9, this->_internal_curdemand(), target);
  }

  // float socNow = 10;
  if (!(this->_internal_socnow() <= 0 && this->_internal_socnow() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(10, this->_internal_socnow(), target);
  }

  // float currOutCapacity = 11;
  if (!(this->_internal_curroutcapacity() <= 0 && this->_internal_curroutcapacity() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(11, this->_internal_curroutcapacity(), target);
  }

  // float volMeasured = 12;
  if (!(this->_internal_volmeasured() <= 0 && this->_internal_volmeasured() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(12, this->_internal_volmeasured(), target);
  }

  // float curMeasured = 13;
  if (!(this->_internal_curmeasured() <= 0 && this->_internal_curmeasured() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(13, this->_internal_curmeasured(), target);
  }

  // float monoBatVolMax = 14;
  if (!(this->_internal_monobatvolmax() <= 0 && this->_internal_monobatvolmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(14, this->_internal_monobatvolmax(), target);
  }

  // float monoBatVolMin = 15;
  if (!(this->_internal_monobatvolmin() <= 0 && this->_internal_monobatvolmin() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(15, this->_internal_monobatvolmin(), target);
  }

  // float tempMax = 16;
  if (!(this->_internal_tempmax() <= 0 && this->_internal_tempmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(16, this->_internal_tempmax(), target);
  }

  // float tempMin = 17;
  if (!(this->_internal_tempmin() <= 0 && this->_internal_tempmin() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(17, this->_internal_tempmin(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:BMS2015PlusInfo.BMSCharging)
  return target;
}

size_t BMSCharging::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:BMS2015PlusInfo.BMSCharging)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 bmsReady = 1;
  if (this->_internal_bmsready() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsready());
  }

  // uint32 chargerReady = 2;
  if (this->_internal_chargerready() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chargerready());
  }

  // uint32 chargeMode = 3;
  if (this->_internal_chargemode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chargemode());
  }

  // uint32 resonForCapacityChange = 4;
  if (this->_internal_resonforcapacitychange() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_resonforcapacitychange());
  }

  // uint32 bmsPause = 5;
  if (this->_internal_bmspause() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmspause());
  }

  // uint32 chgPause = 6;
  if (this->_internal_chgpause() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chgpause());
  }

  // float bmsBatVol = 7;
  if (!(this->_internal_bmsbatvol() <= 0 && this->_internal_bmsbatvol() >= 0)) {
    total_size += 1 + 4;
  }

  // float volDemand = 8;
  if (!(this->_internal_voldemand() <= 0 && this->_internal_voldemand() >= 0)) {
    total_size += 1 + 4;
  }

  // float curDemand = 9;
  if (!(this->_internal_curdemand() <= 0 && this->_internal_curdemand() >= 0)) {
    total_size += 1 + 4;
  }

  // float socNow = 10;
  if (!(this->_internal_socnow() <= 0 && this->_internal_socnow() >= 0)) {
    total_size += 1 + 4;
  }

  // float currOutCapacity = 11;
  if (!(this->_internal_curroutcapacity() <= 0 && this->_internal_curroutcapacity() >= 0)) {
    total_size += 1 + 4;
  }

  // float volMeasured = 12;
  if (!(this->_internal_volmeasured() <= 0 && this->_internal_volmeasured() >= 0)) {
    total_size += 1 + 4;
  }

  // float curMeasured = 13;
  if (!(this->_internal_curmeasured() <= 0 && this->_internal_curmeasured() >= 0)) {
    total_size += 1 + 4;
  }

  // float monoBatVolMax = 14;
  if (!(this->_internal_monobatvolmax() <= 0 && this->_internal_monobatvolmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float monoBatVolMin = 15;
  if (!(this->_internal_monobatvolmin() <= 0 && this->_internal_monobatvolmin() >= 0)) {
    total_size += 1 + 4;
  }

  // float tempMax = 16;
  if (!(this->_internal_tempmax() <= 0 && this->_internal_tempmax() >= 0)) {
    total_size += 2 + 4;
  }

  // float tempMin = 17;
  if (!(this->_internal_tempmin() <= 0 && this->_internal_tempmin() >= 0)) {
    total_size += 2 + 4;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData BMSCharging::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    BMSCharging::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*BMSCharging::GetClassData() const { return &_class_data_; }

void BMSCharging::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<BMSCharging *>(to)->MergeFrom(
      static_cast<const BMSCharging &>(from));
}


void BMSCharging::MergeFrom(const BMSCharging& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:BMS2015PlusInfo.BMSCharging)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_bmsready() != 0) {
    _internal_set_bmsready(from._internal_bmsready());
  }
  if (from._internal_chargerready() != 0) {
    _internal_set_chargerready(from._internal_chargerready());
  }
  if (from._internal_chargemode() != 0) {
    _internal_set_chargemode(from._internal_chargemode());
  }
  if (from._internal_resonforcapacitychange() != 0) {
    _internal_set_resonforcapacitychange(from._internal_resonforcapacitychange());
  }
  if (from._internal_bmspause() != 0) {
    _internal_set_bmspause(from._internal_bmspause());
  }
  if (from._internal_chgpause() != 0) {
    _internal_set_chgpause(from._internal_chgpause());
  }
  if (!(from._internal_bmsbatvol() <= 0 && from._internal_bmsbatvol() >= 0)) {
    _internal_set_bmsbatvol(from._internal_bmsbatvol());
  }
  if (!(from._internal_voldemand() <= 0 && from._internal_voldemand() >= 0)) {
    _internal_set_voldemand(from._internal_voldemand());
  }
  if (!(from._internal_curdemand() <= 0 && from._internal_curdemand() >= 0)) {
    _internal_set_curdemand(from._internal_curdemand());
  }
  if (!(from._internal_socnow() <= 0 && from._internal_socnow() >= 0)) {
    _internal_set_socnow(from._internal_socnow());
  }
  if (!(from._internal_curroutcapacity() <= 0 && from._internal_curroutcapacity() >= 0)) {
    _internal_set_curroutcapacity(from._internal_curroutcapacity());
  }
  if (!(from._internal_volmeasured() <= 0 && from._internal_volmeasured() >= 0)) {
    _internal_set_volmeasured(from._internal_volmeasured());
  }
  if (!(from._internal_curmeasured() <= 0 && from._internal_curmeasured() >= 0)) {
    _internal_set_curmeasured(from._internal_curmeasured());
  }
  if (!(from._internal_monobatvolmax() <= 0 && from._internal_monobatvolmax() >= 0)) {
    _internal_set_monobatvolmax(from._internal_monobatvolmax());
  }
  if (!(from._internal_monobatvolmin() <= 0 && from._internal_monobatvolmin() >= 0)) {
    _internal_set_monobatvolmin(from._internal_monobatvolmin());
  }
  if (!(from._internal_tempmax() <= 0 && from._internal_tempmax() >= 0)) {
    _internal_set_tempmax(from._internal_tempmax());
  }
  if (!(from._internal_tempmin() <= 0 && from._internal_tempmin() >= 0)) {
    _internal_set_tempmin(from._internal_tempmin());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void BMSCharging::CopyFrom(const BMSCharging& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:BMS2015PlusInfo.BMSCharging)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BMSCharging::IsInitialized() const {
  return true;
}

void BMSCharging::InternalSwap(BMSCharging* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(BMSCharging, tempmin_)
      + sizeof(BMSCharging::tempmin_)
      - PROTOBUF_FIELD_OFFSET(BMSCharging, bmsready_)>(
          reinterpret_cast<char*>(&bmsready_),
          reinterpret_cast<char*>(&other->bmsready_));
}

::PROTOBUF_NAMESPACE_ID::Metadata BMSCharging::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_getter, &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fBMS2015P_5fINFO_2eproto[10]);
}

// ===================================================================

class bms2015pMsg::_Internal {
 public:
  static const ::BMS2015PlusInfo::ProtoConferMsg& bmsprotoconfer(const bms2015pMsg* msg);
  static const ::BMS2015PlusInfo::FunConferMsg& bmsfunconfer(const bms2015pMsg* msg);
  static const ::BMS2015PlusInfo::BMSConfig& bmsconfgm(const bms2015pMsg* msg);
  static const ::BMS2015PlusInfo::BMSCharging& bmschargingm(const bms2015pMsg* msg);
  static const ::BMS2015PlusInfo::BMSChargingEnd& bmschargefinishm(const bms2015pMsg* msg);
  static const ::BMS2015PlusInfo::AuthenMsg& bmsauthenm(const bms2015pMsg* msg);
  static const ::BMS2015PlusInfo::ReserveMsg& bmsreservem(const bms2015pMsg* msg);
  static const ::BMS2015PlusInfo::SelfcheckMsg& bmsselfcheckm(const bms2015pMsg* msg);
  static const ::BMS2015PlusInfo::VehicelState& bmsvehicelstatem(const bms2015pMsg* msg);
  static const ::BMS2015PlusInfo::PowerSupplyMsg& powersupplym(const bms2015pMsg* msg);
};

const ::BMS2015PlusInfo::ProtoConferMsg&
bms2015pMsg::_Internal::bmsprotoconfer(const bms2015pMsg* msg) {
  return *msg->bmsprotoconfer_;
}
const ::BMS2015PlusInfo::FunConferMsg&
bms2015pMsg::_Internal::bmsfunconfer(const bms2015pMsg* msg) {
  return *msg->bmsfunconfer_;
}
const ::BMS2015PlusInfo::BMSConfig&
bms2015pMsg::_Internal::bmsconfgm(const bms2015pMsg* msg) {
  return *msg->bmsconfgm_;
}
const ::BMS2015PlusInfo::BMSCharging&
bms2015pMsg::_Internal::bmschargingm(const bms2015pMsg* msg) {
  return *msg->bmschargingm_;
}
const ::BMS2015PlusInfo::BMSChargingEnd&
bms2015pMsg::_Internal::bmschargefinishm(const bms2015pMsg* msg) {
  return *msg->bmschargefinishm_;
}
const ::BMS2015PlusInfo::AuthenMsg&
bms2015pMsg::_Internal::bmsauthenm(const bms2015pMsg* msg) {
  return *msg->bmsauthenm_;
}
const ::BMS2015PlusInfo::ReserveMsg&
bms2015pMsg::_Internal::bmsreservem(const bms2015pMsg* msg) {
  return *msg->bmsreservem_;
}
const ::BMS2015PlusInfo::SelfcheckMsg&
bms2015pMsg::_Internal::bmsselfcheckm(const bms2015pMsg* msg) {
  return *msg->bmsselfcheckm_;
}
const ::BMS2015PlusInfo::VehicelState&
bms2015pMsg::_Internal::bmsvehicelstatem(const bms2015pMsg* msg) {
  return *msg->bmsvehicelstatem_;
}
const ::BMS2015PlusInfo::PowerSupplyMsg&
bms2015pMsg::_Internal::powersupplym(const bms2015pMsg* msg) {
  return *msg->powersupplym_;
}
bms2015pMsg::bms2015pMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:BMS2015PlusInfo.bms2015pMsg)
}
bms2015pMsg::bms2015pMsg(const bms2015pMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_bmsprotoconfer()) {
    bmsprotoconfer_ = new ::BMS2015PlusInfo::ProtoConferMsg(*from.bmsprotoconfer_);
  } else {
    bmsprotoconfer_ = nullptr;
  }
  if (from._internal_has_bmsfunconfer()) {
    bmsfunconfer_ = new ::BMS2015PlusInfo::FunConferMsg(*from.bmsfunconfer_);
  } else {
    bmsfunconfer_ = nullptr;
  }
  if (from._internal_has_bmsconfgm()) {
    bmsconfgm_ = new ::BMS2015PlusInfo::BMSConfig(*from.bmsconfgm_);
  } else {
    bmsconfgm_ = nullptr;
  }
  if (from._internal_has_bmschargingm()) {
    bmschargingm_ = new ::BMS2015PlusInfo::BMSCharging(*from.bmschargingm_);
  } else {
    bmschargingm_ = nullptr;
  }
  if (from._internal_has_bmschargefinishm()) {
    bmschargefinishm_ = new ::BMS2015PlusInfo::BMSChargingEnd(*from.bmschargefinishm_);
  } else {
    bmschargefinishm_ = nullptr;
  }
  if (from._internal_has_bmsauthenm()) {
    bmsauthenm_ = new ::BMS2015PlusInfo::AuthenMsg(*from.bmsauthenm_);
  } else {
    bmsauthenm_ = nullptr;
  }
  if (from._internal_has_bmsreservem()) {
    bmsreservem_ = new ::BMS2015PlusInfo::ReserveMsg(*from.bmsreservem_);
  } else {
    bmsreservem_ = nullptr;
  }
  if (from._internal_has_bmsselfcheckm()) {
    bmsselfcheckm_ = new ::BMS2015PlusInfo::SelfcheckMsg(*from.bmsselfcheckm_);
  } else {
    bmsselfcheckm_ = nullptr;
  }
  if (from._internal_has_bmsvehicelstatem()) {
    bmsvehicelstatem_ = new ::BMS2015PlusInfo::VehicelState(*from.bmsvehicelstatem_);
  } else {
    bmsvehicelstatem_ = nullptr;
  }
  if (from._internal_has_powersupplym()) {
    powersupplym_ = new ::BMS2015PlusInfo::PowerSupplyMsg(*from.powersupplym_);
  } else {
    powersupplym_ = nullptr;
  }
  bmsstate_ = from.bmsstate_;
  // @@protoc_insertion_point(copy_constructor:BMS2015PlusInfo.bms2015pMsg)
}

inline void bms2015pMsg::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&bmsprotoconfer_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&bmsstate_) -
    reinterpret_cast<char*>(&bmsprotoconfer_)) + sizeof(bmsstate_));
}

bms2015pMsg::~bms2015pMsg() {
  // @@protoc_insertion_point(destructor:BMS2015PlusInfo.bms2015pMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void bms2015pMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete bmsprotoconfer_;
  if (this != internal_default_instance()) delete bmsfunconfer_;
  if (this != internal_default_instance()) delete bmsconfgm_;
  if (this != internal_default_instance()) delete bmschargingm_;
  if (this != internal_default_instance()) delete bmschargefinishm_;
  if (this != internal_default_instance()) delete bmsauthenm_;
  if (this != internal_default_instance()) delete bmsreservem_;
  if (this != internal_default_instance()) delete bmsselfcheckm_;
  if (this != internal_default_instance()) delete bmsvehicelstatem_;
  if (this != internal_default_instance()) delete powersupplym_;
}

void bms2015pMsg::ArenaDtor(void* object) {
  bms2015pMsg* _this = reinterpret_cast< bms2015pMsg* >(object);
  (void)_this;
}
void bms2015pMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void bms2015pMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void bms2015pMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:BMS2015PlusInfo.bms2015pMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && bmsprotoconfer_ != nullptr) {
    delete bmsprotoconfer_;
  }
  bmsprotoconfer_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bmsfunconfer_ != nullptr) {
    delete bmsfunconfer_;
  }
  bmsfunconfer_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bmsconfgm_ != nullptr) {
    delete bmsconfgm_;
  }
  bmsconfgm_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bmschargingm_ != nullptr) {
    delete bmschargingm_;
  }
  bmschargingm_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bmschargefinishm_ != nullptr) {
    delete bmschargefinishm_;
  }
  bmschargefinishm_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bmsauthenm_ != nullptr) {
    delete bmsauthenm_;
  }
  bmsauthenm_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bmsreservem_ != nullptr) {
    delete bmsreservem_;
  }
  bmsreservem_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bmsselfcheckm_ != nullptr) {
    delete bmsselfcheckm_;
  }
  bmsselfcheckm_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bmsvehicelstatem_ != nullptr) {
    delete bmsvehicelstatem_;
  }
  bmsvehicelstatem_ = nullptr;
  if (GetArenaForAllocation() == nullptr && powersupplym_ != nullptr) {
    delete powersupplym_;
  }
  powersupplym_ = nullptr;
  bmsstate_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* bms2015pMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .BMS2015PlusInfo.ChargeState bmsState = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_bmsstate(static_cast<::BMS2015PlusInfo::ChargeState>(val));
        } else goto handle_unusual;
        continue;
      // .BMS2015PlusInfo.ProtoConferMsg bmsProtoConfer = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_bmsprotoconfer(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .BMS2015PlusInfo.FunConferMsg bmsFunConfer = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_bmsfunconfer(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .BMS2015PlusInfo.BMSConfig BmsConfgM = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_bmsconfgm(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .BMS2015PlusInfo.BMSCharging BmsChargingM = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_bmschargingm(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .BMS2015PlusInfo.BMSChargingEnd BmsChargeFinishM = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_bmschargefinishm(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .BMS2015PlusInfo.AuthenMsg BmsAuthenM = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_bmsauthenm(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .BMS2015PlusInfo.ReserveMsg BmsReserveM = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          ptr = ctx->ParseMessage(_internal_mutable_bmsreservem(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .BMS2015PlusInfo.SelfcheckMsg BmsSelfCheckM = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          ptr = ctx->ParseMessage(_internal_mutable_bmsselfcheckm(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .BMS2015PlusInfo.VehicelState BmsVehicelStateM = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 82)) {
          ptr = ctx->ParseMessage(_internal_mutable_bmsvehicelstatem(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .BMS2015PlusInfo.PowerSupplyMsg PowerSupplyM = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 90)) {
          ptr = ctx->ParseMessage(_internal_mutable_powersupplym(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* bms2015pMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:BMS2015PlusInfo.bms2015pMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .BMS2015PlusInfo.ChargeState bmsState = 1;
  if (this->_internal_bmsstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_bmsstate(), target);
  }

  // .BMS2015PlusInfo.ProtoConferMsg bmsProtoConfer = 2;
  if (this->_internal_has_bmsprotoconfer()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::bmsprotoconfer(this), target, stream);
  }

  // .BMS2015PlusInfo.FunConferMsg bmsFunConfer = 3;
  if (this->_internal_has_bmsfunconfer()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::bmsfunconfer(this), target, stream);
  }

  // .BMS2015PlusInfo.BMSConfig BmsConfgM = 4;
  if (this->_internal_has_bmsconfgm()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::bmsconfgm(this), target, stream);
  }

  // .BMS2015PlusInfo.BMSCharging BmsChargingM = 5;
  if (this->_internal_has_bmschargingm()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::bmschargingm(this), target, stream);
  }

  // .BMS2015PlusInfo.BMSChargingEnd BmsChargeFinishM = 6;
  if (this->_internal_has_bmschargefinishm()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::bmschargefinishm(this), target, stream);
  }

  // .BMS2015PlusInfo.AuthenMsg BmsAuthenM = 7;
  if (this->_internal_has_bmsauthenm()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::bmsauthenm(this), target, stream);
  }

  // .BMS2015PlusInfo.ReserveMsg BmsReserveM = 8;
  if (this->_internal_has_bmsreservem()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        8, _Internal::bmsreservem(this), target, stream);
  }

  // .BMS2015PlusInfo.SelfcheckMsg BmsSelfCheckM = 9;
  if (this->_internal_has_bmsselfcheckm()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        9, _Internal::bmsselfcheckm(this), target, stream);
  }

  // .BMS2015PlusInfo.VehicelState BmsVehicelStateM = 10;
  if (this->_internal_has_bmsvehicelstatem()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        10, _Internal::bmsvehicelstatem(this), target, stream);
  }

  // .BMS2015PlusInfo.PowerSupplyMsg PowerSupplyM = 11;
  if (this->_internal_has_powersupplym()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        11, _Internal::powersupplym(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:BMS2015PlusInfo.bms2015pMsg)
  return target;
}

size_t bms2015pMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:BMS2015PlusInfo.bms2015pMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .BMS2015PlusInfo.ProtoConferMsg bmsProtoConfer = 2;
  if (this->_internal_has_bmsprotoconfer()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bmsprotoconfer_);
  }

  // .BMS2015PlusInfo.FunConferMsg bmsFunConfer = 3;
  if (this->_internal_has_bmsfunconfer()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bmsfunconfer_);
  }

  // .BMS2015PlusInfo.BMSConfig BmsConfgM = 4;
  if (this->_internal_has_bmsconfgm()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bmsconfgm_);
  }

  // .BMS2015PlusInfo.BMSCharging BmsChargingM = 5;
  if (this->_internal_has_bmschargingm()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bmschargingm_);
  }

  // .BMS2015PlusInfo.BMSChargingEnd BmsChargeFinishM = 6;
  if (this->_internal_has_bmschargefinishm()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bmschargefinishm_);
  }

  // .BMS2015PlusInfo.AuthenMsg BmsAuthenM = 7;
  if (this->_internal_has_bmsauthenm()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bmsauthenm_);
  }

  // .BMS2015PlusInfo.ReserveMsg BmsReserveM = 8;
  if (this->_internal_has_bmsreservem()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bmsreservem_);
  }

  // .BMS2015PlusInfo.SelfcheckMsg BmsSelfCheckM = 9;
  if (this->_internal_has_bmsselfcheckm()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bmsselfcheckm_);
  }

  // .BMS2015PlusInfo.VehicelState BmsVehicelStateM = 10;
  if (this->_internal_has_bmsvehicelstatem()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bmsvehicelstatem_);
  }

  // .BMS2015PlusInfo.PowerSupplyMsg PowerSupplyM = 11;
  if (this->_internal_has_powersupplym()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *powersupplym_);
  }

  // .BMS2015PlusInfo.ChargeState bmsState = 1;
  if (this->_internal_bmsstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_bmsstate());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData bms2015pMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    bms2015pMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*bms2015pMsg::GetClassData() const { return &_class_data_; }

void bms2015pMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<bms2015pMsg *>(to)->MergeFrom(
      static_cast<const bms2015pMsg &>(from));
}


void bms2015pMsg::MergeFrom(const bms2015pMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:BMS2015PlusInfo.bms2015pMsg)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_bmsprotoconfer()) {
    _internal_mutable_bmsprotoconfer()->::BMS2015PlusInfo::ProtoConferMsg::MergeFrom(from._internal_bmsprotoconfer());
  }
  if (from._internal_has_bmsfunconfer()) {
    _internal_mutable_bmsfunconfer()->::BMS2015PlusInfo::FunConferMsg::MergeFrom(from._internal_bmsfunconfer());
  }
  if (from._internal_has_bmsconfgm()) {
    _internal_mutable_bmsconfgm()->::BMS2015PlusInfo::BMSConfig::MergeFrom(from._internal_bmsconfgm());
  }
  if (from._internal_has_bmschargingm()) {
    _internal_mutable_bmschargingm()->::BMS2015PlusInfo::BMSCharging::MergeFrom(from._internal_bmschargingm());
  }
  if (from._internal_has_bmschargefinishm()) {
    _internal_mutable_bmschargefinishm()->::BMS2015PlusInfo::BMSChargingEnd::MergeFrom(from._internal_bmschargefinishm());
  }
  if (from._internal_has_bmsauthenm()) {
    _internal_mutable_bmsauthenm()->::BMS2015PlusInfo::AuthenMsg::MergeFrom(from._internal_bmsauthenm());
  }
  if (from._internal_has_bmsreservem()) {
    _internal_mutable_bmsreservem()->::BMS2015PlusInfo::ReserveMsg::MergeFrom(from._internal_bmsreservem());
  }
  if (from._internal_has_bmsselfcheckm()) {
    _internal_mutable_bmsselfcheckm()->::BMS2015PlusInfo::SelfcheckMsg::MergeFrom(from._internal_bmsselfcheckm());
  }
  if (from._internal_has_bmsvehicelstatem()) {
    _internal_mutable_bmsvehicelstatem()->::BMS2015PlusInfo::VehicelState::MergeFrom(from._internal_bmsvehicelstatem());
  }
  if (from._internal_has_powersupplym()) {
    _internal_mutable_powersupplym()->::BMS2015PlusInfo::PowerSupplyMsg::MergeFrom(from._internal_powersupplym());
  }
  if (from._internal_bmsstate() != 0) {
    _internal_set_bmsstate(from._internal_bmsstate());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void bms2015pMsg::CopyFrom(const bms2015pMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:BMS2015PlusInfo.bms2015pMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool bms2015pMsg::IsInitialized() const {
  return true;
}

void bms2015pMsg::InternalSwap(bms2015pMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(bms2015pMsg, bmsstate_)
      + sizeof(bms2015pMsg::bmsstate_)
      - PROTOBUF_FIELD_OFFSET(bms2015pMsg, bmsprotoconfer_)>(
          reinterpret_cast<char*>(&bmsprotoconfer_),
          reinterpret_cast<char*>(&other->bmsprotoconfer_));
}

::PROTOBUF_NAMESPACE_ID::Metadata bms2015pMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_getter, &descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fBMS2015P_5fINFO_2eproto[11]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace BMS2015PlusInfo
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::BMS2015PlusInfo::ProtoConferMsg* Arena::CreateMaybeMessage< ::BMS2015PlusInfo::ProtoConferMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::BMS2015PlusInfo::ProtoConferMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::BMS2015PlusInfo::FunConferMsg* Arena::CreateMaybeMessage< ::BMS2015PlusInfo::FunConferMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::BMS2015PlusInfo::FunConferMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::BMS2015PlusInfo::BMSConfig* Arena::CreateMaybeMessage< ::BMS2015PlusInfo::BMSConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::BMS2015PlusInfo::BMSConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::BMS2015PlusInfo::DischargeConfig* Arena::CreateMaybeMessage< ::BMS2015PlusInfo::DischargeConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::BMS2015PlusInfo::DischargeConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::BMS2015PlusInfo::AuthenMsg* Arena::CreateMaybeMessage< ::BMS2015PlusInfo::AuthenMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::BMS2015PlusInfo::AuthenMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::BMS2015PlusInfo::ReserveMsg* Arena::CreateMaybeMessage< ::BMS2015PlusInfo::ReserveMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::BMS2015PlusInfo::ReserveMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::BMS2015PlusInfo::SelfcheckMsg* Arena::CreateMaybeMessage< ::BMS2015PlusInfo::SelfcheckMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::BMS2015PlusInfo::SelfcheckMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::BMS2015PlusInfo::VehicelState* Arena::CreateMaybeMessage< ::BMS2015PlusInfo::VehicelState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::BMS2015PlusInfo::VehicelState >(arena);
}
template<> PROTOBUF_NOINLINE ::BMS2015PlusInfo::PowerSupplyMsg* Arena::CreateMaybeMessage< ::BMS2015PlusInfo::PowerSupplyMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::BMS2015PlusInfo::PowerSupplyMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::BMS2015PlusInfo::BMSChargingEnd* Arena::CreateMaybeMessage< ::BMS2015PlusInfo::BMSChargingEnd >(Arena* arena) {
  return Arena::CreateMessageInternal< ::BMS2015PlusInfo::BMSChargingEnd >(arena);
}
template<> PROTOBUF_NOINLINE ::BMS2015PlusInfo::BMSCharging* Arena::CreateMaybeMessage< ::BMS2015PlusInfo::BMSCharging >(Arena* arena) {
  return Arena::CreateMessageInternal< ::BMS2015PlusInfo::BMSCharging >(arena);
}
template<> PROTOBUF_NOINLINE ::BMS2015PlusInfo::bms2015pMsg* Arena::CreateMaybeMessage< ::BMS2015PlusInfo::bms2015pMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::BMS2015PlusInfo::bms2015pMsg >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
