// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GCU_EVS_INFO.proto

#include "GCU_EVS_INFO.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace EVSInfo {
constexpr EVProtoConferMsg::EVProtoConferMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : gbversion_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , cantype_(0u)
  , guidanceversion_(0u)
  , transportversion_(0u)
  , conferres_(0u){}
struct EVProtoConferMsgDefaultTypeInternal {
  constexpr EVProtoConferMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVProtoConferMsgDefaultTypeInternal() {}
  union {
    EVProtoConferMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVProtoConferMsgDefaultTypeInternal _EVProtoConferMsg_default_instance_;
constexpr EVFunConferMsg::EVFunConferMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : configfdc_(0u)
  , authenfdc_(0u)
  , appointfdc_(0u)
  , selfcheckfdc_(0u)
  , powersupplyfdc_(0u)
  , energytransferfdc_(0u)
  , endfdc_(0u){}
struct EVFunConferMsgDefaultTypeInternal {
  constexpr EVFunConferMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVFunConferMsgDefaultTypeInternal() {}
  union {
    EVFunConferMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVFunConferMsgDefaultTypeInternal _EVFunConferMsg_default_instance_;
constexpr EVChgParaConfigMsg::EVChgParaConfigMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : currallowmax_(0)
  , voltallowmax_(0)
  , energyallowmax_(0)
  , nowsoc_(0)
  , cellvoltallowmax_(0)
  , battempallowmax_(0)
  , restarnum_(0u){}
struct EVChgParaConfigMsgDefaultTypeInternal {
  constexpr EVChgParaConfigMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVChgParaConfigMsgDefaultTypeInternal() {}
  union {
    EVChgParaConfigMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVChgParaConfigMsgDefaultTypeInternal _EVChgParaConfigMsg_default_instance_;
constexpr EVAuthenMsg::EVAuthenMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : evin_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , authenwaittime_(0u)
  , nextfdc_(0u){}
struct EVAuthenMsgDefaultTypeInternal {
  constexpr EVAuthenMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVAuthenMsgDefaultTypeInternal() {}
  union {
    EVAuthenMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVAuthenMsgDefaultTypeInternal _EVAuthenMsg_default_instance_;
constexpr EVReserveMsg::EVReserveMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : bmsdesirestarttime_(0u)
  , bmsdesireleavetime_(0u)
  , reserveresult_(0u)
  , immediatechargesupport_(0u){}
struct EVReserveMsgDefaultTypeInternal {
  constexpr EVReserveMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVReserveMsgDefaultTypeInternal() {}
  union {
    EVReserveMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVReserveMsgDefaultTypeInternal _EVReserveMsg_default_instance_;
constexpr EVPowerSupplyMsg::EVPowerSupplyMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : supplystate_(0u)
  , supplyvoldesire_(0)
  , supplycurrdesire_(0)
  , supplyend_(0u){}
struct EVPowerSupplyMsgDefaultTypeInternal {
  constexpr EVPowerSupplyMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVPowerSupplyMsgDefaultTypeInternal() {}
  union {
    EVPowerSupplyMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVPowerSupplyMsgDefaultTypeInternal _EVPowerSupplyMsg_default_instance_;
constexpr EVChargingMsg::EVChargingMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : bmsready_(0u)
  , voldemand_(0)
  , curdemand_(0)
  , chargemode_(0u)
  , socnow_(0)
  , reschgtime_(0u)
  , cellbatvolmax_(0)
  , cellbatvolmin_(0)
  , celltempmax_(0)
  , celltempmin_(0){}
struct EVChargingMsgDefaultTypeInternal {
  constexpr EVChargingMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVChargingMsgDefaultTypeInternal() {}
  union {
    EVChargingMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVChargingMsgDefaultTypeInternal _EVChargingMsg_default_instance_;
constexpr EVChargingEndMsg::EVChargingEndMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : endtype_(0u)
  , endcode_(0u)
  , endreason_(0u)
  , repeat_(0u)
  , bmsstickcheckstate_(0u){}
struct EVChargingEndMsgDefaultTypeInternal {
  constexpr EVChargingEndMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVChargingEndMsgDefaultTypeInternal() {}
  union {
    EVChargingEndMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVChargingEndMsgDefaultTypeInternal _EVChargingEndMsg_default_instance_;
constexpr EVFunConferAckMsg::EVFunConferAckMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : funconferack_(0u)
  , configack_(0u)
  , authenack_(0u)
  , appointack_(0u)
  , selfcheckack_(0u)
  , powersupplyack_(0u)
  , energytransferack_(0u)
  , endack_(0u){}
struct EVFunConferAckMsgDefaultTypeInternal {
  constexpr EVFunConferAckMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVFunConferAckMsgDefaultTypeInternal() {}
  union {
    EVFunConferAckMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVFunConferAckMsgDefaultTypeInternal _EVFunConferAckMsg_default_instance_;
constexpr EVElectricCtrl::EVElectricCtrl(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : contactk5_(0u)
  , contactk6_(0u)
  , cc1_s2_(0u)
  , cc2_s3_(0u)
  , elockstate_(0u)
  , canbus_(0u)
  , ev_pe_(0u)
  , evinsulton_(0u)
  , evcontactk6_(0u)
  , evpause_(0u)
  , sysfan_(0u){}
struct EVElectricCtrlDefaultTypeInternal {
  constexpr EVElectricCtrlDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVElectricCtrlDefaultTypeInternal() {}
  union {
    EVElectricCtrl _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVElectricCtrlDefaultTypeInternal _EVElectricCtrl_default_instance_;
constexpr EVInsultCtrl::EVInsultCtrl(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : insultposres_(0u)
  , insultnegres_(0u)
  , batvol_(0)
  , connecttype_(0u){}
struct EVInsultCtrlDefaultTypeInternal {
  constexpr EVInsultCtrlDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVInsultCtrlDefaultTypeInternal() {}
  union {
    EVInsultCtrl _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVInsultCtrlDefaultTypeInternal _EVInsultCtrl_default_instance_;
constexpr EVStateMsg::EVStateMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : batvol_(0)
  , batcur_(0)
  , nowsoc_(0)
  , nowfc_(0u)
  , nowfdc_(0u)
  , chgmode_(0u)
  , insultposr_(0)
  , insultnegr_(0)
  , cc1volt_(0)
  , workmode_(0u)
  , stopcode_(0u)
  , cc2volt_(0){}
struct EVStateMsgDefaultTypeInternal {
  constexpr EVStateMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVStateMsgDefaultTypeInternal() {}
  union {
    EVStateMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVStateMsgDefaultTypeInternal _EVStateMsg_default_instance_;
constexpr StepPara::StepPara(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : starttime_(uint64_t{0u})
  , intervaltime_(uint64_t{0u})
  , startvalue_(0)
  , minvalue_(0)
  , maxvalue_(0)
  , stepvalue_(0)
  , cyclemode_(0u)
  , stepmode_(0u){}
struct StepParaDefaultTypeInternal {
  constexpr StepParaDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~StepParaDefaultTypeInternal() {}
  union {
    StepPara _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT StepParaDefaultTypeInternal _StepPara_default_instance_;
constexpr EVSStepMsg::EVSStepMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : needvolstep_(nullptr)
  , needcurrstep_(nullptr)
  , socstep_(nullptr)
  , cellvolstep_(nullptr)
  , celltempstep_(nullptr){}
struct EVSStepMsgDefaultTypeInternal {
  constexpr EVSStepMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVSStepMsgDefaultTypeInternal() {}
  union {
    EVSStepMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVSStepMsgDefaultTypeInternal _EVSStepMsg_default_instance_;
constexpr EVMsgCtrl::EVMsgCtrl(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : x2_0x02_state_(0u)
  , x4_0x04_state_(0u)
  , x6_0x06_state_(0u)
  , x9_0x09_state_(0u)
  , b2_0x12_state_(0u)
  , c2_0x22_state_(0u)
  , c4_0x24_state_(0u)
  , d2_0x32_state_(0u)
  , d4_0x34_state_(0u)
  , d6_0x36_state_(0u)
  , d7_0x37_state_(0u)
  , d9_0x39_state_(0u)
  , d10_0x3a_state_(0u)
  , e2_0x42_state_(0u)
  , e4_0x44_state_(0u)
  , f2_0x52_state_(0u)
  , g2_0x62_state_(0u)
  , g3_0x63_state_(0u)
  , g5_0x65_state_(0u)
  , h2_0x72_state_(0u)
  , h3_0x73_state_(0u)
  , h4_0x74_state_(0u)
  , h7_0x77_state_(0u)
  , h9_0x79_state_(0u)
  , h11_0x82_state_(0u)
  , h13_0x84_state_(0u)
  , h14_0x85_state_(0u)
  , h16_0x87_state_(0u)
  , h18_0x89_state_(0u)
  , h20_0x8b_state_(0u)
  , i1_0x91_state_(0u)
  , i4_0x94_state_(0u){}
struct EVMsgCtrlDefaultTypeInternal {
  constexpr EVMsgCtrlDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVMsgCtrlDefaultTypeInternal() {}
  union {
    EVMsgCtrl _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVMsgCtrlDefaultTypeInternal _EVMsgCtrl_default_instance_;
constexpr IpMsg::IpMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : selfip_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , serviceip_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , selfport_(0u)
  , serviceport_(0u){}
struct IpMsgDefaultTypeInternal {
  constexpr IpMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~IpMsgDefaultTypeInternal() {}
  union {
    IpMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT IpMsgDefaultTypeInternal _IpMsg_default_instance_;
}  // namespace EVSInfo
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_GCU_5fEVS_5fINFO_2eproto[16];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_GCU_5fEVS_5fINFO_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_GCU_5fEVS_5fINFO_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_GCU_5fEVS_5fINFO_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVProtoConferMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVProtoConferMsg, cantype_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVProtoConferMsg, gbversion_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVProtoConferMsg, guidanceversion_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVProtoConferMsg, transportversion_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVProtoConferMsg, conferres_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVFunConferMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVFunConferMsg, configfdc_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVFunConferMsg, authenfdc_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVFunConferMsg, appointfdc_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVFunConferMsg, selfcheckfdc_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVFunConferMsg, powersupplyfdc_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVFunConferMsg, energytransferfdc_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVFunConferMsg, endfdc_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChgParaConfigMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChgParaConfigMsg, currallowmax_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChgParaConfigMsg, voltallowmax_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChgParaConfigMsg, energyallowmax_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChgParaConfigMsg, nowsoc_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChgParaConfigMsg, cellvoltallowmax_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChgParaConfigMsg, battempallowmax_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChgParaConfigMsg, restarnum_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVAuthenMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVAuthenMsg, authenwaittime_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVAuthenMsg, evin_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVAuthenMsg, nextfdc_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVReserveMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVReserveMsg, bmsdesirestarttime_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVReserveMsg, bmsdesireleavetime_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVReserveMsg, reserveresult_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVReserveMsg, immediatechargesupport_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVPowerSupplyMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVPowerSupplyMsg, supplystate_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVPowerSupplyMsg, supplyvoldesire_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVPowerSupplyMsg, supplycurrdesire_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVPowerSupplyMsg, supplyend_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChargingMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChargingMsg, bmsready_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChargingMsg, voldemand_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChargingMsg, curdemand_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChargingMsg, chargemode_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChargingMsg, socnow_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChargingMsg, reschgtime_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChargingMsg, cellbatvolmax_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChargingMsg, cellbatvolmin_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChargingMsg, celltempmax_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChargingMsg, celltempmin_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChargingEndMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChargingEndMsg, endtype_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChargingEndMsg, endcode_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChargingEndMsg, endreason_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChargingEndMsg, repeat_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVChargingEndMsg, bmsstickcheckstate_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVFunConferAckMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVFunConferAckMsg, funconferack_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVFunConferAckMsg, configack_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVFunConferAckMsg, authenack_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVFunConferAckMsg, appointack_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVFunConferAckMsg, selfcheckack_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVFunConferAckMsg, powersupplyack_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVFunConferAckMsg, energytransferack_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVFunConferAckMsg, endack_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVElectricCtrl, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVElectricCtrl, contactk5_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVElectricCtrl, contactk6_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVElectricCtrl, cc1_s2_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVElectricCtrl, cc2_s3_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVElectricCtrl, elockstate_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVElectricCtrl, canbus_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVElectricCtrl, ev_pe_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVElectricCtrl, evinsulton_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVElectricCtrl, evcontactk6_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVElectricCtrl, evpause_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVElectricCtrl, sysfan_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVInsultCtrl, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVInsultCtrl, insultposres_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVInsultCtrl, insultnegres_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVInsultCtrl, batvol_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVInsultCtrl, connecttype_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVStateMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVStateMsg, batvol_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVStateMsg, batcur_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVStateMsg, nowsoc_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVStateMsg, nowfc_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVStateMsg, nowfdc_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVStateMsg, chgmode_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVStateMsg, insultposr_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVStateMsg, insultnegr_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVStateMsg, cc1volt_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVStateMsg, workmode_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVStateMsg, stopcode_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVStateMsg, cc2volt_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::StepPara, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::StepPara, starttime_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::StepPara, intervaltime_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::StepPara, startvalue_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::StepPara, minvalue_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::StepPara, maxvalue_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::StepPara, stepvalue_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::StepPara, cyclemode_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::StepPara, stepmode_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVSStepMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVSStepMsg, needvolstep_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVSStepMsg, needcurrstep_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVSStepMsg, socstep_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVSStepMsg, cellvolstep_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVSStepMsg, celltempstep_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, x2_0x02_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, x4_0x04_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, x6_0x06_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, x9_0x09_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, b2_0x12_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, c2_0x22_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, c4_0x24_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, d2_0x32_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, d4_0x34_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, d6_0x36_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, d7_0x37_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, d9_0x39_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, d10_0x3a_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, e2_0x42_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, e4_0x44_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, f2_0x52_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, g2_0x62_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, g3_0x63_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, g5_0x65_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, h2_0x72_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, h3_0x73_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, h4_0x74_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, h7_0x77_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, h9_0x79_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, h11_0x82_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, h13_0x84_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, h14_0x85_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, h16_0x87_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, h18_0x89_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, h20_0x8b_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, i1_0x91_state_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::EVMsgCtrl, i4_0x94_state_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::IpMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::EVSInfo::IpMsg, selfip_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::IpMsg, selfport_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::IpMsg, serviceip_),
  PROTOBUF_FIELD_OFFSET(::EVSInfo::IpMsg, serviceport_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::EVSInfo::EVProtoConferMsg)},
  { 10, -1, sizeof(::EVSInfo::EVFunConferMsg)},
  { 22, -1, sizeof(::EVSInfo::EVChgParaConfigMsg)},
  { 34, -1, sizeof(::EVSInfo::EVAuthenMsg)},
  { 42, -1, sizeof(::EVSInfo::EVReserveMsg)},
  { 51, -1, sizeof(::EVSInfo::EVPowerSupplyMsg)},
  { 60, -1, sizeof(::EVSInfo::EVChargingMsg)},
  { 75, -1, sizeof(::EVSInfo::EVChargingEndMsg)},
  { 85, -1, sizeof(::EVSInfo::EVFunConferAckMsg)},
  { 98, -1, sizeof(::EVSInfo::EVElectricCtrl)},
  { 114, -1, sizeof(::EVSInfo::EVInsultCtrl)},
  { 123, -1, sizeof(::EVSInfo::EVStateMsg)},
  { 140, -1, sizeof(::EVSInfo::StepPara)},
  { 153, -1, sizeof(::EVSInfo::EVSStepMsg)},
  { 163, -1, sizeof(::EVSInfo::EVMsgCtrl)},
  { 200, -1, sizeof(::EVSInfo::IpMsg)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::EVSInfo::_EVProtoConferMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::EVSInfo::_EVFunConferMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::EVSInfo::_EVChgParaConfigMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::EVSInfo::_EVAuthenMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::EVSInfo::_EVReserveMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::EVSInfo::_EVPowerSupplyMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::EVSInfo::_EVChargingMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::EVSInfo::_EVChargingEndMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::EVSInfo::_EVFunConferAckMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::EVSInfo::_EVElectricCtrl_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::EVSInfo::_EVInsultCtrl_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::EVSInfo::_EVStateMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::EVSInfo::_StepPara_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::EVSInfo::_EVSStepMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::EVSInfo::_EVMsgCtrl_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::EVSInfo::_IpMsg_default_instance_),
};

const char descriptor_table_protodef_GCU_5fEVS_5fINFO_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\022GCU_EVS_INFO.proto\022\007EVSInfo\"|\n\020EVProto"
  "ConferMsg\022\017\n\007canType\030\001 \001(\r\022\021\n\tgbVersion\030"
  "\002 \001(\014\022\027\n\017guidanceVersion\030\003 \001(\r\022\030\n\020transp"
  "ortVersion\030\004 \001(\r\022\021\n\tconferRes\030\005 \001(\r\"\243\001\n\016"
  "EVFunConferMsg\022\021\n\tconfigFDC\030\001 \001(\r\022\021\n\taut"
  "henFDC\030\002 \001(\r\022\022\n\nappointFDC\030\003 \001(\r\022\024\n\014self"
  "CheckFDC\030\004 \001(\r\022\026\n\016powerSupplyFDC\030\005 \001(\r\022\031"
  "\n\021energyTransferFDC\030\006 \001(\r\022\016\n\006endFDC\030\007 \001("
  "\r\"\256\001\n\022EVChgParaConfigMsg\022\024\n\014currAllowMAX"
  "\030\001 \001(\002\022\024\n\014voltAllowMAX\030\002 \001(\002\022\026\n\016energyAl"
  "lowMAX\030\003 \001(\002\022\016\n\006nowSOC\030\004 \001(\002\022\030\n\020cellVolt"
  "AllowMAX\030\005 \001(\002\022\027\n\017batTempAllowMAX\030\006 \001(\002\022"
  "\021\n\trestarNum\030\007 \001(\r\"D\n\013EVAuthenMsg\022\026\n\016aut"
  "henWaitTime\030\001 \001(\r\022\014\n\004eVIN\030\002 \001(\014\022\017\n\007nextF"
  "DC\030\003 \001(\r\"}\n\014EVReserveMsg\022\032\n\022bmsDesireSta"
  "rtTime\030\001 \001(\r\022\032\n\022bmsDesireLeaveTime\030\002 \001(\r"
  "\022\025\n\rreserveResult\030\003 \001(\r\022\036\n\026immediateChar"
  "geSupport\030\004 \001(\r\"m\n\020EVPowerSupplyMsg\022\023\n\013s"
  "upplyState\030\001 \001(\r\022\027\n\017supplyVolDesire\030\002 \001("
  "\002\022\030\n\020supplyCurrDesire\030\003 \001(\002\022\021\n\tsupplyEnd"
  "\030\004 \001(\r\"\327\001\n\rEVChargingMsg\022\020\n\010bmsReady\030\001 \001"
  "(\r\022\021\n\tvolDemand\030\002 \001(\002\022\021\n\tcurDemand\030\003 \001(\002"
  "\022\022\n\nchargeMode\030\004 \001(\r\022\016\n\006socNow\030\005 \001(\002\022\022\n\n"
  "resChgTime\030\006 \001(\r\022\025\n\rcellBatVolMax\030\007 \001(\002\022"
  "\025\n\rcellBatVolMin\030\010 \001(\002\022\023\n\013celltempMax\030\t "
  "\001(\002\022\023\n\013celltempMin\030\n \001(\002\"s\n\020EVChargingEn"
  "dMsg\022\017\n\007endType\030\001 \001(\r\022\017\n\007endCode\030\002 \001(\r\022\021"
  "\n\tendReason\030\003 \001(\r\022\016\n\006repeat\030\004 \001(\r\022\032\n\022bms"
  "StickCheckState\030\005 \001(\r\"\274\001\n\021EVFunConferAck"
  "Msg\022\024\n\014funConferAck\030\001 \001(\r\022\021\n\tconfigAck\030\002"
  " \001(\r\022\021\n\tauthenAck\030\003 \001(\r\022\022\n\nappointAck\030\004 "
  "\001(\r\022\024\n\014selfCheckAck\030\005 \001(\r\022\026\n\016powerSupply"
  "Ack\030\006 \001(\r\022\031\n\021energyTransferAck\030\007 \001(\r\022\016\n\006"
  "endAck\030\010 \001(\r\"\323\001\n\016EVElectricCtrl\022\021\n\tconta"
  "ctK5\030\001 \001(\r\022\021\n\tcontactK6\030\002 \001(\r\022\016\n\006CC1_S2\030"
  "\003 \001(\r\022\016\n\006CC2_S3\030\004 \001(\r\022\022\n\nelockState\030\005 \001("
  "\r\022\016\n\006canBus\030\006 \001(\r\022\r\n\005ev_PE\030\007 \001(\r\022\022\n\nevIn"
  "sultOn\030\010 \001(\r\022\023\n\013evContactK6\030\t \001(\r\022\017\n\007evP"
  "ause\030\n \001(\r\022\016\n\006sysFan\030\013 \001(\r\"_\n\014EVInsultCt"
  "rl\022\024\n\014insultPosRes\030\001 \001(\r\022\024\n\014insultNegRes"
  "\030\002 \001(\r\022\016\n\006batVol\030\003 \001(\002\022\023\n\013connectType\030\004 "
  "\001(\r\"\332\001\n\nEVStateMsg\022\016\n\006batVol\030\001 \001(\002\022\016\n\006ba"
  "tCur\030\002 \001(\002\022\016\n\006nowSOC\030\003 \001(\002\022\r\n\005nowFC\030\004 \001("
  "\r\022\016\n\006nowFDC\030\005 \001(\r\022\017\n\007chgMode\030\006 \001(\r\022\022\n\nin"
  "sultPosR\030\007 \001(\002\022\022\n\ninsultNegR\030\010 \001(\002\022\017\n\007cc"
  "1Volt\030\t \001(\002\022\020\n\010workMode\030\n \001(\r\022\020\n\010stopCod"
  "e\030\013 \001(\r\022\017\n\007cc2Volt\030\014 \001(\002\"\243\001\n\010StepPara\022\021\n"
  "\tstartTime\030\001 \001(\004\022\024\n\014intervalTime\030\002 \001(\004\022\022"
  "\n\nstartValue\030\003 \001(\002\022\020\n\010minValue\030\004 \001(\002\022\020\n\010"
  "maxValue\030\005 \001(\002\022\021\n\tstepValue\030\006 \001(\002\022\021\n\tcyc"
  "leMode\030\007 \001(\r\022\020\n\010stepMode\030\010 \001(\r\"\322\001\n\nEVSSt"
  "epMsg\022&\n\013needVolStep\030\001 \001(\0132\021.EVSInfo.Ste"
  "pPara\022\'\n\014needCurrStep\030\002 \001(\0132\021.EVSInfo.St"
  "epPara\022\"\n\007socStep\030\003 \001(\0132\021.EVSInfo.StepPa"
  "ra\022&\n\013cellVolStep\030\004 \001(\0132\021.EVSInfo.StepPa"
  "ra\022\'\n\014cellTempStep\030\005 \001(\0132\021.EVSInfo.StepP"
  "ara\"\362\005\n\tEVMsgCtrl\022\025\n\rx2_0x02_State\030\001 \001(\r"
  "\022\025\n\rx4_0x04_State\030\002 \001(\r\022\025\n\rx6_0x06_State"
  "\030\003 \001(\r\022\025\n\rx9_0x09_State\030\004 \001(\r\022\025\n\rb2_0x12"
  "_State\030\005 \001(\r\022\025\n\rc2_0x22_State\030\006 \001(\r\022\025\n\rc"
  "4_0x24_State\030\007 \001(\r\022\025\n\rd2_0x32_State\030\010 \001("
  "\r\022\025\n\rd4_0x34_State\030\t \001(\r\022\025\n\rd6_0x36_Stat"
  "e\030\n \001(\r\022\025\n\rd7_0x37_State\030\013 \001(\r\022\025\n\rd9_0x3"
  "9_State\030\014 \001(\r\022\026\n\016d10_0x3A_State\030\r \001(\r\022\025\n"
  "\re2_0x42_State\030\016 \001(\r\022\025\n\re4_0x44_State\030\017 "
  "\001(\r\022\025\n\rf2_0x52_State\030\020 \001(\r\022\025\n\rg2_0x62_St"
  "ate\030\021 \001(\r\022\025\n\rg3_0x63_State\030\022 \001(\r\022\025\n\rg5_0"
  "x65_State\030\023 \001(\r\022\025\n\rh2_0x72_State\030\024 \001(\r\022\025"
  "\n\rh3_0x73_State\030\025 \001(\r\022\025\n\rh4_0x74_State\030\026"
  " \001(\r\022\025\n\rh7_0x77_State\030\027 \001(\r\022\025\n\rh9_0x79_S"
  "tate\030\030 \001(\r\022\026\n\016h11_0x82_State\030\031 \001(\r\022\026\n\016h1"
  "3_0x84_State\030\032 \001(\r\022\026\n\016h14_0x85_State\030\033 \001"
  "(\r\022\026\n\016h16_0x87_State\030\034 \001(\r\022\026\n\016h18_0x89_S"
  "tate\030\035 \001(\r\022\026\n\016h20_0x8B_State\030\036 \001(\r\022\025\n\ri1"
  "_0x91_State\030\037 \001(\r\022\025\n\ri4_0x94_State\030  \001(\r"
  "\"Q\n\005IpMsg\022\016\n\006selfIP\030\001 \001(\014\022\020\n\010selfPort\030\002 "
  "\001(\r\022\021\n\tserviceIP\030\003 \001(\014\022\023\n\013servicePort\030\004 "
  "\001(\rb\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_GCU_5fEVS_5fINFO_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GCU_5fEVS_5fINFO_2eproto = {
  false, false, 3091, descriptor_table_protodef_GCU_5fEVS_5fINFO_2eproto, "GCU_EVS_INFO.proto", 
  &descriptor_table_GCU_5fEVS_5fINFO_2eproto_once, nullptr, 0, 16,
  schemas, file_default_instances, TableStruct_GCU_5fEVS_5fINFO_2eproto::offsets,
  file_level_metadata_GCU_5fEVS_5fINFO_2eproto, file_level_enum_descriptors_GCU_5fEVS_5fINFO_2eproto, file_level_service_descriptors_GCU_5fEVS_5fINFO_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_GCU_5fEVS_5fINFO_2eproto_getter() {
  return &descriptor_table_GCU_5fEVS_5fINFO_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_GCU_5fEVS_5fINFO_2eproto(&descriptor_table_GCU_5fEVS_5fINFO_2eproto);
namespace EVSInfo {

// ===================================================================

class EVProtoConferMsg::_Internal {
 public:
};

EVProtoConferMsg::EVProtoConferMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:EVSInfo.EVProtoConferMsg)
}
EVProtoConferMsg::EVProtoConferMsg(const EVProtoConferMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  gbversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_gbversion().empty()) {
    gbversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_gbversion(), 
      GetArenaForAllocation());
  }
  ::memcpy(&cantype_, &from.cantype_,
    static_cast<size_t>(reinterpret_cast<char*>(&conferres_) -
    reinterpret_cast<char*>(&cantype_)) + sizeof(conferres_));
  // @@protoc_insertion_point(copy_constructor:EVSInfo.EVProtoConferMsg)
}

inline void EVProtoConferMsg::SharedCtor() {
gbversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&cantype_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&conferres_) -
    reinterpret_cast<char*>(&cantype_)) + sizeof(conferres_));
}

EVProtoConferMsg::~EVProtoConferMsg() {
  // @@protoc_insertion_point(destructor:EVSInfo.EVProtoConferMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVProtoConferMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  gbversion_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void EVProtoConferMsg::ArenaDtor(void* object) {
  EVProtoConferMsg* _this = reinterpret_cast< EVProtoConferMsg* >(object);
  (void)_this;
}
void EVProtoConferMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVProtoConferMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVProtoConferMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:EVSInfo.EVProtoConferMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  gbversion_.ClearToEmpty();
  ::memset(&cantype_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&conferres_) -
      reinterpret_cast<char*>(&cantype_)) + sizeof(conferres_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVProtoConferMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 canType = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          cantype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes gbVersion = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_gbversion();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 guidanceVersion = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          guidanceversion_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 transportVersion = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          transportversion_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 conferRes = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          conferres_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVProtoConferMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:EVSInfo.EVProtoConferMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 canType = 1;
  if (this->_internal_cantype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_cantype(), target);
  }

  // bytes gbVersion = 2;
  if (!this->_internal_gbversion().empty()) {
    target = stream->WriteBytesMaybeAliased(
        2, this->_internal_gbversion(), target);
  }

  // uint32 guidanceVersion = 3;
  if (this->_internal_guidanceversion() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_guidanceversion(), target);
  }

  // uint32 transportVersion = 4;
  if (this->_internal_transportversion() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_transportversion(), target);
  }

  // uint32 conferRes = 5;
  if (this->_internal_conferres() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_conferres(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:EVSInfo.EVProtoConferMsg)
  return target;
}

size_t EVProtoConferMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:EVSInfo.EVProtoConferMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes gbVersion = 2;
  if (!this->_internal_gbversion().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_gbversion());
  }

  // uint32 canType = 1;
  if (this->_internal_cantype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_cantype());
  }

  // uint32 guidanceVersion = 3;
  if (this->_internal_guidanceversion() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_guidanceversion());
  }

  // uint32 transportVersion = 4;
  if (this->_internal_transportversion() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_transportversion());
  }

  // uint32 conferRes = 5;
  if (this->_internal_conferres() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_conferres());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVProtoConferMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVProtoConferMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVProtoConferMsg::GetClassData() const { return &_class_data_; }

void EVProtoConferMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVProtoConferMsg *>(to)->MergeFrom(
      static_cast<const EVProtoConferMsg &>(from));
}


void EVProtoConferMsg::MergeFrom(const EVProtoConferMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:EVSInfo.EVProtoConferMsg)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_gbversion().empty()) {
    _internal_set_gbversion(from._internal_gbversion());
  }
  if (from._internal_cantype() != 0) {
    _internal_set_cantype(from._internal_cantype());
  }
  if (from._internal_guidanceversion() != 0) {
    _internal_set_guidanceversion(from._internal_guidanceversion());
  }
  if (from._internal_transportversion() != 0) {
    _internal_set_transportversion(from._internal_transportversion());
  }
  if (from._internal_conferres() != 0) {
    _internal_set_conferres(from._internal_conferres());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVProtoConferMsg::CopyFrom(const EVProtoConferMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:EVSInfo.EVProtoConferMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVProtoConferMsg::IsInitialized() const {
  return true;
}

void EVProtoConferMsg::InternalSwap(EVProtoConferMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &gbversion_, GetArenaForAllocation(),
      &other->gbversion_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVProtoConferMsg, conferres_)
      + sizeof(EVProtoConferMsg::conferres_)
      - PROTOBUF_FIELD_OFFSET(EVProtoConferMsg, cantype_)>(
          reinterpret_cast<char*>(&cantype_),
          reinterpret_cast<char*>(&other->cantype_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVProtoConferMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fEVS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fINFO_2eproto[0]);
}

// ===================================================================

class EVFunConferMsg::_Internal {
 public:
};

EVFunConferMsg::EVFunConferMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:EVSInfo.EVFunConferMsg)
}
EVFunConferMsg::EVFunConferMsg(const EVFunConferMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&configfdc_, &from.configfdc_,
    static_cast<size_t>(reinterpret_cast<char*>(&endfdc_) -
    reinterpret_cast<char*>(&configfdc_)) + sizeof(endfdc_));
  // @@protoc_insertion_point(copy_constructor:EVSInfo.EVFunConferMsg)
}

inline void EVFunConferMsg::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&configfdc_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&endfdc_) -
    reinterpret_cast<char*>(&configfdc_)) + sizeof(endfdc_));
}

EVFunConferMsg::~EVFunConferMsg() {
  // @@protoc_insertion_point(destructor:EVSInfo.EVFunConferMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVFunConferMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void EVFunConferMsg::ArenaDtor(void* object) {
  EVFunConferMsg* _this = reinterpret_cast< EVFunConferMsg* >(object);
  (void)_this;
}
void EVFunConferMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVFunConferMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVFunConferMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:EVSInfo.EVFunConferMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&configfdc_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&endfdc_) -
      reinterpret_cast<char*>(&configfdc_)) + sizeof(endfdc_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVFunConferMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 configFDC = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          configfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 authenFDC = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          authenfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 appointFDC = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          appointfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 selfCheckFDC = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          selfcheckfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 powerSupplyFDC = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          powersupplyfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 energyTransferFDC = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          energytransferfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 endFDC = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          endfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVFunConferMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:EVSInfo.EVFunConferMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 configFDC = 1;
  if (this->_internal_configfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_configfdc(), target);
  }

  // uint32 authenFDC = 2;
  if (this->_internal_authenfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_authenfdc(), target);
  }

  // uint32 appointFDC = 3;
  if (this->_internal_appointfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_appointfdc(), target);
  }

  // uint32 selfCheckFDC = 4;
  if (this->_internal_selfcheckfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_selfcheckfdc(), target);
  }

  // uint32 powerSupplyFDC = 5;
  if (this->_internal_powersupplyfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_powersupplyfdc(), target);
  }

  // uint32 energyTransferFDC = 6;
  if (this->_internal_energytransferfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_energytransferfdc(), target);
  }

  // uint32 endFDC = 7;
  if (this->_internal_endfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(7, this->_internal_endfdc(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:EVSInfo.EVFunConferMsg)
  return target;
}

size_t EVFunConferMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:EVSInfo.EVFunConferMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 configFDC = 1;
  if (this->_internal_configfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_configfdc());
  }

  // uint32 authenFDC = 2;
  if (this->_internal_authenfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_authenfdc());
  }

  // uint32 appointFDC = 3;
  if (this->_internal_appointfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_appointfdc());
  }

  // uint32 selfCheckFDC = 4;
  if (this->_internal_selfcheckfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_selfcheckfdc());
  }

  // uint32 powerSupplyFDC = 5;
  if (this->_internal_powersupplyfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_powersupplyfdc());
  }

  // uint32 energyTransferFDC = 6;
  if (this->_internal_energytransferfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_energytransferfdc());
  }

  // uint32 endFDC = 7;
  if (this->_internal_endfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_endfdc());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVFunConferMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVFunConferMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVFunConferMsg::GetClassData() const { return &_class_data_; }

void EVFunConferMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVFunConferMsg *>(to)->MergeFrom(
      static_cast<const EVFunConferMsg &>(from));
}


void EVFunConferMsg::MergeFrom(const EVFunConferMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:EVSInfo.EVFunConferMsg)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_configfdc() != 0) {
    _internal_set_configfdc(from._internal_configfdc());
  }
  if (from._internal_authenfdc() != 0) {
    _internal_set_authenfdc(from._internal_authenfdc());
  }
  if (from._internal_appointfdc() != 0) {
    _internal_set_appointfdc(from._internal_appointfdc());
  }
  if (from._internal_selfcheckfdc() != 0) {
    _internal_set_selfcheckfdc(from._internal_selfcheckfdc());
  }
  if (from._internal_powersupplyfdc() != 0) {
    _internal_set_powersupplyfdc(from._internal_powersupplyfdc());
  }
  if (from._internal_energytransferfdc() != 0) {
    _internal_set_energytransferfdc(from._internal_energytransferfdc());
  }
  if (from._internal_endfdc() != 0) {
    _internal_set_endfdc(from._internal_endfdc());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVFunConferMsg::CopyFrom(const EVFunConferMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:EVSInfo.EVFunConferMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVFunConferMsg::IsInitialized() const {
  return true;
}

void EVFunConferMsg::InternalSwap(EVFunConferMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVFunConferMsg, endfdc_)
      + sizeof(EVFunConferMsg::endfdc_)
      - PROTOBUF_FIELD_OFFSET(EVFunConferMsg, configfdc_)>(
          reinterpret_cast<char*>(&configfdc_),
          reinterpret_cast<char*>(&other->configfdc_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVFunConferMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fEVS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fINFO_2eproto[1]);
}

// ===================================================================

class EVChgParaConfigMsg::_Internal {
 public:
};

EVChgParaConfigMsg::EVChgParaConfigMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:EVSInfo.EVChgParaConfigMsg)
}
EVChgParaConfigMsg::EVChgParaConfigMsg(const EVChgParaConfigMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&currallowmax_, &from.currallowmax_,
    static_cast<size_t>(reinterpret_cast<char*>(&restarnum_) -
    reinterpret_cast<char*>(&currallowmax_)) + sizeof(restarnum_));
  // @@protoc_insertion_point(copy_constructor:EVSInfo.EVChgParaConfigMsg)
}

inline void EVChgParaConfigMsg::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&currallowmax_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&restarnum_) -
    reinterpret_cast<char*>(&currallowmax_)) + sizeof(restarnum_));
}

EVChgParaConfigMsg::~EVChgParaConfigMsg() {
  // @@protoc_insertion_point(destructor:EVSInfo.EVChgParaConfigMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVChgParaConfigMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void EVChgParaConfigMsg::ArenaDtor(void* object) {
  EVChgParaConfigMsg* _this = reinterpret_cast< EVChgParaConfigMsg* >(object);
  (void)_this;
}
void EVChgParaConfigMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVChgParaConfigMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVChgParaConfigMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:EVSInfo.EVChgParaConfigMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&currallowmax_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&restarnum_) -
      reinterpret_cast<char*>(&currallowmax_)) + sizeof(restarnum_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVChgParaConfigMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float currAllowMAX = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          currallowmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float voltAllowMAX = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 21)) {
          voltallowmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float energyAllowMAX = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          energyallowmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float nowSOC = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 37)) {
          nowsoc_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float cellVoltAllowMAX = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 45)) {
          cellvoltallowmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float batTempAllowMAX = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 53)) {
          battempallowmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 restarNum = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          restarnum_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVChgParaConfigMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:EVSInfo.EVChgParaConfigMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float currAllowMAX = 1;
  if (!(this->_internal_currallowmax() <= 0 && this->_internal_currallowmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_currallowmax(), target);
  }

  // float voltAllowMAX = 2;
  if (!(this->_internal_voltallowmax() <= 0 && this->_internal_voltallowmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_voltallowmax(), target);
  }

  // float energyAllowMAX = 3;
  if (!(this->_internal_energyallowmax() <= 0 && this->_internal_energyallowmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_energyallowmax(), target);
  }

  // float nowSOC = 4;
  if (!(this->_internal_nowsoc() <= 0 && this->_internal_nowsoc() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_nowsoc(), target);
  }

  // float cellVoltAllowMAX = 5;
  if (!(this->_internal_cellvoltallowmax() <= 0 && this->_internal_cellvoltallowmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(5, this->_internal_cellvoltallowmax(), target);
  }

  // float batTempAllowMAX = 6;
  if (!(this->_internal_battempallowmax() <= 0 && this->_internal_battempallowmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(6, this->_internal_battempallowmax(), target);
  }

  // uint32 restarNum = 7;
  if (this->_internal_restarnum() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(7, this->_internal_restarnum(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:EVSInfo.EVChgParaConfigMsg)
  return target;
}

size_t EVChgParaConfigMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:EVSInfo.EVChgParaConfigMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float currAllowMAX = 1;
  if (!(this->_internal_currallowmax() <= 0 && this->_internal_currallowmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float voltAllowMAX = 2;
  if (!(this->_internal_voltallowmax() <= 0 && this->_internal_voltallowmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float energyAllowMAX = 3;
  if (!(this->_internal_energyallowmax() <= 0 && this->_internal_energyallowmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float nowSOC = 4;
  if (!(this->_internal_nowsoc() <= 0 && this->_internal_nowsoc() >= 0)) {
    total_size += 1 + 4;
  }

  // float cellVoltAllowMAX = 5;
  if (!(this->_internal_cellvoltallowmax() <= 0 && this->_internal_cellvoltallowmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float batTempAllowMAX = 6;
  if (!(this->_internal_battempallowmax() <= 0 && this->_internal_battempallowmax() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 restarNum = 7;
  if (this->_internal_restarnum() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_restarnum());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVChgParaConfigMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVChgParaConfigMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVChgParaConfigMsg::GetClassData() const { return &_class_data_; }

void EVChgParaConfigMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVChgParaConfigMsg *>(to)->MergeFrom(
      static_cast<const EVChgParaConfigMsg &>(from));
}


void EVChgParaConfigMsg::MergeFrom(const EVChgParaConfigMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:EVSInfo.EVChgParaConfigMsg)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from._internal_currallowmax() <= 0 && from._internal_currallowmax() >= 0)) {
    _internal_set_currallowmax(from._internal_currallowmax());
  }
  if (!(from._internal_voltallowmax() <= 0 && from._internal_voltallowmax() >= 0)) {
    _internal_set_voltallowmax(from._internal_voltallowmax());
  }
  if (!(from._internal_energyallowmax() <= 0 && from._internal_energyallowmax() >= 0)) {
    _internal_set_energyallowmax(from._internal_energyallowmax());
  }
  if (!(from._internal_nowsoc() <= 0 && from._internal_nowsoc() >= 0)) {
    _internal_set_nowsoc(from._internal_nowsoc());
  }
  if (!(from._internal_cellvoltallowmax() <= 0 && from._internal_cellvoltallowmax() >= 0)) {
    _internal_set_cellvoltallowmax(from._internal_cellvoltallowmax());
  }
  if (!(from._internal_battempallowmax() <= 0 && from._internal_battempallowmax() >= 0)) {
    _internal_set_battempallowmax(from._internal_battempallowmax());
  }
  if (from._internal_restarnum() != 0) {
    _internal_set_restarnum(from._internal_restarnum());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVChgParaConfigMsg::CopyFrom(const EVChgParaConfigMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:EVSInfo.EVChgParaConfigMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVChgParaConfigMsg::IsInitialized() const {
  return true;
}

void EVChgParaConfigMsg::InternalSwap(EVChgParaConfigMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVChgParaConfigMsg, restarnum_)
      + sizeof(EVChgParaConfigMsg::restarnum_)
      - PROTOBUF_FIELD_OFFSET(EVChgParaConfigMsg, currallowmax_)>(
          reinterpret_cast<char*>(&currallowmax_),
          reinterpret_cast<char*>(&other->currallowmax_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVChgParaConfigMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fEVS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fINFO_2eproto[2]);
}

// ===================================================================

class EVAuthenMsg::_Internal {
 public:
};

EVAuthenMsg::EVAuthenMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:EVSInfo.EVAuthenMsg)
}
EVAuthenMsg::EVAuthenMsg(const EVAuthenMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  evin_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_evin().empty()) {
    evin_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_evin(), 
      GetArenaForAllocation());
  }
  ::memcpy(&authenwaittime_, &from.authenwaittime_,
    static_cast<size_t>(reinterpret_cast<char*>(&nextfdc_) -
    reinterpret_cast<char*>(&authenwaittime_)) + sizeof(nextfdc_));
  // @@protoc_insertion_point(copy_constructor:EVSInfo.EVAuthenMsg)
}

inline void EVAuthenMsg::SharedCtor() {
evin_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&authenwaittime_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&nextfdc_) -
    reinterpret_cast<char*>(&authenwaittime_)) + sizeof(nextfdc_));
}

EVAuthenMsg::~EVAuthenMsg() {
  // @@protoc_insertion_point(destructor:EVSInfo.EVAuthenMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVAuthenMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  evin_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void EVAuthenMsg::ArenaDtor(void* object) {
  EVAuthenMsg* _this = reinterpret_cast< EVAuthenMsg* >(object);
  (void)_this;
}
void EVAuthenMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVAuthenMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVAuthenMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:EVSInfo.EVAuthenMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  evin_.ClearToEmpty();
  ::memset(&authenwaittime_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&nextfdc_) -
      reinterpret_cast<char*>(&authenwaittime_)) + sizeof(nextfdc_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVAuthenMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 authenWaitTime = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          authenwaittime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes eVIN = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_evin();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 nextFDC = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          nextfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVAuthenMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:EVSInfo.EVAuthenMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 authenWaitTime = 1;
  if (this->_internal_authenwaittime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_authenwaittime(), target);
  }

  // bytes eVIN = 2;
  if (!this->_internal_evin().empty()) {
    target = stream->WriteBytesMaybeAliased(
        2, this->_internal_evin(), target);
  }

  // uint32 nextFDC = 3;
  if (this->_internal_nextfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_nextfdc(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:EVSInfo.EVAuthenMsg)
  return target;
}

size_t EVAuthenMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:EVSInfo.EVAuthenMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes eVIN = 2;
  if (!this->_internal_evin().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_evin());
  }

  // uint32 authenWaitTime = 1;
  if (this->_internal_authenwaittime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_authenwaittime());
  }

  // uint32 nextFDC = 3;
  if (this->_internal_nextfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_nextfdc());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVAuthenMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVAuthenMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVAuthenMsg::GetClassData() const { return &_class_data_; }

void EVAuthenMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVAuthenMsg *>(to)->MergeFrom(
      static_cast<const EVAuthenMsg &>(from));
}


void EVAuthenMsg::MergeFrom(const EVAuthenMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:EVSInfo.EVAuthenMsg)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_evin().empty()) {
    _internal_set_evin(from._internal_evin());
  }
  if (from._internal_authenwaittime() != 0) {
    _internal_set_authenwaittime(from._internal_authenwaittime());
  }
  if (from._internal_nextfdc() != 0) {
    _internal_set_nextfdc(from._internal_nextfdc());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVAuthenMsg::CopyFrom(const EVAuthenMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:EVSInfo.EVAuthenMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVAuthenMsg::IsInitialized() const {
  return true;
}

void EVAuthenMsg::InternalSwap(EVAuthenMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &evin_, GetArenaForAllocation(),
      &other->evin_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVAuthenMsg, nextfdc_)
      + sizeof(EVAuthenMsg::nextfdc_)
      - PROTOBUF_FIELD_OFFSET(EVAuthenMsg, authenwaittime_)>(
          reinterpret_cast<char*>(&authenwaittime_),
          reinterpret_cast<char*>(&other->authenwaittime_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVAuthenMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fEVS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fINFO_2eproto[3]);
}

// ===================================================================

class EVReserveMsg::_Internal {
 public:
};

EVReserveMsg::EVReserveMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:EVSInfo.EVReserveMsg)
}
EVReserveMsg::EVReserveMsg(const EVReserveMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&bmsdesirestarttime_, &from.bmsdesirestarttime_,
    static_cast<size_t>(reinterpret_cast<char*>(&immediatechargesupport_) -
    reinterpret_cast<char*>(&bmsdesirestarttime_)) + sizeof(immediatechargesupport_));
  // @@protoc_insertion_point(copy_constructor:EVSInfo.EVReserveMsg)
}

inline void EVReserveMsg::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&bmsdesirestarttime_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&immediatechargesupport_) -
    reinterpret_cast<char*>(&bmsdesirestarttime_)) + sizeof(immediatechargesupport_));
}

EVReserveMsg::~EVReserveMsg() {
  // @@protoc_insertion_point(destructor:EVSInfo.EVReserveMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVReserveMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void EVReserveMsg::ArenaDtor(void* object) {
  EVReserveMsg* _this = reinterpret_cast< EVReserveMsg* >(object);
  (void)_this;
}
void EVReserveMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVReserveMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVReserveMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:EVSInfo.EVReserveMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&bmsdesirestarttime_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&immediatechargesupport_) -
      reinterpret_cast<char*>(&bmsdesirestarttime_)) + sizeof(immediatechargesupport_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVReserveMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 bmsDesireStartTime = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          bmsdesirestarttime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 bmsDesireLeaveTime = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          bmsdesireleavetime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 reserveResult = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          reserveresult_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 immediateChargeSupport = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          immediatechargesupport_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVReserveMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:EVSInfo.EVReserveMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 bmsDesireStartTime = 1;
  if (this->_internal_bmsdesirestarttime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_bmsdesirestarttime(), target);
  }

  // uint32 bmsDesireLeaveTime = 2;
  if (this->_internal_bmsdesireleavetime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_bmsdesireleavetime(), target);
  }

  // uint32 reserveResult = 3;
  if (this->_internal_reserveresult() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_reserveresult(), target);
  }

  // uint32 immediateChargeSupport = 4;
  if (this->_internal_immediatechargesupport() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_immediatechargesupport(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:EVSInfo.EVReserveMsg)
  return target;
}

size_t EVReserveMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:EVSInfo.EVReserveMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 bmsDesireStartTime = 1;
  if (this->_internal_bmsdesirestarttime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsdesirestarttime());
  }

  // uint32 bmsDesireLeaveTime = 2;
  if (this->_internal_bmsdesireleavetime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsdesireleavetime());
  }

  // uint32 reserveResult = 3;
  if (this->_internal_reserveresult() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_reserveresult());
  }

  // uint32 immediateChargeSupport = 4;
  if (this->_internal_immediatechargesupport() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_immediatechargesupport());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVReserveMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVReserveMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVReserveMsg::GetClassData() const { return &_class_data_; }

void EVReserveMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVReserveMsg *>(to)->MergeFrom(
      static_cast<const EVReserveMsg &>(from));
}


void EVReserveMsg::MergeFrom(const EVReserveMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:EVSInfo.EVReserveMsg)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_bmsdesirestarttime() != 0) {
    _internal_set_bmsdesirestarttime(from._internal_bmsdesirestarttime());
  }
  if (from._internal_bmsdesireleavetime() != 0) {
    _internal_set_bmsdesireleavetime(from._internal_bmsdesireleavetime());
  }
  if (from._internal_reserveresult() != 0) {
    _internal_set_reserveresult(from._internal_reserveresult());
  }
  if (from._internal_immediatechargesupport() != 0) {
    _internal_set_immediatechargesupport(from._internal_immediatechargesupport());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVReserveMsg::CopyFrom(const EVReserveMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:EVSInfo.EVReserveMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVReserveMsg::IsInitialized() const {
  return true;
}

void EVReserveMsg::InternalSwap(EVReserveMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVReserveMsg, immediatechargesupport_)
      + sizeof(EVReserveMsg::immediatechargesupport_)
      - PROTOBUF_FIELD_OFFSET(EVReserveMsg, bmsdesirestarttime_)>(
          reinterpret_cast<char*>(&bmsdesirestarttime_),
          reinterpret_cast<char*>(&other->bmsdesirestarttime_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVReserveMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fEVS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fINFO_2eproto[4]);
}

// ===================================================================

class EVPowerSupplyMsg::_Internal {
 public:
};

EVPowerSupplyMsg::EVPowerSupplyMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:EVSInfo.EVPowerSupplyMsg)
}
EVPowerSupplyMsg::EVPowerSupplyMsg(const EVPowerSupplyMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&supplystate_, &from.supplystate_,
    static_cast<size_t>(reinterpret_cast<char*>(&supplyend_) -
    reinterpret_cast<char*>(&supplystate_)) + sizeof(supplyend_));
  // @@protoc_insertion_point(copy_constructor:EVSInfo.EVPowerSupplyMsg)
}

inline void EVPowerSupplyMsg::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&supplystate_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&supplyend_) -
    reinterpret_cast<char*>(&supplystate_)) + sizeof(supplyend_));
}

EVPowerSupplyMsg::~EVPowerSupplyMsg() {
  // @@protoc_insertion_point(destructor:EVSInfo.EVPowerSupplyMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVPowerSupplyMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void EVPowerSupplyMsg::ArenaDtor(void* object) {
  EVPowerSupplyMsg* _this = reinterpret_cast< EVPowerSupplyMsg* >(object);
  (void)_this;
}
void EVPowerSupplyMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVPowerSupplyMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVPowerSupplyMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:EVSInfo.EVPowerSupplyMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&supplystate_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&supplyend_) -
      reinterpret_cast<char*>(&supplystate_)) + sizeof(supplyend_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVPowerSupplyMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 supplyState = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          supplystate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float supplyVolDesire = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 21)) {
          supplyvoldesire_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float supplyCurrDesire = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          supplycurrdesire_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 supplyEnd = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          supplyend_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVPowerSupplyMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:EVSInfo.EVPowerSupplyMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 supplyState = 1;
  if (this->_internal_supplystate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_supplystate(), target);
  }

  // float supplyVolDesire = 2;
  if (!(this->_internal_supplyvoldesire() <= 0 && this->_internal_supplyvoldesire() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_supplyvoldesire(), target);
  }

  // float supplyCurrDesire = 3;
  if (!(this->_internal_supplycurrdesire() <= 0 && this->_internal_supplycurrdesire() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_supplycurrdesire(), target);
  }

  // uint32 supplyEnd = 4;
  if (this->_internal_supplyend() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_supplyend(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:EVSInfo.EVPowerSupplyMsg)
  return target;
}

size_t EVPowerSupplyMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:EVSInfo.EVPowerSupplyMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 supplyState = 1;
  if (this->_internal_supplystate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_supplystate());
  }

  // float supplyVolDesire = 2;
  if (!(this->_internal_supplyvoldesire() <= 0 && this->_internal_supplyvoldesire() >= 0)) {
    total_size += 1 + 4;
  }

  // float supplyCurrDesire = 3;
  if (!(this->_internal_supplycurrdesire() <= 0 && this->_internal_supplycurrdesire() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 supplyEnd = 4;
  if (this->_internal_supplyend() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_supplyend());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVPowerSupplyMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVPowerSupplyMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVPowerSupplyMsg::GetClassData() const { return &_class_data_; }

void EVPowerSupplyMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVPowerSupplyMsg *>(to)->MergeFrom(
      static_cast<const EVPowerSupplyMsg &>(from));
}


void EVPowerSupplyMsg::MergeFrom(const EVPowerSupplyMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:EVSInfo.EVPowerSupplyMsg)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_supplystate() != 0) {
    _internal_set_supplystate(from._internal_supplystate());
  }
  if (!(from._internal_supplyvoldesire() <= 0 && from._internal_supplyvoldesire() >= 0)) {
    _internal_set_supplyvoldesire(from._internal_supplyvoldesire());
  }
  if (!(from._internal_supplycurrdesire() <= 0 && from._internal_supplycurrdesire() >= 0)) {
    _internal_set_supplycurrdesire(from._internal_supplycurrdesire());
  }
  if (from._internal_supplyend() != 0) {
    _internal_set_supplyend(from._internal_supplyend());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVPowerSupplyMsg::CopyFrom(const EVPowerSupplyMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:EVSInfo.EVPowerSupplyMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVPowerSupplyMsg::IsInitialized() const {
  return true;
}

void EVPowerSupplyMsg::InternalSwap(EVPowerSupplyMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVPowerSupplyMsg, supplyend_)
      + sizeof(EVPowerSupplyMsg::supplyend_)
      - PROTOBUF_FIELD_OFFSET(EVPowerSupplyMsg, supplystate_)>(
          reinterpret_cast<char*>(&supplystate_),
          reinterpret_cast<char*>(&other->supplystate_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVPowerSupplyMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fEVS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fINFO_2eproto[5]);
}

// ===================================================================

class EVChargingMsg::_Internal {
 public:
};

EVChargingMsg::EVChargingMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:EVSInfo.EVChargingMsg)
}
EVChargingMsg::EVChargingMsg(const EVChargingMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&bmsready_, &from.bmsready_,
    static_cast<size_t>(reinterpret_cast<char*>(&celltempmin_) -
    reinterpret_cast<char*>(&bmsready_)) + sizeof(celltempmin_));
  // @@protoc_insertion_point(copy_constructor:EVSInfo.EVChargingMsg)
}

inline void EVChargingMsg::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&bmsready_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&celltempmin_) -
    reinterpret_cast<char*>(&bmsready_)) + sizeof(celltempmin_));
}

EVChargingMsg::~EVChargingMsg() {
  // @@protoc_insertion_point(destructor:EVSInfo.EVChargingMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVChargingMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void EVChargingMsg::ArenaDtor(void* object) {
  EVChargingMsg* _this = reinterpret_cast< EVChargingMsg* >(object);
  (void)_this;
}
void EVChargingMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVChargingMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVChargingMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:EVSInfo.EVChargingMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&bmsready_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&celltempmin_) -
      reinterpret_cast<char*>(&bmsready_)) + sizeof(celltempmin_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVChargingMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 bmsReady = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          bmsready_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float volDemand = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 21)) {
          voldemand_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float curDemand = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          curdemand_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 chargeMode = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          chargemode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float socNow = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 45)) {
          socnow_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 resChgTime = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          reschgtime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float cellBatVolMax = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 61)) {
          cellbatvolmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float cellBatVolMin = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 69)) {
          cellbatvolmin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float celltempMax = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 77)) {
          celltempmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float celltempMin = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 85)) {
          celltempmin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVChargingMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:EVSInfo.EVChargingMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 bmsReady = 1;
  if (this->_internal_bmsready() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_bmsready(), target);
  }

  // float volDemand = 2;
  if (!(this->_internal_voldemand() <= 0 && this->_internal_voldemand() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_voldemand(), target);
  }

  // float curDemand = 3;
  if (!(this->_internal_curdemand() <= 0 && this->_internal_curdemand() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_curdemand(), target);
  }

  // uint32 chargeMode = 4;
  if (this->_internal_chargemode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_chargemode(), target);
  }

  // float socNow = 5;
  if (!(this->_internal_socnow() <= 0 && this->_internal_socnow() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(5, this->_internal_socnow(), target);
  }

  // uint32 resChgTime = 6;
  if (this->_internal_reschgtime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_reschgtime(), target);
  }

  // float cellBatVolMax = 7;
  if (!(this->_internal_cellbatvolmax() <= 0 && this->_internal_cellbatvolmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(7, this->_internal_cellbatvolmax(), target);
  }

  // float cellBatVolMin = 8;
  if (!(this->_internal_cellbatvolmin() <= 0 && this->_internal_cellbatvolmin() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(8, this->_internal_cellbatvolmin(), target);
  }

  // float celltempMax = 9;
  if (!(this->_internal_celltempmax() <= 0 && this->_internal_celltempmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(9, this->_internal_celltempmax(), target);
  }

  // float celltempMin = 10;
  if (!(this->_internal_celltempmin() <= 0 && this->_internal_celltempmin() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(10, this->_internal_celltempmin(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:EVSInfo.EVChargingMsg)
  return target;
}

size_t EVChargingMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:EVSInfo.EVChargingMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 bmsReady = 1;
  if (this->_internal_bmsready() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsready());
  }

  // float volDemand = 2;
  if (!(this->_internal_voldemand() <= 0 && this->_internal_voldemand() >= 0)) {
    total_size += 1 + 4;
  }

  // float curDemand = 3;
  if (!(this->_internal_curdemand() <= 0 && this->_internal_curdemand() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 chargeMode = 4;
  if (this->_internal_chargemode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chargemode());
  }

  // float socNow = 5;
  if (!(this->_internal_socnow() <= 0 && this->_internal_socnow() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 resChgTime = 6;
  if (this->_internal_reschgtime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_reschgtime());
  }

  // float cellBatVolMax = 7;
  if (!(this->_internal_cellbatvolmax() <= 0 && this->_internal_cellbatvolmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float cellBatVolMin = 8;
  if (!(this->_internal_cellbatvolmin() <= 0 && this->_internal_cellbatvolmin() >= 0)) {
    total_size += 1 + 4;
  }

  // float celltempMax = 9;
  if (!(this->_internal_celltempmax() <= 0 && this->_internal_celltempmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float celltempMin = 10;
  if (!(this->_internal_celltempmin() <= 0 && this->_internal_celltempmin() >= 0)) {
    total_size += 1 + 4;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVChargingMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVChargingMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVChargingMsg::GetClassData() const { return &_class_data_; }

void EVChargingMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVChargingMsg *>(to)->MergeFrom(
      static_cast<const EVChargingMsg &>(from));
}


void EVChargingMsg::MergeFrom(const EVChargingMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:EVSInfo.EVChargingMsg)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_bmsready() != 0) {
    _internal_set_bmsready(from._internal_bmsready());
  }
  if (!(from._internal_voldemand() <= 0 && from._internal_voldemand() >= 0)) {
    _internal_set_voldemand(from._internal_voldemand());
  }
  if (!(from._internal_curdemand() <= 0 && from._internal_curdemand() >= 0)) {
    _internal_set_curdemand(from._internal_curdemand());
  }
  if (from._internal_chargemode() != 0) {
    _internal_set_chargemode(from._internal_chargemode());
  }
  if (!(from._internal_socnow() <= 0 && from._internal_socnow() >= 0)) {
    _internal_set_socnow(from._internal_socnow());
  }
  if (from._internal_reschgtime() != 0) {
    _internal_set_reschgtime(from._internal_reschgtime());
  }
  if (!(from._internal_cellbatvolmax() <= 0 && from._internal_cellbatvolmax() >= 0)) {
    _internal_set_cellbatvolmax(from._internal_cellbatvolmax());
  }
  if (!(from._internal_cellbatvolmin() <= 0 && from._internal_cellbatvolmin() >= 0)) {
    _internal_set_cellbatvolmin(from._internal_cellbatvolmin());
  }
  if (!(from._internal_celltempmax() <= 0 && from._internal_celltempmax() >= 0)) {
    _internal_set_celltempmax(from._internal_celltempmax());
  }
  if (!(from._internal_celltempmin() <= 0 && from._internal_celltempmin() >= 0)) {
    _internal_set_celltempmin(from._internal_celltempmin());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVChargingMsg::CopyFrom(const EVChargingMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:EVSInfo.EVChargingMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVChargingMsg::IsInitialized() const {
  return true;
}

void EVChargingMsg::InternalSwap(EVChargingMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVChargingMsg, celltempmin_)
      + sizeof(EVChargingMsg::celltempmin_)
      - PROTOBUF_FIELD_OFFSET(EVChargingMsg, bmsready_)>(
          reinterpret_cast<char*>(&bmsready_),
          reinterpret_cast<char*>(&other->bmsready_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVChargingMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fEVS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fINFO_2eproto[6]);
}

// ===================================================================

class EVChargingEndMsg::_Internal {
 public:
};

EVChargingEndMsg::EVChargingEndMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:EVSInfo.EVChargingEndMsg)
}
EVChargingEndMsg::EVChargingEndMsg(const EVChargingEndMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&endtype_, &from.endtype_,
    static_cast<size_t>(reinterpret_cast<char*>(&bmsstickcheckstate_) -
    reinterpret_cast<char*>(&endtype_)) + sizeof(bmsstickcheckstate_));
  // @@protoc_insertion_point(copy_constructor:EVSInfo.EVChargingEndMsg)
}

inline void EVChargingEndMsg::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&endtype_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&bmsstickcheckstate_) -
    reinterpret_cast<char*>(&endtype_)) + sizeof(bmsstickcheckstate_));
}

EVChargingEndMsg::~EVChargingEndMsg() {
  // @@protoc_insertion_point(destructor:EVSInfo.EVChargingEndMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVChargingEndMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void EVChargingEndMsg::ArenaDtor(void* object) {
  EVChargingEndMsg* _this = reinterpret_cast< EVChargingEndMsg* >(object);
  (void)_this;
}
void EVChargingEndMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVChargingEndMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVChargingEndMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:EVSInfo.EVChargingEndMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&endtype_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&bmsstickcheckstate_) -
      reinterpret_cast<char*>(&endtype_)) + sizeof(bmsstickcheckstate_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVChargingEndMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 endType = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          endtype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 endCode = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          endcode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 endReason = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          endreason_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 repeat = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          repeat_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 bmsStickCheckState = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          bmsstickcheckstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVChargingEndMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:EVSInfo.EVChargingEndMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 endType = 1;
  if (this->_internal_endtype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_endtype(), target);
  }

  // uint32 endCode = 2;
  if (this->_internal_endcode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_endcode(), target);
  }

  // uint32 endReason = 3;
  if (this->_internal_endreason() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_endreason(), target);
  }

  // uint32 repeat = 4;
  if (this->_internal_repeat() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_repeat(), target);
  }

  // uint32 bmsStickCheckState = 5;
  if (this->_internal_bmsstickcheckstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_bmsstickcheckstate(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:EVSInfo.EVChargingEndMsg)
  return target;
}

size_t EVChargingEndMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:EVSInfo.EVChargingEndMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 endType = 1;
  if (this->_internal_endtype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_endtype());
  }

  // uint32 endCode = 2;
  if (this->_internal_endcode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_endcode());
  }

  // uint32 endReason = 3;
  if (this->_internal_endreason() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_endreason());
  }

  // uint32 repeat = 4;
  if (this->_internal_repeat() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_repeat());
  }

  // uint32 bmsStickCheckState = 5;
  if (this->_internal_bmsstickcheckstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsstickcheckstate());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVChargingEndMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVChargingEndMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVChargingEndMsg::GetClassData() const { return &_class_data_; }

void EVChargingEndMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVChargingEndMsg *>(to)->MergeFrom(
      static_cast<const EVChargingEndMsg &>(from));
}


void EVChargingEndMsg::MergeFrom(const EVChargingEndMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:EVSInfo.EVChargingEndMsg)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_endtype() != 0) {
    _internal_set_endtype(from._internal_endtype());
  }
  if (from._internal_endcode() != 0) {
    _internal_set_endcode(from._internal_endcode());
  }
  if (from._internal_endreason() != 0) {
    _internal_set_endreason(from._internal_endreason());
  }
  if (from._internal_repeat() != 0) {
    _internal_set_repeat(from._internal_repeat());
  }
  if (from._internal_bmsstickcheckstate() != 0) {
    _internal_set_bmsstickcheckstate(from._internal_bmsstickcheckstate());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVChargingEndMsg::CopyFrom(const EVChargingEndMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:EVSInfo.EVChargingEndMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVChargingEndMsg::IsInitialized() const {
  return true;
}

void EVChargingEndMsg::InternalSwap(EVChargingEndMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVChargingEndMsg, bmsstickcheckstate_)
      + sizeof(EVChargingEndMsg::bmsstickcheckstate_)
      - PROTOBUF_FIELD_OFFSET(EVChargingEndMsg, endtype_)>(
          reinterpret_cast<char*>(&endtype_),
          reinterpret_cast<char*>(&other->endtype_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVChargingEndMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fEVS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fINFO_2eproto[7]);
}

// ===================================================================

class EVFunConferAckMsg::_Internal {
 public:
};

EVFunConferAckMsg::EVFunConferAckMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:EVSInfo.EVFunConferAckMsg)
}
EVFunConferAckMsg::EVFunConferAckMsg(const EVFunConferAckMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&funconferack_, &from.funconferack_,
    static_cast<size_t>(reinterpret_cast<char*>(&endack_) -
    reinterpret_cast<char*>(&funconferack_)) + sizeof(endack_));
  // @@protoc_insertion_point(copy_constructor:EVSInfo.EVFunConferAckMsg)
}

inline void EVFunConferAckMsg::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&funconferack_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&endack_) -
    reinterpret_cast<char*>(&funconferack_)) + sizeof(endack_));
}

EVFunConferAckMsg::~EVFunConferAckMsg() {
  // @@protoc_insertion_point(destructor:EVSInfo.EVFunConferAckMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVFunConferAckMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void EVFunConferAckMsg::ArenaDtor(void* object) {
  EVFunConferAckMsg* _this = reinterpret_cast< EVFunConferAckMsg* >(object);
  (void)_this;
}
void EVFunConferAckMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVFunConferAckMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVFunConferAckMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:EVSInfo.EVFunConferAckMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&funconferack_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&endack_) -
      reinterpret_cast<char*>(&funconferack_)) + sizeof(endack_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVFunConferAckMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 funConferAck = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          funconferack_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 configAck = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          configack_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 authenAck = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          authenack_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 appointAck = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          appointack_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 selfCheckAck = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          selfcheckack_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 powerSupplyAck = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          powersupplyack_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 energyTransferAck = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          energytransferack_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 endAck = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          endack_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVFunConferAckMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:EVSInfo.EVFunConferAckMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 funConferAck = 1;
  if (this->_internal_funconferack() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_funconferack(), target);
  }

  // uint32 configAck = 2;
  if (this->_internal_configack() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_configack(), target);
  }

  // uint32 authenAck = 3;
  if (this->_internal_authenack() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_authenack(), target);
  }

  // uint32 appointAck = 4;
  if (this->_internal_appointack() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_appointack(), target);
  }

  // uint32 selfCheckAck = 5;
  if (this->_internal_selfcheckack() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_selfcheckack(), target);
  }

  // uint32 powerSupplyAck = 6;
  if (this->_internal_powersupplyack() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_powersupplyack(), target);
  }

  // uint32 energyTransferAck = 7;
  if (this->_internal_energytransferack() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(7, this->_internal_energytransferack(), target);
  }

  // uint32 endAck = 8;
  if (this->_internal_endack() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_endack(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:EVSInfo.EVFunConferAckMsg)
  return target;
}

size_t EVFunConferAckMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:EVSInfo.EVFunConferAckMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 funConferAck = 1;
  if (this->_internal_funconferack() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_funconferack());
  }

  // uint32 configAck = 2;
  if (this->_internal_configack() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_configack());
  }

  // uint32 authenAck = 3;
  if (this->_internal_authenack() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_authenack());
  }

  // uint32 appointAck = 4;
  if (this->_internal_appointack() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_appointack());
  }

  // uint32 selfCheckAck = 5;
  if (this->_internal_selfcheckack() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_selfcheckack());
  }

  // uint32 powerSupplyAck = 6;
  if (this->_internal_powersupplyack() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_powersupplyack());
  }

  // uint32 energyTransferAck = 7;
  if (this->_internal_energytransferack() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_energytransferack());
  }

  // uint32 endAck = 8;
  if (this->_internal_endack() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_endack());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVFunConferAckMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVFunConferAckMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVFunConferAckMsg::GetClassData() const { return &_class_data_; }

void EVFunConferAckMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVFunConferAckMsg *>(to)->MergeFrom(
      static_cast<const EVFunConferAckMsg &>(from));
}


void EVFunConferAckMsg::MergeFrom(const EVFunConferAckMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:EVSInfo.EVFunConferAckMsg)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_funconferack() != 0) {
    _internal_set_funconferack(from._internal_funconferack());
  }
  if (from._internal_configack() != 0) {
    _internal_set_configack(from._internal_configack());
  }
  if (from._internal_authenack() != 0) {
    _internal_set_authenack(from._internal_authenack());
  }
  if (from._internal_appointack() != 0) {
    _internal_set_appointack(from._internal_appointack());
  }
  if (from._internal_selfcheckack() != 0) {
    _internal_set_selfcheckack(from._internal_selfcheckack());
  }
  if (from._internal_powersupplyack() != 0) {
    _internal_set_powersupplyack(from._internal_powersupplyack());
  }
  if (from._internal_energytransferack() != 0) {
    _internal_set_energytransferack(from._internal_energytransferack());
  }
  if (from._internal_endack() != 0) {
    _internal_set_endack(from._internal_endack());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVFunConferAckMsg::CopyFrom(const EVFunConferAckMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:EVSInfo.EVFunConferAckMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVFunConferAckMsg::IsInitialized() const {
  return true;
}

void EVFunConferAckMsg::InternalSwap(EVFunConferAckMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVFunConferAckMsg, endack_)
      + sizeof(EVFunConferAckMsg::endack_)
      - PROTOBUF_FIELD_OFFSET(EVFunConferAckMsg, funconferack_)>(
          reinterpret_cast<char*>(&funconferack_),
          reinterpret_cast<char*>(&other->funconferack_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVFunConferAckMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fEVS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fINFO_2eproto[8]);
}

// ===================================================================

class EVElectricCtrl::_Internal {
 public:
};

EVElectricCtrl::EVElectricCtrl(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:EVSInfo.EVElectricCtrl)
}
EVElectricCtrl::EVElectricCtrl(const EVElectricCtrl& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&contactk5_, &from.contactk5_,
    static_cast<size_t>(reinterpret_cast<char*>(&sysfan_) -
    reinterpret_cast<char*>(&contactk5_)) + sizeof(sysfan_));
  // @@protoc_insertion_point(copy_constructor:EVSInfo.EVElectricCtrl)
}

inline void EVElectricCtrl::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&contactk5_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&sysfan_) -
    reinterpret_cast<char*>(&contactk5_)) + sizeof(sysfan_));
}

EVElectricCtrl::~EVElectricCtrl() {
  // @@protoc_insertion_point(destructor:EVSInfo.EVElectricCtrl)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVElectricCtrl::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void EVElectricCtrl::ArenaDtor(void* object) {
  EVElectricCtrl* _this = reinterpret_cast< EVElectricCtrl* >(object);
  (void)_this;
}
void EVElectricCtrl::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVElectricCtrl::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVElectricCtrl::Clear() {
// @@protoc_insertion_point(message_clear_start:EVSInfo.EVElectricCtrl)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&contactk5_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&sysfan_) -
      reinterpret_cast<char*>(&contactk5_)) + sizeof(sysfan_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVElectricCtrl::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 contactK5 = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          contactk5_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 contactK6 = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          contactk6_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 CC1_S2 = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          cc1_s2_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 CC2_S3 = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          cc2_s3_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 elockState = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          elockstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 canBus = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          canbus_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 ev_PE = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          ev_pe_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 evInsultOn = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          evinsulton_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 evContactK6 = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72)) {
          evcontactk6_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 evPause = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          evpause_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 sysFan = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          sysfan_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVElectricCtrl::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:EVSInfo.EVElectricCtrl)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 contactK5 = 1;
  if (this->_internal_contactk5() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_contactk5(), target);
  }

  // uint32 contactK6 = 2;
  if (this->_internal_contactk6() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_contactk6(), target);
  }

  // uint32 CC1_S2 = 3;
  if (this->_internal_cc1_s2() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_cc1_s2(), target);
  }

  // uint32 CC2_S3 = 4;
  if (this->_internal_cc2_s3() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_cc2_s3(), target);
  }

  // uint32 elockState = 5;
  if (this->_internal_elockstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_elockstate(), target);
  }

  // uint32 canBus = 6;
  if (this->_internal_canbus() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_canbus(), target);
  }

  // uint32 ev_PE = 7;
  if (this->_internal_ev_pe() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(7, this->_internal_ev_pe(), target);
  }

  // uint32 evInsultOn = 8;
  if (this->_internal_evinsulton() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_evinsulton(), target);
  }

  // uint32 evContactK6 = 9;
  if (this->_internal_evcontactk6() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(9, this->_internal_evcontactk6(), target);
  }

  // uint32 evPause = 10;
  if (this->_internal_evpause() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(10, this->_internal_evpause(), target);
  }

  // uint32 sysFan = 11;
  if (this->_internal_sysfan() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(11, this->_internal_sysfan(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:EVSInfo.EVElectricCtrl)
  return target;
}

size_t EVElectricCtrl::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:EVSInfo.EVElectricCtrl)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 contactK5 = 1;
  if (this->_internal_contactk5() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_contactk5());
  }

  // uint32 contactK6 = 2;
  if (this->_internal_contactk6() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_contactk6());
  }

  // uint32 CC1_S2 = 3;
  if (this->_internal_cc1_s2() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_cc1_s2());
  }

  // uint32 CC2_S3 = 4;
  if (this->_internal_cc2_s3() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_cc2_s3());
  }

  // uint32 elockState = 5;
  if (this->_internal_elockstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_elockstate());
  }

  // uint32 canBus = 6;
  if (this->_internal_canbus() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_canbus());
  }

  // uint32 ev_PE = 7;
  if (this->_internal_ev_pe() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_ev_pe());
  }

  // uint32 evInsultOn = 8;
  if (this->_internal_evinsulton() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_evinsulton());
  }

  // uint32 evContactK6 = 9;
  if (this->_internal_evcontactk6() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_evcontactk6());
  }

  // uint32 evPause = 10;
  if (this->_internal_evpause() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_evpause());
  }

  // uint32 sysFan = 11;
  if (this->_internal_sysfan() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_sysfan());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVElectricCtrl::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVElectricCtrl::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVElectricCtrl::GetClassData() const { return &_class_data_; }

void EVElectricCtrl::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVElectricCtrl *>(to)->MergeFrom(
      static_cast<const EVElectricCtrl &>(from));
}


void EVElectricCtrl::MergeFrom(const EVElectricCtrl& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:EVSInfo.EVElectricCtrl)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_contactk5() != 0) {
    _internal_set_contactk5(from._internal_contactk5());
  }
  if (from._internal_contactk6() != 0) {
    _internal_set_contactk6(from._internal_contactk6());
  }
  if (from._internal_cc1_s2() != 0) {
    _internal_set_cc1_s2(from._internal_cc1_s2());
  }
  if (from._internal_cc2_s3() != 0) {
    _internal_set_cc2_s3(from._internal_cc2_s3());
  }
  if (from._internal_elockstate() != 0) {
    _internal_set_elockstate(from._internal_elockstate());
  }
  if (from._internal_canbus() != 0) {
    _internal_set_canbus(from._internal_canbus());
  }
  if (from._internal_ev_pe() != 0) {
    _internal_set_ev_pe(from._internal_ev_pe());
  }
  if (from._internal_evinsulton() != 0) {
    _internal_set_evinsulton(from._internal_evinsulton());
  }
  if (from._internal_evcontactk6() != 0) {
    _internal_set_evcontactk6(from._internal_evcontactk6());
  }
  if (from._internal_evpause() != 0) {
    _internal_set_evpause(from._internal_evpause());
  }
  if (from._internal_sysfan() != 0) {
    _internal_set_sysfan(from._internal_sysfan());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVElectricCtrl::CopyFrom(const EVElectricCtrl& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:EVSInfo.EVElectricCtrl)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVElectricCtrl::IsInitialized() const {
  return true;
}

void EVElectricCtrl::InternalSwap(EVElectricCtrl* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVElectricCtrl, sysfan_)
      + sizeof(EVElectricCtrl::sysfan_)
      - PROTOBUF_FIELD_OFFSET(EVElectricCtrl, contactk5_)>(
          reinterpret_cast<char*>(&contactk5_),
          reinterpret_cast<char*>(&other->contactk5_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVElectricCtrl::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fEVS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fINFO_2eproto[9]);
}

// ===================================================================

class EVInsultCtrl::_Internal {
 public:
};

EVInsultCtrl::EVInsultCtrl(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:EVSInfo.EVInsultCtrl)
}
EVInsultCtrl::EVInsultCtrl(const EVInsultCtrl& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&insultposres_, &from.insultposres_,
    static_cast<size_t>(reinterpret_cast<char*>(&connecttype_) -
    reinterpret_cast<char*>(&insultposres_)) + sizeof(connecttype_));
  // @@protoc_insertion_point(copy_constructor:EVSInfo.EVInsultCtrl)
}

inline void EVInsultCtrl::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&insultposres_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&connecttype_) -
    reinterpret_cast<char*>(&insultposres_)) + sizeof(connecttype_));
}

EVInsultCtrl::~EVInsultCtrl() {
  // @@protoc_insertion_point(destructor:EVSInfo.EVInsultCtrl)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVInsultCtrl::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void EVInsultCtrl::ArenaDtor(void* object) {
  EVInsultCtrl* _this = reinterpret_cast< EVInsultCtrl* >(object);
  (void)_this;
}
void EVInsultCtrl::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVInsultCtrl::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVInsultCtrl::Clear() {
// @@protoc_insertion_point(message_clear_start:EVSInfo.EVInsultCtrl)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&insultposres_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&connecttype_) -
      reinterpret_cast<char*>(&insultposres_)) + sizeof(connecttype_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVInsultCtrl::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 insultPosRes = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          insultposres_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 insultNegRes = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          insultnegres_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float batVol = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          batvol_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 connectType = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          connecttype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVInsultCtrl::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:EVSInfo.EVInsultCtrl)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 insultPosRes = 1;
  if (this->_internal_insultposres() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_insultposres(), target);
  }

  // uint32 insultNegRes = 2;
  if (this->_internal_insultnegres() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_insultnegres(), target);
  }

  // float batVol = 3;
  if (!(this->_internal_batvol() <= 0 && this->_internal_batvol() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_batvol(), target);
  }

  // uint32 connectType = 4;
  if (this->_internal_connecttype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_connecttype(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:EVSInfo.EVInsultCtrl)
  return target;
}

size_t EVInsultCtrl::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:EVSInfo.EVInsultCtrl)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 insultPosRes = 1;
  if (this->_internal_insultposres() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_insultposres());
  }

  // uint32 insultNegRes = 2;
  if (this->_internal_insultnegres() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_insultnegres());
  }

  // float batVol = 3;
  if (!(this->_internal_batvol() <= 0 && this->_internal_batvol() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 connectType = 4;
  if (this->_internal_connecttype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_connecttype());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVInsultCtrl::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVInsultCtrl::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVInsultCtrl::GetClassData() const { return &_class_data_; }

void EVInsultCtrl::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVInsultCtrl *>(to)->MergeFrom(
      static_cast<const EVInsultCtrl &>(from));
}


void EVInsultCtrl::MergeFrom(const EVInsultCtrl& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:EVSInfo.EVInsultCtrl)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_insultposres() != 0) {
    _internal_set_insultposres(from._internal_insultposres());
  }
  if (from._internal_insultnegres() != 0) {
    _internal_set_insultnegres(from._internal_insultnegres());
  }
  if (!(from._internal_batvol() <= 0 && from._internal_batvol() >= 0)) {
    _internal_set_batvol(from._internal_batvol());
  }
  if (from._internal_connecttype() != 0) {
    _internal_set_connecttype(from._internal_connecttype());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVInsultCtrl::CopyFrom(const EVInsultCtrl& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:EVSInfo.EVInsultCtrl)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVInsultCtrl::IsInitialized() const {
  return true;
}

void EVInsultCtrl::InternalSwap(EVInsultCtrl* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVInsultCtrl, connecttype_)
      + sizeof(EVInsultCtrl::connecttype_)
      - PROTOBUF_FIELD_OFFSET(EVInsultCtrl, insultposres_)>(
          reinterpret_cast<char*>(&insultposres_),
          reinterpret_cast<char*>(&other->insultposres_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVInsultCtrl::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fEVS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fINFO_2eproto[10]);
}

// ===================================================================

class EVStateMsg::_Internal {
 public:
};

EVStateMsg::EVStateMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:EVSInfo.EVStateMsg)
}
EVStateMsg::EVStateMsg(const EVStateMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&batvol_, &from.batvol_,
    static_cast<size_t>(reinterpret_cast<char*>(&cc2volt_) -
    reinterpret_cast<char*>(&batvol_)) + sizeof(cc2volt_));
  // @@protoc_insertion_point(copy_constructor:EVSInfo.EVStateMsg)
}

inline void EVStateMsg::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&batvol_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&cc2volt_) -
    reinterpret_cast<char*>(&batvol_)) + sizeof(cc2volt_));
}

EVStateMsg::~EVStateMsg() {
  // @@protoc_insertion_point(destructor:EVSInfo.EVStateMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVStateMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void EVStateMsg::ArenaDtor(void* object) {
  EVStateMsg* _this = reinterpret_cast< EVStateMsg* >(object);
  (void)_this;
}
void EVStateMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVStateMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVStateMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:EVSInfo.EVStateMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&batvol_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&cc2volt_) -
      reinterpret_cast<char*>(&batvol_)) + sizeof(cc2volt_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVStateMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float batVol = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          batvol_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float batCur = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 21)) {
          batcur_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float nowSOC = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          nowsoc_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 nowFC = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          nowfc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 nowFDC = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          nowfdc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 chgMode = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          chgmode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float insultPosR = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 61)) {
          insultposr_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float insultNegR = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 69)) {
          insultnegr_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float cc1Volt = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 77)) {
          cc1volt_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 workMode = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          workmode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 stopCode = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          stopcode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float cc2Volt = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 101)) {
          cc2volt_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVStateMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:EVSInfo.EVStateMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float batVol = 1;
  if (!(this->_internal_batvol() <= 0 && this->_internal_batvol() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_batvol(), target);
  }

  // float batCur = 2;
  if (!(this->_internal_batcur() <= 0 && this->_internal_batcur() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_batcur(), target);
  }

  // float nowSOC = 3;
  if (!(this->_internal_nowsoc() <= 0 && this->_internal_nowsoc() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_nowsoc(), target);
  }

  // uint32 nowFC = 4;
  if (this->_internal_nowfc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_nowfc(), target);
  }

  // uint32 nowFDC = 5;
  if (this->_internal_nowfdc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_nowfdc(), target);
  }

  // uint32 chgMode = 6;
  if (this->_internal_chgmode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_chgmode(), target);
  }

  // float insultPosR = 7;
  if (!(this->_internal_insultposr() <= 0 && this->_internal_insultposr() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(7, this->_internal_insultposr(), target);
  }

  // float insultNegR = 8;
  if (!(this->_internal_insultnegr() <= 0 && this->_internal_insultnegr() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(8, this->_internal_insultnegr(), target);
  }

  // float cc1Volt = 9;
  if (!(this->_internal_cc1volt() <= 0 && this->_internal_cc1volt() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(9, this->_internal_cc1volt(), target);
  }

  // uint32 workMode = 10;
  if (this->_internal_workmode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(10, this->_internal_workmode(), target);
  }

  // uint32 stopCode = 11;
  if (this->_internal_stopcode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(11, this->_internal_stopcode(), target);
  }

  // float cc2Volt = 12;
  if (!(this->_internal_cc2volt() <= 0 && this->_internal_cc2volt() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(12, this->_internal_cc2volt(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:EVSInfo.EVStateMsg)
  return target;
}

size_t EVStateMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:EVSInfo.EVStateMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float batVol = 1;
  if (!(this->_internal_batvol() <= 0 && this->_internal_batvol() >= 0)) {
    total_size += 1 + 4;
  }

  // float batCur = 2;
  if (!(this->_internal_batcur() <= 0 && this->_internal_batcur() >= 0)) {
    total_size += 1 + 4;
  }

  // float nowSOC = 3;
  if (!(this->_internal_nowsoc() <= 0 && this->_internal_nowsoc() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 nowFC = 4;
  if (this->_internal_nowfc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_nowfc());
  }

  // uint32 nowFDC = 5;
  if (this->_internal_nowfdc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_nowfdc());
  }

  // uint32 chgMode = 6;
  if (this->_internal_chgmode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_chgmode());
  }

  // float insultPosR = 7;
  if (!(this->_internal_insultposr() <= 0 && this->_internal_insultposr() >= 0)) {
    total_size += 1 + 4;
  }

  // float insultNegR = 8;
  if (!(this->_internal_insultnegr() <= 0 && this->_internal_insultnegr() >= 0)) {
    total_size += 1 + 4;
  }

  // float cc1Volt = 9;
  if (!(this->_internal_cc1volt() <= 0 && this->_internal_cc1volt() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 workMode = 10;
  if (this->_internal_workmode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_workmode());
  }

  // uint32 stopCode = 11;
  if (this->_internal_stopcode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_stopcode());
  }

  // float cc2Volt = 12;
  if (!(this->_internal_cc2volt() <= 0 && this->_internal_cc2volt() >= 0)) {
    total_size += 1 + 4;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVStateMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVStateMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVStateMsg::GetClassData() const { return &_class_data_; }

void EVStateMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVStateMsg *>(to)->MergeFrom(
      static_cast<const EVStateMsg &>(from));
}


void EVStateMsg::MergeFrom(const EVStateMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:EVSInfo.EVStateMsg)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from._internal_batvol() <= 0 && from._internal_batvol() >= 0)) {
    _internal_set_batvol(from._internal_batvol());
  }
  if (!(from._internal_batcur() <= 0 && from._internal_batcur() >= 0)) {
    _internal_set_batcur(from._internal_batcur());
  }
  if (!(from._internal_nowsoc() <= 0 && from._internal_nowsoc() >= 0)) {
    _internal_set_nowsoc(from._internal_nowsoc());
  }
  if (from._internal_nowfc() != 0) {
    _internal_set_nowfc(from._internal_nowfc());
  }
  if (from._internal_nowfdc() != 0) {
    _internal_set_nowfdc(from._internal_nowfdc());
  }
  if (from._internal_chgmode() != 0) {
    _internal_set_chgmode(from._internal_chgmode());
  }
  if (!(from._internal_insultposr() <= 0 && from._internal_insultposr() >= 0)) {
    _internal_set_insultposr(from._internal_insultposr());
  }
  if (!(from._internal_insultnegr() <= 0 && from._internal_insultnegr() >= 0)) {
    _internal_set_insultnegr(from._internal_insultnegr());
  }
  if (!(from._internal_cc1volt() <= 0 && from._internal_cc1volt() >= 0)) {
    _internal_set_cc1volt(from._internal_cc1volt());
  }
  if (from._internal_workmode() != 0) {
    _internal_set_workmode(from._internal_workmode());
  }
  if (from._internal_stopcode() != 0) {
    _internal_set_stopcode(from._internal_stopcode());
  }
  if (!(from._internal_cc2volt() <= 0 && from._internal_cc2volt() >= 0)) {
    _internal_set_cc2volt(from._internal_cc2volt());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVStateMsg::CopyFrom(const EVStateMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:EVSInfo.EVStateMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVStateMsg::IsInitialized() const {
  return true;
}

void EVStateMsg::InternalSwap(EVStateMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVStateMsg, cc2volt_)
      + sizeof(EVStateMsg::cc2volt_)
      - PROTOBUF_FIELD_OFFSET(EVStateMsg, batvol_)>(
          reinterpret_cast<char*>(&batvol_),
          reinterpret_cast<char*>(&other->batvol_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVStateMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fEVS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fINFO_2eproto[11]);
}

// ===================================================================

class StepPara::_Internal {
 public:
};

StepPara::StepPara(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:EVSInfo.StepPara)
}
StepPara::StepPara(const StepPara& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&starttime_, &from.starttime_,
    static_cast<size_t>(reinterpret_cast<char*>(&stepmode_) -
    reinterpret_cast<char*>(&starttime_)) + sizeof(stepmode_));
  // @@protoc_insertion_point(copy_constructor:EVSInfo.StepPara)
}

inline void StepPara::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&starttime_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&stepmode_) -
    reinterpret_cast<char*>(&starttime_)) + sizeof(stepmode_));
}

StepPara::~StepPara() {
  // @@protoc_insertion_point(destructor:EVSInfo.StepPara)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void StepPara::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void StepPara::ArenaDtor(void* object) {
  StepPara* _this = reinterpret_cast< StepPara* >(object);
  (void)_this;
}
void StepPara::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void StepPara::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void StepPara::Clear() {
// @@protoc_insertion_point(message_clear_start:EVSInfo.StepPara)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&starttime_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&stepmode_) -
      reinterpret_cast<char*>(&starttime_)) + sizeof(stepmode_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* StepPara::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 startTime = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          starttime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint64 intervalTime = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          intervaltime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float startValue = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          startvalue_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float minValue = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 37)) {
          minvalue_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float maxValue = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 45)) {
          maxvalue_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float stepValue = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 53)) {
          stepvalue_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 cycleMode = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          cyclemode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 stepMode = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          stepmode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* StepPara::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:EVSInfo.StepPara)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 startTime = 1;
  if (this->_internal_starttime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_starttime(), target);
  }

  // uint64 intervalTime = 2;
  if (this->_internal_intervaltime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(2, this->_internal_intervaltime(), target);
  }

  // float startValue = 3;
  if (!(this->_internal_startvalue() <= 0 && this->_internal_startvalue() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_startvalue(), target);
  }

  // float minValue = 4;
  if (!(this->_internal_minvalue() <= 0 && this->_internal_minvalue() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_minvalue(), target);
  }

  // float maxValue = 5;
  if (!(this->_internal_maxvalue() <= 0 && this->_internal_maxvalue() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(5, this->_internal_maxvalue(), target);
  }

  // float stepValue = 6;
  if (!(this->_internal_stepvalue() <= 0 && this->_internal_stepvalue() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(6, this->_internal_stepvalue(), target);
  }

  // uint32 cycleMode = 7;
  if (this->_internal_cyclemode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(7, this->_internal_cyclemode(), target);
  }

  // uint32 stepMode = 8;
  if (this->_internal_stepmode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_stepmode(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:EVSInfo.StepPara)
  return target;
}

size_t StepPara::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:EVSInfo.StepPara)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint64 startTime = 1;
  if (this->_internal_starttime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_starttime());
  }

  // uint64 intervalTime = 2;
  if (this->_internal_intervaltime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_intervaltime());
  }

  // float startValue = 3;
  if (!(this->_internal_startvalue() <= 0 && this->_internal_startvalue() >= 0)) {
    total_size += 1 + 4;
  }

  // float minValue = 4;
  if (!(this->_internal_minvalue() <= 0 && this->_internal_minvalue() >= 0)) {
    total_size += 1 + 4;
  }

  // float maxValue = 5;
  if (!(this->_internal_maxvalue() <= 0 && this->_internal_maxvalue() >= 0)) {
    total_size += 1 + 4;
  }

  // float stepValue = 6;
  if (!(this->_internal_stepvalue() <= 0 && this->_internal_stepvalue() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 cycleMode = 7;
  if (this->_internal_cyclemode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_cyclemode());
  }

  // uint32 stepMode = 8;
  if (this->_internal_stepmode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_stepmode());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData StepPara::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    StepPara::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*StepPara::GetClassData() const { return &_class_data_; }

void StepPara::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<StepPara *>(to)->MergeFrom(
      static_cast<const StepPara &>(from));
}


void StepPara::MergeFrom(const StepPara& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:EVSInfo.StepPara)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_starttime() != 0) {
    _internal_set_starttime(from._internal_starttime());
  }
  if (from._internal_intervaltime() != 0) {
    _internal_set_intervaltime(from._internal_intervaltime());
  }
  if (!(from._internal_startvalue() <= 0 && from._internal_startvalue() >= 0)) {
    _internal_set_startvalue(from._internal_startvalue());
  }
  if (!(from._internal_minvalue() <= 0 && from._internal_minvalue() >= 0)) {
    _internal_set_minvalue(from._internal_minvalue());
  }
  if (!(from._internal_maxvalue() <= 0 && from._internal_maxvalue() >= 0)) {
    _internal_set_maxvalue(from._internal_maxvalue());
  }
  if (!(from._internal_stepvalue() <= 0 && from._internal_stepvalue() >= 0)) {
    _internal_set_stepvalue(from._internal_stepvalue());
  }
  if (from._internal_cyclemode() != 0) {
    _internal_set_cyclemode(from._internal_cyclemode());
  }
  if (from._internal_stepmode() != 0) {
    _internal_set_stepmode(from._internal_stepmode());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void StepPara::CopyFrom(const StepPara& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:EVSInfo.StepPara)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StepPara::IsInitialized() const {
  return true;
}

void StepPara::InternalSwap(StepPara* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(StepPara, stepmode_)
      + sizeof(StepPara::stepmode_)
      - PROTOBUF_FIELD_OFFSET(StepPara, starttime_)>(
          reinterpret_cast<char*>(&starttime_),
          reinterpret_cast<char*>(&other->starttime_));
}

::PROTOBUF_NAMESPACE_ID::Metadata StepPara::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fEVS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fINFO_2eproto[12]);
}

// ===================================================================

class EVSStepMsg::_Internal {
 public:
  static const ::EVSInfo::StepPara& needvolstep(const EVSStepMsg* msg);
  static const ::EVSInfo::StepPara& needcurrstep(const EVSStepMsg* msg);
  static const ::EVSInfo::StepPara& socstep(const EVSStepMsg* msg);
  static const ::EVSInfo::StepPara& cellvolstep(const EVSStepMsg* msg);
  static const ::EVSInfo::StepPara& celltempstep(const EVSStepMsg* msg);
};

const ::EVSInfo::StepPara&
EVSStepMsg::_Internal::needvolstep(const EVSStepMsg* msg) {
  return *msg->needvolstep_;
}
const ::EVSInfo::StepPara&
EVSStepMsg::_Internal::needcurrstep(const EVSStepMsg* msg) {
  return *msg->needcurrstep_;
}
const ::EVSInfo::StepPara&
EVSStepMsg::_Internal::socstep(const EVSStepMsg* msg) {
  return *msg->socstep_;
}
const ::EVSInfo::StepPara&
EVSStepMsg::_Internal::cellvolstep(const EVSStepMsg* msg) {
  return *msg->cellvolstep_;
}
const ::EVSInfo::StepPara&
EVSStepMsg::_Internal::celltempstep(const EVSStepMsg* msg) {
  return *msg->celltempstep_;
}
EVSStepMsg::EVSStepMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:EVSInfo.EVSStepMsg)
}
EVSStepMsg::EVSStepMsg(const EVSStepMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_needvolstep()) {
    needvolstep_ = new ::EVSInfo::StepPara(*from.needvolstep_);
  } else {
    needvolstep_ = nullptr;
  }
  if (from._internal_has_needcurrstep()) {
    needcurrstep_ = new ::EVSInfo::StepPara(*from.needcurrstep_);
  } else {
    needcurrstep_ = nullptr;
  }
  if (from._internal_has_socstep()) {
    socstep_ = new ::EVSInfo::StepPara(*from.socstep_);
  } else {
    socstep_ = nullptr;
  }
  if (from._internal_has_cellvolstep()) {
    cellvolstep_ = new ::EVSInfo::StepPara(*from.cellvolstep_);
  } else {
    cellvolstep_ = nullptr;
  }
  if (from._internal_has_celltempstep()) {
    celltempstep_ = new ::EVSInfo::StepPara(*from.celltempstep_);
  } else {
    celltempstep_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:EVSInfo.EVSStepMsg)
}

inline void EVSStepMsg::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&needvolstep_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&celltempstep_) -
    reinterpret_cast<char*>(&needvolstep_)) + sizeof(celltempstep_));
}

EVSStepMsg::~EVSStepMsg() {
  // @@protoc_insertion_point(destructor:EVSInfo.EVSStepMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVSStepMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete needvolstep_;
  if (this != internal_default_instance()) delete needcurrstep_;
  if (this != internal_default_instance()) delete socstep_;
  if (this != internal_default_instance()) delete cellvolstep_;
  if (this != internal_default_instance()) delete celltempstep_;
}

void EVSStepMsg::ArenaDtor(void* object) {
  EVSStepMsg* _this = reinterpret_cast< EVSStepMsg* >(object);
  (void)_this;
}
void EVSStepMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVSStepMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVSStepMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:EVSInfo.EVSStepMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && needvolstep_ != nullptr) {
    delete needvolstep_;
  }
  needvolstep_ = nullptr;
  if (GetArenaForAllocation() == nullptr && needcurrstep_ != nullptr) {
    delete needcurrstep_;
  }
  needcurrstep_ = nullptr;
  if (GetArenaForAllocation() == nullptr && socstep_ != nullptr) {
    delete socstep_;
  }
  socstep_ = nullptr;
  if (GetArenaForAllocation() == nullptr && cellvolstep_ != nullptr) {
    delete cellvolstep_;
  }
  cellvolstep_ = nullptr;
  if (GetArenaForAllocation() == nullptr && celltempstep_ != nullptr) {
    delete celltempstep_;
  }
  celltempstep_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVSStepMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .EVSInfo.StepPara needVolStep = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_needvolstep(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .EVSInfo.StepPara needCurrStep = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_needcurrstep(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .EVSInfo.StepPara socStep = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_socstep(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .EVSInfo.StepPara cellVolStep = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_cellvolstep(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .EVSInfo.StepPara cellTempStep = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_celltempstep(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVSStepMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:EVSInfo.EVSStepMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .EVSInfo.StepPara needVolStep = 1;
  if (this->_internal_has_needvolstep()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::needvolstep(this), target, stream);
  }

  // .EVSInfo.StepPara needCurrStep = 2;
  if (this->_internal_has_needcurrstep()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::needcurrstep(this), target, stream);
  }

  // .EVSInfo.StepPara socStep = 3;
  if (this->_internal_has_socstep()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::socstep(this), target, stream);
  }

  // .EVSInfo.StepPara cellVolStep = 4;
  if (this->_internal_has_cellvolstep()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::cellvolstep(this), target, stream);
  }

  // .EVSInfo.StepPara cellTempStep = 5;
  if (this->_internal_has_celltempstep()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::celltempstep(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:EVSInfo.EVSStepMsg)
  return target;
}

size_t EVSStepMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:EVSInfo.EVSStepMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .EVSInfo.StepPara needVolStep = 1;
  if (this->_internal_has_needvolstep()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *needvolstep_);
  }

  // .EVSInfo.StepPara needCurrStep = 2;
  if (this->_internal_has_needcurrstep()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *needcurrstep_);
  }

  // .EVSInfo.StepPara socStep = 3;
  if (this->_internal_has_socstep()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *socstep_);
  }

  // .EVSInfo.StepPara cellVolStep = 4;
  if (this->_internal_has_cellvolstep()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *cellvolstep_);
  }

  // .EVSInfo.StepPara cellTempStep = 5;
  if (this->_internal_has_celltempstep()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *celltempstep_);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVSStepMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVSStepMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVSStepMsg::GetClassData() const { return &_class_data_; }

void EVSStepMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVSStepMsg *>(to)->MergeFrom(
      static_cast<const EVSStepMsg &>(from));
}


void EVSStepMsg::MergeFrom(const EVSStepMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:EVSInfo.EVSStepMsg)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_needvolstep()) {
    _internal_mutable_needvolstep()->::EVSInfo::StepPara::MergeFrom(from._internal_needvolstep());
  }
  if (from._internal_has_needcurrstep()) {
    _internal_mutable_needcurrstep()->::EVSInfo::StepPara::MergeFrom(from._internal_needcurrstep());
  }
  if (from._internal_has_socstep()) {
    _internal_mutable_socstep()->::EVSInfo::StepPara::MergeFrom(from._internal_socstep());
  }
  if (from._internal_has_cellvolstep()) {
    _internal_mutable_cellvolstep()->::EVSInfo::StepPara::MergeFrom(from._internal_cellvolstep());
  }
  if (from._internal_has_celltempstep()) {
    _internal_mutable_celltempstep()->::EVSInfo::StepPara::MergeFrom(from._internal_celltempstep());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVSStepMsg::CopyFrom(const EVSStepMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:EVSInfo.EVSStepMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVSStepMsg::IsInitialized() const {
  return true;
}

void EVSStepMsg::InternalSwap(EVSStepMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVSStepMsg, celltempstep_)
      + sizeof(EVSStepMsg::celltempstep_)
      - PROTOBUF_FIELD_OFFSET(EVSStepMsg, needvolstep_)>(
          reinterpret_cast<char*>(&needvolstep_),
          reinterpret_cast<char*>(&other->needvolstep_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVSStepMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fEVS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fINFO_2eproto[13]);
}

// ===================================================================

class EVMsgCtrl::_Internal {
 public:
};

EVMsgCtrl::EVMsgCtrl(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:EVSInfo.EVMsgCtrl)
}
EVMsgCtrl::EVMsgCtrl(const EVMsgCtrl& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&x2_0x02_state_, &from.x2_0x02_state_,
    static_cast<size_t>(reinterpret_cast<char*>(&i4_0x94_state_) -
    reinterpret_cast<char*>(&x2_0x02_state_)) + sizeof(i4_0x94_state_));
  // @@protoc_insertion_point(copy_constructor:EVSInfo.EVMsgCtrl)
}

inline void EVMsgCtrl::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&x2_0x02_state_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&i4_0x94_state_) -
    reinterpret_cast<char*>(&x2_0x02_state_)) + sizeof(i4_0x94_state_));
}

EVMsgCtrl::~EVMsgCtrl() {
  // @@protoc_insertion_point(destructor:EVSInfo.EVMsgCtrl)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVMsgCtrl::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void EVMsgCtrl::ArenaDtor(void* object) {
  EVMsgCtrl* _this = reinterpret_cast< EVMsgCtrl* >(object);
  (void)_this;
}
void EVMsgCtrl::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVMsgCtrl::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVMsgCtrl::Clear() {
// @@protoc_insertion_point(message_clear_start:EVSInfo.EVMsgCtrl)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&x2_0x02_state_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&i4_0x94_state_) -
      reinterpret_cast<char*>(&x2_0x02_state_)) + sizeof(i4_0x94_state_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVMsgCtrl::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 x2_0x02_State = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          x2_0x02_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 x4_0x04_State = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          x4_0x04_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 x6_0x06_State = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          x6_0x06_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 x9_0x09_State = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          x9_0x09_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 b2_0x12_State = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          b2_0x12_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 c2_0x22_State = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          c2_0x22_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 c4_0x24_State = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          c4_0x24_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 d2_0x32_State = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          d2_0x32_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 d4_0x34_State = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72)) {
          d4_0x34_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 d6_0x36_State = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          d6_0x36_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 d7_0x37_State = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          d7_0x37_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 d9_0x39_State = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 96)) {
          d9_0x39_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 d10_0x3A_State = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 104)) {
          d10_0x3a_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 e2_0x42_State = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 112)) {
          e2_0x42_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 e4_0x44_State = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 120)) {
          e4_0x44_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 f2_0x52_State = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 128)) {
          f2_0x52_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 g2_0x62_State = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 136)) {
          g2_0x62_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 g3_0x63_State = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 144)) {
          g3_0x63_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 g5_0x65_State = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 152)) {
          g5_0x65_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 h2_0x72_State = 20;
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 160)) {
          h2_0x72_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 h3_0x73_State = 21;
      case 21:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 168)) {
          h3_0x73_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 h4_0x74_State = 22;
      case 22:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 176)) {
          h4_0x74_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 h7_0x77_State = 23;
      case 23:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 184)) {
          h7_0x77_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 h9_0x79_State = 24;
      case 24:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 192)) {
          h9_0x79_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 h11_0x82_State = 25;
      case 25:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 200)) {
          h11_0x82_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 h13_0x84_State = 26;
      case 26:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 208)) {
          h13_0x84_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 h14_0x85_State = 27;
      case 27:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 216)) {
          h14_0x85_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 h16_0x87_State = 28;
      case 28:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 224)) {
          h16_0x87_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 h18_0x89_State = 29;
      case 29:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 232)) {
          h18_0x89_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 h20_0x8B_State = 30;
      case 30:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 240)) {
          h20_0x8b_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 i1_0x91_State = 31;
      case 31:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 248)) {
          i1_0x91_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 i4_0x94_State = 32;
      case 32:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 0)) {
          i4_0x94_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVMsgCtrl::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:EVSInfo.EVMsgCtrl)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 x2_0x02_State = 1;
  if (this->_internal_x2_0x02_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_x2_0x02_state(), target);
  }

  // uint32 x4_0x04_State = 2;
  if (this->_internal_x4_0x04_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_x4_0x04_state(), target);
  }

  // uint32 x6_0x06_State = 3;
  if (this->_internal_x6_0x06_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_x6_0x06_state(), target);
  }

  // uint32 x9_0x09_State = 4;
  if (this->_internal_x9_0x09_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_x9_0x09_state(), target);
  }

  // uint32 b2_0x12_State = 5;
  if (this->_internal_b2_0x12_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_b2_0x12_state(), target);
  }

  // uint32 c2_0x22_State = 6;
  if (this->_internal_c2_0x22_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_c2_0x22_state(), target);
  }

  // uint32 c4_0x24_State = 7;
  if (this->_internal_c4_0x24_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(7, this->_internal_c4_0x24_state(), target);
  }

  // uint32 d2_0x32_State = 8;
  if (this->_internal_d2_0x32_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_d2_0x32_state(), target);
  }

  // uint32 d4_0x34_State = 9;
  if (this->_internal_d4_0x34_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(9, this->_internal_d4_0x34_state(), target);
  }

  // uint32 d6_0x36_State = 10;
  if (this->_internal_d6_0x36_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(10, this->_internal_d6_0x36_state(), target);
  }

  // uint32 d7_0x37_State = 11;
  if (this->_internal_d7_0x37_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(11, this->_internal_d7_0x37_state(), target);
  }

  // uint32 d9_0x39_State = 12;
  if (this->_internal_d9_0x39_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(12, this->_internal_d9_0x39_state(), target);
  }

  // uint32 d10_0x3A_State = 13;
  if (this->_internal_d10_0x3a_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(13, this->_internal_d10_0x3a_state(), target);
  }

  // uint32 e2_0x42_State = 14;
  if (this->_internal_e2_0x42_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(14, this->_internal_e2_0x42_state(), target);
  }

  // uint32 e4_0x44_State = 15;
  if (this->_internal_e4_0x44_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(15, this->_internal_e4_0x44_state(), target);
  }

  // uint32 f2_0x52_State = 16;
  if (this->_internal_f2_0x52_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(16, this->_internal_f2_0x52_state(), target);
  }

  // uint32 g2_0x62_State = 17;
  if (this->_internal_g2_0x62_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(17, this->_internal_g2_0x62_state(), target);
  }

  // uint32 g3_0x63_State = 18;
  if (this->_internal_g3_0x63_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(18, this->_internal_g3_0x63_state(), target);
  }

  // uint32 g5_0x65_State = 19;
  if (this->_internal_g5_0x65_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(19, this->_internal_g5_0x65_state(), target);
  }

  // uint32 h2_0x72_State = 20;
  if (this->_internal_h2_0x72_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(20, this->_internal_h2_0x72_state(), target);
  }

  // uint32 h3_0x73_State = 21;
  if (this->_internal_h3_0x73_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(21, this->_internal_h3_0x73_state(), target);
  }

  // uint32 h4_0x74_State = 22;
  if (this->_internal_h4_0x74_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(22, this->_internal_h4_0x74_state(), target);
  }

  // uint32 h7_0x77_State = 23;
  if (this->_internal_h7_0x77_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(23, this->_internal_h7_0x77_state(), target);
  }

  // uint32 h9_0x79_State = 24;
  if (this->_internal_h9_0x79_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(24, this->_internal_h9_0x79_state(), target);
  }

  // uint32 h11_0x82_State = 25;
  if (this->_internal_h11_0x82_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(25, this->_internal_h11_0x82_state(), target);
  }

  // uint32 h13_0x84_State = 26;
  if (this->_internal_h13_0x84_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(26, this->_internal_h13_0x84_state(), target);
  }

  // uint32 h14_0x85_State = 27;
  if (this->_internal_h14_0x85_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(27, this->_internal_h14_0x85_state(), target);
  }

  // uint32 h16_0x87_State = 28;
  if (this->_internal_h16_0x87_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(28, this->_internal_h16_0x87_state(), target);
  }

  // uint32 h18_0x89_State = 29;
  if (this->_internal_h18_0x89_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(29, this->_internal_h18_0x89_state(), target);
  }

  // uint32 h20_0x8B_State = 30;
  if (this->_internal_h20_0x8b_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(30, this->_internal_h20_0x8b_state(), target);
  }

  // uint32 i1_0x91_State = 31;
  if (this->_internal_i1_0x91_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(31, this->_internal_i1_0x91_state(), target);
  }

  // uint32 i4_0x94_State = 32;
  if (this->_internal_i4_0x94_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(32, this->_internal_i4_0x94_state(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:EVSInfo.EVMsgCtrl)
  return target;
}

size_t EVMsgCtrl::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:EVSInfo.EVMsgCtrl)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 x2_0x02_State = 1;
  if (this->_internal_x2_0x02_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_x2_0x02_state());
  }

  // uint32 x4_0x04_State = 2;
  if (this->_internal_x4_0x04_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_x4_0x04_state());
  }

  // uint32 x6_0x06_State = 3;
  if (this->_internal_x6_0x06_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_x6_0x06_state());
  }

  // uint32 x9_0x09_State = 4;
  if (this->_internal_x9_0x09_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_x9_0x09_state());
  }

  // uint32 b2_0x12_State = 5;
  if (this->_internal_b2_0x12_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_b2_0x12_state());
  }

  // uint32 c2_0x22_State = 6;
  if (this->_internal_c2_0x22_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_c2_0x22_state());
  }

  // uint32 c4_0x24_State = 7;
  if (this->_internal_c4_0x24_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_c4_0x24_state());
  }

  // uint32 d2_0x32_State = 8;
  if (this->_internal_d2_0x32_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_d2_0x32_state());
  }

  // uint32 d4_0x34_State = 9;
  if (this->_internal_d4_0x34_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_d4_0x34_state());
  }

  // uint32 d6_0x36_State = 10;
  if (this->_internal_d6_0x36_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_d6_0x36_state());
  }

  // uint32 d7_0x37_State = 11;
  if (this->_internal_d7_0x37_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_d7_0x37_state());
  }

  // uint32 d9_0x39_State = 12;
  if (this->_internal_d9_0x39_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_d9_0x39_state());
  }

  // uint32 d10_0x3A_State = 13;
  if (this->_internal_d10_0x3a_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_d10_0x3a_state());
  }

  // uint32 e2_0x42_State = 14;
  if (this->_internal_e2_0x42_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_e2_0x42_state());
  }

  // uint32 e4_0x44_State = 15;
  if (this->_internal_e4_0x44_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_e4_0x44_state());
  }

  // uint32 f2_0x52_State = 16;
  if (this->_internal_f2_0x52_state() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_f2_0x52_state());
  }

  // uint32 g2_0x62_State = 17;
  if (this->_internal_g2_0x62_state() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_g2_0x62_state());
  }

  // uint32 g3_0x63_State = 18;
  if (this->_internal_g3_0x63_state() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_g3_0x63_state());
  }

  // uint32 g5_0x65_State = 19;
  if (this->_internal_g5_0x65_state() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_g5_0x65_state());
  }

  // uint32 h2_0x72_State = 20;
  if (this->_internal_h2_0x72_state() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_h2_0x72_state());
  }

  // uint32 h3_0x73_State = 21;
  if (this->_internal_h3_0x73_state() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_h3_0x73_state());
  }

  // uint32 h4_0x74_State = 22;
  if (this->_internal_h4_0x74_state() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_h4_0x74_state());
  }

  // uint32 h7_0x77_State = 23;
  if (this->_internal_h7_0x77_state() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_h7_0x77_state());
  }

  // uint32 h9_0x79_State = 24;
  if (this->_internal_h9_0x79_state() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_h9_0x79_state());
  }

  // uint32 h11_0x82_State = 25;
  if (this->_internal_h11_0x82_state() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_h11_0x82_state());
  }

  // uint32 h13_0x84_State = 26;
  if (this->_internal_h13_0x84_state() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_h13_0x84_state());
  }

  // uint32 h14_0x85_State = 27;
  if (this->_internal_h14_0x85_state() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_h14_0x85_state());
  }

  // uint32 h16_0x87_State = 28;
  if (this->_internal_h16_0x87_state() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_h16_0x87_state());
  }

  // uint32 h18_0x89_State = 29;
  if (this->_internal_h18_0x89_state() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_h18_0x89_state());
  }

  // uint32 h20_0x8B_State = 30;
  if (this->_internal_h20_0x8b_state() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_h20_0x8b_state());
  }

  // uint32 i1_0x91_State = 31;
  if (this->_internal_i1_0x91_state() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_i1_0x91_state());
  }

  // uint32 i4_0x94_State = 32;
  if (this->_internal_i4_0x94_state() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_i4_0x94_state());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVMsgCtrl::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVMsgCtrl::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVMsgCtrl::GetClassData() const { return &_class_data_; }

void EVMsgCtrl::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVMsgCtrl *>(to)->MergeFrom(
      static_cast<const EVMsgCtrl &>(from));
}


void EVMsgCtrl::MergeFrom(const EVMsgCtrl& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:EVSInfo.EVMsgCtrl)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_x2_0x02_state() != 0) {
    _internal_set_x2_0x02_state(from._internal_x2_0x02_state());
  }
  if (from._internal_x4_0x04_state() != 0) {
    _internal_set_x4_0x04_state(from._internal_x4_0x04_state());
  }
  if (from._internal_x6_0x06_state() != 0) {
    _internal_set_x6_0x06_state(from._internal_x6_0x06_state());
  }
  if (from._internal_x9_0x09_state() != 0) {
    _internal_set_x9_0x09_state(from._internal_x9_0x09_state());
  }
  if (from._internal_b2_0x12_state() != 0) {
    _internal_set_b2_0x12_state(from._internal_b2_0x12_state());
  }
  if (from._internal_c2_0x22_state() != 0) {
    _internal_set_c2_0x22_state(from._internal_c2_0x22_state());
  }
  if (from._internal_c4_0x24_state() != 0) {
    _internal_set_c4_0x24_state(from._internal_c4_0x24_state());
  }
  if (from._internal_d2_0x32_state() != 0) {
    _internal_set_d2_0x32_state(from._internal_d2_0x32_state());
  }
  if (from._internal_d4_0x34_state() != 0) {
    _internal_set_d4_0x34_state(from._internal_d4_0x34_state());
  }
  if (from._internal_d6_0x36_state() != 0) {
    _internal_set_d6_0x36_state(from._internal_d6_0x36_state());
  }
  if (from._internal_d7_0x37_state() != 0) {
    _internal_set_d7_0x37_state(from._internal_d7_0x37_state());
  }
  if (from._internal_d9_0x39_state() != 0) {
    _internal_set_d9_0x39_state(from._internal_d9_0x39_state());
  }
  if (from._internal_d10_0x3a_state() != 0) {
    _internal_set_d10_0x3a_state(from._internal_d10_0x3a_state());
  }
  if (from._internal_e2_0x42_state() != 0) {
    _internal_set_e2_0x42_state(from._internal_e2_0x42_state());
  }
  if (from._internal_e4_0x44_state() != 0) {
    _internal_set_e4_0x44_state(from._internal_e4_0x44_state());
  }
  if (from._internal_f2_0x52_state() != 0) {
    _internal_set_f2_0x52_state(from._internal_f2_0x52_state());
  }
  if (from._internal_g2_0x62_state() != 0) {
    _internal_set_g2_0x62_state(from._internal_g2_0x62_state());
  }
  if (from._internal_g3_0x63_state() != 0) {
    _internal_set_g3_0x63_state(from._internal_g3_0x63_state());
  }
  if (from._internal_g5_0x65_state() != 0) {
    _internal_set_g5_0x65_state(from._internal_g5_0x65_state());
  }
  if (from._internal_h2_0x72_state() != 0) {
    _internal_set_h2_0x72_state(from._internal_h2_0x72_state());
  }
  if (from._internal_h3_0x73_state() != 0) {
    _internal_set_h3_0x73_state(from._internal_h3_0x73_state());
  }
  if (from._internal_h4_0x74_state() != 0) {
    _internal_set_h4_0x74_state(from._internal_h4_0x74_state());
  }
  if (from._internal_h7_0x77_state() != 0) {
    _internal_set_h7_0x77_state(from._internal_h7_0x77_state());
  }
  if (from._internal_h9_0x79_state() != 0) {
    _internal_set_h9_0x79_state(from._internal_h9_0x79_state());
  }
  if (from._internal_h11_0x82_state() != 0) {
    _internal_set_h11_0x82_state(from._internal_h11_0x82_state());
  }
  if (from._internal_h13_0x84_state() != 0) {
    _internal_set_h13_0x84_state(from._internal_h13_0x84_state());
  }
  if (from._internal_h14_0x85_state() != 0) {
    _internal_set_h14_0x85_state(from._internal_h14_0x85_state());
  }
  if (from._internal_h16_0x87_state() != 0) {
    _internal_set_h16_0x87_state(from._internal_h16_0x87_state());
  }
  if (from._internal_h18_0x89_state() != 0) {
    _internal_set_h18_0x89_state(from._internal_h18_0x89_state());
  }
  if (from._internal_h20_0x8b_state() != 0) {
    _internal_set_h20_0x8b_state(from._internal_h20_0x8b_state());
  }
  if (from._internal_i1_0x91_state() != 0) {
    _internal_set_i1_0x91_state(from._internal_i1_0x91_state());
  }
  if (from._internal_i4_0x94_state() != 0) {
    _internal_set_i4_0x94_state(from._internal_i4_0x94_state());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVMsgCtrl::CopyFrom(const EVMsgCtrl& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:EVSInfo.EVMsgCtrl)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVMsgCtrl::IsInitialized() const {
  return true;
}

void EVMsgCtrl::InternalSwap(EVMsgCtrl* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVMsgCtrl, i4_0x94_state_)
      + sizeof(EVMsgCtrl::i4_0x94_state_)
      - PROTOBUF_FIELD_OFFSET(EVMsgCtrl, x2_0x02_state_)>(
          reinterpret_cast<char*>(&x2_0x02_state_),
          reinterpret_cast<char*>(&other->x2_0x02_state_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVMsgCtrl::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fEVS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fINFO_2eproto[14]);
}

// ===================================================================

class IpMsg::_Internal {
 public:
};

IpMsg::IpMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:EVSInfo.IpMsg)
}
IpMsg::IpMsg(const IpMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  selfip_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_selfip().empty()) {
    selfip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_selfip(), 
      GetArenaForAllocation());
  }
  serviceip_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_serviceip().empty()) {
    serviceip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_serviceip(), 
      GetArenaForAllocation());
  }
  ::memcpy(&selfport_, &from.selfport_,
    static_cast<size_t>(reinterpret_cast<char*>(&serviceport_) -
    reinterpret_cast<char*>(&selfport_)) + sizeof(serviceport_));
  // @@protoc_insertion_point(copy_constructor:EVSInfo.IpMsg)
}

inline void IpMsg::SharedCtor() {
selfip_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
serviceip_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&selfport_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&serviceport_) -
    reinterpret_cast<char*>(&selfport_)) + sizeof(serviceport_));
}

IpMsg::~IpMsg() {
  // @@protoc_insertion_point(destructor:EVSInfo.IpMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void IpMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  selfip_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  serviceip_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void IpMsg::ArenaDtor(void* object) {
  IpMsg* _this = reinterpret_cast< IpMsg* >(object);
  (void)_this;
}
void IpMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void IpMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void IpMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:EVSInfo.IpMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  selfip_.ClearToEmpty();
  serviceip_.ClearToEmpty();
  ::memset(&selfport_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&serviceport_) -
      reinterpret_cast<char*>(&selfport_)) + sizeof(serviceport_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* IpMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bytes selfIP = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_selfip();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 selfPort = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          selfport_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes serviceIP = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_serviceip();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 servicePort = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          serviceport_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* IpMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:EVSInfo.IpMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes selfIP = 1;
  if (!this->_internal_selfip().empty()) {
    target = stream->WriteBytesMaybeAliased(
        1, this->_internal_selfip(), target);
  }

  // uint32 selfPort = 2;
  if (this->_internal_selfport() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_selfport(), target);
  }

  // bytes serviceIP = 3;
  if (!this->_internal_serviceip().empty()) {
    target = stream->WriteBytesMaybeAliased(
        3, this->_internal_serviceip(), target);
  }

  // uint32 servicePort = 4;
  if (this->_internal_serviceport() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_serviceport(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:EVSInfo.IpMsg)
  return target;
}

size_t IpMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:EVSInfo.IpMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes selfIP = 1;
  if (!this->_internal_selfip().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_selfip());
  }

  // bytes serviceIP = 3;
  if (!this->_internal_serviceip().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_serviceip());
  }

  // uint32 selfPort = 2;
  if (this->_internal_selfport() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_selfport());
  }

  // uint32 servicePort = 4;
  if (this->_internal_serviceport() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_serviceport());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData IpMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    IpMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*IpMsg::GetClassData() const { return &_class_data_; }

void IpMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<IpMsg *>(to)->MergeFrom(
      static_cast<const IpMsg &>(from));
}


void IpMsg::MergeFrom(const IpMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:EVSInfo.IpMsg)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_selfip().empty()) {
    _internal_set_selfip(from._internal_selfip());
  }
  if (!from._internal_serviceip().empty()) {
    _internal_set_serviceip(from._internal_serviceip());
  }
  if (from._internal_selfport() != 0) {
    _internal_set_selfport(from._internal_selfport());
  }
  if (from._internal_serviceport() != 0) {
    _internal_set_serviceport(from._internal_serviceport());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void IpMsg::CopyFrom(const IpMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:EVSInfo.IpMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool IpMsg::IsInitialized() const {
  return true;
}

void IpMsg::InternalSwap(IpMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &selfip_, GetArenaForAllocation(),
      &other->selfip_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &serviceip_, GetArenaForAllocation(),
      &other->serviceip_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(IpMsg, serviceport_)
      + sizeof(IpMsg::serviceport_)
      - PROTOBUF_FIELD_OFFSET(IpMsg, selfport_)>(
          reinterpret_cast<char*>(&selfport_),
          reinterpret_cast<char*>(&other->selfport_));
}

::PROTOBUF_NAMESPACE_ID::Metadata IpMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fINFO_2eproto_getter, &descriptor_table_GCU_5fEVS_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fINFO_2eproto[15]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace EVSInfo
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::EVSInfo::EVProtoConferMsg* Arena::CreateMaybeMessage< ::EVSInfo::EVProtoConferMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::EVSInfo::EVProtoConferMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::EVSInfo::EVFunConferMsg* Arena::CreateMaybeMessage< ::EVSInfo::EVFunConferMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::EVSInfo::EVFunConferMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::EVSInfo::EVChgParaConfigMsg* Arena::CreateMaybeMessage< ::EVSInfo::EVChgParaConfigMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::EVSInfo::EVChgParaConfigMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::EVSInfo::EVAuthenMsg* Arena::CreateMaybeMessage< ::EVSInfo::EVAuthenMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::EVSInfo::EVAuthenMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::EVSInfo::EVReserveMsg* Arena::CreateMaybeMessage< ::EVSInfo::EVReserveMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::EVSInfo::EVReserveMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::EVSInfo::EVPowerSupplyMsg* Arena::CreateMaybeMessage< ::EVSInfo::EVPowerSupplyMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::EVSInfo::EVPowerSupplyMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::EVSInfo::EVChargingMsg* Arena::CreateMaybeMessage< ::EVSInfo::EVChargingMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::EVSInfo::EVChargingMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::EVSInfo::EVChargingEndMsg* Arena::CreateMaybeMessage< ::EVSInfo::EVChargingEndMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::EVSInfo::EVChargingEndMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::EVSInfo::EVFunConferAckMsg* Arena::CreateMaybeMessage< ::EVSInfo::EVFunConferAckMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::EVSInfo::EVFunConferAckMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::EVSInfo::EVElectricCtrl* Arena::CreateMaybeMessage< ::EVSInfo::EVElectricCtrl >(Arena* arena) {
  return Arena::CreateMessageInternal< ::EVSInfo::EVElectricCtrl >(arena);
}
template<> PROTOBUF_NOINLINE ::EVSInfo::EVInsultCtrl* Arena::CreateMaybeMessage< ::EVSInfo::EVInsultCtrl >(Arena* arena) {
  return Arena::CreateMessageInternal< ::EVSInfo::EVInsultCtrl >(arena);
}
template<> PROTOBUF_NOINLINE ::EVSInfo::EVStateMsg* Arena::CreateMaybeMessage< ::EVSInfo::EVStateMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::EVSInfo::EVStateMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::EVSInfo::StepPara* Arena::CreateMaybeMessage< ::EVSInfo::StepPara >(Arena* arena) {
  return Arena::CreateMessageInternal< ::EVSInfo::StepPara >(arena);
}
template<> PROTOBUF_NOINLINE ::EVSInfo::EVSStepMsg* Arena::CreateMaybeMessage< ::EVSInfo::EVSStepMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::EVSInfo::EVSStepMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::EVSInfo::EVMsgCtrl* Arena::CreateMaybeMessage< ::EVSInfo::EVMsgCtrl >(Arena* arena) {
  return Arena::CreateMessageInternal< ::EVSInfo::EVMsgCtrl >(arena);
}
template<> PROTOBUF_NOINLINE ::EVSInfo::IpMsg* Arena::CreateMaybeMessage< ::EVSInfo::IpMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::EVSInfo::IpMsg >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
