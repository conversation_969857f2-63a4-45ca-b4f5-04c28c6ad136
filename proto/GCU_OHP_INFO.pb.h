// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GCU_OHP_INFO.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_GCU_5fOHP_5fINFO_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_GCU_5fOHP_5fINFO_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3017000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3017003 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "GCU_AllFaultEnum.pb.h"
#include "GCU_BMS_INFO.pb.h"
#include "GCU_OSC_INFO.pb.h"
#include "GCU_VCI_INFO.pb.h"
#include "GCU_DMC_INFO.pb.h"
#include "GCU_BMS2015P_INFO.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_GCU_5fOHP_5fINFO_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_GCU_5fOHP_5fINFO_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[12]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GCU_5fOHP_5fINFO_2eproto;
namespace OHPinfo {
class HistoryOrderList;
struct HistoryOrderListDefaultTypeInternal;
extern HistoryOrderListDefaultTypeInternal _HistoryOrderList_default_instance_;
class MeterInfo;
struct MeterInfoDefaultTypeInternal;
extern MeterInfoDefaultTypeInternal _MeterInfo_default_instance_;
class MeterState;
struct MeterStateDefaultTypeInternal;
extern MeterStateDefaultTypeInternal _MeterState_default_instance_;
class OrderBill;
struct OrderBillDefaultTypeInternal;
extern OrderBillDefaultTypeInternal _OrderBill_default_instance_;
class OrderPipelineAns;
struct OrderPipelineAnsDefaultTypeInternal;
extern OrderPipelineAnsDefaultTypeInternal _OrderPipelineAns_default_instance_;
class OrderPipelineInfo;
struct OrderPipelineInfoDefaultTypeInternal;
extern OrderPipelineInfoDefaultTypeInternal _OrderPipelineInfo_default_instance_;
class OrderPipelineState;
struct OrderPipelineStateDefaultTypeInternal;
extern OrderPipelineStateDefaultTypeInternal _OrderPipelineState_default_instance_;
class OrderRate;
struct OrderRateDefaultTypeInternal;
extern OrderRateDefaultTypeInternal _OrderRate_default_instance_;
class RuningOrderState;
struct RuningOrderStateDefaultTypeInternal;
extern RuningOrderStateDefaultTypeInternal _RuningOrderState_default_instance_;
class SettlementModuleState;
struct SettlementModuleStateDefaultTypeInternal;
extern SettlementModuleStateDefaultTypeInternal _SettlementModuleState_default_instance_;
class UUIDValue;
struct UUIDValueDefaultTypeInternal;
extern UUIDValueDefaultTypeInternal _UUIDValue_default_instance_;
class hmiConfigInfo;
struct hmiConfigInfoDefaultTypeInternal;
extern hmiConfigInfoDefaultTypeInternal _hmiConfigInfo_default_instance_;
}  // namespace OHPinfo
PROTOBUF_NAMESPACE_OPEN
template<> ::OHPinfo::HistoryOrderList* Arena::CreateMaybeMessage<::OHPinfo::HistoryOrderList>(Arena*);
template<> ::OHPinfo::MeterInfo* Arena::CreateMaybeMessage<::OHPinfo::MeterInfo>(Arena*);
template<> ::OHPinfo::MeterState* Arena::CreateMaybeMessage<::OHPinfo::MeterState>(Arena*);
template<> ::OHPinfo::OrderBill* Arena::CreateMaybeMessage<::OHPinfo::OrderBill>(Arena*);
template<> ::OHPinfo::OrderPipelineAns* Arena::CreateMaybeMessage<::OHPinfo::OrderPipelineAns>(Arena*);
template<> ::OHPinfo::OrderPipelineInfo* Arena::CreateMaybeMessage<::OHPinfo::OrderPipelineInfo>(Arena*);
template<> ::OHPinfo::OrderPipelineState* Arena::CreateMaybeMessage<::OHPinfo::OrderPipelineState>(Arena*);
template<> ::OHPinfo::OrderRate* Arena::CreateMaybeMessage<::OHPinfo::OrderRate>(Arena*);
template<> ::OHPinfo::RuningOrderState* Arena::CreateMaybeMessage<::OHPinfo::RuningOrderState>(Arena*);
template<> ::OHPinfo::SettlementModuleState* Arena::CreateMaybeMessage<::OHPinfo::SettlementModuleState>(Arena*);
template<> ::OHPinfo::UUIDValue* Arena::CreateMaybeMessage<::OHPinfo::UUIDValue>(Arena*);
template<> ::OHPinfo::hmiConfigInfo* Arena::CreateMaybeMessage<::OHPinfo::hmiConfigInfo>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace OHPinfo {

enum OrderTypeEnum : int {
  DefaultOrderType = 0,
  LocalFreeOrder = 1,
  LocalChargeOrder = 2,
  CloudPlatformOrder = 3,
  DevicePlatformOrder = 4,
  SpecialVehicleOrder = 5,
  VINPrimingOrder = 6,
  OrderTypeEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  OrderTypeEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool OrderTypeEnum_IsValid(int value);
constexpr OrderTypeEnum OrderTypeEnum_MIN = DefaultOrderType;
constexpr OrderTypeEnum OrderTypeEnum_MAX = VINPrimingOrder;
constexpr int OrderTypeEnum_ARRAYSIZE = OrderTypeEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OrderTypeEnum_descriptor();
template<typename T>
inline const std::string& OrderTypeEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, OrderTypeEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function OrderTypeEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    OrderTypeEnum_descriptor(), enum_t_value);
}
inline bool OrderTypeEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, OrderTypeEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<OrderTypeEnum>(
    OrderTypeEnum_descriptor(), name, value);
}
enum OrderSubTypeEnum : int {
  NormalOrderType = 0,
  VINSubTypeOrder = 1,
  UIPassWordOrder = 2,
  NFCCardOrder = 3,
  ETCRFIDOrder = 4,
  LicensePlateOrder = 5,
  VisionRecognitionOrder = 6,
  VoiceRecognitionOrder = 7,
  OrderSubTypeEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  OrderSubTypeEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool OrderSubTypeEnum_IsValid(int value);
constexpr OrderSubTypeEnum OrderSubTypeEnum_MIN = NormalOrderType;
constexpr OrderSubTypeEnum OrderSubTypeEnum_MAX = VoiceRecognitionOrder;
constexpr int OrderSubTypeEnum_ARRAYSIZE = OrderSubTypeEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OrderSubTypeEnum_descriptor();
template<typename T>
inline const std::string& OrderSubTypeEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, OrderSubTypeEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function OrderSubTypeEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    OrderSubTypeEnum_descriptor(), enum_t_value);
}
inline bool OrderSubTypeEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, OrderSubTypeEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<OrderSubTypeEnum>(
    OrderSubTypeEnum_descriptor(), name, value);
}
enum OrderStateEnum : int {
  DefaultOrderState = 0,
  AuthenticationOrder = 1,
  EstablishOrder = 2,
  RuningState = 3,
  SettlementState = 4,
  HangOrderState = 5,
  CompletedState = 6,
  OrderStateEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  OrderStateEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool OrderStateEnum_IsValid(int value);
constexpr OrderStateEnum OrderStateEnum_MIN = DefaultOrderState;
constexpr OrderStateEnum OrderStateEnum_MAX = CompletedState;
constexpr int OrderStateEnum_ARRAYSIZE = OrderStateEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OrderStateEnum_descriptor();
template<typename T>
inline const std::string& OrderStateEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, OrderStateEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function OrderStateEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    OrderStateEnum_descriptor(), enum_t_value);
}
inline bool OrderStateEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, OrderStateEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<OrderStateEnum>(
    OrderStateEnum_descriptor(), name, value);
}
enum PipelineStateEnum : int {
  DefaultPipelineState = 0,
  DisablePipeline = 1,
  IdleState = 2,
  InsertedState = 3,
  PrimingState = 4,
  StartingState = 5,
  ChargingState = 6,
  SettlementPipeline = 7,
  SubPlugMode = 8,
  PipelineStateEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  PipelineStateEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool PipelineStateEnum_IsValid(int value);
constexpr PipelineStateEnum PipelineStateEnum_MIN = DefaultPipelineState;
constexpr PipelineStateEnum PipelineStateEnum_MAX = SubPlugMode;
constexpr int PipelineStateEnum_ARRAYSIZE = PipelineStateEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PipelineStateEnum_descriptor();
template<typename T>
inline const std::string& PipelineStateEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, PipelineStateEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function PipelineStateEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    PipelineStateEnum_descriptor(), enum_t_value);
}
inline bool PipelineStateEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, PipelineStateEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<PipelineStateEnum>(
    PipelineStateEnum_descriptor(), name, value);
}
enum OrderStrategyEnum : int {
  DefaultStrategy = 0,
  LocalFullStrategy = 1,
  LocalDownStrategy = 2,
  SOCStrategy = 3,
  VStrategy = 4,
  EnergyStrategy = 5,
  BillStrategy = 6,
  OrderStrategyEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  OrderStrategyEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool OrderStrategyEnum_IsValid(int value);
constexpr OrderStrategyEnum OrderStrategyEnum_MIN = DefaultStrategy;
constexpr OrderStrategyEnum OrderStrategyEnum_MAX = BillStrategy;
constexpr int OrderStrategyEnum_ARRAYSIZE = OrderStrategyEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OrderStrategyEnum_descriptor();
template<typename T>
inline const std::string& OrderStrategyEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, OrderStrategyEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function OrderStrategyEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    OrderStrategyEnum_descriptor(), enum_t_value);
}
inline bool OrderStrategyEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, OrderStrategyEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<OrderStrategyEnum>(
    OrderStrategyEnum_descriptor(), name, value);
}
enum OrderJurisdictionEnum : int {
  DefaultAuthority = 0,
  LowAuthority = 1,
  NormalAuthority = 2,
  ProprietaryAuthority = 3,
  EmergencyAuthority = 4,
  LocalAuthority = 5,
  OrderJurisdictionEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  OrderJurisdictionEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool OrderJurisdictionEnum_IsValid(int value);
constexpr OrderJurisdictionEnum OrderJurisdictionEnum_MIN = DefaultAuthority;
constexpr OrderJurisdictionEnum OrderJurisdictionEnum_MAX = LocalAuthority;
constexpr int OrderJurisdictionEnum_ARRAYSIZE = OrderJurisdictionEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OrderJurisdictionEnum_descriptor();
template<typename T>
inline const std::string& OrderJurisdictionEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, OrderJurisdictionEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function OrderJurisdictionEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    OrderJurisdictionEnum_descriptor(), enum_t_value);
}
inline bool OrderJurisdictionEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, OrderJurisdictionEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<OrderJurisdictionEnum>(
    OrderJurisdictionEnum_descriptor(), name, value);
}
// ===================================================================

class UUIDValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:OHPinfo.UUIDValue) */ {
 public:
  inline UUIDValue() : UUIDValue(nullptr) {}
  ~UUIDValue() override;
  explicit constexpr UUIDValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UUIDValue(const UUIDValue& from);
  UUIDValue(UUIDValue&& from) noexcept
    : UUIDValue() {
    *this = ::std::move(from);
  }

  inline UUIDValue& operator=(const UUIDValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline UUIDValue& operator=(UUIDValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UUIDValue& default_instance() {
    return *internal_default_instance();
  }
  static inline const UUIDValue* internal_default_instance() {
    return reinterpret_cast<const UUIDValue*>(
               &_UUIDValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(UUIDValue& a, UUIDValue& b) {
    a.Swap(&b);
  }
  inline void Swap(UUIDValue* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UUIDValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline UUIDValue* New() const final {
    return new UUIDValue();
  }

  UUIDValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<UUIDValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UUIDValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const UUIDValue& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UUIDValue* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "OHPinfo.UUIDValue";
  }
  protected:
  explicit UUIDValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValue0FieldNumber = 1,
    kValue1FieldNumber = 2,
  };
  // uint64 value0 = 1;
  void clear_value0();
  ::PROTOBUF_NAMESPACE_ID::uint64 value0() const;
  void set_value0(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_value0() const;
  void _internal_set_value0(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // uint64 value1 = 2;
  void clear_value1();
  ::PROTOBUF_NAMESPACE_ID::uint64 value1() const;
  void set_value1(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_value1() const;
  void _internal_set_value1(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // @@protoc_insertion_point(class_scope:OHPinfo.UUIDValue)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint64 value0_;
  ::PROTOBUF_NAMESPACE_ID::uint64 value1_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fOHP_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class SettlementModuleState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:OHPinfo.SettlementModuleState) */ {
 public:
  inline SettlementModuleState() : SettlementModuleState(nullptr) {}
  ~SettlementModuleState() override;
  explicit constexpr SettlementModuleState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SettlementModuleState(const SettlementModuleState& from);
  SettlementModuleState(SettlementModuleState&& from) noexcept
    : SettlementModuleState() {
    *this = ::std::move(from);
  }

  inline SettlementModuleState& operator=(const SettlementModuleState& from) {
    CopyFrom(from);
    return *this;
  }
  inline SettlementModuleState& operator=(SettlementModuleState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SettlementModuleState& default_instance() {
    return *internal_default_instance();
  }
  static inline const SettlementModuleState* internal_default_instance() {
    return reinterpret_cast<const SettlementModuleState*>(
               &_SettlementModuleState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(SettlementModuleState& a, SettlementModuleState& b) {
    a.Swap(&b);
  }
  inline void Swap(SettlementModuleState* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SettlementModuleState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SettlementModuleState* New() const final {
    return new SettlementModuleState();
  }

  SettlementModuleState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SettlementModuleState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SettlementModuleState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SettlementModuleState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SettlementModuleState* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "OHPinfo.SettlementModuleState";
  }
  protected:
  explicit SettlementModuleState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kModuleIDFieldNumber = 1,
    kOffLineStrategyFieldNumber = 2,
    kNormalStrategyFieldNumber = 3,
    kRegisterStateFieldNumber = 4,
    kPeriodicCommunicationFieldNumber = 5,
    kJurisdictionFieldNumber = 6,
  };
  // .OSCinfo.SettlementModuleEnum ModuleID = 1;
  void clear_moduleid();
  ::OSCinfo::SettlementModuleEnum moduleid() const;
  void set_moduleid(::OSCinfo::SettlementModuleEnum value);
  private:
  ::OSCinfo::SettlementModuleEnum _internal_moduleid() const;
  void _internal_set_moduleid(::OSCinfo::SettlementModuleEnum value);
  public:

  // .OHPinfo.OrderStrategyEnum OffLineStrategy = 2;
  void clear_offlinestrategy();
  ::OHPinfo::OrderStrategyEnum offlinestrategy() const;
  void set_offlinestrategy(::OHPinfo::OrderStrategyEnum value);
  private:
  ::OHPinfo::OrderStrategyEnum _internal_offlinestrategy() const;
  void _internal_set_offlinestrategy(::OHPinfo::OrderStrategyEnum value);
  public:

  // .OHPinfo.OrderStrategyEnum NormalStrategy = 3;
  void clear_normalstrategy();
  ::OHPinfo::OrderStrategyEnum normalstrategy() const;
  void set_normalstrategy(::OHPinfo::OrderStrategyEnum value);
  private:
  ::OHPinfo::OrderStrategyEnum _internal_normalstrategy() const;
  void _internal_set_normalstrategy(::OHPinfo::OrderStrategyEnum value);
  public:

  // uint32 RegisterState = 4;
  void clear_registerstate();
  ::PROTOBUF_NAMESPACE_ID::uint32 registerstate() const;
  void set_registerstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_registerstate() const;
  void _internal_set_registerstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 PeriodicCommunication = 5;
  void clear_periodiccommunication();
  ::PROTOBUF_NAMESPACE_ID::uint32 periodiccommunication() const;
  void set_periodiccommunication(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_periodiccommunication() const;
  void _internal_set_periodiccommunication(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // .OHPinfo.OrderJurisdictionEnum Jurisdiction = 6;
  void clear_jurisdiction();
  ::OHPinfo::OrderJurisdictionEnum jurisdiction() const;
  void set_jurisdiction(::OHPinfo::OrderJurisdictionEnum value);
  private:
  ::OHPinfo::OrderJurisdictionEnum _internal_jurisdiction() const;
  void _internal_set_jurisdiction(::OHPinfo::OrderJurisdictionEnum value);
  public:

  // @@protoc_insertion_point(class_scope:OHPinfo.SettlementModuleState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int moduleid_;
  int offlinestrategy_;
  int normalstrategy_;
  ::PROTOBUF_NAMESPACE_ID::uint32 registerstate_;
  ::PROTOBUF_NAMESPACE_ID::uint32 periodiccommunication_;
  int jurisdiction_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fOHP_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class OrderRate final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:OHPinfo.OrderRate) */ {
 public:
  inline OrderRate() : OrderRate(nullptr) {}
  ~OrderRate() override;
  explicit constexpr OrderRate(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OrderRate(const OrderRate& from);
  OrderRate(OrderRate&& from) noexcept
    : OrderRate() {
    *this = ::std::move(from);
  }

  inline OrderRate& operator=(const OrderRate& from) {
    CopyFrom(from);
    return *this;
  }
  inline OrderRate& operator=(OrderRate&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OrderRate& default_instance() {
    return *internal_default_instance();
  }
  static inline const OrderRate* internal_default_instance() {
    return reinterpret_cast<const OrderRate*>(
               &_OrderRate_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(OrderRate& a, OrderRate& b) {
    a.Swap(&b);
  }
  inline void Swap(OrderRate* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OrderRate* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OrderRate* New() const final {
    return new OrderRate();
  }

  OrderRate* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OrderRate>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OrderRate& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const OrderRate& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OrderRate* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "OHPinfo.OrderRate";
  }
  protected:
  explicit OrderRate(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRuningRateFieldNumber = 3,
    kServiceRateFieldNumber = 4,
    kRateEnumFieldNumber = 5,
  };
  // float RuningRate = 3;
  void clear_runingrate();
  float runingrate() const;
  void set_runingrate(float value);
  private:
  float _internal_runingrate() const;
  void _internal_set_runingrate(float value);
  public:

  // float ServiceRate = 4;
  void clear_servicerate();
  float servicerate() const;
  void set_servicerate(float value);
  private:
  float _internal_servicerate() const;
  void _internal_set_servicerate(float value);
  public:

  // uint32 RateEnum = 5;
  void clear_rateenum();
  ::PROTOBUF_NAMESPACE_ID::uint32 rateenum() const;
  void set_rateenum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_rateenum() const;
  void _internal_set_rateenum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:OHPinfo.OrderRate)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float runingrate_;
  float servicerate_;
  ::PROTOBUF_NAMESPACE_ID::uint32 rateenum_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fOHP_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class OrderBill final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:OHPinfo.OrderBill) */ {
 public:
  inline OrderBill() : OrderBill(nullptr) {}
  ~OrderBill() override;
  explicit constexpr OrderBill(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OrderBill(const OrderBill& from);
  OrderBill(OrderBill&& from) noexcept
    : OrderBill() {
    *this = ::std::move(from);
  }

  inline OrderBill& operator=(const OrderBill& from) {
    CopyFrom(from);
    return *this;
  }
  inline OrderBill& operator=(OrderBill&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OrderBill& default_instance() {
    return *internal_default_instance();
  }
  static inline const OrderBill* internal_default_instance() {
    return reinterpret_cast<const OrderBill*>(
               &_OrderBill_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(OrderBill& a, OrderBill& b) {
    a.Swap(&b);
  }
  inline void Swap(OrderBill* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OrderBill* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OrderBill* New() const final {
    return new OrderBill();
  }

  OrderBill* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OrderBill>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OrderBill& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const OrderBill& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OrderBill* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "OHPinfo.OrderBill";
  }
  protected:
  explicit OrderBill(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRuningBillFieldNumber = 3,
  };
  // float RuningBill = 3;
  void clear_runingbill();
  float runingbill() const;
  void set_runingbill(float value);
  private:
  float _internal_runingbill() const;
  void _internal_set_runingbill(float value);
  public:

  // @@protoc_insertion_point(class_scope:OHPinfo.OrderBill)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float runingbill_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fOHP_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class RuningOrderState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:OHPinfo.RuningOrderState) */ {
 public:
  inline RuningOrderState() : RuningOrderState(nullptr) {}
  ~RuningOrderState() override;
  explicit constexpr RuningOrderState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RuningOrderState(const RuningOrderState& from);
  RuningOrderState(RuningOrderState&& from) noexcept
    : RuningOrderState() {
    *this = ::std::move(from);
  }

  inline RuningOrderState& operator=(const RuningOrderState& from) {
    CopyFrom(from);
    return *this;
  }
  inline RuningOrderState& operator=(RuningOrderState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RuningOrderState& default_instance() {
    return *internal_default_instance();
  }
  static inline const RuningOrderState* internal_default_instance() {
    return reinterpret_cast<const RuningOrderState*>(
               &_RuningOrderState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(RuningOrderState& a, RuningOrderState& b) {
    a.Swap(&b);
  }
  inline void Swap(RuningOrderState* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RuningOrderState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RuningOrderState* New() const final {
    return new RuningOrderState();
  }

  RuningOrderState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RuningOrderState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RuningOrderState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RuningOrderState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RuningOrderState* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "OHPinfo.RuningOrderState";
  }
  protected:
  explicit RuningOrderState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOrderUUIDFieldNumber = 2,
    kStartMeterReadOutFieldNumber = 3,
    kModuleIDFieldNumber = 1,
    kVolMeasuredFieldNumber = 5,
    kNowMeterReadOutFieldNumber = 4,
    kCurMeasuredFieldNumber = 6,
  };
  // .OHPinfo.UUIDValue OrderUUID = 2;
  bool has_orderuuid() const;
  private:
  bool _internal_has_orderuuid() const;
  public:
  void clear_orderuuid();
  const ::OHPinfo::UUIDValue& orderuuid() const;
  PROTOBUF_MUST_USE_RESULT ::OHPinfo::UUIDValue* release_orderuuid();
  ::OHPinfo::UUIDValue* mutable_orderuuid();
  void set_allocated_orderuuid(::OHPinfo::UUIDValue* orderuuid);
  private:
  const ::OHPinfo::UUIDValue& _internal_orderuuid() const;
  ::OHPinfo::UUIDValue* _internal_mutable_orderuuid();
  public:
  void unsafe_arena_set_allocated_orderuuid(
      ::OHPinfo::UUIDValue* orderuuid);
  ::OHPinfo::UUIDValue* unsafe_arena_release_orderuuid();

  // double StartMeterReadOut = 3;
  void clear_startmeterreadout();
  double startmeterreadout() const;
  void set_startmeterreadout(double value);
  private:
  double _internal_startmeterreadout() const;
  void _internal_set_startmeterreadout(double value);
  public:

  // .OSCinfo.SettlementModuleEnum ModuleID = 1;
  void clear_moduleid();
  ::OSCinfo::SettlementModuleEnum moduleid() const;
  void set_moduleid(::OSCinfo::SettlementModuleEnum value);
  private:
  ::OSCinfo::SettlementModuleEnum _internal_moduleid() const;
  void _internal_set_moduleid(::OSCinfo::SettlementModuleEnum value);
  public:

  // float volMeasured = 5;
  void clear_volmeasured();
  float volmeasured() const;
  void set_volmeasured(float value);
  private:
  float _internal_volmeasured() const;
  void _internal_set_volmeasured(float value);
  public:

  // double NowMeterReadOut = 4;
  void clear_nowmeterreadout();
  double nowmeterreadout() const;
  void set_nowmeterreadout(double value);
  private:
  double _internal_nowmeterreadout() const;
  void _internal_set_nowmeterreadout(double value);
  public:

  // float curMeasured = 6;
  void clear_curmeasured();
  float curmeasured() const;
  void set_curmeasured(float value);
  private:
  float _internal_curmeasured() const;
  void _internal_set_curmeasured(float value);
  public:

  // @@protoc_insertion_point(class_scope:OHPinfo.RuningOrderState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::OHPinfo::UUIDValue* orderuuid_;
  double startmeterreadout_;
  int moduleid_;
  float volmeasured_;
  double nowmeterreadout_;
  float curmeasured_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fOHP_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class MeterState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:OHPinfo.MeterState) */ {
 public:
  inline MeterState() : MeterState(nullptr) {}
  ~MeterState() override;
  explicit constexpr MeterState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MeterState(const MeterState& from);
  MeterState(MeterState&& from) noexcept
    : MeterState() {
    *this = ::std::move(from);
  }

  inline MeterState& operator=(const MeterState& from) {
    CopyFrom(from);
    return *this;
  }
  inline MeterState& operator=(MeterState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MeterState& default_instance() {
    return *internal_default_instance();
  }
  static inline const MeterState* internal_default_instance() {
    return reinterpret_cast<const MeterState*>(
               &_MeterState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(MeterState& a, MeterState& b) {
    a.Swap(&b);
  }
  inline void Swap(MeterState* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MeterState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MeterState* New() const final {
    return new MeterState();
  }

  MeterState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MeterState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MeterState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MeterState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MeterState* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "OHPinfo.MeterState";
  }
  protected:
  explicit MeterState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMeterWReadOutFieldNumber = 1,
    kMeterIReadOutFieldNumber = 2,
    kMeterVReadOutFieldNumber = 3,
    kMeterOffLineFieldNumber = 4,
    kMeterCheckFieldNumber = 5,
    kRefreshTimeFieldNumber = 6,
  };
  // double MeterWReadOut = 1;
  void clear_meterwreadout();
  double meterwreadout() const;
  void set_meterwreadout(double value);
  private:
  double _internal_meterwreadout() const;
  void _internal_set_meterwreadout(double value);
  public:

  // double MeterIReadOut = 2;
  void clear_meterireadout();
  double meterireadout() const;
  void set_meterireadout(double value);
  private:
  double _internal_meterireadout() const;
  void _internal_set_meterireadout(double value);
  public:

  // double MeterVReadOut = 3;
  void clear_metervreadout();
  double metervreadout() const;
  void set_metervreadout(double value);
  private:
  double _internal_metervreadout() const;
  void _internal_set_metervreadout(double value);
  public:

  // uint32 MeterOffLine = 4;
  void clear_meteroffline();
  ::PROTOBUF_NAMESPACE_ID::uint32 meteroffline() const;
  void set_meteroffline(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_meteroffline() const;
  void _internal_set_meteroffline(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 MeterCheck = 5;
  void clear_metercheck();
  ::PROTOBUF_NAMESPACE_ID::uint32 metercheck() const;
  void set_metercheck(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_metercheck() const;
  void _internal_set_metercheck(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint64 RefreshTime = 6;
  void clear_refreshtime();
  ::PROTOBUF_NAMESPACE_ID::uint64 refreshtime() const;
  void set_refreshtime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_refreshtime() const;
  void _internal_set_refreshtime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // @@protoc_insertion_point(class_scope:OHPinfo.MeterState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double meterwreadout_;
  double meterireadout_;
  double metervreadout_;
  ::PROTOBUF_NAMESPACE_ID::uint32 meteroffline_;
  ::PROTOBUF_NAMESPACE_ID::uint32 metercheck_;
  ::PROTOBUF_NAMESPACE_ID::uint64 refreshtime_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fOHP_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class MeterInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:OHPinfo.MeterInfo) */ {
 public:
  inline MeterInfo() : MeterInfo(nullptr) {}
  ~MeterInfo() override;
  explicit constexpr MeterInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MeterInfo(const MeterInfo& from);
  MeterInfo(MeterInfo&& from) noexcept
    : MeterInfo() {
    *this = ::std::move(from);
  }

  inline MeterInfo& operator=(const MeterInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline MeterInfo& operator=(MeterInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MeterInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const MeterInfo* internal_default_instance() {
    return reinterpret_cast<const MeterInfo*>(
               &_MeterInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(MeterInfo& a, MeterInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(MeterInfo* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MeterInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MeterInfo* New() const final {
    return new MeterInfo();
  }

  MeterInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MeterInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MeterInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MeterInfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MeterInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "OHPinfo.MeterInfo";
  }
  protected:
  explicit MeterInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMeterSNFieldNumber = 2,
    kMeterVerFieldNumber = 3,
    kMeterIDFieldNumber = 1,
  };
  // string MeterSN = 2;
  void clear_metersn();
  const std::string& metersn() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_metersn(ArgT0&& arg0, ArgT... args);
  std::string* mutable_metersn();
  PROTOBUF_MUST_USE_RESULT std::string* release_metersn();
  void set_allocated_metersn(std::string* metersn);
  private:
  const std::string& _internal_metersn() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_metersn(const std::string& value);
  std::string* _internal_mutable_metersn();
  public:

  // string MeterVer = 3;
  void clear_meterver();
  const std::string& meterver() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_meterver(ArgT0&& arg0, ArgT... args);
  std::string* mutable_meterver();
  PROTOBUF_MUST_USE_RESULT std::string* release_meterver();
  void set_allocated_meterver(std::string* meterver);
  private:
  const std::string& _internal_meterver() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_meterver(const std::string& value);
  std::string* _internal_mutable_meterver();
  public:

  // uint32 MeterID = 1;
  void clear_meterid();
  ::PROTOBUF_NAMESPACE_ID::uint32 meterid() const;
  void set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_meterid() const;
  void _internal_set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:OHPinfo.MeterInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr metersn_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr meterver_;
  ::PROTOBUF_NAMESPACE_ID::uint32 meterid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fOHP_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class HistoryOrderList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:OHPinfo.HistoryOrderList) */ {
 public:
  inline HistoryOrderList() : HistoryOrderList(nullptr) {}
  ~HistoryOrderList() override;
  explicit constexpr HistoryOrderList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HistoryOrderList(const HistoryOrderList& from);
  HistoryOrderList(HistoryOrderList&& from) noexcept
    : HistoryOrderList() {
    *this = ::std::move(from);
  }

  inline HistoryOrderList& operator=(const HistoryOrderList& from) {
    CopyFrom(from);
    return *this;
  }
  inline HistoryOrderList& operator=(HistoryOrderList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HistoryOrderList& default_instance() {
    return *internal_default_instance();
  }
  static inline const HistoryOrderList* internal_default_instance() {
    return reinterpret_cast<const HistoryOrderList*>(
               &_HistoryOrderList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(HistoryOrderList& a, HistoryOrderList& b) {
    a.Swap(&b);
  }
  inline void Swap(HistoryOrderList* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HistoryOrderList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HistoryOrderList* New() const final {
    return new HistoryOrderList();
  }

  HistoryOrderList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HistoryOrderList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HistoryOrderList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const HistoryOrderList& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HistoryOrderList* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "OHPinfo.HistoryOrderList";
  }
  protected:
  explicit HistoryOrderList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRuningRateListFieldNumber = 7,
    kRuningBillListFieldNumber = 9,
    kUserIDFieldNumber = 4,
    kOrderIDFieldNumber = 5,
    kStopReasonFieldNumber = 14,
    kOrderUUIDFieldNumber = 1,
    kOrderStateEnumFieldNumber = 2,
    kMeterIDFieldNumber = 3,
    kRuningRateListSizeFieldNumber = 6,
    kRuningBillListSizeFieldNumber = 8,
    kStarTimeFieldNumber = 10,
    kStopTimeFieldNumber = 11,
    kStartMeterReadOutFieldNumber = 12,
    kStopMeterReadOutFieldNumber = 13,
    kOSCReserve0FieldNumber = 15,
    kOSCReserve1FieldNumber = 16,
  };
  // repeated .OHPinfo.OrderRate RuningRateList = 7;
  int runingratelist_size() const;
  private:
  int _internal_runingratelist_size() const;
  public:
  void clear_runingratelist();
  ::OHPinfo::OrderRate* mutable_runingratelist(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::OHPinfo::OrderRate >*
      mutable_runingratelist();
  private:
  const ::OHPinfo::OrderRate& _internal_runingratelist(int index) const;
  ::OHPinfo::OrderRate* _internal_add_runingratelist();
  public:
  const ::OHPinfo::OrderRate& runingratelist(int index) const;
  ::OHPinfo::OrderRate* add_runingratelist();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::OHPinfo::OrderRate >&
      runingratelist() const;

  // repeated .OHPinfo.OrderBill RuningBillList = 9;
  int runingbilllist_size() const;
  private:
  int _internal_runingbilllist_size() const;
  public:
  void clear_runingbilllist();
  ::OHPinfo::OrderBill* mutable_runingbilllist(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::OHPinfo::OrderBill >*
      mutable_runingbilllist();
  private:
  const ::OHPinfo::OrderBill& _internal_runingbilllist(int index) const;
  ::OHPinfo::OrderBill* _internal_add_runingbilllist();
  public:
  const ::OHPinfo::OrderBill& runingbilllist(int index) const;
  ::OHPinfo::OrderBill* add_runingbilllist();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::OHPinfo::OrderBill >&
      runingbilllist() const;

  // string UserID = 4;
  void clear_userid();
  const std::string& userid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_userid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_userid();
  PROTOBUF_MUST_USE_RESULT std::string* release_userid();
  void set_allocated_userid(std::string* userid);
  private:
  const std::string& _internal_userid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_userid(const std::string& value);
  std::string* _internal_mutable_userid();
  public:

  // string OrderID = 5;
  void clear_orderid();
  const std::string& orderid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_orderid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_orderid();
  PROTOBUF_MUST_USE_RESULT std::string* release_orderid();
  void set_allocated_orderid(std::string* orderid);
  private:
  const std::string& _internal_orderid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_orderid(const std::string& value);
  std::string* _internal_mutable_orderid();
  public:

  // bytes StopReason = 14;
  void clear_stopreason();
  const std::string& stopreason() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_stopreason(ArgT0&& arg0, ArgT... args);
  std::string* mutable_stopreason();
  PROTOBUF_MUST_USE_RESULT std::string* release_stopreason();
  void set_allocated_stopreason(std::string* stopreason);
  private:
  const std::string& _internal_stopreason() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_stopreason(const std::string& value);
  std::string* _internal_mutable_stopreason();
  public:

  // .OHPinfo.UUIDValue OrderUUID = 1;
  bool has_orderuuid() const;
  private:
  bool _internal_has_orderuuid() const;
  public:
  void clear_orderuuid();
  const ::OHPinfo::UUIDValue& orderuuid() const;
  PROTOBUF_MUST_USE_RESULT ::OHPinfo::UUIDValue* release_orderuuid();
  ::OHPinfo::UUIDValue* mutable_orderuuid();
  void set_allocated_orderuuid(::OHPinfo::UUIDValue* orderuuid);
  private:
  const ::OHPinfo::UUIDValue& _internal_orderuuid() const;
  ::OHPinfo::UUIDValue* _internal_mutable_orderuuid();
  public:
  void unsafe_arena_set_allocated_orderuuid(
      ::OHPinfo::UUIDValue* orderuuid);
  ::OHPinfo::UUIDValue* unsafe_arena_release_orderuuid();

  // .OHPinfo.OrderStateEnum OrderStateEnum = 2;
  void clear_orderstateenum();
  ::OHPinfo::OrderStateEnum orderstateenum() const;
  void set_orderstateenum(::OHPinfo::OrderStateEnum value);
  private:
  ::OHPinfo::OrderStateEnum _internal_orderstateenum() const;
  void _internal_set_orderstateenum(::OHPinfo::OrderStateEnum value);
  public:

  // uint32 MeterID = 3;
  void clear_meterid();
  ::PROTOBUF_NAMESPACE_ID::uint32 meterid() const;
  void set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_meterid() const;
  void _internal_set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 RuningRateListSize = 6;
  void clear_runingratelistsize();
  ::PROTOBUF_NAMESPACE_ID::uint32 runingratelistsize() const;
  void set_runingratelistsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_runingratelistsize() const;
  void _internal_set_runingratelistsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 RuningBillListSize = 8;
  void clear_runingbilllistsize();
  ::PROTOBUF_NAMESPACE_ID::uint32 runingbilllistsize() const;
  void set_runingbilllistsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_runingbilllistsize() const;
  void _internal_set_runingbilllistsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 StarTime = 10;
  void clear_startime();
  ::PROTOBUF_NAMESPACE_ID::uint32 startime() const;
  void set_startime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_startime() const;
  void _internal_set_startime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 StopTime = 11;
  void clear_stoptime();
  ::PROTOBUF_NAMESPACE_ID::uint32 stoptime() const;
  void set_stoptime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_stoptime() const;
  void _internal_set_stoptime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // double StartMeterReadOut = 12;
  void clear_startmeterreadout();
  double startmeterreadout() const;
  void set_startmeterreadout(double value);
  private:
  double _internal_startmeterreadout() const;
  void _internal_set_startmeterreadout(double value);
  public:

  // double StopMeterReadOut = 13;
  void clear_stopmeterreadout();
  double stopmeterreadout() const;
  void set_stopmeterreadout(double value);
  private:
  double _internal_stopmeterreadout() const;
  void _internal_set_stopmeterreadout(double value);
  public:

  // uint32 OSCReserve0 = 15;
  void clear_oscreserve0();
  ::PROTOBUF_NAMESPACE_ID::uint32 oscreserve0() const;
  void set_oscreserve0(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_oscreserve0() const;
  void _internal_set_oscreserve0(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 OSCReserve1 = 16;
  void clear_oscreserve1();
  ::PROTOBUF_NAMESPACE_ID::uint32 oscreserve1() const;
  void set_oscreserve1(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_oscreserve1() const;
  void _internal_set_oscreserve1(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:OHPinfo.HistoryOrderList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::OHPinfo::OrderRate > runingratelist_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::OHPinfo::OrderBill > runingbilllist_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr userid_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr orderid_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr stopreason_;
  ::OHPinfo::UUIDValue* orderuuid_;
  int orderstateenum_;
  ::PROTOBUF_NAMESPACE_ID::uint32 meterid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 runingratelistsize_;
  ::PROTOBUF_NAMESPACE_ID::uint32 runingbilllistsize_;
  ::PROTOBUF_NAMESPACE_ID::uint32 startime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 stoptime_;
  double startmeterreadout_;
  double stopmeterreadout_;
  ::PROTOBUF_NAMESPACE_ID::uint32 oscreserve0_;
  ::PROTOBUF_NAMESPACE_ID::uint32 oscreserve1_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fOHP_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class OrderPipelineState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:OHPinfo.OrderPipelineState) */ {
 public:
  inline OrderPipelineState() : OrderPipelineState(nullptr) {}
  ~OrderPipelineState() override;
  explicit constexpr OrderPipelineState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OrderPipelineState(const OrderPipelineState& from);
  OrderPipelineState(OrderPipelineState&& from) noexcept
    : OrderPipelineState() {
    *this = ::std::move(from);
  }

  inline OrderPipelineState& operator=(const OrderPipelineState& from) {
    CopyFrom(from);
    return *this;
  }
  inline OrderPipelineState& operator=(OrderPipelineState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OrderPipelineState& default_instance() {
    return *internal_default_instance();
  }
  static inline const OrderPipelineState* internal_default_instance() {
    return reinterpret_cast<const OrderPipelineState*>(
               &_OrderPipelineState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(OrderPipelineState& a, OrderPipelineState& b) {
    a.Swap(&b);
  }
  inline void Swap(OrderPipelineState* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OrderPipelineState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OrderPipelineState* New() const final {
    return new OrderPipelineState();
  }

  OrderPipelineState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OrderPipelineState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OrderPipelineState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const OrderPipelineState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OrderPipelineState* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "OHPinfo.OrderPipelineState";
  }
  protected:
  explicit OrderPipelineState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMeterIDFieldNumber = 1,
    kModuleOpenFieldNumber = 2,
    kLinkStateFieldNumber = 3,
    kOnLineStateFieldNumber = 4,
    kLockStateFieldNumber = 5,
    kStartTypeFieldNumber = 6,
    kChargingDurationFieldNumber = 7,
    kPipelineStateFieldNumber = 8,
    kNowMeterReadOutFieldNumber = 10,
    kChargingPowerFieldNumber = 9,
    kPreemptFieldNumber = 11,
  };
  // uint32 MeterID = 1;
  void clear_meterid();
  ::PROTOBUF_NAMESPACE_ID::uint32 meterid() const;
  void set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_meterid() const;
  void _internal_set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 ModuleOpen = 2;
  void clear_moduleopen();
  ::PROTOBUF_NAMESPACE_ID::uint32 moduleopen() const;
  void set_moduleopen(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_moduleopen() const;
  void _internal_set_moduleopen(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 LinkState = 3;
  void clear_linkstate();
  ::PROTOBUF_NAMESPACE_ID::uint32 linkstate() const;
  void set_linkstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_linkstate() const;
  void _internal_set_linkstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 OnLineState = 4;
  void clear_onlinestate();
  ::PROTOBUF_NAMESPACE_ID::uint32 onlinestate() const;
  void set_onlinestate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_onlinestate() const;
  void _internal_set_onlinestate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 LockState = 5;
  void clear_lockstate();
  ::PROTOBUF_NAMESPACE_ID::uint32 lockstate() const;
  void set_lockstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_lockstate() const;
  void _internal_set_lockstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 StartType = 6;
  void clear_starttype();
  ::PROTOBUF_NAMESPACE_ID::uint32 starttype() const;
  void set_starttype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_starttype() const;
  void _internal_set_starttype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 ChargingDuration = 7;
  void clear_chargingduration();
  ::PROTOBUF_NAMESPACE_ID::uint32 chargingduration() const;
  void set_chargingduration(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chargingduration() const;
  void _internal_set_chargingduration(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // .OHPinfo.PipelineStateEnum PipelineState = 8;
  void clear_pipelinestate();
  ::OHPinfo::PipelineStateEnum pipelinestate() const;
  void set_pipelinestate(::OHPinfo::PipelineStateEnum value);
  private:
  ::OHPinfo::PipelineStateEnum _internal_pipelinestate() const;
  void _internal_set_pipelinestate(::OHPinfo::PipelineStateEnum value);
  public:

  // double NowMeterReadOut = 10;
  void clear_nowmeterreadout();
  double nowmeterreadout() const;
  void set_nowmeterreadout(double value);
  private:
  double _internal_nowmeterreadout() const;
  void _internal_set_nowmeterreadout(double value);
  public:

  // float ChargingPower = 9;
  void clear_chargingpower();
  float chargingpower() const;
  void set_chargingpower(float value);
  private:
  float _internal_chargingpower() const;
  void _internal_set_chargingpower(float value);
  public:

  // .OSCinfo.SettlementModuleEnum Preempt = 11;
  void clear_preempt();
  ::OSCinfo::SettlementModuleEnum preempt() const;
  void set_preempt(::OSCinfo::SettlementModuleEnum value);
  private:
  ::OSCinfo::SettlementModuleEnum _internal_preempt() const;
  void _internal_set_preempt(::OSCinfo::SettlementModuleEnum value);
  public:

  // @@protoc_insertion_point(class_scope:OHPinfo.OrderPipelineState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 meterid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 moduleopen_;
  ::PROTOBUF_NAMESPACE_ID::uint32 linkstate_;
  ::PROTOBUF_NAMESPACE_ID::uint32 onlinestate_;
  ::PROTOBUF_NAMESPACE_ID::uint32 lockstate_;
  ::PROTOBUF_NAMESPACE_ID::uint32 starttype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chargingduration_;
  int pipelinestate_;
  double nowmeterreadout_;
  float chargingpower_;
  int preempt_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fOHP_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class OrderPipelineAns final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:OHPinfo.OrderPipelineAns) */ {
 public:
  inline OrderPipelineAns() : OrderPipelineAns(nullptr) {}
  ~OrderPipelineAns() override;
  explicit constexpr OrderPipelineAns(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OrderPipelineAns(const OrderPipelineAns& from);
  OrderPipelineAns(OrderPipelineAns&& from) noexcept
    : OrderPipelineAns() {
    *this = ::std::move(from);
  }

  inline OrderPipelineAns& operator=(const OrderPipelineAns& from) {
    CopyFrom(from);
    return *this;
  }
  inline OrderPipelineAns& operator=(OrderPipelineAns&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OrderPipelineAns& default_instance() {
    return *internal_default_instance();
  }
  static inline const OrderPipelineAns* internal_default_instance() {
    return reinterpret_cast<const OrderPipelineAns*>(
               &_OrderPipelineAns_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(OrderPipelineAns& a, OrderPipelineAns& b) {
    a.Swap(&b);
  }
  inline void Swap(OrderPipelineAns* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OrderPipelineAns* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OrderPipelineAns* New() const final {
    return new OrderPipelineAns();
  }

  OrderPipelineAns* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OrderPipelineAns>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OrderPipelineAns& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const OrderPipelineAns& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OrderPipelineAns* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "OHPinfo.OrderPipelineAns";
  }
  protected:
  explicit OrderPipelineAns(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOHPFaultFieldNumber = 10,
    kMainAlarmListFieldNumber = 12,
    kVCIFaultFieldNumber = 14,
    kDMCFaultFieldNumber = 16,
    kADModulePListFieldNumber = 18,
    kMeterSNFieldNumber = 19,
    kMeterVerFieldNumber = 20,
    kBmsShakehandsMFieldNumber = 3,
    kBmsIdentifyMFieldNumber = 4,
    kBmsConfigMFieldNumber = 5,
    kBmsChargingMFieldNumber = 6,
    kBmsChargeFinishMFieldNumber = 7,
    kContactorStateFieldNumber = 8,
    kBms2015PMFieldNumber = 21,
    kMeterIDFieldNumber = 1,
    kBmsStateFieldNumber = 2,
    kOHPFaultSizeFieldNumber = 9,
    kPMMFaultSizeFieldNumber = 11,
    kVCIFaultSizeFieldNumber = 13,
    kDMCFaultSizeFieldNumber = 15,
    kADModuleParamSizeFieldNumber = 17,
  };
  // repeated .AllFaultEnum.OHPFaultState OHPFault = 10;
  int ohpfault_size() const;
  private:
  int _internal_ohpfault_size() const;
  public:
  void clear_ohpfault();
  ::AllFaultEnum::OHPFaultState* mutable_ohpfault(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::OHPFaultState >*
      mutable_ohpfault();
  private:
  const ::AllFaultEnum::OHPFaultState& _internal_ohpfault(int index) const;
  ::AllFaultEnum::OHPFaultState* _internal_add_ohpfault();
  public:
  const ::AllFaultEnum::OHPFaultState& ohpfault(int index) const;
  ::AllFaultEnum::OHPFaultState* add_ohpfault();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::OHPFaultState >&
      ohpfault() const;

  // repeated .AllFaultEnum.PMMFaultState MainAlarmList = 12;
  int mainalarmlist_size() const;
  private:
  int _internal_mainalarmlist_size() const;
  public:
  void clear_mainalarmlist();
  ::AllFaultEnum::PMMFaultState* mutable_mainalarmlist(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::PMMFaultState >*
      mutable_mainalarmlist();
  private:
  const ::AllFaultEnum::PMMFaultState& _internal_mainalarmlist(int index) const;
  ::AllFaultEnum::PMMFaultState* _internal_add_mainalarmlist();
  public:
  const ::AllFaultEnum::PMMFaultState& mainalarmlist(int index) const;
  ::AllFaultEnum::PMMFaultState* add_mainalarmlist();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::PMMFaultState >&
      mainalarmlist() const;

  // repeated .AllFaultEnum.VCIFaultState VCIFault = 14;
  int vcifault_size() const;
  private:
  int _internal_vcifault_size() const;
  public:
  void clear_vcifault();
  ::AllFaultEnum::VCIFaultState* mutable_vcifault(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::VCIFaultState >*
      mutable_vcifault();
  private:
  const ::AllFaultEnum::VCIFaultState& _internal_vcifault(int index) const;
  ::AllFaultEnum::VCIFaultState* _internal_add_vcifault();
  public:
  const ::AllFaultEnum::VCIFaultState& vcifault(int index) const;
  ::AllFaultEnum::VCIFaultState* add_vcifault();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::VCIFaultState >&
      vcifault() const;

  // repeated .AllFaultEnum.DMCFaultState DMCFault = 16;
  int dmcfault_size() const;
  private:
  int _internal_dmcfault_size() const;
  public:
  void clear_dmcfault();
  ::AllFaultEnum::DMCFaultState* mutable_dmcfault(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::DMCFaultState >*
      mutable_dmcfault();
  private:
  const ::AllFaultEnum::DMCFaultState& _internal_dmcfault(int index) const;
  ::AllFaultEnum::DMCFaultState* _internal_add_dmcfault();
  public:
  const ::AllFaultEnum::DMCFaultState& dmcfault(int index) const;
  ::AllFaultEnum::DMCFaultState* add_dmcfault();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::DMCFaultState >&
      dmcfault() const;

  // repeated .AllFaultEnum.ADModuleAlarm ADModulePList = 18;
  int admoduleplist_size() const;
  private:
  int _internal_admoduleplist_size() const;
  public:
  void clear_admoduleplist();
  ::AllFaultEnum::ADModuleAlarm* mutable_admoduleplist(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::ADModuleAlarm >*
      mutable_admoduleplist();
  private:
  const ::AllFaultEnum::ADModuleAlarm& _internal_admoduleplist(int index) const;
  ::AllFaultEnum::ADModuleAlarm* _internal_add_admoduleplist();
  public:
  const ::AllFaultEnum::ADModuleAlarm& admoduleplist(int index) const;
  ::AllFaultEnum::ADModuleAlarm* add_admoduleplist();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::ADModuleAlarm >&
      admoduleplist() const;

  // string MeterSN = 19;
  void clear_metersn();
  const std::string& metersn() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_metersn(ArgT0&& arg0, ArgT... args);
  std::string* mutable_metersn();
  PROTOBUF_MUST_USE_RESULT std::string* release_metersn();
  void set_allocated_metersn(std::string* metersn);
  private:
  const std::string& _internal_metersn() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_metersn(const std::string& value);
  std::string* _internal_mutable_metersn();
  public:

  // string MeterVer = 20;
  void clear_meterver();
  const std::string& meterver() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_meterver(ArgT0&& arg0, ArgT... args);
  std::string* mutable_meterver();
  PROTOBUF_MUST_USE_RESULT std::string* release_meterver();
  void set_allocated_meterver(std::string* meterver);
  private:
  const std::string& _internal_meterver() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_meterver(const std::string& value);
  std::string* _internal_mutable_meterver();
  public:

  // .BMSinfo.BMSHandShake BmsShakehandsM = 3;
  bool has_bmsshakehandsm() const;
  private:
  bool _internal_has_bmsshakehandsm() const;
  public:
  void clear_bmsshakehandsm();
  const ::BMSinfo::BMSHandShake& bmsshakehandsm() const;
  PROTOBUF_MUST_USE_RESULT ::BMSinfo::BMSHandShake* release_bmsshakehandsm();
  ::BMSinfo::BMSHandShake* mutable_bmsshakehandsm();
  void set_allocated_bmsshakehandsm(::BMSinfo::BMSHandShake* bmsshakehandsm);
  private:
  const ::BMSinfo::BMSHandShake& _internal_bmsshakehandsm() const;
  ::BMSinfo::BMSHandShake* _internal_mutable_bmsshakehandsm();
  public:
  void unsafe_arena_set_allocated_bmsshakehandsm(
      ::BMSinfo::BMSHandShake* bmsshakehandsm);
  ::BMSinfo::BMSHandShake* unsafe_arena_release_bmsshakehandsm();

  // .BMSinfo.BMSVerification BmsIdentifyM = 4;
  bool has_bmsidentifym() const;
  private:
  bool _internal_has_bmsidentifym() const;
  public:
  void clear_bmsidentifym();
  const ::BMSinfo::BMSVerification& bmsidentifym() const;
  PROTOBUF_MUST_USE_RESULT ::BMSinfo::BMSVerification* release_bmsidentifym();
  ::BMSinfo::BMSVerification* mutable_bmsidentifym();
  void set_allocated_bmsidentifym(::BMSinfo::BMSVerification* bmsidentifym);
  private:
  const ::BMSinfo::BMSVerification& _internal_bmsidentifym() const;
  ::BMSinfo::BMSVerification* _internal_mutable_bmsidentifym();
  public:
  void unsafe_arena_set_allocated_bmsidentifym(
      ::BMSinfo::BMSVerification* bmsidentifym);
  ::BMSinfo::BMSVerification* unsafe_arena_release_bmsidentifym();

  // .BMSinfo.BMSConfig BmsConfigM = 5;
  bool has_bmsconfigm() const;
  private:
  bool _internal_has_bmsconfigm() const;
  public:
  void clear_bmsconfigm();
  const ::BMSinfo::BMSConfig& bmsconfigm() const;
  PROTOBUF_MUST_USE_RESULT ::BMSinfo::BMSConfig* release_bmsconfigm();
  ::BMSinfo::BMSConfig* mutable_bmsconfigm();
  void set_allocated_bmsconfigm(::BMSinfo::BMSConfig* bmsconfigm);
  private:
  const ::BMSinfo::BMSConfig& _internal_bmsconfigm() const;
  ::BMSinfo::BMSConfig* _internal_mutable_bmsconfigm();
  public:
  void unsafe_arena_set_allocated_bmsconfigm(
      ::BMSinfo::BMSConfig* bmsconfigm);
  ::BMSinfo::BMSConfig* unsafe_arena_release_bmsconfigm();

  // .BMSinfo.BMSCharging BmsChargingM = 6;
  bool has_bmschargingm() const;
  private:
  bool _internal_has_bmschargingm() const;
  public:
  void clear_bmschargingm();
  const ::BMSinfo::BMSCharging& bmschargingm() const;
  PROTOBUF_MUST_USE_RESULT ::BMSinfo::BMSCharging* release_bmschargingm();
  ::BMSinfo::BMSCharging* mutable_bmschargingm();
  void set_allocated_bmschargingm(::BMSinfo::BMSCharging* bmschargingm);
  private:
  const ::BMSinfo::BMSCharging& _internal_bmschargingm() const;
  ::BMSinfo::BMSCharging* _internal_mutable_bmschargingm();
  public:
  void unsafe_arena_set_allocated_bmschargingm(
      ::BMSinfo::BMSCharging* bmschargingm);
  ::BMSinfo::BMSCharging* unsafe_arena_release_bmschargingm();

  // .BMSinfo.BMSChargingEnd BmsChargeFinishM = 7;
  bool has_bmschargefinishm() const;
  private:
  bool _internal_has_bmschargefinishm() const;
  public:
  void clear_bmschargefinishm();
  const ::BMSinfo::BMSChargingEnd& bmschargefinishm() const;
  PROTOBUF_MUST_USE_RESULT ::BMSinfo::BMSChargingEnd* release_bmschargefinishm();
  ::BMSinfo::BMSChargingEnd* mutable_bmschargefinishm();
  void set_allocated_bmschargefinishm(::BMSinfo::BMSChargingEnd* bmschargefinishm);
  private:
  const ::BMSinfo::BMSChargingEnd& _internal_bmschargefinishm() const;
  ::BMSinfo::BMSChargingEnd* _internal_mutable_bmschargefinishm();
  public:
  void unsafe_arena_set_allocated_bmschargefinishm(
      ::BMSinfo::BMSChargingEnd* bmschargefinishm);
  ::BMSinfo::BMSChargingEnd* unsafe_arena_release_bmschargefinishm();

  // .AllFaultEnum.MatrixStatus ContactorState = 8;
  bool has_contactorstate() const;
  private:
  bool _internal_has_contactorstate() const;
  public:
  void clear_contactorstate();
  const ::AllFaultEnum::MatrixStatus& contactorstate() const;
  PROTOBUF_MUST_USE_RESULT ::AllFaultEnum::MatrixStatus* release_contactorstate();
  ::AllFaultEnum::MatrixStatus* mutable_contactorstate();
  void set_allocated_contactorstate(::AllFaultEnum::MatrixStatus* contactorstate);
  private:
  const ::AllFaultEnum::MatrixStatus& _internal_contactorstate() const;
  ::AllFaultEnum::MatrixStatus* _internal_mutable_contactorstate();
  public:
  void unsafe_arena_set_allocated_contactorstate(
      ::AllFaultEnum::MatrixStatus* contactorstate);
  ::AllFaultEnum::MatrixStatus* unsafe_arena_release_contactorstate();

  // .BMS2015PlusInfo.bms2015pMsg bms2015pM = 21;
  bool has_bms2015pm() const;
  private:
  bool _internal_has_bms2015pm() const;
  public:
  void clear_bms2015pm();
  const ::BMS2015PlusInfo::bms2015pMsg& bms2015pm() const;
  PROTOBUF_MUST_USE_RESULT ::BMS2015PlusInfo::bms2015pMsg* release_bms2015pm();
  ::BMS2015PlusInfo::bms2015pMsg* mutable_bms2015pm();
  void set_allocated_bms2015pm(::BMS2015PlusInfo::bms2015pMsg* bms2015pm);
  private:
  const ::BMS2015PlusInfo::bms2015pMsg& _internal_bms2015pm() const;
  ::BMS2015PlusInfo::bms2015pMsg* _internal_mutable_bms2015pm();
  public:
  void unsafe_arena_set_allocated_bms2015pm(
      ::BMS2015PlusInfo::bms2015pMsg* bms2015pm);
  ::BMS2015PlusInfo::bms2015pMsg* unsafe_arena_release_bms2015pm();

  // uint32 MeterID = 1;
  void clear_meterid();
  ::PROTOBUF_NAMESPACE_ID::uint32 meterid() const;
  void set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_meterid() const;
  void _internal_set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // .BMSinfo.ChargeState bmsState = 2;
  void clear_bmsstate();
  ::BMSinfo::ChargeState bmsstate() const;
  void set_bmsstate(::BMSinfo::ChargeState value);
  private:
  ::BMSinfo::ChargeState _internal_bmsstate() const;
  void _internal_set_bmsstate(::BMSinfo::ChargeState value);
  public:

  // uint32 OHPFaultSize = 9;
  void clear_ohpfaultsize();
  ::PROTOBUF_NAMESPACE_ID::uint32 ohpfaultsize() const;
  void set_ohpfaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_ohpfaultsize() const;
  void _internal_set_ohpfaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 PMMFaultSize = 11;
  void clear_pmmfaultsize();
  ::PROTOBUF_NAMESPACE_ID::uint32 pmmfaultsize() const;
  void set_pmmfaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_pmmfaultsize() const;
  void _internal_set_pmmfaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 VCIFaultSize = 13;
  void clear_vcifaultsize();
  ::PROTOBUF_NAMESPACE_ID::uint32 vcifaultsize() const;
  void set_vcifaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_vcifaultsize() const;
  void _internal_set_vcifaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 DMCFaultSize = 15;
  void clear_dmcfaultsize();
  ::PROTOBUF_NAMESPACE_ID::uint32 dmcfaultsize() const;
  void set_dmcfaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_dmcfaultsize() const;
  void _internal_set_dmcfaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 ADModuleParamSize = 17;
  void clear_admoduleparamsize();
  ::PROTOBUF_NAMESPACE_ID::uint32 admoduleparamsize() const;
  void set_admoduleparamsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_admoduleparamsize() const;
  void _internal_set_admoduleparamsize(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:OHPinfo.OrderPipelineAns)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::OHPFaultState > ohpfault_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::PMMFaultState > mainalarmlist_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::VCIFaultState > vcifault_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::DMCFaultState > dmcfault_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::ADModuleAlarm > admoduleplist_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr metersn_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr meterver_;
  ::BMSinfo::BMSHandShake* bmsshakehandsm_;
  ::BMSinfo::BMSVerification* bmsidentifym_;
  ::BMSinfo::BMSConfig* bmsconfigm_;
  ::BMSinfo::BMSCharging* bmschargingm_;
  ::BMSinfo::BMSChargingEnd* bmschargefinishm_;
  ::AllFaultEnum::MatrixStatus* contactorstate_;
  ::BMS2015PlusInfo::bms2015pMsg* bms2015pm_;
  ::PROTOBUF_NAMESPACE_ID::uint32 meterid_;
  int bmsstate_;
  ::PROTOBUF_NAMESPACE_ID::uint32 ohpfaultsize_;
  ::PROTOBUF_NAMESPACE_ID::uint32 pmmfaultsize_;
  ::PROTOBUF_NAMESPACE_ID::uint32 vcifaultsize_;
  ::PROTOBUF_NAMESPACE_ID::uint32 dmcfaultsize_;
  ::PROTOBUF_NAMESPACE_ID::uint32 admoduleparamsize_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fOHP_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class OrderPipelineInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:OHPinfo.OrderPipelineInfo) */ {
 public:
  inline OrderPipelineInfo() : OrderPipelineInfo(nullptr) {}
  ~OrderPipelineInfo() override;
  explicit constexpr OrderPipelineInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OrderPipelineInfo(const OrderPipelineInfo& from);
  OrderPipelineInfo(OrderPipelineInfo&& from) noexcept
    : OrderPipelineInfo() {
    *this = ::std::move(from);
  }

  inline OrderPipelineInfo& operator=(const OrderPipelineInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline OrderPipelineInfo& operator=(OrderPipelineInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OrderPipelineInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const OrderPipelineInfo* internal_default_instance() {
    return reinterpret_cast<const OrderPipelineInfo*>(
               &_OrderPipelineInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(OrderPipelineInfo& a, OrderPipelineInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(OrderPipelineInfo* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OrderPipelineInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OrderPipelineInfo* New() const final {
    return new OrderPipelineInfo();
  }

  OrderPipelineInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OrderPipelineInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OrderPipelineInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const OrderPipelineInfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OrderPipelineInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "OHPinfo.OrderPipelineInfo";
  }
  protected:
  explicit OrderPipelineInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOrderIDFieldNumber = 1,
    kUserIDFieldNumber = 2,
    kStartSocFieldNumber = 3,
    kStartTimeFieldNumber = 4,
  };
  // string OrderID = 1;
  void clear_orderid();
  const std::string& orderid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_orderid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_orderid();
  PROTOBUF_MUST_USE_RESULT std::string* release_orderid();
  void set_allocated_orderid(std::string* orderid);
  private:
  const std::string& _internal_orderid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_orderid(const std::string& value);
  std::string* _internal_mutable_orderid();
  public:

  // string UserID = 2;
  void clear_userid();
  const std::string& userid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_userid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_userid();
  PROTOBUF_MUST_USE_RESULT std::string* release_userid();
  void set_allocated_userid(std::string* userid);
  private:
  const std::string& _internal_userid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_userid(const std::string& value);
  std::string* _internal_mutable_userid();
  public:

  // uint32 StartSoc = 3;
  void clear_startsoc();
  ::PROTOBUF_NAMESPACE_ID::uint32 startsoc() const;
  void set_startsoc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_startsoc() const;
  void _internal_set_startsoc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 StartTime = 4;
  void clear_starttime();
  ::PROTOBUF_NAMESPACE_ID::uint32 starttime() const;
  void set_starttime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_starttime() const;
  void _internal_set_starttime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:OHPinfo.OrderPipelineInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr orderid_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr userid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 startsoc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 starttime_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fOHP_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class hmiConfigInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:OHPinfo.hmiConfigInfo) */ {
 public:
  inline hmiConfigInfo() : hmiConfigInfo(nullptr) {}
  ~hmiConfigInfo() override;
  explicit constexpr hmiConfigInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  hmiConfigInfo(const hmiConfigInfo& from);
  hmiConfigInfo(hmiConfigInfo&& from) noexcept
    : hmiConfigInfo() {
    *this = ::std::move(from);
  }

  inline hmiConfigInfo& operator=(const hmiConfigInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline hmiConfigInfo& operator=(hmiConfigInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const hmiConfigInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const hmiConfigInfo* internal_default_instance() {
    return reinterpret_cast<const hmiConfigInfo*>(
               &_hmiConfigInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(hmiConfigInfo& a, hmiConfigInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(hmiConfigInfo* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(hmiConfigInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline hmiConfigInfo* New() const final {
    return new hmiConfigInfo();
  }

  hmiConfigInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<hmiConfigInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const hmiConfigInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const hmiConfigInfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(hmiConfigInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "OHPinfo.hmiConfigInfo";
  }
  protected:
  explicit hmiConfigInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGunsFieldNumber = 1,
    kGunCodeFieldNumber = 2,
    kGunTypeFieldNumber = 3,
    kVINTypeFieldNumber = 4,
    kAuxTypeFieldNumber = 5,
    kAdminModeFieldNumber = 6,
    kStopTypeFieldNumber = 7,
    kRateTypeFieldNumber = 8,
    kStandbylogoTypeFieldNumber = 9,
    kLedTypeFieldNumber = 10,
    kHistoryTypeFieldNumber = 11,
    kStopChgSocTypeFieldNumber = 12,
    kHmiConfigEnableFieldNumber = 13,
    kNetOfflineWifiEnableFieldNumber = 14,
    kVLPREnableFieldNumber = 15,
  };
  // uint32 guns = 1;
  void clear_guns();
  ::PROTOBUF_NAMESPACE_ID::uint32 guns() const;
  void set_guns(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_guns() const;
  void _internal_set_guns(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 gunCode = 2;
  void clear_guncode();
  ::PROTOBUF_NAMESPACE_ID::uint32 guncode() const;
  void set_guncode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_guncode() const;
  void _internal_set_guncode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // .DMCinfo.GunTypeEnum GunType = 3;
  void clear_guntype();
  ::DMCinfo::GunTypeEnum guntype() const;
  void set_guntype(::DMCinfo::GunTypeEnum value);
  private:
  ::DMCinfo::GunTypeEnum _internal_guntype() const;
  void _internal_set_guntype(::DMCinfo::GunTypeEnum value);
  public:

  // uint32 VINType = 4;
  void clear_vintype();
  ::PROTOBUF_NAMESPACE_ID::uint32 vintype() const;
  void set_vintype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_vintype() const;
  void _internal_set_vintype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 AuxType = 5;
  void clear_auxtype();
  ::PROTOBUF_NAMESPACE_ID::uint32 auxtype() const;
  void set_auxtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_auxtype() const;
  void _internal_set_auxtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 adminMode = 6;
  void clear_adminmode();
  ::PROTOBUF_NAMESPACE_ID::uint32 adminmode() const;
  void set_adminmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_adminmode() const;
  void _internal_set_adminmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 stopType = 7;
  void clear_stoptype();
  ::PROTOBUF_NAMESPACE_ID::uint32 stoptype() const;
  void set_stoptype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_stoptype() const;
  void _internal_set_stoptype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 RateType = 8;
  void clear_ratetype();
  ::PROTOBUF_NAMESPACE_ID::uint32 ratetype() const;
  void set_ratetype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_ratetype() const;
  void _internal_set_ratetype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 standbylogoType = 9;
  void clear_standbylogotype();
  ::PROTOBUF_NAMESPACE_ID::uint32 standbylogotype() const;
  void set_standbylogotype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_standbylogotype() const;
  void _internal_set_standbylogotype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 ledType = 10;
  void clear_ledtype();
  ::PROTOBUF_NAMESPACE_ID::uint32 ledtype() const;
  void set_ledtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_ledtype() const;
  void _internal_set_ledtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 historyType = 11;
  void clear_historytype();
  ::PROTOBUF_NAMESPACE_ID::uint32 historytype() const;
  void set_historytype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_historytype() const;
  void _internal_set_historytype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 stopChgSocType = 12;
  void clear_stopchgsoctype();
  ::PROTOBUF_NAMESPACE_ID::uint32 stopchgsoctype() const;
  void set_stopchgsoctype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_stopchgsoctype() const;
  void _internal_set_stopchgsoctype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 hmiConfigEnable = 13;
  void clear_hmiconfigenable();
  ::PROTOBUF_NAMESPACE_ID::uint32 hmiconfigenable() const;
  void set_hmiconfigenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_hmiconfigenable() const;
  void _internal_set_hmiconfigenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 netOfflineWifiEnable = 14;
  void clear_netofflinewifienable();
  ::PROTOBUF_NAMESPACE_ID::uint32 netofflinewifienable() const;
  void set_netofflinewifienable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_netofflinewifienable() const;
  void _internal_set_netofflinewifienable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 VLPREnable = 15;
  void clear_vlprenable();
  ::PROTOBUF_NAMESPACE_ID::uint32 vlprenable() const;
  void set_vlprenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_vlprenable() const;
  void _internal_set_vlprenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:OHPinfo.hmiConfigInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 guns_;
  ::PROTOBUF_NAMESPACE_ID::uint32 guncode_;
  int guntype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 vintype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 auxtype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 adminmode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 stoptype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 ratetype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 standbylogotype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 ledtype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 historytype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 stopchgsoctype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 hmiconfigenable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 netofflinewifienable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 vlprenable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fOHP_5fINFO_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// UUIDValue

// uint64 value0 = 1;
inline void UUIDValue::clear_value0() {
  value0_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 UUIDValue::_internal_value0() const {
  return value0_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 UUIDValue::value0() const {
  // @@protoc_insertion_point(field_get:OHPinfo.UUIDValue.value0)
  return _internal_value0();
}
inline void UUIDValue::_internal_set_value0(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  value0_ = value;
}
inline void UUIDValue::set_value0(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_value0(value);
  // @@protoc_insertion_point(field_set:OHPinfo.UUIDValue.value0)
}

// uint64 value1 = 2;
inline void UUIDValue::clear_value1() {
  value1_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 UUIDValue::_internal_value1() const {
  return value1_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 UUIDValue::value1() const {
  // @@protoc_insertion_point(field_get:OHPinfo.UUIDValue.value1)
  return _internal_value1();
}
inline void UUIDValue::_internal_set_value1(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  value1_ = value;
}
inline void UUIDValue::set_value1(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_value1(value);
  // @@protoc_insertion_point(field_set:OHPinfo.UUIDValue.value1)
}

// -------------------------------------------------------------------

// SettlementModuleState

// .OSCinfo.SettlementModuleEnum ModuleID = 1;
inline void SettlementModuleState::clear_moduleid() {
  moduleid_ = 0;
}
inline ::OSCinfo::SettlementModuleEnum SettlementModuleState::_internal_moduleid() const {
  return static_cast< ::OSCinfo::SettlementModuleEnum >(moduleid_);
}
inline ::OSCinfo::SettlementModuleEnum SettlementModuleState::moduleid() const {
  // @@protoc_insertion_point(field_get:OHPinfo.SettlementModuleState.ModuleID)
  return _internal_moduleid();
}
inline void SettlementModuleState::_internal_set_moduleid(::OSCinfo::SettlementModuleEnum value) {
  
  moduleid_ = value;
}
inline void SettlementModuleState::set_moduleid(::OSCinfo::SettlementModuleEnum value) {
  _internal_set_moduleid(value);
  // @@protoc_insertion_point(field_set:OHPinfo.SettlementModuleState.ModuleID)
}

// .OHPinfo.OrderStrategyEnum OffLineStrategy = 2;
inline void SettlementModuleState::clear_offlinestrategy() {
  offlinestrategy_ = 0;
}
inline ::OHPinfo::OrderStrategyEnum SettlementModuleState::_internal_offlinestrategy() const {
  return static_cast< ::OHPinfo::OrderStrategyEnum >(offlinestrategy_);
}
inline ::OHPinfo::OrderStrategyEnum SettlementModuleState::offlinestrategy() const {
  // @@protoc_insertion_point(field_get:OHPinfo.SettlementModuleState.OffLineStrategy)
  return _internal_offlinestrategy();
}
inline void SettlementModuleState::_internal_set_offlinestrategy(::OHPinfo::OrderStrategyEnum value) {
  
  offlinestrategy_ = value;
}
inline void SettlementModuleState::set_offlinestrategy(::OHPinfo::OrderStrategyEnum value) {
  _internal_set_offlinestrategy(value);
  // @@protoc_insertion_point(field_set:OHPinfo.SettlementModuleState.OffLineStrategy)
}

// .OHPinfo.OrderStrategyEnum NormalStrategy = 3;
inline void SettlementModuleState::clear_normalstrategy() {
  normalstrategy_ = 0;
}
inline ::OHPinfo::OrderStrategyEnum SettlementModuleState::_internal_normalstrategy() const {
  return static_cast< ::OHPinfo::OrderStrategyEnum >(normalstrategy_);
}
inline ::OHPinfo::OrderStrategyEnum SettlementModuleState::normalstrategy() const {
  // @@protoc_insertion_point(field_get:OHPinfo.SettlementModuleState.NormalStrategy)
  return _internal_normalstrategy();
}
inline void SettlementModuleState::_internal_set_normalstrategy(::OHPinfo::OrderStrategyEnum value) {
  
  normalstrategy_ = value;
}
inline void SettlementModuleState::set_normalstrategy(::OHPinfo::OrderStrategyEnum value) {
  _internal_set_normalstrategy(value);
  // @@protoc_insertion_point(field_set:OHPinfo.SettlementModuleState.NormalStrategy)
}

// uint32 RegisterState = 4;
inline void SettlementModuleState::clear_registerstate() {
  registerstate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SettlementModuleState::_internal_registerstate() const {
  return registerstate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SettlementModuleState::registerstate() const {
  // @@protoc_insertion_point(field_get:OHPinfo.SettlementModuleState.RegisterState)
  return _internal_registerstate();
}
inline void SettlementModuleState::_internal_set_registerstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  registerstate_ = value;
}
inline void SettlementModuleState::set_registerstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_registerstate(value);
  // @@protoc_insertion_point(field_set:OHPinfo.SettlementModuleState.RegisterState)
}

// uint32 PeriodicCommunication = 5;
inline void SettlementModuleState::clear_periodiccommunication() {
  periodiccommunication_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SettlementModuleState::_internal_periodiccommunication() const {
  return periodiccommunication_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SettlementModuleState::periodiccommunication() const {
  // @@protoc_insertion_point(field_get:OHPinfo.SettlementModuleState.PeriodicCommunication)
  return _internal_periodiccommunication();
}
inline void SettlementModuleState::_internal_set_periodiccommunication(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  periodiccommunication_ = value;
}
inline void SettlementModuleState::set_periodiccommunication(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_periodiccommunication(value);
  // @@protoc_insertion_point(field_set:OHPinfo.SettlementModuleState.PeriodicCommunication)
}

// .OHPinfo.OrderJurisdictionEnum Jurisdiction = 6;
inline void SettlementModuleState::clear_jurisdiction() {
  jurisdiction_ = 0;
}
inline ::OHPinfo::OrderJurisdictionEnum SettlementModuleState::_internal_jurisdiction() const {
  return static_cast< ::OHPinfo::OrderJurisdictionEnum >(jurisdiction_);
}
inline ::OHPinfo::OrderJurisdictionEnum SettlementModuleState::jurisdiction() const {
  // @@protoc_insertion_point(field_get:OHPinfo.SettlementModuleState.Jurisdiction)
  return _internal_jurisdiction();
}
inline void SettlementModuleState::_internal_set_jurisdiction(::OHPinfo::OrderJurisdictionEnum value) {
  
  jurisdiction_ = value;
}
inline void SettlementModuleState::set_jurisdiction(::OHPinfo::OrderJurisdictionEnum value) {
  _internal_set_jurisdiction(value);
  // @@protoc_insertion_point(field_set:OHPinfo.SettlementModuleState.Jurisdiction)
}

// -------------------------------------------------------------------

// OrderRate

// float RuningRate = 3;
inline void OrderRate::clear_runingrate() {
  runingrate_ = 0;
}
inline float OrderRate::_internal_runingrate() const {
  return runingrate_;
}
inline float OrderRate::runingrate() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderRate.RuningRate)
  return _internal_runingrate();
}
inline void OrderRate::_internal_set_runingrate(float value) {
  
  runingrate_ = value;
}
inline void OrderRate::set_runingrate(float value) {
  _internal_set_runingrate(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderRate.RuningRate)
}

// float ServiceRate = 4;
inline void OrderRate::clear_servicerate() {
  servicerate_ = 0;
}
inline float OrderRate::_internal_servicerate() const {
  return servicerate_;
}
inline float OrderRate::servicerate() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderRate.ServiceRate)
  return _internal_servicerate();
}
inline void OrderRate::_internal_set_servicerate(float value) {
  
  servicerate_ = value;
}
inline void OrderRate::set_servicerate(float value) {
  _internal_set_servicerate(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderRate.ServiceRate)
}

// uint32 RateEnum = 5;
inline void OrderRate::clear_rateenum() {
  rateenum_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderRate::_internal_rateenum() const {
  return rateenum_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderRate::rateenum() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderRate.RateEnum)
  return _internal_rateenum();
}
inline void OrderRate::_internal_set_rateenum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  rateenum_ = value;
}
inline void OrderRate::set_rateenum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_rateenum(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderRate.RateEnum)
}

// -------------------------------------------------------------------

// OrderBill

// float RuningBill = 3;
inline void OrderBill::clear_runingbill() {
  runingbill_ = 0;
}
inline float OrderBill::_internal_runingbill() const {
  return runingbill_;
}
inline float OrderBill::runingbill() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderBill.RuningBill)
  return _internal_runingbill();
}
inline void OrderBill::_internal_set_runingbill(float value) {
  
  runingbill_ = value;
}
inline void OrderBill::set_runingbill(float value) {
  _internal_set_runingbill(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderBill.RuningBill)
}

// -------------------------------------------------------------------

// RuningOrderState

// .OSCinfo.SettlementModuleEnum ModuleID = 1;
inline void RuningOrderState::clear_moduleid() {
  moduleid_ = 0;
}
inline ::OSCinfo::SettlementModuleEnum RuningOrderState::_internal_moduleid() const {
  return static_cast< ::OSCinfo::SettlementModuleEnum >(moduleid_);
}
inline ::OSCinfo::SettlementModuleEnum RuningOrderState::moduleid() const {
  // @@protoc_insertion_point(field_get:OHPinfo.RuningOrderState.ModuleID)
  return _internal_moduleid();
}
inline void RuningOrderState::_internal_set_moduleid(::OSCinfo::SettlementModuleEnum value) {
  
  moduleid_ = value;
}
inline void RuningOrderState::set_moduleid(::OSCinfo::SettlementModuleEnum value) {
  _internal_set_moduleid(value);
  // @@protoc_insertion_point(field_set:OHPinfo.RuningOrderState.ModuleID)
}

// .OHPinfo.UUIDValue OrderUUID = 2;
inline bool RuningOrderState::_internal_has_orderuuid() const {
  return this != internal_default_instance() && orderuuid_ != nullptr;
}
inline bool RuningOrderState::has_orderuuid() const {
  return _internal_has_orderuuid();
}
inline void RuningOrderState::clear_orderuuid() {
  if (GetArenaForAllocation() == nullptr && orderuuid_ != nullptr) {
    delete orderuuid_;
  }
  orderuuid_ = nullptr;
}
inline const ::OHPinfo::UUIDValue& RuningOrderState::_internal_orderuuid() const {
  const ::OHPinfo::UUIDValue* p = orderuuid_;
  return p != nullptr ? *p : reinterpret_cast<const ::OHPinfo::UUIDValue&>(
      ::OHPinfo::_UUIDValue_default_instance_);
}
inline const ::OHPinfo::UUIDValue& RuningOrderState::orderuuid() const {
  // @@protoc_insertion_point(field_get:OHPinfo.RuningOrderState.OrderUUID)
  return _internal_orderuuid();
}
inline void RuningOrderState::unsafe_arena_set_allocated_orderuuid(
    ::OHPinfo::UUIDValue* orderuuid) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(orderuuid_);
  }
  orderuuid_ = orderuuid;
  if (orderuuid) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:OHPinfo.RuningOrderState.OrderUUID)
}
inline ::OHPinfo::UUIDValue* RuningOrderState::release_orderuuid() {
  
  ::OHPinfo::UUIDValue* temp = orderuuid_;
  orderuuid_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::OHPinfo::UUIDValue* RuningOrderState::unsafe_arena_release_orderuuid() {
  // @@protoc_insertion_point(field_release:OHPinfo.RuningOrderState.OrderUUID)
  
  ::OHPinfo::UUIDValue* temp = orderuuid_;
  orderuuid_ = nullptr;
  return temp;
}
inline ::OHPinfo::UUIDValue* RuningOrderState::_internal_mutable_orderuuid() {
  
  if (orderuuid_ == nullptr) {
    auto* p = CreateMaybeMessage<::OHPinfo::UUIDValue>(GetArenaForAllocation());
    orderuuid_ = p;
  }
  return orderuuid_;
}
inline ::OHPinfo::UUIDValue* RuningOrderState::mutable_orderuuid() {
  ::OHPinfo::UUIDValue* _msg = _internal_mutable_orderuuid();
  // @@protoc_insertion_point(field_mutable:OHPinfo.RuningOrderState.OrderUUID)
  return _msg;
}
inline void RuningOrderState::set_allocated_orderuuid(::OHPinfo::UUIDValue* orderuuid) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete orderuuid_;
  }
  if (orderuuid) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::OHPinfo::UUIDValue>::GetOwningArena(orderuuid);
    if (message_arena != submessage_arena) {
      orderuuid = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, orderuuid, submessage_arena);
    }
    
  } else {
    
  }
  orderuuid_ = orderuuid;
  // @@protoc_insertion_point(field_set_allocated:OHPinfo.RuningOrderState.OrderUUID)
}

// double StartMeterReadOut = 3;
inline void RuningOrderState::clear_startmeterreadout() {
  startmeterreadout_ = 0;
}
inline double RuningOrderState::_internal_startmeterreadout() const {
  return startmeterreadout_;
}
inline double RuningOrderState::startmeterreadout() const {
  // @@protoc_insertion_point(field_get:OHPinfo.RuningOrderState.StartMeterReadOut)
  return _internal_startmeterreadout();
}
inline void RuningOrderState::_internal_set_startmeterreadout(double value) {
  
  startmeterreadout_ = value;
}
inline void RuningOrderState::set_startmeterreadout(double value) {
  _internal_set_startmeterreadout(value);
  // @@protoc_insertion_point(field_set:OHPinfo.RuningOrderState.StartMeterReadOut)
}

// double NowMeterReadOut = 4;
inline void RuningOrderState::clear_nowmeterreadout() {
  nowmeterreadout_ = 0;
}
inline double RuningOrderState::_internal_nowmeterreadout() const {
  return nowmeterreadout_;
}
inline double RuningOrderState::nowmeterreadout() const {
  // @@protoc_insertion_point(field_get:OHPinfo.RuningOrderState.NowMeterReadOut)
  return _internal_nowmeterreadout();
}
inline void RuningOrderState::_internal_set_nowmeterreadout(double value) {
  
  nowmeterreadout_ = value;
}
inline void RuningOrderState::set_nowmeterreadout(double value) {
  _internal_set_nowmeterreadout(value);
  // @@protoc_insertion_point(field_set:OHPinfo.RuningOrderState.NowMeterReadOut)
}

// float volMeasured = 5;
inline void RuningOrderState::clear_volmeasured() {
  volmeasured_ = 0;
}
inline float RuningOrderState::_internal_volmeasured() const {
  return volmeasured_;
}
inline float RuningOrderState::volmeasured() const {
  // @@protoc_insertion_point(field_get:OHPinfo.RuningOrderState.volMeasured)
  return _internal_volmeasured();
}
inline void RuningOrderState::_internal_set_volmeasured(float value) {
  
  volmeasured_ = value;
}
inline void RuningOrderState::set_volmeasured(float value) {
  _internal_set_volmeasured(value);
  // @@protoc_insertion_point(field_set:OHPinfo.RuningOrderState.volMeasured)
}

// float curMeasured = 6;
inline void RuningOrderState::clear_curmeasured() {
  curmeasured_ = 0;
}
inline float RuningOrderState::_internal_curmeasured() const {
  return curmeasured_;
}
inline float RuningOrderState::curmeasured() const {
  // @@protoc_insertion_point(field_get:OHPinfo.RuningOrderState.curMeasured)
  return _internal_curmeasured();
}
inline void RuningOrderState::_internal_set_curmeasured(float value) {
  
  curmeasured_ = value;
}
inline void RuningOrderState::set_curmeasured(float value) {
  _internal_set_curmeasured(value);
  // @@protoc_insertion_point(field_set:OHPinfo.RuningOrderState.curMeasured)
}

// -------------------------------------------------------------------

// MeterState

// double MeterWReadOut = 1;
inline void MeterState::clear_meterwreadout() {
  meterwreadout_ = 0;
}
inline double MeterState::_internal_meterwreadout() const {
  return meterwreadout_;
}
inline double MeterState::meterwreadout() const {
  // @@protoc_insertion_point(field_get:OHPinfo.MeterState.MeterWReadOut)
  return _internal_meterwreadout();
}
inline void MeterState::_internal_set_meterwreadout(double value) {
  
  meterwreadout_ = value;
}
inline void MeterState::set_meterwreadout(double value) {
  _internal_set_meterwreadout(value);
  // @@protoc_insertion_point(field_set:OHPinfo.MeterState.MeterWReadOut)
}

// double MeterIReadOut = 2;
inline void MeterState::clear_meterireadout() {
  meterireadout_ = 0;
}
inline double MeterState::_internal_meterireadout() const {
  return meterireadout_;
}
inline double MeterState::meterireadout() const {
  // @@protoc_insertion_point(field_get:OHPinfo.MeterState.MeterIReadOut)
  return _internal_meterireadout();
}
inline void MeterState::_internal_set_meterireadout(double value) {
  
  meterireadout_ = value;
}
inline void MeterState::set_meterireadout(double value) {
  _internal_set_meterireadout(value);
  // @@protoc_insertion_point(field_set:OHPinfo.MeterState.MeterIReadOut)
}

// double MeterVReadOut = 3;
inline void MeterState::clear_metervreadout() {
  metervreadout_ = 0;
}
inline double MeterState::_internal_metervreadout() const {
  return metervreadout_;
}
inline double MeterState::metervreadout() const {
  // @@protoc_insertion_point(field_get:OHPinfo.MeterState.MeterVReadOut)
  return _internal_metervreadout();
}
inline void MeterState::_internal_set_metervreadout(double value) {
  
  metervreadout_ = value;
}
inline void MeterState::set_metervreadout(double value) {
  _internal_set_metervreadout(value);
  // @@protoc_insertion_point(field_set:OHPinfo.MeterState.MeterVReadOut)
}

// uint32 MeterOffLine = 4;
inline void MeterState::clear_meteroffline() {
  meteroffline_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 MeterState::_internal_meteroffline() const {
  return meteroffline_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 MeterState::meteroffline() const {
  // @@protoc_insertion_point(field_get:OHPinfo.MeterState.MeterOffLine)
  return _internal_meteroffline();
}
inline void MeterState::_internal_set_meteroffline(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  meteroffline_ = value;
}
inline void MeterState::set_meteroffline(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_meteroffline(value);
  // @@protoc_insertion_point(field_set:OHPinfo.MeterState.MeterOffLine)
}

// uint32 MeterCheck = 5;
inline void MeterState::clear_metercheck() {
  metercheck_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 MeterState::_internal_metercheck() const {
  return metercheck_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 MeterState::metercheck() const {
  // @@protoc_insertion_point(field_get:OHPinfo.MeterState.MeterCheck)
  return _internal_metercheck();
}
inline void MeterState::_internal_set_metercheck(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  metercheck_ = value;
}
inline void MeterState::set_metercheck(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_metercheck(value);
  // @@protoc_insertion_point(field_set:OHPinfo.MeterState.MeterCheck)
}

// uint64 RefreshTime = 6;
inline void MeterState::clear_refreshtime() {
  refreshtime_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 MeterState::_internal_refreshtime() const {
  return refreshtime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 MeterState::refreshtime() const {
  // @@protoc_insertion_point(field_get:OHPinfo.MeterState.RefreshTime)
  return _internal_refreshtime();
}
inline void MeterState::_internal_set_refreshtime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  refreshtime_ = value;
}
inline void MeterState::set_refreshtime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_refreshtime(value);
  // @@protoc_insertion_point(field_set:OHPinfo.MeterState.RefreshTime)
}

// -------------------------------------------------------------------

// MeterInfo

// uint32 MeterID = 1;
inline void MeterInfo::clear_meterid() {
  meterid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 MeterInfo::_internal_meterid() const {
  return meterid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 MeterInfo::meterid() const {
  // @@protoc_insertion_point(field_get:OHPinfo.MeterInfo.MeterID)
  return _internal_meterid();
}
inline void MeterInfo::_internal_set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  meterid_ = value;
}
inline void MeterInfo::set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_meterid(value);
  // @@protoc_insertion_point(field_set:OHPinfo.MeterInfo.MeterID)
}

// string MeterSN = 2;
inline void MeterInfo::clear_metersn() {
  metersn_.ClearToEmpty();
}
inline const std::string& MeterInfo::metersn() const {
  // @@protoc_insertion_point(field_get:OHPinfo.MeterInfo.MeterSN)
  return _internal_metersn();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MeterInfo::set_metersn(ArgT0&& arg0, ArgT... args) {
 
 metersn_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:OHPinfo.MeterInfo.MeterSN)
}
inline std::string* MeterInfo::mutable_metersn() {
  std::string* _s = _internal_mutable_metersn();
  // @@protoc_insertion_point(field_mutable:OHPinfo.MeterInfo.MeterSN)
  return _s;
}
inline const std::string& MeterInfo::_internal_metersn() const {
  return metersn_.Get();
}
inline void MeterInfo::_internal_set_metersn(const std::string& value) {
  
  metersn_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* MeterInfo::_internal_mutable_metersn() {
  
  return metersn_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* MeterInfo::release_metersn() {
  // @@protoc_insertion_point(field_release:OHPinfo.MeterInfo.MeterSN)
  return metersn_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void MeterInfo::set_allocated_metersn(std::string* metersn) {
  if (metersn != nullptr) {
    
  } else {
    
  }
  metersn_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), metersn,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:OHPinfo.MeterInfo.MeterSN)
}

// string MeterVer = 3;
inline void MeterInfo::clear_meterver() {
  meterver_.ClearToEmpty();
}
inline const std::string& MeterInfo::meterver() const {
  // @@protoc_insertion_point(field_get:OHPinfo.MeterInfo.MeterVer)
  return _internal_meterver();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MeterInfo::set_meterver(ArgT0&& arg0, ArgT... args) {
 
 meterver_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:OHPinfo.MeterInfo.MeterVer)
}
inline std::string* MeterInfo::mutable_meterver() {
  std::string* _s = _internal_mutable_meterver();
  // @@protoc_insertion_point(field_mutable:OHPinfo.MeterInfo.MeterVer)
  return _s;
}
inline const std::string& MeterInfo::_internal_meterver() const {
  return meterver_.Get();
}
inline void MeterInfo::_internal_set_meterver(const std::string& value) {
  
  meterver_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* MeterInfo::_internal_mutable_meterver() {
  
  return meterver_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* MeterInfo::release_meterver() {
  // @@protoc_insertion_point(field_release:OHPinfo.MeterInfo.MeterVer)
  return meterver_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void MeterInfo::set_allocated_meterver(std::string* meterver) {
  if (meterver != nullptr) {
    
  } else {
    
  }
  meterver_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), meterver,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:OHPinfo.MeterInfo.MeterVer)
}

// -------------------------------------------------------------------

// HistoryOrderList

// .OHPinfo.UUIDValue OrderUUID = 1;
inline bool HistoryOrderList::_internal_has_orderuuid() const {
  return this != internal_default_instance() && orderuuid_ != nullptr;
}
inline bool HistoryOrderList::has_orderuuid() const {
  return _internal_has_orderuuid();
}
inline void HistoryOrderList::clear_orderuuid() {
  if (GetArenaForAllocation() == nullptr && orderuuid_ != nullptr) {
    delete orderuuid_;
  }
  orderuuid_ = nullptr;
}
inline const ::OHPinfo::UUIDValue& HistoryOrderList::_internal_orderuuid() const {
  const ::OHPinfo::UUIDValue* p = orderuuid_;
  return p != nullptr ? *p : reinterpret_cast<const ::OHPinfo::UUIDValue&>(
      ::OHPinfo::_UUIDValue_default_instance_);
}
inline const ::OHPinfo::UUIDValue& HistoryOrderList::orderuuid() const {
  // @@protoc_insertion_point(field_get:OHPinfo.HistoryOrderList.OrderUUID)
  return _internal_orderuuid();
}
inline void HistoryOrderList::unsafe_arena_set_allocated_orderuuid(
    ::OHPinfo::UUIDValue* orderuuid) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(orderuuid_);
  }
  orderuuid_ = orderuuid;
  if (orderuuid) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:OHPinfo.HistoryOrderList.OrderUUID)
}
inline ::OHPinfo::UUIDValue* HistoryOrderList::release_orderuuid() {
  
  ::OHPinfo::UUIDValue* temp = orderuuid_;
  orderuuid_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::OHPinfo::UUIDValue* HistoryOrderList::unsafe_arena_release_orderuuid() {
  // @@protoc_insertion_point(field_release:OHPinfo.HistoryOrderList.OrderUUID)
  
  ::OHPinfo::UUIDValue* temp = orderuuid_;
  orderuuid_ = nullptr;
  return temp;
}
inline ::OHPinfo::UUIDValue* HistoryOrderList::_internal_mutable_orderuuid() {
  
  if (orderuuid_ == nullptr) {
    auto* p = CreateMaybeMessage<::OHPinfo::UUIDValue>(GetArenaForAllocation());
    orderuuid_ = p;
  }
  return orderuuid_;
}
inline ::OHPinfo::UUIDValue* HistoryOrderList::mutable_orderuuid() {
  ::OHPinfo::UUIDValue* _msg = _internal_mutable_orderuuid();
  // @@protoc_insertion_point(field_mutable:OHPinfo.HistoryOrderList.OrderUUID)
  return _msg;
}
inline void HistoryOrderList::set_allocated_orderuuid(::OHPinfo::UUIDValue* orderuuid) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete orderuuid_;
  }
  if (orderuuid) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::OHPinfo::UUIDValue>::GetOwningArena(orderuuid);
    if (message_arena != submessage_arena) {
      orderuuid = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, orderuuid, submessage_arena);
    }
    
  } else {
    
  }
  orderuuid_ = orderuuid;
  // @@protoc_insertion_point(field_set_allocated:OHPinfo.HistoryOrderList.OrderUUID)
}

// .OHPinfo.OrderStateEnum OrderStateEnum = 2;
inline void HistoryOrderList::clear_orderstateenum() {
  orderstateenum_ = 0;
}
inline ::OHPinfo::OrderStateEnum HistoryOrderList::_internal_orderstateenum() const {
  return static_cast< ::OHPinfo::OrderStateEnum >(orderstateenum_);
}
inline ::OHPinfo::OrderStateEnum HistoryOrderList::orderstateenum() const {
  // @@protoc_insertion_point(field_get:OHPinfo.HistoryOrderList.OrderStateEnum)
  return _internal_orderstateenum();
}
inline void HistoryOrderList::_internal_set_orderstateenum(::OHPinfo::OrderStateEnum value) {
  
  orderstateenum_ = value;
}
inline void HistoryOrderList::set_orderstateenum(::OHPinfo::OrderStateEnum value) {
  _internal_set_orderstateenum(value);
  // @@protoc_insertion_point(field_set:OHPinfo.HistoryOrderList.OrderStateEnum)
}

// uint32 MeterID = 3;
inline void HistoryOrderList::clear_meterid() {
  meterid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HistoryOrderList::_internal_meterid() const {
  return meterid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HistoryOrderList::meterid() const {
  // @@protoc_insertion_point(field_get:OHPinfo.HistoryOrderList.MeterID)
  return _internal_meterid();
}
inline void HistoryOrderList::_internal_set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  meterid_ = value;
}
inline void HistoryOrderList::set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_meterid(value);
  // @@protoc_insertion_point(field_set:OHPinfo.HistoryOrderList.MeterID)
}

// string UserID = 4;
inline void HistoryOrderList::clear_userid() {
  userid_.ClearToEmpty();
}
inline const std::string& HistoryOrderList::userid() const {
  // @@protoc_insertion_point(field_get:OHPinfo.HistoryOrderList.UserID)
  return _internal_userid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HistoryOrderList::set_userid(ArgT0&& arg0, ArgT... args) {
 
 userid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:OHPinfo.HistoryOrderList.UserID)
}
inline std::string* HistoryOrderList::mutable_userid() {
  std::string* _s = _internal_mutable_userid();
  // @@protoc_insertion_point(field_mutable:OHPinfo.HistoryOrderList.UserID)
  return _s;
}
inline const std::string& HistoryOrderList::_internal_userid() const {
  return userid_.Get();
}
inline void HistoryOrderList::_internal_set_userid(const std::string& value) {
  
  userid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* HistoryOrderList::_internal_mutable_userid() {
  
  return userid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* HistoryOrderList::release_userid() {
  // @@protoc_insertion_point(field_release:OHPinfo.HistoryOrderList.UserID)
  return userid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void HistoryOrderList::set_allocated_userid(std::string* userid) {
  if (userid != nullptr) {
    
  } else {
    
  }
  userid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), userid,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:OHPinfo.HistoryOrderList.UserID)
}

// string OrderID = 5;
inline void HistoryOrderList::clear_orderid() {
  orderid_.ClearToEmpty();
}
inline const std::string& HistoryOrderList::orderid() const {
  // @@protoc_insertion_point(field_get:OHPinfo.HistoryOrderList.OrderID)
  return _internal_orderid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HistoryOrderList::set_orderid(ArgT0&& arg0, ArgT... args) {
 
 orderid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:OHPinfo.HistoryOrderList.OrderID)
}
inline std::string* HistoryOrderList::mutable_orderid() {
  std::string* _s = _internal_mutable_orderid();
  // @@protoc_insertion_point(field_mutable:OHPinfo.HistoryOrderList.OrderID)
  return _s;
}
inline const std::string& HistoryOrderList::_internal_orderid() const {
  return orderid_.Get();
}
inline void HistoryOrderList::_internal_set_orderid(const std::string& value) {
  
  orderid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* HistoryOrderList::_internal_mutable_orderid() {
  
  return orderid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* HistoryOrderList::release_orderid() {
  // @@protoc_insertion_point(field_release:OHPinfo.HistoryOrderList.OrderID)
  return orderid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void HistoryOrderList::set_allocated_orderid(std::string* orderid) {
  if (orderid != nullptr) {
    
  } else {
    
  }
  orderid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), orderid,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:OHPinfo.HistoryOrderList.OrderID)
}

// uint32 RuningRateListSize = 6;
inline void HistoryOrderList::clear_runingratelistsize() {
  runingratelistsize_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HistoryOrderList::_internal_runingratelistsize() const {
  return runingratelistsize_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HistoryOrderList::runingratelistsize() const {
  // @@protoc_insertion_point(field_get:OHPinfo.HistoryOrderList.RuningRateListSize)
  return _internal_runingratelistsize();
}
inline void HistoryOrderList::_internal_set_runingratelistsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  runingratelistsize_ = value;
}
inline void HistoryOrderList::set_runingratelistsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_runingratelistsize(value);
  // @@protoc_insertion_point(field_set:OHPinfo.HistoryOrderList.RuningRateListSize)
}

// repeated .OHPinfo.OrderRate RuningRateList = 7;
inline int HistoryOrderList::_internal_runingratelist_size() const {
  return runingratelist_.size();
}
inline int HistoryOrderList::runingratelist_size() const {
  return _internal_runingratelist_size();
}
inline void HistoryOrderList::clear_runingratelist() {
  runingratelist_.Clear();
}
inline ::OHPinfo::OrderRate* HistoryOrderList::mutable_runingratelist(int index) {
  // @@protoc_insertion_point(field_mutable:OHPinfo.HistoryOrderList.RuningRateList)
  return runingratelist_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::OHPinfo::OrderRate >*
HistoryOrderList::mutable_runingratelist() {
  // @@protoc_insertion_point(field_mutable_list:OHPinfo.HistoryOrderList.RuningRateList)
  return &runingratelist_;
}
inline const ::OHPinfo::OrderRate& HistoryOrderList::_internal_runingratelist(int index) const {
  return runingratelist_.Get(index);
}
inline const ::OHPinfo::OrderRate& HistoryOrderList::runingratelist(int index) const {
  // @@protoc_insertion_point(field_get:OHPinfo.HistoryOrderList.RuningRateList)
  return _internal_runingratelist(index);
}
inline ::OHPinfo::OrderRate* HistoryOrderList::_internal_add_runingratelist() {
  return runingratelist_.Add();
}
inline ::OHPinfo::OrderRate* HistoryOrderList::add_runingratelist() {
  ::OHPinfo::OrderRate* _add = _internal_add_runingratelist();
  // @@protoc_insertion_point(field_add:OHPinfo.HistoryOrderList.RuningRateList)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::OHPinfo::OrderRate >&
HistoryOrderList::runingratelist() const {
  // @@protoc_insertion_point(field_list:OHPinfo.HistoryOrderList.RuningRateList)
  return runingratelist_;
}

// uint32 RuningBillListSize = 8;
inline void HistoryOrderList::clear_runingbilllistsize() {
  runingbilllistsize_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HistoryOrderList::_internal_runingbilllistsize() const {
  return runingbilllistsize_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HistoryOrderList::runingbilllistsize() const {
  // @@protoc_insertion_point(field_get:OHPinfo.HistoryOrderList.RuningBillListSize)
  return _internal_runingbilllistsize();
}
inline void HistoryOrderList::_internal_set_runingbilllistsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  runingbilllistsize_ = value;
}
inline void HistoryOrderList::set_runingbilllistsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_runingbilllistsize(value);
  // @@protoc_insertion_point(field_set:OHPinfo.HistoryOrderList.RuningBillListSize)
}

// repeated .OHPinfo.OrderBill RuningBillList = 9;
inline int HistoryOrderList::_internal_runingbilllist_size() const {
  return runingbilllist_.size();
}
inline int HistoryOrderList::runingbilllist_size() const {
  return _internal_runingbilllist_size();
}
inline void HistoryOrderList::clear_runingbilllist() {
  runingbilllist_.Clear();
}
inline ::OHPinfo::OrderBill* HistoryOrderList::mutable_runingbilllist(int index) {
  // @@protoc_insertion_point(field_mutable:OHPinfo.HistoryOrderList.RuningBillList)
  return runingbilllist_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::OHPinfo::OrderBill >*
HistoryOrderList::mutable_runingbilllist() {
  // @@protoc_insertion_point(field_mutable_list:OHPinfo.HistoryOrderList.RuningBillList)
  return &runingbilllist_;
}
inline const ::OHPinfo::OrderBill& HistoryOrderList::_internal_runingbilllist(int index) const {
  return runingbilllist_.Get(index);
}
inline const ::OHPinfo::OrderBill& HistoryOrderList::runingbilllist(int index) const {
  // @@protoc_insertion_point(field_get:OHPinfo.HistoryOrderList.RuningBillList)
  return _internal_runingbilllist(index);
}
inline ::OHPinfo::OrderBill* HistoryOrderList::_internal_add_runingbilllist() {
  return runingbilllist_.Add();
}
inline ::OHPinfo::OrderBill* HistoryOrderList::add_runingbilllist() {
  ::OHPinfo::OrderBill* _add = _internal_add_runingbilllist();
  // @@protoc_insertion_point(field_add:OHPinfo.HistoryOrderList.RuningBillList)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::OHPinfo::OrderBill >&
HistoryOrderList::runingbilllist() const {
  // @@protoc_insertion_point(field_list:OHPinfo.HistoryOrderList.RuningBillList)
  return runingbilllist_;
}

// uint32 StarTime = 10;
inline void HistoryOrderList::clear_startime() {
  startime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HistoryOrderList::_internal_startime() const {
  return startime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HistoryOrderList::startime() const {
  // @@protoc_insertion_point(field_get:OHPinfo.HistoryOrderList.StarTime)
  return _internal_startime();
}
inline void HistoryOrderList::_internal_set_startime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  startime_ = value;
}
inline void HistoryOrderList::set_startime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_startime(value);
  // @@protoc_insertion_point(field_set:OHPinfo.HistoryOrderList.StarTime)
}

// uint32 StopTime = 11;
inline void HistoryOrderList::clear_stoptime() {
  stoptime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HistoryOrderList::_internal_stoptime() const {
  return stoptime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HistoryOrderList::stoptime() const {
  // @@protoc_insertion_point(field_get:OHPinfo.HistoryOrderList.StopTime)
  return _internal_stoptime();
}
inline void HistoryOrderList::_internal_set_stoptime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  stoptime_ = value;
}
inline void HistoryOrderList::set_stoptime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_stoptime(value);
  // @@protoc_insertion_point(field_set:OHPinfo.HistoryOrderList.StopTime)
}

// double StartMeterReadOut = 12;
inline void HistoryOrderList::clear_startmeterreadout() {
  startmeterreadout_ = 0;
}
inline double HistoryOrderList::_internal_startmeterreadout() const {
  return startmeterreadout_;
}
inline double HistoryOrderList::startmeterreadout() const {
  // @@protoc_insertion_point(field_get:OHPinfo.HistoryOrderList.StartMeterReadOut)
  return _internal_startmeterreadout();
}
inline void HistoryOrderList::_internal_set_startmeterreadout(double value) {
  
  startmeterreadout_ = value;
}
inline void HistoryOrderList::set_startmeterreadout(double value) {
  _internal_set_startmeterreadout(value);
  // @@protoc_insertion_point(field_set:OHPinfo.HistoryOrderList.StartMeterReadOut)
}

// double StopMeterReadOut = 13;
inline void HistoryOrderList::clear_stopmeterreadout() {
  stopmeterreadout_ = 0;
}
inline double HistoryOrderList::_internal_stopmeterreadout() const {
  return stopmeterreadout_;
}
inline double HistoryOrderList::stopmeterreadout() const {
  // @@protoc_insertion_point(field_get:OHPinfo.HistoryOrderList.StopMeterReadOut)
  return _internal_stopmeterreadout();
}
inline void HistoryOrderList::_internal_set_stopmeterreadout(double value) {
  
  stopmeterreadout_ = value;
}
inline void HistoryOrderList::set_stopmeterreadout(double value) {
  _internal_set_stopmeterreadout(value);
  // @@protoc_insertion_point(field_set:OHPinfo.HistoryOrderList.StopMeterReadOut)
}

// bytes StopReason = 14;
inline void HistoryOrderList::clear_stopreason() {
  stopreason_.ClearToEmpty();
}
inline const std::string& HistoryOrderList::stopreason() const {
  // @@protoc_insertion_point(field_get:OHPinfo.HistoryOrderList.StopReason)
  return _internal_stopreason();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HistoryOrderList::set_stopreason(ArgT0&& arg0, ArgT... args) {
 
 stopreason_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:OHPinfo.HistoryOrderList.StopReason)
}
inline std::string* HistoryOrderList::mutable_stopreason() {
  std::string* _s = _internal_mutable_stopreason();
  // @@protoc_insertion_point(field_mutable:OHPinfo.HistoryOrderList.StopReason)
  return _s;
}
inline const std::string& HistoryOrderList::_internal_stopreason() const {
  return stopreason_.Get();
}
inline void HistoryOrderList::_internal_set_stopreason(const std::string& value) {
  
  stopreason_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* HistoryOrderList::_internal_mutable_stopreason() {
  
  return stopreason_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* HistoryOrderList::release_stopreason() {
  // @@protoc_insertion_point(field_release:OHPinfo.HistoryOrderList.StopReason)
  return stopreason_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void HistoryOrderList::set_allocated_stopreason(std::string* stopreason) {
  if (stopreason != nullptr) {
    
  } else {
    
  }
  stopreason_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), stopreason,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:OHPinfo.HistoryOrderList.StopReason)
}

// uint32 OSCReserve0 = 15;
inline void HistoryOrderList::clear_oscreserve0() {
  oscreserve0_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HistoryOrderList::_internal_oscreserve0() const {
  return oscreserve0_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HistoryOrderList::oscreserve0() const {
  // @@protoc_insertion_point(field_get:OHPinfo.HistoryOrderList.OSCReserve0)
  return _internal_oscreserve0();
}
inline void HistoryOrderList::_internal_set_oscreserve0(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  oscreserve0_ = value;
}
inline void HistoryOrderList::set_oscreserve0(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_oscreserve0(value);
  // @@protoc_insertion_point(field_set:OHPinfo.HistoryOrderList.OSCReserve0)
}

// uint32 OSCReserve1 = 16;
inline void HistoryOrderList::clear_oscreserve1() {
  oscreserve1_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HistoryOrderList::_internal_oscreserve1() const {
  return oscreserve1_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HistoryOrderList::oscreserve1() const {
  // @@protoc_insertion_point(field_get:OHPinfo.HistoryOrderList.OSCReserve1)
  return _internal_oscreserve1();
}
inline void HistoryOrderList::_internal_set_oscreserve1(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  oscreserve1_ = value;
}
inline void HistoryOrderList::set_oscreserve1(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_oscreserve1(value);
  // @@protoc_insertion_point(field_set:OHPinfo.HistoryOrderList.OSCReserve1)
}

// -------------------------------------------------------------------

// OrderPipelineState

// uint32 MeterID = 1;
inline void OrderPipelineState::clear_meterid() {
  meterid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineState::_internal_meterid() const {
  return meterid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineState::meterid() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineState.MeterID)
  return _internal_meterid();
}
inline void OrderPipelineState::_internal_set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  meterid_ = value;
}
inline void OrderPipelineState::set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_meterid(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineState.MeterID)
}

// uint32 ModuleOpen = 2;
inline void OrderPipelineState::clear_moduleopen() {
  moduleopen_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineState::_internal_moduleopen() const {
  return moduleopen_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineState::moduleopen() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineState.ModuleOpen)
  return _internal_moduleopen();
}
inline void OrderPipelineState::_internal_set_moduleopen(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  moduleopen_ = value;
}
inline void OrderPipelineState::set_moduleopen(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_moduleopen(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineState.ModuleOpen)
}

// uint32 LinkState = 3;
inline void OrderPipelineState::clear_linkstate() {
  linkstate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineState::_internal_linkstate() const {
  return linkstate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineState::linkstate() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineState.LinkState)
  return _internal_linkstate();
}
inline void OrderPipelineState::_internal_set_linkstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  linkstate_ = value;
}
inline void OrderPipelineState::set_linkstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_linkstate(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineState.LinkState)
}

// uint32 OnLineState = 4;
inline void OrderPipelineState::clear_onlinestate() {
  onlinestate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineState::_internal_onlinestate() const {
  return onlinestate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineState::onlinestate() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineState.OnLineState)
  return _internal_onlinestate();
}
inline void OrderPipelineState::_internal_set_onlinestate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  onlinestate_ = value;
}
inline void OrderPipelineState::set_onlinestate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_onlinestate(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineState.OnLineState)
}

// uint32 LockState = 5;
inline void OrderPipelineState::clear_lockstate() {
  lockstate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineState::_internal_lockstate() const {
  return lockstate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineState::lockstate() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineState.LockState)
  return _internal_lockstate();
}
inline void OrderPipelineState::_internal_set_lockstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  lockstate_ = value;
}
inline void OrderPipelineState::set_lockstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_lockstate(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineState.LockState)
}

// uint32 StartType = 6;
inline void OrderPipelineState::clear_starttype() {
  starttype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineState::_internal_starttype() const {
  return starttype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineState::starttype() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineState.StartType)
  return _internal_starttype();
}
inline void OrderPipelineState::_internal_set_starttype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  starttype_ = value;
}
inline void OrderPipelineState::set_starttype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_starttype(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineState.StartType)
}

// uint32 ChargingDuration = 7;
inline void OrderPipelineState::clear_chargingduration() {
  chargingduration_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineState::_internal_chargingduration() const {
  return chargingduration_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineState::chargingduration() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineState.ChargingDuration)
  return _internal_chargingduration();
}
inline void OrderPipelineState::_internal_set_chargingduration(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chargingduration_ = value;
}
inline void OrderPipelineState::set_chargingduration(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chargingduration(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineState.ChargingDuration)
}

// .OHPinfo.PipelineStateEnum PipelineState = 8;
inline void OrderPipelineState::clear_pipelinestate() {
  pipelinestate_ = 0;
}
inline ::OHPinfo::PipelineStateEnum OrderPipelineState::_internal_pipelinestate() const {
  return static_cast< ::OHPinfo::PipelineStateEnum >(pipelinestate_);
}
inline ::OHPinfo::PipelineStateEnum OrderPipelineState::pipelinestate() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineState.PipelineState)
  return _internal_pipelinestate();
}
inline void OrderPipelineState::_internal_set_pipelinestate(::OHPinfo::PipelineStateEnum value) {
  
  pipelinestate_ = value;
}
inline void OrderPipelineState::set_pipelinestate(::OHPinfo::PipelineStateEnum value) {
  _internal_set_pipelinestate(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineState.PipelineState)
}

// float ChargingPower = 9;
inline void OrderPipelineState::clear_chargingpower() {
  chargingpower_ = 0;
}
inline float OrderPipelineState::_internal_chargingpower() const {
  return chargingpower_;
}
inline float OrderPipelineState::chargingpower() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineState.ChargingPower)
  return _internal_chargingpower();
}
inline void OrderPipelineState::_internal_set_chargingpower(float value) {
  
  chargingpower_ = value;
}
inline void OrderPipelineState::set_chargingpower(float value) {
  _internal_set_chargingpower(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineState.ChargingPower)
}

// double NowMeterReadOut = 10;
inline void OrderPipelineState::clear_nowmeterreadout() {
  nowmeterreadout_ = 0;
}
inline double OrderPipelineState::_internal_nowmeterreadout() const {
  return nowmeterreadout_;
}
inline double OrderPipelineState::nowmeterreadout() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineState.NowMeterReadOut)
  return _internal_nowmeterreadout();
}
inline void OrderPipelineState::_internal_set_nowmeterreadout(double value) {
  
  nowmeterreadout_ = value;
}
inline void OrderPipelineState::set_nowmeterreadout(double value) {
  _internal_set_nowmeterreadout(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineState.NowMeterReadOut)
}

// .OSCinfo.SettlementModuleEnum Preempt = 11;
inline void OrderPipelineState::clear_preempt() {
  preempt_ = 0;
}
inline ::OSCinfo::SettlementModuleEnum OrderPipelineState::_internal_preempt() const {
  return static_cast< ::OSCinfo::SettlementModuleEnum >(preempt_);
}
inline ::OSCinfo::SettlementModuleEnum OrderPipelineState::preempt() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineState.Preempt)
  return _internal_preempt();
}
inline void OrderPipelineState::_internal_set_preempt(::OSCinfo::SettlementModuleEnum value) {
  
  preempt_ = value;
}
inline void OrderPipelineState::set_preempt(::OSCinfo::SettlementModuleEnum value) {
  _internal_set_preempt(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineState.Preempt)
}

// -------------------------------------------------------------------

// OrderPipelineAns

// uint32 MeterID = 1;
inline void OrderPipelineAns::clear_meterid() {
  meterid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineAns::_internal_meterid() const {
  return meterid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineAns::meterid() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineAns.MeterID)
  return _internal_meterid();
}
inline void OrderPipelineAns::_internal_set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  meterid_ = value;
}
inline void OrderPipelineAns::set_meterid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_meterid(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineAns.MeterID)
}

// .BMSinfo.ChargeState bmsState = 2;
inline void OrderPipelineAns::clear_bmsstate() {
  bmsstate_ = 0;
}
inline ::BMSinfo::ChargeState OrderPipelineAns::_internal_bmsstate() const {
  return static_cast< ::BMSinfo::ChargeState >(bmsstate_);
}
inline ::BMSinfo::ChargeState OrderPipelineAns::bmsstate() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineAns.bmsState)
  return _internal_bmsstate();
}
inline void OrderPipelineAns::_internal_set_bmsstate(::BMSinfo::ChargeState value) {
  
  bmsstate_ = value;
}
inline void OrderPipelineAns::set_bmsstate(::BMSinfo::ChargeState value) {
  _internal_set_bmsstate(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineAns.bmsState)
}

// .BMSinfo.BMSHandShake BmsShakehandsM = 3;
inline bool OrderPipelineAns::_internal_has_bmsshakehandsm() const {
  return this != internal_default_instance() && bmsshakehandsm_ != nullptr;
}
inline bool OrderPipelineAns::has_bmsshakehandsm() const {
  return _internal_has_bmsshakehandsm();
}
inline const ::BMSinfo::BMSHandShake& OrderPipelineAns::_internal_bmsshakehandsm() const {
  const ::BMSinfo::BMSHandShake* p = bmsshakehandsm_;
  return p != nullptr ? *p : reinterpret_cast<const ::BMSinfo::BMSHandShake&>(
      ::BMSinfo::_BMSHandShake_default_instance_);
}
inline const ::BMSinfo::BMSHandShake& OrderPipelineAns::bmsshakehandsm() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineAns.BmsShakehandsM)
  return _internal_bmsshakehandsm();
}
inline void OrderPipelineAns::unsafe_arena_set_allocated_bmsshakehandsm(
    ::BMSinfo::BMSHandShake* bmsshakehandsm) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmsshakehandsm_);
  }
  bmsshakehandsm_ = bmsshakehandsm;
  if (bmsshakehandsm) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:OHPinfo.OrderPipelineAns.BmsShakehandsM)
}
inline ::BMSinfo::BMSHandShake* OrderPipelineAns::release_bmsshakehandsm() {
  
  ::BMSinfo::BMSHandShake* temp = bmsshakehandsm_;
  bmsshakehandsm_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::BMSinfo::BMSHandShake* OrderPipelineAns::unsafe_arena_release_bmsshakehandsm() {
  // @@protoc_insertion_point(field_release:OHPinfo.OrderPipelineAns.BmsShakehandsM)
  
  ::BMSinfo::BMSHandShake* temp = bmsshakehandsm_;
  bmsshakehandsm_ = nullptr;
  return temp;
}
inline ::BMSinfo::BMSHandShake* OrderPipelineAns::_internal_mutable_bmsshakehandsm() {
  
  if (bmsshakehandsm_ == nullptr) {
    auto* p = CreateMaybeMessage<::BMSinfo::BMSHandShake>(GetArenaForAllocation());
    bmsshakehandsm_ = p;
  }
  return bmsshakehandsm_;
}
inline ::BMSinfo::BMSHandShake* OrderPipelineAns::mutable_bmsshakehandsm() {
  ::BMSinfo::BMSHandShake* _msg = _internal_mutable_bmsshakehandsm();
  // @@protoc_insertion_point(field_mutable:OHPinfo.OrderPipelineAns.BmsShakehandsM)
  return _msg;
}
inline void OrderPipelineAns::set_allocated_bmsshakehandsm(::BMSinfo::BMSHandShake* bmsshakehandsm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmsshakehandsm_);
  }
  if (bmsshakehandsm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmsshakehandsm));
    if (message_arena != submessage_arena) {
      bmsshakehandsm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bmsshakehandsm, submessage_arena);
    }
    
  } else {
    
  }
  bmsshakehandsm_ = bmsshakehandsm;
  // @@protoc_insertion_point(field_set_allocated:OHPinfo.OrderPipelineAns.BmsShakehandsM)
}

// .BMSinfo.BMSVerification BmsIdentifyM = 4;
inline bool OrderPipelineAns::_internal_has_bmsidentifym() const {
  return this != internal_default_instance() && bmsidentifym_ != nullptr;
}
inline bool OrderPipelineAns::has_bmsidentifym() const {
  return _internal_has_bmsidentifym();
}
inline const ::BMSinfo::BMSVerification& OrderPipelineAns::_internal_bmsidentifym() const {
  const ::BMSinfo::BMSVerification* p = bmsidentifym_;
  return p != nullptr ? *p : reinterpret_cast<const ::BMSinfo::BMSVerification&>(
      ::BMSinfo::_BMSVerification_default_instance_);
}
inline const ::BMSinfo::BMSVerification& OrderPipelineAns::bmsidentifym() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineAns.BmsIdentifyM)
  return _internal_bmsidentifym();
}
inline void OrderPipelineAns::unsafe_arena_set_allocated_bmsidentifym(
    ::BMSinfo::BMSVerification* bmsidentifym) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmsidentifym_);
  }
  bmsidentifym_ = bmsidentifym;
  if (bmsidentifym) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:OHPinfo.OrderPipelineAns.BmsIdentifyM)
}
inline ::BMSinfo::BMSVerification* OrderPipelineAns::release_bmsidentifym() {
  
  ::BMSinfo::BMSVerification* temp = bmsidentifym_;
  bmsidentifym_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::BMSinfo::BMSVerification* OrderPipelineAns::unsafe_arena_release_bmsidentifym() {
  // @@protoc_insertion_point(field_release:OHPinfo.OrderPipelineAns.BmsIdentifyM)
  
  ::BMSinfo::BMSVerification* temp = bmsidentifym_;
  bmsidentifym_ = nullptr;
  return temp;
}
inline ::BMSinfo::BMSVerification* OrderPipelineAns::_internal_mutable_bmsidentifym() {
  
  if (bmsidentifym_ == nullptr) {
    auto* p = CreateMaybeMessage<::BMSinfo::BMSVerification>(GetArenaForAllocation());
    bmsidentifym_ = p;
  }
  return bmsidentifym_;
}
inline ::BMSinfo::BMSVerification* OrderPipelineAns::mutable_bmsidentifym() {
  ::BMSinfo::BMSVerification* _msg = _internal_mutable_bmsidentifym();
  // @@protoc_insertion_point(field_mutable:OHPinfo.OrderPipelineAns.BmsIdentifyM)
  return _msg;
}
inline void OrderPipelineAns::set_allocated_bmsidentifym(::BMSinfo::BMSVerification* bmsidentifym) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmsidentifym_);
  }
  if (bmsidentifym) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmsidentifym));
    if (message_arena != submessage_arena) {
      bmsidentifym = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bmsidentifym, submessage_arena);
    }
    
  } else {
    
  }
  bmsidentifym_ = bmsidentifym;
  // @@protoc_insertion_point(field_set_allocated:OHPinfo.OrderPipelineAns.BmsIdentifyM)
}

// .BMSinfo.BMSConfig BmsConfigM = 5;
inline bool OrderPipelineAns::_internal_has_bmsconfigm() const {
  return this != internal_default_instance() && bmsconfigm_ != nullptr;
}
inline bool OrderPipelineAns::has_bmsconfigm() const {
  return _internal_has_bmsconfigm();
}
inline const ::BMSinfo::BMSConfig& OrderPipelineAns::_internal_bmsconfigm() const {
  const ::BMSinfo::BMSConfig* p = bmsconfigm_;
  return p != nullptr ? *p : reinterpret_cast<const ::BMSinfo::BMSConfig&>(
      ::BMSinfo::_BMSConfig_default_instance_);
}
inline const ::BMSinfo::BMSConfig& OrderPipelineAns::bmsconfigm() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineAns.BmsConfigM)
  return _internal_bmsconfigm();
}
inline void OrderPipelineAns::unsafe_arena_set_allocated_bmsconfigm(
    ::BMSinfo::BMSConfig* bmsconfigm) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmsconfigm_);
  }
  bmsconfigm_ = bmsconfigm;
  if (bmsconfigm) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:OHPinfo.OrderPipelineAns.BmsConfigM)
}
inline ::BMSinfo::BMSConfig* OrderPipelineAns::release_bmsconfigm() {
  
  ::BMSinfo::BMSConfig* temp = bmsconfigm_;
  bmsconfigm_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::BMSinfo::BMSConfig* OrderPipelineAns::unsafe_arena_release_bmsconfigm() {
  // @@protoc_insertion_point(field_release:OHPinfo.OrderPipelineAns.BmsConfigM)
  
  ::BMSinfo::BMSConfig* temp = bmsconfigm_;
  bmsconfigm_ = nullptr;
  return temp;
}
inline ::BMSinfo::BMSConfig* OrderPipelineAns::_internal_mutable_bmsconfigm() {
  
  if (bmsconfigm_ == nullptr) {
    auto* p = CreateMaybeMessage<::BMSinfo::BMSConfig>(GetArenaForAllocation());
    bmsconfigm_ = p;
  }
  return bmsconfigm_;
}
inline ::BMSinfo::BMSConfig* OrderPipelineAns::mutable_bmsconfigm() {
  ::BMSinfo::BMSConfig* _msg = _internal_mutable_bmsconfigm();
  // @@protoc_insertion_point(field_mutable:OHPinfo.OrderPipelineAns.BmsConfigM)
  return _msg;
}
inline void OrderPipelineAns::set_allocated_bmsconfigm(::BMSinfo::BMSConfig* bmsconfigm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmsconfigm_);
  }
  if (bmsconfigm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmsconfigm));
    if (message_arena != submessage_arena) {
      bmsconfigm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bmsconfigm, submessage_arena);
    }
    
  } else {
    
  }
  bmsconfigm_ = bmsconfigm;
  // @@protoc_insertion_point(field_set_allocated:OHPinfo.OrderPipelineAns.BmsConfigM)
}

// .BMSinfo.BMSCharging BmsChargingM = 6;
inline bool OrderPipelineAns::_internal_has_bmschargingm() const {
  return this != internal_default_instance() && bmschargingm_ != nullptr;
}
inline bool OrderPipelineAns::has_bmschargingm() const {
  return _internal_has_bmschargingm();
}
inline const ::BMSinfo::BMSCharging& OrderPipelineAns::_internal_bmschargingm() const {
  const ::BMSinfo::BMSCharging* p = bmschargingm_;
  return p != nullptr ? *p : reinterpret_cast<const ::BMSinfo::BMSCharging&>(
      ::BMSinfo::_BMSCharging_default_instance_);
}
inline const ::BMSinfo::BMSCharging& OrderPipelineAns::bmschargingm() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineAns.BmsChargingM)
  return _internal_bmschargingm();
}
inline void OrderPipelineAns::unsafe_arena_set_allocated_bmschargingm(
    ::BMSinfo::BMSCharging* bmschargingm) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmschargingm_);
  }
  bmschargingm_ = bmschargingm;
  if (bmschargingm) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:OHPinfo.OrderPipelineAns.BmsChargingM)
}
inline ::BMSinfo::BMSCharging* OrderPipelineAns::release_bmschargingm() {
  
  ::BMSinfo::BMSCharging* temp = bmschargingm_;
  bmschargingm_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::BMSinfo::BMSCharging* OrderPipelineAns::unsafe_arena_release_bmschargingm() {
  // @@protoc_insertion_point(field_release:OHPinfo.OrderPipelineAns.BmsChargingM)
  
  ::BMSinfo::BMSCharging* temp = bmschargingm_;
  bmschargingm_ = nullptr;
  return temp;
}
inline ::BMSinfo::BMSCharging* OrderPipelineAns::_internal_mutable_bmschargingm() {
  
  if (bmschargingm_ == nullptr) {
    auto* p = CreateMaybeMessage<::BMSinfo::BMSCharging>(GetArenaForAllocation());
    bmschargingm_ = p;
  }
  return bmschargingm_;
}
inline ::BMSinfo::BMSCharging* OrderPipelineAns::mutable_bmschargingm() {
  ::BMSinfo::BMSCharging* _msg = _internal_mutable_bmschargingm();
  // @@protoc_insertion_point(field_mutable:OHPinfo.OrderPipelineAns.BmsChargingM)
  return _msg;
}
inline void OrderPipelineAns::set_allocated_bmschargingm(::BMSinfo::BMSCharging* bmschargingm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmschargingm_);
  }
  if (bmschargingm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmschargingm));
    if (message_arena != submessage_arena) {
      bmschargingm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bmschargingm, submessage_arena);
    }
    
  } else {
    
  }
  bmschargingm_ = bmschargingm;
  // @@protoc_insertion_point(field_set_allocated:OHPinfo.OrderPipelineAns.BmsChargingM)
}

// .BMSinfo.BMSChargingEnd BmsChargeFinishM = 7;
inline bool OrderPipelineAns::_internal_has_bmschargefinishm() const {
  return this != internal_default_instance() && bmschargefinishm_ != nullptr;
}
inline bool OrderPipelineAns::has_bmschargefinishm() const {
  return _internal_has_bmschargefinishm();
}
inline const ::BMSinfo::BMSChargingEnd& OrderPipelineAns::_internal_bmschargefinishm() const {
  const ::BMSinfo::BMSChargingEnd* p = bmschargefinishm_;
  return p != nullptr ? *p : reinterpret_cast<const ::BMSinfo::BMSChargingEnd&>(
      ::BMSinfo::_BMSChargingEnd_default_instance_);
}
inline const ::BMSinfo::BMSChargingEnd& OrderPipelineAns::bmschargefinishm() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineAns.BmsChargeFinishM)
  return _internal_bmschargefinishm();
}
inline void OrderPipelineAns::unsafe_arena_set_allocated_bmschargefinishm(
    ::BMSinfo::BMSChargingEnd* bmschargefinishm) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmschargefinishm_);
  }
  bmschargefinishm_ = bmschargefinishm;
  if (bmschargefinishm) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:OHPinfo.OrderPipelineAns.BmsChargeFinishM)
}
inline ::BMSinfo::BMSChargingEnd* OrderPipelineAns::release_bmschargefinishm() {
  
  ::BMSinfo::BMSChargingEnd* temp = bmschargefinishm_;
  bmschargefinishm_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::BMSinfo::BMSChargingEnd* OrderPipelineAns::unsafe_arena_release_bmschargefinishm() {
  // @@protoc_insertion_point(field_release:OHPinfo.OrderPipelineAns.BmsChargeFinishM)
  
  ::BMSinfo::BMSChargingEnd* temp = bmschargefinishm_;
  bmschargefinishm_ = nullptr;
  return temp;
}
inline ::BMSinfo::BMSChargingEnd* OrderPipelineAns::_internal_mutable_bmschargefinishm() {
  
  if (bmschargefinishm_ == nullptr) {
    auto* p = CreateMaybeMessage<::BMSinfo::BMSChargingEnd>(GetArenaForAllocation());
    bmschargefinishm_ = p;
  }
  return bmschargefinishm_;
}
inline ::BMSinfo::BMSChargingEnd* OrderPipelineAns::mutable_bmschargefinishm() {
  ::BMSinfo::BMSChargingEnd* _msg = _internal_mutable_bmschargefinishm();
  // @@protoc_insertion_point(field_mutable:OHPinfo.OrderPipelineAns.BmsChargeFinishM)
  return _msg;
}
inline void OrderPipelineAns::set_allocated_bmschargefinishm(::BMSinfo::BMSChargingEnd* bmschargefinishm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmschargefinishm_);
  }
  if (bmschargefinishm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmschargefinishm));
    if (message_arena != submessage_arena) {
      bmschargefinishm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bmschargefinishm, submessage_arena);
    }
    
  } else {
    
  }
  bmschargefinishm_ = bmschargefinishm;
  // @@protoc_insertion_point(field_set_allocated:OHPinfo.OrderPipelineAns.BmsChargeFinishM)
}

// .AllFaultEnum.MatrixStatus ContactorState = 8;
inline bool OrderPipelineAns::_internal_has_contactorstate() const {
  return this != internal_default_instance() && contactorstate_ != nullptr;
}
inline bool OrderPipelineAns::has_contactorstate() const {
  return _internal_has_contactorstate();
}
inline const ::AllFaultEnum::MatrixStatus& OrderPipelineAns::_internal_contactorstate() const {
  const ::AllFaultEnum::MatrixStatus* p = contactorstate_;
  return p != nullptr ? *p : reinterpret_cast<const ::AllFaultEnum::MatrixStatus&>(
      ::AllFaultEnum::_MatrixStatus_default_instance_);
}
inline const ::AllFaultEnum::MatrixStatus& OrderPipelineAns::contactorstate() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineAns.ContactorState)
  return _internal_contactorstate();
}
inline void OrderPipelineAns::unsafe_arena_set_allocated_contactorstate(
    ::AllFaultEnum::MatrixStatus* contactorstate) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(contactorstate_);
  }
  contactorstate_ = contactorstate;
  if (contactorstate) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:OHPinfo.OrderPipelineAns.ContactorState)
}
inline ::AllFaultEnum::MatrixStatus* OrderPipelineAns::release_contactorstate() {
  
  ::AllFaultEnum::MatrixStatus* temp = contactorstate_;
  contactorstate_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::AllFaultEnum::MatrixStatus* OrderPipelineAns::unsafe_arena_release_contactorstate() {
  // @@protoc_insertion_point(field_release:OHPinfo.OrderPipelineAns.ContactorState)
  
  ::AllFaultEnum::MatrixStatus* temp = contactorstate_;
  contactorstate_ = nullptr;
  return temp;
}
inline ::AllFaultEnum::MatrixStatus* OrderPipelineAns::_internal_mutable_contactorstate() {
  
  if (contactorstate_ == nullptr) {
    auto* p = CreateMaybeMessage<::AllFaultEnum::MatrixStatus>(GetArenaForAllocation());
    contactorstate_ = p;
  }
  return contactorstate_;
}
inline ::AllFaultEnum::MatrixStatus* OrderPipelineAns::mutable_contactorstate() {
  ::AllFaultEnum::MatrixStatus* _msg = _internal_mutable_contactorstate();
  // @@protoc_insertion_point(field_mutable:OHPinfo.OrderPipelineAns.ContactorState)
  return _msg;
}
inline void OrderPipelineAns::set_allocated_contactorstate(::AllFaultEnum::MatrixStatus* contactorstate) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(contactorstate_);
  }
  if (contactorstate) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(contactorstate));
    if (message_arena != submessage_arena) {
      contactorstate = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, contactorstate, submessage_arena);
    }
    
  } else {
    
  }
  contactorstate_ = contactorstate;
  // @@protoc_insertion_point(field_set_allocated:OHPinfo.OrderPipelineAns.ContactorState)
}

// uint32 OHPFaultSize = 9;
inline void OrderPipelineAns::clear_ohpfaultsize() {
  ohpfaultsize_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineAns::_internal_ohpfaultsize() const {
  return ohpfaultsize_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineAns::ohpfaultsize() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineAns.OHPFaultSize)
  return _internal_ohpfaultsize();
}
inline void OrderPipelineAns::_internal_set_ohpfaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  ohpfaultsize_ = value;
}
inline void OrderPipelineAns::set_ohpfaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_ohpfaultsize(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineAns.OHPFaultSize)
}

// repeated .AllFaultEnum.OHPFaultState OHPFault = 10;
inline int OrderPipelineAns::_internal_ohpfault_size() const {
  return ohpfault_.size();
}
inline int OrderPipelineAns::ohpfault_size() const {
  return _internal_ohpfault_size();
}
inline ::AllFaultEnum::OHPFaultState* OrderPipelineAns::mutable_ohpfault(int index) {
  // @@protoc_insertion_point(field_mutable:OHPinfo.OrderPipelineAns.OHPFault)
  return ohpfault_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::OHPFaultState >*
OrderPipelineAns::mutable_ohpfault() {
  // @@protoc_insertion_point(field_mutable_list:OHPinfo.OrderPipelineAns.OHPFault)
  return &ohpfault_;
}
inline const ::AllFaultEnum::OHPFaultState& OrderPipelineAns::_internal_ohpfault(int index) const {
  return ohpfault_.Get(index);
}
inline const ::AllFaultEnum::OHPFaultState& OrderPipelineAns::ohpfault(int index) const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineAns.OHPFault)
  return _internal_ohpfault(index);
}
inline ::AllFaultEnum::OHPFaultState* OrderPipelineAns::_internal_add_ohpfault() {
  return ohpfault_.Add();
}
inline ::AllFaultEnum::OHPFaultState* OrderPipelineAns::add_ohpfault() {
  ::AllFaultEnum::OHPFaultState* _add = _internal_add_ohpfault();
  // @@protoc_insertion_point(field_add:OHPinfo.OrderPipelineAns.OHPFault)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::OHPFaultState >&
OrderPipelineAns::ohpfault() const {
  // @@protoc_insertion_point(field_list:OHPinfo.OrderPipelineAns.OHPFault)
  return ohpfault_;
}

// uint32 PMMFaultSize = 11;
inline void OrderPipelineAns::clear_pmmfaultsize() {
  pmmfaultsize_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineAns::_internal_pmmfaultsize() const {
  return pmmfaultsize_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineAns::pmmfaultsize() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineAns.PMMFaultSize)
  return _internal_pmmfaultsize();
}
inline void OrderPipelineAns::_internal_set_pmmfaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  pmmfaultsize_ = value;
}
inline void OrderPipelineAns::set_pmmfaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_pmmfaultsize(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineAns.PMMFaultSize)
}

// repeated .AllFaultEnum.PMMFaultState MainAlarmList = 12;
inline int OrderPipelineAns::_internal_mainalarmlist_size() const {
  return mainalarmlist_.size();
}
inline int OrderPipelineAns::mainalarmlist_size() const {
  return _internal_mainalarmlist_size();
}
inline ::AllFaultEnum::PMMFaultState* OrderPipelineAns::mutable_mainalarmlist(int index) {
  // @@protoc_insertion_point(field_mutable:OHPinfo.OrderPipelineAns.MainAlarmList)
  return mainalarmlist_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::PMMFaultState >*
OrderPipelineAns::mutable_mainalarmlist() {
  // @@protoc_insertion_point(field_mutable_list:OHPinfo.OrderPipelineAns.MainAlarmList)
  return &mainalarmlist_;
}
inline const ::AllFaultEnum::PMMFaultState& OrderPipelineAns::_internal_mainalarmlist(int index) const {
  return mainalarmlist_.Get(index);
}
inline const ::AllFaultEnum::PMMFaultState& OrderPipelineAns::mainalarmlist(int index) const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineAns.MainAlarmList)
  return _internal_mainalarmlist(index);
}
inline ::AllFaultEnum::PMMFaultState* OrderPipelineAns::_internal_add_mainalarmlist() {
  return mainalarmlist_.Add();
}
inline ::AllFaultEnum::PMMFaultState* OrderPipelineAns::add_mainalarmlist() {
  ::AllFaultEnum::PMMFaultState* _add = _internal_add_mainalarmlist();
  // @@protoc_insertion_point(field_add:OHPinfo.OrderPipelineAns.MainAlarmList)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::PMMFaultState >&
OrderPipelineAns::mainalarmlist() const {
  // @@protoc_insertion_point(field_list:OHPinfo.OrderPipelineAns.MainAlarmList)
  return mainalarmlist_;
}

// uint32 VCIFaultSize = 13;
inline void OrderPipelineAns::clear_vcifaultsize() {
  vcifaultsize_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineAns::_internal_vcifaultsize() const {
  return vcifaultsize_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineAns::vcifaultsize() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineAns.VCIFaultSize)
  return _internal_vcifaultsize();
}
inline void OrderPipelineAns::_internal_set_vcifaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  vcifaultsize_ = value;
}
inline void OrderPipelineAns::set_vcifaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_vcifaultsize(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineAns.VCIFaultSize)
}

// repeated .AllFaultEnum.VCIFaultState VCIFault = 14;
inline int OrderPipelineAns::_internal_vcifault_size() const {
  return vcifault_.size();
}
inline int OrderPipelineAns::vcifault_size() const {
  return _internal_vcifault_size();
}
inline ::AllFaultEnum::VCIFaultState* OrderPipelineAns::mutable_vcifault(int index) {
  // @@protoc_insertion_point(field_mutable:OHPinfo.OrderPipelineAns.VCIFault)
  return vcifault_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::VCIFaultState >*
OrderPipelineAns::mutable_vcifault() {
  // @@protoc_insertion_point(field_mutable_list:OHPinfo.OrderPipelineAns.VCIFault)
  return &vcifault_;
}
inline const ::AllFaultEnum::VCIFaultState& OrderPipelineAns::_internal_vcifault(int index) const {
  return vcifault_.Get(index);
}
inline const ::AllFaultEnum::VCIFaultState& OrderPipelineAns::vcifault(int index) const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineAns.VCIFault)
  return _internal_vcifault(index);
}
inline ::AllFaultEnum::VCIFaultState* OrderPipelineAns::_internal_add_vcifault() {
  return vcifault_.Add();
}
inline ::AllFaultEnum::VCIFaultState* OrderPipelineAns::add_vcifault() {
  ::AllFaultEnum::VCIFaultState* _add = _internal_add_vcifault();
  // @@protoc_insertion_point(field_add:OHPinfo.OrderPipelineAns.VCIFault)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::VCIFaultState >&
OrderPipelineAns::vcifault() const {
  // @@protoc_insertion_point(field_list:OHPinfo.OrderPipelineAns.VCIFault)
  return vcifault_;
}

// uint32 DMCFaultSize = 15;
inline void OrderPipelineAns::clear_dmcfaultsize() {
  dmcfaultsize_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineAns::_internal_dmcfaultsize() const {
  return dmcfaultsize_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineAns::dmcfaultsize() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineAns.DMCFaultSize)
  return _internal_dmcfaultsize();
}
inline void OrderPipelineAns::_internal_set_dmcfaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  dmcfaultsize_ = value;
}
inline void OrderPipelineAns::set_dmcfaultsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_dmcfaultsize(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineAns.DMCFaultSize)
}

// repeated .AllFaultEnum.DMCFaultState DMCFault = 16;
inline int OrderPipelineAns::_internal_dmcfault_size() const {
  return dmcfault_.size();
}
inline int OrderPipelineAns::dmcfault_size() const {
  return _internal_dmcfault_size();
}
inline ::AllFaultEnum::DMCFaultState* OrderPipelineAns::mutable_dmcfault(int index) {
  // @@protoc_insertion_point(field_mutable:OHPinfo.OrderPipelineAns.DMCFault)
  return dmcfault_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::DMCFaultState >*
OrderPipelineAns::mutable_dmcfault() {
  // @@protoc_insertion_point(field_mutable_list:OHPinfo.OrderPipelineAns.DMCFault)
  return &dmcfault_;
}
inline const ::AllFaultEnum::DMCFaultState& OrderPipelineAns::_internal_dmcfault(int index) const {
  return dmcfault_.Get(index);
}
inline const ::AllFaultEnum::DMCFaultState& OrderPipelineAns::dmcfault(int index) const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineAns.DMCFault)
  return _internal_dmcfault(index);
}
inline ::AllFaultEnum::DMCFaultState* OrderPipelineAns::_internal_add_dmcfault() {
  return dmcfault_.Add();
}
inline ::AllFaultEnum::DMCFaultState* OrderPipelineAns::add_dmcfault() {
  ::AllFaultEnum::DMCFaultState* _add = _internal_add_dmcfault();
  // @@protoc_insertion_point(field_add:OHPinfo.OrderPipelineAns.DMCFault)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::DMCFaultState >&
OrderPipelineAns::dmcfault() const {
  // @@protoc_insertion_point(field_list:OHPinfo.OrderPipelineAns.DMCFault)
  return dmcfault_;
}

// uint32 ADModuleParamSize = 17;
inline void OrderPipelineAns::clear_admoduleparamsize() {
  admoduleparamsize_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineAns::_internal_admoduleparamsize() const {
  return admoduleparamsize_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineAns::admoduleparamsize() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineAns.ADModuleParamSize)
  return _internal_admoduleparamsize();
}
inline void OrderPipelineAns::_internal_set_admoduleparamsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  admoduleparamsize_ = value;
}
inline void OrderPipelineAns::set_admoduleparamsize(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_admoduleparamsize(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineAns.ADModuleParamSize)
}

// repeated .AllFaultEnum.ADModuleAlarm ADModulePList = 18;
inline int OrderPipelineAns::_internal_admoduleplist_size() const {
  return admoduleplist_.size();
}
inline int OrderPipelineAns::admoduleplist_size() const {
  return _internal_admoduleplist_size();
}
inline ::AllFaultEnum::ADModuleAlarm* OrderPipelineAns::mutable_admoduleplist(int index) {
  // @@protoc_insertion_point(field_mutable:OHPinfo.OrderPipelineAns.ADModulePList)
  return admoduleplist_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::ADModuleAlarm >*
OrderPipelineAns::mutable_admoduleplist() {
  // @@protoc_insertion_point(field_mutable_list:OHPinfo.OrderPipelineAns.ADModulePList)
  return &admoduleplist_;
}
inline const ::AllFaultEnum::ADModuleAlarm& OrderPipelineAns::_internal_admoduleplist(int index) const {
  return admoduleplist_.Get(index);
}
inline const ::AllFaultEnum::ADModuleAlarm& OrderPipelineAns::admoduleplist(int index) const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineAns.ADModulePList)
  return _internal_admoduleplist(index);
}
inline ::AllFaultEnum::ADModuleAlarm* OrderPipelineAns::_internal_add_admoduleplist() {
  return admoduleplist_.Add();
}
inline ::AllFaultEnum::ADModuleAlarm* OrderPipelineAns::add_admoduleplist() {
  ::AllFaultEnum::ADModuleAlarm* _add = _internal_add_admoduleplist();
  // @@protoc_insertion_point(field_add:OHPinfo.OrderPipelineAns.ADModulePList)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::ADModuleAlarm >&
OrderPipelineAns::admoduleplist() const {
  // @@protoc_insertion_point(field_list:OHPinfo.OrderPipelineAns.ADModulePList)
  return admoduleplist_;
}

// string MeterSN = 19;
inline void OrderPipelineAns::clear_metersn() {
  metersn_.ClearToEmpty();
}
inline const std::string& OrderPipelineAns::metersn() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineAns.MeterSN)
  return _internal_metersn();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OrderPipelineAns::set_metersn(ArgT0&& arg0, ArgT... args) {
 
 metersn_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineAns.MeterSN)
}
inline std::string* OrderPipelineAns::mutable_metersn() {
  std::string* _s = _internal_mutable_metersn();
  // @@protoc_insertion_point(field_mutable:OHPinfo.OrderPipelineAns.MeterSN)
  return _s;
}
inline const std::string& OrderPipelineAns::_internal_metersn() const {
  return metersn_.Get();
}
inline void OrderPipelineAns::_internal_set_metersn(const std::string& value) {
  
  metersn_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* OrderPipelineAns::_internal_mutable_metersn() {
  
  return metersn_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* OrderPipelineAns::release_metersn() {
  // @@protoc_insertion_point(field_release:OHPinfo.OrderPipelineAns.MeterSN)
  return metersn_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void OrderPipelineAns::set_allocated_metersn(std::string* metersn) {
  if (metersn != nullptr) {
    
  } else {
    
  }
  metersn_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), metersn,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:OHPinfo.OrderPipelineAns.MeterSN)
}

// string MeterVer = 20;
inline void OrderPipelineAns::clear_meterver() {
  meterver_.ClearToEmpty();
}
inline const std::string& OrderPipelineAns::meterver() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineAns.MeterVer)
  return _internal_meterver();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OrderPipelineAns::set_meterver(ArgT0&& arg0, ArgT... args) {
 
 meterver_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineAns.MeterVer)
}
inline std::string* OrderPipelineAns::mutable_meterver() {
  std::string* _s = _internal_mutable_meterver();
  // @@protoc_insertion_point(field_mutable:OHPinfo.OrderPipelineAns.MeterVer)
  return _s;
}
inline const std::string& OrderPipelineAns::_internal_meterver() const {
  return meterver_.Get();
}
inline void OrderPipelineAns::_internal_set_meterver(const std::string& value) {
  
  meterver_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* OrderPipelineAns::_internal_mutable_meterver() {
  
  return meterver_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* OrderPipelineAns::release_meterver() {
  // @@protoc_insertion_point(field_release:OHPinfo.OrderPipelineAns.MeterVer)
  return meterver_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void OrderPipelineAns::set_allocated_meterver(std::string* meterver) {
  if (meterver != nullptr) {
    
  } else {
    
  }
  meterver_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), meterver,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:OHPinfo.OrderPipelineAns.MeterVer)
}

// .BMS2015PlusInfo.bms2015pMsg bms2015pM = 21;
inline bool OrderPipelineAns::_internal_has_bms2015pm() const {
  return this != internal_default_instance() && bms2015pm_ != nullptr;
}
inline bool OrderPipelineAns::has_bms2015pm() const {
  return _internal_has_bms2015pm();
}
inline const ::BMS2015PlusInfo::bms2015pMsg& OrderPipelineAns::_internal_bms2015pm() const {
  const ::BMS2015PlusInfo::bms2015pMsg* p = bms2015pm_;
  return p != nullptr ? *p : reinterpret_cast<const ::BMS2015PlusInfo::bms2015pMsg&>(
      ::BMS2015PlusInfo::_bms2015pMsg_default_instance_);
}
inline const ::BMS2015PlusInfo::bms2015pMsg& OrderPipelineAns::bms2015pm() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineAns.bms2015pM)
  return _internal_bms2015pm();
}
inline void OrderPipelineAns::unsafe_arena_set_allocated_bms2015pm(
    ::BMS2015PlusInfo::bms2015pMsg* bms2015pm) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bms2015pm_);
  }
  bms2015pm_ = bms2015pm;
  if (bms2015pm) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:OHPinfo.OrderPipelineAns.bms2015pM)
}
inline ::BMS2015PlusInfo::bms2015pMsg* OrderPipelineAns::release_bms2015pm() {
  
  ::BMS2015PlusInfo::bms2015pMsg* temp = bms2015pm_;
  bms2015pm_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::BMS2015PlusInfo::bms2015pMsg* OrderPipelineAns::unsafe_arena_release_bms2015pm() {
  // @@protoc_insertion_point(field_release:OHPinfo.OrderPipelineAns.bms2015pM)
  
  ::BMS2015PlusInfo::bms2015pMsg* temp = bms2015pm_;
  bms2015pm_ = nullptr;
  return temp;
}
inline ::BMS2015PlusInfo::bms2015pMsg* OrderPipelineAns::_internal_mutable_bms2015pm() {
  
  if (bms2015pm_ == nullptr) {
    auto* p = CreateMaybeMessage<::BMS2015PlusInfo::bms2015pMsg>(GetArenaForAllocation());
    bms2015pm_ = p;
  }
  return bms2015pm_;
}
inline ::BMS2015PlusInfo::bms2015pMsg* OrderPipelineAns::mutable_bms2015pm() {
  ::BMS2015PlusInfo::bms2015pMsg* _msg = _internal_mutable_bms2015pm();
  // @@protoc_insertion_point(field_mutable:OHPinfo.OrderPipelineAns.bms2015pM)
  return _msg;
}
inline void OrderPipelineAns::set_allocated_bms2015pm(::BMS2015PlusInfo::bms2015pMsg* bms2015pm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(bms2015pm_);
  }
  if (bms2015pm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bms2015pm));
    if (message_arena != submessage_arena) {
      bms2015pm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bms2015pm, submessage_arena);
    }
    
  } else {
    
  }
  bms2015pm_ = bms2015pm;
  // @@protoc_insertion_point(field_set_allocated:OHPinfo.OrderPipelineAns.bms2015pM)
}

// -------------------------------------------------------------------

// OrderPipelineInfo

// string OrderID = 1;
inline void OrderPipelineInfo::clear_orderid() {
  orderid_.ClearToEmpty();
}
inline const std::string& OrderPipelineInfo::orderid() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineInfo.OrderID)
  return _internal_orderid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OrderPipelineInfo::set_orderid(ArgT0&& arg0, ArgT... args) {
 
 orderid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineInfo.OrderID)
}
inline std::string* OrderPipelineInfo::mutable_orderid() {
  std::string* _s = _internal_mutable_orderid();
  // @@protoc_insertion_point(field_mutable:OHPinfo.OrderPipelineInfo.OrderID)
  return _s;
}
inline const std::string& OrderPipelineInfo::_internal_orderid() const {
  return orderid_.Get();
}
inline void OrderPipelineInfo::_internal_set_orderid(const std::string& value) {
  
  orderid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* OrderPipelineInfo::_internal_mutable_orderid() {
  
  return orderid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* OrderPipelineInfo::release_orderid() {
  // @@protoc_insertion_point(field_release:OHPinfo.OrderPipelineInfo.OrderID)
  return orderid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void OrderPipelineInfo::set_allocated_orderid(std::string* orderid) {
  if (orderid != nullptr) {
    
  } else {
    
  }
  orderid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), orderid,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:OHPinfo.OrderPipelineInfo.OrderID)
}

// string UserID = 2;
inline void OrderPipelineInfo::clear_userid() {
  userid_.ClearToEmpty();
}
inline const std::string& OrderPipelineInfo::userid() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineInfo.UserID)
  return _internal_userid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OrderPipelineInfo::set_userid(ArgT0&& arg0, ArgT... args) {
 
 userid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineInfo.UserID)
}
inline std::string* OrderPipelineInfo::mutable_userid() {
  std::string* _s = _internal_mutable_userid();
  // @@protoc_insertion_point(field_mutable:OHPinfo.OrderPipelineInfo.UserID)
  return _s;
}
inline const std::string& OrderPipelineInfo::_internal_userid() const {
  return userid_.Get();
}
inline void OrderPipelineInfo::_internal_set_userid(const std::string& value) {
  
  userid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* OrderPipelineInfo::_internal_mutable_userid() {
  
  return userid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* OrderPipelineInfo::release_userid() {
  // @@protoc_insertion_point(field_release:OHPinfo.OrderPipelineInfo.UserID)
  return userid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void OrderPipelineInfo::set_allocated_userid(std::string* userid) {
  if (userid != nullptr) {
    
  } else {
    
  }
  userid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), userid,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:OHPinfo.OrderPipelineInfo.UserID)
}

// uint32 StartSoc = 3;
inline void OrderPipelineInfo::clear_startsoc() {
  startsoc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineInfo::_internal_startsoc() const {
  return startsoc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineInfo::startsoc() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineInfo.StartSoc)
  return _internal_startsoc();
}
inline void OrderPipelineInfo::_internal_set_startsoc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  startsoc_ = value;
}
inline void OrderPipelineInfo::set_startsoc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_startsoc(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineInfo.StartSoc)
}

// uint32 StartTime = 4;
inline void OrderPipelineInfo::clear_starttime() {
  starttime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineInfo::_internal_starttime() const {
  return starttime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OrderPipelineInfo::starttime() const {
  // @@protoc_insertion_point(field_get:OHPinfo.OrderPipelineInfo.StartTime)
  return _internal_starttime();
}
inline void OrderPipelineInfo::_internal_set_starttime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  starttime_ = value;
}
inline void OrderPipelineInfo::set_starttime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_starttime(value);
  // @@protoc_insertion_point(field_set:OHPinfo.OrderPipelineInfo.StartTime)
}

// -------------------------------------------------------------------

// hmiConfigInfo

// uint32 guns = 1;
inline void hmiConfigInfo::clear_guns() {
  guns_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::_internal_guns() const {
  return guns_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::guns() const {
  // @@protoc_insertion_point(field_get:OHPinfo.hmiConfigInfo.guns)
  return _internal_guns();
}
inline void hmiConfigInfo::_internal_set_guns(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  guns_ = value;
}
inline void hmiConfigInfo::set_guns(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_guns(value);
  // @@protoc_insertion_point(field_set:OHPinfo.hmiConfigInfo.guns)
}

// uint32 gunCode = 2;
inline void hmiConfigInfo::clear_guncode() {
  guncode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::_internal_guncode() const {
  return guncode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::guncode() const {
  // @@protoc_insertion_point(field_get:OHPinfo.hmiConfigInfo.gunCode)
  return _internal_guncode();
}
inline void hmiConfigInfo::_internal_set_guncode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  guncode_ = value;
}
inline void hmiConfigInfo::set_guncode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_guncode(value);
  // @@protoc_insertion_point(field_set:OHPinfo.hmiConfigInfo.gunCode)
}

// .DMCinfo.GunTypeEnum GunType = 3;
inline void hmiConfigInfo::clear_guntype() {
  guntype_ = 0;
}
inline ::DMCinfo::GunTypeEnum hmiConfigInfo::_internal_guntype() const {
  return static_cast< ::DMCinfo::GunTypeEnum >(guntype_);
}
inline ::DMCinfo::GunTypeEnum hmiConfigInfo::guntype() const {
  // @@protoc_insertion_point(field_get:OHPinfo.hmiConfigInfo.GunType)
  return _internal_guntype();
}
inline void hmiConfigInfo::_internal_set_guntype(::DMCinfo::GunTypeEnum value) {
  
  guntype_ = value;
}
inline void hmiConfigInfo::set_guntype(::DMCinfo::GunTypeEnum value) {
  _internal_set_guntype(value);
  // @@protoc_insertion_point(field_set:OHPinfo.hmiConfigInfo.GunType)
}

// uint32 VINType = 4;
inline void hmiConfigInfo::clear_vintype() {
  vintype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::_internal_vintype() const {
  return vintype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::vintype() const {
  // @@protoc_insertion_point(field_get:OHPinfo.hmiConfigInfo.VINType)
  return _internal_vintype();
}
inline void hmiConfigInfo::_internal_set_vintype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  vintype_ = value;
}
inline void hmiConfigInfo::set_vintype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_vintype(value);
  // @@protoc_insertion_point(field_set:OHPinfo.hmiConfigInfo.VINType)
}

// uint32 AuxType = 5;
inline void hmiConfigInfo::clear_auxtype() {
  auxtype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::_internal_auxtype() const {
  return auxtype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::auxtype() const {
  // @@protoc_insertion_point(field_get:OHPinfo.hmiConfigInfo.AuxType)
  return _internal_auxtype();
}
inline void hmiConfigInfo::_internal_set_auxtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  auxtype_ = value;
}
inline void hmiConfigInfo::set_auxtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_auxtype(value);
  // @@protoc_insertion_point(field_set:OHPinfo.hmiConfigInfo.AuxType)
}

// uint32 adminMode = 6;
inline void hmiConfigInfo::clear_adminmode() {
  adminmode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::_internal_adminmode() const {
  return adminmode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::adminmode() const {
  // @@protoc_insertion_point(field_get:OHPinfo.hmiConfigInfo.adminMode)
  return _internal_adminmode();
}
inline void hmiConfigInfo::_internal_set_adminmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  adminmode_ = value;
}
inline void hmiConfigInfo::set_adminmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_adminmode(value);
  // @@protoc_insertion_point(field_set:OHPinfo.hmiConfigInfo.adminMode)
}

// uint32 stopType = 7;
inline void hmiConfigInfo::clear_stoptype() {
  stoptype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::_internal_stoptype() const {
  return stoptype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::stoptype() const {
  // @@protoc_insertion_point(field_get:OHPinfo.hmiConfigInfo.stopType)
  return _internal_stoptype();
}
inline void hmiConfigInfo::_internal_set_stoptype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  stoptype_ = value;
}
inline void hmiConfigInfo::set_stoptype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_stoptype(value);
  // @@protoc_insertion_point(field_set:OHPinfo.hmiConfigInfo.stopType)
}

// uint32 RateType = 8;
inline void hmiConfigInfo::clear_ratetype() {
  ratetype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::_internal_ratetype() const {
  return ratetype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::ratetype() const {
  // @@protoc_insertion_point(field_get:OHPinfo.hmiConfigInfo.RateType)
  return _internal_ratetype();
}
inline void hmiConfigInfo::_internal_set_ratetype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  ratetype_ = value;
}
inline void hmiConfigInfo::set_ratetype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_ratetype(value);
  // @@protoc_insertion_point(field_set:OHPinfo.hmiConfigInfo.RateType)
}

// uint32 standbylogoType = 9;
inline void hmiConfigInfo::clear_standbylogotype() {
  standbylogotype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::_internal_standbylogotype() const {
  return standbylogotype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::standbylogotype() const {
  // @@protoc_insertion_point(field_get:OHPinfo.hmiConfigInfo.standbylogoType)
  return _internal_standbylogotype();
}
inline void hmiConfigInfo::_internal_set_standbylogotype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  standbylogotype_ = value;
}
inline void hmiConfigInfo::set_standbylogotype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_standbylogotype(value);
  // @@protoc_insertion_point(field_set:OHPinfo.hmiConfigInfo.standbylogoType)
}

// uint32 ledType = 10;
inline void hmiConfigInfo::clear_ledtype() {
  ledtype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::_internal_ledtype() const {
  return ledtype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::ledtype() const {
  // @@protoc_insertion_point(field_get:OHPinfo.hmiConfigInfo.ledType)
  return _internal_ledtype();
}
inline void hmiConfigInfo::_internal_set_ledtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  ledtype_ = value;
}
inline void hmiConfigInfo::set_ledtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_ledtype(value);
  // @@protoc_insertion_point(field_set:OHPinfo.hmiConfigInfo.ledType)
}

// uint32 historyType = 11;
inline void hmiConfigInfo::clear_historytype() {
  historytype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::_internal_historytype() const {
  return historytype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::historytype() const {
  // @@protoc_insertion_point(field_get:OHPinfo.hmiConfigInfo.historyType)
  return _internal_historytype();
}
inline void hmiConfigInfo::_internal_set_historytype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  historytype_ = value;
}
inline void hmiConfigInfo::set_historytype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_historytype(value);
  // @@protoc_insertion_point(field_set:OHPinfo.hmiConfigInfo.historyType)
}

// uint32 stopChgSocType = 12;
inline void hmiConfigInfo::clear_stopchgsoctype() {
  stopchgsoctype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::_internal_stopchgsoctype() const {
  return stopchgsoctype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::stopchgsoctype() const {
  // @@protoc_insertion_point(field_get:OHPinfo.hmiConfigInfo.stopChgSocType)
  return _internal_stopchgsoctype();
}
inline void hmiConfigInfo::_internal_set_stopchgsoctype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  stopchgsoctype_ = value;
}
inline void hmiConfigInfo::set_stopchgsoctype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_stopchgsoctype(value);
  // @@protoc_insertion_point(field_set:OHPinfo.hmiConfigInfo.stopChgSocType)
}

// uint32 hmiConfigEnable = 13;
inline void hmiConfigInfo::clear_hmiconfigenable() {
  hmiconfigenable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::_internal_hmiconfigenable() const {
  return hmiconfigenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::hmiconfigenable() const {
  // @@protoc_insertion_point(field_get:OHPinfo.hmiConfigInfo.hmiConfigEnable)
  return _internal_hmiconfigenable();
}
inline void hmiConfigInfo::_internal_set_hmiconfigenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  hmiconfigenable_ = value;
}
inline void hmiConfigInfo::set_hmiconfigenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_hmiconfigenable(value);
  // @@protoc_insertion_point(field_set:OHPinfo.hmiConfigInfo.hmiConfigEnable)
}

// uint32 netOfflineWifiEnable = 14;
inline void hmiConfigInfo::clear_netofflinewifienable() {
  netofflinewifienable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::_internal_netofflinewifienable() const {
  return netofflinewifienable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::netofflinewifienable() const {
  // @@protoc_insertion_point(field_get:OHPinfo.hmiConfigInfo.netOfflineWifiEnable)
  return _internal_netofflinewifienable();
}
inline void hmiConfigInfo::_internal_set_netofflinewifienable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  netofflinewifienable_ = value;
}
inline void hmiConfigInfo::set_netofflinewifienable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_netofflinewifienable(value);
  // @@protoc_insertion_point(field_set:OHPinfo.hmiConfigInfo.netOfflineWifiEnable)
}

// uint32 VLPREnable = 15;
inline void hmiConfigInfo::clear_vlprenable() {
  vlprenable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::_internal_vlprenable() const {
  return vlprenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 hmiConfigInfo::vlprenable() const {
  // @@protoc_insertion_point(field_get:OHPinfo.hmiConfigInfo.VLPREnable)
  return _internal_vlprenable();
}
inline void hmiConfigInfo::_internal_set_vlprenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  vlprenable_ = value;
}
inline void hmiConfigInfo::set_vlprenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_vlprenable(value);
  // @@protoc_insertion_point(field_set:OHPinfo.hmiConfigInfo.VLPREnable)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace OHPinfo

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::OHPinfo::OrderTypeEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::OHPinfo::OrderTypeEnum>() {
  return ::OHPinfo::OrderTypeEnum_descriptor();
}
template <> struct is_proto_enum< ::OHPinfo::OrderSubTypeEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::OHPinfo::OrderSubTypeEnum>() {
  return ::OHPinfo::OrderSubTypeEnum_descriptor();
}
template <> struct is_proto_enum< ::OHPinfo::OrderStateEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::OHPinfo::OrderStateEnum>() {
  return ::OHPinfo::OrderStateEnum_descriptor();
}
template <> struct is_proto_enum< ::OHPinfo::PipelineStateEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::OHPinfo::PipelineStateEnum>() {
  return ::OHPinfo::PipelineStateEnum_descriptor();
}
template <> struct is_proto_enum< ::OHPinfo::OrderStrategyEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::OHPinfo::OrderStrategyEnum>() {
  return ::OHPinfo::OrderStrategyEnum_descriptor();
}
template <> struct is_proto_enum< ::OHPinfo::OrderJurisdictionEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::OHPinfo::OrderJurisdictionEnum>() {
  return ::OHPinfo::OrderJurisdictionEnum_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_GCU_5fOHP_5fINFO_2eproto
