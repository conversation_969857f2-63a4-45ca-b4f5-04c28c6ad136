// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GCU_VCI_INFO.proto

#include "GCU_VCI_INFO.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace VCIinfo {
constexpr GunConnectState::GunConnectState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : terminalid_(0u)
  , gunid_(0u)
  , auxdriver_(0u)
  , auxfeedback_(0u)
  , elockdriver_(0u)
  , elockfeedback_(0u)
  , auxrttype_(0u)
  , temppositive_(0)
  , tempnegative_(0)
  , elockmode_(0u)
  , positionedstate_(0u)
  , dualstate_(0u)
  , linkstate_(0u)
  , gbtprotype_(0)
{}
struct GunConnectStateDefaultTypeInternal {
  constexpr GunConnectStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GunConnectStateDefaultTypeInternal() {}
  union {
    GunConnectState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GunConnectStateDefaultTypeInternal _GunConnectState_default_instance_;
constexpr ConnectStageSysCtrl::ConnectStageSysCtrl(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : terminalid_(0u)
  , gunid_(0u)
  , elockcmd_(0u)
  , starttype_(0u)
  , auxtype_(0u)
  , elockmode_(0u){}
struct ConnectStageSysCtrlDefaultTypeInternal {
  constexpr ConnectStageSysCtrlDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ConnectStageSysCtrlDefaultTypeInternal() {}
  union {
    ConnectStageSysCtrl _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ConnectStageSysCtrlDefaultTypeInternal _ConnectStageSysCtrl_default_instance_;
constexpr VCIsendBMSDemand::VCIsendBMSDemand(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : vdemand_(0)
  , idemand_(0)
  , vptpdemand_(0)
  , iptpdemand_(0)
  , batvoltage_(0)
  , modvoltage_(0){}
struct VCIsendBMSDemandDefaultTypeInternal {
  constexpr VCIsendBMSDemandDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~VCIsendBMSDemandDefaultTypeInternal() {}
  union {
    VCIsendBMSDemand _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT VCIsendBMSDemandDefaultTypeInternal _VCIsendBMSDemand_default_instance_;
constexpr ChargerRevive::ChargerRevive(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ischarging_(0u)
  , isvinstart_(0u)
  , faultstate1_(0u)
  , faultstate2_(0u)
  , faultstate3_(0u)
  , bmscommstate_(0u)
  , bmsrecvstate_(0u)
  , bmstype_(0u)
  , bmstimeoutcnt_(0u)
  , elockstate_(0u)
  , auxpowerstate_(0u)
  , insultstate_(0u)
  , insultresult_(0u)
  , bmscurrentmax_(0)
  , bmsvoltagemax_(0)
  , cellvoltagemax_(0)
  , celltempmax_(0)
  , insultvoltage_(0){}
struct ChargerReviveDefaultTypeInternal {
  constexpr ChargerReviveDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ChargerReviveDefaultTypeInternal() {}
  union {
    ChargerRevive _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ChargerReviveDefaultTypeInternal _ChargerRevive_default_instance_;
constexpr ContactorState::ContactorState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : gunnum_(0u)
  , contactorstate_(0)
{}
struct ContactorStateDefaultTypeInternal {
  constexpr ContactorStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ContactorStateDefaultTypeInternal() {}
  union {
    ContactorState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ContactorStateDefaultTypeInternal _ContactorState_default_instance_;
constexpr CurrModAllocState::CurrModAllocState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : gunnum_(0u)
  , mainsize_(0u){}
struct CurrModAllocStateDefaultTypeInternal {
  constexpr CurrModAllocStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CurrModAllocStateDefaultTypeInternal() {}
  union {
    CurrModAllocState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CurrModAllocStateDefaultTypeInternal _CurrModAllocState_default_instance_;
}  // namespace VCIinfo
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_GCU_5fVCI_5fINFO_2eproto[6];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_GCU_5fVCI_5fINFO_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_GCU_5fVCI_5fINFO_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_GCU_5fVCI_5fINFO_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::VCIinfo::GunConnectState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::VCIinfo::GunConnectState, terminalid_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::GunConnectState, gunid_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::GunConnectState, auxdriver_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::GunConnectState, auxfeedback_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::GunConnectState, elockdriver_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::GunConnectState, elockfeedback_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::GunConnectState, auxrttype_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::GunConnectState, temppositive_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::GunConnectState, tempnegative_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::GunConnectState, elockmode_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::GunConnectState, positionedstate_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::GunConnectState, dualstate_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::GunConnectState, linkstate_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::GunConnectState, gbtprotype_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ConnectStageSysCtrl, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ConnectStageSysCtrl, terminalid_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ConnectStageSysCtrl, gunid_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ConnectStageSysCtrl, elockcmd_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ConnectStageSysCtrl, starttype_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ConnectStageSysCtrl, auxtype_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ConnectStageSysCtrl, elockmode_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::VCIinfo::VCIsendBMSDemand, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::VCIinfo::VCIsendBMSDemand, vdemand_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::VCIsendBMSDemand, idemand_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::VCIsendBMSDemand, vptpdemand_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::VCIsendBMSDemand, iptpdemand_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::VCIsendBMSDemand, batvoltage_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::VCIsendBMSDemand, modvoltage_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ChargerRevive, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ChargerRevive, ischarging_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ChargerRevive, isvinstart_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ChargerRevive, faultstate1_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ChargerRevive, faultstate2_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ChargerRevive, faultstate3_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ChargerRevive, bmscommstate_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ChargerRevive, bmsrecvstate_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ChargerRevive, bmstype_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ChargerRevive, bmstimeoutcnt_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ChargerRevive, elockstate_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ChargerRevive, auxpowerstate_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ChargerRevive, insultstate_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ChargerRevive, insultresult_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ChargerRevive, bmscurrentmax_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ChargerRevive, bmsvoltagemax_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ChargerRevive, cellvoltagemax_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ChargerRevive, celltempmax_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ChargerRevive, insultvoltage_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ContactorState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ContactorState, gunnum_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::ContactorState, contactorstate_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::VCIinfo::CurrModAllocState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::VCIinfo::CurrModAllocState, gunnum_),
  PROTOBUF_FIELD_OFFSET(::VCIinfo::CurrModAllocState, mainsize_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::VCIinfo::GunConnectState)},
  { 19, -1, sizeof(::VCIinfo::ConnectStageSysCtrl)},
  { 30, -1, sizeof(::VCIinfo::VCIsendBMSDemand)},
  { 41, -1, sizeof(::VCIinfo::ChargerRevive)},
  { 64, -1, sizeof(::VCIinfo::ContactorState)},
  { 71, -1, sizeof(::VCIinfo::CurrModAllocState)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::VCIinfo::_GunConnectState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::VCIinfo::_ConnectStageSysCtrl_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::VCIinfo::_VCIsendBMSDemand_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::VCIinfo::_ChargerRevive_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::VCIinfo::_ContactorState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::VCIinfo::_CurrModAllocState_default_instance_),
};

const char descriptor_table_protodef_GCU_5fVCI_5fINFO_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\022GCU_VCI_INFO.proto\022\007VCIinfo\032\026GCU_AllFa"
  "ultEnum.proto\032\022GCU_BMS_INFO.proto\032\022GCU_D"
  "MC_INFO.proto\"\302\002\n\017GunConnectState\022\022\n\nter"
  "minalID\030\001 \001(\r\022\r\n\005gunID\030\002 \001(\r\022\021\n\tauxDrive"
  "r\030\003 \001(\r\022\023\n\013auxFeedback\030\004 \001(\r\022\023\n\013eLockDri"
  "ver\030\005 \001(\r\022\025\n\reLockFeedback\030\006 \001(\r\022\021\n\tauxR"
  "tType\030\007 \001(\r\022\024\n\014tempPositive\030\010 \001(\002\022\024\n\014tem"
  "pNegative\030\t \001(\002\022\021\n\tElockMode\030\n \001(\r\022\027\n\017po"
  "sitionedState\030\013 \001(\r\022\021\n\tdualState\030\014 \001(\r\022\021"
  "\n\tlinkState\030\r \001(\r\022\'\n\nGbtProType\030\016 \001(\0162\023."
  "DMCinfo.GunProEnum\"\201\001\n\023ConnectStageSysCt"
  "rl\022\022\n\nterminalID\030\001 \001(\r\022\r\n\005gunID\030\002 \001(\r\022\020\n"
  "\010elockCmd\030\003 \001(\r\022\021\n\tstartType\030\004 \001(\r\022\017\n\007Au"
  "xType\030\005 \001(\r\022\021\n\tElockMode\030\006 \001(\r\"\204\001\n\020VCIse"
  "ndBMSDemand\022\017\n\007VDemand\030\001 \001(\002\022\017\n\007IDemand\030"
  "\002 \001(\002\022\022\n\nVPTPDemand\030\003 \001(\002\022\022\n\nIPTPDemand\030"
  "\004 \001(\002\022\022\n\nbatVoltage\030\005 \001(\002\022\022\n\nmodVoltage\030"
  "\006 \001(\002\"\222\003\n\rChargerRevive\022\022\n\nisCharging\030\001 "
  "\001(\r\022\022\n\nisVINStart\030\002 \001(\r\022\023\n\013faultState1\030\003"
  " \001(\r\022\023\n\013faultState2\030\004 \001(\r\022\023\n\013faultState3"
  "\030\005 \001(\r\022\024\n\014bmsCommState\030\006 \001(\r\022\024\n\014bmsRecvS"
  "tate\030\007 \001(\r\022\017\n\007bmsType\030\010 \001(\r\022\025\n\rbmsTimeou"
  "tCnt\030\t \001(\r\022\022\n\nelockState\030\n \001(\r\022\025\n\rauxPow"
  "erState\030\013 \001(\r\022\023\n\013insultState\030\014 \001(\r\022\024\n\014in"
  "sultResult\030\r \001(\r\022\025\n\rbmsCurrentMax\030\016 \001(\002\022"
  "\025\n\rbmsVoltageMax\030\017 \001(\002\022\026\n\016cellVoltageMax"
  "\030\020 \001(\002\022\023\n\013cellTempMax\030\021 \001(\002\022\025\n\rinsultVol"
  "tage\030\022 \001(\002\"U\n\016ContactorState\022\016\n\006gunNum\030\001"
  " \001(\r\0223\n\016ContactorState\030\002 \001(\0162\033.VCIinfo.C"
  "ontactorStateEnum\"5\n\021CurrModAllocState\022\016"
  "\n\006gunNum\030\001 \001(\r\022\020\n\010MainSize\030\002 \001(\r*\205\001\n\022Con"
  "tactorStateEnum\022\n\n\006UnKown\020\000\022\020\n\014DriveFail"
  "ure\020\001\022\025\n\021ContactorAdhesion\020\002\022\022\n\016Contacto"
  "rReady\020\005\022\025\n\021ContactorCharging\020\006\022\017\n\013Aggre"
  "gation\020\007*\267\001\n\rgunMatchState\022\017\n\013UnKownStat"
  "e\020\000\022\021\n\rgunMatchStart\020\001\022\020\n\014OutputVoltOk\020\002"
  "\022\021\n\rVoltSampledOk\020\003\022\016\n\ngunMatchOk\020\004\022\020\n\014g"
  "unMatchFail\020\005\022\023\n\017gunMatchTimeout\020\006\022\021\n\rgu"
  "nMatchEndOk\020\007\022\023\n\017gunMatchEndFail\020\010P\000P\001P\002"
  "b\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_GCU_5fVCI_5fINFO_2eproto_deps[3] = {
  &::descriptor_table_GCU_5fAllFaultEnum_2eproto,
  &::descriptor_table_GCU_5fBMS_5fINFO_2eproto,
  &::descriptor_table_GCU_5fDMC_5fINFO_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_GCU_5fVCI_5fINFO_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GCU_5fVCI_5fINFO_2eproto = {
  false, false, 1568, descriptor_table_protodef_GCU_5fVCI_5fINFO_2eproto, "GCU_VCI_INFO.proto", 
  &descriptor_table_GCU_5fVCI_5fINFO_2eproto_once, descriptor_table_GCU_5fVCI_5fINFO_2eproto_deps, 3, 6,
  schemas, file_default_instances, TableStruct_GCU_5fVCI_5fINFO_2eproto::offsets,
  file_level_metadata_GCU_5fVCI_5fINFO_2eproto, file_level_enum_descriptors_GCU_5fVCI_5fINFO_2eproto, file_level_service_descriptors_GCU_5fVCI_5fINFO_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_GCU_5fVCI_5fINFO_2eproto_getter() {
  return &descriptor_table_GCU_5fVCI_5fINFO_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_GCU_5fVCI_5fINFO_2eproto(&descriptor_table_GCU_5fVCI_5fINFO_2eproto);
namespace VCIinfo {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ContactorStateEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fVCI_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fVCI_5fINFO_2eproto[0];
}
bool ContactorStateEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 5:
    case 6:
    case 7:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* gunMatchState_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fVCI_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fVCI_5fINFO_2eproto[1];
}
bool gunMatchState_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class GunConnectState::_Internal {
 public:
};

GunConnectState::GunConnectState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:VCIinfo.GunConnectState)
}
GunConnectState::GunConnectState(const GunConnectState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&terminalid_, &from.terminalid_,
    static_cast<size_t>(reinterpret_cast<char*>(&gbtprotype_) -
    reinterpret_cast<char*>(&terminalid_)) + sizeof(gbtprotype_));
  // @@protoc_insertion_point(copy_constructor:VCIinfo.GunConnectState)
}

inline void GunConnectState::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&terminalid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&gbtprotype_) -
    reinterpret_cast<char*>(&terminalid_)) + sizeof(gbtprotype_));
}

GunConnectState::~GunConnectState() {
  // @@protoc_insertion_point(destructor:VCIinfo.GunConnectState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GunConnectState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GunConnectState::ArenaDtor(void* object) {
  GunConnectState* _this = reinterpret_cast< GunConnectState* >(object);
  (void)_this;
}
void GunConnectState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GunConnectState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GunConnectState::Clear() {
// @@protoc_insertion_point(message_clear_start:VCIinfo.GunConnectState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&terminalid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&gbtprotype_) -
      reinterpret_cast<char*>(&terminalid_)) + sizeof(gbtprotype_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GunConnectState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 terminalID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          terminalid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 gunID = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          gunid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 auxDriver = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          auxdriver_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 auxFeedback = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          auxfeedback_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 eLockDriver = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          elockdriver_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 eLockFeedback = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          elockfeedback_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 auxRtType = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          auxrttype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float tempPositive = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 69)) {
          temppositive_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float tempNegative = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 77)) {
          tempnegative_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 ElockMode = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          elockmode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 positionedState = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          positionedstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 dualState = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 96)) {
          dualstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 linkState = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 104)) {
          linkstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DMCinfo.GunProEnum GbtProType = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 112)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_gbtprotype(static_cast<::DMCinfo::GunProEnum>(val));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* GunConnectState::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:VCIinfo.GunConnectState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 terminalID = 1;
  if (this->_internal_terminalid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_terminalid(), target);
  }

  // uint32 gunID = 2;
  if (this->_internal_gunid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_gunid(), target);
  }

  // uint32 auxDriver = 3;
  if (this->_internal_auxdriver() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_auxdriver(), target);
  }

  // uint32 auxFeedback = 4;
  if (this->_internal_auxfeedback() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_auxfeedback(), target);
  }

  // uint32 eLockDriver = 5;
  if (this->_internal_elockdriver() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_elockdriver(), target);
  }

  // uint32 eLockFeedback = 6;
  if (this->_internal_elockfeedback() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_elockfeedback(), target);
  }

  // uint32 auxRtType = 7;
  if (this->_internal_auxrttype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(7, this->_internal_auxrttype(), target);
  }

  // float tempPositive = 8;
  if (!(this->_internal_temppositive() <= 0 && this->_internal_temppositive() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(8, this->_internal_temppositive(), target);
  }

  // float tempNegative = 9;
  if (!(this->_internal_tempnegative() <= 0 && this->_internal_tempnegative() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(9, this->_internal_tempnegative(), target);
  }

  // uint32 ElockMode = 10;
  if (this->_internal_elockmode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(10, this->_internal_elockmode(), target);
  }

  // uint32 positionedState = 11;
  if (this->_internal_positionedstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(11, this->_internal_positionedstate(), target);
  }

  // uint32 dualState = 12;
  if (this->_internal_dualstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(12, this->_internal_dualstate(), target);
  }

  // uint32 linkState = 13;
  if (this->_internal_linkstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(13, this->_internal_linkstate(), target);
  }

  // .DMCinfo.GunProEnum GbtProType = 14;
  if (this->_internal_gbtprotype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      14, this->_internal_gbtprotype(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:VCIinfo.GunConnectState)
  return target;
}

size_t GunConnectState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:VCIinfo.GunConnectState)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 terminalID = 1;
  if (this->_internal_terminalid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_terminalid());
  }

  // uint32 gunID = 2;
  if (this->_internal_gunid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_gunid());
  }

  // uint32 auxDriver = 3;
  if (this->_internal_auxdriver() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_auxdriver());
  }

  // uint32 auxFeedback = 4;
  if (this->_internal_auxfeedback() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_auxfeedback());
  }

  // uint32 eLockDriver = 5;
  if (this->_internal_elockdriver() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_elockdriver());
  }

  // uint32 eLockFeedback = 6;
  if (this->_internal_elockfeedback() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_elockfeedback());
  }

  // uint32 auxRtType = 7;
  if (this->_internal_auxrttype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_auxrttype());
  }

  // float tempPositive = 8;
  if (!(this->_internal_temppositive() <= 0 && this->_internal_temppositive() >= 0)) {
    total_size += 1 + 4;
  }

  // float tempNegative = 9;
  if (!(this->_internal_tempnegative() <= 0 && this->_internal_tempnegative() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 ElockMode = 10;
  if (this->_internal_elockmode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_elockmode());
  }

  // uint32 positionedState = 11;
  if (this->_internal_positionedstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_positionedstate());
  }

  // uint32 dualState = 12;
  if (this->_internal_dualstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_dualstate());
  }

  // uint32 linkState = 13;
  if (this->_internal_linkstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_linkstate());
  }

  // .DMCinfo.GunProEnum GbtProType = 14;
  if (this->_internal_gbtprotype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_gbtprotype());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GunConnectState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GunConnectState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GunConnectState::GetClassData() const { return &_class_data_; }

void GunConnectState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<GunConnectState *>(to)->MergeFrom(
      static_cast<const GunConnectState &>(from));
}


void GunConnectState::MergeFrom(const GunConnectState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:VCIinfo.GunConnectState)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_terminalid() != 0) {
    _internal_set_terminalid(from._internal_terminalid());
  }
  if (from._internal_gunid() != 0) {
    _internal_set_gunid(from._internal_gunid());
  }
  if (from._internal_auxdriver() != 0) {
    _internal_set_auxdriver(from._internal_auxdriver());
  }
  if (from._internal_auxfeedback() != 0) {
    _internal_set_auxfeedback(from._internal_auxfeedback());
  }
  if (from._internal_elockdriver() != 0) {
    _internal_set_elockdriver(from._internal_elockdriver());
  }
  if (from._internal_elockfeedback() != 0) {
    _internal_set_elockfeedback(from._internal_elockfeedback());
  }
  if (from._internal_auxrttype() != 0) {
    _internal_set_auxrttype(from._internal_auxrttype());
  }
  if (!(from._internal_temppositive() <= 0 && from._internal_temppositive() >= 0)) {
    _internal_set_temppositive(from._internal_temppositive());
  }
  if (!(from._internal_tempnegative() <= 0 && from._internal_tempnegative() >= 0)) {
    _internal_set_tempnegative(from._internal_tempnegative());
  }
  if (from._internal_elockmode() != 0) {
    _internal_set_elockmode(from._internal_elockmode());
  }
  if (from._internal_positionedstate() != 0) {
    _internal_set_positionedstate(from._internal_positionedstate());
  }
  if (from._internal_dualstate() != 0) {
    _internal_set_dualstate(from._internal_dualstate());
  }
  if (from._internal_linkstate() != 0) {
    _internal_set_linkstate(from._internal_linkstate());
  }
  if (from._internal_gbtprotype() != 0) {
    _internal_set_gbtprotype(from._internal_gbtprotype());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GunConnectState::CopyFrom(const GunConnectState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:VCIinfo.GunConnectState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GunConnectState::IsInitialized() const {
  return true;
}

void GunConnectState::InternalSwap(GunConnectState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GunConnectState, gbtprotype_)
      + sizeof(GunConnectState::gbtprotype_)
      - PROTOBUF_FIELD_OFFSET(GunConnectState, terminalid_)>(
          reinterpret_cast<char*>(&terminalid_),
          reinterpret_cast<char*>(&other->terminalid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GunConnectState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fVCI_5fINFO_2eproto_getter, &descriptor_table_GCU_5fVCI_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fVCI_5fINFO_2eproto[0]);
}

// ===================================================================

class ConnectStageSysCtrl::_Internal {
 public:
};

ConnectStageSysCtrl::ConnectStageSysCtrl(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:VCIinfo.ConnectStageSysCtrl)
}
ConnectStageSysCtrl::ConnectStageSysCtrl(const ConnectStageSysCtrl& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&terminalid_, &from.terminalid_,
    static_cast<size_t>(reinterpret_cast<char*>(&elockmode_) -
    reinterpret_cast<char*>(&terminalid_)) + sizeof(elockmode_));
  // @@protoc_insertion_point(copy_constructor:VCIinfo.ConnectStageSysCtrl)
}

inline void ConnectStageSysCtrl::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&terminalid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&elockmode_) -
    reinterpret_cast<char*>(&terminalid_)) + sizeof(elockmode_));
}

ConnectStageSysCtrl::~ConnectStageSysCtrl() {
  // @@protoc_insertion_point(destructor:VCIinfo.ConnectStageSysCtrl)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ConnectStageSysCtrl::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void ConnectStageSysCtrl::ArenaDtor(void* object) {
  ConnectStageSysCtrl* _this = reinterpret_cast< ConnectStageSysCtrl* >(object);
  (void)_this;
}
void ConnectStageSysCtrl::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ConnectStageSysCtrl::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ConnectStageSysCtrl::Clear() {
// @@protoc_insertion_point(message_clear_start:VCIinfo.ConnectStageSysCtrl)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&terminalid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&elockmode_) -
      reinterpret_cast<char*>(&terminalid_)) + sizeof(elockmode_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ConnectStageSysCtrl::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 terminalID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          terminalid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 gunID = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          gunid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 elockCmd = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          elockcmd_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 startType = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          starttype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 AuxType = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          auxtype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 ElockMode = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          elockmode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ConnectStageSysCtrl::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:VCIinfo.ConnectStageSysCtrl)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 terminalID = 1;
  if (this->_internal_terminalid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_terminalid(), target);
  }

  // uint32 gunID = 2;
  if (this->_internal_gunid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_gunid(), target);
  }

  // uint32 elockCmd = 3;
  if (this->_internal_elockcmd() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_elockcmd(), target);
  }

  // uint32 startType = 4;
  if (this->_internal_starttype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_starttype(), target);
  }

  // uint32 AuxType = 5;
  if (this->_internal_auxtype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_auxtype(), target);
  }

  // uint32 ElockMode = 6;
  if (this->_internal_elockmode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_elockmode(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:VCIinfo.ConnectStageSysCtrl)
  return target;
}

size_t ConnectStageSysCtrl::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:VCIinfo.ConnectStageSysCtrl)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 terminalID = 1;
  if (this->_internal_terminalid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_terminalid());
  }

  // uint32 gunID = 2;
  if (this->_internal_gunid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_gunid());
  }

  // uint32 elockCmd = 3;
  if (this->_internal_elockcmd() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_elockcmd());
  }

  // uint32 startType = 4;
  if (this->_internal_starttype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_starttype());
  }

  // uint32 AuxType = 5;
  if (this->_internal_auxtype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_auxtype());
  }

  // uint32 ElockMode = 6;
  if (this->_internal_elockmode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_elockmode());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ConnectStageSysCtrl::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ConnectStageSysCtrl::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ConnectStageSysCtrl::GetClassData() const { return &_class_data_; }

void ConnectStageSysCtrl::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<ConnectStageSysCtrl *>(to)->MergeFrom(
      static_cast<const ConnectStageSysCtrl &>(from));
}


void ConnectStageSysCtrl::MergeFrom(const ConnectStageSysCtrl& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:VCIinfo.ConnectStageSysCtrl)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_terminalid() != 0) {
    _internal_set_terminalid(from._internal_terminalid());
  }
  if (from._internal_gunid() != 0) {
    _internal_set_gunid(from._internal_gunid());
  }
  if (from._internal_elockcmd() != 0) {
    _internal_set_elockcmd(from._internal_elockcmd());
  }
  if (from._internal_starttype() != 0) {
    _internal_set_starttype(from._internal_starttype());
  }
  if (from._internal_auxtype() != 0) {
    _internal_set_auxtype(from._internal_auxtype());
  }
  if (from._internal_elockmode() != 0) {
    _internal_set_elockmode(from._internal_elockmode());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ConnectStageSysCtrl::CopyFrom(const ConnectStageSysCtrl& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:VCIinfo.ConnectStageSysCtrl)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ConnectStageSysCtrl::IsInitialized() const {
  return true;
}

void ConnectStageSysCtrl::InternalSwap(ConnectStageSysCtrl* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ConnectStageSysCtrl, elockmode_)
      + sizeof(ConnectStageSysCtrl::elockmode_)
      - PROTOBUF_FIELD_OFFSET(ConnectStageSysCtrl, terminalid_)>(
          reinterpret_cast<char*>(&terminalid_),
          reinterpret_cast<char*>(&other->terminalid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ConnectStageSysCtrl::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fVCI_5fINFO_2eproto_getter, &descriptor_table_GCU_5fVCI_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fVCI_5fINFO_2eproto[1]);
}

// ===================================================================

class VCIsendBMSDemand::_Internal {
 public:
};

VCIsendBMSDemand::VCIsendBMSDemand(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:VCIinfo.VCIsendBMSDemand)
}
VCIsendBMSDemand::VCIsendBMSDemand(const VCIsendBMSDemand& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&vdemand_, &from.vdemand_,
    static_cast<size_t>(reinterpret_cast<char*>(&modvoltage_) -
    reinterpret_cast<char*>(&vdemand_)) + sizeof(modvoltage_));
  // @@protoc_insertion_point(copy_constructor:VCIinfo.VCIsendBMSDemand)
}

inline void VCIsendBMSDemand::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&vdemand_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&modvoltage_) -
    reinterpret_cast<char*>(&vdemand_)) + sizeof(modvoltage_));
}

VCIsendBMSDemand::~VCIsendBMSDemand() {
  // @@protoc_insertion_point(destructor:VCIinfo.VCIsendBMSDemand)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void VCIsendBMSDemand::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void VCIsendBMSDemand::ArenaDtor(void* object) {
  VCIsendBMSDemand* _this = reinterpret_cast< VCIsendBMSDemand* >(object);
  (void)_this;
}
void VCIsendBMSDemand::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void VCIsendBMSDemand::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void VCIsendBMSDemand::Clear() {
// @@protoc_insertion_point(message_clear_start:VCIinfo.VCIsendBMSDemand)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&vdemand_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&modvoltage_) -
      reinterpret_cast<char*>(&vdemand_)) + sizeof(modvoltage_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* VCIsendBMSDemand::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float VDemand = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          vdemand_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float IDemand = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 21)) {
          idemand_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float VPTPDemand = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          vptpdemand_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float IPTPDemand = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 37)) {
          iptpdemand_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float batVoltage = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 45)) {
          batvoltage_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float modVoltage = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 53)) {
          modvoltage_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* VCIsendBMSDemand::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:VCIinfo.VCIsendBMSDemand)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float VDemand = 1;
  if (!(this->_internal_vdemand() <= 0 && this->_internal_vdemand() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_vdemand(), target);
  }

  // float IDemand = 2;
  if (!(this->_internal_idemand() <= 0 && this->_internal_idemand() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_idemand(), target);
  }

  // float VPTPDemand = 3;
  if (!(this->_internal_vptpdemand() <= 0 && this->_internal_vptpdemand() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_vptpdemand(), target);
  }

  // float IPTPDemand = 4;
  if (!(this->_internal_iptpdemand() <= 0 && this->_internal_iptpdemand() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_iptpdemand(), target);
  }

  // float batVoltage = 5;
  if (!(this->_internal_batvoltage() <= 0 && this->_internal_batvoltage() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(5, this->_internal_batvoltage(), target);
  }

  // float modVoltage = 6;
  if (!(this->_internal_modvoltage() <= 0 && this->_internal_modvoltage() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(6, this->_internal_modvoltage(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:VCIinfo.VCIsendBMSDemand)
  return target;
}

size_t VCIsendBMSDemand::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:VCIinfo.VCIsendBMSDemand)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float VDemand = 1;
  if (!(this->_internal_vdemand() <= 0 && this->_internal_vdemand() >= 0)) {
    total_size += 1 + 4;
  }

  // float IDemand = 2;
  if (!(this->_internal_idemand() <= 0 && this->_internal_idemand() >= 0)) {
    total_size += 1 + 4;
  }

  // float VPTPDemand = 3;
  if (!(this->_internal_vptpdemand() <= 0 && this->_internal_vptpdemand() >= 0)) {
    total_size += 1 + 4;
  }

  // float IPTPDemand = 4;
  if (!(this->_internal_iptpdemand() <= 0 && this->_internal_iptpdemand() >= 0)) {
    total_size += 1 + 4;
  }

  // float batVoltage = 5;
  if (!(this->_internal_batvoltage() <= 0 && this->_internal_batvoltage() >= 0)) {
    total_size += 1 + 4;
  }

  // float modVoltage = 6;
  if (!(this->_internal_modvoltage() <= 0 && this->_internal_modvoltage() >= 0)) {
    total_size += 1 + 4;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData VCIsendBMSDemand::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    VCIsendBMSDemand::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*VCIsendBMSDemand::GetClassData() const { return &_class_data_; }

void VCIsendBMSDemand::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<VCIsendBMSDemand *>(to)->MergeFrom(
      static_cast<const VCIsendBMSDemand &>(from));
}


void VCIsendBMSDemand::MergeFrom(const VCIsendBMSDemand& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:VCIinfo.VCIsendBMSDemand)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from._internal_vdemand() <= 0 && from._internal_vdemand() >= 0)) {
    _internal_set_vdemand(from._internal_vdemand());
  }
  if (!(from._internal_idemand() <= 0 && from._internal_idemand() >= 0)) {
    _internal_set_idemand(from._internal_idemand());
  }
  if (!(from._internal_vptpdemand() <= 0 && from._internal_vptpdemand() >= 0)) {
    _internal_set_vptpdemand(from._internal_vptpdemand());
  }
  if (!(from._internal_iptpdemand() <= 0 && from._internal_iptpdemand() >= 0)) {
    _internal_set_iptpdemand(from._internal_iptpdemand());
  }
  if (!(from._internal_batvoltage() <= 0 && from._internal_batvoltage() >= 0)) {
    _internal_set_batvoltage(from._internal_batvoltage());
  }
  if (!(from._internal_modvoltage() <= 0 && from._internal_modvoltage() >= 0)) {
    _internal_set_modvoltage(from._internal_modvoltage());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void VCIsendBMSDemand::CopyFrom(const VCIsendBMSDemand& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:VCIinfo.VCIsendBMSDemand)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VCIsendBMSDemand::IsInitialized() const {
  return true;
}

void VCIsendBMSDemand::InternalSwap(VCIsendBMSDemand* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(VCIsendBMSDemand, modvoltage_)
      + sizeof(VCIsendBMSDemand::modvoltage_)
      - PROTOBUF_FIELD_OFFSET(VCIsendBMSDemand, vdemand_)>(
          reinterpret_cast<char*>(&vdemand_),
          reinterpret_cast<char*>(&other->vdemand_));
}

::PROTOBUF_NAMESPACE_ID::Metadata VCIsendBMSDemand::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fVCI_5fINFO_2eproto_getter, &descriptor_table_GCU_5fVCI_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fVCI_5fINFO_2eproto[2]);
}

// ===================================================================

class ChargerRevive::_Internal {
 public:
};

ChargerRevive::ChargerRevive(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:VCIinfo.ChargerRevive)
}
ChargerRevive::ChargerRevive(const ChargerRevive& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&ischarging_, &from.ischarging_,
    static_cast<size_t>(reinterpret_cast<char*>(&insultvoltage_) -
    reinterpret_cast<char*>(&ischarging_)) + sizeof(insultvoltage_));
  // @@protoc_insertion_point(copy_constructor:VCIinfo.ChargerRevive)
}

inline void ChargerRevive::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ischarging_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&insultvoltage_) -
    reinterpret_cast<char*>(&ischarging_)) + sizeof(insultvoltage_));
}

ChargerRevive::~ChargerRevive() {
  // @@protoc_insertion_point(destructor:VCIinfo.ChargerRevive)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ChargerRevive::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void ChargerRevive::ArenaDtor(void* object) {
  ChargerRevive* _this = reinterpret_cast< ChargerRevive* >(object);
  (void)_this;
}
void ChargerRevive::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ChargerRevive::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ChargerRevive::Clear() {
// @@protoc_insertion_point(message_clear_start:VCIinfo.ChargerRevive)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&ischarging_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&insultvoltage_) -
      reinterpret_cast<char*>(&ischarging_)) + sizeof(insultvoltage_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ChargerRevive::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 isCharging = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ischarging_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 isVINStart = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          isvinstart_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 faultState1 = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          faultstate1_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 faultState2 = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          faultstate2_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 faultState3 = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          faultstate3_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 bmsCommState = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          bmscommstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 bmsRecvState = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          bmsrecvstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 bmsType = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          bmstype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 bmsTimeoutCnt = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72)) {
          bmstimeoutcnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 elockState = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          elockstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 auxPowerState = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          auxpowerstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 insultState = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 96)) {
          insultstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 insultResult = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 104)) {
          insultresult_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float bmsCurrentMax = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 117)) {
          bmscurrentmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float bmsVoltageMax = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 125)) {
          bmsvoltagemax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float cellVoltageMax = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 133)) {
          cellvoltagemax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float cellTempMax = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 141)) {
          celltempmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float insultVoltage = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 149)) {
          insultvoltage_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ChargerRevive::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:VCIinfo.ChargerRevive)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 isCharging = 1;
  if (this->_internal_ischarging() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_ischarging(), target);
  }

  // uint32 isVINStart = 2;
  if (this->_internal_isvinstart() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_isvinstart(), target);
  }

  // uint32 faultState1 = 3;
  if (this->_internal_faultstate1() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_faultstate1(), target);
  }

  // uint32 faultState2 = 4;
  if (this->_internal_faultstate2() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_faultstate2(), target);
  }

  // uint32 faultState3 = 5;
  if (this->_internal_faultstate3() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_faultstate3(), target);
  }

  // uint32 bmsCommState = 6;
  if (this->_internal_bmscommstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_bmscommstate(), target);
  }

  // uint32 bmsRecvState = 7;
  if (this->_internal_bmsrecvstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(7, this->_internal_bmsrecvstate(), target);
  }

  // uint32 bmsType = 8;
  if (this->_internal_bmstype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_bmstype(), target);
  }

  // uint32 bmsTimeoutCnt = 9;
  if (this->_internal_bmstimeoutcnt() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(9, this->_internal_bmstimeoutcnt(), target);
  }

  // uint32 elockState = 10;
  if (this->_internal_elockstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(10, this->_internal_elockstate(), target);
  }

  // uint32 auxPowerState = 11;
  if (this->_internal_auxpowerstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(11, this->_internal_auxpowerstate(), target);
  }

  // uint32 insultState = 12;
  if (this->_internal_insultstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(12, this->_internal_insultstate(), target);
  }

  // uint32 insultResult = 13;
  if (this->_internal_insultresult() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(13, this->_internal_insultresult(), target);
  }

  // float bmsCurrentMax = 14;
  if (!(this->_internal_bmscurrentmax() <= 0 && this->_internal_bmscurrentmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(14, this->_internal_bmscurrentmax(), target);
  }

  // float bmsVoltageMax = 15;
  if (!(this->_internal_bmsvoltagemax() <= 0 && this->_internal_bmsvoltagemax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(15, this->_internal_bmsvoltagemax(), target);
  }

  // float cellVoltageMax = 16;
  if (!(this->_internal_cellvoltagemax() <= 0 && this->_internal_cellvoltagemax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(16, this->_internal_cellvoltagemax(), target);
  }

  // float cellTempMax = 17;
  if (!(this->_internal_celltempmax() <= 0 && this->_internal_celltempmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(17, this->_internal_celltempmax(), target);
  }

  // float insultVoltage = 18;
  if (!(this->_internal_insultvoltage() <= 0 && this->_internal_insultvoltage() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(18, this->_internal_insultvoltage(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:VCIinfo.ChargerRevive)
  return target;
}

size_t ChargerRevive::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:VCIinfo.ChargerRevive)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 isCharging = 1;
  if (this->_internal_ischarging() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_ischarging());
  }

  // uint32 isVINStart = 2;
  if (this->_internal_isvinstart() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_isvinstart());
  }

  // uint32 faultState1 = 3;
  if (this->_internal_faultstate1() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_faultstate1());
  }

  // uint32 faultState2 = 4;
  if (this->_internal_faultstate2() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_faultstate2());
  }

  // uint32 faultState3 = 5;
  if (this->_internal_faultstate3() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_faultstate3());
  }

  // uint32 bmsCommState = 6;
  if (this->_internal_bmscommstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmscommstate());
  }

  // uint32 bmsRecvState = 7;
  if (this->_internal_bmsrecvstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsrecvstate());
  }

  // uint32 bmsType = 8;
  if (this->_internal_bmstype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmstype());
  }

  // uint32 bmsTimeoutCnt = 9;
  if (this->_internal_bmstimeoutcnt() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmstimeoutcnt());
  }

  // uint32 elockState = 10;
  if (this->_internal_elockstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_elockstate());
  }

  // uint32 auxPowerState = 11;
  if (this->_internal_auxpowerstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_auxpowerstate());
  }

  // uint32 insultState = 12;
  if (this->_internal_insultstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_insultstate());
  }

  // uint32 insultResult = 13;
  if (this->_internal_insultresult() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_insultresult());
  }

  // float bmsCurrentMax = 14;
  if (!(this->_internal_bmscurrentmax() <= 0 && this->_internal_bmscurrentmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float bmsVoltageMax = 15;
  if (!(this->_internal_bmsvoltagemax() <= 0 && this->_internal_bmsvoltagemax() >= 0)) {
    total_size += 1 + 4;
  }

  // float cellVoltageMax = 16;
  if (!(this->_internal_cellvoltagemax() <= 0 && this->_internal_cellvoltagemax() >= 0)) {
    total_size += 2 + 4;
  }

  // float cellTempMax = 17;
  if (!(this->_internal_celltempmax() <= 0 && this->_internal_celltempmax() >= 0)) {
    total_size += 2 + 4;
  }

  // float insultVoltage = 18;
  if (!(this->_internal_insultvoltage() <= 0 && this->_internal_insultvoltage() >= 0)) {
    total_size += 2 + 4;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ChargerRevive::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ChargerRevive::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ChargerRevive::GetClassData() const { return &_class_data_; }

void ChargerRevive::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<ChargerRevive *>(to)->MergeFrom(
      static_cast<const ChargerRevive &>(from));
}


void ChargerRevive::MergeFrom(const ChargerRevive& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:VCIinfo.ChargerRevive)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_ischarging() != 0) {
    _internal_set_ischarging(from._internal_ischarging());
  }
  if (from._internal_isvinstart() != 0) {
    _internal_set_isvinstart(from._internal_isvinstart());
  }
  if (from._internal_faultstate1() != 0) {
    _internal_set_faultstate1(from._internal_faultstate1());
  }
  if (from._internal_faultstate2() != 0) {
    _internal_set_faultstate2(from._internal_faultstate2());
  }
  if (from._internal_faultstate3() != 0) {
    _internal_set_faultstate3(from._internal_faultstate3());
  }
  if (from._internal_bmscommstate() != 0) {
    _internal_set_bmscommstate(from._internal_bmscommstate());
  }
  if (from._internal_bmsrecvstate() != 0) {
    _internal_set_bmsrecvstate(from._internal_bmsrecvstate());
  }
  if (from._internal_bmstype() != 0) {
    _internal_set_bmstype(from._internal_bmstype());
  }
  if (from._internal_bmstimeoutcnt() != 0) {
    _internal_set_bmstimeoutcnt(from._internal_bmstimeoutcnt());
  }
  if (from._internal_elockstate() != 0) {
    _internal_set_elockstate(from._internal_elockstate());
  }
  if (from._internal_auxpowerstate() != 0) {
    _internal_set_auxpowerstate(from._internal_auxpowerstate());
  }
  if (from._internal_insultstate() != 0) {
    _internal_set_insultstate(from._internal_insultstate());
  }
  if (from._internal_insultresult() != 0) {
    _internal_set_insultresult(from._internal_insultresult());
  }
  if (!(from._internal_bmscurrentmax() <= 0 && from._internal_bmscurrentmax() >= 0)) {
    _internal_set_bmscurrentmax(from._internal_bmscurrentmax());
  }
  if (!(from._internal_bmsvoltagemax() <= 0 && from._internal_bmsvoltagemax() >= 0)) {
    _internal_set_bmsvoltagemax(from._internal_bmsvoltagemax());
  }
  if (!(from._internal_cellvoltagemax() <= 0 && from._internal_cellvoltagemax() >= 0)) {
    _internal_set_cellvoltagemax(from._internal_cellvoltagemax());
  }
  if (!(from._internal_celltempmax() <= 0 && from._internal_celltempmax() >= 0)) {
    _internal_set_celltempmax(from._internal_celltempmax());
  }
  if (!(from._internal_insultvoltage() <= 0 && from._internal_insultvoltage() >= 0)) {
    _internal_set_insultvoltage(from._internal_insultvoltage());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ChargerRevive::CopyFrom(const ChargerRevive& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:VCIinfo.ChargerRevive)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ChargerRevive::IsInitialized() const {
  return true;
}

void ChargerRevive::InternalSwap(ChargerRevive* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ChargerRevive, insultvoltage_)
      + sizeof(ChargerRevive::insultvoltage_)
      - PROTOBUF_FIELD_OFFSET(ChargerRevive, ischarging_)>(
          reinterpret_cast<char*>(&ischarging_),
          reinterpret_cast<char*>(&other->ischarging_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ChargerRevive::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fVCI_5fINFO_2eproto_getter, &descriptor_table_GCU_5fVCI_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fVCI_5fINFO_2eproto[3]);
}

// ===================================================================

class ContactorState::_Internal {
 public:
};

ContactorState::ContactorState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:VCIinfo.ContactorState)
}
ContactorState::ContactorState(const ContactorState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&gunnum_, &from.gunnum_,
    static_cast<size_t>(reinterpret_cast<char*>(&contactorstate_) -
    reinterpret_cast<char*>(&gunnum_)) + sizeof(contactorstate_));
  // @@protoc_insertion_point(copy_constructor:VCIinfo.ContactorState)
}

inline void ContactorState::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&gunnum_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&contactorstate_) -
    reinterpret_cast<char*>(&gunnum_)) + sizeof(contactorstate_));
}

ContactorState::~ContactorState() {
  // @@protoc_insertion_point(destructor:VCIinfo.ContactorState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ContactorState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void ContactorState::ArenaDtor(void* object) {
  ContactorState* _this = reinterpret_cast< ContactorState* >(object);
  (void)_this;
}
void ContactorState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ContactorState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ContactorState::Clear() {
// @@protoc_insertion_point(message_clear_start:VCIinfo.ContactorState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&gunnum_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&contactorstate_) -
      reinterpret_cast<char*>(&gunnum_)) + sizeof(contactorstate_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ContactorState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 gunNum = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          gunnum_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .VCIinfo.ContactorStateEnum ContactorState = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_contactorstate(static_cast<::VCIinfo::ContactorStateEnum>(val));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ContactorState::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:VCIinfo.ContactorState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 gunNum = 1;
  if (this->_internal_gunnum() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_gunnum(), target);
  }

  // .VCIinfo.ContactorStateEnum ContactorState = 2;
  if (this->_internal_contactorstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_contactorstate(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:VCIinfo.ContactorState)
  return target;
}

size_t ContactorState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:VCIinfo.ContactorState)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 gunNum = 1;
  if (this->_internal_gunnum() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_gunnum());
  }

  // .VCIinfo.ContactorStateEnum ContactorState = 2;
  if (this->_internal_contactorstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_contactorstate());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ContactorState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ContactorState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ContactorState::GetClassData() const { return &_class_data_; }

void ContactorState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<ContactorState *>(to)->MergeFrom(
      static_cast<const ContactorState &>(from));
}


void ContactorState::MergeFrom(const ContactorState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:VCIinfo.ContactorState)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_gunnum() != 0) {
    _internal_set_gunnum(from._internal_gunnum());
  }
  if (from._internal_contactorstate() != 0) {
    _internal_set_contactorstate(from._internal_contactorstate());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ContactorState::CopyFrom(const ContactorState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:VCIinfo.ContactorState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ContactorState::IsInitialized() const {
  return true;
}

void ContactorState::InternalSwap(ContactorState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ContactorState, contactorstate_)
      + sizeof(ContactorState::contactorstate_)
      - PROTOBUF_FIELD_OFFSET(ContactorState, gunnum_)>(
          reinterpret_cast<char*>(&gunnum_),
          reinterpret_cast<char*>(&other->gunnum_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ContactorState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fVCI_5fINFO_2eproto_getter, &descriptor_table_GCU_5fVCI_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fVCI_5fINFO_2eproto[4]);
}

// ===================================================================

class CurrModAllocState::_Internal {
 public:
};

CurrModAllocState::CurrModAllocState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:VCIinfo.CurrModAllocState)
}
CurrModAllocState::CurrModAllocState(const CurrModAllocState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&gunnum_, &from.gunnum_,
    static_cast<size_t>(reinterpret_cast<char*>(&mainsize_) -
    reinterpret_cast<char*>(&gunnum_)) + sizeof(mainsize_));
  // @@protoc_insertion_point(copy_constructor:VCIinfo.CurrModAllocState)
}

inline void CurrModAllocState::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&gunnum_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&mainsize_) -
    reinterpret_cast<char*>(&gunnum_)) + sizeof(mainsize_));
}

CurrModAllocState::~CurrModAllocState() {
  // @@protoc_insertion_point(destructor:VCIinfo.CurrModAllocState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CurrModAllocState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void CurrModAllocState::ArenaDtor(void* object) {
  CurrModAllocState* _this = reinterpret_cast< CurrModAllocState* >(object);
  (void)_this;
}
void CurrModAllocState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CurrModAllocState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CurrModAllocState::Clear() {
// @@protoc_insertion_point(message_clear_start:VCIinfo.CurrModAllocState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&gunnum_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&mainsize_) -
      reinterpret_cast<char*>(&gunnum_)) + sizeof(mainsize_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CurrModAllocState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 gunNum = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          gunnum_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 MainSize = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          mainsize_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* CurrModAllocState::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:VCIinfo.CurrModAllocState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 gunNum = 1;
  if (this->_internal_gunnum() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_gunnum(), target);
  }

  // uint32 MainSize = 2;
  if (this->_internal_mainsize() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_mainsize(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:VCIinfo.CurrModAllocState)
  return target;
}

size_t CurrModAllocState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:VCIinfo.CurrModAllocState)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 gunNum = 1;
  if (this->_internal_gunnum() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_gunnum());
  }

  // uint32 MainSize = 2;
  if (this->_internal_mainsize() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_mainsize());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CurrModAllocState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CurrModAllocState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CurrModAllocState::GetClassData() const { return &_class_data_; }

void CurrModAllocState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<CurrModAllocState *>(to)->MergeFrom(
      static_cast<const CurrModAllocState &>(from));
}


void CurrModAllocState::MergeFrom(const CurrModAllocState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:VCIinfo.CurrModAllocState)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_gunnum() != 0) {
    _internal_set_gunnum(from._internal_gunnum());
  }
  if (from._internal_mainsize() != 0) {
    _internal_set_mainsize(from._internal_mainsize());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CurrModAllocState::CopyFrom(const CurrModAllocState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:VCIinfo.CurrModAllocState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CurrModAllocState::IsInitialized() const {
  return true;
}

void CurrModAllocState::InternalSwap(CurrModAllocState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CurrModAllocState, mainsize_)
      + sizeof(CurrModAllocState::mainsize_)
      - PROTOBUF_FIELD_OFFSET(CurrModAllocState, gunnum_)>(
          reinterpret_cast<char*>(&gunnum_),
          reinterpret_cast<char*>(&other->gunnum_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CurrModAllocState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fVCI_5fINFO_2eproto_getter, &descriptor_table_GCU_5fVCI_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fVCI_5fINFO_2eproto[5]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace VCIinfo
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::VCIinfo::GunConnectState* Arena::CreateMaybeMessage< ::VCIinfo::GunConnectState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::VCIinfo::GunConnectState >(arena);
}
template<> PROTOBUF_NOINLINE ::VCIinfo::ConnectStageSysCtrl* Arena::CreateMaybeMessage< ::VCIinfo::ConnectStageSysCtrl >(Arena* arena) {
  return Arena::CreateMessageInternal< ::VCIinfo::ConnectStageSysCtrl >(arena);
}
template<> PROTOBUF_NOINLINE ::VCIinfo::VCIsendBMSDemand* Arena::CreateMaybeMessage< ::VCIinfo::VCIsendBMSDemand >(Arena* arena) {
  return Arena::CreateMessageInternal< ::VCIinfo::VCIsendBMSDemand >(arena);
}
template<> PROTOBUF_NOINLINE ::VCIinfo::ChargerRevive* Arena::CreateMaybeMessage< ::VCIinfo::ChargerRevive >(Arena* arena) {
  return Arena::CreateMessageInternal< ::VCIinfo::ChargerRevive >(arena);
}
template<> PROTOBUF_NOINLINE ::VCIinfo::ContactorState* Arena::CreateMaybeMessage< ::VCIinfo::ContactorState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::VCIinfo::ContactorState >(arena);
}
template<> PROTOBUF_NOINLINE ::VCIinfo::CurrModAllocState* Arena::CreateMaybeMessage< ::VCIinfo::CurrModAllocState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::VCIinfo::CurrModAllocState >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
