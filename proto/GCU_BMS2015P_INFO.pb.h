// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GCU_BMS2015P_INFO.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_GCU_5fBMS2015P_5fINFO_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_GCU_5fBMS2015P_5fINFO_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3017000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3017003 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_GCU_5fBMS2015P_5fINFO_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_GCU_5fBMS2015P_5fINFO_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[12]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GCU_5fBMS2015P_5fINFO_2eproto;
namespace BMS2015PlusInfo {
class AuthenMsg;
struct AuthenMsgDefaultTypeInternal;
extern AuthenMsgDefaultTypeInternal _AuthenMsg_default_instance_;
class BMSCharging;
struct BMSChargingDefaultTypeInternal;
extern BMSChargingDefaultTypeInternal _BMSCharging_default_instance_;
class BMSChargingEnd;
struct BMSChargingEndDefaultTypeInternal;
extern BMSChargingEndDefaultTypeInternal _BMSChargingEnd_default_instance_;
class BMSConfig;
struct BMSConfigDefaultTypeInternal;
extern BMSConfigDefaultTypeInternal _BMSConfig_default_instance_;
class DischargeConfig;
struct DischargeConfigDefaultTypeInternal;
extern DischargeConfigDefaultTypeInternal _DischargeConfig_default_instance_;
class FunConferMsg;
struct FunConferMsgDefaultTypeInternal;
extern FunConferMsgDefaultTypeInternal _FunConferMsg_default_instance_;
class PowerSupplyMsg;
struct PowerSupplyMsgDefaultTypeInternal;
extern PowerSupplyMsgDefaultTypeInternal _PowerSupplyMsg_default_instance_;
class ProtoConferMsg;
struct ProtoConferMsgDefaultTypeInternal;
extern ProtoConferMsgDefaultTypeInternal _ProtoConferMsg_default_instance_;
class ReserveMsg;
struct ReserveMsgDefaultTypeInternal;
extern ReserveMsgDefaultTypeInternal _ReserveMsg_default_instance_;
class SelfcheckMsg;
struct SelfcheckMsgDefaultTypeInternal;
extern SelfcheckMsgDefaultTypeInternal _SelfcheckMsg_default_instance_;
class VehicelState;
struct VehicelStateDefaultTypeInternal;
extern VehicelStateDefaultTypeInternal _VehicelState_default_instance_;
class bms2015pMsg;
struct bms2015pMsgDefaultTypeInternal;
extern bms2015pMsgDefaultTypeInternal _bms2015pMsg_default_instance_;
}  // namespace BMS2015PlusInfo
PROTOBUF_NAMESPACE_OPEN
template<> ::BMS2015PlusInfo::AuthenMsg* Arena::CreateMaybeMessage<::BMS2015PlusInfo::AuthenMsg>(Arena*);
template<> ::BMS2015PlusInfo::BMSCharging* Arena::CreateMaybeMessage<::BMS2015PlusInfo::BMSCharging>(Arena*);
template<> ::BMS2015PlusInfo::BMSChargingEnd* Arena::CreateMaybeMessage<::BMS2015PlusInfo::BMSChargingEnd>(Arena*);
template<> ::BMS2015PlusInfo::BMSConfig* Arena::CreateMaybeMessage<::BMS2015PlusInfo::BMSConfig>(Arena*);
template<> ::BMS2015PlusInfo::DischargeConfig* Arena::CreateMaybeMessage<::BMS2015PlusInfo::DischargeConfig>(Arena*);
template<> ::BMS2015PlusInfo::FunConferMsg* Arena::CreateMaybeMessage<::BMS2015PlusInfo::FunConferMsg>(Arena*);
template<> ::BMS2015PlusInfo::PowerSupplyMsg* Arena::CreateMaybeMessage<::BMS2015PlusInfo::PowerSupplyMsg>(Arena*);
template<> ::BMS2015PlusInfo::ProtoConferMsg* Arena::CreateMaybeMessage<::BMS2015PlusInfo::ProtoConferMsg>(Arena*);
template<> ::BMS2015PlusInfo::ReserveMsg* Arena::CreateMaybeMessage<::BMS2015PlusInfo::ReserveMsg>(Arena*);
template<> ::BMS2015PlusInfo::SelfcheckMsg* Arena::CreateMaybeMessage<::BMS2015PlusInfo::SelfcheckMsg>(Arena*);
template<> ::BMS2015PlusInfo::VehicelState* Arena::CreateMaybeMessage<::BMS2015PlusInfo::VehicelState>(Arena*);
template<> ::BMS2015PlusInfo::bms2015pMsg* Arena::CreateMaybeMessage<::BMS2015PlusInfo::bms2015pMsg>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace BMS2015PlusInfo {

enum ChargeState : int {
  DefaultState = 0,
  ChargeCreate = 1,
  ProtoConfer = 2,
  FunctionConfer = 3,
  Authentication = 4,
  AppointStage = 5,
  SelfCheck = 6,
  PowerSupply = 7,
  PreCharge = 8,
  EnergyTransfer = 9,
  TransferEnd = 10,
  ChgDestory = 11,
  ChargeState_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  ChargeState_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool ChargeState_IsValid(int value);
constexpr ChargeState ChargeState_MIN = DefaultState;
constexpr ChargeState ChargeState_MAX = ChgDestory;
constexpr int ChargeState_ARRAYSIZE = ChargeState_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ChargeState_descriptor();
template<typename T>
inline const std::string& ChargeState_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ChargeState>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ChargeState_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ChargeState_descriptor(), enum_t_value);
}
inline bool ChargeState_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ChargeState* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ChargeState>(
    ChargeState_descriptor(), name, value);
}
// ===================================================================

class ProtoConferMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:BMS2015PlusInfo.ProtoConferMsg) */ {
 public:
  inline ProtoConferMsg() : ProtoConferMsg(nullptr) {}
  ~ProtoConferMsg() override;
  explicit constexpr ProtoConferMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ProtoConferMsg(const ProtoConferMsg& from);
  ProtoConferMsg(ProtoConferMsg&& from) noexcept
    : ProtoConferMsg() {
    *this = ::std::move(from);
  }

  inline ProtoConferMsg& operator=(const ProtoConferMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProtoConferMsg& operator=(ProtoConferMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProtoConferMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProtoConferMsg* internal_default_instance() {
    return reinterpret_cast<const ProtoConferMsg*>(
               &_ProtoConferMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ProtoConferMsg& a, ProtoConferMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(ProtoConferMsg* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProtoConferMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ProtoConferMsg* New() const final {
    return new ProtoConferMsg();
  }

  ProtoConferMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ProtoConferMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ProtoConferMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ProtoConferMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProtoConferMsg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "BMS2015PlusInfo.ProtoConferMsg";
  }
  protected:
  explicit ProtoConferMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kChgGbVersionFieldNumber = 5,
    kEvGbVersionFieldNumber = 6,
    kChargerGuidanceVersionFieldNumber = 7,
    kChargerTransportVersionFieldNumber = 8,
    kBmsGuidanceVersionFieldNumber = 9,
    kBmsTransportVersionFieldNumber = 10,
    kChargerCanTypeFieldNumber = 1,
    kBmsCanTypeFieldNumber = 2,
    kChargerConferResultFieldNumber = 3,
    kEVConferResultFieldNumber = 4,
  };
  // bytes chgGbVersion = 5;
  void clear_chggbversion();
  const std::string& chggbversion() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_chggbversion(ArgT0&& arg0, ArgT... args);
  std::string* mutable_chggbversion();
  PROTOBUF_MUST_USE_RESULT std::string* release_chggbversion();
  void set_allocated_chggbversion(std::string* chggbversion);
  private:
  const std::string& _internal_chggbversion() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_chggbversion(const std::string& value);
  std::string* _internal_mutable_chggbversion();
  public:

  // bytes EvGbVersion = 6;
  void clear_evgbversion();
  const std::string& evgbversion() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_evgbversion(ArgT0&& arg0, ArgT... args);
  std::string* mutable_evgbversion();
  PROTOBUF_MUST_USE_RESULT std::string* release_evgbversion();
  void set_allocated_evgbversion(std::string* evgbversion);
  private:
  const std::string& _internal_evgbversion() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_evgbversion(const std::string& value);
  std::string* _internal_mutable_evgbversion();
  public:

  // bytes chargerGuidanceVersion = 7;
  void clear_chargerguidanceversion();
  const std::string& chargerguidanceversion() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_chargerguidanceversion(ArgT0&& arg0, ArgT... args);
  std::string* mutable_chargerguidanceversion();
  PROTOBUF_MUST_USE_RESULT std::string* release_chargerguidanceversion();
  void set_allocated_chargerguidanceversion(std::string* chargerguidanceversion);
  private:
  const std::string& _internal_chargerguidanceversion() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_chargerguidanceversion(const std::string& value);
  std::string* _internal_mutable_chargerguidanceversion();
  public:

  // bytes chargerTransportVersion = 8;
  void clear_chargertransportversion();
  const std::string& chargertransportversion() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_chargertransportversion(ArgT0&& arg0, ArgT... args);
  std::string* mutable_chargertransportversion();
  PROTOBUF_MUST_USE_RESULT std::string* release_chargertransportversion();
  void set_allocated_chargertransportversion(std::string* chargertransportversion);
  private:
  const std::string& _internal_chargertransportversion() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_chargertransportversion(const std::string& value);
  std::string* _internal_mutable_chargertransportversion();
  public:

  // bytes bmsGuidanceVersion = 9;
  void clear_bmsguidanceversion();
  const std::string& bmsguidanceversion() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_bmsguidanceversion(ArgT0&& arg0, ArgT... args);
  std::string* mutable_bmsguidanceversion();
  PROTOBUF_MUST_USE_RESULT std::string* release_bmsguidanceversion();
  void set_allocated_bmsguidanceversion(std::string* bmsguidanceversion);
  private:
  const std::string& _internal_bmsguidanceversion() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_bmsguidanceversion(const std::string& value);
  std::string* _internal_mutable_bmsguidanceversion();
  public:

  // bytes bmsTransportVersion = 10;
  void clear_bmstransportversion();
  const std::string& bmstransportversion() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_bmstransportversion(ArgT0&& arg0, ArgT... args);
  std::string* mutable_bmstransportversion();
  PROTOBUF_MUST_USE_RESULT std::string* release_bmstransportversion();
  void set_allocated_bmstransportversion(std::string* bmstransportversion);
  private:
  const std::string& _internal_bmstransportversion() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_bmstransportversion(const std::string& value);
  std::string* _internal_mutable_bmstransportversion();
  public:

  // uint32 chargerCanType = 1;
  void clear_chargercantype();
  ::PROTOBUF_NAMESPACE_ID::uint32 chargercantype() const;
  void set_chargercantype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chargercantype() const;
  void _internal_set_chargercantype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 bmsCanType = 2;
  void clear_bmscantype();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmscantype() const;
  void set_bmscantype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmscantype() const;
  void _internal_set_bmscantype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 chargerConferResult = 3;
  void clear_chargerconferresult();
  ::PROTOBUF_NAMESPACE_ID::uint32 chargerconferresult() const;
  void set_chargerconferresult(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chargerconferresult() const;
  void _internal_set_chargerconferresult(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 EVConferResult = 4;
  void clear_evconferresult();
  ::PROTOBUF_NAMESPACE_ID::uint32 evconferresult() const;
  void set_evconferresult(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_evconferresult() const;
  void _internal_set_evconferresult(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:BMS2015PlusInfo.ProtoConferMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr chggbversion_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr evgbversion_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr chargerguidanceversion_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr chargertransportversion_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr bmsguidanceversion_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr bmstransportversion_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chargercantype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmscantype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chargerconferresult_;
  ::PROTOBUF_NAMESPACE_ID::uint32 evconferresult_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fBMS2015P_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class FunConferMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:BMS2015PlusInfo.FunConferMsg) */ {
 public:
  inline FunConferMsg() : FunConferMsg(nullptr) {}
  ~FunConferMsg() override;
  explicit constexpr FunConferMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FunConferMsg(const FunConferMsg& from);
  FunConferMsg(FunConferMsg&& from) noexcept
    : FunConferMsg() {
    *this = ::std::move(from);
  }

  inline FunConferMsg& operator=(const FunConferMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline FunConferMsg& operator=(FunConferMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FunConferMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const FunConferMsg* internal_default_instance() {
    return reinterpret_cast<const FunConferMsg*>(
               &_FunConferMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(FunConferMsg& a, FunConferMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(FunConferMsg* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FunConferMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline FunConferMsg* New() const final {
    return new FunConferMsg();
  }

  FunConferMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FunConferMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FunConferMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const FunConferMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FunConferMsg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "BMS2015PlusInfo.FunConferMsg";
  }
  protected:
  explicit FunConferMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kChgConfigFDCFieldNumber = 1,
    kChgAuthenFDCFieldNumber = 2,
    kChgAppointFDCFieldNumber = 3,
    kChgSelfCheckFDCFieldNumber = 4,
    kChgPowerSupplyFDCFieldNumber = 5,
    kChgEnergyTransferFDCFieldNumber = 6,
    kChgEndFDCFieldNumber = 7,
    kEVConfigFDCFieldNumber = 8,
    kEVAuthenFDCFieldNumber = 9,
    kEVAppointFDCFieldNumber = 10,
    kEVSelfCheckFDCFieldNumber = 11,
    kEVPowerSupplyFDCFieldNumber = 12,
    kEVEnergyTransferFDCFieldNumber = 13,
    kEVEndFDCFieldNumber = 14,
  };
  // uint32 chgConfigFDC = 1;
  void clear_chgconfigfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 chgconfigfdc() const;
  void set_chgconfigfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chgconfigfdc() const;
  void _internal_set_chgconfigfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 chgAuthenFDC = 2;
  void clear_chgauthenfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 chgauthenfdc() const;
  void set_chgauthenfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chgauthenfdc() const;
  void _internal_set_chgauthenfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 chgAppointFDC = 3;
  void clear_chgappointfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 chgappointfdc() const;
  void set_chgappointfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chgappointfdc() const;
  void _internal_set_chgappointfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 chgSelfCheckFDC = 4;
  void clear_chgselfcheckfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 chgselfcheckfdc() const;
  void set_chgselfcheckfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chgselfcheckfdc() const;
  void _internal_set_chgselfcheckfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 chgPowerSupplyFDC = 5;
  void clear_chgpowersupplyfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 chgpowersupplyfdc() const;
  void set_chgpowersupplyfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chgpowersupplyfdc() const;
  void _internal_set_chgpowersupplyfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 chgEnergyTransferFDC = 6;
  void clear_chgenergytransferfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 chgenergytransferfdc() const;
  void set_chgenergytransferfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chgenergytransferfdc() const;
  void _internal_set_chgenergytransferfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 chgEndFDC = 7;
  void clear_chgendfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 chgendfdc() const;
  void set_chgendfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chgendfdc() const;
  void _internal_set_chgendfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 EVConfigFDC = 8;
  void clear_evconfigfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 evconfigfdc() const;
  void set_evconfigfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_evconfigfdc() const;
  void _internal_set_evconfigfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 EVAuthenFDC = 9;
  void clear_evauthenfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 evauthenfdc() const;
  void set_evauthenfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_evauthenfdc() const;
  void _internal_set_evauthenfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 EVAppointFDC = 10;
  void clear_evappointfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 evappointfdc() const;
  void set_evappointfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_evappointfdc() const;
  void _internal_set_evappointfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 EVSelfCheckFDC = 11;
  void clear_evselfcheckfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 evselfcheckfdc() const;
  void set_evselfcheckfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_evselfcheckfdc() const;
  void _internal_set_evselfcheckfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 EVPowerSupplyFDC = 12;
  void clear_evpowersupplyfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 evpowersupplyfdc() const;
  void set_evpowersupplyfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_evpowersupplyfdc() const;
  void _internal_set_evpowersupplyfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 EVEnergyTransferFDC = 13;
  void clear_evenergytransferfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 evenergytransferfdc() const;
  void set_evenergytransferfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_evenergytransferfdc() const;
  void _internal_set_evenergytransferfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 EVEndFDC = 14;
  void clear_evendfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 evendfdc() const;
  void set_evendfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_evendfdc() const;
  void _internal_set_evendfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:BMS2015PlusInfo.FunConferMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chgconfigfdc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chgauthenfdc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chgappointfdc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chgselfcheckfdc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chgpowersupplyfdc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chgenergytransferfdc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chgendfdc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 evconfigfdc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 evauthenfdc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 evappointfdc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 evselfcheckfdc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 evpowersupplyfdc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 evenergytransferfdc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 evendfdc_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fBMS2015P_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class BMSConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:BMS2015PlusInfo.BMSConfig) */ {
 public:
  inline BMSConfig() : BMSConfig(nullptr) {}
  ~BMSConfig() override;
  explicit constexpr BMSConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BMSConfig(const BMSConfig& from);
  BMSConfig(BMSConfig&& from) noexcept
    : BMSConfig() {
    *this = ::std::move(from);
  }

  inline BMSConfig& operator=(const BMSConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline BMSConfig& operator=(BMSConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BMSConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const BMSConfig* internal_default_instance() {
    return reinterpret_cast<const BMSConfig*>(
               &_BMSConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(BMSConfig& a, BMSConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(BMSConfig* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BMSConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BMSConfig* New() const final {
    return new BMSConfig();
  }

  BMSConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BMSConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BMSConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const BMSConfig& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BMSConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "BMS2015PlusInfo.BMSConfig";
  }
  protected:
  explicit BMSConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMonoVolMaxAllowedFieldNumber = 1,
    kCurAllowedMaxFieldNumber = 2,
    kEnergyAllowdMaxFieldNumber = 3,
    kVolAllowedMaxFieldNumber = 4,
    kTempAllowedMaxFieldNumber = 5,
    kStartSOCFieldNumber = 6,
    kVolBatNowFieldNumber = 7,
    kVolChargerMaxFieldNumber = 8,
    kVolChargerMinFieldNumber = 9,
    kCurChargerMaxFieldNumber = 10,
    kCurChargerMinFieldNumber = 11,
    kCurrUpRateFieldNumber = 12,
    kCurrDownRateFieldNumber = 13,
    kChargerAllowedRestartNumFieldNumber = 14,
    kBmsAllowedRestartNumFieldNumber = 15,
  };
  // float monoVolMaxAllowed = 1;
  void clear_monovolmaxallowed();
  float monovolmaxallowed() const;
  void set_monovolmaxallowed(float value);
  private:
  float _internal_monovolmaxallowed() const;
  void _internal_set_monovolmaxallowed(float value);
  public:

  // float curAllowedMax = 2;
  void clear_curallowedmax();
  float curallowedmax() const;
  void set_curallowedmax(float value);
  private:
  float _internal_curallowedmax() const;
  void _internal_set_curallowedmax(float value);
  public:

  // float energyAllowdMax = 3;
  void clear_energyallowdmax();
  float energyallowdmax() const;
  void set_energyallowdmax(float value);
  private:
  float _internal_energyallowdmax() const;
  void _internal_set_energyallowdmax(float value);
  public:

  // float volAllowedMax = 4;
  void clear_volallowedmax();
  float volallowedmax() const;
  void set_volallowedmax(float value);
  private:
  float _internal_volallowedmax() const;
  void _internal_set_volallowedmax(float value);
  public:

  // float tempAllowedMax = 5;
  void clear_tempallowedmax();
  float tempallowedmax() const;
  void set_tempallowedmax(float value);
  private:
  float _internal_tempallowedmax() const;
  void _internal_set_tempallowedmax(float value);
  public:

  // float startSOC = 6;
  void clear_startsoc();
  float startsoc() const;
  void set_startsoc(float value);
  private:
  float _internal_startsoc() const;
  void _internal_set_startsoc(float value);
  public:

  // float volBatNow = 7;
  void clear_volbatnow();
  float volbatnow() const;
  void set_volbatnow(float value);
  private:
  float _internal_volbatnow() const;
  void _internal_set_volbatnow(float value);
  public:

  // float volChargerMax = 8;
  void clear_volchargermax();
  float volchargermax() const;
  void set_volchargermax(float value);
  private:
  float _internal_volchargermax() const;
  void _internal_set_volchargermax(float value);
  public:

  // float volChargerMin = 9;
  void clear_volchargermin();
  float volchargermin() const;
  void set_volchargermin(float value);
  private:
  float _internal_volchargermin() const;
  void _internal_set_volchargermin(float value);
  public:

  // float curChargerMax = 10;
  void clear_curchargermax();
  float curchargermax() const;
  void set_curchargermax(float value);
  private:
  float _internal_curchargermax() const;
  void _internal_set_curchargermax(float value);
  public:

  // float curChargerMin = 11;
  void clear_curchargermin();
  float curchargermin() const;
  void set_curchargermin(float value);
  private:
  float _internal_curchargermin() const;
  void _internal_set_curchargermin(float value);
  public:

  // uint32 currUpRate = 12;
  void clear_curruprate();
  ::PROTOBUF_NAMESPACE_ID::uint32 curruprate() const;
  void set_curruprate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_curruprate() const;
  void _internal_set_curruprate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 currDownRate = 13;
  void clear_currdownrate();
  ::PROTOBUF_NAMESPACE_ID::uint32 currdownrate() const;
  void set_currdownrate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_currdownrate() const;
  void _internal_set_currdownrate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 chargerAllowedRestartNum = 14;
  void clear_chargerallowedrestartnum();
  ::PROTOBUF_NAMESPACE_ID::uint32 chargerallowedrestartnum() const;
  void set_chargerallowedrestartnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chargerallowedrestartnum() const;
  void _internal_set_chargerallowedrestartnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 bmsAllowedRestartNum = 15;
  void clear_bmsallowedrestartnum();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsallowedrestartnum() const;
  void set_bmsallowedrestartnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsallowedrestartnum() const;
  void _internal_set_bmsallowedrestartnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:BMS2015PlusInfo.BMSConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float monovolmaxallowed_;
  float curallowedmax_;
  float energyallowdmax_;
  float volallowedmax_;
  float tempallowedmax_;
  float startsoc_;
  float volbatnow_;
  float volchargermax_;
  float volchargermin_;
  float curchargermax_;
  float curchargermin_;
  ::PROTOBUF_NAMESPACE_ID::uint32 curruprate_;
  ::PROTOBUF_NAMESPACE_ID::uint32 currdownrate_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chargerallowedrestartnum_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsallowedrestartnum_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fBMS2015P_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class DischargeConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:BMS2015PlusInfo.DischargeConfig) */ {
 public:
  inline DischargeConfig() : DischargeConfig(nullptr) {}
  ~DischargeConfig() override;
  explicit constexpr DischargeConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DischargeConfig(const DischargeConfig& from);
  DischargeConfig(DischargeConfig&& from) noexcept
    : DischargeConfig() {
    *this = ::std::move(from);
  }

  inline DischargeConfig& operator=(const DischargeConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline DischargeConfig& operator=(DischargeConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DischargeConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const DischargeConfig* internal_default_instance() {
    return reinterpret_cast<const DischargeConfig*>(
               &_DischargeConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(DischargeConfig& a, DischargeConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(DischargeConfig* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DischargeConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DischargeConfig* New() const final {
    return new DischargeConfig();
  }

  DischargeConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DischargeConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DischargeConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DischargeConfig& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DischargeConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "BMS2015PlusInfo.DischargeConfig";
  }
  protected:
  explicit DischargeConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kVolDischargeMaxFieldNumber = 1,
    kVolDischargeMinFieldNumber = 2,
    kCurDischargeMaxFieldNumber = 3,
    kCurDischargeMinFieldNumber = 4,
    kVolBmsAllowedMinFieldNumber = 5,
    kCurBmsAllowedMaxFieldNumber = 6,
    kCurBmsAllowedMinFieldNumber = 7,
    kVolCellAllowedMaxFieldNumber = 8,
    kVolCellAllowedMinFieldNumber = 9,
    kSocAllowedMinFieldNumber = 10,
    kTotalBatteryCycleNumFieldNumber = 11,
    kAllowedBatteryCycleNumFieldNumber = 12,
    kDesireResidueRangeFieldNumber = 13,
    kTempAllowedMaxFieldNumber = 14,
    kChargerAllowedRestartNumFieldNumber = 15,
    kBmsAllowedRestartNumFieldNumber = 16,
  };
  // float volDischargeMax = 1;
  void clear_voldischargemax();
  float voldischargemax() const;
  void set_voldischargemax(float value);
  private:
  float _internal_voldischargemax() const;
  void _internal_set_voldischargemax(float value);
  public:

  // float volDischargeMin = 2;
  void clear_voldischargemin();
  float voldischargemin() const;
  void set_voldischargemin(float value);
  private:
  float _internal_voldischargemin() const;
  void _internal_set_voldischargemin(float value);
  public:

  // float curDischargeMax = 3;
  void clear_curdischargemax();
  float curdischargemax() const;
  void set_curdischargemax(float value);
  private:
  float _internal_curdischargemax() const;
  void _internal_set_curdischargemax(float value);
  public:

  // float curDischargeMin = 4;
  void clear_curdischargemin();
  float curdischargemin() const;
  void set_curdischargemin(float value);
  private:
  float _internal_curdischargemin() const;
  void _internal_set_curdischargemin(float value);
  public:

  // float volBmsAllowedMin = 5;
  void clear_volbmsallowedmin();
  float volbmsallowedmin() const;
  void set_volbmsallowedmin(float value);
  private:
  float _internal_volbmsallowedmin() const;
  void _internal_set_volbmsallowedmin(float value);
  public:

  // float curBmsAllowedMax = 6;
  void clear_curbmsallowedmax();
  float curbmsallowedmax() const;
  void set_curbmsallowedmax(float value);
  private:
  float _internal_curbmsallowedmax() const;
  void _internal_set_curbmsallowedmax(float value);
  public:

  // float curBmsAllowedMin = 7;
  void clear_curbmsallowedmin();
  float curbmsallowedmin() const;
  void set_curbmsallowedmin(float value);
  private:
  float _internal_curbmsallowedmin() const;
  void _internal_set_curbmsallowedmin(float value);
  public:

  // float volCellAllowedMax = 8;
  void clear_volcellallowedmax();
  float volcellallowedmax() const;
  void set_volcellallowedmax(float value);
  private:
  float _internal_volcellallowedmax() const;
  void _internal_set_volcellallowedmax(float value);
  public:

  // float volCellAllowedMin = 9;
  void clear_volcellallowedmin();
  float volcellallowedmin() const;
  void set_volcellallowedmin(float value);
  private:
  float _internal_volcellallowedmin() const;
  void _internal_set_volcellallowedmin(float value);
  public:

  // float socAllowedMin = 10;
  void clear_socallowedmin();
  float socallowedmin() const;
  void set_socallowedmin(float value);
  private:
  float _internal_socallowedmin() const;
  void _internal_set_socallowedmin(float value);
  public:

  // float totalBatteryCycleNum = 11;
  void clear_totalbatterycyclenum();
  float totalbatterycyclenum() const;
  void set_totalbatterycyclenum(float value);
  private:
  float _internal_totalbatterycyclenum() const;
  void _internal_set_totalbatterycyclenum(float value);
  public:

  // float allowedBatteryCycleNum = 12;
  void clear_allowedbatterycyclenum();
  float allowedbatterycyclenum() const;
  void set_allowedbatterycyclenum(float value);
  private:
  float _internal_allowedbatterycyclenum() const;
  void _internal_set_allowedbatterycyclenum(float value);
  public:

  // float desireResidueRange = 13;
  void clear_desireresiduerange();
  float desireresiduerange() const;
  void set_desireresiduerange(float value);
  private:
  float _internal_desireresiduerange() const;
  void _internal_set_desireresiduerange(float value);
  public:

  // float tempAllowedMax = 14;
  void clear_tempallowedmax();
  float tempallowedmax() const;
  void set_tempallowedmax(float value);
  private:
  float _internal_tempallowedmax() const;
  void _internal_set_tempallowedmax(float value);
  public:

  // uint32 chargerAllowedRestartNum = 15;
  void clear_chargerallowedrestartnum();
  ::PROTOBUF_NAMESPACE_ID::uint32 chargerallowedrestartnum() const;
  void set_chargerallowedrestartnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chargerallowedrestartnum() const;
  void _internal_set_chargerallowedrestartnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 bmsAllowedRestartNum = 16;
  void clear_bmsallowedrestartnum();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsallowedrestartnum() const;
  void set_bmsallowedrestartnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsallowedrestartnum() const;
  void _internal_set_bmsallowedrestartnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:BMS2015PlusInfo.DischargeConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float voldischargemax_;
  float voldischargemin_;
  float curdischargemax_;
  float curdischargemin_;
  float volbmsallowedmin_;
  float curbmsallowedmax_;
  float curbmsallowedmin_;
  float volcellallowedmax_;
  float volcellallowedmin_;
  float socallowedmin_;
  float totalbatterycyclenum_;
  float allowedbatterycyclenum_;
  float desireresiduerange_;
  float tempallowedmax_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chargerallowedrestartnum_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsallowedrestartnum_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fBMS2015P_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class AuthenMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:BMS2015PlusInfo.AuthenMsg) */ {
 public:
  inline AuthenMsg() : AuthenMsg(nullptr) {}
  ~AuthenMsg() override;
  explicit constexpr AuthenMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AuthenMsg(const AuthenMsg& from);
  AuthenMsg(AuthenMsg&& from) noexcept
    : AuthenMsg() {
    *this = ::std::move(from);
  }

  inline AuthenMsg& operator=(const AuthenMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline AuthenMsg& operator=(AuthenMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AuthenMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const AuthenMsg* internal_default_instance() {
    return reinterpret_cast<const AuthenMsg*>(
               &_AuthenMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(AuthenMsg& a, AuthenMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(AuthenMsg* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AuthenMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AuthenMsg* New() const final {
    return new AuthenMsg();
  }

  AuthenMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AuthenMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AuthenMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AuthenMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AuthenMsg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "BMS2015PlusInfo.AuthenMsg";
  }
  protected:
  explicit AuthenMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEVINFieldNumber = 6,
    kBatProducerFieldNumber = 7,
    kChargerOperatorsFieldNumber = 8,
    kChargerNumberFieldNumber = 9,
    kNowFDCFieldNumber = 1,
    kAuthenWaitTimeFieldNumber = 2,
    kBmsWaitAuthenStateFieldNumber = 3,
    kAuthenResultFieldNumber = 4,
    kSucceedAuthenFDCFieldNumber = 5,
  };
  // bytes eVIN = 6;
  void clear_evin();
  const std::string& evin() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_evin(ArgT0&& arg0, ArgT... args);
  std::string* mutable_evin();
  PROTOBUF_MUST_USE_RESULT std::string* release_evin();
  void set_allocated_evin(std::string* evin);
  private:
  const std::string& _internal_evin() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_evin(const std::string& value);
  std::string* _internal_mutable_evin();
  public:

  // bytes batProducer = 7;
  void clear_batproducer();
  const std::string& batproducer() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_batproducer(ArgT0&& arg0, ArgT... args);
  std::string* mutable_batproducer();
  PROTOBUF_MUST_USE_RESULT std::string* release_batproducer();
  void set_allocated_batproducer(std::string* batproducer);
  private:
  const std::string& _internal_batproducer() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_batproducer(const std::string& value);
  std::string* _internal_mutable_batproducer();
  public:

  // bytes chargerOperators = 8;
  void clear_chargeroperators();
  const std::string& chargeroperators() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_chargeroperators(ArgT0&& arg0, ArgT... args);
  std::string* mutable_chargeroperators();
  PROTOBUF_MUST_USE_RESULT std::string* release_chargeroperators();
  void set_allocated_chargeroperators(std::string* chargeroperators);
  private:
  const std::string& _internal_chargeroperators() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_chargeroperators(const std::string& value);
  std::string* _internal_mutable_chargeroperators();
  public:

  // bytes chargerNumber = 9;
  void clear_chargernumber();
  const std::string& chargernumber() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_chargernumber(ArgT0&& arg0, ArgT... args);
  std::string* mutable_chargernumber();
  PROTOBUF_MUST_USE_RESULT std::string* release_chargernumber();
  void set_allocated_chargernumber(std::string* chargernumber);
  private:
  const std::string& _internal_chargernumber() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_chargernumber(const std::string& value);
  std::string* _internal_mutable_chargernumber();
  public:

  // uint32 nowFDC = 1;
  void clear_nowfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 nowfdc() const;
  void set_nowfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_nowfdc() const;
  void _internal_set_nowfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 authenWaitTime = 2;
  void clear_authenwaittime();
  ::PROTOBUF_NAMESPACE_ID::uint32 authenwaittime() const;
  void set_authenwaittime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_authenwaittime() const;
  void _internal_set_authenwaittime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 bmsWaitAuthenState = 3;
  void clear_bmswaitauthenstate();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmswaitauthenstate() const;
  void set_bmswaitauthenstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmswaitauthenstate() const;
  void _internal_set_bmswaitauthenstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 authenResult = 4;
  void clear_authenresult();
  ::PROTOBUF_NAMESPACE_ID::uint32 authenresult() const;
  void set_authenresult(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_authenresult() const;
  void _internal_set_authenresult(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 succeedAuthenFDC = 5;
  void clear_succeedauthenfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 succeedauthenfdc() const;
  void set_succeedauthenfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_succeedauthenfdc() const;
  void _internal_set_succeedauthenfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:BMS2015PlusInfo.AuthenMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr evin_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr batproducer_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr chargeroperators_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr chargernumber_;
  ::PROTOBUF_NAMESPACE_ID::uint32 nowfdc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 authenwaittime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmswaitauthenstate_;
  ::PROTOBUF_NAMESPACE_ID::uint32 authenresult_;
  ::PROTOBUF_NAMESPACE_ID::uint32 succeedauthenfdc_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fBMS2015P_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class ReserveMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:BMS2015PlusInfo.ReserveMsg) */ {
 public:
  inline ReserveMsg() : ReserveMsg(nullptr) {}
  ~ReserveMsg() override;
  explicit constexpr ReserveMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ReserveMsg(const ReserveMsg& from);
  ReserveMsg(ReserveMsg&& from) noexcept
    : ReserveMsg() {
    *this = ::std::move(from);
  }

  inline ReserveMsg& operator=(const ReserveMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline ReserveMsg& operator=(ReserveMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ReserveMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const ReserveMsg* internal_default_instance() {
    return reinterpret_cast<const ReserveMsg*>(
               &_ReserveMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(ReserveMsg& a, ReserveMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(ReserveMsg* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ReserveMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ReserveMsg* New() const final {
    return new ReserveMsg();
  }

  ReserveMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ReserveMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ReserveMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ReserveMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ReserveMsg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "BMS2015PlusInfo.ReserveMsg";
  }
  protected:
  explicit ReserveMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBmsDesireStartTimeFieldNumber = 1,
    kBmsDesireLeaveTimeFieldNumber = 2,
    kChargerOutPowerMaxFieldNumber = 3,
    kImmediateChargeSupportFieldNumber = 4,
    kReserveResultFieldNumber = 5,
  };
  // uint32 bmsDesireStartTime = 1;
  void clear_bmsdesirestarttime();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsdesirestarttime() const;
  void set_bmsdesirestarttime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsdesirestarttime() const;
  void _internal_set_bmsdesirestarttime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 bmsDesireLeaveTime = 2;
  void clear_bmsdesireleavetime();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsdesireleavetime() const;
  void set_bmsdesireleavetime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsdesireleavetime() const;
  void _internal_set_bmsdesireleavetime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // float chargerOutPowerMax = 3;
  void clear_chargeroutpowermax();
  float chargeroutpowermax() const;
  void set_chargeroutpowermax(float value);
  private:
  float _internal_chargeroutpowermax() const;
  void _internal_set_chargeroutpowermax(float value);
  public:

  // uint32 immediateChargeSupport = 4;
  void clear_immediatechargesupport();
  ::PROTOBUF_NAMESPACE_ID::uint32 immediatechargesupport() const;
  void set_immediatechargesupport(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_immediatechargesupport() const;
  void _internal_set_immediatechargesupport(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 reserveResult = 5;
  void clear_reserveresult();
  ::PROTOBUF_NAMESPACE_ID::uint32 reserveresult() const;
  void set_reserveresult(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_reserveresult() const;
  void _internal_set_reserveresult(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:BMS2015PlusInfo.ReserveMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsdesirestarttime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsdesireleavetime_;
  float chargeroutpowermax_;
  ::PROTOBUF_NAMESPACE_ID::uint32 immediatechargesupport_;
  ::PROTOBUF_NAMESPACE_ID::uint32 reserveresult_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fBMS2015P_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class SelfcheckMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:BMS2015PlusInfo.SelfcheckMsg) */ {
 public:
  inline SelfcheckMsg() : SelfcheckMsg(nullptr) {}
  ~SelfcheckMsg() override;
  explicit constexpr SelfcheckMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SelfcheckMsg(const SelfcheckMsg& from);
  SelfcheckMsg(SelfcheckMsg&& from) noexcept
    : SelfcheckMsg() {
    *this = ::std::move(from);
  }

  inline SelfcheckMsg& operator=(const SelfcheckMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline SelfcheckMsg& operator=(SelfcheckMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SelfcheckMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const SelfcheckMsg* internal_default_instance() {
    return reinterpret_cast<const SelfcheckMsg*>(
               &_SelfcheckMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(SelfcheckMsg& a, SelfcheckMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(SelfcheckMsg* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SelfcheckMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SelfcheckMsg* New() const final {
    return new SelfcheckMsg();
  }

  SelfcheckMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SelfcheckMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SelfcheckMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SelfcheckMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SelfcheckMsg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "BMS2015PlusInfo.SelfcheckMsg";
  }
  protected:
  explicit SelfcheckMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStickCheckStateFieldNumber = 1,
    kShortCircuitCheckStateFieldNumber = 2,
    kInsultCheckStateFieldNumber = 3,
    kDischargeStateFieldNumber = 4,
  };
  // uint32 stickCheckState = 1;
  void clear_stickcheckstate();
  ::PROTOBUF_NAMESPACE_ID::uint32 stickcheckstate() const;
  void set_stickcheckstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_stickcheckstate() const;
  void _internal_set_stickcheckstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 shortCircuitCheckState = 2;
  void clear_shortcircuitcheckstate();
  ::PROTOBUF_NAMESPACE_ID::uint32 shortcircuitcheckstate() const;
  void set_shortcircuitcheckstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_shortcircuitcheckstate() const;
  void _internal_set_shortcircuitcheckstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 insultCheckState = 3;
  void clear_insultcheckstate();
  ::PROTOBUF_NAMESPACE_ID::uint32 insultcheckstate() const;
  void set_insultcheckstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_insultcheckstate() const;
  void _internal_set_insultcheckstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 dischargeState = 4;
  void clear_dischargestate();
  ::PROTOBUF_NAMESPACE_ID::uint32 dischargestate() const;
  void set_dischargestate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_dischargestate() const;
  void _internal_set_dischargestate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:BMS2015PlusInfo.SelfcheckMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 stickcheckstate_;
  ::PROTOBUF_NAMESPACE_ID::uint32 shortcircuitcheckstate_;
  ::PROTOBUF_NAMESPACE_ID::uint32 insultcheckstate_;
  ::PROTOBUF_NAMESPACE_ID::uint32 dischargestate_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fBMS2015P_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class VehicelState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:BMS2015PlusInfo.VehicelState) */ {
 public:
  inline VehicelState() : VehicelState(nullptr) {}
  ~VehicelState() override;
  explicit constexpr VehicelState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  VehicelState(const VehicelState& from);
  VehicelState(VehicelState&& from) noexcept
    : VehicelState() {
    *this = ::std::move(from);
  }

  inline VehicelState& operator=(const VehicelState& from) {
    CopyFrom(from);
    return *this;
  }
  inline VehicelState& operator=(VehicelState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const VehicelState& default_instance() {
    return *internal_default_instance();
  }
  static inline const VehicelState* internal_default_instance() {
    return reinterpret_cast<const VehicelState*>(
               &_VehicelState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(VehicelState& a, VehicelState& b) {
    a.Swap(&b);
  }
  inline void Swap(VehicelState* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VehicelState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline VehicelState* New() const final {
    return new VehicelState();
  }

  VehicelState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<VehicelState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const VehicelState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const VehicelState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VehicelState* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "BMS2015PlusInfo.VehicelState";
  }
  protected:
  explicit VehicelState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kVehicleC5StateFieldNumber = 1,
    kVehicleC6StateFieldNumber = 2,
    kVehicelElockStateFieldNumber = 3,
  };
  // uint32 vehicleC5State = 1;
  void clear_vehiclec5state();
  ::PROTOBUF_NAMESPACE_ID::uint32 vehiclec5state() const;
  void set_vehiclec5state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_vehiclec5state() const;
  void _internal_set_vehiclec5state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 vehicleC6State = 2;
  void clear_vehiclec6state();
  ::PROTOBUF_NAMESPACE_ID::uint32 vehiclec6state() const;
  void set_vehiclec6state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_vehiclec6state() const;
  void _internal_set_vehiclec6state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 vehicelElockState = 3;
  void clear_vehicelelockstate();
  ::PROTOBUF_NAMESPACE_ID::uint32 vehicelelockstate() const;
  void set_vehicelelockstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_vehicelelockstate() const;
  void _internal_set_vehicelelockstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:BMS2015PlusInfo.VehicelState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 vehiclec5state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 vehiclec6state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 vehicelelockstate_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fBMS2015P_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class PowerSupplyMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:BMS2015PlusInfo.PowerSupplyMsg) */ {
 public:
  inline PowerSupplyMsg() : PowerSupplyMsg(nullptr) {}
  ~PowerSupplyMsg() override;
  explicit constexpr PowerSupplyMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PowerSupplyMsg(const PowerSupplyMsg& from);
  PowerSupplyMsg(PowerSupplyMsg&& from) noexcept
    : PowerSupplyMsg() {
    *this = ::std::move(from);
  }

  inline PowerSupplyMsg& operator=(const PowerSupplyMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline PowerSupplyMsg& operator=(PowerSupplyMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PowerSupplyMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const PowerSupplyMsg* internal_default_instance() {
    return reinterpret_cast<const PowerSupplyMsg*>(
               &_PowerSupplyMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(PowerSupplyMsg& a, PowerSupplyMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(PowerSupplyMsg* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PowerSupplyMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline PowerSupplyMsg* New() const final {
    return new PowerSupplyMsg();
  }

  PowerSupplyMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<PowerSupplyMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PowerSupplyMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PowerSupplyMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PowerSupplyMsg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "BMS2015PlusInfo.PowerSupplyMsg";
  }
  protected:
  explicit PowerSupplyMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kChargerSupplyStateFieldNumber = 1,
    kEvSupplyStateFieldNumber = 2,
    kVolDesireFieldNumber = 3,
    kCurrDesireFieldNumber = 4,
    kOutVolFieldNumber = 5,
    kOutCurrFieldNumber = 6,
    kChgOutCurrMaxFieldNumber = 7,
    kResonForCapacityChangeFieldNumber = 8,
  };
  // uint32 chargerSupplyState = 1;
  void clear_chargersupplystate();
  ::PROTOBUF_NAMESPACE_ID::uint32 chargersupplystate() const;
  void set_chargersupplystate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chargersupplystate() const;
  void _internal_set_chargersupplystate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 EvSupplyState = 2;
  void clear_evsupplystate();
  ::PROTOBUF_NAMESPACE_ID::uint32 evsupplystate() const;
  void set_evsupplystate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_evsupplystate() const;
  void _internal_set_evsupplystate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // float volDesire = 3;
  void clear_voldesire();
  float voldesire() const;
  void set_voldesire(float value);
  private:
  float _internal_voldesire() const;
  void _internal_set_voldesire(float value);
  public:

  // float currDesire = 4;
  void clear_currdesire();
  float currdesire() const;
  void set_currdesire(float value);
  private:
  float _internal_currdesire() const;
  void _internal_set_currdesire(float value);
  public:

  // float outVol = 5;
  void clear_outvol();
  float outvol() const;
  void set_outvol(float value);
  private:
  float _internal_outvol() const;
  void _internal_set_outvol(float value);
  public:

  // float outCurr = 6;
  void clear_outcurr();
  float outcurr() const;
  void set_outcurr(float value);
  private:
  float _internal_outcurr() const;
  void _internal_set_outcurr(float value);
  public:

  // float chgOutCurrMax = 7;
  void clear_chgoutcurrmax();
  float chgoutcurrmax() const;
  void set_chgoutcurrmax(float value);
  private:
  float _internal_chgoutcurrmax() const;
  void _internal_set_chgoutcurrmax(float value);
  public:

  // float resonForCapacityChange = 8;
  void clear_resonforcapacitychange();
  float resonforcapacitychange() const;
  void set_resonforcapacitychange(float value);
  private:
  float _internal_resonforcapacitychange() const;
  void _internal_set_resonforcapacitychange(float value);
  public:

  // @@protoc_insertion_point(class_scope:BMS2015PlusInfo.PowerSupplyMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chargersupplystate_;
  ::PROTOBUF_NAMESPACE_ID::uint32 evsupplystate_;
  float voldesire_;
  float currdesire_;
  float outvol_;
  float outcurr_;
  float chgoutcurrmax_;
  float resonforcapacitychange_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fBMS2015P_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class BMSChargingEnd final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:BMS2015PlusInfo.BMSChargingEnd) */ {
 public:
  inline BMSChargingEnd() : BMSChargingEnd(nullptr) {}
  ~BMSChargingEnd() override;
  explicit constexpr BMSChargingEnd(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BMSChargingEnd(const BMSChargingEnd& from);
  BMSChargingEnd(BMSChargingEnd&& from) noexcept
    : BMSChargingEnd() {
    *this = ::std::move(from);
  }

  inline BMSChargingEnd& operator=(const BMSChargingEnd& from) {
    CopyFrom(from);
    return *this;
  }
  inline BMSChargingEnd& operator=(BMSChargingEnd&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BMSChargingEnd& default_instance() {
    return *internal_default_instance();
  }
  static inline const BMSChargingEnd* internal_default_instance() {
    return reinterpret_cast<const BMSChargingEnd*>(
               &_BMSChargingEnd_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(BMSChargingEnd& a, BMSChargingEnd& b) {
    a.Swap(&b);
  }
  inline void Swap(BMSChargingEnd* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BMSChargingEnd* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BMSChargingEnd* New() const final {
    return new BMSChargingEnd();
  }

  BMSChargingEnd* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BMSChargingEnd>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BMSChargingEnd& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const BMSChargingEnd& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BMSChargingEnd* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "BMS2015PlusInfo.BMSChargingEnd";
  }
  protected:
  explicit BMSChargingEnd(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBmsStickCheckStateFieldNumber = 1,
    kChgStickCheckEnableFieldNumber = 2,
    kEnergyChgFieldNumber = 3,
    kEnergyDischargeFieldNumber = 4,
    kEndSOCFieldNumber = 5,
    kChargerStopTypeFieldNumber = 6,
    kChargerStopCodeFieldNumber = 7,
    kBmsStopTypeFieldNumber = 8,
    kBmsStopCodeFieldNumber = 9,
    kChgReconnectEnableFieldNumber = 10,
    kReconnectEnableFieldNumber = 11,
  };
  // uint32 bmsStickCheckState = 1;
  void clear_bmsstickcheckstate();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsstickcheckstate() const;
  void set_bmsstickcheckstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsstickcheckstate() const;
  void _internal_set_bmsstickcheckstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 chgStickCheckEnable = 2;
  void clear_chgstickcheckenable();
  ::PROTOBUF_NAMESPACE_ID::uint32 chgstickcheckenable() const;
  void set_chgstickcheckenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chgstickcheckenable() const;
  void _internal_set_chgstickcheckenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // float energyChg = 3;
  void clear_energychg();
  float energychg() const;
  void set_energychg(float value);
  private:
  float _internal_energychg() const;
  void _internal_set_energychg(float value);
  public:

  // float energyDischarge = 4;
  void clear_energydischarge();
  float energydischarge() const;
  void set_energydischarge(float value);
  private:
  float _internal_energydischarge() const;
  void _internal_set_energydischarge(float value);
  public:

  // float endSOC = 5;
  void clear_endsoc();
  float endsoc() const;
  void set_endsoc(float value);
  private:
  float _internal_endsoc() const;
  void _internal_set_endsoc(float value);
  public:

  // uint32 chargerStopType = 6;
  void clear_chargerstoptype();
  ::PROTOBUF_NAMESPACE_ID::uint32 chargerstoptype() const;
  void set_chargerstoptype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chargerstoptype() const;
  void _internal_set_chargerstoptype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 chargerStopCode = 7;
  void clear_chargerstopcode();
  ::PROTOBUF_NAMESPACE_ID::uint32 chargerstopcode() const;
  void set_chargerstopcode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chargerstopcode() const;
  void _internal_set_chargerstopcode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 bmsStopType = 8;
  void clear_bmsstoptype();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsstoptype() const;
  void set_bmsstoptype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsstoptype() const;
  void _internal_set_bmsstoptype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 bmsStopCode = 9;
  void clear_bmsstopcode();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsstopcode() const;
  void set_bmsstopcode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsstopcode() const;
  void _internal_set_bmsstopcode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 chgReconnectEnable = 10;
  void clear_chgreconnectenable();
  ::PROTOBUF_NAMESPACE_ID::uint32 chgreconnectenable() const;
  void set_chgreconnectenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chgreconnectenable() const;
  void _internal_set_chgreconnectenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 reconnectEnable = 11;
  void clear_reconnectenable();
  ::PROTOBUF_NAMESPACE_ID::uint32 reconnectenable() const;
  void set_reconnectenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_reconnectenable() const;
  void _internal_set_reconnectenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:BMS2015PlusInfo.BMSChargingEnd)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsstickcheckstate_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chgstickcheckenable_;
  float energychg_;
  float energydischarge_;
  float endsoc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chargerstoptype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chargerstopcode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsstoptype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsstopcode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chgreconnectenable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 reconnectenable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fBMS2015P_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class BMSCharging final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:BMS2015PlusInfo.BMSCharging) */ {
 public:
  inline BMSCharging() : BMSCharging(nullptr) {}
  ~BMSCharging() override;
  explicit constexpr BMSCharging(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BMSCharging(const BMSCharging& from);
  BMSCharging(BMSCharging&& from) noexcept
    : BMSCharging() {
    *this = ::std::move(from);
  }

  inline BMSCharging& operator=(const BMSCharging& from) {
    CopyFrom(from);
    return *this;
  }
  inline BMSCharging& operator=(BMSCharging&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BMSCharging& default_instance() {
    return *internal_default_instance();
  }
  static inline const BMSCharging* internal_default_instance() {
    return reinterpret_cast<const BMSCharging*>(
               &_BMSCharging_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(BMSCharging& a, BMSCharging& b) {
    a.Swap(&b);
  }
  inline void Swap(BMSCharging* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BMSCharging* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BMSCharging* New() const final {
    return new BMSCharging();
  }

  BMSCharging* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BMSCharging>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BMSCharging& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const BMSCharging& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BMSCharging* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "BMS2015PlusInfo.BMSCharging";
  }
  protected:
  explicit BMSCharging(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBmsReadyFieldNumber = 1,
    kChargerReadyFieldNumber = 2,
    kChargeModeFieldNumber = 3,
    kResonForCapacityChangeFieldNumber = 4,
    kBmsPauseFieldNumber = 5,
    kChgPauseFieldNumber = 6,
    kBmsBatVolFieldNumber = 7,
    kVolDemandFieldNumber = 8,
    kCurDemandFieldNumber = 9,
    kSocNowFieldNumber = 10,
    kCurrOutCapacityFieldNumber = 11,
    kVolMeasuredFieldNumber = 12,
    kCurMeasuredFieldNumber = 13,
    kMonoBatVolMaxFieldNumber = 14,
    kMonoBatVolMinFieldNumber = 15,
    kTempMaxFieldNumber = 16,
    kTempMinFieldNumber = 17,
  };
  // uint32 bmsReady = 1;
  void clear_bmsready();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsready() const;
  void set_bmsready(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsready() const;
  void _internal_set_bmsready(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 chargerReady = 2;
  void clear_chargerready();
  ::PROTOBUF_NAMESPACE_ID::uint32 chargerready() const;
  void set_chargerready(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chargerready() const;
  void _internal_set_chargerready(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 chargeMode = 3;
  void clear_chargemode();
  ::PROTOBUF_NAMESPACE_ID::uint32 chargemode() const;
  void set_chargemode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chargemode() const;
  void _internal_set_chargemode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 resonForCapacityChange = 4;
  void clear_resonforcapacitychange();
  ::PROTOBUF_NAMESPACE_ID::uint32 resonforcapacitychange() const;
  void set_resonforcapacitychange(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_resonforcapacitychange() const;
  void _internal_set_resonforcapacitychange(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 bmsPause = 5;
  void clear_bmspause();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmspause() const;
  void set_bmspause(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmspause() const;
  void _internal_set_bmspause(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 chgPause = 6;
  void clear_chgpause();
  ::PROTOBUF_NAMESPACE_ID::uint32 chgpause() const;
  void set_chgpause(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chgpause() const;
  void _internal_set_chgpause(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // float bmsBatVol = 7;
  void clear_bmsbatvol();
  float bmsbatvol() const;
  void set_bmsbatvol(float value);
  private:
  float _internal_bmsbatvol() const;
  void _internal_set_bmsbatvol(float value);
  public:

  // float volDemand = 8;
  void clear_voldemand();
  float voldemand() const;
  void set_voldemand(float value);
  private:
  float _internal_voldemand() const;
  void _internal_set_voldemand(float value);
  public:

  // float curDemand = 9;
  void clear_curdemand();
  float curdemand() const;
  void set_curdemand(float value);
  private:
  float _internal_curdemand() const;
  void _internal_set_curdemand(float value);
  public:

  // float socNow = 10;
  void clear_socnow();
  float socnow() const;
  void set_socnow(float value);
  private:
  float _internal_socnow() const;
  void _internal_set_socnow(float value);
  public:

  // float currOutCapacity = 11;
  void clear_curroutcapacity();
  float curroutcapacity() const;
  void set_curroutcapacity(float value);
  private:
  float _internal_curroutcapacity() const;
  void _internal_set_curroutcapacity(float value);
  public:

  // float volMeasured = 12;
  void clear_volmeasured();
  float volmeasured() const;
  void set_volmeasured(float value);
  private:
  float _internal_volmeasured() const;
  void _internal_set_volmeasured(float value);
  public:

  // float curMeasured = 13;
  void clear_curmeasured();
  float curmeasured() const;
  void set_curmeasured(float value);
  private:
  float _internal_curmeasured() const;
  void _internal_set_curmeasured(float value);
  public:

  // float monoBatVolMax = 14;
  void clear_monobatvolmax();
  float monobatvolmax() const;
  void set_monobatvolmax(float value);
  private:
  float _internal_monobatvolmax() const;
  void _internal_set_monobatvolmax(float value);
  public:

  // float monoBatVolMin = 15;
  void clear_monobatvolmin();
  float monobatvolmin() const;
  void set_monobatvolmin(float value);
  private:
  float _internal_monobatvolmin() const;
  void _internal_set_monobatvolmin(float value);
  public:

  // float tempMax = 16;
  void clear_tempmax();
  float tempmax() const;
  void set_tempmax(float value);
  private:
  float _internal_tempmax() const;
  void _internal_set_tempmax(float value);
  public:

  // float tempMin = 17;
  void clear_tempmin();
  float tempmin() const;
  void set_tempmin(float value);
  private:
  float _internal_tempmin() const;
  void _internal_set_tempmin(float value);
  public:

  // @@protoc_insertion_point(class_scope:BMS2015PlusInfo.BMSCharging)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsready_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chargerready_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chargemode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 resonforcapacitychange_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmspause_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chgpause_;
  float bmsbatvol_;
  float voldemand_;
  float curdemand_;
  float socnow_;
  float curroutcapacity_;
  float volmeasured_;
  float curmeasured_;
  float monobatvolmax_;
  float monobatvolmin_;
  float tempmax_;
  float tempmin_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fBMS2015P_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class bms2015pMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:BMS2015PlusInfo.bms2015pMsg) */ {
 public:
  inline bms2015pMsg() : bms2015pMsg(nullptr) {}
  ~bms2015pMsg() override;
  explicit constexpr bms2015pMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  bms2015pMsg(const bms2015pMsg& from);
  bms2015pMsg(bms2015pMsg&& from) noexcept
    : bms2015pMsg() {
    *this = ::std::move(from);
  }

  inline bms2015pMsg& operator=(const bms2015pMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline bms2015pMsg& operator=(bms2015pMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const bms2015pMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const bms2015pMsg* internal_default_instance() {
    return reinterpret_cast<const bms2015pMsg*>(
               &_bms2015pMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(bms2015pMsg& a, bms2015pMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(bms2015pMsg* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(bms2015pMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline bms2015pMsg* New() const final {
    return new bms2015pMsg();
  }

  bms2015pMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<bms2015pMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const bms2015pMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const bms2015pMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(bms2015pMsg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "BMS2015PlusInfo.bms2015pMsg";
  }
  protected:
  explicit bms2015pMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBmsProtoConferFieldNumber = 2,
    kBmsFunConferFieldNumber = 3,
    kBmsConfgMFieldNumber = 4,
    kBmsChargingMFieldNumber = 5,
    kBmsChargeFinishMFieldNumber = 6,
    kBmsAuthenMFieldNumber = 7,
    kBmsReserveMFieldNumber = 8,
    kBmsSelfCheckMFieldNumber = 9,
    kBmsVehicelStateMFieldNumber = 10,
    kPowerSupplyMFieldNumber = 11,
    kBmsStateFieldNumber = 1,
  };
  // .BMS2015PlusInfo.ProtoConferMsg bmsProtoConfer = 2;
  bool has_bmsprotoconfer() const;
  private:
  bool _internal_has_bmsprotoconfer() const;
  public:
  void clear_bmsprotoconfer();
  const ::BMS2015PlusInfo::ProtoConferMsg& bmsprotoconfer() const;
  PROTOBUF_MUST_USE_RESULT ::BMS2015PlusInfo::ProtoConferMsg* release_bmsprotoconfer();
  ::BMS2015PlusInfo::ProtoConferMsg* mutable_bmsprotoconfer();
  void set_allocated_bmsprotoconfer(::BMS2015PlusInfo::ProtoConferMsg* bmsprotoconfer);
  private:
  const ::BMS2015PlusInfo::ProtoConferMsg& _internal_bmsprotoconfer() const;
  ::BMS2015PlusInfo::ProtoConferMsg* _internal_mutable_bmsprotoconfer();
  public:
  void unsafe_arena_set_allocated_bmsprotoconfer(
      ::BMS2015PlusInfo::ProtoConferMsg* bmsprotoconfer);
  ::BMS2015PlusInfo::ProtoConferMsg* unsafe_arena_release_bmsprotoconfer();

  // .BMS2015PlusInfo.FunConferMsg bmsFunConfer = 3;
  bool has_bmsfunconfer() const;
  private:
  bool _internal_has_bmsfunconfer() const;
  public:
  void clear_bmsfunconfer();
  const ::BMS2015PlusInfo::FunConferMsg& bmsfunconfer() const;
  PROTOBUF_MUST_USE_RESULT ::BMS2015PlusInfo::FunConferMsg* release_bmsfunconfer();
  ::BMS2015PlusInfo::FunConferMsg* mutable_bmsfunconfer();
  void set_allocated_bmsfunconfer(::BMS2015PlusInfo::FunConferMsg* bmsfunconfer);
  private:
  const ::BMS2015PlusInfo::FunConferMsg& _internal_bmsfunconfer() const;
  ::BMS2015PlusInfo::FunConferMsg* _internal_mutable_bmsfunconfer();
  public:
  void unsafe_arena_set_allocated_bmsfunconfer(
      ::BMS2015PlusInfo::FunConferMsg* bmsfunconfer);
  ::BMS2015PlusInfo::FunConferMsg* unsafe_arena_release_bmsfunconfer();

  // .BMS2015PlusInfo.BMSConfig BmsConfgM = 4;
  bool has_bmsconfgm() const;
  private:
  bool _internal_has_bmsconfgm() const;
  public:
  void clear_bmsconfgm();
  const ::BMS2015PlusInfo::BMSConfig& bmsconfgm() const;
  PROTOBUF_MUST_USE_RESULT ::BMS2015PlusInfo::BMSConfig* release_bmsconfgm();
  ::BMS2015PlusInfo::BMSConfig* mutable_bmsconfgm();
  void set_allocated_bmsconfgm(::BMS2015PlusInfo::BMSConfig* bmsconfgm);
  private:
  const ::BMS2015PlusInfo::BMSConfig& _internal_bmsconfgm() const;
  ::BMS2015PlusInfo::BMSConfig* _internal_mutable_bmsconfgm();
  public:
  void unsafe_arena_set_allocated_bmsconfgm(
      ::BMS2015PlusInfo::BMSConfig* bmsconfgm);
  ::BMS2015PlusInfo::BMSConfig* unsafe_arena_release_bmsconfgm();

  // .BMS2015PlusInfo.BMSCharging BmsChargingM = 5;
  bool has_bmschargingm() const;
  private:
  bool _internal_has_bmschargingm() const;
  public:
  void clear_bmschargingm();
  const ::BMS2015PlusInfo::BMSCharging& bmschargingm() const;
  PROTOBUF_MUST_USE_RESULT ::BMS2015PlusInfo::BMSCharging* release_bmschargingm();
  ::BMS2015PlusInfo::BMSCharging* mutable_bmschargingm();
  void set_allocated_bmschargingm(::BMS2015PlusInfo::BMSCharging* bmschargingm);
  private:
  const ::BMS2015PlusInfo::BMSCharging& _internal_bmschargingm() const;
  ::BMS2015PlusInfo::BMSCharging* _internal_mutable_bmschargingm();
  public:
  void unsafe_arena_set_allocated_bmschargingm(
      ::BMS2015PlusInfo::BMSCharging* bmschargingm);
  ::BMS2015PlusInfo::BMSCharging* unsafe_arena_release_bmschargingm();

  // .BMS2015PlusInfo.BMSChargingEnd BmsChargeFinishM = 6;
  bool has_bmschargefinishm() const;
  private:
  bool _internal_has_bmschargefinishm() const;
  public:
  void clear_bmschargefinishm();
  const ::BMS2015PlusInfo::BMSChargingEnd& bmschargefinishm() const;
  PROTOBUF_MUST_USE_RESULT ::BMS2015PlusInfo::BMSChargingEnd* release_bmschargefinishm();
  ::BMS2015PlusInfo::BMSChargingEnd* mutable_bmschargefinishm();
  void set_allocated_bmschargefinishm(::BMS2015PlusInfo::BMSChargingEnd* bmschargefinishm);
  private:
  const ::BMS2015PlusInfo::BMSChargingEnd& _internal_bmschargefinishm() const;
  ::BMS2015PlusInfo::BMSChargingEnd* _internal_mutable_bmschargefinishm();
  public:
  void unsafe_arena_set_allocated_bmschargefinishm(
      ::BMS2015PlusInfo::BMSChargingEnd* bmschargefinishm);
  ::BMS2015PlusInfo::BMSChargingEnd* unsafe_arena_release_bmschargefinishm();

  // .BMS2015PlusInfo.AuthenMsg BmsAuthenM = 7;
  bool has_bmsauthenm() const;
  private:
  bool _internal_has_bmsauthenm() const;
  public:
  void clear_bmsauthenm();
  const ::BMS2015PlusInfo::AuthenMsg& bmsauthenm() const;
  PROTOBUF_MUST_USE_RESULT ::BMS2015PlusInfo::AuthenMsg* release_bmsauthenm();
  ::BMS2015PlusInfo::AuthenMsg* mutable_bmsauthenm();
  void set_allocated_bmsauthenm(::BMS2015PlusInfo::AuthenMsg* bmsauthenm);
  private:
  const ::BMS2015PlusInfo::AuthenMsg& _internal_bmsauthenm() const;
  ::BMS2015PlusInfo::AuthenMsg* _internal_mutable_bmsauthenm();
  public:
  void unsafe_arena_set_allocated_bmsauthenm(
      ::BMS2015PlusInfo::AuthenMsg* bmsauthenm);
  ::BMS2015PlusInfo::AuthenMsg* unsafe_arena_release_bmsauthenm();

  // .BMS2015PlusInfo.ReserveMsg BmsReserveM = 8;
  bool has_bmsreservem() const;
  private:
  bool _internal_has_bmsreservem() const;
  public:
  void clear_bmsreservem();
  const ::BMS2015PlusInfo::ReserveMsg& bmsreservem() const;
  PROTOBUF_MUST_USE_RESULT ::BMS2015PlusInfo::ReserveMsg* release_bmsreservem();
  ::BMS2015PlusInfo::ReserveMsg* mutable_bmsreservem();
  void set_allocated_bmsreservem(::BMS2015PlusInfo::ReserveMsg* bmsreservem);
  private:
  const ::BMS2015PlusInfo::ReserveMsg& _internal_bmsreservem() const;
  ::BMS2015PlusInfo::ReserveMsg* _internal_mutable_bmsreservem();
  public:
  void unsafe_arena_set_allocated_bmsreservem(
      ::BMS2015PlusInfo::ReserveMsg* bmsreservem);
  ::BMS2015PlusInfo::ReserveMsg* unsafe_arena_release_bmsreservem();

  // .BMS2015PlusInfo.SelfcheckMsg BmsSelfCheckM = 9;
  bool has_bmsselfcheckm() const;
  private:
  bool _internal_has_bmsselfcheckm() const;
  public:
  void clear_bmsselfcheckm();
  const ::BMS2015PlusInfo::SelfcheckMsg& bmsselfcheckm() const;
  PROTOBUF_MUST_USE_RESULT ::BMS2015PlusInfo::SelfcheckMsg* release_bmsselfcheckm();
  ::BMS2015PlusInfo::SelfcheckMsg* mutable_bmsselfcheckm();
  void set_allocated_bmsselfcheckm(::BMS2015PlusInfo::SelfcheckMsg* bmsselfcheckm);
  private:
  const ::BMS2015PlusInfo::SelfcheckMsg& _internal_bmsselfcheckm() const;
  ::BMS2015PlusInfo::SelfcheckMsg* _internal_mutable_bmsselfcheckm();
  public:
  void unsafe_arena_set_allocated_bmsselfcheckm(
      ::BMS2015PlusInfo::SelfcheckMsg* bmsselfcheckm);
  ::BMS2015PlusInfo::SelfcheckMsg* unsafe_arena_release_bmsselfcheckm();

  // .BMS2015PlusInfo.VehicelState BmsVehicelStateM = 10;
  bool has_bmsvehicelstatem() const;
  private:
  bool _internal_has_bmsvehicelstatem() const;
  public:
  void clear_bmsvehicelstatem();
  const ::BMS2015PlusInfo::VehicelState& bmsvehicelstatem() const;
  PROTOBUF_MUST_USE_RESULT ::BMS2015PlusInfo::VehicelState* release_bmsvehicelstatem();
  ::BMS2015PlusInfo::VehicelState* mutable_bmsvehicelstatem();
  void set_allocated_bmsvehicelstatem(::BMS2015PlusInfo::VehicelState* bmsvehicelstatem);
  private:
  const ::BMS2015PlusInfo::VehicelState& _internal_bmsvehicelstatem() const;
  ::BMS2015PlusInfo::VehicelState* _internal_mutable_bmsvehicelstatem();
  public:
  void unsafe_arena_set_allocated_bmsvehicelstatem(
      ::BMS2015PlusInfo::VehicelState* bmsvehicelstatem);
  ::BMS2015PlusInfo::VehicelState* unsafe_arena_release_bmsvehicelstatem();

  // .BMS2015PlusInfo.PowerSupplyMsg PowerSupplyM = 11;
  bool has_powersupplym() const;
  private:
  bool _internal_has_powersupplym() const;
  public:
  void clear_powersupplym();
  const ::BMS2015PlusInfo::PowerSupplyMsg& powersupplym() const;
  PROTOBUF_MUST_USE_RESULT ::BMS2015PlusInfo::PowerSupplyMsg* release_powersupplym();
  ::BMS2015PlusInfo::PowerSupplyMsg* mutable_powersupplym();
  void set_allocated_powersupplym(::BMS2015PlusInfo::PowerSupplyMsg* powersupplym);
  private:
  const ::BMS2015PlusInfo::PowerSupplyMsg& _internal_powersupplym() const;
  ::BMS2015PlusInfo::PowerSupplyMsg* _internal_mutable_powersupplym();
  public:
  void unsafe_arena_set_allocated_powersupplym(
      ::BMS2015PlusInfo::PowerSupplyMsg* powersupplym);
  ::BMS2015PlusInfo::PowerSupplyMsg* unsafe_arena_release_powersupplym();

  // .BMS2015PlusInfo.ChargeState bmsState = 1;
  void clear_bmsstate();
  ::BMS2015PlusInfo::ChargeState bmsstate() const;
  void set_bmsstate(::BMS2015PlusInfo::ChargeState value);
  private:
  ::BMS2015PlusInfo::ChargeState _internal_bmsstate() const;
  void _internal_set_bmsstate(::BMS2015PlusInfo::ChargeState value);
  public:

  // @@protoc_insertion_point(class_scope:BMS2015PlusInfo.bms2015pMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::BMS2015PlusInfo::ProtoConferMsg* bmsprotoconfer_;
  ::BMS2015PlusInfo::FunConferMsg* bmsfunconfer_;
  ::BMS2015PlusInfo::BMSConfig* bmsconfgm_;
  ::BMS2015PlusInfo::BMSCharging* bmschargingm_;
  ::BMS2015PlusInfo::BMSChargingEnd* bmschargefinishm_;
  ::BMS2015PlusInfo::AuthenMsg* bmsauthenm_;
  ::BMS2015PlusInfo::ReserveMsg* bmsreservem_;
  ::BMS2015PlusInfo::SelfcheckMsg* bmsselfcheckm_;
  ::BMS2015PlusInfo::VehicelState* bmsvehicelstatem_;
  ::BMS2015PlusInfo::PowerSupplyMsg* powersupplym_;
  int bmsstate_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fBMS2015P_5fINFO_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ProtoConferMsg

// uint32 chargerCanType = 1;
inline void ProtoConferMsg::clear_chargercantype() {
  chargercantype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ProtoConferMsg::_internal_chargercantype() const {
  return chargercantype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ProtoConferMsg::chargercantype() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.ProtoConferMsg.chargerCanType)
  return _internal_chargercantype();
}
inline void ProtoConferMsg::_internal_set_chargercantype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chargercantype_ = value;
}
inline void ProtoConferMsg::set_chargercantype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chargercantype(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.ProtoConferMsg.chargerCanType)
}

// uint32 bmsCanType = 2;
inline void ProtoConferMsg::clear_bmscantype() {
  bmscantype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ProtoConferMsg::_internal_bmscantype() const {
  return bmscantype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ProtoConferMsg::bmscantype() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.ProtoConferMsg.bmsCanType)
  return _internal_bmscantype();
}
inline void ProtoConferMsg::_internal_set_bmscantype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmscantype_ = value;
}
inline void ProtoConferMsg::set_bmscantype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmscantype(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.ProtoConferMsg.bmsCanType)
}

// uint32 chargerConferResult = 3;
inline void ProtoConferMsg::clear_chargerconferresult() {
  chargerconferresult_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ProtoConferMsg::_internal_chargerconferresult() const {
  return chargerconferresult_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ProtoConferMsg::chargerconferresult() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.ProtoConferMsg.chargerConferResult)
  return _internal_chargerconferresult();
}
inline void ProtoConferMsg::_internal_set_chargerconferresult(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chargerconferresult_ = value;
}
inline void ProtoConferMsg::set_chargerconferresult(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chargerconferresult(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.ProtoConferMsg.chargerConferResult)
}

// uint32 EVConferResult = 4;
inline void ProtoConferMsg::clear_evconferresult() {
  evconferresult_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ProtoConferMsg::_internal_evconferresult() const {
  return evconferresult_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ProtoConferMsg::evconferresult() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.ProtoConferMsg.EVConferResult)
  return _internal_evconferresult();
}
inline void ProtoConferMsg::_internal_set_evconferresult(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  evconferresult_ = value;
}
inline void ProtoConferMsg::set_evconferresult(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_evconferresult(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.ProtoConferMsg.EVConferResult)
}

// bytes chgGbVersion = 5;
inline void ProtoConferMsg::clear_chggbversion() {
  chggbversion_.ClearToEmpty();
}
inline const std::string& ProtoConferMsg::chggbversion() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.ProtoConferMsg.chgGbVersion)
  return _internal_chggbversion();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProtoConferMsg::set_chggbversion(ArgT0&& arg0, ArgT... args) {
 
 chggbversion_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.ProtoConferMsg.chgGbVersion)
}
inline std::string* ProtoConferMsg::mutable_chggbversion() {
  std::string* _s = _internal_mutable_chggbversion();
  // @@protoc_insertion_point(field_mutable:BMS2015PlusInfo.ProtoConferMsg.chgGbVersion)
  return _s;
}
inline const std::string& ProtoConferMsg::_internal_chggbversion() const {
  return chggbversion_.Get();
}
inline void ProtoConferMsg::_internal_set_chggbversion(const std::string& value) {
  
  chggbversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ProtoConferMsg::_internal_mutable_chggbversion() {
  
  return chggbversion_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ProtoConferMsg::release_chggbversion() {
  // @@protoc_insertion_point(field_release:BMS2015PlusInfo.ProtoConferMsg.chgGbVersion)
  return chggbversion_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ProtoConferMsg::set_allocated_chggbversion(std::string* chggbversion) {
  if (chggbversion != nullptr) {
    
  } else {
    
  }
  chggbversion_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), chggbversion,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:BMS2015PlusInfo.ProtoConferMsg.chgGbVersion)
}

// bytes EvGbVersion = 6;
inline void ProtoConferMsg::clear_evgbversion() {
  evgbversion_.ClearToEmpty();
}
inline const std::string& ProtoConferMsg::evgbversion() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.ProtoConferMsg.EvGbVersion)
  return _internal_evgbversion();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProtoConferMsg::set_evgbversion(ArgT0&& arg0, ArgT... args) {
 
 evgbversion_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.ProtoConferMsg.EvGbVersion)
}
inline std::string* ProtoConferMsg::mutable_evgbversion() {
  std::string* _s = _internal_mutable_evgbversion();
  // @@protoc_insertion_point(field_mutable:BMS2015PlusInfo.ProtoConferMsg.EvGbVersion)
  return _s;
}
inline const std::string& ProtoConferMsg::_internal_evgbversion() const {
  return evgbversion_.Get();
}
inline void ProtoConferMsg::_internal_set_evgbversion(const std::string& value) {
  
  evgbversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ProtoConferMsg::_internal_mutable_evgbversion() {
  
  return evgbversion_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ProtoConferMsg::release_evgbversion() {
  // @@protoc_insertion_point(field_release:BMS2015PlusInfo.ProtoConferMsg.EvGbVersion)
  return evgbversion_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ProtoConferMsg::set_allocated_evgbversion(std::string* evgbversion) {
  if (evgbversion != nullptr) {
    
  } else {
    
  }
  evgbversion_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), evgbversion,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:BMS2015PlusInfo.ProtoConferMsg.EvGbVersion)
}

// bytes chargerGuidanceVersion = 7;
inline void ProtoConferMsg::clear_chargerguidanceversion() {
  chargerguidanceversion_.ClearToEmpty();
}
inline const std::string& ProtoConferMsg::chargerguidanceversion() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.ProtoConferMsg.chargerGuidanceVersion)
  return _internal_chargerguidanceversion();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProtoConferMsg::set_chargerguidanceversion(ArgT0&& arg0, ArgT... args) {
 
 chargerguidanceversion_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.ProtoConferMsg.chargerGuidanceVersion)
}
inline std::string* ProtoConferMsg::mutable_chargerguidanceversion() {
  std::string* _s = _internal_mutable_chargerguidanceversion();
  // @@protoc_insertion_point(field_mutable:BMS2015PlusInfo.ProtoConferMsg.chargerGuidanceVersion)
  return _s;
}
inline const std::string& ProtoConferMsg::_internal_chargerguidanceversion() const {
  return chargerguidanceversion_.Get();
}
inline void ProtoConferMsg::_internal_set_chargerguidanceversion(const std::string& value) {
  
  chargerguidanceversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ProtoConferMsg::_internal_mutable_chargerguidanceversion() {
  
  return chargerguidanceversion_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ProtoConferMsg::release_chargerguidanceversion() {
  // @@protoc_insertion_point(field_release:BMS2015PlusInfo.ProtoConferMsg.chargerGuidanceVersion)
  return chargerguidanceversion_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ProtoConferMsg::set_allocated_chargerguidanceversion(std::string* chargerguidanceversion) {
  if (chargerguidanceversion != nullptr) {
    
  } else {
    
  }
  chargerguidanceversion_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), chargerguidanceversion,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:BMS2015PlusInfo.ProtoConferMsg.chargerGuidanceVersion)
}

// bytes chargerTransportVersion = 8;
inline void ProtoConferMsg::clear_chargertransportversion() {
  chargertransportversion_.ClearToEmpty();
}
inline const std::string& ProtoConferMsg::chargertransportversion() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.ProtoConferMsg.chargerTransportVersion)
  return _internal_chargertransportversion();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProtoConferMsg::set_chargertransportversion(ArgT0&& arg0, ArgT... args) {
 
 chargertransportversion_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.ProtoConferMsg.chargerTransportVersion)
}
inline std::string* ProtoConferMsg::mutable_chargertransportversion() {
  std::string* _s = _internal_mutable_chargertransportversion();
  // @@protoc_insertion_point(field_mutable:BMS2015PlusInfo.ProtoConferMsg.chargerTransportVersion)
  return _s;
}
inline const std::string& ProtoConferMsg::_internal_chargertransportversion() const {
  return chargertransportversion_.Get();
}
inline void ProtoConferMsg::_internal_set_chargertransportversion(const std::string& value) {
  
  chargertransportversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ProtoConferMsg::_internal_mutable_chargertransportversion() {
  
  return chargertransportversion_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ProtoConferMsg::release_chargertransportversion() {
  // @@protoc_insertion_point(field_release:BMS2015PlusInfo.ProtoConferMsg.chargerTransportVersion)
  return chargertransportversion_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ProtoConferMsg::set_allocated_chargertransportversion(std::string* chargertransportversion) {
  if (chargertransportversion != nullptr) {
    
  } else {
    
  }
  chargertransportversion_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), chargertransportversion,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:BMS2015PlusInfo.ProtoConferMsg.chargerTransportVersion)
}

// bytes bmsGuidanceVersion = 9;
inline void ProtoConferMsg::clear_bmsguidanceversion() {
  bmsguidanceversion_.ClearToEmpty();
}
inline const std::string& ProtoConferMsg::bmsguidanceversion() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.ProtoConferMsg.bmsGuidanceVersion)
  return _internal_bmsguidanceversion();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProtoConferMsg::set_bmsguidanceversion(ArgT0&& arg0, ArgT... args) {
 
 bmsguidanceversion_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.ProtoConferMsg.bmsGuidanceVersion)
}
inline std::string* ProtoConferMsg::mutable_bmsguidanceversion() {
  std::string* _s = _internal_mutable_bmsguidanceversion();
  // @@protoc_insertion_point(field_mutable:BMS2015PlusInfo.ProtoConferMsg.bmsGuidanceVersion)
  return _s;
}
inline const std::string& ProtoConferMsg::_internal_bmsguidanceversion() const {
  return bmsguidanceversion_.Get();
}
inline void ProtoConferMsg::_internal_set_bmsguidanceversion(const std::string& value) {
  
  bmsguidanceversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ProtoConferMsg::_internal_mutable_bmsguidanceversion() {
  
  return bmsguidanceversion_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ProtoConferMsg::release_bmsguidanceversion() {
  // @@protoc_insertion_point(field_release:BMS2015PlusInfo.ProtoConferMsg.bmsGuidanceVersion)
  return bmsguidanceversion_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ProtoConferMsg::set_allocated_bmsguidanceversion(std::string* bmsguidanceversion) {
  if (bmsguidanceversion != nullptr) {
    
  } else {
    
  }
  bmsguidanceversion_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), bmsguidanceversion,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:BMS2015PlusInfo.ProtoConferMsg.bmsGuidanceVersion)
}

// bytes bmsTransportVersion = 10;
inline void ProtoConferMsg::clear_bmstransportversion() {
  bmstransportversion_.ClearToEmpty();
}
inline const std::string& ProtoConferMsg::bmstransportversion() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.ProtoConferMsg.bmsTransportVersion)
  return _internal_bmstransportversion();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProtoConferMsg::set_bmstransportversion(ArgT0&& arg0, ArgT... args) {
 
 bmstransportversion_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.ProtoConferMsg.bmsTransportVersion)
}
inline std::string* ProtoConferMsg::mutable_bmstransportversion() {
  std::string* _s = _internal_mutable_bmstransportversion();
  // @@protoc_insertion_point(field_mutable:BMS2015PlusInfo.ProtoConferMsg.bmsTransportVersion)
  return _s;
}
inline const std::string& ProtoConferMsg::_internal_bmstransportversion() const {
  return bmstransportversion_.Get();
}
inline void ProtoConferMsg::_internal_set_bmstransportversion(const std::string& value) {
  
  bmstransportversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ProtoConferMsg::_internal_mutable_bmstransportversion() {
  
  return bmstransportversion_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ProtoConferMsg::release_bmstransportversion() {
  // @@protoc_insertion_point(field_release:BMS2015PlusInfo.ProtoConferMsg.bmsTransportVersion)
  return bmstransportversion_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ProtoConferMsg::set_allocated_bmstransportversion(std::string* bmstransportversion) {
  if (bmstransportversion != nullptr) {
    
  } else {
    
  }
  bmstransportversion_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), bmstransportversion,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:BMS2015PlusInfo.ProtoConferMsg.bmsTransportVersion)
}

// -------------------------------------------------------------------

// FunConferMsg

// uint32 chgConfigFDC = 1;
inline void FunConferMsg::clear_chgconfigfdc() {
  chgconfigfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::_internal_chgconfigfdc() const {
  return chgconfigfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::chgconfigfdc() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.FunConferMsg.chgConfigFDC)
  return _internal_chgconfigfdc();
}
inline void FunConferMsg::_internal_set_chgconfigfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chgconfigfdc_ = value;
}
inline void FunConferMsg::set_chgconfigfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chgconfigfdc(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.FunConferMsg.chgConfigFDC)
}

// uint32 chgAuthenFDC = 2;
inline void FunConferMsg::clear_chgauthenfdc() {
  chgauthenfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::_internal_chgauthenfdc() const {
  return chgauthenfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::chgauthenfdc() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.FunConferMsg.chgAuthenFDC)
  return _internal_chgauthenfdc();
}
inline void FunConferMsg::_internal_set_chgauthenfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chgauthenfdc_ = value;
}
inline void FunConferMsg::set_chgauthenfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chgauthenfdc(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.FunConferMsg.chgAuthenFDC)
}

// uint32 chgAppointFDC = 3;
inline void FunConferMsg::clear_chgappointfdc() {
  chgappointfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::_internal_chgappointfdc() const {
  return chgappointfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::chgappointfdc() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.FunConferMsg.chgAppointFDC)
  return _internal_chgappointfdc();
}
inline void FunConferMsg::_internal_set_chgappointfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chgappointfdc_ = value;
}
inline void FunConferMsg::set_chgappointfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chgappointfdc(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.FunConferMsg.chgAppointFDC)
}

// uint32 chgSelfCheckFDC = 4;
inline void FunConferMsg::clear_chgselfcheckfdc() {
  chgselfcheckfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::_internal_chgselfcheckfdc() const {
  return chgselfcheckfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::chgselfcheckfdc() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.FunConferMsg.chgSelfCheckFDC)
  return _internal_chgselfcheckfdc();
}
inline void FunConferMsg::_internal_set_chgselfcheckfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chgselfcheckfdc_ = value;
}
inline void FunConferMsg::set_chgselfcheckfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chgselfcheckfdc(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.FunConferMsg.chgSelfCheckFDC)
}

// uint32 chgPowerSupplyFDC = 5;
inline void FunConferMsg::clear_chgpowersupplyfdc() {
  chgpowersupplyfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::_internal_chgpowersupplyfdc() const {
  return chgpowersupplyfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::chgpowersupplyfdc() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.FunConferMsg.chgPowerSupplyFDC)
  return _internal_chgpowersupplyfdc();
}
inline void FunConferMsg::_internal_set_chgpowersupplyfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chgpowersupplyfdc_ = value;
}
inline void FunConferMsg::set_chgpowersupplyfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chgpowersupplyfdc(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.FunConferMsg.chgPowerSupplyFDC)
}

// uint32 chgEnergyTransferFDC = 6;
inline void FunConferMsg::clear_chgenergytransferfdc() {
  chgenergytransferfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::_internal_chgenergytransferfdc() const {
  return chgenergytransferfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::chgenergytransferfdc() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.FunConferMsg.chgEnergyTransferFDC)
  return _internal_chgenergytransferfdc();
}
inline void FunConferMsg::_internal_set_chgenergytransferfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chgenergytransferfdc_ = value;
}
inline void FunConferMsg::set_chgenergytransferfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chgenergytransferfdc(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.FunConferMsg.chgEnergyTransferFDC)
}

// uint32 chgEndFDC = 7;
inline void FunConferMsg::clear_chgendfdc() {
  chgendfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::_internal_chgendfdc() const {
  return chgendfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::chgendfdc() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.FunConferMsg.chgEndFDC)
  return _internal_chgendfdc();
}
inline void FunConferMsg::_internal_set_chgendfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chgendfdc_ = value;
}
inline void FunConferMsg::set_chgendfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chgendfdc(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.FunConferMsg.chgEndFDC)
}

// uint32 EVConfigFDC = 8;
inline void FunConferMsg::clear_evconfigfdc() {
  evconfigfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::_internal_evconfigfdc() const {
  return evconfigfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::evconfigfdc() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.FunConferMsg.EVConfigFDC)
  return _internal_evconfigfdc();
}
inline void FunConferMsg::_internal_set_evconfigfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  evconfigfdc_ = value;
}
inline void FunConferMsg::set_evconfigfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_evconfigfdc(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.FunConferMsg.EVConfigFDC)
}

// uint32 EVAuthenFDC = 9;
inline void FunConferMsg::clear_evauthenfdc() {
  evauthenfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::_internal_evauthenfdc() const {
  return evauthenfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::evauthenfdc() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.FunConferMsg.EVAuthenFDC)
  return _internal_evauthenfdc();
}
inline void FunConferMsg::_internal_set_evauthenfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  evauthenfdc_ = value;
}
inline void FunConferMsg::set_evauthenfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_evauthenfdc(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.FunConferMsg.EVAuthenFDC)
}

// uint32 EVAppointFDC = 10;
inline void FunConferMsg::clear_evappointfdc() {
  evappointfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::_internal_evappointfdc() const {
  return evappointfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::evappointfdc() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.FunConferMsg.EVAppointFDC)
  return _internal_evappointfdc();
}
inline void FunConferMsg::_internal_set_evappointfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  evappointfdc_ = value;
}
inline void FunConferMsg::set_evappointfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_evappointfdc(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.FunConferMsg.EVAppointFDC)
}

// uint32 EVSelfCheckFDC = 11;
inline void FunConferMsg::clear_evselfcheckfdc() {
  evselfcheckfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::_internal_evselfcheckfdc() const {
  return evselfcheckfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::evselfcheckfdc() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.FunConferMsg.EVSelfCheckFDC)
  return _internal_evselfcheckfdc();
}
inline void FunConferMsg::_internal_set_evselfcheckfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  evselfcheckfdc_ = value;
}
inline void FunConferMsg::set_evselfcheckfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_evselfcheckfdc(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.FunConferMsg.EVSelfCheckFDC)
}

// uint32 EVPowerSupplyFDC = 12;
inline void FunConferMsg::clear_evpowersupplyfdc() {
  evpowersupplyfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::_internal_evpowersupplyfdc() const {
  return evpowersupplyfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::evpowersupplyfdc() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.FunConferMsg.EVPowerSupplyFDC)
  return _internal_evpowersupplyfdc();
}
inline void FunConferMsg::_internal_set_evpowersupplyfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  evpowersupplyfdc_ = value;
}
inline void FunConferMsg::set_evpowersupplyfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_evpowersupplyfdc(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.FunConferMsg.EVPowerSupplyFDC)
}

// uint32 EVEnergyTransferFDC = 13;
inline void FunConferMsg::clear_evenergytransferfdc() {
  evenergytransferfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::_internal_evenergytransferfdc() const {
  return evenergytransferfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::evenergytransferfdc() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.FunConferMsg.EVEnergyTransferFDC)
  return _internal_evenergytransferfdc();
}
inline void FunConferMsg::_internal_set_evenergytransferfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  evenergytransferfdc_ = value;
}
inline void FunConferMsg::set_evenergytransferfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_evenergytransferfdc(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.FunConferMsg.EVEnergyTransferFDC)
}

// uint32 EVEndFDC = 14;
inline void FunConferMsg::clear_evendfdc() {
  evendfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::_internal_evendfdc() const {
  return evendfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FunConferMsg::evendfdc() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.FunConferMsg.EVEndFDC)
  return _internal_evendfdc();
}
inline void FunConferMsg::_internal_set_evendfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  evendfdc_ = value;
}
inline void FunConferMsg::set_evendfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_evendfdc(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.FunConferMsg.EVEndFDC)
}

// -------------------------------------------------------------------

// BMSConfig

// float monoVolMaxAllowed = 1;
inline void BMSConfig::clear_monovolmaxallowed() {
  monovolmaxallowed_ = 0;
}
inline float BMSConfig::_internal_monovolmaxallowed() const {
  return monovolmaxallowed_;
}
inline float BMSConfig::monovolmaxallowed() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSConfig.monoVolMaxAllowed)
  return _internal_monovolmaxallowed();
}
inline void BMSConfig::_internal_set_monovolmaxallowed(float value) {
  
  monovolmaxallowed_ = value;
}
inline void BMSConfig::set_monovolmaxallowed(float value) {
  _internal_set_monovolmaxallowed(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSConfig.monoVolMaxAllowed)
}

// float curAllowedMax = 2;
inline void BMSConfig::clear_curallowedmax() {
  curallowedmax_ = 0;
}
inline float BMSConfig::_internal_curallowedmax() const {
  return curallowedmax_;
}
inline float BMSConfig::curallowedmax() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSConfig.curAllowedMax)
  return _internal_curallowedmax();
}
inline void BMSConfig::_internal_set_curallowedmax(float value) {
  
  curallowedmax_ = value;
}
inline void BMSConfig::set_curallowedmax(float value) {
  _internal_set_curallowedmax(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSConfig.curAllowedMax)
}

// float energyAllowdMax = 3;
inline void BMSConfig::clear_energyallowdmax() {
  energyallowdmax_ = 0;
}
inline float BMSConfig::_internal_energyallowdmax() const {
  return energyallowdmax_;
}
inline float BMSConfig::energyallowdmax() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSConfig.energyAllowdMax)
  return _internal_energyallowdmax();
}
inline void BMSConfig::_internal_set_energyallowdmax(float value) {
  
  energyallowdmax_ = value;
}
inline void BMSConfig::set_energyallowdmax(float value) {
  _internal_set_energyallowdmax(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSConfig.energyAllowdMax)
}

// float volAllowedMax = 4;
inline void BMSConfig::clear_volallowedmax() {
  volallowedmax_ = 0;
}
inline float BMSConfig::_internal_volallowedmax() const {
  return volallowedmax_;
}
inline float BMSConfig::volallowedmax() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSConfig.volAllowedMax)
  return _internal_volallowedmax();
}
inline void BMSConfig::_internal_set_volallowedmax(float value) {
  
  volallowedmax_ = value;
}
inline void BMSConfig::set_volallowedmax(float value) {
  _internal_set_volallowedmax(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSConfig.volAllowedMax)
}

// float tempAllowedMax = 5;
inline void BMSConfig::clear_tempallowedmax() {
  tempallowedmax_ = 0;
}
inline float BMSConfig::_internal_tempallowedmax() const {
  return tempallowedmax_;
}
inline float BMSConfig::tempallowedmax() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSConfig.tempAllowedMax)
  return _internal_tempallowedmax();
}
inline void BMSConfig::_internal_set_tempallowedmax(float value) {
  
  tempallowedmax_ = value;
}
inline void BMSConfig::set_tempallowedmax(float value) {
  _internal_set_tempallowedmax(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSConfig.tempAllowedMax)
}

// float startSOC = 6;
inline void BMSConfig::clear_startsoc() {
  startsoc_ = 0;
}
inline float BMSConfig::_internal_startsoc() const {
  return startsoc_;
}
inline float BMSConfig::startsoc() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSConfig.startSOC)
  return _internal_startsoc();
}
inline void BMSConfig::_internal_set_startsoc(float value) {
  
  startsoc_ = value;
}
inline void BMSConfig::set_startsoc(float value) {
  _internal_set_startsoc(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSConfig.startSOC)
}

// float volBatNow = 7;
inline void BMSConfig::clear_volbatnow() {
  volbatnow_ = 0;
}
inline float BMSConfig::_internal_volbatnow() const {
  return volbatnow_;
}
inline float BMSConfig::volbatnow() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSConfig.volBatNow)
  return _internal_volbatnow();
}
inline void BMSConfig::_internal_set_volbatnow(float value) {
  
  volbatnow_ = value;
}
inline void BMSConfig::set_volbatnow(float value) {
  _internal_set_volbatnow(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSConfig.volBatNow)
}

// float volChargerMax = 8;
inline void BMSConfig::clear_volchargermax() {
  volchargermax_ = 0;
}
inline float BMSConfig::_internal_volchargermax() const {
  return volchargermax_;
}
inline float BMSConfig::volchargermax() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSConfig.volChargerMax)
  return _internal_volchargermax();
}
inline void BMSConfig::_internal_set_volchargermax(float value) {
  
  volchargermax_ = value;
}
inline void BMSConfig::set_volchargermax(float value) {
  _internal_set_volchargermax(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSConfig.volChargerMax)
}

// float volChargerMin = 9;
inline void BMSConfig::clear_volchargermin() {
  volchargermin_ = 0;
}
inline float BMSConfig::_internal_volchargermin() const {
  return volchargermin_;
}
inline float BMSConfig::volchargermin() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSConfig.volChargerMin)
  return _internal_volchargermin();
}
inline void BMSConfig::_internal_set_volchargermin(float value) {
  
  volchargermin_ = value;
}
inline void BMSConfig::set_volchargermin(float value) {
  _internal_set_volchargermin(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSConfig.volChargerMin)
}

// float curChargerMax = 10;
inline void BMSConfig::clear_curchargermax() {
  curchargermax_ = 0;
}
inline float BMSConfig::_internal_curchargermax() const {
  return curchargermax_;
}
inline float BMSConfig::curchargermax() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSConfig.curChargerMax)
  return _internal_curchargermax();
}
inline void BMSConfig::_internal_set_curchargermax(float value) {
  
  curchargermax_ = value;
}
inline void BMSConfig::set_curchargermax(float value) {
  _internal_set_curchargermax(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSConfig.curChargerMax)
}

// float curChargerMin = 11;
inline void BMSConfig::clear_curchargermin() {
  curchargermin_ = 0;
}
inline float BMSConfig::_internal_curchargermin() const {
  return curchargermin_;
}
inline float BMSConfig::curchargermin() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSConfig.curChargerMin)
  return _internal_curchargermin();
}
inline void BMSConfig::_internal_set_curchargermin(float value) {
  
  curchargermin_ = value;
}
inline void BMSConfig::set_curchargermin(float value) {
  _internal_set_curchargermin(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSConfig.curChargerMin)
}

// uint32 currUpRate = 12;
inline void BMSConfig::clear_curruprate() {
  curruprate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSConfig::_internal_curruprate() const {
  return curruprate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSConfig::curruprate() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSConfig.currUpRate)
  return _internal_curruprate();
}
inline void BMSConfig::_internal_set_curruprate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  curruprate_ = value;
}
inline void BMSConfig::set_curruprate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_curruprate(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSConfig.currUpRate)
}

// uint32 currDownRate = 13;
inline void BMSConfig::clear_currdownrate() {
  currdownrate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSConfig::_internal_currdownrate() const {
  return currdownrate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSConfig::currdownrate() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSConfig.currDownRate)
  return _internal_currdownrate();
}
inline void BMSConfig::_internal_set_currdownrate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  currdownrate_ = value;
}
inline void BMSConfig::set_currdownrate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_currdownrate(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSConfig.currDownRate)
}

// uint32 chargerAllowedRestartNum = 14;
inline void BMSConfig::clear_chargerallowedrestartnum() {
  chargerallowedrestartnum_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSConfig::_internal_chargerallowedrestartnum() const {
  return chargerallowedrestartnum_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSConfig::chargerallowedrestartnum() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSConfig.chargerAllowedRestartNum)
  return _internal_chargerallowedrestartnum();
}
inline void BMSConfig::_internal_set_chargerallowedrestartnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chargerallowedrestartnum_ = value;
}
inline void BMSConfig::set_chargerallowedrestartnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chargerallowedrestartnum(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSConfig.chargerAllowedRestartNum)
}

// uint32 bmsAllowedRestartNum = 15;
inline void BMSConfig::clear_bmsallowedrestartnum() {
  bmsallowedrestartnum_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSConfig::_internal_bmsallowedrestartnum() const {
  return bmsallowedrestartnum_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSConfig::bmsallowedrestartnum() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSConfig.bmsAllowedRestartNum)
  return _internal_bmsallowedrestartnum();
}
inline void BMSConfig::_internal_set_bmsallowedrestartnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsallowedrestartnum_ = value;
}
inline void BMSConfig::set_bmsallowedrestartnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsallowedrestartnum(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSConfig.bmsAllowedRestartNum)
}

// -------------------------------------------------------------------

// DischargeConfig

// float volDischargeMax = 1;
inline void DischargeConfig::clear_voldischargemax() {
  voldischargemax_ = 0;
}
inline float DischargeConfig::_internal_voldischargemax() const {
  return voldischargemax_;
}
inline float DischargeConfig::voldischargemax() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.DischargeConfig.volDischargeMax)
  return _internal_voldischargemax();
}
inline void DischargeConfig::_internal_set_voldischargemax(float value) {
  
  voldischargemax_ = value;
}
inline void DischargeConfig::set_voldischargemax(float value) {
  _internal_set_voldischargemax(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.DischargeConfig.volDischargeMax)
}

// float volDischargeMin = 2;
inline void DischargeConfig::clear_voldischargemin() {
  voldischargemin_ = 0;
}
inline float DischargeConfig::_internal_voldischargemin() const {
  return voldischargemin_;
}
inline float DischargeConfig::voldischargemin() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.DischargeConfig.volDischargeMin)
  return _internal_voldischargemin();
}
inline void DischargeConfig::_internal_set_voldischargemin(float value) {
  
  voldischargemin_ = value;
}
inline void DischargeConfig::set_voldischargemin(float value) {
  _internal_set_voldischargemin(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.DischargeConfig.volDischargeMin)
}

// float curDischargeMax = 3;
inline void DischargeConfig::clear_curdischargemax() {
  curdischargemax_ = 0;
}
inline float DischargeConfig::_internal_curdischargemax() const {
  return curdischargemax_;
}
inline float DischargeConfig::curdischargemax() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.DischargeConfig.curDischargeMax)
  return _internal_curdischargemax();
}
inline void DischargeConfig::_internal_set_curdischargemax(float value) {
  
  curdischargemax_ = value;
}
inline void DischargeConfig::set_curdischargemax(float value) {
  _internal_set_curdischargemax(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.DischargeConfig.curDischargeMax)
}

// float curDischargeMin = 4;
inline void DischargeConfig::clear_curdischargemin() {
  curdischargemin_ = 0;
}
inline float DischargeConfig::_internal_curdischargemin() const {
  return curdischargemin_;
}
inline float DischargeConfig::curdischargemin() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.DischargeConfig.curDischargeMin)
  return _internal_curdischargemin();
}
inline void DischargeConfig::_internal_set_curdischargemin(float value) {
  
  curdischargemin_ = value;
}
inline void DischargeConfig::set_curdischargemin(float value) {
  _internal_set_curdischargemin(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.DischargeConfig.curDischargeMin)
}

// float volBmsAllowedMin = 5;
inline void DischargeConfig::clear_volbmsallowedmin() {
  volbmsallowedmin_ = 0;
}
inline float DischargeConfig::_internal_volbmsallowedmin() const {
  return volbmsallowedmin_;
}
inline float DischargeConfig::volbmsallowedmin() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.DischargeConfig.volBmsAllowedMin)
  return _internal_volbmsallowedmin();
}
inline void DischargeConfig::_internal_set_volbmsallowedmin(float value) {
  
  volbmsallowedmin_ = value;
}
inline void DischargeConfig::set_volbmsallowedmin(float value) {
  _internal_set_volbmsallowedmin(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.DischargeConfig.volBmsAllowedMin)
}

// float curBmsAllowedMax = 6;
inline void DischargeConfig::clear_curbmsallowedmax() {
  curbmsallowedmax_ = 0;
}
inline float DischargeConfig::_internal_curbmsallowedmax() const {
  return curbmsallowedmax_;
}
inline float DischargeConfig::curbmsallowedmax() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.DischargeConfig.curBmsAllowedMax)
  return _internal_curbmsallowedmax();
}
inline void DischargeConfig::_internal_set_curbmsallowedmax(float value) {
  
  curbmsallowedmax_ = value;
}
inline void DischargeConfig::set_curbmsallowedmax(float value) {
  _internal_set_curbmsallowedmax(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.DischargeConfig.curBmsAllowedMax)
}

// float curBmsAllowedMin = 7;
inline void DischargeConfig::clear_curbmsallowedmin() {
  curbmsallowedmin_ = 0;
}
inline float DischargeConfig::_internal_curbmsallowedmin() const {
  return curbmsallowedmin_;
}
inline float DischargeConfig::curbmsallowedmin() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.DischargeConfig.curBmsAllowedMin)
  return _internal_curbmsallowedmin();
}
inline void DischargeConfig::_internal_set_curbmsallowedmin(float value) {
  
  curbmsallowedmin_ = value;
}
inline void DischargeConfig::set_curbmsallowedmin(float value) {
  _internal_set_curbmsallowedmin(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.DischargeConfig.curBmsAllowedMin)
}

// float volCellAllowedMax = 8;
inline void DischargeConfig::clear_volcellallowedmax() {
  volcellallowedmax_ = 0;
}
inline float DischargeConfig::_internal_volcellallowedmax() const {
  return volcellallowedmax_;
}
inline float DischargeConfig::volcellallowedmax() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.DischargeConfig.volCellAllowedMax)
  return _internal_volcellallowedmax();
}
inline void DischargeConfig::_internal_set_volcellallowedmax(float value) {
  
  volcellallowedmax_ = value;
}
inline void DischargeConfig::set_volcellallowedmax(float value) {
  _internal_set_volcellallowedmax(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.DischargeConfig.volCellAllowedMax)
}

// float volCellAllowedMin = 9;
inline void DischargeConfig::clear_volcellallowedmin() {
  volcellallowedmin_ = 0;
}
inline float DischargeConfig::_internal_volcellallowedmin() const {
  return volcellallowedmin_;
}
inline float DischargeConfig::volcellallowedmin() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.DischargeConfig.volCellAllowedMin)
  return _internal_volcellallowedmin();
}
inline void DischargeConfig::_internal_set_volcellallowedmin(float value) {
  
  volcellallowedmin_ = value;
}
inline void DischargeConfig::set_volcellallowedmin(float value) {
  _internal_set_volcellallowedmin(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.DischargeConfig.volCellAllowedMin)
}

// float socAllowedMin = 10;
inline void DischargeConfig::clear_socallowedmin() {
  socallowedmin_ = 0;
}
inline float DischargeConfig::_internal_socallowedmin() const {
  return socallowedmin_;
}
inline float DischargeConfig::socallowedmin() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.DischargeConfig.socAllowedMin)
  return _internal_socallowedmin();
}
inline void DischargeConfig::_internal_set_socallowedmin(float value) {
  
  socallowedmin_ = value;
}
inline void DischargeConfig::set_socallowedmin(float value) {
  _internal_set_socallowedmin(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.DischargeConfig.socAllowedMin)
}

// float totalBatteryCycleNum = 11;
inline void DischargeConfig::clear_totalbatterycyclenum() {
  totalbatterycyclenum_ = 0;
}
inline float DischargeConfig::_internal_totalbatterycyclenum() const {
  return totalbatterycyclenum_;
}
inline float DischargeConfig::totalbatterycyclenum() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.DischargeConfig.totalBatteryCycleNum)
  return _internal_totalbatterycyclenum();
}
inline void DischargeConfig::_internal_set_totalbatterycyclenum(float value) {
  
  totalbatterycyclenum_ = value;
}
inline void DischargeConfig::set_totalbatterycyclenum(float value) {
  _internal_set_totalbatterycyclenum(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.DischargeConfig.totalBatteryCycleNum)
}

// float allowedBatteryCycleNum = 12;
inline void DischargeConfig::clear_allowedbatterycyclenum() {
  allowedbatterycyclenum_ = 0;
}
inline float DischargeConfig::_internal_allowedbatterycyclenum() const {
  return allowedbatterycyclenum_;
}
inline float DischargeConfig::allowedbatterycyclenum() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.DischargeConfig.allowedBatteryCycleNum)
  return _internal_allowedbatterycyclenum();
}
inline void DischargeConfig::_internal_set_allowedbatterycyclenum(float value) {
  
  allowedbatterycyclenum_ = value;
}
inline void DischargeConfig::set_allowedbatterycyclenum(float value) {
  _internal_set_allowedbatterycyclenum(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.DischargeConfig.allowedBatteryCycleNum)
}

// float desireResidueRange = 13;
inline void DischargeConfig::clear_desireresiduerange() {
  desireresiduerange_ = 0;
}
inline float DischargeConfig::_internal_desireresiduerange() const {
  return desireresiduerange_;
}
inline float DischargeConfig::desireresiduerange() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.DischargeConfig.desireResidueRange)
  return _internal_desireresiduerange();
}
inline void DischargeConfig::_internal_set_desireresiduerange(float value) {
  
  desireresiduerange_ = value;
}
inline void DischargeConfig::set_desireresiduerange(float value) {
  _internal_set_desireresiduerange(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.DischargeConfig.desireResidueRange)
}

// float tempAllowedMax = 14;
inline void DischargeConfig::clear_tempallowedmax() {
  tempallowedmax_ = 0;
}
inline float DischargeConfig::_internal_tempallowedmax() const {
  return tempallowedmax_;
}
inline float DischargeConfig::tempallowedmax() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.DischargeConfig.tempAllowedMax)
  return _internal_tempallowedmax();
}
inline void DischargeConfig::_internal_set_tempallowedmax(float value) {
  
  tempallowedmax_ = value;
}
inline void DischargeConfig::set_tempallowedmax(float value) {
  _internal_set_tempallowedmax(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.DischargeConfig.tempAllowedMax)
}

// uint32 chargerAllowedRestartNum = 15;
inline void DischargeConfig::clear_chargerallowedrestartnum() {
  chargerallowedrestartnum_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 DischargeConfig::_internal_chargerallowedrestartnum() const {
  return chargerallowedrestartnum_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 DischargeConfig::chargerallowedrestartnum() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.DischargeConfig.chargerAllowedRestartNum)
  return _internal_chargerallowedrestartnum();
}
inline void DischargeConfig::_internal_set_chargerallowedrestartnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chargerallowedrestartnum_ = value;
}
inline void DischargeConfig::set_chargerallowedrestartnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chargerallowedrestartnum(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.DischargeConfig.chargerAllowedRestartNum)
}

// uint32 bmsAllowedRestartNum = 16;
inline void DischargeConfig::clear_bmsallowedrestartnum() {
  bmsallowedrestartnum_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 DischargeConfig::_internal_bmsallowedrestartnum() const {
  return bmsallowedrestartnum_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 DischargeConfig::bmsallowedrestartnum() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.DischargeConfig.bmsAllowedRestartNum)
  return _internal_bmsallowedrestartnum();
}
inline void DischargeConfig::_internal_set_bmsallowedrestartnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsallowedrestartnum_ = value;
}
inline void DischargeConfig::set_bmsallowedrestartnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsallowedrestartnum(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.DischargeConfig.bmsAllowedRestartNum)
}

// -------------------------------------------------------------------

// AuthenMsg

// uint32 nowFDC = 1;
inline void AuthenMsg::clear_nowfdc() {
  nowfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 AuthenMsg::_internal_nowfdc() const {
  return nowfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 AuthenMsg::nowfdc() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.AuthenMsg.nowFDC)
  return _internal_nowfdc();
}
inline void AuthenMsg::_internal_set_nowfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  nowfdc_ = value;
}
inline void AuthenMsg::set_nowfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_nowfdc(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.AuthenMsg.nowFDC)
}

// uint32 authenWaitTime = 2;
inline void AuthenMsg::clear_authenwaittime() {
  authenwaittime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 AuthenMsg::_internal_authenwaittime() const {
  return authenwaittime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 AuthenMsg::authenwaittime() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.AuthenMsg.authenWaitTime)
  return _internal_authenwaittime();
}
inline void AuthenMsg::_internal_set_authenwaittime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  authenwaittime_ = value;
}
inline void AuthenMsg::set_authenwaittime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_authenwaittime(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.AuthenMsg.authenWaitTime)
}

// uint32 bmsWaitAuthenState = 3;
inline void AuthenMsg::clear_bmswaitauthenstate() {
  bmswaitauthenstate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 AuthenMsg::_internal_bmswaitauthenstate() const {
  return bmswaitauthenstate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 AuthenMsg::bmswaitauthenstate() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.AuthenMsg.bmsWaitAuthenState)
  return _internal_bmswaitauthenstate();
}
inline void AuthenMsg::_internal_set_bmswaitauthenstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmswaitauthenstate_ = value;
}
inline void AuthenMsg::set_bmswaitauthenstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmswaitauthenstate(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.AuthenMsg.bmsWaitAuthenState)
}

// uint32 authenResult = 4;
inline void AuthenMsg::clear_authenresult() {
  authenresult_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 AuthenMsg::_internal_authenresult() const {
  return authenresult_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 AuthenMsg::authenresult() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.AuthenMsg.authenResult)
  return _internal_authenresult();
}
inline void AuthenMsg::_internal_set_authenresult(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  authenresult_ = value;
}
inline void AuthenMsg::set_authenresult(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_authenresult(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.AuthenMsg.authenResult)
}

// uint32 succeedAuthenFDC = 5;
inline void AuthenMsg::clear_succeedauthenfdc() {
  succeedauthenfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 AuthenMsg::_internal_succeedauthenfdc() const {
  return succeedauthenfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 AuthenMsg::succeedauthenfdc() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.AuthenMsg.succeedAuthenFDC)
  return _internal_succeedauthenfdc();
}
inline void AuthenMsg::_internal_set_succeedauthenfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  succeedauthenfdc_ = value;
}
inline void AuthenMsg::set_succeedauthenfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_succeedauthenfdc(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.AuthenMsg.succeedAuthenFDC)
}

// bytes eVIN = 6;
inline void AuthenMsg::clear_evin() {
  evin_.ClearToEmpty();
}
inline const std::string& AuthenMsg::evin() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.AuthenMsg.eVIN)
  return _internal_evin();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AuthenMsg::set_evin(ArgT0&& arg0, ArgT... args) {
 
 evin_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.AuthenMsg.eVIN)
}
inline std::string* AuthenMsg::mutable_evin() {
  std::string* _s = _internal_mutable_evin();
  // @@protoc_insertion_point(field_mutable:BMS2015PlusInfo.AuthenMsg.eVIN)
  return _s;
}
inline const std::string& AuthenMsg::_internal_evin() const {
  return evin_.Get();
}
inline void AuthenMsg::_internal_set_evin(const std::string& value) {
  
  evin_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AuthenMsg::_internal_mutable_evin() {
  
  return evin_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AuthenMsg::release_evin() {
  // @@protoc_insertion_point(field_release:BMS2015PlusInfo.AuthenMsg.eVIN)
  return evin_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AuthenMsg::set_allocated_evin(std::string* evin) {
  if (evin != nullptr) {
    
  } else {
    
  }
  evin_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), evin,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:BMS2015PlusInfo.AuthenMsg.eVIN)
}

// bytes batProducer = 7;
inline void AuthenMsg::clear_batproducer() {
  batproducer_.ClearToEmpty();
}
inline const std::string& AuthenMsg::batproducer() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.AuthenMsg.batProducer)
  return _internal_batproducer();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AuthenMsg::set_batproducer(ArgT0&& arg0, ArgT... args) {
 
 batproducer_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.AuthenMsg.batProducer)
}
inline std::string* AuthenMsg::mutable_batproducer() {
  std::string* _s = _internal_mutable_batproducer();
  // @@protoc_insertion_point(field_mutable:BMS2015PlusInfo.AuthenMsg.batProducer)
  return _s;
}
inline const std::string& AuthenMsg::_internal_batproducer() const {
  return batproducer_.Get();
}
inline void AuthenMsg::_internal_set_batproducer(const std::string& value) {
  
  batproducer_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AuthenMsg::_internal_mutable_batproducer() {
  
  return batproducer_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AuthenMsg::release_batproducer() {
  // @@protoc_insertion_point(field_release:BMS2015PlusInfo.AuthenMsg.batProducer)
  return batproducer_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AuthenMsg::set_allocated_batproducer(std::string* batproducer) {
  if (batproducer != nullptr) {
    
  } else {
    
  }
  batproducer_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), batproducer,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:BMS2015PlusInfo.AuthenMsg.batProducer)
}

// bytes chargerOperators = 8;
inline void AuthenMsg::clear_chargeroperators() {
  chargeroperators_.ClearToEmpty();
}
inline const std::string& AuthenMsg::chargeroperators() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.AuthenMsg.chargerOperators)
  return _internal_chargeroperators();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AuthenMsg::set_chargeroperators(ArgT0&& arg0, ArgT... args) {
 
 chargeroperators_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.AuthenMsg.chargerOperators)
}
inline std::string* AuthenMsg::mutable_chargeroperators() {
  std::string* _s = _internal_mutable_chargeroperators();
  // @@protoc_insertion_point(field_mutable:BMS2015PlusInfo.AuthenMsg.chargerOperators)
  return _s;
}
inline const std::string& AuthenMsg::_internal_chargeroperators() const {
  return chargeroperators_.Get();
}
inline void AuthenMsg::_internal_set_chargeroperators(const std::string& value) {
  
  chargeroperators_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AuthenMsg::_internal_mutable_chargeroperators() {
  
  return chargeroperators_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AuthenMsg::release_chargeroperators() {
  // @@protoc_insertion_point(field_release:BMS2015PlusInfo.AuthenMsg.chargerOperators)
  return chargeroperators_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AuthenMsg::set_allocated_chargeroperators(std::string* chargeroperators) {
  if (chargeroperators != nullptr) {
    
  } else {
    
  }
  chargeroperators_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), chargeroperators,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:BMS2015PlusInfo.AuthenMsg.chargerOperators)
}

// bytes chargerNumber = 9;
inline void AuthenMsg::clear_chargernumber() {
  chargernumber_.ClearToEmpty();
}
inline const std::string& AuthenMsg::chargernumber() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.AuthenMsg.chargerNumber)
  return _internal_chargernumber();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AuthenMsg::set_chargernumber(ArgT0&& arg0, ArgT... args) {
 
 chargernumber_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.AuthenMsg.chargerNumber)
}
inline std::string* AuthenMsg::mutable_chargernumber() {
  std::string* _s = _internal_mutable_chargernumber();
  // @@protoc_insertion_point(field_mutable:BMS2015PlusInfo.AuthenMsg.chargerNumber)
  return _s;
}
inline const std::string& AuthenMsg::_internal_chargernumber() const {
  return chargernumber_.Get();
}
inline void AuthenMsg::_internal_set_chargernumber(const std::string& value) {
  
  chargernumber_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AuthenMsg::_internal_mutable_chargernumber() {
  
  return chargernumber_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AuthenMsg::release_chargernumber() {
  // @@protoc_insertion_point(field_release:BMS2015PlusInfo.AuthenMsg.chargerNumber)
  return chargernumber_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AuthenMsg::set_allocated_chargernumber(std::string* chargernumber) {
  if (chargernumber != nullptr) {
    
  } else {
    
  }
  chargernumber_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), chargernumber,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:BMS2015PlusInfo.AuthenMsg.chargerNumber)
}

// -------------------------------------------------------------------

// ReserveMsg

// uint32 bmsDesireStartTime = 1;
inline void ReserveMsg::clear_bmsdesirestarttime() {
  bmsdesirestarttime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ReserveMsg::_internal_bmsdesirestarttime() const {
  return bmsdesirestarttime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ReserveMsg::bmsdesirestarttime() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.ReserveMsg.bmsDesireStartTime)
  return _internal_bmsdesirestarttime();
}
inline void ReserveMsg::_internal_set_bmsdesirestarttime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsdesirestarttime_ = value;
}
inline void ReserveMsg::set_bmsdesirestarttime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsdesirestarttime(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.ReserveMsg.bmsDesireStartTime)
}

// uint32 bmsDesireLeaveTime = 2;
inline void ReserveMsg::clear_bmsdesireleavetime() {
  bmsdesireleavetime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ReserveMsg::_internal_bmsdesireleavetime() const {
  return bmsdesireleavetime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ReserveMsg::bmsdesireleavetime() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.ReserveMsg.bmsDesireLeaveTime)
  return _internal_bmsdesireleavetime();
}
inline void ReserveMsg::_internal_set_bmsdesireleavetime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsdesireleavetime_ = value;
}
inline void ReserveMsg::set_bmsdesireleavetime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsdesireleavetime(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.ReserveMsg.bmsDesireLeaveTime)
}

// float chargerOutPowerMax = 3;
inline void ReserveMsg::clear_chargeroutpowermax() {
  chargeroutpowermax_ = 0;
}
inline float ReserveMsg::_internal_chargeroutpowermax() const {
  return chargeroutpowermax_;
}
inline float ReserveMsg::chargeroutpowermax() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.ReserveMsg.chargerOutPowerMax)
  return _internal_chargeroutpowermax();
}
inline void ReserveMsg::_internal_set_chargeroutpowermax(float value) {
  
  chargeroutpowermax_ = value;
}
inline void ReserveMsg::set_chargeroutpowermax(float value) {
  _internal_set_chargeroutpowermax(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.ReserveMsg.chargerOutPowerMax)
}

// uint32 immediateChargeSupport = 4;
inline void ReserveMsg::clear_immediatechargesupport() {
  immediatechargesupport_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ReserveMsg::_internal_immediatechargesupport() const {
  return immediatechargesupport_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ReserveMsg::immediatechargesupport() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.ReserveMsg.immediateChargeSupport)
  return _internal_immediatechargesupport();
}
inline void ReserveMsg::_internal_set_immediatechargesupport(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  immediatechargesupport_ = value;
}
inline void ReserveMsg::set_immediatechargesupport(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_immediatechargesupport(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.ReserveMsg.immediateChargeSupport)
}

// uint32 reserveResult = 5;
inline void ReserveMsg::clear_reserveresult() {
  reserveresult_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ReserveMsg::_internal_reserveresult() const {
  return reserveresult_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ReserveMsg::reserveresult() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.ReserveMsg.reserveResult)
  return _internal_reserveresult();
}
inline void ReserveMsg::_internal_set_reserveresult(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  reserveresult_ = value;
}
inline void ReserveMsg::set_reserveresult(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_reserveresult(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.ReserveMsg.reserveResult)
}

// -------------------------------------------------------------------

// SelfcheckMsg

// uint32 stickCheckState = 1;
inline void SelfcheckMsg::clear_stickcheckstate() {
  stickcheckstate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SelfcheckMsg::_internal_stickcheckstate() const {
  return stickcheckstate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SelfcheckMsg::stickcheckstate() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.SelfcheckMsg.stickCheckState)
  return _internal_stickcheckstate();
}
inline void SelfcheckMsg::_internal_set_stickcheckstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  stickcheckstate_ = value;
}
inline void SelfcheckMsg::set_stickcheckstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_stickcheckstate(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.SelfcheckMsg.stickCheckState)
}

// uint32 shortCircuitCheckState = 2;
inline void SelfcheckMsg::clear_shortcircuitcheckstate() {
  shortcircuitcheckstate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SelfcheckMsg::_internal_shortcircuitcheckstate() const {
  return shortcircuitcheckstate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SelfcheckMsg::shortcircuitcheckstate() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.SelfcheckMsg.shortCircuitCheckState)
  return _internal_shortcircuitcheckstate();
}
inline void SelfcheckMsg::_internal_set_shortcircuitcheckstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  shortcircuitcheckstate_ = value;
}
inline void SelfcheckMsg::set_shortcircuitcheckstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_shortcircuitcheckstate(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.SelfcheckMsg.shortCircuitCheckState)
}

// uint32 insultCheckState = 3;
inline void SelfcheckMsg::clear_insultcheckstate() {
  insultcheckstate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SelfcheckMsg::_internal_insultcheckstate() const {
  return insultcheckstate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SelfcheckMsg::insultcheckstate() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.SelfcheckMsg.insultCheckState)
  return _internal_insultcheckstate();
}
inline void SelfcheckMsg::_internal_set_insultcheckstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  insultcheckstate_ = value;
}
inline void SelfcheckMsg::set_insultcheckstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_insultcheckstate(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.SelfcheckMsg.insultCheckState)
}

// uint32 dischargeState = 4;
inline void SelfcheckMsg::clear_dischargestate() {
  dischargestate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SelfcheckMsg::_internal_dischargestate() const {
  return dischargestate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SelfcheckMsg::dischargestate() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.SelfcheckMsg.dischargeState)
  return _internal_dischargestate();
}
inline void SelfcheckMsg::_internal_set_dischargestate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  dischargestate_ = value;
}
inline void SelfcheckMsg::set_dischargestate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_dischargestate(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.SelfcheckMsg.dischargeState)
}

// -------------------------------------------------------------------

// VehicelState

// uint32 vehicleC5State = 1;
inline void VehicelState::clear_vehiclec5state() {
  vehiclec5state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VehicelState::_internal_vehiclec5state() const {
  return vehiclec5state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VehicelState::vehiclec5state() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.VehicelState.vehicleC5State)
  return _internal_vehiclec5state();
}
inline void VehicelState::_internal_set_vehiclec5state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  vehiclec5state_ = value;
}
inline void VehicelState::set_vehiclec5state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_vehiclec5state(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.VehicelState.vehicleC5State)
}

// uint32 vehicleC6State = 2;
inline void VehicelState::clear_vehiclec6state() {
  vehiclec6state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VehicelState::_internal_vehiclec6state() const {
  return vehiclec6state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VehicelState::vehiclec6state() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.VehicelState.vehicleC6State)
  return _internal_vehiclec6state();
}
inline void VehicelState::_internal_set_vehiclec6state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  vehiclec6state_ = value;
}
inline void VehicelState::set_vehiclec6state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_vehiclec6state(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.VehicelState.vehicleC6State)
}

// uint32 vehicelElockState = 3;
inline void VehicelState::clear_vehicelelockstate() {
  vehicelelockstate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VehicelState::_internal_vehicelelockstate() const {
  return vehicelelockstate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VehicelState::vehicelelockstate() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.VehicelState.vehicelElockState)
  return _internal_vehicelelockstate();
}
inline void VehicelState::_internal_set_vehicelelockstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  vehicelelockstate_ = value;
}
inline void VehicelState::set_vehicelelockstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_vehicelelockstate(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.VehicelState.vehicelElockState)
}

// -------------------------------------------------------------------

// PowerSupplyMsg

// uint32 chargerSupplyState = 1;
inline void PowerSupplyMsg::clear_chargersupplystate() {
  chargersupplystate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PowerSupplyMsg::_internal_chargersupplystate() const {
  return chargersupplystate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PowerSupplyMsg::chargersupplystate() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.PowerSupplyMsg.chargerSupplyState)
  return _internal_chargersupplystate();
}
inline void PowerSupplyMsg::_internal_set_chargersupplystate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chargersupplystate_ = value;
}
inline void PowerSupplyMsg::set_chargersupplystate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chargersupplystate(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.PowerSupplyMsg.chargerSupplyState)
}

// uint32 EvSupplyState = 2;
inline void PowerSupplyMsg::clear_evsupplystate() {
  evsupplystate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PowerSupplyMsg::_internal_evsupplystate() const {
  return evsupplystate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PowerSupplyMsg::evsupplystate() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.PowerSupplyMsg.EvSupplyState)
  return _internal_evsupplystate();
}
inline void PowerSupplyMsg::_internal_set_evsupplystate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  evsupplystate_ = value;
}
inline void PowerSupplyMsg::set_evsupplystate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_evsupplystate(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.PowerSupplyMsg.EvSupplyState)
}

// float volDesire = 3;
inline void PowerSupplyMsg::clear_voldesire() {
  voldesire_ = 0;
}
inline float PowerSupplyMsg::_internal_voldesire() const {
  return voldesire_;
}
inline float PowerSupplyMsg::voldesire() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.PowerSupplyMsg.volDesire)
  return _internal_voldesire();
}
inline void PowerSupplyMsg::_internal_set_voldesire(float value) {
  
  voldesire_ = value;
}
inline void PowerSupplyMsg::set_voldesire(float value) {
  _internal_set_voldesire(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.PowerSupplyMsg.volDesire)
}

// float currDesire = 4;
inline void PowerSupplyMsg::clear_currdesire() {
  currdesire_ = 0;
}
inline float PowerSupplyMsg::_internal_currdesire() const {
  return currdesire_;
}
inline float PowerSupplyMsg::currdesire() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.PowerSupplyMsg.currDesire)
  return _internal_currdesire();
}
inline void PowerSupplyMsg::_internal_set_currdesire(float value) {
  
  currdesire_ = value;
}
inline void PowerSupplyMsg::set_currdesire(float value) {
  _internal_set_currdesire(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.PowerSupplyMsg.currDesire)
}

// float outVol = 5;
inline void PowerSupplyMsg::clear_outvol() {
  outvol_ = 0;
}
inline float PowerSupplyMsg::_internal_outvol() const {
  return outvol_;
}
inline float PowerSupplyMsg::outvol() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.PowerSupplyMsg.outVol)
  return _internal_outvol();
}
inline void PowerSupplyMsg::_internal_set_outvol(float value) {
  
  outvol_ = value;
}
inline void PowerSupplyMsg::set_outvol(float value) {
  _internal_set_outvol(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.PowerSupplyMsg.outVol)
}

// float outCurr = 6;
inline void PowerSupplyMsg::clear_outcurr() {
  outcurr_ = 0;
}
inline float PowerSupplyMsg::_internal_outcurr() const {
  return outcurr_;
}
inline float PowerSupplyMsg::outcurr() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.PowerSupplyMsg.outCurr)
  return _internal_outcurr();
}
inline void PowerSupplyMsg::_internal_set_outcurr(float value) {
  
  outcurr_ = value;
}
inline void PowerSupplyMsg::set_outcurr(float value) {
  _internal_set_outcurr(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.PowerSupplyMsg.outCurr)
}

// float chgOutCurrMax = 7;
inline void PowerSupplyMsg::clear_chgoutcurrmax() {
  chgoutcurrmax_ = 0;
}
inline float PowerSupplyMsg::_internal_chgoutcurrmax() const {
  return chgoutcurrmax_;
}
inline float PowerSupplyMsg::chgoutcurrmax() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.PowerSupplyMsg.chgOutCurrMax)
  return _internal_chgoutcurrmax();
}
inline void PowerSupplyMsg::_internal_set_chgoutcurrmax(float value) {
  
  chgoutcurrmax_ = value;
}
inline void PowerSupplyMsg::set_chgoutcurrmax(float value) {
  _internal_set_chgoutcurrmax(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.PowerSupplyMsg.chgOutCurrMax)
}

// float resonForCapacityChange = 8;
inline void PowerSupplyMsg::clear_resonforcapacitychange() {
  resonforcapacitychange_ = 0;
}
inline float PowerSupplyMsg::_internal_resonforcapacitychange() const {
  return resonforcapacitychange_;
}
inline float PowerSupplyMsg::resonforcapacitychange() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.PowerSupplyMsg.resonForCapacityChange)
  return _internal_resonforcapacitychange();
}
inline void PowerSupplyMsg::_internal_set_resonforcapacitychange(float value) {
  
  resonforcapacitychange_ = value;
}
inline void PowerSupplyMsg::set_resonforcapacitychange(float value) {
  _internal_set_resonforcapacitychange(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.PowerSupplyMsg.resonForCapacityChange)
}

// -------------------------------------------------------------------

// BMSChargingEnd

// uint32 bmsStickCheckState = 1;
inline void BMSChargingEnd::clear_bmsstickcheckstate() {
  bmsstickcheckstate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::_internal_bmsstickcheckstate() const {
  return bmsstickcheckstate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::bmsstickcheckstate() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSChargingEnd.bmsStickCheckState)
  return _internal_bmsstickcheckstate();
}
inline void BMSChargingEnd::_internal_set_bmsstickcheckstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsstickcheckstate_ = value;
}
inline void BMSChargingEnd::set_bmsstickcheckstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsstickcheckstate(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSChargingEnd.bmsStickCheckState)
}

// uint32 chgStickCheckEnable = 2;
inline void BMSChargingEnd::clear_chgstickcheckenable() {
  chgstickcheckenable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::_internal_chgstickcheckenable() const {
  return chgstickcheckenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::chgstickcheckenable() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSChargingEnd.chgStickCheckEnable)
  return _internal_chgstickcheckenable();
}
inline void BMSChargingEnd::_internal_set_chgstickcheckenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chgstickcheckenable_ = value;
}
inline void BMSChargingEnd::set_chgstickcheckenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chgstickcheckenable(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSChargingEnd.chgStickCheckEnable)
}

// float energyChg = 3;
inline void BMSChargingEnd::clear_energychg() {
  energychg_ = 0;
}
inline float BMSChargingEnd::_internal_energychg() const {
  return energychg_;
}
inline float BMSChargingEnd::energychg() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSChargingEnd.energyChg)
  return _internal_energychg();
}
inline void BMSChargingEnd::_internal_set_energychg(float value) {
  
  energychg_ = value;
}
inline void BMSChargingEnd::set_energychg(float value) {
  _internal_set_energychg(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSChargingEnd.energyChg)
}

// float energyDischarge = 4;
inline void BMSChargingEnd::clear_energydischarge() {
  energydischarge_ = 0;
}
inline float BMSChargingEnd::_internal_energydischarge() const {
  return energydischarge_;
}
inline float BMSChargingEnd::energydischarge() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSChargingEnd.energyDischarge)
  return _internal_energydischarge();
}
inline void BMSChargingEnd::_internal_set_energydischarge(float value) {
  
  energydischarge_ = value;
}
inline void BMSChargingEnd::set_energydischarge(float value) {
  _internal_set_energydischarge(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSChargingEnd.energyDischarge)
}

// float endSOC = 5;
inline void BMSChargingEnd::clear_endsoc() {
  endsoc_ = 0;
}
inline float BMSChargingEnd::_internal_endsoc() const {
  return endsoc_;
}
inline float BMSChargingEnd::endsoc() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSChargingEnd.endSOC)
  return _internal_endsoc();
}
inline void BMSChargingEnd::_internal_set_endsoc(float value) {
  
  endsoc_ = value;
}
inline void BMSChargingEnd::set_endsoc(float value) {
  _internal_set_endsoc(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSChargingEnd.endSOC)
}

// uint32 chargerStopType = 6;
inline void BMSChargingEnd::clear_chargerstoptype() {
  chargerstoptype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::_internal_chargerstoptype() const {
  return chargerstoptype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::chargerstoptype() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSChargingEnd.chargerStopType)
  return _internal_chargerstoptype();
}
inline void BMSChargingEnd::_internal_set_chargerstoptype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chargerstoptype_ = value;
}
inline void BMSChargingEnd::set_chargerstoptype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chargerstoptype(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSChargingEnd.chargerStopType)
}

// uint32 chargerStopCode = 7;
inline void BMSChargingEnd::clear_chargerstopcode() {
  chargerstopcode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::_internal_chargerstopcode() const {
  return chargerstopcode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::chargerstopcode() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSChargingEnd.chargerStopCode)
  return _internal_chargerstopcode();
}
inline void BMSChargingEnd::_internal_set_chargerstopcode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chargerstopcode_ = value;
}
inline void BMSChargingEnd::set_chargerstopcode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chargerstopcode(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSChargingEnd.chargerStopCode)
}

// uint32 bmsStopType = 8;
inline void BMSChargingEnd::clear_bmsstoptype() {
  bmsstoptype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::_internal_bmsstoptype() const {
  return bmsstoptype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::bmsstoptype() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSChargingEnd.bmsStopType)
  return _internal_bmsstoptype();
}
inline void BMSChargingEnd::_internal_set_bmsstoptype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsstoptype_ = value;
}
inline void BMSChargingEnd::set_bmsstoptype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsstoptype(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSChargingEnd.bmsStopType)
}

// uint32 bmsStopCode = 9;
inline void BMSChargingEnd::clear_bmsstopcode() {
  bmsstopcode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::_internal_bmsstopcode() const {
  return bmsstopcode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::bmsstopcode() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSChargingEnd.bmsStopCode)
  return _internal_bmsstopcode();
}
inline void BMSChargingEnd::_internal_set_bmsstopcode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsstopcode_ = value;
}
inline void BMSChargingEnd::set_bmsstopcode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsstopcode(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSChargingEnd.bmsStopCode)
}

// uint32 chgReconnectEnable = 10;
inline void BMSChargingEnd::clear_chgreconnectenable() {
  chgreconnectenable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::_internal_chgreconnectenable() const {
  return chgreconnectenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::chgreconnectenable() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSChargingEnd.chgReconnectEnable)
  return _internal_chgreconnectenable();
}
inline void BMSChargingEnd::_internal_set_chgreconnectenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chgreconnectenable_ = value;
}
inline void BMSChargingEnd::set_chgreconnectenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chgreconnectenable(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSChargingEnd.chgReconnectEnable)
}

// uint32 reconnectEnable = 11;
inline void BMSChargingEnd::clear_reconnectenable() {
  reconnectenable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::_internal_reconnectenable() const {
  return reconnectenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::reconnectenable() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSChargingEnd.reconnectEnable)
  return _internal_reconnectenable();
}
inline void BMSChargingEnd::_internal_set_reconnectenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  reconnectenable_ = value;
}
inline void BMSChargingEnd::set_reconnectenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_reconnectenable(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSChargingEnd.reconnectEnable)
}

// -------------------------------------------------------------------

// BMSCharging

// uint32 bmsReady = 1;
inline void BMSCharging::clear_bmsready() {
  bmsready_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_bmsready() const {
  return bmsready_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::bmsready() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSCharging.bmsReady)
  return _internal_bmsready();
}
inline void BMSCharging::_internal_set_bmsready(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsready_ = value;
}
inline void BMSCharging::set_bmsready(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsready(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSCharging.bmsReady)
}

// uint32 chargerReady = 2;
inline void BMSCharging::clear_chargerready() {
  chargerready_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_chargerready() const {
  return chargerready_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::chargerready() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSCharging.chargerReady)
  return _internal_chargerready();
}
inline void BMSCharging::_internal_set_chargerready(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chargerready_ = value;
}
inline void BMSCharging::set_chargerready(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chargerready(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSCharging.chargerReady)
}

// uint32 chargeMode = 3;
inline void BMSCharging::clear_chargemode() {
  chargemode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_chargemode() const {
  return chargemode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::chargemode() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSCharging.chargeMode)
  return _internal_chargemode();
}
inline void BMSCharging::_internal_set_chargemode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chargemode_ = value;
}
inline void BMSCharging::set_chargemode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chargemode(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSCharging.chargeMode)
}

// uint32 resonForCapacityChange = 4;
inline void BMSCharging::clear_resonforcapacitychange() {
  resonforcapacitychange_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_resonforcapacitychange() const {
  return resonforcapacitychange_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::resonforcapacitychange() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSCharging.resonForCapacityChange)
  return _internal_resonforcapacitychange();
}
inline void BMSCharging::_internal_set_resonforcapacitychange(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  resonforcapacitychange_ = value;
}
inline void BMSCharging::set_resonforcapacitychange(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_resonforcapacitychange(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSCharging.resonForCapacityChange)
}

// uint32 bmsPause = 5;
inline void BMSCharging::clear_bmspause() {
  bmspause_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_bmspause() const {
  return bmspause_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::bmspause() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSCharging.bmsPause)
  return _internal_bmspause();
}
inline void BMSCharging::_internal_set_bmspause(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmspause_ = value;
}
inline void BMSCharging::set_bmspause(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmspause(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSCharging.bmsPause)
}

// uint32 chgPause = 6;
inline void BMSCharging::clear_chgpause() {
  chgpause_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_chgpause() const {
  return chgpause_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::chgpause() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSCharging.chgPause)
  return _internal_chgpause();
}
inline void BMSCharging::_internal_set_chgpause(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chgpause_ = value;
}
inline void BMSCharging::set_chgpause(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chgpause(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSCharging.chgPause)
}

// float bmsBatVol = 7;
inline void BMSCharging::clear_bmsbatvol() {
  bmsbatvol_ = 0;
}
inline float BMSCharging::_internal_bmsbatvol() const {
  return bmsbatvol_;
}
inline float BMSCharging::bmsbatvol() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSCharging.bmsBatVol)
  return _internal_bmsbatvol();
}
inline void BMSCharging::_internal_set_bmsbatvol(float value) {
  
  bmsbatvol_ = value;
}
inline void BMSCharging::set_bmsbatvol(float value) {
  _internal_set_bmsbatvol(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSCharging.bmsBatVol)
}

// float volDemand = 8;
inline void BMSCharging::clear_voldemand() {
  voldemand_ = 0;
}
inline float BMSCharging::_internal_voldemand() const {
  return voldemand_;
}
inline float BMSCharging::voldemand() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSCharging.volDemand)
  return _internal_voldemand();
}
inline void BMSCharging::_internal_set_voldemand(float value) {
  
  voldemand_ = value;
}
inline void BMSCharging::set_voldemand(float value) {
  _internal_set_voldemand(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSCharging.volDemand)
}

// float curDemand = 9;
inline void BMSCharging::clear_curdemand() {
  curdemand_ = 0;
}
inline float BMSCharging::_internal_curdemand() const {
  return curdemand_;
}
inline float BMSCharging::curdemand() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSCharging.curDemand)
  return _internal_curdemand();
}
inline void BMSCharging::_internal_set_curdemand(float value) {
  
  curdemand_ = value;
}
inline void BMSCharging::set_curdemand(float value) {
  _internal_set_curdemand(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSCharging.curDemand)
}

// float socNow = 10;
inline void BMSCharging::clear_socnow() {
  socnow_ = 0;
}
inline float BMSCharging::_internal_socnow() const {
  return socnow_;
}
inline float BMSCharging::socnow() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSCharging.socNow)
  return _internal_socnow();
}
inline void BMSCharging::_internal_set_socnow(float value) {
  
  socnow_ = value;
}
inline void BMSCharging::set_socnow(float value) {
  _internal_set_socnow(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSCharging.socNow)
}

// float currOutCapacity = 11;
inline void BMSCharging::clear_curroutcapacity() {
  curroutcapacity_ = 0;
}
inline float BMSCharging::_internal_curroutcapacity() const {
  return curroutcapacity_;
}
inline float BMSCharging::curroutcapacity() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSCharging.currOutCapacity)
  return _internal_curroutcapacity();
}
inline void BMSCharging::_internal_set_curroutcapacity(float value) {
  
  curroutcapacity_ = value;
}
inline void BMSCharging::set_curroutcapacity(float value) {
  _internal_set_curroutcapacity(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSCharging.currOutCapacity)
}

// float volMeasured = 12;
inline void BMSCharging::clear_volmeasured() {
  volmeasured_ = 0;
}
inline float BMSCharging::_internal_volmeasured() const {
  return volmeasured_;
}
inline float BMSCharging::volmeasured() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSCharging.volMeasured)
  return _internal_volmeasured();
}
inline void BMSCharging::_internal_set_volmeasured(float value) {
  
  volmeasured_ = value;
}
inline void BMSCharging::set_volmeasured(float value) {
  _internal_set_volmeasured(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSCharging.volMeasured)
}

// float curMeasured = 13;
inline void BMSCharging::clear_curmeasured() {
  curmeasured_ = 0;
}
inline float BMSCharging::_internal_curmeasured() const {
  return curmeasured_;
}
inline float BMSCharging::curmeasured() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSCharging.curMeasured)
  return _internal_curmeasured();
}
inline void BMSCharging::_internal_set_curmeasured(float value) {
  
  curmeasured_ = value;
}
inline void BMSCharging::set_curmeasured(float value) {
  _internal_set_curmeasured(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSCharging.curMeasured)
}

// float monoBatVolMax = 14;
inline void BMSCharging::clear_monobatvolmax() {
  monobatvolmax_ = 0;
}
inline float BMSCharging::_internal_monobatvolmax() const {
  return monobatvolmax_;
}
inline float BMSCharging::monobatvolmax() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSCharging.monoBatVolMax)
  return _internal_monobatvolmax();
}
inline void BMSCharging::_internal_set_monobatvolmax(float value) {
  
  monobatvolmax_ = value;
}
inline void BMSCharging::set_monobatvolmax(float value) {
  _internal_set_monobatvolmax(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSCharging.monoBatVolMax)
}

// float monoBatVolMin = 15;
inline void BMSCharging::clear_monobatvolmin() {
  monobatvolmin_ = 0;
}
inline float BMSCharging::_internal_monobatvolmin() const {
  return monobatvolmin_;
}
inline float BMSCharging::monobatvolmin() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSCharging.monoBatVolMin)
  return _internal_monobatvolmin();
}
inline void BMSCharging::_internal_set_monobatvolmin(float value) {
  
  monobatvolmin_ = value;
}
inline void BMSCharging::set_monobatvolmin(float value) {
  _internal_set_monobatvolmin(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSCharging.monoBatVolMin)
}

// float tempMax = 16;
inline void BMSCharging::clear_tempmax() {
  tempmax_ = 0;
}
inline float BMSCharging::_internal_tempmax() const {
  return tempmax_;
}
inline float BMSCharging::tempmax() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSCharging.tempMax)
  return _internal_tempmax();
}
inline void BMSCharging::_internal_set_tempmax(float value) {
  
  tempmax_ = value;
}
inline void BMSCharging::set_tempmax(float value) {
  _internal_set_tempmax(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSCharging.tempMax)
}

// float tempMin = 17;
inline void BMSCharging::clear_tempmin() {
  tempmin_ = 0;
}
inline float BMSCharging::_internal_tempmin() const {
  return tempmin_;
}
inline float BMSCharging::tempmin() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.BMSCharging.tempMin)
  return _internal_tempmin();
}
inline void BMSCharging::_internal_set_tempmin(float value) {
  
  tempmin_ = value;
}
inline void BMSCharging::set_tempmin(float value) {
  _internal_set_tempmin(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.BMSCharging.tempMin)
}

// -------------------------------------------------------------------

// bms2015pMsg

// .BMS2015PlusInfo.ChargeState bmsState = 1;
inline void bms2015pMsg::clear_bmsstate() {
  bmsstate_ = 0;
}
inline ::BMS2015PlusInfo::ChargeState bms2015pMsg::_internal_bmsstate() const {
  return static_cast< ::BMS2015PlusInfo::ChargeState >(bmsstate_);
}
inline ::BMS2015PlusInfo::ChargeState bms2015pMsg::bmsstate() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.bms2015pMsg.bmsState)
  return _internal_bmsstate();
}
inline void bms2015pMsg::_internal_set_bmsstate(::BMS2015PlusInfo::ChargeState value) {
  
  bmsstate_ = value;
}
inline void bms2015pMsg::set_bmsstate(::BMS2015PlusInfo::ChargeState value) {
  _internal_set_bmsstate(value);
  // @@protoc_insertion_point(field_set:BMS2015PlusInfo.bms2015pMsg.bmsState)
}

// .BMS2015PlusInfo.ProtoConferMsg bmsProtoConfer = 2;
inline bool bms2015pMsg::_internal_has_bmsprotoconfer() const {
  return this != internal_default_instance() && bmsprotoconfer_ != nullptr;
}
inline bool bms2015pMsg::has_bmsprotoconfer() const {
  return _internal_has_bmsprotoconfer();
}
inline void bms2015pMsg::clear_bmsprotoconfer() {
  if (GetArenaForAllocation() == nullptr && bmsprotoconfer_ != nullptr) {
    delete bmsprotoconfer_;
  }
  bmsprotoconfer_ = nullptr;
}
inline const ::BMS2015PlusInfo::ProtoConferMsg& bms2015pMsg::_internal_bmsprotoconfer() const {
  const ::BMS2015PlusInfo::ProtoConferMsg* p = bmsprotoconfer_;
  return p != nullptr ? *p : reinterpret_cast<const ::BMS2015PlusInfo::ProtoConferMsg&>(
      ::BMS2015PlusInfo::_ProtoConferMsg_default_instance_);
}
inline const ::BMS2015PlusInfo::ProtoConferMsg& bms2015pMsg::bmsprotoconfer() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.bms2015pMsg.bmsProtoConfer)
  return _internal_bmsprotoconfer();
}
inline void bms2015pMsg::unsafe_arena_set_allocated_bmsprotoconfer(
    ::BMS2015PlusInfo::ProtoConferMsg* bmsprotoconfer) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmsprotoconfer_);
  }
  bmsprotoconfer_ = bmsprotoconfer;
  if (bmsprotoconfer) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:BMS2015PlusInfo.bms2015pMsg.bmsProtoConfer)
}
inline ::BMS2015PlusInfo::ProtoConferMsg* bms2015pMsg::release_bmsprotoconfer() {
  
  ::BMS2015PlusInfo::ProtoConferMsg* temp = bmsprotoconfer_;
  bmsprotoconfer_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::BMS2015PlusInfo::ProtoConferMsg* bms2015pMsg::unsafe_arena_release_bmsprotoconfer() {
  // @@protoc_insertion_point(field_release:BMS2015PlusInfo.bms2015pMsg.bmsProtoConfer)
  
  ::BMS2015PlusInfo::ProtoConferMsg* temp = bmsprotoconfer_;
  bmsprotoconfer_ = nullptr;
  return temp;
}
inline ::BMS2015PlusInfo::ProtoConferMsg* bms2015pMsg::_internal_mutable_bmsprotoconfer() {
  
  if (bmsprotoconfer_ == nullptr) {
    auto* p = CreateMaybeMessage<::BMS2015PlusInfo::ProtoConferMsg>(GetArenaForAllocation());
    bmsprotoconfer_ = p;
  }
  return bmsprotoconfer_;
}
inline ::BMS2015PlusInfo::ProtoConferMsg* bms2015pMsg::mutable_bmsprotoconfer() {
  ::BMS2015PlusInfo::ProtoConferMsg* _msg = _internal_mutable_bmsprotoconfer();
  // @@protoc_insertion_point(field_mutable:BMS2015PlusInfo.bms2015pMsg.bmsProtoConfer)
  return _msg;
}
inline void bms2015pMsg::set_allocated_bmsprotoconfer(::BMS2015PlusInfo::ProtoConferMsg* bmsprotoconfer) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete bmsprotoconfer_;
  }
  if (bmsprotoconfer) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::BMS2015PlusInfo::ProtoConferMsg>::GetOwningArena(bmsprotoconfer);
    if (message_arena != submessage_arena) {
      bmsprotoconfer = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bmsprotoconfer, submessage_arena);
    }
    
  } else {
    
  }
  bmsprotoconfer_ = bmsprotoconfer;
  // @@protoc_insertion_point(field_set_allocated:BMS2015PlusInfo.bms2015pMsg.bmsProtoConfer)
}

// .BMS2015PlusInfo.FunConferMsg bmsFunConfer = 3;
inline bool bms2015pMsg::_internal_has_bmsfunconfer() const {
  return this != internal_default_instance() && bmsfunconfer_ != nullptr;
}
inline bool bms2015pMsg::has_bmsfunconfer() const {
  return _internal_has_bmsfunconfer();
}
inline void bms2015pMsg::clear_bmsfunconfer() {
  if (GetArenaForAllocation() == nullptr && bmsfunconfer_ != nullptr) {
    delete bmsfunconfer_;
  }
  bmsfunconfer_ = nullptr;
}
inline const ::BMS2015PlusInfo::FunConferMsg& bms2015pMsg::_internal_bmsfunconfer() const {
  const ::BMS2015PlusInfo::FunConferMsg* p = bmsfunconfer_;
  return p != nullptr ? *p : reinterpret_cast<const ::BMS2015PlusInfo::FunConferMsg&>(
      ::BMS2015PlusInfo::_FunConferMsg_default_instance_);
}
inline const ::BMS2015PlusInfo::FunConferMsg& bms2015pMsg::bmsfunconfer() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.bms2015pMsg.bmsFunConfer)
  return _internal_bmsfunconfer();
}
inline void bms2015pMsg::unsafe_arena_set_allocated_bmsfunconfer(
    ::BMS2015PlusInfo::FunConferMsg* bmsfunconfer) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmsfunconfer_);
  }
  bmsfunconfer_ = bmsfunconfer;
  if (bmsfunconfer) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:BMS2015PlusInfo.bms2015pMsg.bmsFunConfer)
}
inline ::BMS2015PlusInfo::FunConferMsg* bms2015pMsg::release_bmsfunconfer() {
  
  ::BMS2015PlusInfo::FunConferMsg* temp = bmsfunconfer_;
  bmsfunconfer_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::BMS2015PlusInfo::FunConferMsg* bms2015pMsg::unsafe_arena_release_bmsfunconfer() {
  // @@protoc_insertion_point(field_release:BMS2015PlusInfo.bms2015pMsg.bmsFunConfer)
  
  ::BMS2015PlusInfo::FunConferMsg* temp = bmsfunconfer_;
  bmsfunconfer_ = nullptr;
  return temp;
}
inline ::BMS2015PlusInfo::FunConferMsg* bms2015pMsg::_internal_mutable_bmsfunconfer() {
  
  if (bmsfunconfer_ == nullptr) {
    auto* p = CreateMaybeMessage<::BMS2015PlusInfo::FunConferMsg>(GetArenaForAllocation());
    bmsfunconfer_ = p;
  }
  return bmsfunconfer_;
}
inline ::BMS2015PlusInfo::FunConferMsg* bms2015pMsg::mutable_bmsfunconfer() {
  ::BMS2015PlusInfo::FunConferMsg* _msg = _internal_mutable_bmsfunconfer();
  // @@protoc_insertion_point(field_mutable:BMS2015PlusInfo.bms2015pMsg.bmsFunConfer)
  return _msg;
}
inline void bms2015pMsg::set_allocated_bmsfunconfer(::BMS2015PlusInfo::FunConferMsg* bmsfunconfer) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete bmsfunconfer_;
  }
  if (bmsfunconfer) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::BMS2015PlusInfo::FunConferMsg>::GetOwningArena(bmsfunconfer);
    if (message_arena != submessage_arena) {
      bmsfunconfer = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bmsfunconfer, submessage_arena);
    }
    
  } else {
    
  }
  bmsfunconfer_ = bmsfunconfer;
  // @@protoc_insertion_point(field_set_allocated:BMS2015PlusInfo.bms2015pMsg.bmsFunConfer)
}

// .BMS2015PlusInfo.BMSConfig BmsConfgM = 4;
inline bool bms2015pMsg::_internal_has_bmsconfgm() const {
  return this != internal_default_instance() && bmsconfgm_ != nullptr;
}
inline bool bms2015pMsg::has_bmsconfgm() const {
  return _internal_has_bmsconfgm();
}
inline void bms2015pMsg::clear_bmsconfgm() {
  if (GetArenaForAllocation() == nullptr && bmsconfgm_ != nullptr) {
    delete bmsconfgm_;
  }
  bmsconfgm_ = nullptr;
}
inline const ::BMS2015PlusInfo::BMSConfig& bms2015pMsg::_internal_bmsconfgm() const {
  const ::BMS2015PlusInfo::BMSConfig* p = bmsconfgm_;
  return p != nullptr ? *p : reinterpret_cast<const ::BMS2015PlusInfo::BMSConfig&>(
      ::BMS2015PlusInfo::_BMSConfig_default_instance_);
}
inline const ::BMS2015PlusInfo::BMSConfig& bms2015pMsg::bmsconfgm() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.bms2015pMsg.BmsConfgM)
  return _internal_bmsconfgm();
}
inline void bms2015pMsg::unsafe_arena_set_allocated_bmsconfgm(
    ::BMS2015PlusInfo::BMSConfig* bmsconfgm) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmsconfgm_);
  }
  bmsconfgm_ = bmsconfgm;
  if (bmsconfgm) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:BMS2015PlusInfo.bms2015pMsg.BmsConfgM)
}
inline ::BMS2015PlusInfo::BMSConfig* bms2015pMsg::release_bmsconfgm() {
  
  ::BMS2015PlusInfo::BMSConfig* temp = bmsconfgm_;
  bmsconfgm_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::BMS2015PlusInfo::BMSConfig* bms2015pMsg::unsafe_arena_release_bmsconfgm() {
  // @@protoc_insertion_point(field_release:BMS2015PlusInfo.bms2015pMsg.BmsConfgM)
  
  ::BMS2015PlusInfo::BMSConfig* temp = bmsconfgm_;
  bmsconfgm_ = nullptr;
  return temp;
}
inline ::BMS2015PlusInfo::BMSConfig* bms2015pMsg::_internal_mutable_bmsconfgm() {
  
  if (bmsconfgm_ == nullptr) {
    auto* p = CreateMaybeMessage<::BMS2015PlusInfo::BMSConfig>(GetArenaForAllocation());
    bmsconfgm_ = p;
  }
  return bmsconfgm_;
}
inline ::BMS2015PlusInfo::BMSConfig* bms2015pMsg::mutable_bmsconfgm() {
  ::BMS2015PlusInfo::BMSConfig* _msg = _internal_mutable_bmsconfgm();
  // @@protoc_insertion_point(field_mutable:BMS2015PlusInfo.bms2015pMsg.BmsConfgM)
  return _msg;
}
inline void bms2015pMsg::set_allocated_bmsconfgm(::BMS2015PlusInfo::BMSConfig* bmsconfgm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete bmsconfgm_;
  }
  if (bmsconfgm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::BMS2015PlusInfo::BMSConfig>::GetOwningArena(bmsconfgm);
    if (message_arena != submessage_arena) {
      bmsconfgm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bmsconfgm, submessage_arena);
    }
    
  } else {
    
  }
  bmsconfgm_ = bmsconfgm;
  // @@protoc_insertion_point(field_set_allocated:BMS2015PlusInfo.bms2015pMsg.BmsConfgM)
}

// .BMS2015PlusInfo.BMSCharging BmsChargingM = 5;
inline bool bms2015pMsg::_internal_has_bmschargingm() const {
  return this != internal_default_instance() && bmschargingm_ != nullptr;
}
inline bool bms2015pMsg::has_bmschargingm() const {
  return _internal_has_bmschargingm();
}
inline void bms2015pMsg::clear_bmschargingm() {
  if (GetArenaForAllocation() == nullptr && bmschargingm_ != nullptr) {
    delete bmschargingm_;
  }
  bmschargingm_ = nullptr;
}
inline const ::BMS2015PlusInfo::BMSCharging& bms2015pMsg::_internal_bmschargingm() const {
  const ::BMS2015PlusInfo::BMSCharging* p = bmschargingm_;
  return p != nullptr ? *p : reinterpret_cast<const ::BMS2015PlusInfo::BMSCharging&>(
      ::BMS2015PlusInfo::_BMSCharging_default_instance_);
}
inline const ::BMS2015PlusInfo::BMSCharging& bms2015pMsg::bmschargingm() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.bms2015pMsg.BmsChargingM)
  return _internal_bmschargingm();
}
inline void bms2015pMsg::unsafe_arena_set_allocated_bmschargingm(
    ::BMS2015PlusInfo::BMSCharging* bmschargingm) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmschargingm_);
  }
  bmschargingm_ = bmschargingm;
  if (bmschargingm) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:BMS2015PlusInfo.bms2015pMsg.BmsChargingM)
}
inline ::BMS2015PlusInfo::BMSCharging* bms2015pMsg::release_bmschargingm() {
  
  ::BMS2015PlusInfo::BMSCharging* temp = bmschargingm_;
  bmschargingm_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::BMS2015PlusInfo::BMSCharging* bms2015pMsg::unsafe_arena_release_bmschargingm() {
  // @@protoc_insertion_point(field_release:BMS2015PlusInfo.bms2015pMsg.BmsChargingM)
  
  ::BMS2015PlusInfo::BMSCharging* temp = bmschargingm_;
  bmschargingm_ = nullptr;
  return temp;
}
inline ::BMS2015PlusInfo::BMSCharging* bms2015pMsg::_internal_mutable_bmschargingm() {
  
  if (bmschargingm_ == nullptr) {
    auto* p = CreateMaybeMessage<::BMS2015PlusInfo::BMSCharging>(GetArenaForAllocation());
    bmschargingm_ = p;
  }
  return bmschargingm_;
}
inline ::BMS2015PlusInfo::BMSCharging* bms2015pMsg::mutable_bmschargingm() {
  ::BMS2015PlusInfo::BMSCharging* _msg = _internal_mutable_bmschargingm();
  // @@protoc_insertion_point(field_mutable:BMS2015PlusInfo.bms2015pMsg.BmsChargingM)
  return _msg;
}
inline void bms2015pMsg::set_allocated_bmschargingm(::BMS2015PlusInfo::BMSCharging* bmschargingm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete bmschargingm_;
  }
  if (bmschargingm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::BMS2015PlusInfo::BMSCharging>::GetOwningArena(bmschargingm);
    if (message_arena != submessage_arena) {
      bmschargingm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bmschargingm, submessage_arena);
    }
    
  } else {
    
  }
  bmschargingm_ = bmschargingm;
  // @@protoc_insertion_point(field_set_allocated:BMS2015PlusInfo.bms2015pMsg.BmsChargingM)
}

// .BMS2015PlusInfo.BMSChargingEnd BmsChargeFinishM = 6;
inline bool bms2015pMsg::_internal_has_bmschargefinishm() const {
  return this != internal_default_instance() && bmschargefinishm_ != nullptr;
}
inline bool bms2015pMsg::has_bmschargefinishm() const {
  return _internal_has_bmschargefinishm();
}
inline void bms2015pMsg::clear_bmschargefinishm() {
  if (GetArenaForAllocation() == nullptr && bmschargefinishm_ != nullptr) {
    delete bmschargefinishm_;
  }
  bmschargefinishm_ = nullptr;
}
inline const ::BMS2015PlusInfo::BMSChargingEnd& bms2015pMsg::_internal_bmschargefinishm() const {
  const ::BMS2015PlusInfo::BMSChargingEnd* p = bmschargefinishm_;
  return p != nullptr ? *p : reinterpret_cast<const ::BMS2015PlusInfo::BMSChargingEnd&>(
      ::BMS2015PlusInfo::_BMSChargingEnd_default_instance_);
}
inline const ::BMS2015PlusInfo::BMSChargingEnd& bms2015pMsg::bmschargefinishm() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.bms2015pMsg.BmsChargeFinishM)
  return _internal_bmschargefinishm();
}
inline void bms2015pMsg::unsafe_arena_set_allocated_bmschargefinishm(
    ::BMS2015PlusInfo::BMSChargingEnd* bmschargefinishm) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmschargefinishm_);
  }
  bmschargefinishm_ = bmschargefinishm;
  if (bmschargefinishm) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:BMS2015PlusInfo.bms2015pMsg.BmsChargeFinishM)
}
inline ::BMS2015PlusInfo::BMSChargingEnd* bms2015pMsg::release_bmschargefinishm() {
  
  ::BMS2015PlusInfo::BMSChargingEnd* temp = bmschargefinishm_;
  bmschargefinishm_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::BMS2015PlusInfo::BMSChargingEnd* bms2015pMsg::unsafe_arena_release_bmschargefinishm() {
  // @@protoc_insertion_point(field_release:BMS2015PlusInfo.bms2015pMsg.BmsChargeFinishM)
  
  ::BMS2015PlusInfo::BMSChargingEnd* temp = bmschargefinishm_;
  bmschargefinishm_ = nullptr;
  return temp;
}
inline ::BMS2015PlusInfo::BMSChargingEnd* bms2015pMsg::_internal_mutable_bmschargefinishm() {
  
  if (bmschargefinishm_ == nullptr) {
    auto* p = CreateMaybeMessage<::BMS2015PlusInfo::BMSChargingEnd>(GetArenaForAllocation());
    bmschargefinishm_ = p;
  }
  return bmschargefinishm_;
}
inline ::BMS2015PlusInfo::BMSChargingEnd* bms2015pMsg::mutable_bmschargefinishm() {
  ::BMS2015PlusInfo::BMSChargingEnd* _msg = _internal_mutable_bmschargefinishm();
  // @@protoc_insertion_point(field_mutable:BMS2015PlusInfo.bms2015pMsg.BmsChargeFinishM)
  return _msg;
}
inline void bms2015pMsg::set_allocated_bmschargefinishm(::BMS2015PlusInfo::BMSChargingEnd* bmschargefinishm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete bmschargefinishm_;
  }
  if (bmschargefinishm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::BMS2015PlusInfo::BMSChargingEnd>::GetOwningArena(bmschargefinishm);
    if (message_arena != submessage_arena) {
      bmschargefinishm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bmschargefinishm, submessage_arena);
    }
    
  } else {
    
  }
  bmschargefinishm_ = bmschargefinishm;
  // @@protoc_insertion_point(field_set_allocated:BMS2015PlusInfo.bms2015pMsg.BmsChargeFinishM)
}

// .BMS2015PlusInfo.AuthenMsg BmsAuthenM = 7;
inline bool bms2015pMsg::_internal_has_bmsauthenm() const {
  return this != internal_default_instance() && bmsauthenm_ != nullptr;
}
inline bool bms2015pMsg::has_bmsauthenm() const {
  return _internal_has_bmsauthenm();
}
inline void bms2015pMsg::clear_bmsauthenm() {
  if (GetArenaForAllocation() == nullptr && bmsauthenm_ != nullptr) {
    delete bmsauthenm_;
  }
  bmsauthenm_ = nullptr;
}
inline const ::BMS2015PlusInfo::AuthenMsg& bms2015pMsg::_internal_bmsauthenm() const {
  const ::BMS2015PlusInfo::AuthenMsg* p = bmsauthenm_;
  return p != nullptr ? *p : reinterpret_cast<const ::BMS2015PlusInfo::AuthenMsg&>(
      ::BMS2015PlusInfo::_AuthenMsg_default_instance_);
}
inline const ::BMS2015PlusInfo::AuthenMsg& bms2015pMsg::bmsauthenm() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.bms2015pMsg.BmsAuthenM)
  return _internal_bmsauthenm();
}
inline void bms2015pMsg::unsafe_arena_set_allocated_bmsauthenm(
    ::BMS2015PlusInfo::AuthenMsg* bmsauthenm) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmsauthenm_);
  }
  bmsauthenm_ = bmsauthenm;
  if (bmsauthenm) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:BMS2015PlusInfo.bms2015pMsg.BmsAuthenM)
}
inline ::BMS2015PlusInfo::AuthenMsg* bms2015pMsg::release_bmsauthenm() {
  
  ::BMS2015PlusInfo::AuthenMsg* temp = bmsauthenm_;
  bmsauthenm_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::BMS2015PlusInfo::AuthenMsg* bms2015pMsg::unsafe_arena_release_bmsauthenm() {
  // @@protoc_insertion_point(field_release:BMS2015PlusInfo.bms2015pMsg.BmsAuthenM)
  
  ::BMS2015PlusInfo::AuthenMsg* temp = bmsauthenm_;
  bmsauthenm_ = nullptr;
  return temp;
}
inline ::BMS2015PlusInfo::AuthenMsg* bms2015pMsg::_internal_mutable_bmsauthenm() {
  
  if (bmsauthenm_ == nullptr) {
    auto* p = CreateMaybeMessage<::BMS2015PlusInfo::AuthenMsg>(GetArenaForAllocation());
    bmsauthenm_ = p;
  }
  return bmsauthenm_;
}
inline ::BMS2015PlusInfo::AuthenMsg* bms2015pMsg::mutable_bmsauthenm() {
  ::BMS2015PlusInfo::AuthenMsg* _msg = _internal_mutable_bmsauthenm();
  // @@protoc_insertion_point(field_mutable:BMS2015PlusInfo.bms2015pMsg.BmsAuthenM)
  return _msg;
}
inline void bms2015pMsg::set_allocated_bmsauthenm(::BMS2015PlusInfo::AuthenMsg* bmsauthenm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete bmsauthenm_;
  }
  if (bmsauthenm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::BMS2015PlusInfo::AuthenMsg>::GetOwningArena(bmsauthenm);
    if (message_arena != submessage_arena) {
      bmsauthenm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bmsauthenm, submessage_arena);
    }
    
  } else {
    
  }
  bmsauthenm_ = bmsauthenm;
  // @@protoc_insertion_point(field_set_allocated:BMS2015PlusInfo.bms2015pMsg.BmsAuthenM)
}

// .BMS2015PlusInfo.ReserveMsg BmsReserveM = 8;
inline bool bms2015pMsg::_internal_has_bmsreservem() const {
  return this != internal_default_instance() && bmsreservem_ != nullptr;
}
inline bool bms2015pMsg::has_bmsreservem() const {
  return _internal_has_bmsreservem();
}
inline void bms2015pMsg::clear_bmsreservem() {
  if (GetArenaForAllocation() == nullptr && bmsreservem_ != nullptr) {
    delete bmsreservem_;
  }
  bmsreservem_ = nullptr;
}
inline const ::BMS2015PlusInfo::ReserveMsg& bms2015pMsg::_internal_bmsreservem() const {
  const ::BMS2015PlusInfo::ReserveMsg* p = bmsreservem_;
  return p != nullptr ? *p : reinterpret_cast<const ::BMS2015PlusInfo::ReserveMsg&>(
      ::BMS2015PlusInfo::_ReserveMsg_default_instance_);
}
inline const ::BMS2015PlusInfo::ReserveMsg& bms2015pMsg::bmsreservem() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.bms2015pMsg.BmsReserveM)
  return _internal_bmsreservem();
}
inline void bms2015pMsg::unsafe_arena_set_allocated_bmsreservem(
    ::BMS2015PlusInfo::ReserveMsg* bmsreservem) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmsreservem_);
  }
  bmsreservem_ = bmsreservem;
  if (bmsreservem) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:BMS2015PlusInfo.bms2015pMsg.BmsReserveM)
}
inline ::BMS2015PlusInfo::ReserveMsg* bms2015pMsg::release_bmsreservem() {
  
  ::BMS2015PlusInfo::ReserveMsg* temp = bmsreservem_;
  bmsreservem_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::BMS2015PlusInfo::ReserveMsg* bms2015pMsg::unsafe_arena_release_bmsreservem() {
  // @@protoc_insertion_point(field_release:BMS2015PlusInfo.bms2015pMsg.BmsReserveM)
  
  ::BMS2015PlusInfo::ReserveMsg* temp = bmsreservem_;
  bmsreservem_ = nullptr;
  return temp;
}
inline ::BMS2015PlusInfo::ReserveMsg* bms2015pMsg::_internal_mutable_bmsreservem() {
  
  if (bmsreservem_ == nullptr) {
    auto* p = CreateMaybeMessage<::BMS2015PlusInfo::ReserveMsg>(GetArenaForAllocation());
    bmsreservem_ = p;
  }
  return bmsreservem_;
}
inline ::BMS2015PlusInfo::ReserveMsg* bms2015pMsg::mutable_bmsreservem() {
  ::BMS2015PlusInfo::ReserveMsg* _msg = _internal_mutable_bmsreservem();
  // @@protoc_insertion_point(field_mutable:BMS2015PlusInfo.bms2015pMsg.BmsReserveM)
  return _msg;
}
inline void bms2015pMsg::set_allocated_bmsreservem(::BMS2015PlusInfo::ReserveMsg* bmsreservem) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete bmsreservem_;
  }
  if (bmsreservem) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::BMS2015PlusInfo::ReserveMsg>::GetOwningArena(bmsreservem);
    if (message_arena != submessage_arena) {
      bmsreservem = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bmsreservem, submessage_arena);
    }
    
  } else {
    
  }
  bmsreservem_ = bmsreservem;
  // @@protoc_insertion_point(field_set_allocated:BMS2015PlusInfo.bms2015pMsg.BmsReserveM)
}

// .BMS2015PlusInfo.SelfcheckMsg BmsSelfCheckM = 9;
inline bool bms2015pMsg::_internal_has_bmsselfcheckm() const {
  return this != internal_default_instance() && bmsselfcheckm_ != nullptr;
}
inline bool bms2015pMsg::has_bmsselfcheckm() const {
  return _internal_has_bmsselfcheckm();
}
inline void bms2015pMsg::clear_bmsselfcheckm() {
  if (GetArenaForAllocation() == nullptr && bmsselfcheckm_ != nullptr) {
    delete bmsselfcheckm_;
  }
  bmsselfcheckm_ = nullptr;
}
inline const ::BMS2015PlusInfo::SelfcheckMsg& bms2015pMsg::_internal_bmsselfcheckm() const {
  const ::BMS2015PlusInfo::SelfcheckMsg* p = bmsselfcheckm_;
  return p != nullptr ? *p : reinterpret_cast<const ::BMS2015PlusInfo::SelfcheckMsg&>(
      ::BMS2015PlusInfo::_SelfcheckMsg_default_instance_);
}
inline const ::BMS2015PlusInfo::SelfcheckMsg& bms2015pMsg::bmsselfcheckm() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.bms2015pMsg.BmsSelfCheckM)
  return _internal_bmsselfcheckm();
}
inline void bms2015pMsg::unsafe_arena_set_allocated_bmsselfcheckm(
    ::BMS2015PlusInfo::SelfcheckMsg* bmsselfcheckm) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmsselfcheckm_);
  }
  bmsselfcheckm_ = bmsselfcheckm;
  if (bmsselfcheckm) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:BMS2015PlusInfo.bms2015pMsg.BmsSelfCheckM)
}
inline ::BMS2015PlusInfo::SelfcheckMsg* bms2015pMsg::release_bmsselfcheckm() {
  
  ::BMS2015PlusInfo::SelfcheckMsg* temp = bmsselfcheckm_;
  bmsselfcheckm_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::BMS2015PlusInfo::SelfcheckMsg* bms2015pMsg::unsafe_arena_release_bmsselfcheckm() {
  // @@protoc_insertion_point(field_release:BMS2015PlusInfo.bms2015pMsg.BmsSelfCheckM)
  
  ::BMS2015PlusInfo::SelfcheckMsg* temp = bmsselfcheckm_;
  bmsselfcheckm_ = nullptr;
  return temp;
}
inline ::BMS2015PlusInfo::SelfcheckMsg* bms2015pMsg::_internal_mutable_bmsselfcheckm() {
  
  if (bmsselfcheckm_ == nullptr) {
    auto* p = CreateMaybeMessage<::BMS2015PlusInfo::SelfcheckMsg>(GetArenaForAllocation());
    bmsselfcheckm_ = p;
  }
  return bmsselfcheckm_;
}
inline ::BMS2015PlusInfo::SelfcheckMsg* bms2015pMsg::mutable_bmsselfcheckm() {
  ::BMS2015PlusInfo::SelfcheckMsg* _msg = _internal_mutable_bmsselfcheckm();
  // @@protoc_insertion_point(field_mutable:BMS2015PlusInfo.bms2015pMsg.BmsSelfCheckM)
  return _msg;
}
inline void bms2015pMsg::set_allocated_bmsselfcheckm(::BMS2015PlusInfo::SelfcheckMsg* bmsselfcheckm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete bmsselfcheckm_;
  }
  if (bmsselfcheckm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::BMS2015PlusInfo::SelfcheckMsg>::GetOwningArena(bmsselfcheckm);
    if (message_arena != submessage_arena) {
      bmsselfcheckm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bmsselfcheckm, submessage_arena);
    }
    
  } else {
    
  }
  bmsselfcheckm_ = bmsselfcheckm;
  // @@protoc_insertion_point(field_set_allocated:BMS2015PlusInfo.bms2015pMsg.BmsSelfCheckM)
}

// .BMS2015PlusInfo.VehicelState BmsVehicelStateM = 10;
inline bool bms2015pMsg::_internal_has_bmsvehicelstatem() const {
  return this != internal_default_instance() && bmsvehicelstatem_ != nullptr;
}
inline bool bms2015pMsg::has_bmsvehicelstatem() const {
  return _internal_has_bmsvehicelstatem();
}
inline void bms2015pMsg::clear_bmsvehicelstatem() {
  if (GetArenaForAllocation() == nullptr && bmsvehicelstatem_ != nullptr) {
    delete bmsvehicelstatem_;
  }
  bmsvehicelstatem_ = nullptr;
}
inline const ::BMS2015PlusInfo::VehicelState& bms2015pMsg::_internal_bmsvehicelstatem() const {
  const ::BMS2015PlusInfo::VehicelState* p = bmsvehicelstatem_;
  return p != nullptr ? *p : reinterpret_cast<const ::BMS2015PlusInfo::VehicelState&>(
      ::BMS2015PlusInfo::_VehicelState_default_instance_);
}
inline const ::BMS2015PlusInfo::VehicelState& bms2015pMsg::bmsvehicelstatem() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.bms2015pMsg.BmsVehicelStateM)
  return _internal_bmsvehicelstatem();
}
inline void bms2015pMsg::unsafe_arena_set_allocated_bmsvehicelstatem(
    ::BMS2015PlusInfo::VehicelState* bmsvehicelstatem) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmsvehicelstatem_);
  }
  bmsvehicelstatem_ = bmsvehicelstatem;
  if (bmsvehicelstatem) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:BMS2015PlusInfo.bms2015pMsg.BmsVehicelStateM)
}
inline ::BMS2015PlusInfo::VehicelState* bms2015pMsg::release_bmsvehicelstatem() {
  
  ::BMS2015PlusInfo::VehicelState* temp = bmsvehicelstatem_;
  bmsvehicelstatem_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::BMS2015PlusInfo::VehicelState* bms2015pMsg::unsafe_arena_release_bmsvehicelstatem() {
  // @@protoc_insertion_point(field_release:BMS2015PlusInfo.bms2015pMsg.BmsVehicelStateM)
  
  ::BMS2015PlusInfo::VehicelState* temp = bmsvehicelstatem_;
  bmsvehicelstatem_ = nullptr;
  return temp;
}
inline ::BMS2015PlusInfo::VehicelState* bms2015pMsg::_internal_mutable_bmsvehicelstatem() {
  
  if (bmsvehicelstatem_ == nullptr) {
    auto* p = CreateMaybeMessage<::BMS2015PlusInfo::VehicelState>(GetArenaForAllocation());
    bmsvehicelstatem_ = p;
  }
  return bmsvehicelstatem_;
}
inline ::BMS2015PlusInfo::VehicelState* bms2015pMsg::mutable_bmsvehicelstatem() {
  ::BMS2015PlusInfo::VehicelState* _msg = _internal_mutable_bmsvehicelstatem();
  // @@protoc_insertion_point(field_mutable:BMS2015PlusInfo.bms2015pMsg.BmsVehicelStateM)
  return _msg;
}
inline void bms2015pMsg::set_allocated_bmsvehicelstatem(::BMS2015PlusInfo::VehicelState* bmsvehicelstatem) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete bmsvehicelstatem_;
  }
  if (bmsvehicelstatem) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::BMS2015PlusInfo::VehicelState>::GetOwningArena(bmsvehicelstatem);
    if (message_arena != submessage_arena) {
      bmsvehicelstatem = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bmsvehicelstatem, submessage_arena);
    }
    
  } else {
    
  }
  bmsvehicelstatem_ = bmsvehicelstatem;
  // @@protoc_insertion_point(field_set_allocated:BMS2015PlusInfo.bms2015pMsg.BmsVehicelStateM)
}

// .BMS2015PlusInfo.PowerSupplyMsg PowerSupplyM = 11;
inline bool bms2015pMsg::_internal_has_powersupplym() const {
  return this != internal_default_instance() && powersupplym_ != nullptr;
}
inline bool bms2015pMsg::has_powersupplym() const {
  return _internal_has_powersupplym();
}
inline void bms2015pMsg::clear_powersupplym() {
  if (GetArenaForAllocation() == nullptr && powersupplym_ != nullptr) {
    delete powersupplym_;
  }
  powersupplym_ = nullptr;
}
inline const ::BMS2015PlusInfo::PowerSupplyMsg& bms2015pMsg::_internal_powersupplym() const {
  const ::BMS2015PlusInfo::PowerSupplyMsg* p = powersupplym_;
  return p != nullptr ? *p : reinterpret_cast<const ::BMS2015PlusInfo::PowerSupplyMsg&>(
      ::BMS2015PlusInfo::_PowerSupplyMsg_default_instance_);
}
inline const ::BMS2015PlusInfo::PowerSupplyMsg& bms2015pMsg::powersupplym() const {
  // @@protoc_insertion_point(field_get:BMS2015PlusInfo.bms2015pMsg.PowerSupplyM)
  return _internal_powersupplym();
}
inline void bms2015pMsg::unsafe_arena_set_allocated_powersupplym(
    ::BMS2015PlusInfo::PowerSupplyMsg* powersupplym) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(powersupplym_);
  }
  powersupplym_ = powersupplym;
  if (powersupplym) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:BMS2015PlusInfo.bms2015pMsg.PowerSupplyM)
}
inline ::BMS2015PlusInfo::PowerSupplyMsg* bms2015pMsg::release_powersupplym() {
  
  ::BMS2015PlusInfo::PowerSupplyMsg* temp = powersupplym_;
  powersupplym_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::BMS2015PlusInfo::PowerSupplyMsg* bms2015pMsg::unsafe_arena_release_powersupplym() {
  // @@protoc_insertion_point(field_release:BMS2015PlusInfo.bms2015pMsg.PowerSupplyM)
  
  ::BMS2015PlusInfo::PowerSupplyMsg* temp = powersupplym_;
  powersupplym_ = nullptr;
  return temp;
}
inline ::BMS2015PlusInfo::PowerSupplyMsg* bms2015pMsg::_internal_mutable_powersupplym() {
  
  if (powersupplym_ == nullptr) {
    auto* p = CreateMaybeMessage<::BMS2015PlusInfo::PowerSupplyMsg>(GetArenaForAllocation());
    powersupplym_ = p;
  }
  return powersupplym_;
}
inline ::BMS2015PlusInfo::PowerSupplyMsg* bms2015pMsg::mutable_powersupplym() {
  ::BMS2015PlusInfo::PowerSupplyMsg* _msg = _internal_mutable_powersupplym();
  // @@protoc_insertion_point(field_mutable:BMS2015PlusInfo.bms2015pMsg.PowerSupplyM)
  return _msg;
}
inline void bms2015pMsg::set_allocated_powersupplym(::BMS2015PlusInfo::PowerSupplyMsg* powersupplym) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete powersupplym_;
  }
  if (powersupplym) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::BMS2015PlusInfo::PowerSupplyMsg>::GetOwningArena(powersupplym);
    if (message_arena != submessage_arena) {
      powersupplym = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, powersupplym, submessage_arena);
    }
    
  } else {
    
  }
  powersupplym_ = powersupplym;
  // @@protoc_insertion_point(field_set_allocated:BMS2015PlusInfo.bms2015pMsg.PowerSupplyM)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace BMS2015PlusInfo

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::BMS2015PlusInfo::ChargeState> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::BMS2015PlusInfo::ChargeState>() {
  return ::BMS2015PlusInfo::ChargeState_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_GCU_5fBMS2015P_5fINFO_2eproto
