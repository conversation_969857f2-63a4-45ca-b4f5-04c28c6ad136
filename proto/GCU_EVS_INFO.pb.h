// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GCU_EVS_INFO.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_GCU_5fEVS_5fINFO_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_GCU_5fEVS_5fINFO_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3017000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3017003 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_GCU_5fEVS_5fINFO_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_GCU_5fEVS_5fINFO_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[16]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GCU_5fEVS_5fINFO_2eproto;
namespace EVSInfo {
class EVAuthenMsg;
struct EVAuthenMsgDefaultTypeInternal;
extern EVAuthenMsgDefaultTypeInternal _EVAuthenMsg_default_instance_;
class EVChargingEndMsg;
struct EVChargingEndMsgDefaultTypeInternal;
extern EVChargingEndMsgDefaultTypeInternal _EVChargingEndMsg_default_instance_;
class EVChargingMsg;
struct EVChargingMsgDefaultTypeInternal;
extern EVChargingMsgDefaultTypeInternal _EVChargingMsg_default_instance_;
class EVChgParaConfigMsg;
struct EVChgParaConfigMsgDefaultTypeInternal;
extern EVChgParaConfigMsgDefaultTypeInternal _EVChgParaConfigMsg_default_instance_;
class EVElectricCtrl;
struct EVElectricCtrlDefaultTypeInternal;
extern EVElectricCtrlDefaultTypeInternal _EVElectricCtrl_default_instance_;
class EVFunConferAckMsg;
struct EVFunConferAckMsgDefaultTypeInternal;
extern EVFunConferAckMsgDefaultTypeInternal _EVFunConferAckMsg_default_instance_;
class EVFunConferMsg;
struct EVFunConferMsgDefaultTypeInternal;
extern EVFunConferMsgDefaultTypeInternal _EVFunConferMsg_default_instance_;
class EVInsultCtrl;
struct EVInsultCtrlDefaultTypeInternal;
extern EVInsultCtrlDefaultTypeInternal _EVInsultCtrl_default_instance_;
class EVMsgCtrl;
struct EVMsgCtrlDefaultTypeInternal;
extern EVMsgCtrlDefaultTypeInternal _EVMsgCtrl_default_instance_;
class EVPowerSupplyMsg;
struct EVPowerSupplyMsgDefaultTypeInternal;
extern EVPowerSupplyMsgDefaultTypeInternal _EVPowerSupplyMsg_default_instance_;
class EVProtoConferMsg;
struct EVProtoConferMsgDefaultTypeInternal;
extern EVProtoConferMsgDefaultTypeInternal _EVProtoConferMsg_default_instance_;
class EVReserveMsg;
struct EVReserveMsgDefaultTypeInternal;
extern EVReserveMsgDefaultTypeInternal _EVReserveMsg_default_instance_;
class EVSStepMsg;
struct EVSStepMsgDefaultTypeInternal;
extern EVSStepMsgDefaultTypeInternal _EVSStepMsg_default_instance_;
class EVStateMsg;
struct EVStateMsgDefaultTypeInternal;
extern EVStateMsgDefaultTypeInternal _EVStateMsg_default_instance_;
class IpMsg;
struct IpMsgDefaultTypeInternal;
extern IpMsgDefaultTypeInternal _IpMsg_default_instance_;
class StepPara;
struct StepParaDefaultTypeInternal;
extern StepParaDefaultTypeInternal _StepPara_default_instance_;
}  // namespace EVSInfo
PROTOBUF_NAMESPACE_OPEN
template<> ::EVSInfo::EVAuthenMsg* Arena::CreateMaybeMessage<::EVSInfo::EVAuthenMsg>(Arena*);
template<> ::EVSInfo::EVChargingEndMsg* Arena::CreateMaybeMessage<::EVSInfo::EVChargingEndMsg>(Arena*);
template<> ::EVSInfo::EVChargingMsg* Arena::CreateMaybeMessage<::EVSInfo::EVChargingMsg>(Arena*);
template<> ::EVSInfo::EVChgParaConfigMsg* Arena::CreateMaybeMessage<::EVSInfo::EVChgParaConfigMsg>(Arena*);
template<> ::EVSInfo::EVElectricCtrl* Arena::CreateMaybeMessage<::EVSInfo::EVElectricCtrl>(Arena*);
template<> ::EVSInfo::EVFunConferAckMsg* Arena::CreateMaybeMessage<::EVSInfo::EVFunConferAckMsg>(Arena*);
template<> ::EVSInfo::EVFunConferMsg* Arena::CreateMaybeMessage<::EVSInfo::EVFunConferMsg>(Arena*);
template<> ::EVSInfo::EVInsultCtrl* Arena::CreateMaybeMessage<::EVSInfo::EVInsultCtrl>(Arena*);
template<> ::EVSInfo::EVMsgCtrl* Arena::CreateMaybeMessage<::EVSInfo::EVMsgCtrl>(Arena*);
template<> ::EVSInfo::EVPowerSupplyMsg* Arena::CreateMaybeMessage<::EVSInfo::EVPowerSupplyMsg>(Arena*);
template<> ::EVSInfo::EVProtoConferMsg* Arena::CreateMaybeMessage<::EVSInfo::EVProtoConferMsg>(Arena*);
template<> ::EVSInfo::EVReserveMsg* Arena::CreateMaybeMessage<::EVSInfo::EVReserveMsg>(Arena*);
template<> ::EVSInfo::EVSStepMsg* Arena::CreateMaybeMessage<::EVSInfo::EVSStepMsg>(Arena*);
template<> ::EVSInfo::EVStateMsg* Arena::CreateMaybeMessage<::EVSInfo::EVStateMsg>(Arena*);
template<> ::EVSInfo::IpMsg* Arena::CreateMaybeMessage<::EVSInfo::IpMsg>(Arena*);
template<> ::EVSInfo::StepPara* Arena::CreateMaybeMessage<::EVSInfo::StepPara>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace EVSInfo {

// ===================================================================

class EVProtoConferMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:EVSInfo.EVProtoConferMsg) */ {
 public:
  inline EVProtoConferMsg() : EVProtoConferMsg(nullptr) {}
  ~EVProtoConferMsg() override;
  explicit constexpr EVProtoConferMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVProtoConferMsg(const EVProtoConferMsg& from);
  EVProtoConferMsg(EVProtoConferMsg&& from) noexcept
    : EVProtoConferMsg() {
    *this = ::std::move(from);
  }

  inline EVProtoConferMsg& operator=(const EVProtoConferMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVProtoConferMsg& operator=(EVProtoConferMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVProtoConferMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVProtoConferMsg* internal_default_instance() {
    return reinterpret_cast<const EVProtoConferMsg*>(
               &_EVProtoConferMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(EVProtoConferMsg& a, EVProtoConferMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(EVProtoConferMsg* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVProtoConferMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVProtoConferMsg* New() const final {
    return new EVProtoConferMsg();
  }

  EVProtoConferMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVProtoConferMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVProtoConferMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVProtoConferMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVProtoConferMsg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "EVSInfo.EVProtoConferMsg";
  }
  protected:
  explicit EVProtoConferMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGbVersionFieldNumber = 2,
    kCanTypeFieldNumber = 1,
    kGuidanceVersionFieldNumber = 3,
    kTransportVersionFieldNumber = 4,
    kConferResFieldNumber = 5,
  };
  // bytes gbVersion = 2;
  void clear_gbversion();
  const std::string& gbversion() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_gbversion(ArgT0&& arg0, ArgT... args);
  std::string* mutable_gbversion();
  PROTOBUF_MUST_USE_RESULT std::string* release_gbversion();
  void set_allocated_gbversion(std::string* gbversion);
  private:
  const std::string& _internal_gbversion() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_gbversion(const std::string& value);
  std::string* _internal_mutable_gbversion();
  public:

  // uint32 canType = 1;
  void clear_cantype();
  ::PROTOBUF_NAMESPACE_ID::uint32 cantype() const;
  void set_cantype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_cantype() const;
  void _internal_set_cantype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 guidanceVersion = 3;
  void clear_guidanceversion();
  ::PROTOBUF_NAMESPACE_ID::uint32 guidanceversion() const;
  void set_guidanceversion(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_guidanceversion() const;
  void _internal_set_guidanceversion(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 transportVersion = 4;
  void clear_transportversion();
  ::PROTOBUF_NAMESPACE_ID::uint32 transportversion() const;
  void set_transportversion(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_transportversion() const;
  void _internal_set_transportversion(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 conferRes = 5;
  void clear_conferres();
  ::PROTOBUF_NAMESPACE_ID::uint32 conferres() const;
  void set_conferres(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_conferres() const;
  void _internal_set_conferres(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:EVSInfo.EVProtoConferMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr gbversion_;
  ::PROTOBUF_NAMESPACE_ID::uint32 cantype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 guidanceversion_;
  ::PROTOBUF_NAMESPACE_ID::uint32 transportversion_;
  ::PROTOBUF_NAMESPACE_ID::uint32 conferres_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class EVFunConferMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:EVSInfo.EVFunConferMsg) */ {
 public:
  inline EVFunConferMsg() : EVFunConferMsg(nullptr) {}
  ~EVFunConferMsg() override;
  explicit constexpr EVFunConferMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVFunConferMsg(const EVFunConferMsg& from);
  EVFunConferMsg(EVFunConferMsg&& from) noexcept
    : EVFunConferMsg() {
    *this = ::std::move(from);
  }

  inline EVFunConferMsg& operator=(const EVFunConferMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVFunConferMsg& operator=(EVFunConferMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVFunConferMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVFunConferMsg* internal_default_instance() {
    return reinterpret_cast<const EVFunConferMsg*>(
               &_EVFunConferMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(EVFunConferMsg& a, EVFunConferMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(EVFunConferMsg* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVFunConferMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVFunConferMsg* New() const final {
    return new EVFunConferMsg();
  }

  EVFunConferMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVFunConferMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVFunConferMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVFunConferMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVFunConferMsg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "EVSInfo.EVFunConferMsg";
  }
  protected:
  explicit EVFunConferMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kConfigFDCFieldNumber = 1,
    kAuthenFDCFieldNumber = 2,
    kAppointFDCFieldNumber = 3,
    kSelfCheckFDCFieldNumber = 4,
    kPowerSupplyFDCFieldNumber = 5,
    kEnergyTransferFDCFieldNumber = 6,
    kEndFDCFieldNumber = 7,
  };
  // uint32 configFDC = 1;
  void clear_configfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 configfdc() const;
  void set_configfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_configfdc() const;
  void _internal_set_configfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 authenFDC = 2;
  void clear_authenfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 authenfdc() const;
  void set_authenfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_authenfdc() const;
  void _internal_set_authenfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 appointFDC = 3;
  void clear_appointfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 appointfdc() const;
  void set_appointfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_appointfdc() const;
  void _internal_set_appointfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 selfCheckFDC = 4;
  void clear_selfcheckfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 selfcheckfdc() const;
  void set_selfcheckfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_selfcheckfdc() const;
  void _internal_set_selfcheckfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 powerSupplyFDC = 5;
  void clear_powersupplyfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 powersupplyfdc() const;
  void set_powersupplyfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_powersupplyfdc() const;
  void _internal_set_powersupplyfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 energyTransferFDC = 6;
  void clear_energytransferfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 energytransferfdc() const;
  void set_energytransferfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_energytransferfdc() const;
  void _internal_set_energytransferfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 endFDC = 7;
  void clear_endfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 endfdc() const;
  void set_endfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_endfdc() const;
  void _internal_set_endfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:EVSInfo.EVFunConferMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 configfdc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 authenfdc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 appointfdc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 selfcheckfdc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 powersupplyfdc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 energytransferfdc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 endfdc_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class EVChgParaConfigMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:EVSInfo.EVChgParaConfigMsg) */ {
 public:
  inline EVChgParaConfigMsg() : EVChgParaConfigMsg(nullptr) {}
  ~EVChgParaConfigMsg() override;
  explicit constexpr EVChgParaConfigMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVChgParaConfigMsg(const EVChgParaConfigMsg& from);
  EVChgParaConfigMsg(EVChgParaConfigMsg&& from) noexcept
    : EVChgParaConfigMsg() {
    *this = ::std::move(from);
  }

  inline EVChgParaConfigMsg& operator=(const EVChgParaConfigMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVChgParaConfigMsg& operator=(EVChgParaConfigMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVChgParaConfigMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVChgParaConfigMsg* internal_default_instance() {
    return reinterpret_cast<const EVChgParaConfigMsg*>(
               &_EVChgParaConfigMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(EVChgParaConfigMsg& a, EVChgParaConfigMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(EVChgParaConfigMsg* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVChgParaConfigMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVChgParaConfigMsg* New() const final {
    return new EVChgParaConfigMsg();
  }

  EVChgParaConfigMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVChgParaConfigMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVChgParaConfigMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVChgParaConfigMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVChgParaConfigMsg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "EVSInfo.EVChgParaConfigMsg";
  }
  protected:
  explicit EVChgParaConfigMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCurrAllowMAXFieldNumber = 1,
    kVoltAllowMAXFieldNumber = 2,
    kEnergyAllowMAXFieldNumber = 3,
    kNowSOCFieldNumber = 4,
    kCellVoltAllowMAXFieldNumber = 5,
    kBatTempAllowMAXFieldNumber = 6,
    kRestarNumFieldNumber = 7,
  };
  // float currAllowMAX = 1;
  void clear_currallowmax();
  float currallowmax() const;
  void set_currallowmax(float value);
  private:
  float _internal_currallowmax() const;
  void _internal_set_currallowmax(float value);
  public:

  // float voltAllowMAX = 2;
  void clear_voltallowmax();
  float voltallowmax() const;
  void set_voltallowmax(float value);
  private:
  float _internal_voltallowmax() const;
  void _internal_set_voltallowmax(float value);
  public:

  // float energyAllowMAX = 3;
  void clear_energyallowmax();
  float energyallowmax() const;
  void set_energyallowmax(float value);
  private:
  float _internal_energyallowmax() const;
  void _internal_set_energyallowmax(float value);
  public:

  // float nowSOC = 4;
  void clear_nowsoc();
  float nowsoc() const;
  void set_nowsoc(float value);
  private:
  float _internal_nowsoc() const;
  void _internal_set_nowsoc(float value);
  public:

  // float cellVoltAllowMAX = 5;
  void clear_cellvoltallowmax();
  float cellvoltallowmax() const;
  void set_cellvoltallowmax(float value);
  private:
  float _internal_cellvoltallowmax() const;
  void _internal_set_cellvoltallowmax(float value);
  public:

  // float batTempAllowMAX = 6;
  void clear_battempallowmax();
  float battempallowmax() const;
  void set_battempallowmax(float value);
  private:
  float _internal_battempallowmax() const;
  void _internal_set_battempallowmax(float value);
  public:

  // uint32 restarNum = 7;
  void clear_restarnum();
  ::PROTOBUF_NAMESPACE_ID::uint32 restarnum() const;
  void set_restarnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_restarnum() const;
  void _internal_set_restarnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:EVSInfo.EVChgParaConfigMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float currallowmax_;
  float voltallowmax_;
  float energyallowmax_;
  float nowsoc_;
  float cellvoltallowmax_;
  float battempallowmax_;
  ::PROTOBUF_NAMESPACE_ID::uint32 restarnum_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class EVAuthenMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:EVSInfo.EVAuthenMsg) */ {
 public:
  inline EVAuthenMsg() : EVAuthenMsg(nullptr) {}
  ~EVAuthenMsg() override;
  explicit constexpr EVAuthenMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVAuthenMsg(const EVAuthenMsg& from);
  EVAuthenMsg(EVAuthenMsg&& from) noexcept
    : EVAuthenMsg() {
    *this = ::std::move(from);
  }

  inline EVAuthenMsg& operator=(const EVAuthenMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVAuthenMsg& operator=(EVAuthenMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVAuthenMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVAuthenMsg* internal_default_instance() {
    return reinterpret_cast<const EVAuthenMsg*>(
               &_EVAuthenMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(EVAuthenMsg& a, EVAuthenMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(EVAuthenMsg* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVAuthenMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVAuthenMsg* New() const final {
    return new EVAuthenMsg();
  }

  EVAuthenMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVAuthenMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVAuthenMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVAuthenMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVAuthenMsg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "EVSInfo.EVAuthenMsg";
  }
  protected:
  explicit EVAuthenMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEVINFieldNumber = 2,
    kAuthenWaitTimeFieldNumber = 1,
    kNextFDCFieldNumber = 3,
  };
  // bytes eVIN = 2;
  void clear_evin();
  const std::string& evin() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_evin(ArgT0&& arg0, ArgT... args);
  std::string* mutable_evin();
  PROTOBUF_MUST_USE_RESULT std::string* release_evin();
  void set_allocated_evin(std::string* evin);
  private:
  const std::string& _internal_evin() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_evin(const std::string& value);
  std::string* _internal_mutable_evin();
  public:

  // uint32 authenWaitTime = 1;
  void clear_authenwaittime();
  ::PROTOBUF_NAMESPACE_ID::uint32 authenwaittime() const;
  void set_authenwaittime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_authenwaittime() const;
  void _internal_set_authenwaittime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 nextFDC = 3;
  void clear_nextfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 nextfdc() const;
  void set_nextfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_nextfdc() const;
  void _internal_set_nextfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:EVSInfo.EVAuthenMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr evin_;
  ::PROTOBUF_NAMESPACE_ID::uint32 authenwaittime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 nextfdc_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class EVReserveMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:EVSInfo.EVReserveMsg) */ {
 public:
  inline EVReserveMsg() : EVReserveMsg(nullptr) {}
  ~EVReserveMsg() override;
  explicit constexpr EVReserveMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVReserveMsg(const EVReserveMsg& from);
  EVReserveMsg(EVReserveMsg&& from) noexcept
    : EVReserveMsg() {
    *this = ::std::move(from);
  }

  inline EVReserveMsg& operator=(const EVReserveMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVReserveMsg& operator=(EVReserveMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVReserveMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVReserveMsg* internal_default_instance() {
    return reinterpret_cast<const EVReserveMsg*>(
               &_EVReserveMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(EVReserveMsg& a, EVReserveMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(EVReserveMsg* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVReserveMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVReserveMsg* New() const final {
    return new EVReserveMsg();
  }

  EVReserveMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVReserveMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVReserveMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVReserveMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVReserveMsg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "EVSInfo.EVReserveMsg";
  }
  protected:
  explicit EVReserveMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBmsDesireStartTimeFieldNumber = 1,
    kBmsDesireLeaveTimeFieldNumber = 2,
    kReserveResultFieldNumber = 3,
    kImmediateChargeSupportFieldNumber = 4,
  };
  // uint32 bmsDesireStartTime = 1;
  void clear_bmsdesirestarttime();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsdesirestarttime() const;
  void set_bmsdesirestarttime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsdesirestarttime() const;
  void _internal_set_bmsdesirestarttime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 bmsDesireLeaveTime = 2;
  void clear_bmsdesireleavetime();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsdesireleavetime() const;
  void set_bmsdesireleavetime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsdesireleavetime() const;
  void _internal_set_bmsdesireleavetime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 reserveResult = 3;
  void clear_reserveresult();
  ::PROTOBUF_NAMESPACE_ID::uint32 reserveresult() const;
  void set_reserveresult(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_reserveresult() const;
  void _internal_set_reserveresult(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 immediateChargeSupport = 4;
  void clear_immediatechargesupport();
  ::PROTOBUF_NAMESPACE_ID::uint32 immediatechargesupport() const;
  void set_immediatechargesupport(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_immediatechargesupport() const;
  void _internal_set_immediatechargesupport(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:EVSInfo.EVReserveMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsdesirestarttime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsdesireleavetime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 reserveresult_;
  ::PROTOBUF_NAMESPACE_ID::uint32 immediatechargesupport_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class EVPowerSupplyMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:EVSInfo.EVPowerSupplyMsg) */ {
 public:
  inline EVPowerSupplyMsg() : EVPowerSupplyMsg(nullptr) {}
  ~EVPowerSupplyMsg() override;
  explicit constexpr EVPowerSupplyMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVPowerSupplyMsg(const EVPowerSupplyMsg& from);
  EVPowerSupplyMsg(EVPowerSupplyMsg&& from) noexcept
    : EVPowerSupplyMsg() {
    *this = ::std::move(from);
  }

  inline EVPowerSupplyMsg& operator=(const EVPowerSupplyMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVPowerSupplyMsg& operator=(EVPowerSupplyMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVPowerSupplyMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVPowerSupplyMsg* internal_default_instance() {
    return reinterpret_cast<const EVPowerSupplyMsg*>(
               &_EVPowerSupplyMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(EVPowerSupplyMsg& a, EVPowerSupplyMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(EVPowerSupplyMsg* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVPowerSupplyMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVPowerSupplyMsg* New() const final {
    return new EVPowerSupplyMsg();
  }

  EVPowerSupplyMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVPowerSupplyMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVPowerSupplyMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVPowerSupplyMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVPowerSupplyMsg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "EVSInfo.EVPowerSupplyMsg";
  }
  protected:
  explicit EVPowerSupplyMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSupplyStateFieldNumber = 1,
    kSupplyVolDesireFieldNumber = 2,
    kSupplyCurrDesireFieldNumber = 3,
    kSupplyEndFieldNumber = 4,
  };
  // uint32 supplyState = 1;
  void clear_supplystate();
  ::PROTOBUF_NAMESPACE_ID::uint32 supplystate() const;
  void set_supplystate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_supplystate() const;
  void _internal_set_supplystate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // float supplyVolDesire = 2;
  void clear_supplyvoldesire();
  float supplyvoldesire() const;
  void set_supplyvoldesire(float value);
  private:
  float _internal_supplyvoldesire() const;
  void _internal_set_supplyvoldesire(float value);
  public:

  // float supplyCurrDesire = 3;
  void clear_supplycurrdesire();
  float supplycurrdesire() const;
  void set_supplycurrdesire(float value);
  private:
  float _internal_supplycurrdesire() const;
  void _internal_set_supplycurrdesire(float value);
  public:

  // uint32 supplyEnd = 4;
  void clear_supplyend();
  ::PROTOBUF_NAMESPACE_ID::uint32 supplyend() const;
  void set_supplyend(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_supplyend() const;
  void _internal_set_supplyend(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:EVSInfo.EVPowerSupplyMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 supplystate_;
  float supplyvoldesire_;
  float supplycurrdesire_;
  ::PROTOBUF_NAMESPACE_ID::uint32 supplyend_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class EVChargingMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:EVSInfo.EVChargingMsg) */ {
 public:
  inline EVChargingMsg() : EVChargingMsg(nullptr) {}
  ~EVChargingMsg() override;
  explicit constexpr EVChargingMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVChargingMsg(const EVChargingMsg& from);
  EVChargingMsg(EVChargingMsg&& from) noexcept
    : EVChargingMsg() {
    *this = ::std::move(from);
  }

  inline EVChargingMsg& operator=(const EVChargingMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVChargingMsg& operator=(EVChargingMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVChargingMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVChargingMsg* internal_default_instance() {
    return reinterpret_cast<const EVChargingMsg*>(
               &_EVChargingMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(EVChargingMsg& a, EVChargingMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(EVChargingMsg* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVChargingMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVChargingMsg* New() const final {
    return new EVChargingMsg();
  }

  EVChargingMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVChargingMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVChargingMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVChargingMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVChargingMsg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "EVSInfo.EVChargingMsg";
  }
  protected:
  explicit EVChargingMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBmsReadyFieldNumber = 1,
    kVolDemandFieldNumber = 2,
    kCurDemandFieldNumber = 3,
    kChargeModeFieldNumber = 4,
    kSocNowFieldNumber = 5,
    kResChgTimeFieldNumber = 6,
    kCellBatVolMaxFieldNumber = 7,
    kCellBatVolMinFieldNumber = 8,
    kCelltempMaxFieldNumber = 9,
    kCelltempMinFieldNumber = 10,
  };
  // uint32 bmsReady = 1;
  void clear_bmsready();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsready() const;
  void set_bmsready(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsready() const;
  void _internal_set_bmsready(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // float volDemand = 2;
  void clear_voldemand();
  float voldemand() const;
  void set_voldemand(float value);
  private:
  float _internal_voldemand() const;
  void _internal_set_voldemand(float value);
  public:

  // float curDemand = 3;
  void clear_curdemand();
  float curdemand() const;
  void set_curdemand(float value);
  private:
  float _internal_curdemand() const;
  void _internal_set_curdemand(float value);
  public:

  // uint32 chargeMode = 4;
  void clear_chargemode();
  ::PROTOBUF_NAMESPACE_ID::uint32 chargemode() const;
  void set_chargemode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chargemode() const;
  void _internal_set_chargemode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // float socNow = 5;
  void clear_socnow();
  float socnow() const;
  void set_socnow(float value);
  private:
  float _internal_socnow() const;
  void _internal_set_socnow(float value);
  public:

  // uint32 resChgTime = 6;
  void clear_reschgtime();
  ::PROTOBUF_NAMESPACE_ID::uint32 reschgtime() const;
  void set_reschgtime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_reschgtime() const;
  void _internal_set_reschgtime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // float cellBatVolMax = 7;
  void clear_cellbatvolmax();
  float cellbatvolmax() const;
  void set_cellbatvolmax(float value);
  private:
  float _internal_cellbatvolmax() const;
  void _internal_set_cellbatvolmax(float value);
  public:

  // float cellBatVolMin = 8;
  void clear_cellbatvolmin();
  float cellbatvolmin() const;
  void set_cellbatvolmin(float value);
  private:
  float _internal_cellbatvolmin() const;
  void _internal_set_cellbatvolmin(float value);
  public:

  // float celltempMax = 9;
  void clear_celltempmax();
  float celltempmax() const;
  void set_celltempmax(float value);
  private:
  float _internal_celltempmax() const;
  void _internal_set_celltempmax(float value);
  public:

  // float celltempMin = 10;
  void clear_celltempmin();
  float celltempmin() const;
  void set_celltempmin(float value);
  private:
  float _internal_celltempmin() const;
  void _internal_set_celltempmin(float value);
  public:

  // @@protoc_insertion_point(class_scope:EVSInfo.EVChargingMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsready_;
  float voldemand_;
  float curdemand_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chargemode_;
  float socnow_;
  ::PROTOBUF_NAMESPACE_ID::uint32 reschgtime_;
  float cellbatvolmax_;
  float cellbatvolmin_;
  float celltempmax_;
  float celltempmin_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class EVChargingEndMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:EVSInfo.EVChargingEndMsg) */ {
 public:
  inline EVChargingEndMsg() : EVChargingEndMsg(nullptr) {}
  ~EVChargingEndMsg() override;
  explicit constexpr EVChargingEndMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVChargingEndMsg(const EVChargingEndMsg& from);
  EVChargingEndMsg(EVChargingEndMsg&& from) noexcept
    : EVChargingEndMsg() {
    *this = ::std::move(from);
  }

  inline EVChargingEndMsg& operator=(const EVChargingEndMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVChargingEndMsg& operator=(EVChargingEndMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVChargingEndMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVChargingEndMsg* internal_default_instance() {
    return reinterpret_cast<const EVChargingEndMsg*>(
               &_EVChargingEndMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(EVChargingEndMsg& a, EVChargingEndMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(EVChargingEndMsg* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVChargingEndMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVChargingEndMsg* New() const final {
    return new EVChargingEndMsg();
  }

  EVChargingEndMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVChargingEndMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVChargingEndMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVChargingEndMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVChargingEndMsg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "EVSInfo.EVChargingEndMsg";
  }
  protected:
  explicit EVChargingEndMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEndTypeFieldNumber = 1,
    kEndCodeFieldNumber = 2,
    kEndReasonFieldNumber = 3,
    kRepeatFieldNumber = 4,
    kBmsStickCheckStateFieldNumber = 5,
  };
  // uint32 endType = 1;
  void clear_endtype();
  ::PROTOBUF_NAMESPACE_ID::uint32 endtype() const;
  void set_endtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_endtype() const;
  void _internal_set_endtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 endCode = 2;
  void clear_endcode();
  ::PROTOBUF_NAMESPACE_ID::uint32 endcode() const;
  void set_endcode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_endcode() const;
  void _internal_set_endcode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 endReason = 3;
  void clear_endreason();
  ::PROTOBUF_NAMESPACE_ID::uint32 endreason() const;
  void set_endreason(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_endreason() const;
  void _internal_set_endreason(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 repeat = 4;
  void clear_repeat();
  ::PROTOBUF_NAMESPACE_ID::uint32 repeat() const;
  void set_repeat(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_repeat() const;
  void _internal_set_repeat(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 bmsStickCheckState = 5;
  void clear_bmsstickcheckstate();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsstickcheckstate() const;
  void set_bmsstickcheckstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsstickcheckstate() const;
  void _internal_set_bmsstickcheckstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:EVSInfo.EVChargingEndMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 endtype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 endcode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 endreason_;
  ::PROTOBUF_NAMESPACE_ID::uint32 repeat_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsstickcheckstate_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class EVFunConferAckMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:EVSInfo.EVFunConferAckMsg) */ {
 public:
  inline EVFunConferAckMsg() : EVFunConferAckMsg(nullptr) {}
  ~EVFunConferAckMsg() override;
  explicit constexpr EVFunConferAckMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVFunConferAckMsg(const EVFunConferAckMsg& from);
  EVFunConferAckMsg(EVFunConferAckMsg&& from) noexcept
    : EVFunConferAckMsg() {
    *this = ::std::move(from);
  }

  inline EVFunConferAckMsg& operator=(const EVFunConferAckMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVFunConferAckMsg& operator=(EVFunConferAckMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVFunConferAckMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVFunConferAckMsg* internal_default_instance() {
    return reinterpret_cast<const EVFunConferAckMsg*>(
               &_EVFunConferAckMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(EVFunConferAckMsg& a, EVFunConferAckMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(EVFunConferAckMsg* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVFunConferAckMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVFunConferAckMsg* New() const final {
    return new EVFunConferAckMsg();
  }

  EVFunConferAckMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVFunConferAckMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVFunConferAckMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVFunConferAckMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVFunConferAckMsg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "EVSInfo.EVFunConferAckMsg";
  }
  protected:
  explicit EVFunConferAckMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFunConferAckFieldNumber = 1,
    kConfigAckFieldNumber = 2,
    kAuthenAckFieldNumber = 3,
    kAppointAckFieldNumber = 4,
    kSelfCheckAckFieldNumber = 5,
    kPowerSupplyAckFieldNumber = 6,
    kEnergyTransferAckFieldNumber = 7,
    kEndAckFieldNumber = 8,
  };
  // uint32 funConferAck = 1;
  void clear_funconferack();
  ::PROTOBUF_NAMESPACE_ID::uint32 funconferack() const;
  void set_funconferack(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_funconferack() const;
  void _internal_set_funconferack(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 configAck = 2;
  void clear_configack();
  ::PROTOBUF_NAMESPACE_ID::uint32 configack() const;
  void set_configack(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_configack() const;
  void _internal_set_configack(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 authenAck = 3;
  void clear_authenack();
  ::PROTOBUF_NAMESPACE_ID::uint32 authenack() const;
  void set_authenack(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_authenack() const;
  void _internal_set_authenack(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 appointAck = 4;
  void clear_appointack();
  ::PROTOBUF_NAMESPACE_ID::uint32 appointack() const;
  void set_appointack(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_appointack() const;
  void _internal_set_appointack(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 selfCheckAck = 5;
  void clear_selfcheckack();
  ::PROTOBUF_NAMESPACE_ID::uint32 selfcheckack() const;
  void set_selfcheckack(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_selfcheckack() const;
  void _internal_set_selfcheckack(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 powerSupplyAck = 6;
  void clear_powersupplyack();
  ::PROTOBUF_NAMESPACE_ID::uint32 powersupplyack() const;
  void set_powersupplyack(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_powersupplyack() const;
  void _internal_set_powersupplyack(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 energyTransferAck = 7;
  void clear_energytransferack();
  ::PROTOBUF_NAMESPACE_ID::uint32 energytransferack() const;
  void set_energytransferack(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_energytransferack() const;
  void _internal_set_energytransferack(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 endAck = 8;
  void clear_endack();
  ::PROTOBUF_NAMESPACE_ID::uint32 endack() const;
  void set_endack(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_endack() const;
  void _internal_set_endack(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:EVSInfo.EVFunConferAckMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 funconferack_;
  ::PROTOBUF_NAMESPACE_ID::uint32 configack_;
  ::PROTOBUF_NAMESPACE_ID::uint32 authenack_;
  ::PROTOBUF_NAMESPACE_ID::uint32 appointack_;
  ::PROTOBUF_NAMESPACE_ID::uint32 selfcheckack_;
  ::PROTOBUF_NAMESPACE_ID::uint32 powersupplyack_;
  ::PROTOBUF_NAMESPACE_ID::uint32 energytransferack_;
  ::PROTOBUF_NAMESPACE_ID::uint32 endack_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class EVElectricCtrl final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:EVSInfo.EVElectricCtrl) */ {
 public:
  inline EVElectricCtrl() : EVElectricCtrl(nullptr) {}
  ~EVElectricCtrl() override;
  explicit constexpr EVElectricCtrl(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVElectricCtrl(const EVElectricCtrl& from);
  EVElectricCtrl(EVElectricCtrl&& from) noexcept
    : EVElectricCtrl() {
    *this = ::std::move(from);
  }

  inline EVElectricCtrl& operator=(const EVElectricCtrl& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVElectricCtrl& operator=(EVElectricCtrl&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVElectricCtrl& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVElectricCtrl* internal_default_instance() {
    return reinterpret_cast<const EVElectricCtrl*>(
               &_EVElectricCtrl_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(EVElectricCtrl& a, EVElectricCtrl& b) {
    a.Swap(&b);
  }
  inline void Swap(EVElectricCtrl* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVElectricCtrl* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVElectricCtrl* New() const final {
    return new EVElectricCtrl();
  }

  EVElectricCtrl* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVElectricCtrl>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVElectricCtrl& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVElectricCtrl& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVElectricCtrl* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "EVSInfo.EVElectricCtrl";
  }
  protected:
  explicit EVElectricCtrl(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kContactK5FieldNumber = 1,
    kContactK6FieldNumber = 2,
    kCC1S2FieldNumber = 3,
    kCC2S3FieldNumber = 4,
    kElockStateFieldNumber = 5,
    kCanBusFieldNumber = 6,
    kEvPEFieldNumber = 7,
    kEvInsultOnFieldNumber = 8,
    kEvContactK6FieldNumber = 9,
    kEvPauseFieldNumber = 10,
    kSysFanFieldNumber = 11,
  };
  // uint32 contactK5 = 1;
  void clear_contactk5();
  ::PROTOBUF_NAMESPACE_ID::uint32 contactk5() const;
  void set_contactk5(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_contactk5() const;
  void _internal_set_contactk5(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 contactK6 = 2;
  void clear_contactk6();
  ::PROTOBUF_NAMESPACE_ID::uint32 contactk6() const;
  void set_contactk6(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_contactk6() const;
  void _internal_set_contactk6(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 CC1_S2 = 3;
  void clear_cc1_s2();
  ::PROTOBUF_NAMESPACE_ID::uint32 cc1_s2() const;
  void set_cc1_s2(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_cc1_s2() const;
  void _internal_set_cc1_s2(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 CC2_S3 = 4;
  void clear_cc2_s3();
  ::PROTOBUF_NAMESPACE_ID::uint32 cc2_s3() const;
  void set_cc2_s3(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_cc2_s3() const;
  void _internal_set_cc2_s3(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 elockState = 5;
  void clear_elockstate();
  ::PROTOBUF_NAMESPACE_ID::uint32 elockstate() const;
  void set_elockstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_elockstate() const;
  void _internal_set_elockstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 canBus = 6;
  void clear_canbus();
  ::PROTOBUF_NAMESPACE_ID::uint32 canbus() const;
  void set_canbus(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_canbus() const;
  void _internal_set_canbus(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 ev_PE = 7;
  void clear_ev_pe();
  ::PROTOBUF_NAMESPACE_ID::uint32 ev_pe() const;
  void set_ev_pe(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_ev_pe() const;
  void _internal_set_ev_pe(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 evInsultOn = 8;
  void clear_evinsulton();
  ::PROTOBUF_NAMESPACE_ID::uint32 evinsulton() const;
  void set_evinsulton(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_evinsulton() const;
  void _internal_set_evinsulton(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 evContactK6 = 9;
  void clear_evcontactk6();
  ::PROTOBUF_NAMESPACE_ID::uint32 evcontactk6() const;
  void set_evcontactk6(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_evcontactk6() const;
  void _internal_set_evcontactk6(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 evPause = 10;
  void clear_evpause();
  ::PROTOBUF_NAMESPACE_ID::uint32 evpause() const;
  void set_evpause(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_evpause() const;
  void _internal_set_evpause(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 sysFan = 11;
  void clear_sysfan();
  ::PROTOBUF_NAMESPACE_ID::uint32 sysfan() const;
  void set_sysfan(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_sysfan() const;
  void _internal_set_sysfan(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:EVSInfo.EVElectricCtrl)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 contactk5_;
  ::PROTOBUF_NAMESPACE_ID::uint32 contactk6_;
  ::PROTOBUF_NAMESPACE_ID::uint32 cc1_s2_;
  ::PROTOBUF_NAMESPACE_ID::uint32 cc2_s3_;
  ::PROTOBUF_NAMESPACE_ID::uint32 elockstate_;
  ::PROTOBUF_NAMESPACE_ID::uint32 canbus_;
  ::PROTOBUF_NAMESPACE_ID::uint32 ev_pe_;
  ::PROTOBUF_NAMESPACE_ID::uint32 evinsulton_;
  ::PROTOBUF_NAMESPACE_ID::uint32 evcontactk6_;
  ::PROTOBUF_NAMESPACE_ID::uint32 evpause_;
  ::PROTOBUF_NAMESPACE_ID::uint32 sysfan_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class EVInsultCtrl final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:EVSInfo.EVInsultCtrl) */ {
 public:
  inline EVInsultCtrl() : EVInsultCtrl(nullptr) {}
  ~EVInsultCtrl() override;
  explicit constexpr EVInsultCtrl(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVInsultCtrl(const EVInsultCtrl& from);
  EVInsultCtrl(EVInsultCtrl&& from) noexcept
    : EVInsultCtrl() {
    *this = ::std::move(from);
  }

  inline EVInsultCtrl& operator=(const EVInsultCtrl& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVInsultCtrl& operator=(EVInsultCtrl&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVInsultCtrl& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVInsultCtrl* internal_default_instance() {
    return reinterpret_cast<const EVInsultCtrl*>(
               &_EVInsultCtrl_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(EVInsultCtrl& a, EVInsultCtrl& b) {
    a.Swap(&b);
  }
  inline void Swap(EVInsultCtrl* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVInsultCtrl* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVInsultCtrl* New() const final {
    return new EVInsultCtrl();
  }

  EVInsultCtrl* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVInsultCtrl>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVInsultCtrl& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVInsultCtrl& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVInsultCtrl* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "EVSInfo.EVInsultCtrl";
  }
  protected:
  explicit EVInsultCtrl(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInsultPosResFieldNumber = 1,
    kInsultNegResFieldNumber = 2,
    kBatVolFieldNumber = 3,
    kConnectTypeFieldNumber = 4,
  };
  // uint32 insultPosRes = 1;
  void clear_insultposres();
  ::PROTOBUF_NAMESPACE_ID::uint32 insultposres() const;
  void set_insultposres(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_insultposres() const;
  void _internal_set_insultposres(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 insultNegRes = 2;
  void clear_insultnegres();
  ::PROTOBUF_NAMESPACE_ID::uint32 insultnegres() const;
  void set_insultnegres(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_insultnegres() const;
  void _internal_set_insultnegres(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // float batVol = 3;
  void clear_batvol();
  float batvol() const;
  void set_batvol(float value);
  private:
  float _internal_batvol() const;
  void _internal_set_batvol(float value);
  public:

  // uint32 connectType = 4;
  void clear_connecttype();
  ::PROTOBUF_NAMESPACE_ID::uint32 connecttype() const;
  void set_connecttype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_connecttype() const;
  void _internal_set_connecttype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:EVSInfo.EVInsultCtrl)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 insultposres_;
  ::PROTOBUF_NAMESPACE_ID::uint32 insultnegres_;
  float batvol_;
  ::PROTOBUF_NAMESPACE_ID::uint32 connecttype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class EVStateMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:EVSInfo.EVStateMsg) */ {
 public:
  inline EVStateMsg() : EVStateMsg(nullptr) {}
  ~EVStateMsg() override;
  explicit constexpr EVStateMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVStateMsg(const EVStateMsg& from);
  EVStateMsg(EVStateMsg&& from) noexcept
    : EVStateMsg() {
    *this = ::std::move(from);
  }

  inline EVStateMsg& operator=(const EVStateMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVStateMsg& operator=(EVStateMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVStateMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVStateMsg* internal_default_instance() {
    return reinterpret_cast<const EVStateMsg*>(
               &_EVStateMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(EVStateMsg& a, EVStateMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(EVStateMsg* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVStateMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVStateMsg* New() const final {
    return new EVStateMsg();
  }

  EVStateMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVStateMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVStateMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVStateMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVStateMsg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "EVSInfo.EVStateMsg";
  }
  protected:
  explicit EVStateMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBatVolFieldNumber = 1,
    kBatCurFieldNumber = 2,
    kNowSOCFieldNumber = 3,
    kNowFCFieldNumber = 4,
    kNowFDCFieldNumber = 5,
    kChgModeFieldNumber = 6,
    kInsultPosRFieldNumber = 7,
    kInsultNegRFieldNumber = 8,
    kCc1VoltFieldNumber = 9,
    kWorkModeFieldNumber = 10,
    kStopCodeFieldNumber = 11,
    kCc2VoltFieldNumber = 12,
  };
  // float batVol = 1;
  void clear_batvol();
  float batvol() const;
  void set_batvol(float value);
  private:
  float _internal_batvol() const;
  void _internal_set_batvol(float value);
  public:

  // float batCur = 2;
  void clear_batcur();
  float batcur() const;
  void set_batcur(float value);
  private:
  float _internal_batcur() const;
  void _internal_set_batcur(float value);
  public:

  // float nowSOC = 3;
  void clear_nowsoc();
  float nowsoc() const;
  void set_nowsoc(float value);
  private:
  float _internal_nowsoc() const;
  void _internal_set_nowsoc(float value);
  public:

  // uint32 nowFC = 4;
  void clear_nowfc();
  ::PROTOBUF_NAMESPACE_ID::uint32 nowfc() const;
  void set_nowfc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_nowfc() const;
  void _internal_set_nowfc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 nowFDC = 5;
  void clear_nowfdc();
  ::PROTOBUF_NAMESPACE_ID::uint32 nowfdc() const;
  void set_nowfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_nowfdc() const;
  void _internal_set_nowfdc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 chgMode = 6;
  void clear_chgmode();
  ::PROTOBUF_NAMESPACE_ID::uint32 chgmode() const;
  void set_chgmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chgmode() const;
  void _internal_set_chgmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // float insultPosR = 7;
  void clear_insultposr();
  float insultposr() const;
  void set_insultposr(float value);
  private:
  float _internal_insultposr() const;
  void _internal_set_insultposr(float value);
  public:

  // float insultNegR = 8;
  void clear_insultnegr();
  float insultnegr() const;
  void set_insultnegr(float value);
  private:
  float _internal_insultnegr() const;
  void _internal_set_insultnegr(float value);
  public:

  // float cc1Volt = 9;
  void clear_cc1volt();
  float cc1volt() const;
  void set_cc1volt(float value);
  private:
  float _internal_cc1volt() const;
  void _internal_set_cc1volt(float value);
  public:

  // uint32 workMode = 10;
  void clear_workmode();
  ::PROTOBUF_NAMESPACE_ID::uint32 workmode() const;
  void set_workmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_workmode() const;
  void _internal_set_workmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 stopCode = 11;
  void clear_stopcode();
  ::PROTOBUF_NAMESPACE_ID::uint32 stopcode() const;
  void set_stopcode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_stopcode() const;
  void _internal_set_stopcode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // float cc2Volt = 12;
  void clear_cc2volt();
  float cc2volt() const;
  void set_cc2volt(float value);
  private:
  float _internal_cc2volt() const;
  void _internal_set_cc2volt(float value);
  public:

  // @@protoc_insertion_point(class_scope:EVSInfo.EVStateMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float batvol_;
  float batcur_;
  float nowsoc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 nowfc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 nowfdc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chgmode_;
  float insultposr_;
  float insultnegr_;
  float cc1volt_;
  ::PROTOBUF_NAMESPACE_ID::uint32 workmode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 stopcode_;
  float cc2volt_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class StepPara final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:EVSInfo.StepPara) */ {
 public:
  inline StepPara() : StepPara(nullptr) {}
  ~StepPara() override;
  explicit constexpr StepPara(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StepPara(const StepPara& from);
  StepPara(StepPara&& from) noexcept
    : StepPara() {
    *this = ::std::move(from);
  }

  inline StepPara& operator=(const StepPara& from) {
    CopyFrom(from);
    return *this;
  }
  inline StepPara& operator=(StepPara&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StepPara& default_instance() {
    return *internal_default_instance();
  }
  static inline const StepPara* internal_default_instance() {
    return reinterpret_cast<const StepPara*>(
               &_StepPara_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(StepPara& a, StepPara& b) {
    a.Swap(&b);
  }
  inline void Swap(StepPara* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StepPara* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline StepPara* New() const final {
    return new StepPara();
  }

  StepPara* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StepPara>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StepPara& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const StepPara& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StepPara* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "EVSInfo.StepPara";
  }
  protected:
  explicit StepPara(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStartTimeFieldNumber = 1,
    kIntervalTimeFieldNumber = 2,
    kStartValueFieldNumber = 3,
    kMinValueFieldNumber = 4,
    kMaxValueFieldNumber = 5,
    kStepValueFieldNumber = 6,
    kCycleModeFieldNumber = 7,
    kStepModeFieldNumber = 8,
  };
  // uint64 startTime = 1;
  void clear_starttime();
  ::PROTOBUF_NAMESPACE_ID::uint64 starttime() const;
  void set_starttime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_starttime() const;
  void _internal_set_starttime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // uint64 intervalTime = 2;
  void clear_intervaltime();
  ::PROTOBUF_NAMESPACE_ID::uint64 intervaltime() const;
  void set_intervaltime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_intervaltime() const;
  void _internal_set_intervaltime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // float startValue = 3;
  void clear_startvalue();
  float startvalue() const;
  void set_startvalue(float value);
  private:
  float _internal_startvalue() const;
  void _internal_set_startvalue(float value);
  public:

  // float minValue = 4;
  void clear_minvalue();
  float minvalue() const;
  void set_minvalue(float value);
  private:
  float _internal_minvalue() const;
  void _internal_set_minvalue(float value);
  public:

  // float maxValue = 5;
  void clear_maxvalue();
  float maxvalue() const;
  void set_maxvalue(float value);
  private:
  float _internal_maxvalue() const;
  void _internal_set_maxvalue(float value);
  public:

  // float stepValue = 6;
  void clear_stepvalue();
  float stepvalue() const;
  void set_stepvalue(float value);
  private:
  float _internal_stepvalue() const;
  void _internal_set_stepvalue(float value);
  public:

  // uint32 cycleMode = 7;
  void clear_cyclemode();
  ::PROTOBUF_NAMESPACE_ID::uint32 cyclemode() const;
  void set_cyclemode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_cyclemode() const;
  void _internal_set_cyclemode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 stepMode = 8;
  void clear_stepmode();
  ::PROTOBUF_NAMESPACE_ID::uint32 stepmode() const;
  void set_stepmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_stepmode() const;
  void _internal_set_stepmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:EVSInfo.StepPara)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint64 starttime_;
  ::PROTOBUF_NAMESPACE_ID::uint64 intervaltime_;
  float startvalue_;
  float minvalue_;
  float maxvalue_;
  float stepvalue_;
  ::PROTOBUF_NAMESPACE_ID::uint32 cyclemode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 stepmode_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class EVSStepMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:EVSInfo.EVSStepMsg) */ {
 public:
  inline EVSStepMsg() : EVSStepMsg(nullptr) {}
  ~EVSStepMsg() override;
  explicit constexpr EVSStepMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVSStepMsg(const EVSStepMsg& from);
  EVSStepMsg(EVSStepMsg&& from) noexcept
    : EVSStepMsg() {
    *this = ::std::move(from);
  }

  inline EVSStepMsg& operator=(const EVSStepMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVSStepMsg& operator=(EVSStepMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVSStepMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVSStepMsg* internal_default_instance() {
    return reinterpret_cast<const EVSStepMsg*>(
               &_EVSStepMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(EVSStepMsg& a, EVSStepMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(EVSStepMsg* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVSStepMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVSStepMsg* New() const final {
    return new EVSStepMsg();
  }

  EVSStepMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVSStepMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVSStepMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVSStepMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVSStepMsg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "EVSInfo.EVSStepMsg";
  }
  protected:
  explicit EVSStepMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNeedVolStepFieldNumber = 1,
    kNeedCurrStepFieldNumber = 2,
    kSocStepFieldNumber = 3,
    kCellVolStepFieldNumber = 4,
    kCellTempStepFieldNumber = 5,
  };
  // .EVSInfo.StepPara needVolStep = 1;
  bool has_needvolstep() const;
  private:
  bool _internal_has_needvolstep() const;
  public:
  void clear_needvolstep();
  const ::EVSInfo::StepPara& needvolstep() const;
  PROTOBUF_MUST_USE_RESULT ::EVSInfo::StepPara* release_needvolstep();
  ::EVSInfo::StepPara* mutable_needvolstep();
  void set_allocated_needvolstep(::EVSInfo::StepPara* needvolstep);
  private:
  const ::EVSInfo::StepPara& _internal_needvolstep() const;
  ::EVSInfo::StepPara* _internal_mutable_needvolstep();
  public:
  void unsafe_arena_set_allocated_needvolstep(
      ::EVSInfo::StepPara* needvolstep);
  ::EVSInfo::StepPara* unsafe_arena_release_needvolstep();

  // .EVSInfo.StepPara needCurrStep = 2;
  bool has_needcurrstep() const;
  private:
  bool _internal_has_needcurrstep() const;
  public:
  void clear_needcurrstep();
  const ::EVSInfo::StepPara& needcurrstep() const;
  PROTOBUF_MUST_USE_RESULT ::EVSInfo::StepPara* release_needcurrstep();
  ::EVSInfo::StepPara* mutable_needcurrstep();
  void set_allocated_needcurrstep(::EVSInfo::StepPara* needcurrstep);
  private:
  const ::EVSInfo::StepPara& _internal_needcurrstep() const;
  ::EVSInfo::StepPara* _internal_mutable_needcurrstep();
  public:
  void unsafe_arena_set_allocated_needcurrstep(
      ::EVSInfo::StepPara* needcurrstep);
  ::EVSInfo::StepPara* unsafe_arena_release_needcurrstep();

  // .EVSInfo.StepPara socStep = 3;
  bool has_socstep() const;
  private:
  bool _internal_has_socstep() const;
  public:
  void clear_socstep();
  const ::EVSInfo::StepPara& socstep() const;
  PROTOBUF_MUST_USE_RESULT ::EVSInfo::StepPara* release_socstep();
  ::EVSInfo::StepPara* mutable_socstep();
  void set_allocated_socstep(::EVSInfo::StepPara* socstep);
  private:
  const ::EVSInfo::StepPara& _internal_socstep() const;
  ::EVSInfo::StepPara* _internal_mutable_socstep();
  public:
  void unsafe_arena_set_allocated_socstep(
      ::EVSInfo::StepPara* socstep);
  ::EVSInfo::StepPara* unsafe_arena_release_socstep();

  // .EVSInfo.StepPara cellVolStep = 4;
  bool has_cellvolstep() const;
  private:
  bool _internal_has_cellvolstep() const;
  public:
  void clear_cellvolstep();
  const ::EVSInfo::StepPara& cellvolstep() const;
  PROTOBUF_MUST_USE_RESULT ::EVSInfo::StepPara* release_cellvolstep();
  ::EVSInfo::StepPara* mutable_cellvolstep();
  void set_allocated_cellvolstep(::EVSInfo::StepPara* cellvolstep);
  private:
  const ::EVSInfo::StepPara& _internal_cellvolstep() const;
  ::EVSInfo::StepPara* _internal_mutable_cellvolstep();
  public:
  void unsafe_arena_set_allocated_cellvolstep(
      ::EVSInfo::StepPara* cellvolstep);
  ::EVSInfo::StepPara* unsafe_arena_release_cellvolstep();

  // .EVSInfo.StepPara cellTempStep = 5;
  bool has_celltempstep() const;
  private:
  bool _internal_has_celltempstep() const;
  public:
  void clear_celltempstep();
  const ::EVSInfo::StepPara& celltempstep() const;
  PROTOBUF_MUST_USE_RESULT ::EVSInfo::StepPara* release_celltempstep();
  ::EVSInfo::StepPara* mutable_celltempstep();
  void set_allocated_celltempstep(::EVSInfo::StepPara* celltempstep);
  private:
  const ::EVSInfo::StepPara& _internal_celltempstep() const;
  ::EVSInfo::StepPara* _internal_mutable_celltempstep();
  public:
  void unsafe_arena_set_allocated_celltempstep(
      ::EVSInfo::StepPara* celltempstep);
  ::EVSInfo::StepPara* unsafe_arena_release_celltempstep();

  // @@protoc_insertion_point(class_scope:EVSInfo.EVSStepMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::EVSInfo::StepPara* needvolstep_;
  ::EVSInfo::StepPara* needcurrstep_;
  ::EVSInfo::StepPara* socstep_;
  ::EVSInfo::StepPara* cellvolstep_;
  ::EVSInfo::StepPara* celltempstep_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class EVMsgCtrl final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:EVSInfo.EVMsgCtrl) */ {
 public:
  inline EVMsgCtrl() : EVMsgCtrl(nullptr) {}
  ~EVMsgCtrl() override;
  explicit constexpr EVMsgCtrl(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVMsgCtrl(const EVMsgCtrl& from);
  EVMsgCtrl(EVMsgCtrl&& from) noexcept
    : EVMsgCtrl() {
    *this = ::std::move(from);
  }

  inline EVMsgCtrl& operator=(const EVMsgCtrl& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVMsgCtrl& operator=(EVMsgCtrl&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVMsgCtrl& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVMsgCtrl* internal_default_instance() {
    return reinterpret_cast<const EVMsgCtrl*>(
               &_EVMsgCtrl_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(EVMsgCtrl& a, EVMsgCtrl& b) {
    a.Swap(&b);
  }
  inline void Swap(EVMsgCtrl* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVMsgCtrl* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVMsgCtrl* New() const final {
    return new EVMsgCtrl();
  }

  EVMsgCtrl* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVMsgCtrl>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVMsgCtrl& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVMsgCtrl& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVMsgCtrl* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "EVSInfo.EVMsgCtrl";
  }
  protected:
  explicit EVMsgCtrl(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kX20X02StateFieldNumber = 1,
    kX40X04StateFieldNumber = 2,
    kX60X06StateFieldNumber = 3,
    kX90X09StateFieldNumber = 4,
    kB20X12StateFieldNumber = 5,
    kC20X22StateFieldNumber = 6,
    kC40X24StateFieldNumber = 7,
    kD20X32StateFieldNumber = 8,
    kD40X34StateFieldNumber = 9,
    kD60X36StateFieldNumber = 10,
    kD70X37StateFieldNumber = 11,
    kD90X39StateFieldNumber = 12,
    kD100X3AStateFieldNumber = 13,
    kE20X42StateFieldNumber = 14,
    kE40X44StateFieldNumber = 15,
    kF20X52StateFieldNumber = 16,
    kG20X62StateFieldNumber = 17,
    kG30X63StateFieldNumber = 18,
    kG50X65StateFieldNumber = 19,
    kH20X72StateFieldNumber = 20,
    kH30X73StateFieldNumber = 21,
    kH40X74StateFieldNumber = 22,
    kH70X77StateFieldNumber = 23,
    kH90X79StateFieldNumber = 24,
    kH110X82StateFieldNumber = 25,
    kH130X84StateFieldNumber = 26,
    kH140X85StateFieldNumber = 27,
    kH160X87StateFieldNumber = 28,
    kH180X89StateFieldNumber = 29,
    kH200X8BStateFieldNumber = 30,
    kI10X91StateFieldNumber = 31,
    kI40X94StateFieldNumber = 32,
  };
  // uint32 x2_0x02_State = 1;
  void clear_x2_0x02_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 x2_0x02_state() const;
  void set_x2_0x02_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_x2_0x02_state() const;
  void _internal_set_x2_0x02_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 x4_0x04_State = 2;
  void clear_x4_0x04_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 x4_0x04_state() const;
  void set_x4_0x04_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_x4_0x04_state() const;
  void _internal_set_x4_0x04_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 x6_0x06_State = 3;
  void clear_x6_0x06_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 x6_0x06_state() const;
  void set_x6_0x06_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_x6_0x06_state() const;
  void _internal_set_x6_0x06_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 x9_0x09_State = 4;
  void clear_x9_0x09_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 x9_0x09_state() const;
  void set_x9_0x09_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_x9_0x09_state() const;
  void _internal_set_x9_0x09_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 b2_0x12_State = 5;
  void clear_b2_0x12_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 b2_0x12_state() const;
  void set_b2_0x12_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_b2_0x12_state() const;
  void _internal_set_b2_0x12_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 c2_0x22_State = 6;
  void clear_c2_0x22_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 c2_0x22_state() const;
  void set_c2_0x22_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_c2_0x22_state() const;
  void _internal_set_c2_0x22_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 c4_0x24_State = 7;
  void clear_c4_0x24_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 c4_0x24_state() const;
  void set_c4_0x24_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_c4_0x24_state() const;
  void _internal_set_c4_0x24_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 d2_0x32_State = 8;
  void clear_d2_0x32_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 d2_0x32_state() const;
  void set_d2_0x32_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_d2_0x32_state() const;
  void _internal_set_d2_0x32_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 d4_0x34_State = 9;
  void clear_d4_0x34_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 d4_0x34_state() const;
  void set_d4_0x34_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_d4_0x34_state() const;
  void _internal_set_d4_0x34_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 d6_0x36_State = 10;
  void clear_d6_0x36_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 d6_0x36_state() const;
  void set_d6_0x36_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_d6_0x36_state() const;
  void _internal_set_d6_0x36_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 d7_0x37_State = 11;
  void clear_d7_0x37_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 d7_0x37_state() const;
  void set_d7_0x37_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_d7_0x37_state() const;
  void _internal_set_d7_0x37_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 d9_0x39_State = 12;
  void clear_d9_0x39_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 d9_0x39_state() const;
  void set_d9_0x39_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_d9_0x39_state() const;
  void _internal_set_d9_0x39_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 d10_0x3A_State = 13;
  void clear_d10_0x3a_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 d10_0x3a_state() const;
  void set_d10_0x3a_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_d10_0x3a_state() const;
  void _internal_set_d10_0x3a_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 e2_0x42_State = 14;
  void clear_e2_0x42_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 e2_0x42_state() const;
  void set_e2_0x42_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_e2_0x42_state() const;
  void _internal_set_e2_0x42_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 e4_0x44_State = 15;
  void clear_e4_0x44_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 e4_0x44_state() const;
  void set_e4_0x44_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_e4_0x44_state() const;
  void _internal_set_e4_0x44_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 f2_0x52_State = 16;
  void clear_f2_0x52_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 f2_0x52_state() const;
  void set_f2_0x52_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_f2_0x52_state() const;
  void _internal_set_f2_0x52_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 g2_0x62_State = 17;
  void clear_g2_0x62_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 g2_0x62_state() const;
  void set_g2_0x62_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_g2_0x62_state() const;
  void _internal_set_g2_0x62_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 g3_0x63_State = 18;
  void clear_g3_0x63_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 g3_0x63_state() const;
  void set_g3_0x63_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_g3_0x63_state() const;
  void _internal_set_g3_0x63_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 g5_0x65_State = 19;
  void clear_g5_0x65_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 g5_0x65_state() const;
  void set_g5_0x65_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_g5_0x65_state() const;
  void _internal_set_g5_0x65_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 h2_0x72_State = 20;
  void clear_h2_0x72_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 h2_0x72_state() const;
  void set_h2_0x72_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_h2_0x72_state() const;
  void _internal_set_h2_0x72_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 h3_0x73_State = 21;
  void clear_h3_0x73_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 h3_0x73_state() const;
  void set_h3_0x73_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_h3_0x73_state() const;
  void _internal_set_h3_0x73_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 h4_0x74_State = 22;
  void clear_h4_0x74_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 h4_0x74_state() const;
  void set_h4_0x74_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_h4_0x74_state() const;
  void _internal_set_h4_0x74_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 h7_0x77_State = 23;
  void clear_h7_0x77_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 h7_0x77_state() const;
  void set_h7_0x77_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_h7_0x77_state() const;
  void _internal_set_h7_0x77_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 h9_0x79_State = 24;
  void clear_h9_0x79_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 h9_0x79_state() const;
  void set_h9_0x79_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_h9_0x79_state() const;
  void _internal_set_h9_0x79_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 h11_0x82_State = 25;
  void clear_h11_0x82_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 h11_0x82_state() const;
  void set_h11_0x82_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_h11_0x82_state() const;
  void _internal_set_h11_0x82_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 h13_0x84_State = 26;
  void clear_h13_0x84_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 h13_0x84_state() const;
  void set_h13_0x84_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_h13_0x84_state() const;
  void _internal_set_h13_0x84_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 h14_0x85_State = 27;
  void clear_h14_0x85_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 h14_0x85_state() const;
  void set_h14_0x85_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_h14_0x85_state() const;
  void _internal_set_h14_0x85_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 h16_0x87_State = 28;
  void clear_h16_0x87_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 h16_0x87_state() const;
  void set_h16_0x87_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_h16_0x87_state() const;
  void _internal_set_h16_0x87_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 h18_0x89_State = 29;
  void clear_h18_0x89_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 h18_0x89_state() const;
  void set_h18_0x89_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_h18_0x89_state() const;
  void _internal_set_h18_0x89_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 h20_0x8B_State = 30;
  void clear_h20_0x8b_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 h20_0x8b_state() const;
  void set_h20_0x8b_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_h20_0x8b_state() const;
  void _internal_set_h20_0x8b_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 i1_0x91_State = 31;
  void clear_i1_0x91_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 i1_0x91_state() const;
  void set_i1_0x91_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_i1_0x91_state() const;
  void _internal_set_i1_0x91_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 i4_0x94_State = 32;
  void clear_i4_0x94_state();
  ::PROTOBUF_NAMESPACE_ID::uint32 i4_0x94_state() const;
  void set_i4_0x94_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_i4_0x94_state() const;
  void _internal_set_i4_0x94_state(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:EVSInfo.EVMsgCtrl)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 x2_0x02_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 x4_0x04_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 x6_0x06_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 x9_0x09_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 b2_0x12_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 c2_0x22_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 c4_0x24_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 d2_0x32_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 d4_0x34_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 d6_0x36_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 d7_0x37_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 d9_0x39_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 d10_0x3a_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 e2_0x42_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 e4_0x44_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 f2_0x52_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 g2_0x62_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 g3_0x63_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 g5_0x65_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 h2_0x72_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 h3_0x73_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 h4_0x74_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 h7_0x77_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 h9_0x79_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 h11_0x82_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 h13_0x84_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 h14_0x85_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 h16_0x87_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 h18_0x89_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 h20_0x8b_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 i1_0x91_state_;
  ::PROTOBUF_NAMESPACE_ID::uint32 i4_0x94_state_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class IpMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:EVSInfo.IpMsg) */ {
 public:
  inline IpMsg() : IpMsg(nullptr) {}
  ~IpMsg() override;
  explicit constexpr IpMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  IpMsg(const IpMsg& from);
  IpMsg(IpMsg&& from) noexcept
    : IpMsg() {
    *this = ::std::move(from);
  }

  inline IpMsg& operator=(const IpMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline IpMsg& operator=(IpMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const IpMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const IpMsg* internal_default_instance() {
    return reinterpret_cast<const IpMsg*>(
               &_IpMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(IpMsg& a, IpMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(IpMsg* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(IpMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline IpMsg* New() const final {
    return new IpMsg();
  }

  IpMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<IpMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const IpMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const IpMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(IpMsg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "EVSInfo.IpMsg";
  }
  protected:
  explicit IpMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSelfIPFieldNumber = 1,
    kServiceIPFieldNumber = 3,
    kSelfPortFieldNumber = 2,
    kServicePortFieldNumber = 4,
  };
  // bytes selfIP = 1;
  void clear_selfip();
  const std::string& selfip() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_selfip(ArgT0&& arg0, ArgT... args);
  std::string* mutable_selfip();
  PROTOBUF_MUST_USE_RESULT std::string* release_selfip();
  void set_allocated_selfip(std::string* selfip);
  private:
  const std::string& _internal_selfip() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_selfip(const std::string& value);
  std::string* _internal_mutable_selfip();
  public:

  // bytes serviceIP = 3;
  void clear_serviceip();
  const std::string& serviceip() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_serviceip(ArgT0&& arg0, ArgT... args);
  std::string* mutable_serviceip();
  PROTOBUF_MUST_USE_RESULT std::string* release_serviceip();
  void set_allocated_serviceip(std::string* serviceip);
  private:
  const std::string& _internal_serviceip() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_serviceip(const std::string& value);
  std::string* _internal_mutable_serviceip();
  public:

  // uint32 selfPort = 2;
  void clear_selfport();
  ::PROTOBUF_NAMESPACE_ID::uint32 selfport() const;
  void set_selfport(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_selfport() const;
  void _internal_set_selfport(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 servicePort = 4;
  void clear_serviceport();
  ::PROTOBUF_NAMESPACE_ID::uint32 serviceport() const;
  void set_serviceport(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_serviceport() const;
  void _internal_set_serviceport(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:EVSInfo.IpMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr selfip_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr serviceip_;
  ::PROTOBUF_NAMESPACE_ID::uint32 selfport_;
  ::PROTOBUF_NAMESPACE_ID::uint32 serviceport_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fINFO_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// EVProtoConferMsg

// uint32 canType = 1;
inline void EVProtoConferMsg::clear_cantype() {
  cantype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVProtoConferMsg::_internal_cantype() const {
  return cantype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVProtoConferMsg::cantype() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVProtoConferMsg.canType)
  return _internal_cantype();
}
inline void EVProtoConferMsg::_internal_set_cantype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  cantype_ = value;
}
inline void EVProtoConferMsg::set_cantype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_cantype(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVProtoConferMsg.canType)
}

// bytes gbVersion = 2;
inline void EVProtoConferMsg::clear_gbversion() {
  gbversion_.ClearToEmpty();
}
inline const std::string& EVProtoConferMsg::gbversion() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVProtoConferMsg.gbVersion)
  return _internal_gbversion();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EVProtoConferMsg::set_gbversion(ArgT0&& arg0, ArgT... args) {
 
 gbversion_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:EVSInfo.EVProtoConferMsg.gbVersion)
}
inline std::string* EVProtoConferMsg::mutable_gbversion() {
  std::string* _s = _internal_mutable_gbversion();
  // @@protoc_insertion_point(field_mutable:EVSInfo.EVProtoConferMsg.gbVersion)
  return _s;
}
inline const std::string& EVProtoConferMsg::_internal_gbversion() const {
  return gbversion_.Get();
}
inline void EVProtoConferMsg::_internal_set_gbversion(const std::string& value) {
  
  gbversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* EVProtoConferMsg::_internal_mutable_gbversion() {
  
  return gbversion_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* EVProtoConferMsg::release_gbversion() {
  // @@protoc_insertion_point(field_release:EVSInfo.EVProtoConferMsg.gbVersion)
  return gbversion_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void EVProtoConferMsg::set_allocated_gbversion(std::string* gbversion) {
  if (gbversion != nullptr) {
    
  } else {
    
  }
  gbversion_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), gbversion,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:EVSInfo.EVProtoConferMsg.gbVersion)
}

// uint32 guidanceVersion = 3;
inline void EVProtoConferMsg::clear_guidanceversion() {
  guidanceversion_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVProtoConferMsg::_internal_guidanceversion() const {
  return guidanceversion_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVProtoConferMsg::guidanceversion() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVProtoConferMsg.guidanceVersion)
  return _internal_guidanceversion();
}
inline void EVProtoConferMsg::_internal_set_guidanceversion(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  guidanceversion_ = value;
}
inline void EVProtoConferMsg::set_guidanceversion(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_guidanceversion(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVProtoConferMsg.guidanceVersion)
}

// uint32 transportVersion = 4;
inline void EVProtoConferMsg::clear_transportversion() {
  transportversion_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVProtoConferMsg::_internal_transportversion() const {
  return transportversion_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVProtoConferMsg::transportversion() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVProtoConferMsg.transportVersion)
  return _internal_transportversion();
}
inline void EVProtoConferMsg::_internal_set_transportversion(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  transportversion_ = value;
}
inline void EVProtoConferMsg::set_transportversion(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_transportversion(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVProtoConferMsg.transportVersion)
}

// uint32 conferRes = 5;
inline void EVProtoConferMsg::clear_conferres() {
  conferres_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVProtoConferMsg::_internal_conferres() const {
  return conferres_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVProtoConferMsg::conferres() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVProtoConferMsg.conferRes)
  return _internal_conferres();
}
inline void EVProtoConferMsg::_internal_set_conferres(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  conferres_ = value;
}
inline void EVProtoConferMsg::set_conferres(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_conferres(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVProtoConferMsg.conferRes)
}

// -------------------------------------------------------------------

// EVFunConferMsg

// uint32 configFDC = 1;
inline void EVFunConferMsg::clear_configfdc() {
  configfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferMsg::_internal_configfdc() const {
  return configfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferMsg::configfdc() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVFunConferMsg.configFDC)
  return _internal_configfdc();
}
inline void EVFunConferMsg::_internal_set_configfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  configfdc_ = value;
}
inline void EVFunConferMsg::set_configfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_configfdc(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVFunConferMsg.configFDC)
}

// uint32 authenFDC = 2;
inline void EVFunConferMsg::clear_authenfdc() {
  authenfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferMsg::_internal_authenfdc() const {
  return authenfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferMsg::authenfdc() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVFunConferMsg.authenFDC)
  return _internal_authenfdc();
}
inline void EVFunConferMsg::_internal_set_authenfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  authenfdc_ = value;
}
inline void EVFunConferMsg::set_authenfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_authenfdc(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVFunConferMsg.authenFDC)
}

// uint32 appointFDC = 3;
inline void EVFunConferMsg::clear_appointfdc() {
  appointfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferMsg::_internal_appointfdc() const {
  return appointfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferMsg::appointfdc() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVFunConferMsg.appointFDC)
  return _internal_appointfdc();
}
inline void EVFunConferMsg::_internal_set_appointfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  appointfdc_ = value;
}
inline void EVFunConferMsg::set_appointfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_appointfdc(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVFunConferMsg.appointFDC)
}

// uint32 selfCheckFDC = 4;
inline void EVFunConferMsg::clear_selfcheckfdc() {
  selfcheckfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferMsg::_internal_selfcheckfdc() const {
  return selfcheckfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferMsg::selfcheckfdc() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVFunConferMsg.selfCheckFDC)
  return _internal_selfcheckfdc();
}
inline void EVFunConferMsg::_internal_set_selfcheckfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  selfcheckfdc_ = value;
}
inline void EVFunConferMsg::set_selfcheckfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_selfcheckfdc(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVFunConferMsg.selfCheckFDC)
}

// uint32 powerSupplyFDC = 5;
inline void EVFunConferMsg::clear_powersupplyfdc() {
  powersupplyfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferMsg::_internal_powersupplyfdc() const {
  return powersupplyfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferMsg::powersupplyfdc() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVFunConferMsg.powerSupplyFDC)
  return _internal_powersupplyfdc();
}
inline void EVFunConferMsg::_internal_set_powersupplyfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  powersupplyfdc_ = value;
}
inline void EVFunConferMsg::set_powersupplyfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_powersupplyfdc(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVFunConferMsg.powerSupplyFDC)
}

// uint32 energyTransferFDC = 6;
inline void EVFunConferMsg::clear_energytransferfdc() {
  energytransferfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferMsg::_internal_energytransferfdc() const {
  return energytransferfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferMsg::energytransferfdc() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVFunConferMsg.energyTransferFDC)
  return _internal_energytransferfdc();
}
inline void EVFunConferMsg::_internal_set_energytransferfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  energytransferfdc_ = value;
}
inline void EVFunConferMsg::set_energytransferfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_energytransferfdc(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVFunConferMsg.energyTransferFDC)
}

// uint32 endFDC = 7;
inline void EVFunConferMsg::clear_endfdc() {
  endfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferMsg::_internal_endfdc() const {
  return endfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferMsg::endfdc() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVFunConferMsg.endFDC)
  return _internal_endfdc();
}
inline void EVFunConferMsg::_internal_set_endfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  endfdc_ = value;
}
inline void EVFunConferMsg::set_endfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_endfdc(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVFunConferMsg.endFDC)
}

// -------------------------------------------------------------------

// EVChgParaConfigMsg

// float currAllowMAX = 1;
inline void EVChgParaConfigMsg::clear_currallowmax() {
  currallowmax_ = 0;
}
inline float EVChgParaConfigMsg::_internal_currallowmax() const {
  return currallowmax_;
}
inline float EVChgParaConfigMsg::currallowmax() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVChgParaConfigMsg.currAllowMAX)
  return _internal_currallowmax();
}
inline void EVChgParaConfigMsg::_internal_set_currallowmax(float value) {
  
  currallowmax_ = value;
}
inline void EVChgParaConfigMsg::set_currallowmax(float value) {
  _internal_set_currallowmax(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVChgParaConfigMsg.currAllowMAX)
}

// float voltAllowMAX = 2;
inline void EVChgParaConfigMsg::clear_voltallowmax() {
  voltallowmax_ = 0;
}
inline float EVChgParaConfigMsg::_internal_voltallowmax() const {
  return voltallowmax_;
}
inline float EVChgParaConfigMsg::voltallowmax() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVChgParaConfigMsg.voltAllowMAX)
  return _internal_voltallowmax();
}
inline void EVChgParaConfigMsg::_internal_set_voltallowmax(float value) {
  
  voltallowmax_ = value;
}
inline void EVChgParaConfigMsg::set_voltallowmax(float value) {
  _internal_set_voltallowmax(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVChgParaConfigMsg.voltAllowMAX)
}

// float energyAllowMAX = 3;
inline void EVChgParaConfigMsg::clear_energyallowmax() {
  energyallowmax_ = 0;
}
inline float EVChgParaConfigMsg::_internal_energyallowmax() const {
  return energyallowmax_;
}
inline float EVChgParaConfigMsg::energyallowmax() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVChgParaConfigMsg.energyAllowMAX)
  return _internal_energyallowmax();
}
inline void EVChgParaConfigMsg::_internal_set_energyallowmax(float value) {
  
  energyallowmax_ = value;
}
inline void EVChgParaConfigMsg::set_energyallowmax(float value) {
  _internal_set_energyallowmax(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVChgParaConfigMsg.energyAllowMAX)
}

// float nowSOC = 4;
inline void EVChgParaConfigMsg::clear_nowsoc() {
  nowsoc_ = 0;
}
inline float EVChgParaConfigMsg::_internal_nowsoc() const {
  return nowsoc_;
}
inline float EVChgParaConfigMsg::nowsoc() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVChgParaConfigMsg.nowSOC)
  return _internal_nowsoc();
}
inline void EVChgParaConfigMsg::_internal_set_nowsoc(float value) {
  
  nowsoc_ = value;
}
inline void EVChgParaConfigMsg::set_nowsoc(float value) {
  _internal_set_nowsoc(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVChgParaConfigMsg.nowSOC)
}

// float cellVoltAllowMAX = 5;
inline void EVChgParaConfigMsg::clear_cellvoltallowmax() {
  cellvoltallowmax_ = 0;
}
inline float EVChgParaConfigMsg::_internal_cellvoltallowmax() const {
  return cellvoltallowmax_;
}
inline float EVChgParaConfigMsg::cellvoltallowmax() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVChgParaConfigMsg.cellVoltAllowMAX)
  return _internal_cellvoltallowmax();
}
inline void EVChgParaConfigMsg::_internal_set_cellvoltallowmax(float value) {
  
  cellvoltallowmax_ = value;
}
inline void EVChgParaConfigMsg::set_cellvoltallowmax(float value) {
  _internal_set_cellvoltallowmax(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVChgParaConfigMsg.cellVoltAllowMAX)
}

// float batTempAllowMAX = 6;
inline void EVChgParaConfigMsg::clear_battempallowmax() {
  battempallowmax_ = 0;
}
inline float EVChgParaConfigMsg::_internal_battempallowmax() const {
  return battempallowmax_;
}
inline float EVChgParaConfigMsg::battempallowmax() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVChgParaConfigMsg.batTempAllowMAX)
  return _internal_battempallowmax();
}
inline void EVChgParaConfigMsg::_internal_set_battempallowmax(float value) {
  
  battempallowmax_ = value;
}
inline void EVChgParaConfigMsg::set_battempallowmax(float value) {
  _internal_set_battempallowmax(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVChgParaConfigMsg.batTempAllowMAX)
}

// uint32 restarNum = 7;
inline void EVChgParaConfigMsg::clear_restarnum() {
  restarnum_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVChgParaConfigMsg::_internal_restarnum() const {
  return restarnum_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVChgParaConfigMsg::restarnum() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVChgParaConfigMsg.restarNum)
  return _internal_restarnum();
}
inline void EVChgParaConfigMsg::_internal_set_restarnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  restarnum_ = value;
}
inline void EVChgParaConfigMsg::set_restarnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_restarnum(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVChgParaConfigMsg.restarNum)
}

// -------------------------------------------------------------------

// EVAuthenMsg

// uint32 authenWaitTime = 1;
inline void EVAuthenMsg::clear_authenwaittime() {
  authenwaittime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVAuthenMsg::_internal_authenwaittime() const {
  return authenwaittime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVAuthenMsg::authenwaittime() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVAuthenMsg.authenWaitTime)
  return _internal_authenwaittime();
}
inline void EVAuthenMsg::_internal_set_authenwaittime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  authenwaittime_ = value;
}
inline void EVAuthenMsg::set_authenwaittime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_authenwaittime(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVAuthenMsg.authenWaitTime)
}

// bytes eVIN = 2;
inline void EVAuthenMsg::clear_evin() {
  evin_.ClearToEmpty();
}
inline const std::string& EVAuthenMsg::evin() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVAuthenMsg.eVIN)
  return _internal_evin();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EVAuthenMsg::set_evin(ArgT0&& arg0, ArgT... args) {
 
 evin_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:EVSInfo.EVAuthenMsg.eVIN)
}
inline std::string* EVAuthenMsg::mutable_evin() {
  std::string* _s = _internal_mutable_evin();
  // @@protoc_insertion_point(field_mutable:EVSInfo.EVAuthenMsg.eVIN)
  return _s;
}
inline const std::string& EVAuthenMsg::_internal_evin() const {
  return evin_.Get();
}
inline void EVAuthenMsg::_internal_set_evin(const std::string& value) {
  
  evin_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* EVAuthenMsg::_internal_mutable_evin() {
  
  return evin_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* EVAuthenMsg::release_evin() {
  // @@protoc_insertion_point(field_release:EVSInfo.EVAuthenMsg.eVIN)
  return evin_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void EVAuthenMsg::set_allocated_evin(std::string* evin) {
  if (evin != nullptr) {
    
  } else {
    
  }
  evin_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), evin,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:EVSInfo.EVAuthenMsg.eVIN)
}

// uint32 nextFDC = 3;
inline void EVAuthenMsg::clear_nextfdc() {
  nextfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVAuthenMsg::_internal_nextfdc() const {
  return nextfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVAuthenMsg::nextfdc() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVAuthenMsg.nextFDC)
  return _internal_nextfdc();
}
inline void EVAuthenMsg::_internal_set_nextfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  nextfdc_ = value;
}
inline void EVAuthenMsg::set_nextfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_nextfdc(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVAuthenMsg.nextFDC)
}

// -------------------------------------------------------------------

// EVReserveMsg

// uint32 bmsDesireStartTime = 1;
inline void EVReserveMsg::clear_bmsdesirestarttime() {
  bmsdesirestarttime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVReserveMsg::_internal_bmsdesirestarttime() const {
  return bmsdesirestarttime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVReserveMsg::bmsdesirestarttime() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVReserveMsg.bmsDesireStartTime)
  return _internal_bmsdesirestarttime();
}
inline void EVReserveMsg::_internal_set_bmsdesirestarttime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsdesirestarttime_ = value;
}
inline void EVReserveMsg::set_bmsdesirestarttime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsdesirestarttime(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVReserveMsg.bmsDesireStartTime)
}

// uint32 bmsDesireLeaveTime = 2;
inline void EVReserveMsg::clear_bmsdesireleavetime() {
  bmsdesireleavetime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVReserveMsg::_internal_bmsdesireleavetime() const {
  return bmsdesireleavetime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVReserveMsg::bmsdesireleavetime() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVReserveMsg.bmsDesireLeaveTime)
  return _internal_bmsdesireleavetime();
}
inline void EVReserveMsg::_internal_set_bmsdesireleavetime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsdesireleavetime_ = value;
}
inline void EVReserveMsg::set_bmsdesireleavetime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsdesireleavetime(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVReserveMsg.bmsDesireLeaveTime)
}

// uint32 reserveResult = 3;
inline void EVReserveMsg::clear_reserveresult() {
  reserveresult_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVReserveMsg::_internal_reserveresult() const {
  return reserveresult_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVReserveMsg::reserveresult() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVReserveMsg.reserveResult)
  return _internal_reserveresult();
}
inline void EVReserveMsg::_internal_set_reserveresult(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  reserveresult_ = value;
}
inline void EVReserveMsg::set_reserveresult(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_reserveresult(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVReserveMsg.reserveResult)
}

// uint32 immediateChargeSupport = 4;
inline void EVReserveMsg::clear_immediatechargesupport() {
  immediatechargesupport_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVReserveMsg::_internal_immediatechargesupport() const {
  return immediatechargesupport_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVReserveMsg::immediatechargesupport() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVReserveMsg.immediateChargeSupport)
  return _internal_immediatechargesupport();
}
inline void EVReserveMsg::_internal_set_immediatechargesupport(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  immediatechargesupport_ = value;
}
inline void EVReserveMsg::set_immediatechargesupport(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_immediatechargesupport(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVReserveMsg.immediateChargeSupport)
}

// -------------------------------------------------------------------

// EVPowerSupplyMsg

// uint32 supplyState = 1;
inline void EVPowerSupplyMsg::clear_supplystate() {
  supplystate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVPowerSupplyMsg::_internal_supplystate() const {
  return supplystate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVPowerSupplyMsg::supplystate() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVPowerSupplyMsg.supplyState)
  return _internal_supplystate();
}
inline void EVPowerSupplyMsg::_internal_set_supplystate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  supplystate_ = value;
}
inline void EVPowerSupplyMsg::set_supplystate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_supplystate(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVPowerSupplyMsg.supplyState)
}

// float supplyVolDesire = 2;
inline void EVPowerSupplyMsg::clear_supplyvoldesire() {
  supplyvoldesire_ = 0;
}
inline float EVPowerSupplyMsg::_internal_supplyvoldesire() const {
  return supplyvoldesire_;
}
inline float EVPowerSupplyMsg::supplyvoldesire() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVPowerSupplyMsg.supplyVolDesire)
  return _internal_supplyvoldesire();
}
inline void EVPowerSupplyMsg::_internal_set_supplyvoldesire(float value) {
  
  supplyvoldesire_ = value;
}
inline void EVPowerSupplyMsg::set_supplyvoldesire(float value) {
  _internal_set_supplyvoldesire(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVPowerSupplyMsg.supplyVolDesire)
}

// float supplyCurrDesire = 3;
inline void EVPowerSupplyMsg::clear_supplycurrdesire() {
  supplycurrdesire_ = 0;
}
inline float EVPowerSupplyMsg::_internal_supplycurrdesire() const {
  return supplycurrdesire_;
}
inline float EVPowerSupplyMsg::supplycurrdesire() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVPowerSupplyMsg.supplyCurrDesire)
  return _internal_supplycurrdesire();
}
inline void EVPowerSupplyMsg::_internal_set_supplycurrdesire(float value) {
  
  supplycurrdesire_ = value;
}
inline void EVPowerSupplyMsg::set_supplycurrdesire(float value) {
  _internal_set_supplycurrdesire(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVPowerSupplyMsg.supplyCurrDesire)
}

// uint32 supplyEnd = 4;
inline void EVPowerSupplyMsg::clear_supplyend() {
  supplyend_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVPowerSupplyMsg::_internal_supplyend() const {
  return supplyend_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVPowerSupplyMsg::supplyend() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVPowerSupplyMsg.supplyEnd)
  return _internal_supplyend();
}
inline void EVPowerSupplyMsg::_internal_set_supplyend(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  supplyend_ = value;
}
inline void EVPowerSupplyMsg::set_supplyend(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_supplyend(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVPowerSupplyMsg.supplyEnd)
}

// -------------------------------------------------------------------

// EVChargingMsg

// uint32 bmsReady = 1;
inline void EVChargingMsg::clear_bmsready() {
  bmsready_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVChargingMsg::_internal_bmsready() const {
  return bmsready_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVChargingMsg::bmsready() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVChargingMsg.bmsReady)
  return _internal_bmsready();
}
inline void EVChargingMsg::_internal_set_bmsready(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsready_ = value;
}
inline void EVChargingMsg::set_bmsready(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsready(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVChargingMsg.bmsReady)
}

// float volDemand = 2;
inline void EVChargingMsg::clear_voldemand() {
  voldemand_ = 0;
}
inline float EVChargingMsg::_internal_voldemand() const {
  return voldemand_;
}
inline float EVChargingMsg::voldemand() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVChargingMsg.volDemand)
  return _internal_voldemand();
}
inline void EVChargingMsg::_internal_set_voldemand(float value) {
  
  voldemand_ = value;
}
inline void EVChargingMsg::set_voldemand(float value) {
  _internal_set_voldemand(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVChargingMsg.volDemand)
}

// float curDemand = 3;
inline void EVChargingMsg::clear_curdemand() {
  curdemand_ = 0;
}
inline float EVChargingMsg::_internal_curdemand() const {
  return curdemand_;
}
inline float EVChargingMsg::curdemand() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVChargingMsg.curDemand)
  return _internal_curdemand();
}
inline void EVChargingMsg::_internal_set_curdemand(float value) {
  
  curdemand_ = value;
}
inline void EVChargingMsg::set_curdemand(float value) {
  _internal_set_curdemand(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVChargingMsg.curDemand)
}

// uint32 chargeMode = 4;
inline void EVChargingMsg::clear_chargemode() {
  chargemode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVChargingMsg::_internal_chargemode() const {
  return chargemode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVChargingMsg::chargemode() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVChargingMsg.chargeMode)
  return _internal_chargemode();
}
inline void EVChargingMsg::_internal_set_chargemode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chargemode_ = value;
}
inline void EVChargingMsg::set_chargemode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chargemode(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVChargingMsg.chargeMode)
}

// float socNow = 5;
inline void EVChargingMsg::clear_socnow() {
  socnow_ = 0;
}
inline float EVChargingMsg::_internal_socnow() const {
  return socnow_;
}
inline float EVChargingMsg::socnow() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVChargingMsg.socNow)
  return _internal_socnow();
}
inline void EVChargingMsg::_internal_set_socnow(float value) {
  
  socnow_ = value;
}
inline void EVChargingMsg::set_socnow(float value) {
  _internal_set_socnow(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVChargingMsg.socNow)
}

// uint32 resChgTime = 6;
inline void EVChargingMsg::clear_reschgtime() {
  reschgtime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVChargingMsg::_internal_reschgtime() const {
  return reschgtime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVChargingMsg::reschgtime() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVChargingMsg.resChgTime)
  return _internal_reschgtime();
}
inline void EVChargingMsg::_internal_set_reschgtime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  reschgtime_ = value;
}
inline void EVChargingMsg::set_reschgtime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_reschgtime(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVChargingMsg.resChgTime)
}

// float cellBatVolMax = 7;
inline void EVChargingMsg::clear_cellbatvolmax() {
  cellbatvolmax_ = 0;
}
inline float EVChargingMsg::_internal_cellbatvolmax() const {
  return cellbatvolmax_;
}
inline float EVChargingMsg::cellbatvolmax() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVChargingMsg.cellBatVolMax)
  return _internal_cellbatvolmax();
}
inline void EVChargingMsg::_internal_set_cellbatvolmax(float value) {
  
  cellbatvolmax_ = value;
}
inline void EVChargingMsg::set_cellbatvolmax(float value) {
  _internal_set_cellbatvolmax(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVChargingMsg.cellBatVolMax)
}

// float cellBatVolMin = 8;
inline void EVChargingMsg::clear_cellbatvolmin() {
  cellbatvolmin_ = 0;
}
inline float EVChargingMsg::_internal_cellbatvolmin() const {
  return cellbatvolmin_;
}
inline float EVChargingMsg::cellbatvolmin() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVChargingMsg.cellBatVolMin)
  return _internal_cellbatvolmin();
}
inline void EVChargingMsg::_internal_set_cellbatvolmin(float value) {
  
  cellbatvolmin_ = value;
}
inline void EVChargingMsg::set_cellbatvolmin(float value) {
  _internal_set_cellbatvolmin(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVChargingMsg.cellBatVolMin)
}

// float celltempMax = 9;
inline void EVChargingMsg::clear_celltempmax() {
  celltempmax_ = 0;
}
inline float EVChargingMsg::_internal_celltempmax() const {
  return celltempmax_;
}
inline float EVChargingMsg::celltempmax() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVChargingMsg.celltempMax)
  return _internal_celltempmax();
}
inline void EVChargingMsg::_internal_set_celltempmax(float value) {
  
  celltempmax_ = value;
}
inline void EVChargingMsg::set_celltempmax(float value) {
  _internal_set_celltempmax(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVChargingMsg.celltempMax)
}

// float celltempMin = 10;
inline void EVChargingMsg::clear_celltempmin() {
  celltempmin_ = 0;
}
inline float EVChargingMsg::_internal_celltempmin() const {
  return celltempmin_;
}
inline float EVChargingMsg::celltempmin() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVChargingMsg.celltempMin)
  return _internal_celltempmin();
}
inline void EVChargingMsg::_internal_set_celltempmin(float value) {
  
  celltempmin_ = value;
}
inline void EVChargingMsg::set_celltempmin(float value) {
  _internal_set_celltempmin(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVChargingMsg.celltempMin)
}

// -------------------------------------------------------------------

// EVChargingEndMsg

// uint32 endType = 1;
inline void EVChargingEndMsg::clear_endtype() {
  endtype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVChargingEndMsg::_internal_endtype() const {
  return endtype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVChargingEndMsg::endtype() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVChargingEndMsg.endType)
  return _internal_endtype();
}
inline void EVChargingEndMsg::_internal_set_endtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  endtype_ = value;
}
inline void EVChargingEndMsg::set_endtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_endtype(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVChargingEndMsg.endType)
}

// uint32 endCode = 2;
inline void EVChargingEndMsg::clear_endcode() {
  endcode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVChargingEndMsg::_internal_endcode() const {
  return endcode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVChargingEndMsg::endcode() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVChargingEndMsg.endCode)
  return _internal_endcode();
}
inline void EVChargingEndMsg::_internal_set_endcode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  endcode_ = value;
}
inline void EVChargingEndMsg::set_endcode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_endcode(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVChargingEndMsg.endCode)
}

// uint32 endReason = 3;
inline void EVChargingEndMsg::clear_endreason() {
  endreason_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVChargingEndMsg::_internal_endreason() const {
  return endreason_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVChargingEndMsg::endreason() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVChargingEndMsg.endReason)
  return _internal_endreason();
}
inline void EVChargingEndMsg::_internal_set_endreason(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  endreason_ = value;
}
inline void EVChargingEndMsg::set_endreason(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_endreason(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVChargingEndMsg.endReason)
}

// uint32 repeat = 4;
inline void EVChargingEndMsg::clear_repeat() {
  repeat_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVChargingEndMsg::_internal_repeat() const {
  return repeat_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVChargingEndMsg::repeat() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVChargingEndMsg.repeat)
  return _internal_repeat();
}
inline void EVChargingEndMsg::_internal_set_repeat(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  repeat_ = value;
}
inline void EVChargingEndMsg::set_repeat(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_repeat(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVChargingEndMsg.repeat)
}

// uint32 bmsStickCheckState = 5;
inline void EVChargingEndMsg::clear_bmsstickcheckstate() {
  bmsstickcheckstate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVChargingEndMsg::_internal_bmsstickcheckstate() const {
  return bmsstickcheckstate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVChargingEndMsg::bmsstickcheckstate() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVChargingEndMsg.bmsStickCheckState)
  return _internal_bmsstickcheckstate();
}
inline void EVChargingEndMsg::_internal_set_bmsstickcheckstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsstickcheckstate_ = value;
}
inline void EVChargingEndMsg::set_bmsstickcheckstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsstickcheckstate(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVChargingEndMsg.bmsStickCheckState)
}

// -------------------------------------------------------------------

// EVFunConferAckMsg

// uint32 funConferAck = 1;
inline void EVFunConferAckMsg::clear_funconferack() {
  funconferack_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferAckMsg::_internal_funconferack() const {
  return funconferack_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferAckMsg::funconferack() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVFunConferAckMsg.funConferAck)
  return _internal_funconferack();
}
inline void EVFunConferAckMsg::_internal_set_funconferack(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  funconferack_ = value;
}
inline void EVFunConferAckMsg::set_funconferack(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_funconferack(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVFunConferAckMsg.funConferAck)
}

// uint32 configAck = 2;
inline void EVFunConferAckMsg::clear_configack() {
  configack_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferAckMsg::_internal_configack() const {
  return configack_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferAckMsg::configack() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVFunConferAckMsg.configAck)
  return _internal_configack();
}
inline void EVFunConferAckMsg::_internal_set_configack(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  configack_ = value;
}
inline void EVFunConferAckMsg::set_configack(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_configack(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVFunConferAckMsg.configAck)
}

// uint32 authenAck = 3;
inline void EVFunConferAckMsg::clear_authenack() {
  authenack_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferAckMsg::_internal_authenack() const {
  return authenack_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferAckMsg::authenack() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVFunConferAckMsg.authenAck)
  return _internal_authenack();
}
inline void EVFunConferAckMsg::_internal_set_authenack(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  authenack_ = value;
}
inline void EVFunConferAckMsg::set_authenack(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_authenack(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVFunConferAckMsg.authenAck)
}

// uint32 appointAck = 4;
inline void EVFunConferAckMsg::clear_appointack() {
  appointack_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferAckMsg::_internal_appointack() const {
  return appointack_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferAckMsg::appointack() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVFunConferAckMsg.appointAck)
  return _internal_appointack();
}
inline void EVFunConferAckMsg::_internal_set_appointack(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  appointack_ = value;
}
inline void EVFunConferAckMsg::set_appointack(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_appointack(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVFunConferAckMsg.appointAck)
}

// uint32 selfCheckAck = 5;
inline void EVFunConferAckMsg::clear_selfcheckack() {
  selfcheckack_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferAckMsg::_internal_selfcheckack() const {
  return selfcheckack_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferAckMsg::selfcheckack() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVFunConferAckMsg.selfCheckAck)
  return _internal_selfcheckack();
}
inline void EVFunConferAckMsg::_internal_set_selfcheckack(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  selfcheckack_ = value;
}
inline void EVFunConferAckMsg::set_selfcheckack(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_selfcheckack(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVFunConferAckMsg.selfCheckAck)
}

// uint32 powerSupplyAck = 6;
inline void EVFunConferAckMsg::clear_powersupplyack() {
  powersupplyack_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferAckMsg::_internal_powersupplyack() const {
  return powersupplyack_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferAckMsg::powersupplyack() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVFunConferAckMsg.powerSupplyAck)
  return _internal_powersupplyack();
}
inline void EVFunConferAckMsg::_internal_set_powersupplyack(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  powersupplyack_ = value;
}
inline void EVFunConferAckMsg::set_powersupplyack(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_powersupplyack(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVFunConferAckMsg.powerSupplyAck)
}

// uint32 energyTransferAck = 7;
inline void EVFunConferAckMsg::clear_energytransferack() {
  energytransferack_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferAckMsg::_internal_energytransferack() const {
  return energytransferack_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferAckMsg::energytransferack() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVFunConferAckMsg.energyTransferAck)
  return _internal_energytransferack();
}
inline void EVFunConferAckMsg::_internal_set_energytransferack(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  energytransferack_ = value;
}
inline void EVFunConferAckMsg::set_energytransferack(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_energytransferack(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVFunConferAckMsg.energyTransferAck)
}

// uint32 endAck = 8;
inline void EVFunConferAckMsg::clear_endack() {
  endack_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferAckMsg::_internal_endack() const {
  return endack_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVFunConferAckMsg::endack() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVFunConferAckMsg.endAck)
  return _internal_endack();
}
inline void EVFunConferAckMsg::_internal_set_endack(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  endack_ = value;
}
inline void EVFunConferAckMsg::set_endack(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_endack(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVFunConferAckMsg.endAck)
}

// -------------------------------------------------------------------

// EVElectricCtrl

// uint32 contactK5 = 1;
inline void EVElectricCtrl::clear_contactk5() {
  contactk5_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVElectricCtrl::_internal_contactk5() const {
  return contactk5_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVElectricCtrl::contactk5() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVElectricCtrl.contactK5)
  return _internal_contactk5();
}
inline void EVElectricCtrl::_internal_set_contactk5(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  contactk5_ = value;
}
inline void EVElectricCtrl::set_contactk5(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_contactk5(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVElectricCtrl.contactK5)
}

// uint32 contactK6 = 2;
inline void EVElectricCtrl::clear_contactk6() {
  contactk6_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVElectricCtrl::_internal_contactk6() const {
  return contactk6_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVElectricCtrl::contactk6() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVElectricCtrl.contactK6)
  return _internal_contactk6();
}
inline void EVElectricCtrl::_internal_set_contactk6(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  contactk6_ = value;
}
inline void EVElectricCtrl::set_contactk6(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_contactk6(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVElectricCtrl.contactK6)
}

// uint32 CC1_S2 = 3;
inline void EVElectricCtrl::clear_cc1_s2() {
  cc1_s2_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVElectricCtrl::_internal_cc1_s2() const {
  return cc1_s2_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVElectricCtrl::cc1_s2() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVElectricCtrl.CC1_S2)
  return _internal_cc1_s2();
}
inline void EVElectricCtrl::_internal_set_cc1_s2(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  cc1_s2_ = value;
}
inline void EVElectricCtrl::set_cc1_s2(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_cc1_s2(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVElectricCtrl.CC1_S2)
}

// uint32 CC2_S3 = 4;
inline void EVElectricCtrl::clear_cc2_s3() {
  cc2_s3_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVElectricCtrl::_internal_cc2_s3() const {
  return cc2_s3_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVElectricCtrl::cc2_s3() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVElectricCtrl.CC2_S3)
  return _internal_cc2_s3();
}
inline void EVElectricCtrl::_internal_set_cc2_s3(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  cc2_s3_ = value;
}
inline void EVElectricCtrl::set_cc2_s3(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_cc2_s3(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVElectricCtrl.CC2_S3)
}

// uint32 elockState = 5;
inline void EVElectricCtrl::clear_elockstate() {
  elockstate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVElectricCtrl::_internal_elockstate() const {
  return elockstate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVElectricCtrl::elockstate() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVElectricCtrl.elockState)
  return _internal_elockstate();
}
inline void EVElectricCtrl::_internal_set_elockstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  elockstate_ = value;
}
inline void EVElectricCtrl::set_elockstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_elockstate(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVElectricCtrl.elockState)
}

// uint32 canBus = 6;
inline void EVElectricCtrl::clear_canbus() {
  canbus_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVElectricCtrl::_internal_canbus() const {
  return canbus_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVElectricCtrl::canbus() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVElectricCtrl.canBus)
  return _internal_canbus();
}
inline void EVElectricCtrl::_internal_set_canbus(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  canbus_ = value;
}
inline void EVElectricCtrl::set_canbus(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_canbus(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVElectricCtrl.canBus)
}

// uint32 ev_PE = 7;
inline void EVElectricCtrl::clear_ev_pe() {
  ev_pe_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVElectricCtrl::_internal_ev_pe() const {
  return ev_pe_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVElectricCtrl::ev_pe() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVElectricCtrl.ev_PE)
  return _internal_ev_pe();
}
inline void EVElectricCtrl::_internal_set_ev_pe(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  ev_pe_ = value;
}
inline void EVElectricCtrl::set_ev_pe(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_ev_pe(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVElectricCtrl.ev_PE)
}

// uint32 evInsultOn = 8;
inline void EVElectricCtrl::clear_evinsulton() {
  evinsulton_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVElectricCtrl::_internal_evinsulton() const {
  return evinsulton_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVElectricCtrl::evinsulton() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVElectricCtrl.evInsultOn)
  return _internal_evinsulton();
}
inline void EVElectricCtrl::_internal_set_evinsulton(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  evinsulton_ = value;
}
inline void EVElectricCtrl::set_evinsulton(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_evinsulton(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVElectricCtrl.evInsultOn)
}

// uint32 evContactK6 = 9;
inline void EVElectricCtrl::clear_evcontactk6() {
  evcontactk6_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVElectricCtrl::_internal_evcontactk6() const {
  return evcontactk6_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVElectricCtrl::evcontactk6() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVElectricCtrl.evContactK6)
  return _internal_evcontactk6();
}
inline void EVElectricCtrl::_internal_set_evcontactk6(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  evcontactk6_ = value;
}
inline void EVElectricCtrl::set_evcontactk6(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_evcontactk6(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVElectricCtrl.evContactK6)
}

// uint32 evPause = 10;
inline void EVElectricCtrl::clear_evpause() {
  evpause_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVElectricCtrl::_internal_evpause() const {
  return evpause_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVElectricCtrl::evpause() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVElectricCtrl.evPause)
  return _internal_evpause();
}
inline void EVElectricCtrl::_internal_set_evpause(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  evpause_ = value;
}
inline void EVElectricCtrl::set_evpause(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_evpause(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVElectricCtrl.evPause)
}

// uint32 sysFan = 11;
inline void EVElectricCtrl::clear_sysfan() {
  sysfan_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVElectricCtrl::_internal_sysfan() const {
  return sysfan_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVElectricCtrl::sysfan() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVElectricCtrl.sysFan)
  return _internal_sysfan();
}
inline void EVElectricCtrl::_internal_set_sysfan(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  sysfan_ = value;
}
inline void EVElectricCtrl::set_sysfan(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_sysfan(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVElectricCtrl.sysFan)
}

// -------------------------------------------------------------------

// EVInsultCtrl

// uint32 insultPosRes = 1;
inline void EVInsultCtrl::clear_insultposres() {
  insultposres_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVInsultCtrl::_internal_insultposres() const {
  return insultposres_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVInsultCtrl::insultposres() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVInsultCtrl.insultPosRes)
  return _internal_insultposres();
}
inline void EVInsultCtrl::_internal_set_insultposres(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  insultposres_ = value;
}
inline void EVInsultCtrl::set_insultposres(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_insultposres(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVInsultCtrl.insultPosRes)
}

// uint32 insultNegRes = 2;
inline void EVInsultCtrl::clear_insultnegres() {
  insultnegres_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVInsultCtrl::_internal_insultnegres() const {
  return insultnegres_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVInsultCtrl::insultnegres() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVInsultCtrl.insultNegRes)
  return _internal_insultnegres();
}
inline void EVInsultCtrl::_internal_set_insultnegres(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  insultnegres_ = value;
}
inline void EVInsultCtrl::set_insultnegres(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_insultnegres(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVInsultCtrl.insultNegRes)
}

// float batVol = 3;
inline void EVInsultCtrl::clear_batvol() {
  batvol_ = 0;
}
inline float EVInsultCtrl::_internal_batvol() const {
  return batvol_;
}
inline float EVInsultCtrl::batvol() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVInsultCtrl.batVol)
  return _internal_batvol();
}
inline void EVInsultCtrl::_internal_set_batvol(float value) {
  
  batvol_ = value;
}
inline void EVInsultCtrl::set_batvol(float value) {
  _internal_set_batvol(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVInsultCtrl.batVol)
}

// uint32 connectType = 4;
inline void EVInsultCtrl::clear_connecttype() {
  connecttype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVInsultCtrl::_internal_connecttype() const {
  return connecttype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVInsultCtrl::connecttype() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVInsultCtrl.connectType)
  return _internal_connecttype();
}
inline void EVInsultCtrl::_internal_set_connecttype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  connecttype_ = value;
}
inline void EVInsultCtrl::set_connecttype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_connecttype(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVInsultCtrl.connectType)
}

// -------------------------------------------------------------------

// EVStateMsg

// float batVol = 1;
inline void EVStateMsg::clear_batvol() {
  batvol_ = 0;
}
inline float EVStateMsg::_internal_batvol() const {
  return batvol_;
}
inline float EVStateMsg::batvol() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVStateMsg.batVol)
  return _internal_batvol();
}
inline void EVStateMsg::_internal_set_batvol(float value) {
  
  batvol_ = value;
}
inline void EVStateMsg::set_batvol(float value) {
  _internal_set_batvol(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVStateMsg.batVol)
}

// float batCur = 2;
inline void EVStateMsg::clear_batcur() {
  batcur_ = 0;
}
inline float EVStateMsg::_internal_batcur() const {
  return batcur_;
}
inline float EVStateMsg::batcur() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVStateMsg.batCur)
  return _internal_batcur();
}
inline void EVStateMsg::_internal_set_batcur(float value) {
  
  batcur_ = value;
}
inline void EVStateMsg::set_batcur(float value) {
  _internal_set_batcur(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVStateMsg.batCur)
}

// float nowSOC = 3;
inline void EVStateMsg::clear_nowsoc() {
  nowsoc_ = 0;
}
inline float EVStateMsg::_internal_nowsoc() const {
  return nowsoc_;
}
inline float EVStateMsg::nowsoc() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVStateMsg.nowSOC)
  return _internal_nowsoc();
}
inline void EVStateMsg::_internal_set_nowsoc(float value) {
  
  nowsoc_ = value;
}
inline void EVStateMsg::set_nowsoc(float value) {
  _internal_set_nowsoc(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVStateMsg.nowSOC)
}

// uint32 nowFC = 4;
inline void EVStateMsg::clear_nowfc() {
  nowfc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVStateMsg::_internal_nowfc() const {
  return nowfc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVStateMsg::nowfc() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVStateMsg.nowFC)
  return _internal_nowfc();
}
inline void EVStateMsg::_internal_set_nowfc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  nowfc_ = value;
}
inline void EVStateMsg::set_nowfc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_nowfc(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVStateMsg.nowFC)
}

// uint32 nowFDC = 5;
inline void EVStateMsg::clear_nowfdc() {
  nowfdc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVStateMsg::_internal_nowfdc() const {
  return nowfdc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVStateMsg::nowfdc() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVStateMsg.nowFDC)
  return _internal_nowfdc();
}
inline void EVStateMsg::_internal_set_nowfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  nowfdc_ = value;
}
inline void EVStateMsg::set_nowfdc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_nowfdc(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVStateMsg.nowFDC)
}

// uint32 chgMode = 6;
inline void EVStateMsg::clear_chgmode() {
  chgmode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVStateMsg::_internal_chgmode() const {
  return chgmode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVStateMsg::chgmode() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVStateMsg.chgMode)
  return _internal_chgmode();
}
inline void EVStateMsg::_internal_set_chgmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chgmode_ = value;
}
inline void EVStateMsg::set_chgmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chgmode(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVStateMsg.chgMode)
}

// float insultPosR = 7;
inline void EVStateMsg::clear_insultposr() {
  insultposr_ = 0;
}
inline float EVStateMsg::_internal_insultposr() const {
  return insultposr_;
}
inline float EVStateMsg::insultposr() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVStateMsg.insultPosR)
  return _internal_insultposr();
}
inline void EVStateMsg::_internal_set_insultposr(float value) {
  
  insultposr_ = value;
}
inline void EVStateMsg::set_insultposr(float value) {
  _internal_set_insultposr(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVStateMsg.insultPosR)
}

// float insultNegR = 8;
inline void EVStateMsg::clear_insultnegr() {
  insultnegr_ = 0;
}
inline float EVStateMsg::_internal_insultnegr() const {
  return insultnegr_;
}
inline float EVStateMsg::insultnegr() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVStateMsg.insultNegR)
  return _internal_insultnegr();
}
inline void EVStateMsg::_internal_set_insultnegr(float value) {
  
  insultnegr_ = value;
}
inline void EVStateMsg::set_insultnegr(float value) {
  _internal_set_insultnegr(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVStateMsg.insultNegR)
}

// float cc1Volt = 9;
inline void EVStateMsg::clear_cc1volt() {
  cc1volt_ = 0;
}
inline float EVStateMsg::_internal_cc1volt() const {
  return cc1volt_;
}
inline float EVStateMsg::cc1volt() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVStateMsg.cc1Volt)
  return _internal_cc1volt();
}
inline void EVStateMsg::_internal_set_cc1volt(float value) {
  
  cc1volt_ = value;
}
inline void EVStateMsg::set_cc1volt(float value) {
  _internal_set_cc1volt(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVStateMsg.cc1Volt)
}

// uint32 workMode = 10;
inline void EVStateMsg::clear_workmode() {
  workmode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVStateMsg::_internal_workmode() const {
  return workmode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVStateMsg::workmode() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVStateMsg.workMode)
  return _internal_workmode();
}
inline void EVStateMsg::_internal_set_workmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  workmode_ = value;
}
inline void EVStateMsg::set_workmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_workmode(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVStateMsg.workMode)
}

// uint32 stopCode = 11;
inline void EVStateMsg::clear_stopcode() {
  stopcode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVStateMsg::_internal_stopcode() const {
  return stopcode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVStateMsg::stopcode() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVStateMsg.stopCode)
  return _internal_stopcode();
}
inline void EVStateMsg::_internal_set_stopcode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  stopcode_ = value;
}
inline void EVStateMsg::set_stopcode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_stopcode(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVStateMsg.stopCode)
}

// float cc2Volt = 12;
inline void EVStateMsg::clear_cc2volt() {
  cc2volt_ = 0;
}
inline float EVStateMsg::_internal_cc2volt() const {
  return cc2volt_;
}
inline float EVStateMsg::cc2volt() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVStateMsg.cc2Volt)
  return _internal_cc2volt();
}
inline void EVStateMsg::_internal_set_cc2volt(float value) {
  
  cc2volt_ = value;
}
inline void EVStateMsg::set_cc2volt(float value) {
  _internal_set_cc2volt(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVStateMsg.cc2Volt)
}

// -------------------------------------------------------------------

// StepPara

// uint64 startTime = 1;
inline void StepPara::clear_starttime() {
  starttime_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 StepPara::_internal_starttime() const {
  return starttime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 StepPara::starttime() const {
  // @@protoc_insertion_point(field_get:EVSInfo.StepPara.startTime)
  return _internal_starttime();
}
inline void StepPara::_internal_set_starttime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  starttime_ = value;
}
inline void StepPara::set_starttime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_starttime(value);
  // @@protoc_insertion_point(field_set:EVSInfo.StepPara.startTime)
}

// uint64 intervalTime = 2;
inline void StepPara::clear_intervaltime() {
  intervaltime_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 StepPara::_internal_intervaltime() const {
  return intervaltime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 StepPara::intervaltime() const {
  // @@protoc_insertion_point(field_get:EVSInfo.StepPara.intervalTime)
  return _internal_intervaltime();
}
inline void StepPara::_internal_set_intervaltime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  intervaltime_ = value;
}
inline void StepPara::set_intervaltime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_intervaltime(value);
  // @@protoc_insertion_point(field_set:EVSInfo.StepPara.intervalTime)
}

// float startValue = 3;
inline void StepPara::clear_startvalue() {
  startvalue_ = 0;
}
inline float StepPara::_internal_startvalue() const {
  return startvalue_;
}
inline float StepPara::startvalue() const {
  // @@protoc_insertion_point(field_get:EVSInfo.StepPara.startValue)
  return _internal_startvalue();
}
inline void StepPara::_internal_set_startvalue(float value) {
  
  startvalue_ = value;
}
inline void StepPara::set_startvalue(float value) {
  _internal_set_startvalue(value);
  // @@protoc_insertion_point(field_set:EVSInfo.StepPara.startValue)
}

// float minValue = 4;
inline void StepPara::clear_minvalue() {
  minvalue_ = 0;
}
inline float StepPara::_internal_minvalue() const {
  return minvalue_;
}
inline float StepPara::minvalue() const {
  // @@protoc_insertion_point(field_get:EVSInfo.StepPara.minValue)
  return _internal_minvalue();
}
inline void StepPara::_internal_set_minvalue(float value) {
  
  minvalue_ = value;
}
inline void StepPara::set_minvalue(float value) {
  _internal_set_minvalue(value);
  // @@protoc_insertion_point(field_set:EVSInfo.StepPara.minValue)
}

// float maxValue = 5;
inline void StepPara::clear_maxvalue() {
  maxvalue_ = 0;
}
inline float StepPara::_internal_maxvalue() const {
  return maxvalue_;
}
inline float StepPara::maxvalue() const {
  // @@protoc_insertion_point(field_get:EVSInfo.StepPara.maxValue)
  return _internal_maxvalue();
}
inline void StepPara::_internal_set_maxvalue(float value) {
  
  maxvalue_ = value;
}
inline void StepPara::set_maxvalue(float value) {
  _internal_set_maxvalue(value);
  // @@protoc_insertion_point(field_set:EVSInfo.StepPara.maxValue)
}

// float stepValue = 6;
inline void StepPara::clear_stepvalue() {
  stepvalue_ = 0;
}
inline float StepPara::_internal_stepvalue() const {
  return stepvalue_;
}
inline float StepPara::stepvalue() const {
  // @@protoc_insertion_point(field_get:EVSInfo.StepPara.stepValue)
  return _internal_stepvalue();
}
inline void StepPara::_internal_set_stepvalue(float value) {
  
  stepvalue_ = value;
}
inline void StepPara::set_stepvalue(float value) {
  _internal_set_stepvalue(value);
  // @@protoc_insertion_point(field_set:EVSInfo.StepPara.stepValue)
}

// uint32 cycleMode = 7;
inline void StepPara::clear_cyclemode() {
  cyclemode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 StepPara::_internal_cyclemode() const {
  return cyclemode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 StepPara::cyclemode() const {
  // @@protoc_insertion_point(field_get:EVSInfo.StepPara.cycleMode)
  return _internal_cyclemode();
}
inline void StepPara::_internal_set_cyclemode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  cyclemode_ = value;
}
inline void StepPara::set_cyclemode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_cyclemode(value);
  // @@protoc_insertion_point(field_set:EVSInfo.StepPara.cycleMode)
}

// uint32 stepMode = 8;
inline void StepPara::clear_stepmode() {
  stepmode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 StepPara::_internal_stepmode() const {
  return stepmode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 StepPara::stepmode() const {
  // @@protoc_insertion_point(field_get:EVSInfo.StepPara.stepMode)
  return _internal_stepmode();
}
inline void StepPara::_internal_set_stepmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  stepmode_ = value;
}
inline void StepPara::set_stepmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_stepmode(value);
  // @@protoc_insertion_point(field_set:EVSInfo.StepPara.stepMode)
}

// -------------------------------------------------------------------

// EVSStepMsg

// .EVSInfo.StepPara needVolStep = 1;
inline bool EVSStepMsg::_internal_has_needvolstep() const {
  return this != internal_default_instance() && needvolstep_ != nullptr;
}
inline bool EVSStepMsg::has_needvolstep() const {
  return _internal_has_needvolstep();
}
inline void EVSStepMsg::clear_needvolstep() {
  if (GetArenaForAllocation() == nullptr && needvolstep_ != nullptr) {
    delete needvolstep_;
  }
  needvolstep_ = nullptr;
}
inline const ::EVSInfo::StepPara& EVSStepMsg::_internal_needvolstep() const {
  const ::EVSInfo::StepPara* p = needvolstep_;
  return p != nullptr ? *p : reinterpret_cast<const ::EVSInfo::StepPara&>(
      ::EVSInfo::_StepPara_default_instance_);
}
inline const ::EVSInfo::StepPara& EVSStepMsg::needvolstep() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVSStepMsg.needVolStep)
  return _internal_needvolstep();
}
inline void EVSStepMsg::unsafe_arena_set_allocated_needvolstep(
    ::EVSInfo::StepPara* needvolstep) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(needvolstep_);
  }
  needvolstep_ = needvolstep;
  if (needvolstep) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:EVSInfo.EVSStepMsg.needVolStep)
}
inline ::EVSInfo::StepPara* EVSStepMsg::release_needvolstep() {
  
  ::EVSInfo::StepPara* temp = needvolstep_;
  needvolstep_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::EVSInfo::StepPara* EVSStepMsg::unsafe_arena_release_needvolstep() {
  // @@protoc_insertion_point(field_release:EVSInfo.EVSStepMsg.needVolStep)
  
  ::EVSInfo::StepPara* temp = needvolstep_;
  needvolstep_ = nullptr;
  return temp;
}
inline ::EVSInfo::StepPara* EVSStepMsg::_internal_mutable_needvolstep() {
  
  if (needvolstep_ == nullptr) {
    auto* p = CreateMaybeMessage<::EVSInfo::StepPara>(GetArenaForAllocation());
    needvolstep_ = p;
  }
  return needvolstep_;
}
inline ::EVSInfo::StepPara* EVSStepMsg::mutable_needvolstep() {
  ::EVSInfo::StepPara* _msg = _internal_mutable_needvolstep();
  // @@protoc_insertion_point(field_mutable:EVSInfo.EVSStepMsg.needVolStep)
  return _msg;
}
inline void EVSStepMsg::set_allocated_needvolstep(::EVSInfo::StepPara* needvolstep) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete needvolstep_;
  }
  if (needvolstep) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::EVSInfo::StepPara>::GetOwningArena(needvolstep);
    if (message_arena != submessage_arena) {
      needvolstep = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, needvolstep, submessage_arena);
    }
    
  } else {
    
  }
  needvolstep_ = needvolstep;
  // @@protoc_insertion_point(field_set_allocated:EVSInfo.EVSStepMsg.needVolStep)
}

// .EVSInfo.StepPara needCurrStep = 2;
inline bool EVSStepMsg::_internal_has_needcurrstep() const {
  return this != internal_default_instance() && needcurrstep_ != nullptr;
}
inline bool EVSStepMsg::has_needcurrstep() const {
  return _internal_has_needcurrstep();
}
inline void EVSStepMsg::clear_needcurrstep() {
  if (GetArenaForAllocation() == nullptr && needcurrstep_ != nullptr) {
    delete needcurrstep_;
  }
  needcurrstep_ = nullptr;
}
inline const ::EVSInfo::StepPara& EVSStepMsg::_internal_needcurrstep() const {
  const ::EVSInfo::StepPara* p = needcurrstep_;
  return p != nullptr ? *p : reinterpret_cast<const ::EVSInfo::StepPara&>(
      ::EVSInfo::_StepPara_default_instance_);
}
inline const ::EVSInfo::StepPara& EVSStepMsg::needcurrstep() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVSStepMsg.needCurrStep)
  return _internal_needcurrstep();
}
inline void EVSStepMsg::unsafe_arena_set_allocated_needcurrstep(
    ::EVSInfo::StepPara* needcurrstep) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(needcurrstep_);
  }
  needcurrstep_ = needcurrstep;
  if (needcurrstep) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:EVSInfo.EVSStepMsg.needCurrStep)
}
inline ::EVSInfo::StepPara* EVSStepMsg::release_needcurrstep() {
  
  ::EVSInfo::StepPara* temp = needcurrstep_;
  needcurrstep_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::EVSInfo::StepPara* EVSStepMsg::unsafe_arena_release_needcurrstep() {
  // @@protoc_insertion_point(field_release:EVSInfo.EVSStepMsg.needCurrStep)
  
  ::EVSInfo::StepPara* temp = needcurrstep_;
  needcurrstep_ = nullptr;
  return temp;
}
inline ::EVSInfo::StepPara* EVSStepMsg::_internal_mutable_needcurrstep() {
  
  if (needcurrstep_ == nullptr) {
    auto* p = CreateMaybeMessage<::EVSInfo::StepPara>(GetArenaForAllocation());
    needcurrstep_ = p;
  }
  return needcurrstep_;
}
inline ::EVSInfo::StepPara* EVSStepMsg::mutable_needcurrstep() {
  ::EVSInfo::StepPara* _msg = _internal_mutable_needcurrstep();
  // @@protoc_insertion_point(field_mutable:EVSInfo.EVSStepMsg.needCurrStep)
  return _msg;
}
inline void EVSStepMsg::set_allocated_needcurrstep(::EVSInfo::StepPara* needcurrstep) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete needcurrstep_;
  }
  if (needcurrstep) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::EVSInfo::StepPara>::GetOwningArena(needcurrstep);
    if (message_arena != submessage_arena) {
      needcurrstep = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, needcurrstep, submessage_arena);
    }
    
  } else {
    
  }
  needcurrstep_ = needcurrstep;
  // @@protoc_insertion_point(field_set_allocated:EVSInfo.EVSStepMsg.needCurrStep)
}

// .EVSInfo.StepPara socStep = 3;
inline bool EVSStepMsg::_internal_has_socstep() const {
  return this != internal_default_instance() && socstep_ != nullptr;
}
inline bool EVSStepMsg::has_socstep() const {
  return _internal_has_socstep();
}
inline void EVSStepMsg::clear_socstep() {
  if (GetArenaForAllocation() == nullptr && socstep_ != nullptr) {
    delete socstep_;
  }
  socstep_ = nullptr;
}
inline const ::EVSInfo::StepPara& EVSStepMsg::_internal_socstep() const {
  const ::EVSInfo::StepPara* p = socstep_;
  return p != nullptr ? *p : reinterpret_cast<const ::EVSInfo::StepPara&>(
      ::EVSInfo::_StepPara_default_instance_);
}
inline const ::EVSInfo::StepPara& EVSStepMsg::socstep() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVSStepMsg.socStep)
  return _internal_socstep();
}
inline void EVSStepMsg::unsafe_arena_set_allocated_socstep(
    ::EVSInfo::StepPara* socstep) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(socstep_);
  }
  socstep_ = socstep;
  if (socstep) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:EVSInfo.EVSStepMsg.socStep)
}
inline ::EVSInfo::StepPara* EVSStepMsg::release_socstep() {
  
  ::EVSInfo::StepPara* temp = socstep_;
  socstep_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::EVSInfo::StepPara* EVSStepMsg::unsafe_arena_release_socstep() {
  // @@protoc_insertion_point(field_release:EVSInfo.EVSStepMsg.socStep)
  
  ::EVSInfo::StepPara* temp = socstep_;
  socstep_ = nullptr;
  return temp;
}
inline ::EVSInfo::StepPara* EVSStepMsg::_internal_mutable_socstep() {
  
  if (socstep_ == nullptr) {
    auto* p = CreateMaybeMessage<::EVSInfo::StepPara>(GetArenaForAllocation());
    socstep_ = p;
  }
  return socstep_;
}
inline ::EVSInfo::StepPara* EVSStepMsg::mutable_socstep() {
  ::EVSInfo::StepPara* _msg = _internal_mutable_socstep();
  // @@protoc_insertion_point(field_mutable:EVSInfo.EVSStepMsg.socStep)
  return _msg;
}
inline void EVSStepMsg::set_allocated_socstep(::EVSInfo::StepPara* socstep) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete socstep_;
  }
  if (socstep) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::EVSInfo::StepPara>::GetOwningArena(socstep);
    if (message_arena != submessage_arena) {
      socstep = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, socstep, submessage_arena);
    }
    
  } else {
    
  }
  socstep_ = socstep;
  // @@protoc_insertion_point(field_set_allocated:EVSInfo.EVSStepMsg.socStep)
}

// .EVSInfo.StepPara cellVolStep = 4;
inline bool EVSStepMsg::_internal_has_cellvolstep() const {
  return this != internal_default_instance() && cellvolstep_ != nullptr;
}
inline bool EVSStepMsg::has_cellvolstep() const {
  return _internal_has_cellvolstep();
}
inline void EVSStepMsg::clear_cellvolstep() {
  if (GetArenaForAllocation() == nullptr && cellvolstep_ != nullptr) {
    delete cellvolstep_;
  }
  cellvolstep_ = nullptr;
}
inline const ::EVSInfo::StepPara& EVSStepMsg::_internal_cellvolstep() const {
  const ::EVSInfo::StepPara* p = cellvolstep_;
  return p != nullptr ? *p : reinterpret_cast<const ::EVSInfo::StepPara&>(
      ::EVSInfo::_StepPara_default_instance_);
}
inline const ::EVSInfo::StepPara& EVSStepMsg::cellvolstep() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVSStepMsg.cellVolStep)
  return _internal_cellvolstep();
}
inline void EVSStepMsg::unsafe_arena_set_allocated_cellvolstep(
    ::EVSInfo::StepPara* cellvolstep) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(cellvolstep_);
  }
  cellvolstep_ = cellvolstep;
  if (cellvolstep) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:EVSInfo.EVSStepMsg.cellVolStep)
}
inline ::EVSInfo::StepPara* EVSStepMsg::release_cellvolstep() {
  
  ::EVSInfo::StepPara* temp = cellvolstep_;
  cellvolstep_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::EVSInfo::StepPara* EVSStepMsg::unsafe_arena_release_cellvolstep() {
  // @@protoc_insertion_point(field_release:EVSInfo.EVSStepMsg.cellVolStep)
  
  ::EVSInfo::StepPara* temp = cellvolstep_;
  cellvolstep_ = nullptr;
  return temp;
}
inline ::EVSInfo::StepPara* EVSStepMsg::_internal_mutable_cellvolstep() {
  
  if (cellvolstep_ == nullptr) {
    auto* p = CreateMaybeMessage<::EVSInfo::StepPara>(GetArenaForAllocation());
    cellvolstep_ = p;
  }
  return cellvolstep_;
}
inline ::EVSInfo::StepPara* EVSStepMsg::mutable_cellvolstep() {
  ::EVSInfo::StepPara* _msg = _internal_mutable_cellvolstep();
  // @@protoc_insertion_point(field_mutable:EVSInfo.EVSStepMsg.cellVolStep)
  return _msg;
}
inline void EVSStepMsg::set_allocated_cellvolstep(::EVSInfo::StepPara* cellvolstep) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete cellvolstep_;
  }
  if (cellvolstep) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::EVSInfo::StepPara>::GetOwningArena(cellvolstep);
    if (message_arena != submessage_arena) {
      cellvolstep = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, cellvolstep, submessage_arena);
    }
    
  } else {
    
  }
  cellvolstep_ = cellvolstep;
  // @@protoc_insertion_point(field_set_allocated:EVSInfo.EVSStepMsg.cellVolStep)
}

// .EVSInfo.StepPara cellTempStep = 5;
inline bool EVSStepMsg::_internal_has_celltempstep() const {
  return this != internal_default_instance() && celltempstep_ != nullptr;
}
inline bool EVSStepMsg::has_celltempstep() const {
  return _internal_has_celltempstep();
}
inline void EVSStepMsg::clear_celltempstep() {
  if (GetArenaForAllocation() == nullptr && celltempstep_ != nullptr) {
    delete celltempstep_;
  }
  celltempstep_ = nullptr;
}
inline const ::EVSInfo::StepPara& EVSStepMsg::_internal_celltempstep() const {
  const ::EVSInfo::StepPara* p = celltempstep_;
  return p != nullptr ? *p : reinterpret_cast<const ::EVSInfo::StepPara&>(
      ::EVSInfo::_StepPara_default_instance_);
}
inline const ::EVSInfo::StepPara& EVSStepMsg::celltempstep() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVSStepMsg.cellTempStep)
  return _internal_celltempstep();
}
inline void EVSStepMsg::unsafe_arena_set_allocated_celltempstep(
    ::EVSInfo::StepPara* celltempstep) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(celltempstep_);
  }
  celltempstep_ = celltempstep;
  if (celltempstep) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:EVSInfo.EVSStepMsg.cellTempStep)
}
inline ::EVSInfo::StepPara* EVSStepMsg::release_celltempstep() {
  
  ::EVSInfo::StepPara* temp = celltempstep_;
  celltempstep_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::EVSInfo::StepPara* EVSStepMsg::unsafe_arena_release_celltempstep() {
  // @@protoc_insertion_point(field_release:EVSInfo.EVSStepMsg.cellTempStep)
  
  ::EVSInfo::StepPara* temp = celltempstep_;
  celltempstep_ = nullptr;
  return temp;
}
inline ::EVSInfo::StepPara* EVSStepMsg::_internal_mutable_celltempstep() {
  
  if (celltempstep_ == nullptr) {
    auto* p = CreateMaybeMessage<::EVSInfo::StepPara>(GetArenaForAllocation());
    celltempstep_ = p;
  }
  return celltempstep_;
}
inline ::EVSInfo::StepPara* EVSStepMsg::mutable_celltempstep() {
  ::EVSInfo::StepPara* _msg = _internal_mutable_celltempstep();
  // @@protoc_insertion_point(field_mutable:EVSInfo.EVSStepMsg.cellTempStep)
  return _msg;
}
inline void EVSStepMsg::set_allocated_celltempstep(::EVSInfo::StepPara* celltempstep) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete celltempstep_;
  }
  if (celltempstep) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::EVSInfo::StepPara>::GetOwningArena(celltempstep);
    if (message_arena != submessage_arena) {
      celltempstep = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, celltempstep, submessage_arena);
    }
    
  } else {
    
  }
  celltempstep_ = celltempstep;
  // @@protoc_insertion_point(field_set_allocated:EVSInfo.EVSStepMsg.cellTempStep)
}

// -------------------------------------------------------------------

// EVMsgCtrl

// uint32 x2_0x02_State = 1;
inline void EVMsgCtrl::clear_x2_0x02_state() {
  x2_0x02_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_x2_0x02_state() const {
  return x2_0x02_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::x2_0x02_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.x2_0x02_State)
  return _internal_x2_0x02_state();
}
inline void EVMsgCtrl::_internal_set_x2_0x02_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  x2_0x02_state_ = value;
}
inline void EVMsgCtrl::set_x2_0x02_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_x2_0x02_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.x2_0x02_State)
}

// uint32 x4_0x04_State = 2;
inline void EVMsgCtrl::clear_x4_0x04_state() {
  x4_0x04_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_x4_0x04_state() const {
  return x4_0x04_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::x4_0x04_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.x4_0x04_State)
  return _internal_x4_0x04_state();
}
inline void EVMsgCtrl::_internal_set_x4_0x04_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  x4_0x04_state_ = value;
}
inline void EVMsgCtrl::set_x4_0x04_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_x4_0x04_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.x4_0x04_State)
}

// uint32 x6_0x06_State = 3;
inline void EVMsgCtrl::clear_x6_0x06_state() {
  x6_0x06_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_x6_0x06_state() const {
  return x6_0x06_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::x6_0x06_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.x6_0x06_State)
  return _internal_x6_0x06_state();
}
inline void EVMsgCtrl::_internal_set_x6_0x06_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  x6_0x06_state_ = value;
}
inline void EVMsgCtrl::set_x6_0x06_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_x6_0x06_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.x6_0x06_State)
}

// uint32 x9_0x09_State = 4;
inline void EVMsgCtrl::clear_x9_0x09_state() {
  x9_0x09_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_x9_0x09_state() const {
  return x9_0x09_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::x9_0x09_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.x9_0x09_State)
  return _internal_x9_0x09_state();
}
inline void EVMsgCtrl::_internal_set_x9_0x09_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  x9_0x09_state_ = value;
}
inline void EVMsgCtrl::set_x9_0x09_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_x9_0x09_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.x9_0x09_State)
}

// uint32 b2_0x12_State = 5;
inline void EVMsgCtrl::clear_b2_0x12_state() {
  b2_0x12_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_b2_0x12_state() const {
  return b2_0x12_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::b2_0x12_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.b2_0x12_State)
  return _internal_b2_0x12_state();
}
inline void EVMsgCtrl::_internal_set_b2_0x12_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  b2_0x12_state_ = value;
}
inline void EVMsgCtrl::set_b2_0x12_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_b2_0x12_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.b2_0x12_State)
}

// uint32 c2_0x22_State = 6;
inline void EVMsgCtrl::clear_c2_0x22_state() {
  c2_0x22_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_c2_0x22_state() const {
  return c2_0x22_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::c2_0x22_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.c2_0x22_State)
  return _internal_c2_0x22_state();
}
inline void EVMsgCtrl::_internal_set_c2_0x22_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  c2_0x22_state_ = value;
}
inline void EVMsgCtrl::set_c2_0x22_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_c2_0x22_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.c2_0x22_State)
}

// uint32 c4_0x24_State = 7;
inline void EVMsgCtrl::clear_c4_0x24_state() {
  c4_0x24_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_c4_0x24_state() const {
  return c4_0x24_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::c4_0x24_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.c4_0x24_State)
  return _internal_c4_0x24_state();
}
inline void EVMsgCtrl::_internal_set_c4_0x24_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  c4_0x24_state_ = value;
}
inline void EVMsgCtrl::set_c4_0x24_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_c4_0x24_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.c4_0x24_State)
}

// uint32 d2_0x32_State = 8;
inline void EVMsgCtrl::clear_d2_0x32_state() {
  d2_0x32_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_d2_0x32_state() const {
  return d2_0x32_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::d2_0x32_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.d2_0x32_State)
  return _internal_d2_0x32_state();
}
inline void EVMsgCtrl::_internal_set_d2_0x32_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  d2_0x32_state_ = value;
}
inline void EVMsgCtrl::set_d2_0x32_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_d2_0x32_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.d2_0x32_State)
}

// uint32 d4_0x34_State = 9;
inline void EVMsgCtrl::clear_d4_0x34_state() {
  d4_0x34_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_d4_0x34_state() const {
  return d4_0x34_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::d4_0x34_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.d4_0x34_State)
  return _internal_d4_0x34_state();
}
inline void EVMsgCtrl::_internal_set_d4_0x34_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  d4_0x34_state_ = value;
}
inline void EVMsgCtrl::set_d4_0x34_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_d4_0x34_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.d4_0x34_State)
}

// uint32 d6_0x36_State = 10;
inline void EVMsgCtrl::clear_d6_0x36_state() {
  d6_0x36_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_d6_0x36_state() const {
  return d6_0x36_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::d6_0x36_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.d6_0x36_State)
  return _internal_d6_0x36_state();
}
inline void EVMsgCtrl::_internal_set_d6_0x36_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  d6_0x36_state_ = value;
}
inline void EVMsgCtrl::set_d6_0x36_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_d6_0x36_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.d6_0x36_State)
}

// uint32 d7_0x37_State = 11;
inline void EVMsgCtrl::clear_d7_0x37_state() {
  d7_0x37_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_d7_0x37_state() const {
  return d7_0x37_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::d7_0x37_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.d7_0x37_State)
  return _internal_d7_0x37_state();
}
inline void EVMsgCtrl::_internal_set_d7_0x37_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  d7_0x37_state_ = value;
}
inline void EVMsgCtrl::set_d7_0x37_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_d7_0x37_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.d7_0x37_State)
}

// uint32 d9_0x39_State = 12;
inline void EVMsgCtrl::clear_d9_0x39_state() {
  d9_0x39_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_d9_0x39_state() const {
  return d9_0x39_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::d9_0x39_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.d9_0x39_State)
  return _internal_d9_0x39_state();
}
inline void EVMsgCtrl::_internal_set_d9_0x39_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  d9_0x39_state_ = value;
}
inline void EVMsgCtrl::set_d9_0x39_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_d9_0x39_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.d9_0x39_State)
}

// uint32 d10_0x3A_State = 13;
inline void EVMsgCtrl::clear_d10_0x3a_state() {
  d10_0x3a_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_d10_0x3a_state() const {
  return d10_0x3a_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::d10_0x3a_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.d10_0x3A_State)
  return _internal_d10_0x3a_state();
}
inline void EVMsgCtrl::_internal_set_d10_0x3a_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  d10_0x3a_state_ = value;
}
inline void EVMsgCtrl::set_d10_0x3a_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_d10_0x3a_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.d10_0x3A_State)
}

// uint32 e2_0x42_State = 14;
inline void EVMsgCtrl::clear_e2_0x42_state() {
  e2_0x42_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_e2_0x42_state() const {
  return e2_0x42_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::e2_0x42_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.e2_0x42_State)
  return _internal_e2_0x42_state();
}
inline void EVMsgCtrl::_internal_set_e2_0x42_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  e2_0x42_state_ = value;
}
inline void EVMsgCtrl::set_e2_0x42_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_e2_0x42_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.e2_0x42_State)
}

// uint32 e4_0x44_State = 15;
inline void EVMsgCtrl::clear_e4_0x44_state() {
  e4_0x44_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_e4_0x44_state() const {
  return e4_0x44_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::e4_0x44_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.e4_0x44_State)
  return _internal_e4_0x44_state();
}
inline void EVMsgCtrl::_internal_set_e4_0x44_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  e4_0x44_state_ = value;
}
inline void EVMsgCtrl::set_e4_0x44_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_e4_0x44_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.e4_0x44_State)
}

// uint32 f2_0x52_State = 16;
inline void EVMsgCtrl::clear_f2_0x52_state() {
  f2_0x52_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_f2_0x52_state() const {
  return f2_0x52_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::f2_0x52_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.f2_0x52_State)
  return _internal_f2_0x52_state();
}
inline void EVMsgCtrl::_internal_set_f2_0x52_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  f2_0x52_state_ = value;
}
inline void EVMsgCtrl::set_f2_0x52_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_f2_0x52_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.f2_0x52_State)
}

// uint32 g2_0x62_State = 17;
inline void EVMsgCtrl::clear_g2_0x62_state() {
  g2_0x62_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_g2_0x62_state() const {
  return g2_0x62_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::g2_0x62_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.g2_0x62_State)
  return _internal_g2_0x62_state();
}
inline void EVMsgCtrl::_internal_set_g2_0x62_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  g2_0x62_state_ = value;
}
inline void EVMsgCtrl::set_g2_0x62_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_g2_0x62_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.g2_0x62_State)
}

// uint32 g3_0x63_State = 18;
inline void EVMsgCtrl::clear_g3_0x63_state() {
  g3_0x63_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_g3_0x63_state() const {
  return g3_0x63_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::g3_0x63_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.g3_0x63_State)
  return _internal_g3_0x63_state();
}
inline void EVMsgCtrl::_internal_set_g3_0x63_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  g3_0x63_state_ = value;
}
inline void EVMsgCtrl::set_g3_0x63_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_g3_0x63_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.g3_0x63_State)
}

// uint32 g5_0x65_State = 19;
inline void EVMsgCtrl::clear_g5_0x65_state() {
  g5_0x65_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_g5_0x65_state() const {
  return g5_0x65_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::g5_0x65_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.g5_0x65_State)
  return _internal_g5_0x65_state();
}
inline void EVMsgCtrl::_internal_set_g5_0x65_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  g5_0x65_state_ = value;
}
inline void EVMsgCtrl::set_g5_0x65_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_g5_0x65_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.g5_0x65_State)
}

// uint32 h2_0x72_State = 20;
inline void EVMsgCtrl::clear_h2_0x72_state() {
  h2_0x72_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_h2_0x72_state() const {
  return h2_0x72_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::h2_0x72_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.h2_0x72_State)
  return _internal_h2_0x72_state();
}
inline void EVMsgCtrl::_internal_set_h2_0x72_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  h2_0x72_state_ = value;
}
inline void EVMsgCtrl::set_h2_0x72_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_h2_0x72_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.h2_0x72_State)
}

// uint32 h3_0x73_State = 21;
inline void EVMsgCtrl::clear_h3_0x73_state() {
  h3_0x73_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_h3_0x73_state() const {
  return h3_0x73_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::h3_0x73_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.h3_0x73_State)
  return _internal_h3_0x73_state();
}
inline void EVMsgCtrl::_internal_set_h3_0x73_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  h3_0x73_state_ = value;
}
inline void EVMsgCtrl::set_h3_0x73_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_h3_0x73_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.h3_0x73_State)
}

// uint32 h4_0x74_State = 22;
inline void EVMsgCtrl::clear_h4_0x74_state() {
  h4_0x74_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_h4_0x74_state() const {
  return h4_0x74_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::h4_0x74_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.h4_0x74_State)
  return _internal_h4_0x74_state();
}
inline void EVMsgCtrl::_internal_set_h4_0x74_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  h4_0x74_state_ = value;
}
inline void EVMsgCtrl::set_h4_0x74_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_h4_0x74_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.h4_0x74_State)
}

// uint32 h7_0x77_State = 23;
inline void EVMsgCtrl::clear_h7_0x77_state() {
  h7_0x77_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_h7_0x77_state() const {
  return h7_0x77_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::h7_0x77_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.h7_0x77_State)
  return _internal_h7_0x77_state();
}
inline void EVMsgCtrl::_internal_set_h7_0x77_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  h7_0x77_state_ = value;
}
inline void EVMsgCtrl::set_h7_0x77_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_h7_0x77_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.h7_0x77_State)
}

// uint32 h9_0x79_State = 24;
inline void EVMsgCtrl::clear_h9_0x79_state() {
  h9_0x79_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_h9_0x79_state() const {
  return h9_0x79_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::h9_0x79_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.h9_0x79_State)
  return _internal_h9_0x79_state();
}
inline void EVMsgCtrl::_internal_set_h9_0x79_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  h9_0x79_state_ = value;
}
inline void EVMsgCtrl::set_h9_0x79_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_h9_0x79_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.h9_0x79_State)
}

// uint32 h11_0x82_State = 25;
inline void EVMsgCtrl::clear_h11_0x82_state() {
  h11_0x82_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_h11_0x82_state() const {
  return h11_0x82_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::h11_0x82_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.h11_0x82_State)
  return _internal_h11_0x82_state();
}
inline void EVMsgCtrl::_internal_set_h11_0x82_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  h11_0x82_state_ = value;
}
inline void EVMsgCtrl::set_h11_0x82_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_h11_0x82_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.h11_0x82_State)
}

// uint32 h13_0x84_State = 26;
inline void EVMsgCtrl::clear_h13_0x84_state() {
  h13_0x84_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_h13_0x84_state() const {
  return h13_0x84_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::h13_0x84_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.h13_0x84_State)
  return _internal_h13_0x84_state();
}
inline void EVMsgCtrl::_internal_set_h13_0x84_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  h13_0x84_state_ = value;
}
inline void EVMsgCtrl::set_h13_0x84_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_h13_0x84_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.h13_0x84_State)
}

// uint32 h14_0x85_State = 27;
inline void EVMsgCtrl::clear_h14_0x85_state() {
  h14_0x85_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_h14_0x85_state() const {
  return h14_0x85_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::h14_0x85_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.h14_0x85_State)
  return _internal_h14_0x85_state();
}
inline void EVMsgCtrl::_internal_set_h14_0x85_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  h14_0x85_state_ = value;
}
inline void EVMsgCtrl::set_h14_0x85_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_h14_0x85_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.h14_0x85_State)
}

// uint32 h16_0x87_State = 28;
inline void EVMsgCtrl::clear_h16_0x87_state() {
  h16_0x87_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_h16_0x87_state() const {
  return h16_0x87_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::h16_0x87_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.h16_0x87_State)
  return _internal_h16_0x87_state();
}
inline void EVMsgCtrl::_internal_set_h16_0x87_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  h16_0x87_state_ = value;
}
inline void EVMsgCtrl::set_h16_0x87_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_h16_0x87_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.h16_0x87_State)
}

// uint32 h18_0x89_State = 29;
inline void EVMsgCtrl::clear_h18_0x89_state() {
  h18_0x89_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_h18_0x89_state() const {
  return h18_0x89_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::h18_0x89_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.h18_0x89_State)
  return _internal_h18_0x89_state();
}
inline void EVMsgCtrl::_internal_set_h18_0x89_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  h18_0x89_state_ = value;
}
inline void EVMsgCtrl::set_h18_0x89_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_h18_0x89_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.h18_0x89_State)
}

// uint32 h20_0x8B_State = 30;
inline void EVMsgCtrl::clear_h20_0x8b_state() {
  h20_0x8b_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_h20_0x8b_state() const {
  return h20_0x8b_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::h20_0x8b_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.h20_0x8B_State)
  return _internal_h20_0x8b_state();
}
inline void EVMsgCtrl::_internal_set_h20_0x8b_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  h20_0x8b_state_ = value;
}
inline void EVMsgCtrl::set_h20_0x8b_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_h20_0x8b_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.h20_0x8B_State)
}

// uint32 i1_0x91_State = 31;
inline void EVMsgCtrl::clear_i1_0x91_state() {
  i1_0x91_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_i1_0x91_state() const {
  return i1_0x91_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::i1_0x91_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.i1_0x91_State)
  return _internal_i1_0x91_state();
}
inline void EVMsgCtrl::_internal_set_i1_0x91_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  i1_0x91_state_ = value;
}
inline void EVMsgCtrl::set_i1_0x91_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_i1_0x91_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.i1_0x91_State)
}

// uint32 i4_0x94_State = 32;
inline void EVMsgCtrl::clear_i4_0x94_state() {
  i4_0x94_state_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::_internal_i4_0x94_state() const {
  return i4_0x94_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVMsgCtrl::i4_0x94_state() const {
  // @@protoc_insertion_point(field_get:EVSInfo.EVMsgCtrl.i4_0x94_State)
  return _internal_i4_0x94_state();
}
inline void EVMsgCtrl::_internal_set_i4_0x94_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  i4_0x94_state_ = value;
}
inline void EVMsgCtrl::set_i4_0x94_state(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_i4_0x94_state(value);
  // @@protoc_insertion_point(field_set:EVSInfo.EVMsgCtrl.i4_0x94_State)
}

// -------------------------------------------------------------------

// IpMsg

// bytes selfIP = 1;
inline void IpMsg::clear_selfip() {
  selfip_.ClearToEmpty();
}
inline const std::string& IpMsg::selfip() const {
  // @@protoc_insertion_point(field_get:EVSInfo.IpMsg.selfIP)
  return _internal_selfip();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void IpMsg::set_selfip(ArgT0&& arg0, ArgT... args) {
 
 selfip_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:EVSInfo.IpMsg.selfIP)
}
inline std::string* IpMsg::mutable_selfip() {
  std::string* _s = _internal_mutable_selfip();
  // @@protoc_insertion_point(field_mutable:EVSInfo.IpMsg.selfIP)
  return _s;
}
inline const std::string& IpMsg::_internal_selfip() const {
  return selfip_.Get();
}
inline void IpMsg::_internal_set_selfip(const std::string& value) {
  
  selfip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* IpMsg::_internal_mutable_selfip() {
  
  return selfip_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* IpMsg::release_selfip() {
  // @@protoc_insertion_point(field_release:EVSInfo.IpMsg.selfIP)
  return selfip_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void IpMsg::set_allocated_selfip(std::string* selfip) {
  if (selfip != nullptr) {
    
  } else {
    
  }
  selfip_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), selfip,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:EVSInfo.IpMsg.selfIP)
}

// uint32 selfPort = 2;
inline void IpMsg::clear_selfport() {
  selfport_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 IpMsg::_internal_selfport() const {
  return selfport_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 IpMsg::selfport() const {
  // @@protoc_insertion_point(field_get:EVSInfo.IpMsg.selfPort)
  return _internal_selfport();
}
inline void IpMsg::_internal_set_selfport(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  selfport_ = value;
}
inline void IpMsg::set_selfport(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_selfport(value);
  // @@protoc_insertion_point(field_set:EVSInfo.IpMsg.selfPort)
}

// bytes serviceIP = 3;
inline void IpMsg::clear_serviceip() {
  serviceip_.ClearToEmpty();
}
inline const std::string& IpMsg::serviceip() const {
  // @@protoc_insertion_point(field_get:EVSInfo.IpMsg.serviceIP)
  return _internal_serviceip();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void IpMsg::set_serviceip(ArgT0&& arg0, ArgT... args) {
 
 serviceip_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:EVSInfo.IpMsg.serviceIP)
}
inline std::string* IpMsg::mutable_serviceip() {
  std::string* _s = _internal_mutable_serviceip();
  // @@protoc_insertion_point(field_mutable:EVSInfo.IpMsg.serviceIP)
  return _s;
}
inline const std::string& IpMsg::_internal_serviceip() const {
  return serviceip_.Get();
}
inline void IpMsg::_internal_set_serviceip(const std::string& value) {
  
  serviceip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* IpMsg::_internal_mutable_serviceip() {
  
  return serviceip_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* IpMsg::release_serviceip() {
  // @@protoc_insertion_point(field_release:EVSInfo.IpMsg.serviceIP)
  return serviceip_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void IpMsg::set_allocated_serviceip(std::string* serviceip) {
  if (serviceip != nullptr) {
    
  } else {
    
  }
  serviceip_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), serviceip,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:EVSInfo.IpMsg.serviceIP)
}

// uint32 servicePort = 4;
inline void IpMsg::clear_serviceport() {
  serviceport_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 IpMsg::_internal_serviceport() const {
  return serviceport_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 IpMsg::serviceport() const {
  // @@protoc_insertion_point(field_get:EVSInfo.IpMsg.servicePort)
  return _internal_serviceport();
}
inline void IpMsg::_internal_set_serviceport(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  serviceport_ = value;
}
inline void IpMsg::set_serviceport(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_serviceport(value);
  // @@protoc_insertion_point(field_set:EVSInfo.IpMsg.servicePort)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace EVSInfo

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_GCU_5fEVS_5fINFO_2eproto
