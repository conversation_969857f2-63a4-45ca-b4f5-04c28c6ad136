// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GCU_AllFaultEnum.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_GCU_5fAllFaultEnum_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_GCU_5fAllFaultEnum_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3017000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3017003 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_GCU_5fAllFaultEnum_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_GCU_5fAllFaultEnum_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[10]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GCU_5fAllFaultEnum_2eproto;
namespace AllFaultEnum {
class ADModuleAlarm;
struct ADModuleAlarmDefaultTypeInternal;
extern ADModuleAlarmDefaultTypeInternal _ADModuleAlarm_default_instance_;
class DMCContactorFaultSend;
struct DMCContactorFaultSendDefaultTypeInternal;
extern DMCContactorFaultSendDefaultTypeInternal _DMCContactorFaultSend_default_instance_;
class DMCFaultState;
struct DMCFaultStateDefaultTypeInternal;
extern DMCFaultStateDefaultTypeInternal _DMCFaultState_default_instance_;
class MatrixStatus;
struct MatrixStatusDefaultTypeInternal;
extern MatrixStatusDefaultTypeInternal _MatrixStatus_default_instance_;
class OHPContactorFaultSend;
struct OHPContactorFaultSendDefaultTypeInternal;
extern OHPContactorFaultSendDefaultTypeInternal _OHPContactorFaultSend_default_instance_;
class OHPFaultState;
struct OHPFaultStateDefaultTypeInternal;
extern OHPFaultStateDefaultTypeInternal _OHPFaultState_default_instance_;
class PMMContactorFaultSend;
struct PMMContactorFaultSendDefaultTypeInternal;
extern PMMContactorFaultSendDefaultTypeInternal _PMMContactorFaultSend_default_instance_;
class PMMFaultState;
struct PMMFaultStateDefaultTypeInternal;
extern PMMFaultStateDefaultTypeInternal _PMMFaultState_default_instance_;
class VCIContactorFaultSend;
struct VCIContactorFaultSendDefaultTypeInternal;
extern VCIContactorFaultSendDefaultTypeInternal _VCIContactorFaultSend_default_instance_;
class VCIFaultState;
struct VCIFaultStateDefaultTypeInternal;
extern VCIFaultStateDefaultTypeInternal _VCIFaultState_default_instance_;
}  // namespace AllFaultEnum
PROTOBUF_NAMESPACE_OPEN
template<> ::AllFaultEnum::ADModuleAlarm* Arena::CreateMaybeMessage<::AllFaultEnum::ADModuleAlarm>(Arena*);
template<> ::AllFaultEnum::DMCContactorFaultSend* Arena::CreateMaybeMessage<::AllFaultEnum::DMCContactorFaultSend>(Arena*);
template<> ::AllFaultEnum::DMCFaultState* Arena::CreateMaybeMessage<::AllFaultEnum::DMCFaultState>(Arena*);
template<> ::AllFaultEnum::MatrixStatus* Arena::CreateMaybeMessage<::AllFaultEnum::MatrixStatus>(Arena*);
template<> ::AllFaultEnum::OHPContactorFaultSend* Arena::CreateMaybeMessage<::AllFaultEnum::OHPContactorFaultSend>(Arena*);
template<> ::AllFaultEnum::OHPFaultState* Arena::CreateMaybeMessage<::AllFaultEnum::OHPFaultState>(Arena*);
template<> ::AllFaultEnum::PMMContactorFaultSend* Arena::CreateMaybeMessage<::AllFaultEnum::PMMContactorFaultSend>(Arena*);
template<> ::AllFaultEnum::PMMFaultState* Arena::CreateMaybeMessage<::AllFaultEnum::PMMFaultState>(Arena*);
template<> ::AllFaultEnum::VCIContactorFaultSend* Arena::CreateMaybeMessage<::AllFaultEnum::VCIContactorFaultSend>(Arena*);
template<> ::AllFaultEnum::VCIFaultState* Arena::CreateMaybeMessage<::AllFaultEnum::VCIFaultState>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace AllFaultEnum {

enum VCIFaultEnum : int {
  DefaultVCIFault = 0,
  gunShortCircuit = 1,
  BMSCellVolOver = 2,
  BMSPackVolOver = 3,
  BMSPackCurrOver = 4,
  BMSCellTempOver = 5,
  BMSTempOutControl = 6,
  BMSRelayAdhesion = 7,
  BMSOverCharge = 8,
  BMSAuxPowerAbnormal = 9,
  BMSInnerRelayOpenCircuit = 10,
  BMSDemandCurrAbnormal = 11,
  BMSDemandVolAbnormal = 12,
  BMSBatVolAbnormal = 13,
  BMSDataUpdateABNORMAL = 14,
  BMSBCSCurrABNORMAL = 15,
  ServiceOffline = 16,
  CC1VoltAbnormal = 17,
  GunStateAbnormal = 18,
  ElockAbnormal = 19,
  GunReserveConnect = 20,
  AuxPowerAbnormal = 21,
  InsultVolHigh = 22,
  InsultVolLow = 23,
  InsultWarn = 24,
  InsultAbnormal = 25,
  DisChgFail = 26,
  StartTimeout = 27,
  BatAllowVolLow = 28,
  StartVoltHigh = 29,
  ElockUnlock = 30,
  BatVolHigh = 32,
  BatVolLow = 33,
  BatVolDiffFromBCP = 34,
  PreChgVolHigh = 35,
  PreChgVolLow = 36,
  FuseBreakFault = 37,
  OutOverCurr = 38,
  OutOverVol = 39,
  MeterCurrAbnormal = 40,
  MeterVolAbnormal = 41,
  GunTempWarn = 42,
  GunOverTemp = 43,
  NoActiveCurr = 44,
  SOCFull = 45,
  ChgForbidTimeout = 46,
  ChgSysAbnormal = 47,
  BCSCurrDiff = 48,
  BCSVolDiff = 49,
  GunNotAtHome = 50,
  GunLifeWarn = 51,
  VINStartTimeOut = 52,
  BATVOLsimpleABNORMAL = 53,
  OutOverVOLT = 54,
  OutLowVOLT = 55,
  OutCurrHIGH = 56,
  GunTempSensorFault = 57,
  BmsDemandVolLow = 58,
  InnerCommFault = 59,
  ElockErrDrawGun = 60,
  UserDrawGun = 61,
  BSMTimeout = 62,
  BSTParaMisMatch = 63,
  BMSProtoVerUnMatch = 64,
  BRMTimeout = 65,
  BRMAbnormal = 66,
  BCPTimeout = 67,
  BCPAbnormal = 68,
  BROTimeout = 69,
  BROaaTimeout = 70,
  BCLTimeout = 71,
  BCSTimeout = 72,
  BSMCellVolLow = 73,
  BSMSocHigh = 74,
  BSMSocLow = 75,
  BSMPackOverCurr = 76,
  BSMBatTempHigh = 77,
  BSMInsultAbnormal = 78,
  BSMContactorAbnormal = 79,
  BSMCellVolHigh = 80,
  BSTTimeout = 81,
  BSTMeetSOC = 82,
  BSTMeetVol = 83,
  BSTMeetCellvol = 84,
  BSTInsultFault = 85,
  BSTOutConnectorOverTemp = 86,
  BSTCommponentOverTemp = 87,
  BSTChgConnectorAbnormal = 88,
  BSTBatOverTemp = 89,
  BSTHighVolRelayFault = 90,
  BSTcc2Fault = 91,
  BSTOtherFault = 92,
  BSTOverCurr = 93,
  BSTVolAbnormal = 94,
  BSTNoReason = 95,
  BSDTimeout = 96,
  DoorSensor = 97,
  EmergencyStopV = 98,
  ContactorOpenFault = 99,
  ContactorCloseFault = 100,
  FANerror = 101,
  PMMlockFault = 102,
  ContactorPEDriver = 103,
  ContactorNEDriver = 104,
  ContactorPEStick = 105,
  ContactorNEStick = 106,
  CanSendFault = 107,
  CanBufferFull = 108,
  BMSNotReply = 109,
  PMMnoADmodle = 110,
  BMSDemandLow3A = 111,
  InsultLow700k = 112,
  ADFixing = 113,
  ADnoFix = 114,
  MccbBreakFault = 115,
  PreChargeBatVolLow = 116,
  BCSCurrBigBCL = 118,
  BCSCurrLow = 119,
  BCSCurrHigh = 120,
  CooperDifferTerminal = 121,
  CooperStepStart = 122,
  SOCHighStop = 123,
  BMSstateErr = 124,
  BSTNullErr = 125,
  BSTotherFaultStop = 126,
  InsultSampErr = 127,
  Smoke = 128,
  Water = 129,
  ToppleFall = 130,
  Lightning = 131,
  Dustproof = 132,
  CooperGunNumErr = 133,
  CurrOffsetErr = 134,
  BCSCurrOverBCP = 135,
  LC_OverVolt = 144,
  LC_LowVolt = 145,
  LC_LowLiquidLevel = 146,
  LC_PumpOverCurr = 147,
  LC_PumpLowPress = 148,
  LC_PumpOverPress = 149,
  LC_PumpOverTemp = 150,
  LC_PumpLockDrotor = 151,
  LC_PumpCommErr = 152,
  LC_LowFlow = 153,
  LC_HighFlow = 154,
  LC_FanOverCurr = 155,
  LC_DevCommuErr = 156,
  LC_ParamSetErr = 157,
  CtAppointPlanChange = 160,
  CtAppointTimeNotCome = 161,
  CtChgLeakage = 162,
  CtConnectErr = 163,
  CtVehiElockOpen = 164,
  CtVehiS2Open = 165,
  CtChgFuncConferTimeout = 166,
  CtChgParaConfigTimeout = 167,
  CtChgAuthenTimeout = 168,
  CtChgAppointTimeout = 169,
  CtChgSelfCheckTimeout = 170,
  CtChgPowerSupplyTimeout = 171,
  CtChgPreChgTimeout = 172,
  CtChgEnergyTransTimeout = 173,
  CtChgEndTimeout = 174,
  CtBmsNecessFunLack = 175,
  CtBmsAuthenFunLack = 176,
  CtBmsParaNoMatch = 177,
  CtChgAuthenFail = 178,
  CtBmsNotAllowAppoint = 179,
  CtBmsWakeUpFail = 180,
  CtPauseTimeout = 181,
  CtPauseCntOver = 182,
  CtPauseRepeat = 183,
  BtNormalStop = 192,
  BtUserStop = 193,
  BtAppointPlanChange = 194,
  BtInsultFail = 195,
  BtConnectErr = 196,
  BtPEOpen = 197,
  BtCC2Err = 198,
  BtCC3Err = 199,
  BtElockErr = 200,
  BtFaultStop = 201,
  BtEmergencyStop = 202,
  BtS1OpenStop = 203,
  BtChargerStop = 204,
  BtChgFuncConferTimeout = 205,
  BtChgParaConfigTimeout = 206,
  BtChgAuthenTimeout = 207,
  BtChgAppointTimeout = 208,
  BtChgSelfCheckTimeout = 209,
  BtChgPowerSupplyTimeout = 210,
  BtChgPreChgTimeout = 211,
  BtChgEnergyTransTimeout = 212,
  BtChgEndTimeout = 213,
  BtNecessFunLack = 214,
  BtFunConferFail = 215,
  BtParaNoMatch = 216,
  BtAuthenFail = 217,
  BtNotAllowAppoint = 218,
  BtWakeUpFail = 219,
  BtSupplyVolHigh = 220,
  BtSupplyCurrHigh = 221,
  BtSupplyVolErr = 222,
  BtSupplyCurrErr = 223,
  BtSupplyModSwitchFail = 224,
  BtTransVolHigh = 225,
  BtTransCurrHigh = 226,
  BtTransVolErr = 227,
  BtTransCurrErr = 228,
  BtGunOverTemp = 229,
  BtChargerStopFail = 230,
  BtPauseTimeout = 231,
  BtPauseCntOver = 232,
  BtPauseRepeat = 233,
  BtStageEnsureFail = 234,
  BtStageRequestTimeout = 235,
  StageConfirmTimeout = 236,
  EvStopTimeout = 237,
  EvWakeupTimeout = 238,
  FunConferTimeout = 239,
  EvConfigTimeout = 240,
  EvAuthenTimeout = 241,
  EvAppointMsgTimeout = 242,
  EvAppointConferTimeout = 243,
  EvBCLTimeout = 244,
  EvPauseTimeout = 245,
  EvBSDTimeout = 246,
  VCIFaultEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  VCIFaultEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool VCIFaultEnum_IsValid(int value);
constexpr VCIFaultEnum VCIFaultEnum_MIN = DefaultVCIFault;
constexpr VCIFaultEnum VCIFaultEnum_MAX = EvBSDTimeout;
constexpr int VCIFaultEnum_ARRAYSIZE = VCIFaultEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* VCIFaultEnum_descriptor();
template<typename T>
inline const std::string& VCIFaultEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, VCIFaultEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function VCIFaultEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    VCIFaultEnum_descriptor(), enum_t_value);
}
inline bool VCIFaultEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, VCIFaultEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<VCIFaultEnum>(
    VCIFaultEnum_descriptor(), name, value);
}
enum PMMFaultEnum : int {
  DefaultPMMFault = 0,
  HvdcStartFail = 1,
  HvdcGroupFail = 2,
  HvdcOffline = 3,
  HvdcStopFail = 4,
  OutOverVolt = 16,
  OverTemp = 17,
  FanFault = 18,
  EEPROMFault = 19,
  CANErr = 20,
  ACLowVolt = 21,
  ACLackPhase = 22,
  SerUnblncdCurr = 23,
  IDRepeat = 24,
  ACOverVolt = 25,
  PFCProtect = 26,
  SlightUnblncdCurr = 27,
  DischgAlarm = 28,
  SCFault = 29,
  InnerComAlarm = 30,
  ElectGridAbnor = 32,
  ModRelayFault = 33,
  OutCaplifeWarn = 34,
  ACCut = 35,
  DCOCP = 36,
  BoardcorrectFail = 37,
  DiodeTempWarn = 38,
  MOSTempWarn = 39,
  PFCTempWarn = 40,
  NoChgMod = 49,
  ModStartFail = 50,
  OutShortCircuit = 51,
  ContactPosStick = 52,
  ContactNegStick = 53,
  ContactPosDiscnct = 54,
  ContactNegDiscnct = 55,
  InputACOverVolt = 56,
  InputACLowVolt = 57,
  InputACLakePhase = 58,
  ArrayContactorCmdAbnormal = 59,
  OutContactorCmdAbnormal = 60,
  ArrayContactorStick = 61,
  ArrayContactorDiscnct = 62,
  EmergencyStopP = 64,
  GateMagnetP = 65,
  ADModuleHybrid = 66,
  SPDFaultP = 67,
  SmokeSensorP = 68,
  WaterSensorP = 69,
  TiltSensorP = 70,
  InContactorCmdAbnormal = 71,
  ACContactorStick = 72,
  ACContactorDiscnct = 73,
  Systerm_DC_FanFault = 74,
  Systerm_AC_FanFault = 75,
  Systerm_Meter_Fault = 76,
  PDUOffline = 77,
  LinkArrayContactorStick = 78,
  Gun_NoChgModule = 79,
  HvdcAllMissing = 80,
  HvdcOutOfRange = 81,
  HvdcAddrTrample = 82,
  HvdcAddrErr = 83,
  HvdcAddrNull = 84,
  SystermConfigFault = 85,
  MatrixFault = 86,
  GunLinkModulePowerOffFailed = 87,
  SocketVciOffline = 88,
  SocketOhpOffline = 89,
  SocketDmcOffline = 90,
  SocketFsmOffline = 91,
  DustproofBlocked = 96,
  PMMFaultMax = 255,
  PMMFaultEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  PMMFaultEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool PMMFaultEnum_IsValid(int value);
constexpr PMMFaultEnum PMMFaultEnum_MIN = DefaultPMMFault;
constexpr PMMFaultEnum PMMFaultEnum_MAX = PMMFaultMax;
constexpr int PMMFaultEnum_ARRAYSIZE = PMMFaultEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PMMFaultEnum_descriptor();
template<typename T>
inline const std::string& PMMFaultEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, PMMFaultEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function PMMFaultEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    PMMFaultEnum_descriptor(), enum_t_value);
}
inline bool PMMFaultEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, PMMFaultEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<PMMFaultEnum>(
    PMMFaultEnum_descriptor(), name, value);
}
enum OHPFaultEnum : int {
  DefaultOHPFault = 0,
  LTEFault = 1,
  WiFiFault = 2,
  GmacFault = 3,
  NetFault = 4,
  SettlementCloudFault = 5,
  SettlementCloudStop = 6,
  StartUpTimeOut = 16,
  PeriodicCommunicationFault = 17,
  OrderSettlementFault = 18,
  OrderVinTimeStop = 19,
  SerialNumberFault = 32,
  UUIDFault = 33,
  FRAMFault = 34,
  MeterPowerFault = 35,
  MeterCurrFault = 36,
  MeterOffLine = 37,
  MeterVolFault = 38,
  MeterDevStartFail = 39,
  DBFault = 40,
  MeterPowerForwardJump = 41,
  MeterPowerReverseJump = 42,
  GateMagnet = 48,
  EmergencyStop = 49,
  SPDFault = 50,
  SysFanFault = 51,
  SysOverTemperature = 52,
  CtrlOverTemperature = 53,
  SmokeSensor = 54,
  WaterSensor = 55,
  TiltSensor = 56,
  HMIOffLine = 57,
  SOCOffLine = 58,
  NFCOffLine = 59,
  SpeakerFault = 60,
  MicphoneFault = 61,
  CameraFault = 62,
  LPROffline = 63,
  FSMnoService = 64,
  VCInoService = 65,
  PMMnoService = 66,
  LCRnoService = 67,
  DMCnoService = 68,
  LOSnoService = 69,
  OscnoService = 70,
  OrderStartFault = 71,
  StartUpCheckOhpEnableFail = 96,
  StartUpCheckSysEnableFail = 97,
  StartUpGetOrderFault = 98,
  StartUpFramWriteFail = 99,
  StartUpDbWriteFail = 100,
  StartTimeOut = 101,
  AuthTimeOut = 102,
  PreStartTimeOut = 103,
  PreStartGetVinFail = 104,
  AuthFail = 105,
  AdFixTimeOut = 112,
  DrawGunAbnormal = 113,
  SystemTimeFault = 114,
  StartCheckOSCRateParamFail = 129,
  StartCheckOSCParamEmpty = 130,
  StartCheckSysTimeFail = 131,
  StartSelfErrPipelineState = 132,
  StartCheckPipelineStart = 133,
  StartCheckIdle = 134,
  StartCheckFault = 135,
  StartFailNotFoundOrder = 144,
  FRAMReadFFFFFail = 145,
  MultiOscOffline = 146,
  PanicOhpCreate = 147,
  OHPFaultEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  OHPFaultEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool OHPFaultEnum_IsValid(int value);
constexpr OHPFaultEnum OHPFaultEnum_MIN = DefaultOHPFault;
constexpr OHPFaultEnum OHPFaultEnum_MAX = PanicOhpCreate;
constexpr int OHPFaultEnum_ARRAYSIZE = OHPFaultEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OHPFaultEnum_descriptor();
template<typename T>
inline const std::string& OHPFaultEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, OHPFaultEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function OHPFaultEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    OHPFaultEnum_descriptor(), enum_t_value);
}
inline bool OHPFaultEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, OHPFaultEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<OHPFaultEnum>(
    OHPFaultEnum_descriptor(), name, value);
}
enum DMCFaultEnum : int {
  DefaultDMCFault = 0,
  PendingFault = 1,
  DisputeFault = 2,
  DMCFaultEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  DMCFaultEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool DMCFaultEnum_IsValid(int value);
constexpr DMCFaultEnum DMCFaultEnum_MIN = DefaultDMCFault;
constexpr DMCFaultEnum DMCFaultEnum_MAX = DisputeFault;
constexpr int DMCFaultEnum_ARRAYSIZE = DMCFaultEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DMCFaultEnum_descriptor();
template<typename T>
inline const std::string& DMCFaultEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, DMCFaultEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function DMCFaultEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    DMCFaultEnum_descriptor(), enum_t_value);
}
inline bool DMCFaultEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, DMCFaultEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<DMCFaultEnum>(
    DMCFaultEnum_descriptor(), name, value);
}
enum AlarmStateEnum : int {
  DefaultAlarm = 0,
  ADModuleNormal = 1,
  ADModuleFaultNo = 2,
  AlarmStateEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  AlarmStateEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool AlarmStateEnum_IsValid(int value);
constexpr AlarmStateEnum AlarmStateEnum_MIN = DefaultAlarm;
constexpr AlarmStateEnum AlarmStateEnum_MAX = ADModuleFaultNo;
constexpr int AlarmStateEnum_ARRAYSIZE = AlarmStateEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* AlarmStateEnum_descriptor();
template<typename T>
inline const std::string& AlarmStateEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, AlarmStateEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function AlarmStateEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    AlarmStateEnum_descriptor(), enum_t_value);
}
inline bool AlarmStateEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, AlarmStateEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<AlarmStateEnum>(
    AlarmStateEnum_descriptor(), name, value);
}
// ===================================================================

class VCIFaultState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:AllFaultEnum.VCIFaultState) */ {
 public:
  inline VCIFaultState() : VCIFaultState(nullptr) {}
  ~VCIFaultState() override;
  explicit constexpr VCIFaultState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  VCIFaultState(const VCIFaultState& from);
  VCIFaultState(VCIFaultState&& from) noexcept
    : VCIFaultState() {
    *this = ::std::move(from);
  }

  inline VCIFaultState& operator=(const VCIFaultState& from) {
    CopyFrom(from);
    return *this;
  }
  inline VCIFaultState& operator=(VCIFaultState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const VCIFaultState& default_instance() {
    return *internal_default_instance();
  }
  static inline const VCIFaultState* internal_default_instance() {
    return reinterpret_cast<const VCIFaultState*>(
               &_VCIFaultState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(VCIFaultState& a, VCIFaultState& b) {
    a.Swap(&b);
  }
  inline void Swap(VCIFaultState* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VCIFaultState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline VCIFaultState* New() const final {
    return new VCIFaultState();
  }

  VCIFaultState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<VCIFaultState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const VCIFaultState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const VCIFaultState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VCIFaultState* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "AllFaultEnum.VCIFaultState";
  }
  protected:
  explicit VCIFaultState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFaultTypeFieldNumber = 1,
    kFaultStateFieldNumber = 2,
    kFaultRaiseTimeFieldNumber = 3,
    kFaultDownTimeFieldNumber = 4,
    kShutDownTypeFieldNumber = 5,
    kShowTypeFieldNumber = 6,
  };
  // .AllFaultEnum.VCIFaultEnum faultType = 1;
  void clear_faulttype();
  ::AllFaultEnum::VCIFaultEnum faulttype() const;
  void set_faulttype(::AllFaultEnum::VCIFaultEnum value);
  private:
  ::AllFaultEnum::VCIFaultEnum _internal_faulttype() const;
  void _internal_set_faulttype(::AllFaultEnum::VCIFaultEnum value);
  public:

  // .AllFaultEnum.AlarmStateEnum faultState = 2;
  void clear_faultstate();
  ::AllFaultEnum::AlarmStateEnum faultstate() const;
  void set_faultstate(::AllFaultEnum::AlarmStateEnum value);
  private:
  ::AllFaultEnum::AlarmStateEnum _internal_faultstate() const;
  void _internal_set_faultstate(::AllFaultEnum::AlarmStateEnum value);
  public:

  // uint64 faultRaiseTime = 3;
  void clear_faultraisetime();
  ::PROTOBUF_NAMESPACE_ID::uint64 faultraisetime() const;
  void set_faultraisetime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_faultraisetime() const;
  void _internal_set_faultraisetime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // uint64 faultDownTime = 4;
  void clear_faultdowntime();
  ::PROTOBUF_NAMESPACE_ID::uint64 faultdowntime() const;
  void set_faultdowntime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_faultdowntime() const;
  void _internal_set_faultdowntime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // uint32 ShutDownType = 5;
  void clear_shutdowntype();
  ::PROTOBUF_NAMESPACE_ID::uint32 shutdowntype() const;
  void set_shutdowntype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_shutdowntype() const;
  void _internal_set_shutdowntype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 ShowType = 6;
  void clear_showtype();
  ::PROTOBUF_NAMESPACE_ID::uint32 showtype() const;
  void set_showtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_showtype() const;
  void _internal_set_showtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:AllFaultEnum.VCIFaultState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int faulttype_;
  int faultstate_;
  ::PROTOBUF_NAMESPACE_ID::uint64 faultraisetime_;
  ::PROTOBUF_NAMESPACE_ID::uint64 faultdowntime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 shutdowntype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 showtype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fAllFaultEnum_2eproto;
};
// -------------------------------------------------------------------

class PMMFaultState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:AllFaultEnum.PMMFaultState) */ {
 public:
  inline PMMFaultState() : PMMFaultState(nullptr) {}
  ~PMMFaultState() override;
  explicit constexpr PMMFaultState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PMMFaultState(const PMMFaultState& from);
  PMMFaultState(PMMFaultState&& from) noexcept
    : PMMFaultState() {
    *this = ::std::move(from);
  }

  inline PMMFaultState& operator=(const PMMFaultState& from) {
    CopyFrom(from);
    return *this;
  }
  inline PMMFaultState& operator=(PMMFaultState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PMMFaultState& default_instance() {
    return *internal_default_instance();
  }
  static inline const PMMFaultState* internal_default_instance() {
    return reinterpret_cast<const PMMFaultState*>(
               &_PMMFaultState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(PMMFaultState& a, PMMFaultState& b) {
    a.Swap(&b);
  }
  inline void Swap(PMMFaultState* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PMMFaultState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline PMMFaultState* New() const final {
    return new PMMFaultState();
  }

  PMMFaultState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<PMMFaultState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PMMFaultState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PMMFaultState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PMMFaultState* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "AllFaultEnum.PMMFaultState";
  }
  protected:
  explicit PMMFaultState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFaultTypeFieldNumber = 1,
    kFaultStateFieldNumber = 2,
    kFaultRaiseTimeFieldNumber = 3,
    kFaultDownTimeFieldNumber = 4,
    kShutDownTypeFieldNumber = 5,
    kShowTypeFieldNumber = 6,
  };
  // .AllFaultEnum.PMMFaultEnum faultType = 1;
  void clear_faulttype();
  ::AllFaultEnum::PMMFaultEnum faulttype() const;
  void set_faulttype(::AllFaultEnum::PMMFaultEnum value);
  private:
  ::AllFaultEnum::PMMFaultEnum _internal_faulttype() const;
  void _internal_set_faulttype(::AllFaultEnum::PMMFaultEnum value);
  public:

  // .AllFaultEnum.AlarmStateEnum faultState = 2;
  void clear_faultstate();
  ::AllFaultEnum::AlarmStateEnum faultstate() const;
  void set_faultstate(::AllFaultEnum::AlarmStateEnum value);
  private:
  ::AllFaultEnum::AlarmStateEnum _internal_faultstate() const;
  void _internal_set_faultstate(::AllFaultEnum::AlarmStateEnum value);
  public:

  // uint64 faultRaiseTime = 3;
  void clear_faultraisetime();
  ::PROTOBUF_NAMESPACE_ID::uint64 faultraisetime() const;
  void set_faultraisetime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_faultraisetime() const;
  void _internal_set_faultraisetime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // uint64 faultDownTime = 4;
  void clear_faultdowntime();
  ::PROTOBUF_NAMESPACE_ID::uint64 faultdowntime() const;
  void set_faultdowntime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_faultdowntime() const;
  void _internal_set_faultdowntime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // uint32 ShutDownType = 5;
  void clear_shutdowntype();
  ::PROTOBUF_NAMESPACE_ID::uint32 shutdowntype() const;
  void set_shutdowntype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_shutdowntype() const;
  void _internal_set_shutdowntype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 ShowType = 6;
  void clear_showtype();
  ::PROTOBUF_NAMESPACE_ID::uint32 showtype() const;
  void set_showtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_showtype() const;
  void _internal_set_showtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:AllFaultEnum.PMMFaultState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int faulttype_;
  int faultstate_;
  ::PROTOBUF_NAMESPACE_ID::uint64 faultraisetime_;
  ::PROTOBUF_NAMESPACE_ID::uint64 faultdowntime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 shutdowntype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 showtype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fAllFaultEnum_2eproto;
};
// -------------------------------------------------------------------

class OHPFaultState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:AllFaultEnum.OHPFaultState) */ {
 public:
  inline OHPFaultState() : OHPFaultState(nullptr) {}
  ~OHPFaultState() override;
  explicit constexpr OHPFaultState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OHPFaultState(const OHPFaultState& from);
  OHPFaultState(OHPFaultState&& from) noexcept
    : OHPFaultState() {
    *this = ::std::move(from);
  }

  inline OHPFaultState& operator=(const OHPFaultState& from) {
    CopyFrom(from);
    return *this;
  }
  inline OHPFaultState& operator=(OHPFaultState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OHPFaultState& default_instance() {
    return *internal_default_instance();
  }
  static inline const OHPFaultState* internal_default_instance() {
    return reinterpret_cast<const OHPFaultState*>(
               &_OHPFaultState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(OHPFaultState& a, OHPFaultState& b) {
    a.Swap(&b);
  }
  inline void Swap(OHPFaultState* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OHPFaultState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OHPFaultState* New() const final {
    return new OHPFaultState();
  }

  OHPFaultState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OHPFaultState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OHPFaultState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const OHPFaultState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OHPFaultState* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "AllFaultEnum.OHPFaultState";
  }
  protected:
  explicit OHPFaultState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFaultTypeFieldNumber = 1,
    kFaultStateFieldNumber = 2,
    kFaultRaiseTimeFieldNumber = 3,
    kFaultDownTimeFieldNumber = 4,
    kShutDownTypeFieldNumber = 5,
    kShowTypeFieldNumber = 6,
  };
  // .AllFaultEnum.OHPFaultEnum faultType = 1;
  void clear_faulttype();
  ::AllFaultEnum::OHPFaultEnum faulttype() const;
  void set_faulttype(::AllFaultEnum::OHPFaultEnum value);
  private:
  ::AllFaultEnum::OHPFaultEnum _internal_faulttype() const;
  void _internal_set_faulttype(::AllFaultEnum::OHPFaultEnum value);
  public:

  // .AllFaultEnum.AlarmStateEnum faultState = 2;
  void clear_faultstate();
  ::AllFaultEnum::AlarmStateEnum faultstate() const;
  void set_faultstate(::AllFaultEnum::AlarmStateEnum value);
  private:
  ::AllFaultEnum::AlarmStateEnum _internal_faultstate() const;
  void _internal_set_faultstate(::AllFaultEnum::AlarmStateEnum value);
  public:

  // uint64 faultRaiseTime = 3;
  void clear_faultraisetime();
  ::PROTOBUF_NAMESPACE_ID::uint64 faultraisetime() const;
  void set_faultraisetime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_faultraisetime() const;
  void _internal_set_faultraisetime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // uint64 faultDownTime = 4;
  void clear_faultdowntime();
  ::PROTOBUF_NAMESPACE_ID::uint64 faultdowntime() const;
  void set_faultdowntime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_faultdowntime() const;
  void _internal_set_faultdowntime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // uint32 ShutDownType = 5;
  void clear_shutdowntype();
  ::PROTOBUF_NAMESPACE_ID::uint32 shutdowntype() const;
  void set_shutdowntype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_shutdowntype() const;
  void _internal_set_shutdowntype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 ShowType = 6;
  void clear_showtype();
  ::PROTOBUF_NAMESPACE_ID::uint32 showtype() const;
  void set_showtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_showtype() const;
  void _internal_set_showtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:AllFaultEnum.OHPFaultState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int faulttype_;
  int faultstate_;
  ::PROTOBUF_NAMESPACE_ID::uint64 faultraisetime_;
  ::PROTOBUF_NAMESPACE_ID::uint64 faultdowntime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 shutdowntype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 showtype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fAllFaultEnum_2eproto;
};
// -------------------------------------------------------------------

class DMCFaultState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:AllFaultEnum.DMCFaultState) */ {
 public:
  inline DMCFaultState() : DMCFaultState(nullptr) {}
  ~DMCFaultState() override;
  explicit constexpr DMCFaultState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DMCFaultState(const DMCFaultState& from);
  DMCFaultState(DMCFaultState&& from) noexcept
    : DMCFaultState() {
    *this = ::std::move(from);
  }

  inline DMCFaultState& operator=(const DMCFaultState& from) {
    CopyFrom(from);
    return *this;
  }
  inline DMCFaultState& operator=(DMCFaultState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DMCFaultState& default_instance() {
    return *internal_default_instance();
  }
  static inline const DMCFaultState* internal_default_instance() {
    return reinterpret_cast<const DMCFaultState*>(
               &_DMCFaultState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(DMCFaultState& a, DMCFaultState& b) {
    a.Swap(&b);
  }
  inline void Swap(DMCFaultState* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DMCFaultState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DMCFaultState* New() const final {
    return new DMCFaultState();
  }

  DMCFaultState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DMCFaultState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DMCFaultState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DMCFaultState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DMCFaultState* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "AllFaultEnum.DMCFaultState";
  }
  protected:
  explicit DMCFaultState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFaultTypeFieldNumber = 1,
    kFaultStateFieldNumber = 2,
    kFaultRaiseTimeFieldNumber = 3,
    kFaultDownTimeFieldNumber = 4,
    kShutDownTypeFieldNumber = 5,
    kShowTypeFieldNumber = 6,
  };
  // .AllFaultEnum.DMCFaultEnum faultType = 1;
  void clear_faulttype();
  ::AllFaultEnum::DMCFaultEnum faulttype() const;
  void set_faulttype(::AllFaultEnum::DMCFaultEnum value);
  private:
  ::AllFaultEnum::DMCFaultEnum _internal_faulttype() const;
  void _internal_set_faulttype(::AllFaultEnum::DMCFaultEnum value);
  public:

  // .AllFaultEnum.AlarmStateEnum faultState = 2;
  void clear_faultstate();
  ::AllFaultEnum::AlarmStateEnum faultstate() const;
  void set_faultstate(::AllFaultEnum::AlarmStateEnum value);
  private:
  ::AllFaultEnum::AlarmStateEnum _internal_faultstate() const;
  void _internal_set_faultstate(::AllFaultEnum::AlarmStateEnum value);
  public:

  // uint64 faultRaiseTime = 3;
  void clear_faultraisetime();
  ::PROTOBUF_NAMESPACE_ID::uint64 faultraisetime() const;
  void set_faultraisetime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_faultraisetime() const;
  void _internal_set_faultraisetime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // uint64 faultDownTime = 4;
  void clear_faultdowntime();
  ::PROTOBUF_NAMESPACE_ID::uint64 faultdowntime() const;
  void set_faultdowntime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_faultdowntime() const;
  void _internal_set_faultdowntime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // uint32 ShutDownType = 5;
  void clear_shutdowntype();
  ::PROTOBUF_NAMESPACE_ID::uint32 shutdowntype() const;
  void set_shutdowntype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_shutdowntype() const;
  void _internal_set_shutdowntype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 ShowType = 6;
  void clear_showtype();
  ::PROTOBUF_NAMESPACE_ID::uint32 showtype() const;
  void set_showtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_showtype() const;
  void _internal_set_showtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:AllFaultEnum.DMCFaultState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int faulttype_;
  int faultstate_;
  ::PROTOBUF_NAMESPACE_ID::uint64 faultraisetime_;
  ::PROTOBUF_NAMESPACE_ID::uint64 faultdowntime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 shutdowntype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 showtype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fAllFaultEnum_2eproto;
};
// -------------------------------------------------------------------

class MatrixStatus final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:AllFaultEnum.MatrixStatus) */ {
 public:
  inline MatrixStatus() : MatrixStatus(nullptr) {}
  ~MatrixStatus() override;
  explicit constexpr MatrixStatus(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MatrixStatus(const MatrixStatus& from);
  MatrixStatus(MatrixStatus&& from) noexcept
    : MatrixStatus() {
    *this = ::std::move(from);
  }

  inline MatrixStatus& operator=(const MatrixStatus& from) {
    CopyFrom(from);
    return *this;
  }
  inline MatrixStatus& operator=(MatrixStatus&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MatrixStatus& default_instance() {
    return *internal_default_instance();
  }
  static inline const MatrixStatus* internal_default_instance() {
    return reinterpret_cast<const MatrixStatus*>(
               &_MatrixStatus_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(MatrixStatus& a, MatrixStatus& b) {
    a.Swap(&b);
  }
  inline void Swap(MatrixStatus* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MatrixStatus* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MatrixStatus* New() const final {
    return new MatrixStatus();
  }

  MatrixStatus* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MatrixStatus>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MatrixStatus& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MatrixStatus& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MatrixStatus* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "AllFaultEnum.MatrixStatus";
  }
  protected:
  explicit MatrixStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMatrixContactorIDFieldNumber = 1,
    kAlarmAnsListFieldNumber = 2,
    kAlarmAttrFieldNumber = 3,
    kRealFaultContIDFieldNumber = 4,
  };
  // uint32 MatrixContactorID = 1;
  void clear_matrixcontactorid();
  ::PROTOBUF_NAMESPACE_ID::uint32 matrixcontactorid() const;
  void set_matrixcontactorid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_matrixcontactorid() const;
  void _internal_set_matrixcontactorid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // .AllFaultEnum.PMMFaultEnum AlarmAnsList = 2;
  void clear_alarmanslist();
  ::AllFaultEnum::PMMFaultEnum alarmanslist() const;
  void set_alarmanslist(::AllFaultEnum::PMMFaultEnum value);
  private:
  ::AllFaultEnum::PMMFaultEnum _internal_alarmanslist() const;
  void _internal_set_alarmanslist(::AllFaultEnum::PMMFaultEnum value);
  public:

  // uint32 AlarmAttr = 3;
  void clear_alarmattr();
  ::PROTOBUF_NAMESPACE_ID::uint32 alarmattr() const;
  void set_alarmattr(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_alarmattr() const;
  void _internal_set_alarmattr(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 realFaultContID = 4;
  void clear_realfaultcontid();
  ::PROTOBUF_NAMESPACE_ID::uint32 realfaultcontid() const;
  void set_realfaultcontid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_realfaultcontid() const;
  void _internal_set_realfaultcontid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:AllFaultEnum.MatrixStatus)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 matrixcontactorid_;
  int alarmanslist_;
  ::PROTOBUF_NAMESPACE_ID::uint32 alarmattr_;
  ::PROTOBUF_NAMESPACE_ID::uint32 realfaultcontid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fAllFaultEnum_2eproto;
};
// -------------------------------------------------------------------

class ADModuleAlarm final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:AllFaultEnum.ADModuleAlarm) */ {
 public:
  inline ADModuleAlarm() : ADModuleAlarm(nullptr) {}
  ~ADModuleAlarm() override;
  explicit constexpr ADModuleAlarm(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ADModuleAlarm(const ADModuleAlarm& from);
  ADModuleAlarm(ADModuleAlarm&& from) noexcept
    : ADModuleAlarm() {
    *this = ::std::move(from);
  }

  inline ADModuleAlarm& operator=(const ADModuleAlarm& from) {
    CopyFrom(from);
    return *this;
  }
  inline ADModuleAlarm& operator=(ADModuleAlarm&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ADModuleAlarm& default_instance() {
    return *internal_default_instance();
  }
  static inline const ADModuleAlarm* internal_default_instance() {
    return reinterpret_cast<const ADModuleAlarm*>(
               &_ADModuleAlarm_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(ADModuleAlarm& a, ADModuleAlarm& b) {
    a.Swap(&b);
  }
  inline void Swap(ADModuleAlarm* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ADModuleAlarm* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ADModuleAlarm* New() const final {
    return new ADModuleAlarm();
  }

  ADModuleAlarm* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ADModuleAlarm>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ADModuleAlarm& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ADModuleAlarm& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ADModuleAlarm* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "AllFaultEnum.ADModuleAlarm";
  }
  protected:
  explicit ADModuleAlarm(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kADModuleListFieldNumber = 2,
    kADModuleIDFieldNumber = 1,
  };
  // repeated .AllFaultEnum.PMMFaultState ADModuleList = 2;
  int admodulelist_size() const;
  private:
  int _internal_admodulelist_size() const;
  public:
  void clear_admodulelist();
  ::AllFaultEnum::PMMFaultState* mutable_admodulelist(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::PMMFaultState >*
      mutable_admodulelist();
  private:
  const ::AllFaultEnum::PMMFaultState& _internal_admodulelist(int index) const;
  ::AllFaultEnum::PMMFaultState* _internal_add_admodulelist();
  public:
  const ::AllFaultEnum::PMMFaultState& admodulelist(int index) const;
  ::AllFaultEnum::PMMFaultState* add_admodulelist();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::PMMFaultState >&
      admodulelist() const;

  // uint32 ADModuleID = 1;
  void clear_admoduleid();
  ::PROTOBUF_NAMESPACE_ID::uint32 admoduleid() const;
  void set_admoduleid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_admoduleid() const;
  void _internal_set_admoduleid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:AllFaultEnum.ADModuleAlarm)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::PMMFaultState > admodulelist_;
  ::PROTOBUF_NAMESPACE_ID::uint32 admoduleid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fAllFaultEnum_2eproto;
};
// -------------------------------------------------------------------

class VCIContactorFaultSend final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:AllFaultEnum.VCIContactorFaultSend) */ {
 public:
  inline VCIContactorFaultSend() : VCIContactorFaultSend(nullptr) {}
  ~VCIContactorFaultSend() override;
  explicit constexpr VCIContactorFaultSend(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  VCIContactorFaultSend(const VCIContactorFaultSend& from);
  VCIContactorFaultSend(VCIContactorFaultSend&& from) noexcept
    : VCIContactorFaultSend() {
    *this = ::std::move(from);
  }

  inline VCIContactorFaultSend& operator=(const VCIContactorFaultSend& from) {
    CopyFrom(from);
    return *this;
  }
  inline VCIContactorFaultSend& operator=(VCIContactorFaultSend&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const VCIContactorFaultSend& default_instance() {
    return *internal_default_instance();
  }
  static inline const VCIContactorFaultSend* internal_default_instance() {
    return reinterpret_cast<const VCIContactorFaultSend*>(
               &_VCIContactorFaultSend_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(VCIContactorFaultSend& a, VCIContactorFaultSend& b) {
    a.Swap(&b);
  }
  inline void Swap(VCIContactorFaultSend* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VCIContactorFaultSend* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline VCIContactorFaultSend* New() const final {
    return new VCIContactorFaultSend();
  }

  VCIContactorFaultSend* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<VCIContactorFaultSend>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const VCIContactorFaultSend& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const VCIContactorFaultSend& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VCIContactorFaultSend* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "AllFaultEnum.VCIContactorFaultSend";
  }
  protected:
  explicit VCIContactorFaultSend(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kVCIFaultFieldNumber = 2,
    kGunNumFieldNumber = 1,
  };
  // repeated .AllFaultEnum.VCIFaultState VCIFault = 2;
  int vcifault_size() const;
  private:
  int _internal_vcifault_size() const;
  public:
  void clear_vcifault();
  ::AllFaultEnum::VCIFaultState* mutable_vcifault(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::VCIFaultState >*
      mutable_vcifault();
  private:
  const ::AllFaultEnum::VCIFaultState& _internal_vcifault(int index) const;
  ::AllFaultEnum::VCIFaultState* _internal_add_vcifault();
  public:
  const ::AllFaultEnum::VCIFaultState& vcifault(int index) const;
  ::AllFaultEnum::VCIFaultState* add_vcifault();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::VCIFaultState >&
      vcifault() const;

  // uint32 gunNum = 1;
  void clear_gunnum();
  ::PROTOBUF_NAMESPACE_ID::uint32 gunnum() const;
  void set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_gunnum() const;
  void _internal_set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:AllFaultEnum.VCIContactorFaultSend)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::VCIFaultState > vcifault_;
  ::PROTOBUF_NAMESPACE_ID::uint32 gunnum_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fAllFaultEnum_2eproto;
};
// -------------------------------------------------------------------

class PMMContactorFaultSend final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:AllFaultEnum.PMMContactorFaultSend) */ {
 public:
  inline PMMContactorFaultSend() : PMMContactorFaultSend(nullptr) {}
  ~PMMContactorFaultSend() override;
  explicit constexpr PMMContactorFaultSend(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PMMContactorFaultSend(const PMMContactorFaultSend& from);
  PMMContactorFaultSend(PMMContactorFaultSend&& from) noexcept
    : PMMContactorFaultSend() {
    *this = ::std::move(from);
  }

  inline PMMContactorFaultSend& operator=(const PMMContactorFaultSend& from) {
    CopyFrom(from);
    return *this;
  }
  inline PMMContactorFaultSend& operator=(PMMContactorFaultSend&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PMMContactorFaultSend& default_instance() {
    return *internal_default_instance();
  }
  static inline const PMMContactorFaultSend* internal_default_instance() {
    return reinterpret_cast<const PMMContactorFaultSend*>(
               &_PMMContactorFaultSend_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(PMMContactorFaultSend& a, PMMContactorFaultSend& b) {
    a.Swap(&b);
  }
  inline void Swap(PMMContactorFaultSend* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PMMContactorFaultSend* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline PMMContactorFaultSend* New() const final {
    return new PMMContactorFaultSend();
  }

  PMMContactorFaultSend* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<PMMContactorFaultSend>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PMMContactorFaultSend& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PMMContactorFaultSend& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PMMContactorFaultSend* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "AllFaultEnum.PMMContactorFaultSend";
  }
  protected:
  explicit PMMContactorFaultSend(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPMMFaultFieldNumber = 2,
    kGunNumFieldNumber = 1,
  };
  // repeated .AllFaultEnum.PMMFaultState PMMFault = 2;
  int pmmfault_size() const;
  private:
  int _internal_pmmfault_size() const;
  public:
  void clear_pmmfault();
  ::AllFaultEnum::PMMFaultState* mutable_pmmfault(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::PMMFaultState >*
      mutable_pmmfault();
  private:
  const ::AllFaultEnum::PMMFaultState& _internal_pmmfault(int index) const;
  ::AllFaultEnum::PMMFaultState* _internal_add_pmmfault();
  public:
  const ::AllFaultEnum::PMMFaultState& pmmfault(int index) const;
  ::AllFaultEnum::PMMFaultState* add_pmmfault();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::PMMFaultState >&
      pmmfault() const;

  // uint32 gunNum = 1;
  void clear_gunnum();
  ::PROTOBUF_NAMESPACE_ID::uint32 gunnum() const;
  void set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_gunnum() const;
  void _internal_set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:AllFaultEnum.PMMContactorFaultSend)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::PMMFaultState > pmmfault_;
  ::PROTOBUF_NAMESPACE_ID::uint32 gunnum_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fAllFaultEnum_2eproto;
};
// -------------------------------------------------------------------

class OHPContactorFaultSend final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:AllFaultEnum.OHPContactorFaultSend) */ {
 public:
  inline OHPContactorFaultSend() : OHPContactorFaultSend(nullptr) {}
  ~OHPContactorFaultSend() override;
  explicit constexpr OHPContactorFaultSend(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OHPContactorFaultSend(const OHPContactorFaultSend& from);
  OHPContactorFaultSend(OHPContactorFaultSend&& from) noexcept
    : OHPContactorFaultSend() {
    *this = ::std::move(from);
  }

  inline OHPContactorFaultSend& operator=(const OHPContactorFaultSend& from) {
    CopyFrom(from);
    return *this;
  }
  inline OHPContactorFaultSend& operator=(OHPContactorFaultSend&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OHPContactorFaultSend& default_instance() {
    return *internal_default_instance();
  }
  static inline const OHPContactorFaultSend* internal_default_instance() {
    return reinterpret_cast<const OHPContactorFaultSend*>(
               &_OHPContactorFaultSend_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(OHPContactorFaultSend& a, OHPContactorFaultSend& b) {
    a.Swap(&b);
  }
  inline void Swap(OHPContactorFaultSend* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OHPContactorFaultSend* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OHPContactorFaultSend* New() const final {
    return new OHPContactorFaultSend();
  }

  OHPContactorFaultSend* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OHPContactorFaultSend>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OHPContactorFaultSend& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const OHPContactorFaultSend& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OHPContactorFaultSend* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "AllFaultEnum.OHPContactorFaultSend";
  }
  protected:
  explicit OHPContactorFaultSend(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOHPFaultFieldNumber = 2,
    kGunNumFieldNumber = 1,
  };
  // repeated .AllFaultEnum.OHPFaultState OHPFault = 2;
  int ohpfault_size() const;
  private:
  int _internal_ohpfault_size() const;
  public:
  void clear_ohpfault();
  ::AllFaultEnum::OHPFaultState* mutable_ohpfault(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::OHPFaultState >*
      mutable_ohpfault();
  private:
  const ::AllFaultEnum::OHPFaultState& _internal_ohpfault(int index) const;
  ::AllFaultEnum::OHPFaultState* _internal_add_ohpfault();
  public:
  const ::AllFaultEnum::OHPFaultState& ohpfault(int index) const;
  ::AllFaultEnum::OHPFaultState* add_ohpfault();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::OHPFaultState >&
      ohpfault() const;

  // uint32 gunNum = 1;
  void clear_gunnum();
  ::PROTOBUF_NAMESPACE_ID::uint32 gunnum() const;
  void set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_gunnum() const;
  void _internal_set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:AllFaultEnum.OHPContactorFaultSend)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::OHPFaultState > ohpfault_;
  ::PROTOBUF_NAMESPACE_ID::uint32 gunnum_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fAllFaultEnum_2eproto;
};
// -------------------------------------------------------------------

class DMCContactorFaultSend final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:AllFaultEnum.DMCContactorFaultSend) */ {
 public:
  inline DMCContactorFaultSend() : DMCContactorFaultSend(nullptr) {}
  ~DMCContactorFaultSend() override;
  explicit constexpr DMCContactorFaultSend(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DMCContactorFaultSend(const DMCContactorFaultSend& from);
  DMCContactorFaultSend(DMCContactorFaultSend&& from) noexcept
    : DMCContactorFaultSend() {
    *this = ::std::move(from);
  }

  inline DMCContactorFaultSend& operator=(const DMCContactorFaultSend& from) {
    CopyFrom(from);
    return *this;
  }
  inline DMCContactorFaultSend& operator=(DMCContactorFaultSend&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DMCContactorFaultSend& default_instance() {
    return *internal_default_instance();
  }
  static inline const DMCContactorFaultSend* internal_default_instance() {
    return reinterpret_cast<const DMCContactorFaultSend*>(
               &_DMCContactorFaultSend_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(DMCContactorFaultSend& a, DMCContactorFaultSend& b) {
    a.Swap(&b);
  }
  inline void Swap(DMCContactorFaultSend* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DMCContactorFaultSend* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DMCContactorFaultSend* New() const final {
    return new DMCContactorFaultSend();
  }

  DMCContactorFaultSend* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DMCContactorFaultSend>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DMCContactorFaultSend& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DMCContactorFaultSend& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DMCContactorFaultSend* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "AllFaultEnum.DMCContactorFaultSend";
  }
  protected:
  explicit DMCContactorFaultSend(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDMCFaultFieldNumber = 2,
    kGunNumFieldNumber = 1,
  };
  // repeated .AllFaultEnum.DMCFaultState DMCFault = 2;
  int dmcfault_size() const;
  private:
  int _internal_dmcfault_size() const;
  public:
  void clear_dmcfault();
  ::AllFaultEnum::DMCFaultState* mutable_dmcfault(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::DMCFaultState >*
      mutable_dmcfault();
  private:
  const ::AllFaultEnum::DMCFaultState& _internal_dmcfault(int index) const;
  ::AllFaultEnum::DMCFaultState* _internal_add_dmcfault();
  public:
  const ::AllFaultEnum::DMCFaultState& dmcfault(int index) const;
  ::AllFaultEnum::DMCFaultState* add_dmcfault();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::DMCFaultState >&
      dmcfault() const;

  // uint32 gunNum = 1;
  void clear_gunnum();
  ::PROTOBUF_NAMESPACE_ID::uint32 gunnum() const;
  void set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_gunnum() const;
  void _internal_set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:AllFaultEnum.DMCContactorFaultSend)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::DMCFaultState > dmcfault_;
  ::PROTOBUF_NAMESPACE_ID::uint32 gunnum_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fAllFaultEnum_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// VCIFaultState

// .AllFaultEnum.VCIFaultEnum faultType = 1;
inline void VCIFaultState::clear_faulttype() {
  faulttype_ = 0;
}
inline ::AllFaultEnum::VCIFaultEnum VCIFaultState::_internal_faulttype() const {
  return static_cast< ::AllFaultEnum::VCIFaultEnum >(faulttype_);
}
inline ::AllFaultEnum::VCIFaultEnum VCIFaultState::faulttype() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.VCIFaultState.faultType)
  return _internal_faulttype();
}
inline void VCIFaultState::_internal_set_faulttype(::AllFaultEnum::VCIFaultEnum value) {
  
  faulttype_ = value;
}
inline void VCIFaultState::set_faulttype(::AllFaultEnum::VCIFaultEnum value) {
  _internal_set_faulttype(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.VCIFaultState.faultType)
}

// .AllFaultEnum.AlarmStateEnum faultState = 2;
inline void VCIFaultState::clear_faultstate() {
  faultstate_ = 0;
}
inline ::AllFaultEnum::AlarmStateEnum VCIFaultState::_internal_faultstate() const {
  return static_cast< ::AllFaultEnum::AlarmStateEnum >(faultstate_);
}
inline ::AllFaultEnum::AlarmStateEnum VCIFaultState::faultstate() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.VCIFaultState.faultState)
  return _internal_faultstate();
}
inline void VCIFaultState::_internal_set_faultstate(::AllFaultEnum::AlarmStateEnum value) {
  
  faultstate_ = value;
}
inline void VCIFaultState::set_faultstate(::AllFaultEnum::AlarmStateEnum value) {
  _internal_set_faultstate(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.VCIFaultState.faultState)
}

// uint64 faultRaiseTime = 3;
inline void VCIFaultState::clear_faultraisetime() {
  faultraisetime_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 VCIFaultState::_internal_faultraisetime() const {
  return faultraisetime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 VCIFaultState::faultraisetime() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.VCIFaultState.faultRaiseTime)
  return _internal_faultraisetime();
}
inline void VCIFaultState::_internal_set_faultraisetime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  faultraisetime_ = value;
}
inline void VCIFaultState::set_faultraisetime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_faultraisetime(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.VCIFaultState.faultRaiseTime)
}

// uint64 faultDownTime = 4;
inline void VCIFaultState::clear_faultdowntime() {
  faultdowntime_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 VCIFaultState::_internal_faultdowntime() const {
  return faultdowntime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 VCIFaultState::faultdowntime() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.VCIFaultState.faultDownTime)
  return _internal_faultdowntime();
}
inline void VCIFaultState::_internal_set_faultdowntime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  faultdowntime_ = value;
}
inline void VCIFaultState::set_faultdowntime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_faultdowntime(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.VCIFaultState.faultDownTime)
}

// uint32 ShutDownType = 5;
inline void VCIFaultState::clear_shutdowntype() {
  shutdowntype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIFaultState::_internal_shutdowntype() const {
  return shutdowntype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIFaultState::shutdowntype() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.VCIFaultState.ShutDownType)
  return _internal_shutdowntype();
}
inline void VCIFaultState::_internal_set_shutdowntype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  shutdowntype_ = value;
}
inline void VCIFaultState::set_shutdowntype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_shutdowntype(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.VCIFaultState.ShutDownType)
}

// uint32 ShowType = 6;
inline void VCIFaultState::clear_showtype() {
  showtype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIFaultState::_internal_showtype() const {
  return showtype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIFaultState::showtype() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.VCIFaultState.ShowType)
  return _internal_showtype();
}
inline void VCIFaultState::_internal_set_showtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  showtype_ = value;
}
inline void VCIFaultState::set_showtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_showtype(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.VCIFaultState.ShowType)
}

// -------------------------------------------------------------------

// PMMFaultState

// .AllFaultEnum.PMMFaultEnum faultType = 1;
inline void PMMFaultState::clear_faulttype() {
  faulttype_ = 0;
}
inline ::AllFaultEnum::PMMFaultEnum PMMFaultState::_internal_faulttype() const {
  return static_cast< ::AllFaultEnum::PMMFaultEnum >(faulttype_);
}
inline ::AllFaultEnum::PMMFaultEnum PMMFaultState::faulttype() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.PMMFaultState.faultType)
  return _internal_faulttype();
}
inline void PMMFaultState::_internal_set_faulttype(::AllFaultEnum::PMMFaultEnum value) {
  
  faulttype_ = value;
}
inline void PMMFaultState::set_faulttype(::AllFaultEnum::PMMFaultEnum value) {
  _internal_set_faulttype(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.PMMFaultState.faultType)
}

// .AllFaultEnum.AlarmStateEnum faultState = 2;
inline void PMMFaultState::clear_faultstate() {
  faultstate_ = 0;
}
inline ::AllFaultEnum::AlarmStateEnum PMMFaultState::_internal_faultstate() const {
  return static_cast< ::AllFaultEnum::AlarmStateEnum >(faultstate_);
}
inline ::AllFaultEnum::AlarmStateEnum PMMFaultState::faultstate() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.PMMFaultState.faultState)
  return _internal_faultstate();
}
inline void PMMFaultState::_internal_set_faultstate(::AllFaultEnum::AlarmStateEnum value) {
  
  faultstate_ = value;
}
inline void PMMFaultState::set_faultstate(::AllFaultEnum::AlarmStateEnum value) {
  _internal_set_faultstate(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.PMMFaultState.faultState)
}

// uint64 faultRaiseTime = 3;
inline void PMMFaultState::clear_faultraisetime() {
  faultraisetime_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 PMMFaultState::_internal_faultraisetime() const {
  return faultraisetime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 PMMFaultState::faultraisetime() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.PMMFaultState.faultRaiseTime)
  return _internal_faultraisetime();
}
inline void PMMFaultState::_internal_set_faultraisetime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  faultraisetime_ = value;
}
inline void PMMFaultState::set_faultraisetime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_faultraisetime(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.PMMFaultState.faultRaiseTime)
}

// uint64 faultDownTime = 4;
inline void PMMFaultState::clear_faultdowntime() {
  faultdowntime_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 PMMFaultState::_internal_faultdowntime() const {
  return faultdowntime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 PMMFaultState::faultdowntime() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.PMMFaultState.faultDownTime)
  return _internal_faultdowntime();
}
inline void PMMFaultState::_internal_set_faultdowntime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  faultdowntime_ = value;
}
inline void PMMFaultState::set_faultdowntime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_faultdowntime(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.PMMFaultState.faultDownTime)
}

// uint32 ShutDownType = 5;
inline void PMMFaultState::clear_shutdowntype() {
  shutdowntype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMFaultState::_internal_shutdowntype() const {
  return shutdowntype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMFaultState::shutdowntype() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.PMMFaultState.ShutDownType)
  return _internal_shutdowntype();
}
inline void PMMFaultState::_internal_set_shutdowntype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  shutdowntype_ = value;
}
inline void PMMFaultState::set_shutdowntype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_shutdowntype(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.PMMFaultState.ShutDownType)
}

// uint32 ShowType = 6;
inline void PMMFaultState::clear_showtype() {
  showtype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMFaultState::_internal_showtype() const {
  return showtype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMFaultState::showtype() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.PMMFaultState.ShowType)
  return _internal_showtype();
}
inline void PMMFaultState::_internal_set_showtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  showtype_ = value;
}
inline void PMMFaultState::set_showtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_showtype(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.PMMFaultState.ShowType)
}

// -------------------------------------------------------------------

// OHPFaultState

// .AllFaultEnum.OHPFaultEnum faultType = 1;
inline void OHPFaultState::clear_faulttype() {
  faulttype_ = 0;
}
inline ::AllFaultEnum::OHPFaultEnum OHPFaultState::_internal_faulttype() const {
  return static_cast< ::AllFaultEnum::OHPFaultEnum >(faulttype_);
}
inline ::AllFaultEnum::OHPFaultEnum OHPFaultState::faulttype() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.OHPFaultState.faultType)
  return _internal_faulttype();
}
inline void OHPFaultState::_internal_set_faulttype(::AllFaultEnum::OHPFaultEnum value) {
  
  faulttype_ = value;
}
inline void OHPFaultState::set_faulttype(::AllFaultEnum::OHPFaultEnum value) {
  _internal_set_faulttype(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.OHPFaultState.faultType)
}

// .AllFaultEnum.AlarmStateEnum faultState = 2;
inline void OHPFaultState::clear_faultstate() {
  faultstate_ = 0;
}
inline ::AllFaultEnum::AlarmStateEnum OHPFaultState::_internal_faultstate() const {
  return static_cast< ::AllFaultEnum::AlarmStateEnum >(faultstate_);
}
inline ::AllFaultEnum::AlarmStateEnum OHPFaultState::faultstate() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.OHPFaultState.faultState)
  return _internal_faultstate();
}
inline void OHPFaultState::_internal_set_faultstate(::AllFaultEnum::AlarmStateEnum value) {
  
  faultstate_ = value;
}
inline void OHPFaultState::set_faultstate(::AllFaultEnum::AlarmStateEnum value) {
  _internal_set_faultstate(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.OHPFaultState.faultState)
}

// uint64 faultRaiseTime = 3;
inline void OHPFaultState::clear_faultraisetime() {
  faultraisetime_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 OHPFaultState::_internal_faultraisetime() const {
  return faultraisetime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 OHPFaultState::faultraisetime() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.OHPFaultState.faultRaiseTime)
  return _internal_faultraisetime();
}
inline void OHPFaultState::_internal_set_faultraisetime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  faultraisetime_ = value;
}
inline void OHPFaultState::set_faultraisetime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_faultraisetime(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.OHPFaultState.faultRaiseTime)
}

// uint64 faultDownTime = 4;
inline void OHPFaultState::clear_faultdowntime() {
  faultdowntime_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 OHPFaultState::_internal_faultdowntime() const {
  return faultdowntime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 OHPFaultState::faultdowntime() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.OHPFaultState.faultDownTime)
  return _internal_faultdowntime();
}
inline void OHPFaultState::_internal_set_faultdowntime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  faultdowntime_ = value;
}
inline void OHPFaultState::set_faultdowntime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_faultdowntime(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.OHPFaultState.faultDownTime)
}

// uint32 ShutDownType = 5;
inline void OHPFaultState::clear_shutdowntype() {
  shutdowntype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPFaultState::_internal_shutdowntype() const {
  return shutdowntype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPFaultState::shutdowntype() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.OHPFaultState.ShutDownType)
  return _internal_shutdowntype();
}
inline void OHPFaultState::_internal_set_shutdowntype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  shutdowntype_ = value;
}
inline void OHPFaultState::set_shutdowntype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_shutdowntype(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.OHPFaultState.ShutDownType)
}

// uint32 ShowType = 6;
inline void OHPFaultState::clear_showtype() {
  showtype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPFaultState::_internal_showtype() const {
  return showtype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPFaultState::showtype() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.OHPFaultState.ShowType)
  return _internal_showtype();
}
inline void OHPFaultState::_internal_set_showtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  showtype_ = value;
}
inline void OHPFaultState::set_showtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_showtype(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.OHPFaultState.ShowType)
}

// -------------------------------------------------------------------

// DMCFaultState

// .AllFaultEnum.DMCFaultEnum faultType = 1;
inline void DMCFaultState::clear_faulttype() {
  faulttype_ = 0;
}
inline ::AllFaultEnum::DMCFaultEnum DMCFaultState::_internal_faulttype() const {
  return static_cast< ::AllFaultEnum::DMCFaultEnum >(faulttype_);
}
inline ::AllFaultEnum::DMCFaultEnum DMCFaultState::faulttype() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.DMCFaultState.faultType)
  return _internal_faulttype();
}
inline void DMCFaultState::_internal_set_faulttype(::AllFaultEnum::DMCFaultEnum value) {
  
  faulttype_ = value;
}
inline void DMCFaultState::set_faulttype(::AllFaultEnum::DMCFaultEnum value) {
  _internal_set_faulttype(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.DMCFaultState.faultType)
}

// .AllFaultEnum.AlarmStateEnum faultState = 2;
inline void DMCFaultState::clear_faultstate() {
  faultstate_ = 0;
}
inline ::AllFaultEnum::AlarmStateEnum DMCFaultState::_internal_faultstate() const {
  return static_cast< ::AllFaultEnum::AlarmStateEnum >(faultstate_);
}
inline ::AllFaultEnum::AlarmStateEnum DMCFaultState::faultstate() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.DMCFaultState.faultState)
  return _internal_faultstate();
}
inline void DMCFaultState::_internal_set_faultstate(::AllFaultEnum::AlarmStateEnum value) {
  
  faultstate_ = value;
}
inline void DMCFaultState::set_faultstate(::AllFaultEnum::AlarmStateEnum value) {
  _internal_set_faultstate(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.DMCFaultState.faultState)
}

// uint64 faultRaiseTime = 3;
inline void DMCFaultState::clear_faultraisetime() {
  faultraisetime_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 DMCFaultState::_internal_faultraisetime() const {
  return faultraisetime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 DMCFaultState::faultraisetime() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.DMCFaultState.faultRaiseTime)
  return _internal_faultraisetime();
}
inline void DMCFaultState::_internal_set_faultraisetime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  faultraisetime_ = value;
}
inline void DMCFaultState::set_faultraisetime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_faultraisetime(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.DMCFaultState.faultRaiseTime)
}

// uint64 faultDownTime = 4;
inline void DMCFaultState::clear_faultdowntime() {
  faultdowntime_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 DMCFaultState::_internal_faultdowntime() const {
  return faultdowntime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 DMCFaultState::faultdowntime() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.DMCFaultState.faultDownTime)
  return _internal_faultdowntime();
}
inline void DMCFaultState::_internal_set_faultdowntime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  faultdowntime_ = value;
}
inline void DMCFaultState::set_faultdowntime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_faultdowntime(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.DMCFaultState.faultDownTime)
}

// uint32 ShutDownType = 5;
inline void DMCFaultState::clear_shutdowntype() {
  shutdowntype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 DMCFaultState::_internal_shutdowntype() const {
  return shutdowntype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 DMCFaultState::shutdowntype() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.DMCFaultState.ShutDownType)
  return _internal_shutdowntype();
}
inline void DMCFaultState::_internal_set_shutdowntype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  shutdowntype_ = value;
}
inline void DMCFaultState::set_shutdowntype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_shutdowntype(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.DMCFaultState.ShutDownType)
}

// uint32 ShowType = 6;
inline void DMCFaultState::clear_showtype() {
  showtype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 DMCFaultState::_internal_showtype() const {
  return showtype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 DMCFaultState::showtype() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.DMCFaultState.ShowType)
  return _internal_showtype();
}
inline void DMCFaultState::_internal_set_showtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  showtype_ = value;
}
inline void DMCFaultState::set_showtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_showtype(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.DMCFaultState.ShowType)
}

// -------------------------------------------------------------------

// MatrixStatus

// uint32 MatrixContactorID = 1;
inline void MatrixStatus::clear_matrixcontactorid() {
  matrixcontactorid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 MatrixStatus::_internal_matrixcontactorid() const {
  return matrixcontactorid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 MatrixStatus::matrixcontactorid() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.MatrixStatus.MatrixContactorID)
  return _internal_matrixcontactorid();
}
inline void MatrixStatus::_internal_set_matrixcontactorid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  matrixcontactorid_ = value;
}
inline void MatrixStatus::set_matrixcontactorid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_matrixcontactorid(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.MatrixStatus.MatrixContactorID)
}

// .AllFaultEnum.PMMFaultEnum AlarmAnsList = 2;
inline void MatrixStatus::clear_alarmanslist() {
  alarmanslist_ = 0;
}
inline ::AllFaultEnum::PMMFaultEnum MatrixStatus::_internal_alarmanslist() const {
  return static_cast< ::AllFaultEnum::PMMFaultEnum >(alarmanslist_);
}
inline ::AllFaultEnum::PMMFaultEnum MatrixStatus::alarmanslist() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.MatrixStatus.AlarmAnsList)
  return _internal_alarmanslist();
}
inline void MatrixStatus::_internal_set_alarmanslist(::AllFaultEnum::PMMFaultEnum value) {
  
  alarmanslist_ = value;
}
inline void MatrixStatus::set_alarmanslist(::AllFaultEnum::PMMFaultEnum value) {
  _internal_set_alarmanslist(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.MatrixStatus.AlarmAnsList)
}

// uint32 AlarmAttr = 3;
inline void MatrixStatus::clear_alarmattr() {
  alarmattr_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 MatrixStatus::_internal_alarmattr() const {
  return alarmattr_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 MatrixStatus::alarmattr() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.MatrixStatus.AlarmAttr)
  return _internal_alarmattr();
}
inline void MatrixStatus::_internal_set_alarmattr(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  alarmattr_ = value;
}
inline void MatrixStatus::set_alarmattr(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_alarmattr(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.MatrixStatus.AlarmAttr)
}

// uint32 realFaultContID = 4;
inline void MatrixStatus::clear_realfaultcontid() {
  realfaultcontid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 MatrixStatus::_internal_realfaultcontid() const {
  return realfaultcontid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 MatrixStatus::realfaultcontid() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.MatrixStatus.realFaultContID)
  return _internal_realfaultcontid();
}
inline void MatrixStatus::_internal_set_realfaultcontid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  realfaultcontid_ = value;
}
inline void MatrixStatus::set_realfaultcontid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_realfaultcontid(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.MatrixStatus.realFaultContID)
}

// -------------------------------------------------------------------

// ADModuleAlarm

// uint32 ADModuleID = 1;
inline void ADModuleAlarm::clear_admoduleid() {
  admoduleid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ADModuleAlarm::_internal_admoduleid() const {
  return admoduleid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ADModuleAlarm::admoduleid() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.ADModuleAlarm.ADModuleID)
  return _internal_admoduleid();
}
inline void ADModuleAlarm::_internal_set_admoduleid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  admoduleid_ = value;
}
inline void ADModuleAlarm::set_admoduleid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_admoduleid(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.ADModuleAlarm.ADModuleID)
}

// repeated .AllFaultEnum.PMMFaultState ADModuleList = 2;
inline int ADModuleAlarm::_internal_admodulelist_size() const {
  return admodulelist_.size();
}
inline int ADModuleAlarm::admodulelist_size() const {
  return _internal_admodulelist_size();
}
inline void ADModuleAlarm::clear_admodulelist() {
  admodulelist_.Clear();
}
inline ::AllFaultEnum::PMMFaultState* ADModuleAlarm::mutable_admodulelist(int index) {
  // @@protoc_insertion_point(field_mutable:AllFaultEnum.ADModuleAlarm.ADModuleList)
  return admodulelist_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::PMMFaultState >*
ADModuleAlarm::mutable_admodulelist() {
  // @@protoc_insertion_point(field_mutable_list:AllFaultEnum.ADModuleAlarm.ADModuleList)
  return &admodulelist_;
}
inline const ::AllFaultEnum::PMMFaultState& ADModuleAlarm::_internal_admodulelist(int index) const {
  return admodulelist_.Get(index);
}
inline const ::AllFaultEnum::PMMFaultState& ADModuleAlarm::admodulelist(int index) const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.ADModuleAlarm.ADModuleList)
  return _internal_admodulelist(index);
}
inline ::AllFaultEnum::PMMFaultState* ADModuleAlarm::_internal_add_admodulelist() {
  return admodulelist_.Add();
}
inline ::AllFaultEnum::PMMFaultState* ADModuleAlarm::add_admodulelist() {
  ::AllFaultEnum::PMMFaultState* _add = _internal_add_admodulelist();
  // @@protoc_insertion_point(field_add:AllFaultEnum.ADModuleAlarm.ADModuleList)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::PMMFaultState >&
ADModuleAlarm::admodulelist() const {
  // @@protoc_insertion_point(field_list:AllFaultEnum.ADModuleAlarm.ADModuleList)
  return admodulelist_;
}

// -------------------------------------------------------------------

// VCIContactorFaultSend

// uint32 gunNum = 1;
inline void VCIContactorFaultSend::clear_gunnum() {
  gunnum_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIContactorFaultSend::_internal_gunnum() const {
  return gunnum_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIContactorFaultSend::gunnum() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.VCIContactorFaultSend.gunNum)
  return _internal_gunnum();
}
inline void VCIContactorFaultSend::_internal_set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  gunnum_ = value;
}
inline void VCIContactorFaultSend::set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_gunnum(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.VCIContactorFaultSend.gunNum)
}

// repeated .AllFaultEnum.VCIFaultState VCIFault = 2;
inline int VCIContactorFaultSend::_internal_vcifault_size() const {
  return vcifault_.size();
}
inline int VCIContactorFaultSend::vcifault_size() const {
  return _internal_vcifault_size();
}
inline void VCIContactorFaultSend::clear_vcifault() {
  vcifault_.Clear();
}
inline ::AllFaultEnum::VCIFaultState* VCIContactorFaultSend::mutable_vcifault(int index) {
  // @@protoc_insertion_point(field_mutable:AllFaultEnum.VCIContactorFaultSend.VCIFault)
  return vcifault_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::VCIFaultState >*
VCIContactorFaultSend::mutable_vcifault() {
  // @@protoc_insertion_point(field_mutable_list:AllFaultEnum.VCIContactorFaultSend.VCIFault)
  return &vcifault_;
}
inline const ::AllFaultEnum::VCIFaultState& VCIContactorFaultSend::_internal_vcifault(int index) const {
  return vcifault_.Get(index);
}
inline const ::AllFaultEnum::VCIFaultState& VCIContactorFaultSend::vcifault(int index) const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.VCIContactorFaultSend.VCIFault)
  return _internal_vcifault(index);
}
inline ::AllFaultEnum::VCIFaultState* VCIContactorFaultSend::_internal_add_vcifault() {
  return vcifault_.Add();
}
inline ::AllFaultEnum::VCIFaultState* VCIContactorFaultSend::add_vcifault() {
  ::AllFaultEnum::VCIFaultState* _add = _internal_add_vcifault();
  // @@protoc_insertion_point(field_add:AllFaultEnum.VCIContactorFaultSend.VCIFault)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::VCIFaultState >&
VCIContactorFaultSend::vcifault() const {
  // @@protoc_insertion_point(field_list:AllFaultEnum.VCIContactorFaultSend.VCIFault)
  return vcifault_;
}

// -------------------------------------------------------------------

// PMMContactorFaultSend

// uint32 gunNum = 1;
inline void PMMContactorFaultSend::clear_gunnum() {
  gunnum_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMContactorFaultSend::_internal_gunnum() const {
  return gunnum_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMContactorFaultSend::gunnum() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.PMMContactorFaultSend.gunNum)
  return _internal_gunnum();
}
inline void PMMContactorFaultSend::_internal_set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  gunnum_ = value;
}
inline void PMMContactorFaultSend::set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_gunnum(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.PMMContactorFaultSend.gunNum)
}

// repeated .AllFaultEnum.PMMFaultState PMMFault = 2;
inline int PMMContactorFaultSend::_internal_pmmfault_size() const {
  return pmmfault_.size();
}
inline int PMMContactorFaultSend::pmmfault_size() const {
  return _internal_pmmfault_size();
}
inline void PMMContactorFaultSend::clear_pmmfault() {
  pmmfault_.Clear();
}
inline ::AllFaultEnum::PMMFaultState* PMMContactorFaultSend::mutable_pmmfault(int index) {
  // @@protoc_insertion_point(field_mutable:AllFaultEnum.PMMContactorFaultSend.PMMFault)
  return pmmfault_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::PMMFaultState >*
PMMContactorFaultSend::mutable_pmmfault() {
  // @@protoc_insertion_point(field_mutable_list:AllFaultEnum.PMMContactorFaultSend.PMMFault)
  return &pmmfault_;
}
inline const ::AllFaultEnum::PMMFaultState& PMMContactorFaultSend::_internal_pmmfault(int index) const {
  return pmmfault_.Get(index);
}
inline const ::AllFaultEnum::PMMFaultState& PMMContactorFaultSend::pmmfault(int index) const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.PMMContactorFaultSend.PMMFault)
  return _internal_pmmfault(index);
}
inline ::AllFaultEnum::PMMFaultState* PMMContactorFaultSend::_internal_add_pmmfault() {
  return pmmfault_.Add();
}
inline ::AllFaultEnum::PMMFaultState* PMMContactorFaultSend::add_pmmfault() {
  ::AllFaultEnum::PMMFaultState* _add = _internal_add_pmmfault();
  // @@protoc_insertion_point(field_add:AllFaultEnum.PMMContactorFaultSend.PMMFault)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::PMMFaultState >&
PMMContactorFaultSend::pmmfault() const {
  // @@protoc_insertion_point(field_list:AllFaultEnum.PMMContactorFaultSend.PMMFault)
  return pmmfault_;
}

// -------------------------------------------------------------------

// OHPContactorFaultSend

// uint32 gunNum = 1;
inline void OHPContactorFaultSend::clear_gunnum() {
  gunnum_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPContactorFaultSend::_internal_gunnum() const {
  return gunnum_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPContactorFaultSend::gunnum() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.OHPContactorFaultSend.gunNum)
  return _internal_gunnum();
}
inline void OHPContactorFaultSend::_internal_set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  gunnum_ = value;
}
inline void OHPContactorFaultSend::set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_gunnum(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.OHPContactorFaultSend.gunNum)
}

// repeated .AllFaultEnum.OHPFaultState OHPFault = 2;
inline int OHPContactorFaultSend::_internal_ohpfault_size() const {
  return ohpfault_.size();
}
inline int OHPContactorFaultSend::ohpfault_size() const {
  return _internal_ohpfault_size();
}
inline void OHPContactorFaultSend::clear_ohpfault() {
  ohpfault_.Clear();
}
inline ::AllFaultEnum::OHPFaultState* OHPContactorFaultSend::mutable_ohpfault(int index) {
  // @@protoc_insertion_point(field_mutable:AllFaultEnum.OHPContactorFaultSend.OHPFault)
  return ohpfault_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::OHPFaultState >*
OHPContactorFaultSend::mutable_ohpfault() {
  // @@protoc_insertion_point(field_mutable_list:AllFaultEnum.OHPContactorFaultSend.OHPFault)
  return &ohpfault_;
}
inline const ::AllFaultEnum::OHPFaultState& OHPContactorFaultSend::_internal_ohpfault(int index) const {
  return ohpfault_.Get(index);
}
inline const ::AllFaultEnum::OHPFaultState& OHPContactorFaultSend::ohpfault(int index) const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.OHPContactorFaultSend.OHPFault)
  return _internal_ohpfault(index);
}
inline ::AllFaultEnum::OHPFaultState* OHPContactorFaultSend::_internal_add_ohpfault() {
  return ohpfault_.Add();
}
inline ::AllFaultEnum::OHPFaultState* OHPContactorFaultSend::add_ohpfault() {
  ::AllFaultEnum::OHPFaultState* _add = _internal_add_ohpfault();
  // @@protoc_insertion_point(field_add:AllFaultEnum.OHPContactorFaultSend.OHPFault)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::OHPFaultState >&
OHPContactorFaultSend::ohpfault() const {
  // @@protoc_insertion_point(field_list:AllFaultEnum.OHPContactorFaultSend.OHPFault)
  return ohpfault_;
}

// -------------------------------------------------------------------

// DMCContactorFaultSend

// uint32 gunNum = 1;
inline void DMCContactorFaultSend::clear_gunnum() {
  gunnum_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 DMCContactorFaultSend::_internal_gunnum() const {
  return gunnum_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 DMCContactorFaultSend::gunnum() const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.DMCContactorFaultSend.gunNum)
  return _internal_gunnum();
}
inline void DMCContactorFaultSend::_internal_set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  gunnum_ = value;
}
inline void DMCContactorFaultSend::set_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_gunnum(value);
  // @@protoc_insertion_point(field_set:AllFaultEnum.DMCContactorFaultSend.gunNum)
}

// repeated .AllFaultEnum.DMCFaultState DMCFault = 2;
inline int DMCContactorFaultSend::_internal_dmcfault_size() const {
  return dmcfault_.size();
}
inline int DMCContactorFaultSend::dmcfault_size() const {
  return _internal_dmcfault_size();
}
inline void DMCContactorFaultSend::clear_dmcfault() {
  dmcfault_.Clear();
}
inline ::AllFaultEnum::DMCFaultState* DMCContactorFaultSend::mutable_dmcfault(int index) {
  // @@protoc_insertion_point(field_mutable:AllFaultEnum.DMCContactorFaultSend.DMCFault)
  return dmcfault_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::DMCFaultState >*
DMCContactorFaultSend::mutable_dmcfault() {
  // @@protoc_insertion_point(field_mutable_list:AllFaultEnum.DMCContactorFaultSend.DMCFault)
  return &dmcfault_;
}
inline const ::AllFaultEnum::DMCFaultState& DMCContactorFaultSend::_internal_dmcfault(int index) const {
  return dmcfault_.Get(index);
}
inline const ::AllFaultEnum::DMCFaultState& DMCContactorFaultSend::dmcfault(int index) const {
  // @@protoc_insertion_point(field_get:AllFaultEnum.DMCContactorFaultSend.DMCFault)
  return _internal_dmcfault(index);
}
inline ::AllFaultEnum::DMCFaultState* DMCContactorFaultSend::_internal_add_dmcfault() {
  return dmcfault_.Add();
}
inline ::AllFaultEnum::DMCFaultState* DMCContactorFaultSend::add_dmcfault() {
  ::AllFaultEnum::DMCFaultState* _add = _internal_add_dmcfault();
  // @@protoc_insertion_point(field_add:AllFaultEnum.DMCContactorFaultSend.DMCFault)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::AllFaultEnum::DMCFaultState >&
DMCContactorFaultSend::dmcfault() const {
  // @@protoc_insertion_point(field_list:AllFaultEnum.DMCContactorFaultSend.DMCFault)
  return dmcfault_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace AllFaultEnum

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::AllFaultEnum::VCIFaultEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::AllFaultEnum::VCIFaultEnum>() {
  return ::AllFaultEnum::VCIFaultEnum_descriptor();
}
template <> struct is_proto_enum< ::AllFaultEnum::PMMFaultEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::AllFaultEnum::PMMFaultEnum>() {
  return ::AllFaultEnum::PMMFaultEnum_descriptor();
}
template <> struct is_proto_enum< ::AllFaultEnum::OHPFaultEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::AllFaultEnum::OHPFaultEnum>() {
  return ::AllFaultEnum::OHPFaultEnum_descriptor();
}
template <> struct is_proto_enum< ::AllFaultEnum::DMCFaultEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::AllFaultEnum::DMCFaultEnum>() {
  return ::AllFaultEnum::DMCFaultEnum_descriptor();
}
template <> struct is_proto_enum< ::AllFaultEnum::AlarmStateEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::AllFaultEnum::AlarmStateEnum>() {
  return ::AllFaultEnum::AlarmStateEnum_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_GCU_5fAllFaultEnum_2eproto
