// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GCU_EVS_HMI.proto

#include "GCU_EVS_HMI.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace gcu_evs_hmi {
namespace protobuf {
constexpr EVSLogin::EVSLogin(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : evsid_(uint64_t{0u})
  , interval_(0u){}
struct EVSLoginDefaultTypeInternal {
  constexpr EVSLoginDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVSLoginDefaultTypeInternal() {}
  union {
    EVSLogin _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVSLoginDefaultTypeInternal _EVSLogin_default_instance_;
constexpr EVSLoginAns::EVSLoginAns(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : evsid_(uint64_t{0u})
  , serverid_(0u)
  , interval_(0u){}
struct EVSLoginAnsDefaultTypeInternal {
  constexpr EVSLoginAnsDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVSLoginAnsDefaultTypeInternal() {}
  union {
    EVSLoginAns _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVSLoginAnsDefaultTypeInternal _EVSLoginAns_default_instance_;
constexpr EVSHb::EVSHb(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : evsstate_(nullptr)
  , evsid_(uint64_t{0u})
  , heartbeatcnt_(0u)
  , onoffstate_(0u){}
struct EVSHbDefaultTypeInternal {
  constexpr EVSHbDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVSHbDefaultTypeInternal() {}
  union {
    EVSHb _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVSHbDefaultTypeInternal _EVSHb_default_instance_;
constexpr EVSHbReply::EVSHbReply(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : evsid_(uint64_t{0u})
  , serverid_(0u)
  , heartbeatcnt_(0u)
  , evscmd_(0u)
  , offlinemode_(0u)
  , evsreset_(0u){}
struct EVSHbReplyDefaultTypeInternal {
  constexpr EVSHbReplyDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVSHbReplyDefaultTypeInternal() {}
  union {
    EVSHbReply _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVSHbReplyDefaultTypeInternal _EVSHbReply_default_instance_;
constexpr EVSMsgSet::EVSMsgSet(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : protoconferm_(nullptr)
  , funconferm_(nullptr)
  , paraconfigm_(nullptr)
  , authenm_(nullptr)
  , reservem_(nullptr)
  , powersupplym_(nullptr)
  , chargingm_(nullptr)
  , chargingendm_(nullptr)
  , evsid_(uint64_t{0u}){}
struct EVSMsgSetDefaultTypeInternal {
  constexpr EVSMsgSetDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVSMsgSetDefaultTypeInternal() {}
  union {
    EVSMsgSet _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVSMsgSetDefaultTypeInternal _EVSMsgSet_default_instance_;
constexpr EVSMsgCtrl::EVSMsgCtrl(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : msgctrl_(nullptr)
  , msgcycletime_(nullptr)
  , msgmaxsendtime_(nullptr)
  , funconferack_(nullptr)
  , evsid_(uint64_t{0u}){}
struct EVSMsgCtrlDefaultTypeInternal {
  constexpr EVSMsgCtrlDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVSMsgCtrlDefaultTypeInternal() {}
  union {
    EVSMsgCtrl _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVSMsgCtrlDefaultTypeInternal _EVSMsgCtrl_default_instance_;
constexpr EVSSysCtrl::EVSSysCtrl(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : electricctrl_(nullptr)
  , insultctrl_(nullptr)
  , ipmsg_(nullptr)
  , evsid_(uint64_t{0u}){}
struct EVSSysCtrlDefaultTypeInternal {
  constexpr EVSSysCtrlDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVSSysCtrlDefaultTypeInternal() {}
  union {
    EVSSysCtrl _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVSSysCtrlDefaultTypeInternal _EVSSysCtrl_default_instance_;
constexpr EVSStepMsgSet::EVSStepMsgSet(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : stepmsg_(nullptr)
  , evsid_(uint64_t{0u}){}
struct EVSStepMsgSetDefaultTypeInternal {
  constexpr EVSStepMsgSetDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVSStepMsgSetDefaultTypeInternal() {}
  union {
    EVSStepMsgSet _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVSStepMsgSetDefaultTypeInternal _EVSStepMsgSet_default_instance_;
constexpr EVSGetMsg::EVSGetMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : evsid_(uint64_t{0u})
  , gettime_(uint64_t{0u})
  , msgid_(0u){}
struct EVSGetMsgDefaultTypeInternal {
  constexpr EVSGetMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVSGetMsgDefaultTypeInternal() {}
  union {
    EVSGetMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVSGetMsgDefaultTypeInternal _EVSGetMsg_default_instance_;
constexpr EVSSetReply::EVSSetReply(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : evsid_(uint64_t{0u})
  , msgid_(0u)
  , setack_(0u){}
struct EVSSetReplyDefaultTypeInternal {
  constexpr EVSSetReplyDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EVSSetReplyDefaultTypeInternal() {}
  union {
    EVSSetReply _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EVSSetReplyDefaultTypeInternal _EVSSetReply_default_instance_;
}  // namespace protobuf
}  // namespace gcu_evs_hmi
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_GCU_5fEVS_5fHMI_2eproto[10];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_GCU_5fEVS_5fHMI_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_GCU_5fEVS_5fHMI_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_GCU_5fEVS_5fHMI_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSLogin, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSLogin, evsid_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSLogin, interval_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSLoginAns, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSLoginAns, evsid_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSLoginAns, serverid_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSLoginAns, interval_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSHb, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSHb, evsid_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSHb, heartbeatcnt_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSHb, onoffstate_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSHb, evsstate_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSHbReply, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSHbReply, evsid_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSHbReply, serverid_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSHbReply, heartbeatcnt_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSHbReply, evscmd_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSHbReply, offlinemode_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSHbReply, evsreset_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSMsgSet, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSMsgSet, evsid_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSMsgSet, protoconferm_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSMsgSet, funconferm_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSMsgSet, paraconfigm_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSMsgSet, authenm_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSMsgSet, reservem_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSMsgSet, powersupplym_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSMsgSet, chargingm_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSMsgSet, chargingendm_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSMsgCtrl, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSMsgCtrl, evsid_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSMsgCtrl, msgctrl_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSMsgCtrl, msgcycletime_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSMsgCtrl, msgmaxsendtime_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSMsgCtrl, funconferack_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSSysCtrl, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSSysCtrl, evsid_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSSysCtrl, electricctrl_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSSysCtrl, insultctrl_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSSysCtrl, ipmsg_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSStepMsgSet, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSStepMsgSet, evsid_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSStepMsgSet, stepmsg_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSGetMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSGetMsg, evsid_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSGetMsg, msgid_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSGetMsg, gettime_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSSetReply, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSSetReply, evsid_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSSetReply, msgid_),
  PROTOBUF_FIELD_OFFSET(::gcu_evs_hmi::protobuf::EVSSetReply, setack_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::gcu_evs_hmi::protobuf::EVSLogin)},
  { 7, -1, sizeof(::gcu_evs_hmi::protobuf::EVSLoginAns)},
  { 15, -1, sizeof(::gcu_evs_hmi::protobuf::EVSHb)},
  { 24, -1, sizeof(::gcu_evs_hmi::protobuf::EVSHbReply)},
  { 35, -1, sizeof(::gcu_evs_hmi::protobuf::EVSMsgSet)},
  { 49, -1, sizeof(::gcu_evs_hmi::protobuf::EVSMsgCtrl)},
  { 59, -1, sizeof(::gcu_evs_hmi::protobuf::EVSSysCtrl)},
  { 68, -1, sizeof(::gcu_evs_hmi::protobuf::EVSStepMsgSet)},
  { 75, -1, sizeof(::gcu_evs_hmi::protobuf::EVSGetMsg)},
  { 83, -1, sizeof(::gcu_evs_hmi::protobuf::EVSSetReply)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gcu_evs_hmi::protobuf::_EVSLogin_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gcu_evs_hmi::protobuf::_EVSLoginAns_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gcu_evs_hmi::protobuf::_EVSHb_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gcu_evs_hmi::protobuf::_EVSHbReply_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gcu_evs_hmi::protobuf::_EVSMsgSet_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gcu_evs_hmi::protobuf::_EVSMsgCtrl_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gcu_evs_hmi::protobuf::_EVSSysCtrl_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gcu_evs_hmi::protobuf::_EVSStepMsgSet_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gcu_evs_hmi::protobuf::_EVSGetMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gcu_evs_hmi::protobuf::_EVSSetReply_default_instance_),
};

const char descriptor_table_protodef_GCU_5fEVS_5fHMI_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\021GCU_EVS_HMI.proto\022\024gcu_evs_hmi.protobu"
  "f\032\022GCU_EVS_INFO.proto\"+\n\010EVSLogin\022\r\n\005evs"
  "ID\030\001 \001(\004\022\020\n\010interval\030\002 \001(\r\"@\n\013EVSLoginAn"
  "s\022\r\n\005evsID\030\001 \001(\004\022\020\n\010serverID\030\002 \001(\r\022\020\n\010in"
  "terval\030\003 \001(\r\"g\n\005EVSHb\022\r\n\005evsID\030\001 \001(\004\022\024\n\014"
  "heartbeatCnt\030\002 \001(\r\022\022\n\nonOffState\030\003 \001(\r\022%"
  "\n\010evsState\030\004 \001(\0132\023.EVSInfo.EVStateMsg\"z\n"
  "\nEVSHbReply\022\r\n\005evsID\030\001 \001(\004\022\020\n\010serverID\030\002"
  " \001(\r\022\024\n\014heartbeatCnt\030\003 \001(\r\022\016\n\006evsCmd\030\004 \001"
  "(\r\022\023\n\013offlineMode\030\005 \001(\r\022\020\n\010evsReset\030\006 \001("
  "\r\"\207\003\n\tEVSMsgSet\022\r\n\005evsID\030\001 \001(\004\022/\n\014protoC"
  "onferM\030\002 \001(\0132\031.EVSInfo.EVProtoConferMsg\022"
  "+\n\nfunConferM\030\003 \001(\0132\027.EVSInfo.EVFunConfe"
  "rMsg\0220\n\013paraConfigM\030\004 \001(\0132\033.EVSInfo.EVCh"
  "gParaConfigMsg\022%\n\007authenM\030\005 \001(\0132\024.EVSInf"
  "o.EVAuthenMsg\022\'\n\010reserveM\030\006 \001(\0132\025.EVSInf"
  "o.EVReserveMsg\022/\n\014powerSupplyM\030\007 \001(\0132\031.E"
  "VSInfo.EVPowerSupplyMsg\022)\n\tchargingM\030\010 \001"
  "(\0132\026.EVSInfo.EVChargingMsg\022/\n\014chargingEn"
  "dM\030\t \001(\0132\031.EVSInfo.EVChargingEndMsg\"\310\001\n\n"
  "EVSMsgCtrl\022\r\n\005evsID\030\001 \001(\004\022#\n\007msgCtrl\030\002 \001"
  "(\0132\022.EVSInfo.EVMsgCtrl\022(\n\014msgCycleTime\030\003"
  " \001(\0132\022.EVSInfo.EVMsgCtrl\022*\n\016msgMaxSendTI"
  "me\030\004 \001(\0132\022.EVSInfo.EVMsgCtrl\0220\n\014funConfe"
  "rAck\030\005 \001(\0132\032.EVSInfo.EVFunConferAckMsg\"\224"
  "\001\n\nEVSSysCtrl\022\r\n\005evsID\030\001 \001(\004\022-\n\014electric"
  "Ctrl\030\002 \001(\0132\027.EVSInfo.EVElectricCtrl\022)\n\ni"
  "nsultCtrl\030\003 \001(\0132\025.EVSInfo.EVInsultCtrl\022\035"
  "\n\005ipMsg\030\004 \001(\0132\016.EVSInfo.IpMsg\"D\n\rEVSStep"
  "MsgSet\022\r\n\005evsID\030\001 \001(\004\022$\n\007stepMsg\030\002 \001(\0132\023"
  ".EVSInfo.EVSStepMsg\":\n\tEVSGetMsg\022\r\n\005evsI"
  "D\030\001 \001(\004\022\r\n\005msgID\030\002 \001(\r\022\017\n\007getTime\030\003 \001(\004\""
  ";\n\013EVSSetReply\022\r\n\005evsID\030\001 \001(\004\022\r\n\005msgID\030\002"
  " \001(\r\022\016\n\006setAck\030\003 \001(\rP\000b\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_GCU_5fEVS_5fHMI_2eproto_deps[1] = {
  &::descriptor_table_GCU_5fEVS_5fINFO_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_GCU_5fEVS_5fHMI_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GCU_5fEVS_5fHMI_2eproto = {
  false, false, 1350, descriptor_table_protodef_GCU_5fEVS_5fHMI_2eproto, "GCU_EVS_HMI.proto", 
  &descriptor_table_GCU_5fEVS_5fHMI_2eproto_once, descriptor_table_GCU_5fEVS_5fHMI_2eproto_deps, 1, 10,
  schemas, file_default_instances, TableStruct_GCU_5fEVS_5fHMI_2eproto::offsets,
  file_level_metadata_GCU_5fEVS_5fHMI_2eproto, file_level_enum_descriptors_GCU_5fEVS_5fHMI_2eproto, file_level_service_descriptors_GCU_5fEVS_5fHMI_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_GCU_5fEVS_5fHMI_2eproto_getter() {
  return &descriptor_table_GCU_5fEVS_5fHMI_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_GCU_5fEVS_5fHMI_2eproto(&descriptor_table_GCU_5fEVS_5fHMI_2eproto);
namespace gcu_evs_hmi {
namespace protobuf {

// ===================================================================

class EVSLogin::_Internal {
 public:
};

EVSLogin::EVSLogin(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:gcu_evs_hmi.protobuf.EVSLogin)
}
EVSLogin::EVSLogin(const EVSLogin& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&evsid_, &from.evsid_,
    static_cast<size_t>(reinterpret_cast<char*>(&interval_) -
    reinterpret_cast<char*>(&evsid_)) + sizeof(interval_));
  // @@protoc_insertion_point(copy_constructor:gcu_evs_hmi.protobuf.EVSLogin)
}

inline void EVSLogin::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&evsid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&interval_) -
    reinterpret_cast<char*>(&evsid_)) + sizeof(interval_));
}

EVSLogin::~EVSLogin() {
  // @@protoc_insertion_point(destructor:gcu_evs_hmi.protobuf.EVSLogin)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVSLogin::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void EVSLogin::ArenaDtor(void* object) {
  EVSLogin* _this = reinterpret_cast< EVSLogin* >(object);
  (void)_this;
}
void EVSLogin::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVSLogin::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVSLogin::Clear() {
// @@protoc_insertion_point(message_clear_start:gcu_evs_hmi.protobuf.EVSLogin)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&evsid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&interval_) -
      reinterpret_cast<char*>(&evsid_)) + sizeof(interval_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVSLogin::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 evsID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          evsid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 interval = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          interval_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVSLogin::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gcu_evs_hmi.protobuf.EVSLogin)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 evsID = 1;
  if (this->_internal_evsid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_evsid(), target);
  }

  // uint32 interval = 2;
  if (this->_internal_interval() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_interval(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gcu_evs_hmi.protobuf.EVSLogin)
  return target;
}

size_t EVSLogin::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gcu_evs_hmi.protobuf.EVSLogin)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint64 evsID = 1;
  if (this->_internal_evsid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_evsid());
  }

  // uint32 interval = 2;
  if (this->_internal_interval() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_interval());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVSLogin::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVSLogin::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVSLogin::GetClassData() const { return &_class_data_; }

void EVSLogin::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVSLogin *>(to)->MergeFrom(
      static_cast<const EVSLogin &>(from));
}


void EVSLogin::MergeFrom(const EVSLogin& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gcu_evs_hmi.protobuf.EVSLogin)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_evsid() != 0) {
    _internal_set_evsid(from._internal_evsid());
  }
  if (from._internal_interval() != 0) {
    _internal_set_interval(from._internal_interval());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVSLogin::CopyFrom(const EVSLogin& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gcu_evs_hmi.protobuf.EVSLogin)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVSLogin::IsInitialized() const {
  return true;
}

void EVSLogin::InternalSwap(EVSLogin* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVSLogin, interval_)
      + sizeof(EVSLogin::interval_)
      - PROTOBUF_FIELD_OFFSET(EVSLogin, evsid_)>(
          reinterpret_cast<char*>(&evsid_),
          reinterpret_cast<char*>(&other->evsid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVSLogin::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fHMI_2eproto_getter, &descriptor_table_GCU_5fEVS_5fHMI_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fHMI_2eproto[0]);
}

// ===================================================================

class EVSLoginAns::_Internal {
 public:
};

EVSLoginAns::EVSLoginAns(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:gcu_evs_hmi.protobuf.EVSLoginAns)
}
EVSLoginAns::EVSLoginAns(const EVSLoginAns& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&evsid_, &from.evsid_,
    static_cast<size_t>(reinterpret_cast<char*>(&interval_) -
    reinterpret_cast<char*>(&evsid_)) + sizeof(interval_));
  // @@protoc_insertion_point(copy_constructor:gcu_evs_hmi.protobuf.EVSLoginAns)
}

inline void EVSLoginAns::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&evsid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&interval_) -
    reinterpret_cast<char*>(&evsid_)) + sizeof(interval_));
}

EVSLoginAns::~EVSLoginAns() {
  // @@protoc_insertion_point(destructor:gcu_evs_hmi.protobuf.EVSLoginAns)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVSLoginAns::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void EVSLoginAns::ArenaDtor(void* object) {
  EVSLoginAns* _this = reinterpret_cast< EVSLoginAns* >(object);
  (void)_this;
}
void EVSLoginAns::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVSLoginAns::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVSLoginAns::Clear() {
// @@protoc_insertion_point(message_clear_start:gcu_evs_hmi.protobuf.EVSLoginAns)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&evsid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&interval_) -
      reinterpret_cast<char*>(&evsid_)) + sizeof(interval_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVSLoginAns::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 evsID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          evsid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 serverID = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          serverid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 interval = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          interval_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVSLoginAns::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gcu_evs_hmi.protobuf.EVSLoginAns)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 evsID = 1;
  if (this->_internal_evsid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_evsid(), target);
  }

  // uint32 serverID = 2;
  if (this->_internal_serverid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_serverid(), target);
  }

  // uint32 interval = 3;
  if (this->_internal_interval() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_interval(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gcu_evs_hmi.protobuf.EVSLoginAns)
  return target;
}

size_t EVSLoginAns::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gcu_evs_hmi.protobuf.EVSLoginAns)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint64 evsID = 1;
  if (this->_internal_evsid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_evsid());
  }

  // uint32 serverID = 2;
  if (this->_internal_serverid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_serverid());
  }

  // uint32 interval = 3;
  if (this->_internal_interval() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_interval());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVSLoginAns::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVSLoginAns::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVSLoginAns::GetClassData() const { return &_class_data_; }

void EVSLoginAns::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVSLoginAns *>(to)->MergeFrom(
      static_cast<const EVSLoginAns &>(from));
}


void EVSLoginAns::MergeFrom(const EVSLoginAns& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gcu_evs_hmi.protobuf.EVSLoginAns)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_evsid() != 0) {
    _internal_set_evsid(from._internal_evsid());
  }
  if (from._internal_serverid() != 0) {
    _internal_set_serverid(from._internal_serverid());
  }
  if (from._internal_interval() != 0) {
    _internal_set_interval(from._internal_interval());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVSLoginAns::CopyFrom(const EVSLoginAns& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gcu_evs_hmi.protobuf.EVSLoginAns)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVSLoginAns::IsInitialized() const {
  return true;
}

void EVSLoginAns::InternalSwap(EVSLoginAns* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVSLoginAns, interval_)
      + sizeof(EVSLoginAns::interval_)
      - PROTOBUF_FIELD_OFFSET(EVSLoginAns, evsid_)>(
          reinterpret_cast<char*>(&evsid_),
          reinterpret_cast<char*>(&other->evsid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVSLoginAns::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fHMI_2eproto_getter, &descriptor_table_GCU_5fEVS_5fHMI_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fHMI_2eproto[1]);
}

// ===================================================================

class EVSHb::_Internal {
 public:
  static const ::EVSInfo::EVStateMsg& evsstate(const EVSHb* msg);
};

const ::EVSInfo::EVStateMsg&
EVSHb::_Internal::evsstate(const EVSHb* msg) {
  return *msg->evsstate_;
}
void EVSHb::clear_evsstate() {
  if (GetArenaForAllocation() == nullptr && evsstate_ != nullptr) {
    delete evsstate_;
  }
  evsstate_ = nullptr;
}
EVSHb::EVSHb(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:gcu_evs_hmi.protobuf.EVSHb)
}
EVSHb::EVSHb(const EVSHb& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_evsstate()) {
    evsstate_ = new ::EVSInfo::EVStateMsg(*from.evsstate_);
  } else {
    evsstate_ = nullptr;
  }
  ::memcpy(&evsid_, &from.evsid_,
    static_cast<size_t>(reinterpret_cast<char*>(&onoffstate_) -
    reinterpret_cast<char*>(&evsid_)) + sizeof(onoffstate_));
  // @@protoc_insertion_point(copy_constructor:gcu_evs_hmi.protobuf.EVSHb)
}

inline void EVSHb::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&evsstate_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&onoffstate_) -
    reinterpret_cast<char*>(&evsstate_)) + sizeof(onoffstate_));
}

EVSHb::~EVSHb() {
  // @@protoc_insertion_point(destructor:gcu_evs_hmi.protobuf.EVSHb)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVSHb::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete evsstate_;
}

void EVSHb::ArenaDtor(void* object) {
  EVSHb* _this = reinterpret_cast< EVSHb* >(object);
  (void)_this;
}
void EVSHb::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVSHb::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVSHb::Clear() {
// @@protoc_insertion_point(message_clear_start:gcu_evs_hmi.protobuf.EVSHb)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && evsstate_ != nullptr) {
    delete evsstate_;
  }
  evsstate_ = nullptr;
  ::memset(&evsid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&onoffstate_) -
      reinterpret_cast<char*>(&evsid_)) + sizeof(onoffstate_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVSHb::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 evsID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          evsid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 heartbeatCnt = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          heartbeatcnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 onOffState = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          onoffstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .EVSInfo.EVStateMsg evsState = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_evsstate(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVSHb::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gcu_evs_hmi.protobuf.EVSHb)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 evsID = 1;
  if (this->_internal_evsid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_evsid(), target);
  }

  // uint32 heartbeatCnt = 2;
  if (this->_internal_heartbeatcnt() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_heartbeatcnt(), target);
  }

  // uint32 onOffState = 3;
  if (this->_internal_onoffstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_onoffstate(), target);
  }

  // .EVSInfo.EVStateMsg evsState = 4;
  if (this->_internal_has_evsstate()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::evsstate(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gcu_evs_hmi.protobuf.EVSHb)
  return target;
}

size_t EVSHb::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gcu_evs_hmi.protobuf.EVSHb)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .EVSInfo.EVStateMsg evsState = 4;
  if (this->_internal_has_evsstate()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *evsstate_);
  }

  // uint64 evsID = 1;
  if (this->_internal_evsid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_evsid());
  }

  // uint32 heartbeatCnt = 2;
  if (this->_internal_heartbeatcnt() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_heartbeatcnt());
  }

  // uint32 onOffState = 3;
  if (this->_internal_onoffstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_onoffstate());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVSHb::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVSHb::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVSHb::GetClassData() const { return &_class_data_; }

void EVSHb::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVSHb *>(to)->MergeFrom(
      static_cast<const EVSHb &>(from));
}


void EVSHb::MergeFrom(const EVSHb& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gcu_evs_hmi.protobuf.EVSHb)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_evsstate()) {
    _internal_mutable_evsstate()->::EVSInfo::EVStateMsg::MergeFrom(from._internal_evsstate());
  }
  if (from._internal_evsid() != 0) {
    _internal_set_evsid(from._internal_evsid());
  }
  if (from._internal_heartbeatcnt() != 0) {
    _internal_set_heartbeatcnt(from._internal_heartbeatcnt());
  }
  if (from._internal_onoffstate() != 0) {
    _internal_set_onoffstate(from._internal_onoffstate());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVSHb::CopyFrom(const EVSHb& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gcu_evs_hmi.protobuf.EVSHb)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVSHb::IsInitialized() const {
  return true;
}

void EVSHb::InternalSwap(EVSHb* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVSHb, onoffstate_)
      + sizeof(EVSHb::onoffstate_)
      - PROTOBUF_FIELD_OFFSET(EVSHb, evsstate_)>(
          reinterpret_cast<char*>(&evsstate_),
          reinterpret_cast<char*>(&other->evsstate_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVSHb::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fHMI_2eproto_getter, &descriptor_table_GCU_5fEVS_5fHMI_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fHMI_2eproto[2]);
}

// ===================================================================

class EVSHbReply::_Internal {
 public:
};

EVSHbReply::EVSHbReply(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:gcu_evs_hmi.protobuf.EVSHbReply)
}
EVSHbReply::EVSHbReply(const EVSHbReply& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&evsid_, &from.evsid_,
    static_cast<size_t>(reinterpret_cast<char*>(&evsreset_) -
    reinterpret_cast<char*>(&evsid_)) + sizeof(evsreset_));
  // @@protoc_insertion_point(copy_constructor:gcu_evs_hmi.protobuf.EVSHbReply)
}

inline void EVSHbReply::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&evsid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&evsreset_) -
    reinterpret_cast<char*>(&evsid_)) + sizeof(evsreset_));
}

EVSHbReply::~EVSHbReply() {
  // @@protoc_insertion_point(destructor:gcu_evs_hmi.protobuf.EVSHbReply)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVSHbReply::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void EVSHbReply::ArenaDtor(void* object) {
  EVSHbReply* _this = reinterpret_cast< EVSHbReply* >(object);
  (void)_this;
}
void EVSHbReply::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVSHbReply::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVSHbReply::Clear() {
// @@protoc_insertion_point(message_clear_start:gcu_evs_hmi.protobuf.EVSHbReply)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&evsid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&evsreset_) -
      reinterpret_cast<char*>(&evsid_)) + sizeof(evsreset_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVSHbReply::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 evsID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          evsid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 serverID = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          serverid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 heartbeatCnt = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          heartbeatcnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 evsCmd = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          evscmd_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 offlineMode = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          offlinemode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 evsReset = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          evsreset_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVSHbReply::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gcu_evs_hmi.protobuf.EVSHbReply)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 evsID = 1;
  if (this->_internal_evsid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_evsid(), target);
  }

  // uint32 serverID = 2;
  if (this->_internal_serverid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_serverid(), target);
  }

  // uint32 heartbeatCnt = 3;
  if (this->_internal_heartbeatcnt() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_heartbeatcnt(), target);
  }

  // uint32 evsCmd = 4;
  if (this->_internal_evscmd() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_evscmd(), target);
  }

  // uint32 offlineMode = 5;
  if (this->_internal_offlinemode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_offlinemode(), target);
  }

  // uint32 evsReset = 6;
  if (this->_internal_evsreset() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_evsreset(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gcu_evs_hmi.protobuf.EVSHbReply)
  return target;
}

size_t EVSHbReply::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gcu_evs_hmi.protobuf.EVSHbReply)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint64 evsID = 1;
  if (this->_internal_evsid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_evsid());
  }

  // uint32 serverID = 2;
  if (this->_internal_serverid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_serverid());
  }

  // uint32 heartbeatCnt = 3;
  if (this->_internal_heartbeatcnt() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_heartbeatcnt());
  }

  // uint32 evsCmd = 4;
  if (this->_internal_evscmd() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_evscmd());
  }

  // uint32 offlineMode = 5;
  if (this->_internal_offlinemode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_offlinemode());
  }

  // uint32 evsReset = 6;
  if (this->_internal_evsreset() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_evsreset());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVSHbReply::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVSHbReply::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVSHbReply::GetClassData() const { return &_class_data_; }

void EVSHbReply::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVSHbReply *>(to)->MergeFrom(
      static_cast<const EVSHbReply &>(from));
}


void EVSHbReply::MergeFrom(const EVSHbReply& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gcu_evs_hmi.protobuf.EVSHbReply)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_evsid() != 0) {
    _internal_set_evsid(from._internal_evsid());
  }
  if (from._internal_serverid() != 0) {
    _internal_set_serverid(from._internal_serverid());
  }
  if (from._internal_heartbeatcnt() != 0) {
    _internal_set_heartbeatcnt(from._internal_heartbeatcnt());
  }
  if (from._internal_evscmd() != 0) {
    _internal_set_evscmd(from._internal_evscmd());
  }
  if (from._internal_offlinemode() != 0) {
    _internal_set_offlinemode(from._internal_offlinemode());
  }
  if (from._internal_evsreset() != 0) {
    _internal_set_evsreset(from._internal_evsreset());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVSHbReply::CopyFrom(const EVSHbReply& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gcu_evs_hmi.protobuf.EVSHbReply)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVSHbReply::IsInitialized() const {
  return true;
}

void EVSHbReply::InternalSwap(EVSHbReply* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVSHbReply, evsreset_)
      + sizeof(EVSHbReply::evsreset_)
      - PROTOBUF_FIELD_OFFSET(EVSHbReply, evsid_)>(
          reinterpret_cast<char*>(&evsid_),
          reinterpret_cast<char*>(&other->evsid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVSHbReply::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fHMI_2eproto_getter, &descriptor_table_GCU_5fEVS_5fHMI_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fHMI_2eproto[3]);
}

// ===================================================================

class EVSMsgSet::_Internal {
 public:
  static const ::EVSInfo::EVProtoConferMsg& protoconferm(const EVSMsgSet* msg);
  static const ::EVSInfo::EVFunConferMsg& funconferm(const EVSMsgSet* msg);
  static const ::EVSInfo::EVChgParaConfigMsg& paraconfigm(const EVSMsgSet* msg);
  static const ::EVSInfo::EVAuthenMsg& authenm(const EVSMsgSet* msg);
  static const ::EVSInfo::EVReserveMsg& reservem(const EVSMsgSet* msg);
  static const ::EVSInfo::EVPowerSupplyMsg& powersupplym(const EVSMsgSet* msg);
  static const ::EVSInfo::EVChargingMsg& chargingm(const EVSMsgSet* msg);
  static const ::EVSInfo::EVChargingEndMsg& chargingendm(const EVSMsgSet* msg);
};

const ::EVSInfo::EVProtoConferMsg&
EVSMsgSet::_Internal::protoconferm(const EVSMsgSet* msg) {
  return *msg->protoconferm_;
}
const ::EVSInfo::EVFunConferMsg&
EVSMsgSet::_Internal::funconferm(const EVSMsgSet* msg) {
  return *msg->funconferm_;
}
const ::EVSInfo::EVChgParaConfigMsg&
EVSMsgSet::_Internal::paraconfigm(const EVSMsgSet* msg) {
  return *msg->paraconfigm_;
}
const ::EVSInfo::EVAuthenMsg&
EVSMsgSet::_Internal::authenm(const EVSMsgSet* msg) {
  return *msg->authenm_;
}
const ::EVSInfo::EVReserveMsg&
EVSMsgSet::_Internal::reservem(const EVSMsgSet* msg) {
  return *msg->reservem_;
}
const ::EVSInfo::EVPowerSupplyMsg&
EVSMsgSet::_Internal::powersupplym(const EVSMsgSet* msg) {
  return *msg->powersupplym_;
}
const ::EVSInfo::EVChargingMsg&
EVSMsgSet::_Internal::chargingm(const EVSMsgSet* msg) {
  return *msg->chargingm_;
}
const ::EVSInfo::EVChargingEndMsg&
EVSMsgSet::_Internal::chargingendm(const EVSMsgSet* msg) {
  return *msg->chargingendm_;
}
void EVSMsgSet::clear_protoconferm() {
  if (GetArenaForAllocation() == nullptr && protoconferm_ != nullptr) {
    delete protoconferm_;
  }
  protoconferm_ = nullptr;
}
void EVSMsgSet::clear_funconferm() {
  if (GetArenaForAllocation() == nullptr && funconferm_ != nullptr) {
    delete funconferm_;
  }
  funconferm_ = nullptr;
}
void EVSMsgSet::clear_paraconfigm() {
  if (GetArenaForAllocation() == nullptr && paraconfigm_ != nullptr) {
    delete paraconfigm_;
  }
  paraconfigm_ = nullptr;
}
void EVSMsgSet::clear_authenm() {
  if (GetArenaForAllocation() == nullptr && authenm_ != nullptr) {
    delete authenm_;
  }
  authenm_ = nullptr;
}
void EVSMsgSet::clear_reservem() {
  if (GetArenaForAllocation() == nullptr && reservem_ != nullptr) {
    delete reservem_;
  }
  reservem_ = nullptr;
}
void EVSMsgSet::clear_powersupplym() {
  if (GetArenaForAllocation() == nullptr && powersupplym_ != nullptr) {
    delete powersupplym_;
  }
  powersupplym_ = nullptr;
}
void EVSMsgSet::clear_chargingm() {
  if (GetArenaForAllocation() == nullptr && chargingm_ != nullptr) {
    delete chargingm_;
  }
  chargingm_ = nullptr;
}
void EVSMsgSet::clear_chargingendm() {
  if (GetArenaForAllocation() == nullptr && chargingendm_ != nullptr) {
    delete chargingendm_;
  }
  chargingendm_ = nullptr;
}
EVSMsgSet::EVSMsgSet(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:gcu_evs_hmi.protobuf.EVSMsgSet)
}
EVSMsgSet::EVSMsgSet(const EVSMsgSet& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_protoconferm()) {
    protoconferm_ = new ::EVSInfo::EVProtoConferMsg(*from.protoconferm_);
  } else {
    protoconferm_ = nullptr;
  }
  if (from._internal_has_funconferm()) {
    funconferm_ = new ::EVSInfo::EVFunConferMsg(*from.funconferm_);
  } else {
    funconferm_ = nullptr;
  }
  if (from._internal_has_paraconfigm()) {
    paraconfigm_ = new ::EVSInfo::EVChgParaConfigMsg(*from.paraconfigm_);
  } else {
    paraconfigm_ = nullptr;
  }
  if (from._internal_has_authenm()) {
    authenm_ = new ::EVSInfo::EVAuthenMsg(*from.authenm_);
  } else {
    authenm_ = nullptr;
  }
  if (from._internal_has_reservem()) {
    reservem_ = new ::EVSInfo::EVReserveMsg(*from.reservem_);
  } else {
    reservem_ = nullptr;
  }
  if (from._internal_has_powersupplym()) {
    powersupplym_ = new ::EVSInfo::EVPowerSupplyMsg(*from.powersupplym_);
  } else {
    powersupplym_ = nullptr;
  }
  if (from._internal_has_chargingm()) {
    chargingm_ = new ::EVSInfo::EVChargingMsg(*from.chargingm_);
  } else {
    chargingm_ = nullptr;
  }
  if (from._internal_has_chargingendm()) {
    chargingendm_ = new ::EVSInfo::EVChargingEndMsg(*from.chargingendm_);
  } else {
    chargingendm_ = nullptr;
  }
  evsid_ = from.evsid_;
  // @@protoc_insertion_point(copy_constructor:gcu_evs_hmi.protobuf.EVSMsgSet)
}

inline void EVSMsgSet::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&protoconferm_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&evsid_) -
    reinterpret_cast<char*>(&protoconferm_)) + sizeof(evsid_));
}

EVSMsgSet::~EVSMsgSet() {
  // @@protoc_insertion_point(destructor:gcu_evs_hmi.protobuf.EVSMsgSet)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVSMsgSet::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete protoconferm_;
  if (this != internal_default_instance()) delete funconferm_;
  if (this != internal_default_instance()) delete paraconfigm_;
  if (this != internal_default_instance()) delete authenm_;
  if (this != internal_default_instance()) delete reservem_;
  if (this != internal_default_instance()) delete powersupplym_;
  if (this != internal_default_instance()) delete chargingm_;
  if (this != internal_default_instance()) delete chargingendm_;
}

void EVSMsgSet::ArenaDtor(void* object) {
  EVSMsgSet* _this = reinterpret_cast< EVSMsgSet* >(object);
  (void)_this;
}
void EVSMsgSet::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVSMsgSet::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVSMsgSet::Clear() {
// @@protoc_insertion_point(message_clear_start:gcu_evs_hmi.protobuf.EVSMsgSet)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && protoconferm_ != nullptr) {
    delete protoconferm_;
  }
  protoconferm_ = nullptr;
  if (GetArenaForAllocation() == nullptr && funconferm_ != nullptr) {
    delete funconferm_;
  }
  funconferm_ = nullptr;
  if (GetArenaForAllocation() == nullptr && paraconfigm_ != nullptr) {
    delete paraconfigm_;
  }
  paraconfigm_ = nullptr;
  if (GetArenaForAllocation() == nullptr && authenm_ != nullptr) {
    delete authenm_;
  }
  authenm_ = nullptr;
  if (GetArenaForAllocation() == nullptr && reservem_ != nullptr) {
    delete reservem_;
  }
  reservem_ = nullptr;
  if (GetArenaForAllocation() == nullptr && powersupplym_ != nullptr) {
    delete powersupplym_;
  }
  powersupplym_ = nullptr;
  if (GetArenaForAllocation() == nullptr && chargingm_ != nullptr) {
    delete chargingm_;
  }
  chargingm_ = nullptr;
  if (GetArenaForAllocation() == nullptr && chargingendm_ != nullptr) {
    delete chargingendm_;
  }
  chargingendm_ = nullptr;
  evsid_ = uint64_t{0u};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVSMsgSet::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 evsID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          evsid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .EVSInfo.EVProtoConferMsg protoConferM = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_protoconferm(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .EVSInfo.EVFunConferMsg funConferM = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_funconferm(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .EVSInfo.EVChgParaConfigMsg paraConfigM = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_paraconfigm(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .EVSInfo.EVAuthenMsg authenM = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_authenm(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .EVSInfo.EVReserveMsg reserveM = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_reservem(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .EVSInfo.EVPowerSupplyMsg powerSupplyM = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_powersupplym(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .EVSInfo.EVChargingMsg chargingM = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          ptr = ctx->ParseMessage(_internal_mutable_chargingm(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .EVSInfo.EVChargingEndMsg chargingEndM = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          ptr = ctx->ParseMessage(_internal_mutable_chargingendm(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVSMsgSet::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gcu_evs_hmi.protobuf.EVSMsgSet)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 evsID = 1;
  if (this->_internal_evsid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_evsid(), target);
  }

  // .EVSInfo.EVProtoConferMsg protoConferM = 2;
  if (this->_internal_has_protoconferm()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::protoconferm(this), target, stream);
  }

  // .EVSInfo.EVFunConferMsg funConferM = 3;
  if (this->_internal_has_funconferm()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::funconferm(this), target, stream);
  }

  // .EVSInfo.EVChgParaConfigMsg paraConfigM = 4;
  if (this->_internal_has_paraconfigm()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::paraconfigm(this), target, stream);
  }

  // .EVSInfo.EVAuthenMsg authenM = 5;
  if (this->_internal_has_authenm()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::authenm(this), target, stream);
  }

  // .EVSInfo.EVReserveMsg reserveM = 6;
  if (this->_internal_has_reservem()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::reservem(this), target, stream);
  }

  // .EVSInfo.EVPowerSupplyMsg powerSupplyM = 7;
  if (this->_internal_has_powersupplym()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::powersupplym(this), target, stream);
  }

  // .EVSInfo.EVChargingMsg chargingM = 8;
  if (this->_internal_has_chargingm()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        8, _Internal::chargingm(this), target, stream);
  }

  // .EVSInfo.EVChargingEndMsg chargingEndM = 9;
  if (this->_internal_has_chargingendm()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        9, _Internal::chargingendm(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gcu_evs_hmi.protobuf.EVSMsgSet)
  return target;
}

size_t EVSMsgSet::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gcu_evs_hmi.protobuf.EVSMsgSet)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .EVSInfo.EVProtoConferMsg protoConferM = 2;
  if (this->_internal_has_protoconferm()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *protoconferm_);
  }

  // .EVSInfo.EVFunConferMsg funConferM = 3;
  if (this->_internal_has_funconferm()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *funconferm_);
  }

  // .EVSInfo.EVChgParaConfigMsg paraConfigM = 4;
  if (this->_internal_has_paraconfigm()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *paraconfigm_);
  }

  // .EVSInfo.EVAuthenMsg authenM = 5;
  if (this->_internal_has_authenm()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *authenm_);
  }

  // .EVSInfo.EVReserveMsg reserveM = 6;
  if (this->_internal_has_reservem()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *reservem_);
  }

  // .EVSInfo.EVPowerSupplyMsg powerSupplyM = 7;
  if (this->_internal_has_powersupplym()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *powersupplym_);
  }

  // .EVSInfo.EVChargingMsg chargingM = 8;
  if (this->_internal_has_chargingm()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *chargingm_);
  }

  // .EVSInfo.EVChargingEndMsg chargingEndM = 9;
  if (this->_internal_has_chargingendm()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *chargingendm_);
  }

  // uint64 evsID = 1;
  if (this->_internal_evsid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_evsid());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVSMsgSet::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVSMsgSet::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVSMsgSet::GetClassData() const { return &_class_data_; }

void EVSMsgSet::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVSMsgSet *>(to)->MergeFrom(
      static_cast<const EVSMsgSet &>(from));
}


void EVSMsgSet::MergeFrom(const EVSMsgSet& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gcu_evs_hmi.protobuf.EVSMsgSet)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_protoconferm()) {
    _internal_mutable_protoconferm()->::EVSInfo::EVProtoConferMsg::MergeFrom(from._internal_protoconferm());
  }
  if (from._internal_has_funconferm()) {
    _internal_mutable_funconferm()->::EVSInfo::EVFunConferMsg::MergeFrom(from._internal_funconferm());
  }
  if (from._internal_has_paraconfigm()) {
    _internal_mutable_paraconfigm()->::EVSInfo::EVChgParaConfigMsg::MergeFrom(from._internal_paraconfigm());
  }
  if (from._internal_has_authenm()) {
    _internal_mutable_authenm()->::EVSInfo::EVAuthenMsg::MergeFrom(from._internal_authenm());
  }
  if (from._internal_has_reservem()) {
    _internal_mutable_reservem()->::EVSInfo::EVReserveMsg::MergeFrom(from._internal_reservem());
  }
  if (from._internal_has_powersupplym()) {
    _internal_mutable_powersupplym()->::EVSInfo::EVPowerSupplyMsg::MergeFrom(from._internal_powersupplym());
  }
  if (from._internal_has_chargingm()) {
    _internal_mutable_chargingm()->::EVSInfo::EVChargingMsg::MergeFrom(from._internal_chargingm());
  }
  if (from._internal_has_chargingendm()) {
    _internal_mutable_chargingendm()->::EVSInfo::EVChargingEndMsg::MergeFrom(from._internal_chargingendm());
  }
  if (from._internal_evsid() != 0) {
    _internal_set_evsid(from._internal_evsid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVSMsgSet::CopyFrom(const EVSMsgSet& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gcu_evs_hmi.protobuf.EVSMsgSet)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVSMsgSet::IsInitialized() const {
  return true;
}

void EVSMsgSet::InternalSwap(EVSMsgSet* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVSMsgSet, evsid_)
      + sizeof(EVSMsgSet::evsid_)
      - PROTOBUF_FIELD_OFFSET(EVSMsgSet, protoconferm_)>(
          reinterpret_cast<char*>(&protoconferm_),
          reinterpret_cast<char*>(&other->protoconferm_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVSMsgSet::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fHMI_2eproto_getter, &descriptor_table_GCU_5fEVS_5fHMI_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fHMI_2eproto[4]);
}

// ===================================================================

class EVSMsgCtrl::_Internal {
 public:
  static const ::EVSInfo::EVMsgCtrl& msgctrl(const EVSMsgCtrl* msg);
  static const ::EVSInfo::EVMsgCtrl& msgcycletime(const EVSMsgCtrl* msg);
  static const ::EVSInfo::EVMsgCtrl& msgmaxsendtime(const EVSMsgCtrl* msg);
  static const ::EVSInfo::EVFunConferAckMsg& funconferack(const EVSMsgCtrl* msg);
};

const ::EVSInfo::EVMsgCtrl&
EVSMsgCtrl::_Internal::msgctrl(const EVSMsgCtrl* msg) {
  return *msg->msgctrl_;
}
const ::EVSInfo::EVMsgCtrl&
EVSMsgCtrl::_Internal::msgcycletime(const EVSMsgCtrl* msg) {
  return *msg->msgcycletime_;
}
const ::EVSInfo::EVMsgCtrl&
EVSMsgCtrl::_Internal::msgmaxsendtime(const EVSMsgCtrl* msg) {
  return *msg->msgmaxsendtime_;
}
const ::EVSInfo::EVFunConferAckMsg&
EVSMsgCtrl::_Internal::funconferack(const EVSMsgCtrl* msg) {
  return *msg->funconferack_;
}
void EVSMsgCtrl::clear_msgctrl() {
  if (GetArenaForAllocation() == nullptr && msgctrl_ != nullptr) {
    delete msgctrl_;
  }
  msgctrl_ = nullptr;
}
void EVSMsgCtrl::clear_msgcycletime() {
  if (GetArenaForAllocation() == nullptr && msgcycletime_ != nullptr) {
    delete msgcycletime_;
  }
  msgcycletime_ = nullptr;
}
void EVSMsgCtrl::clear_msgmaxsendtime() {
  if (GetArenaForAllocation() == nullptr && msgmaxsendtime_ != nullptr) {
    delete msgmaxsendtime_;
  }
  msgmaxsendtime_ = nullptr;
}
void EVSMsgCtrl::clear_funconferack() {
  if (GetArenaForAllocation() == nullptr && funconferack_ != nullptr) {
    delete funconferack_;
  }
  funconferack_ = nullptr;
}
EVSMsgCtrl::EVSMsgCtrl(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:gcu_evs_hmi.protobuf.EVSMsgCtrl)
}
EVSMsgCtrl::EVSMsgCtrl(const EVSMsgCtrl& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_msgctrl()) {
    msgctrl_ = new ::EVSInfo::EVMsgCtrl(*from.msgctrl_);
  } else {
    msgctrl_ = nullptr;
  }
  if (from._internal_has_msgcycletime()) {
    msgcycletime_ = new ::EVSInfo::EVMsgCtrl(*from.msgcycletime_);
  } else {
    msgcycletime_ = nullptr;
  }
  if (from._internal_has_msgmaxsendtime()) {
    msgmaxsendtime_ = new ::EVSInfo::EVMsgCtrl(*from.msgmaxsendtime_);
  } else {
    msgmaxsendtime_ = nullptr;
  }
  if (from._internal_has_funconferack()) {
    funconferack_ = new ::EVSInfo::EVFunConferAckMsg(*from.funconferack_);
  } else {
    funconferack_ = nullptr;
  }
  evsid_ = from.evsid_;
  // @@protoc_insertion_point(copy_constructor:gcu_evs_hmi.protobuf.EVSMsgCtrl)
}

inline void EVSMsgCtrl::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&msgctrl_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&evsid_) -
    reinterpret_cast<char*>(&msgctrl_)) + sizeof(evsid_));
}

EVSMsgCtrl::~EVSMsgCtrl() {
  // @@protoc_insertion_point(destructor:gcu_evs_hmi.protobuf.EVSMsgCtrl)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVSMsgCtrl::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete msgctrl_;
  if (this != internal_default_instance()) delete msgcycletime_;
  if (this != internal_default_instance()) delete msgmaxsendtime_;
  if (this != internal_default_instance()) delete funconferack_;
}

void EVSMsgCtrl::ArenaDtor(void* object) {
  EVSMsgCtrl* _this = reinterpret_cast< EVSMsgCtrl* >(object);
  (void)_this;
}
void EVSMsgCtrl::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVSMsgCtrl::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVSMsgCtrl::Clear() {
// @@protoc_insertion_point(message_clear_start:gcu_evs_hmi.protobuf.EVSMsgCtrl)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && msgctrl_ != nullptr) {
    delete msgctrl_;
  }
  msgctrl_ = nullptr;
  if (GetArenaForAllocation() == nullptr && msgcycletime_ != nullptr) {
    delete msgcycletime_;
  }
  msgcycletime_ = nullptr;
  if (GetArenaForAllocation() == nullptr && msgmaxsendtime_ != nullptr) {
    delete msgmaxsendtime_;
  }
  msgmaxsendtime_ = nullptr;
  if (GetArenaForAllocation() == nullptr && funconferack_ != nullptr) {
    delete funconferack_;
  }
  funconferack_ = nullptr;
  evsid_ = uint64_t{0u};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVSMsgCtrl::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 evsID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          evsid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .EVSInfo.EVMsgCtrl msgCtrl = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_msgctrl(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .EVSInfo.EVMsgCtrl msgCycleTime = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_msgcycletime(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .EVSInfo.EVMsgCtrl msgMaxSendTIme = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_msgmaxsendtime(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .EVSInfo.EVFunConferAckMsg funConferAck = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_funconferack(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVSMsgCtrl::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gcu_evs_hmi.protobuf.EVSMsgCtrl)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 evsID = 1;
  if (this->_internal_evsid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_evsid(), target);
  }

  // .EVSInfo.EVMsgCtrl msgCtrl = 2;
  if (this->_internal_has_msgctrl()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::msgctrl(this), target, stream);
  }

  // .EVSInfo.EVMsgCtrl msgCycleTime = 3;
  if (this->_internal_has_msgcycletime()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::msgcycletime(this), target, stream);
  }

  // .EVSInfo.EVMsgCtrl msgMaxSendTIme = 4;
  if (this->_internal_has_msgmaxsendtime()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::msgmaxsendtime(this), target, stream);
  }

  // .EVSInfo.EVFunConferAckMsg funConferAck = 5;
  if (this->_internal_has_funconferack()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::funconferack(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gcu_evs_hmi.protobuf.EVSMsgCtrl)
  return target;
}

size_t EVSMsgCtrl::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gcu_evs_hmi.protobuf.EVSMsgCtrl)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .EVSInfo.EVMsgCtrl msgCtrl = 2;
  if (this->_internal_has_msgctrl()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *msgctrl_);
  }

  // .EVSInfo.EVMsgCtrl msgCycleTime = 3;
  if (this->_internal_has_msgcycletime()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *msgcycletime_);
  }

  // .EVSInfo.EVMsgCtrl msgMaxSendTIme = 4;
  if (this->_internal_has_msgmaxsendtime()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *msgmaxsendtime_);
  }

  // .EVSInfo.EVFunConferAckMsg funConferAck = 5;
  if (this->_internal_has_funconferack()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *funconferack_);
  }

  // uint64 evsID = 1;
  if (this->_internal_evsid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_evsid());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVSMsgCtrl::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVSMsgCtrl::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVSMsgCtrl::GetClassData() const { return &_class_data_; }

void EVSMsgCtrl::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVSMsgCtrl *>(to)->MergeFrom(
      static_cast<const EVSMsgCtrl &>(from));
}


void EVSMsgCtrl::MergeFrom(const EVSMsgCtrl& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gcu_evs_hmi.protobuf.EVSMsgCtrl)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_msgctrl()) {
    _internal_mutable_msgctrl()->::EVSInfo::EVMsgCtrl::MergeFrom(from._internal_msgctrl());
  }
  if (from._internal_has_msgcycletime()) {
    _internal_mutable_msgcycletime()->::EVSInfo::EVMsgCtrl::MergeFrom(from._internal_msgcycletime());
  }
  if (from._internal_has_msgmaxsendtime()) {
    _internal_mutable_msgmaxsendtime()->::EVSInfo::EVMsgCtrl::MergeFrom(from._internal_msgmaxsendtime());
  }
  if (from._internal_has_funconferack()) {
    _internal_mutable_funconferack()->::EVSInfo::EVFunConferAckMsg::MergeFrom(from._internal_funconferack());
  }
  if (from._internal_evsid() != 0) {
    _internal_set_evsid(from._internal_evsid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVSMsgCtrl::CopyFrom(const EVSMsgCtrl& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gcu_evs_hmi.protobuf.EVSMsgCtrl)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVSMsgCtrl::IsInitialized() const {
  return true;
}

void EVSMsgCtrl::InternalSwap(EVSMsgCtrl* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVSMsgCtrl, evsid_)
      + sizeof(EVSMsgCtrl::evsid_)
      - PROTOBUF_FIELD_OFFSET(EVSMsgCtrl, msgctrl_)>(
          reinterpret_cast<char*>(&msgctrl_),
          reinterpret_cast<char*>(&other->msgctrl_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVSMsgCtrl::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fHMI_2eproto_getter, &descriptor_table_GCU_5fEVS_5fHMI_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fHMI_2eproto[5]);
}

// ===================================================================

class EVSSysCtrl::_Internal {
 public:
  static const ::EVSInfo::EVElectricCtrl& electricctrl(const EVSSysCtrl* msg);
  static const ::EVSInfo::EVInsultCtrl& insultctrl(const EVSSysCtrl* msg);
  static const ::EVSInfo::IpMsg& ipmsg(const EVSSysCtrl* msg);
};

const ::EVSInfo::EVElectricCtrl&
EVSSysCtrl::_Internal::electricctrl(const EVSSysCtrl* msg) {
  return *msg->electricctrl_;
}
const ::EVSInfo::EVInsultCtrl&
EVSSysCtrl::_Internal::insultctrl(const EVSSysCtrl* msg) {
  return *msg->insultctrl_;
}
const ::EVSInfo::IpMsg&
EVSSysCtrl::_Internal::ipmsg(const EVSSysCtrl* msg) {
  return *msg->ipmsg_;
}
void EVSSysCtrl::clear_electricctrl() {
  if (GetArenaForAllocation() == nullptr && electricctrl_ != nullptr) {
    delete electricctrl_;
  }
  electricctrl_ = nullptr;
}
void EVSSysCtrl::clear_insultctrl() {
  if (GetArenaForAllocation() == nullptr && insultctrl_ != nullptr) {
    delete insultctrl_;
  }
  insultctrl_ = nullptr;
}
void EVSSysCtrl::clear_ipmsg() {
  if (GetArenaForAllocation() == nullptr && ipmsg_ != nullptr) {
    delete ipmsg_;
  }
  ipmsg_ = nullptr;
}
EVSSysCtrl::EVSSysCtrl(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:gcu_evs_hmi.protobuf.EVSSysCtrl)
}
EVSSysCtrl::EVSSysCtrl(const EVSSysCtrl& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_electricctrl()) {
    electricctrl_ = new ::EVSInfo::EVElectricCtrl(*from.electricctrl_);
  } else {
    electricctrl_ = nullptr;
  }
  if (from._internal_has_insultctrl()) {
    insultctrl_ = new ::EVSInfo::EVInsultCtrl(*from.insultctrl_);
  } else {
    insultctrl_ = nullptr;
  }
  if (from._internal_has_ipmsg()) {
    ipmsg_ = new ::EVSInfo::IpMsg(*from.ipmsg_);
  } else {
    ipmsg_ = nullptr;
  }
  evsid_ = from.evsid_;
  // @@protoc_insertion_point(copy_constructor:gcu_evs_hmi.protobuf.EVSSysCtrl)
}

inline void EVSSysCtrl::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&electricctrl_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&evsid_) -
    reinterpret_cast<char*>(&electricctrl_)) + sizeof(evsid_));
}

EVSSysCtrl::~EVSSysCtrl() {
  // @@protoc_insertion_point(destructor:gcu_evs_hmi.protobuf.EVSSysCtrl)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVSSysCtrl::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete electricctrl_;
  if (this != internal_default_instance()) delete insultctrl_;
  if (this != internal_default_instance()) delete ipmsg_;
}

void EVSSysCtrl::ArenaDtor(void* object) {
  EVSSysCtrl* _this = reinterpret_cast< EVSSysCtrl* >(object);
  (void)_this;
}
void EVSSysCtrl::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVSSysCtrl::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVSSysCtrl::Clear() {
// @@protoc_insertion_point(message_clear_start:gcu_evs_hmi.protobuf.EVSSysCtrl)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && electricctrl_ != nullptr) {
    delete electricctrl_;
  }
  electricctrl_ = nullptr;
  if (GetArenaForAllocation() == nullptr && insultctrl_ != nullptr) {
    delete insultctrl_;
  }
  insultctrl_ = nullptr;
  if (GetArenaForAllocation() == nullptr && ipmsg_ != nullptr) {
    delete ipmsg_;
  }
  ipmsg_ = nullptr;
  evsid_ = uint64_t{0u};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVSSysCtrl::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 evsID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          evsid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .EVSInfo.EVElectricCtrl electricCtrl = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_electricctrl(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .EVSInfo.EVInsultCtrl insultCtrl = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_insultctrl(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .EVSInfo.IpMsg ipMsg = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_ipmsg(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVSSysCtrl::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gcu_evs_hmi.protobuf.EVSSysCtrl)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 evsID = 1;
  if (this->_internal_evsid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_evsid(), target);
  }

  // .EVSInfo.EVElectricCtrl electricCtrl = 2;
  if (this->_internal_has_electricctrl()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::electricctrl(this), target, stream);
  }

  // .EVSInfo.EVInsultCtrl insultCtrl = 3;
  if (this->_internal_has_insultctrl()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::insultctrl(this), target, stream);
  }

  // .EVSInfo.IpMsg ipMsg = 4;
  if (this->_internal_has_ipmsg()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::ipmsg(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gcu_evs_hmi.protobuf.EVSSysCtrl)
  return target;
}

size_t EVSSysCtrl::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gcu_evs_hmi.protobuf.EVSSysCtrl)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .EVSInfo.EVElectricCtrl electricCtrl = 2;
  if (this->_internal_has_electricctrl()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *electricctrl_);
  }

  // .EVSInfo.EVInsultCtrl insultCtrl = 3;
  if (this->_internal_has_insultctrl()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *insultctrl_);
  }

  // .EVSInfo.IpMsg ipMsg = 4;
  if (this->_internal_has_ipmsg()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ipmsg_);
  }

  // uint64 evsID = 1;
  if (this->_internal_evsid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_evsid());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVSSysCtrl::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVSSysCtrl::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVSSysCtrl::GetClassData() const { return &_class_data_; }

void EVSSysCtrl::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVSSysCtrl *>(to)->MergeFrom(
      static_cast<const EVSSysCtrl &>(from));
}


void EVSSysCtrl::MergeFrom(const EVSSysCtrl& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gcu_evs_hmi.protobuf.EVSSysCtrl)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_electricctrl()) {
    _internal_mutable_electricctrl()->::EVSInfo::EVElectricCtrl::MergeFrom(from._internal_electricctrl());
  }
  if (from._internal_has_insultctrl()) {
    _internal_mutable_insultctrl()->::EVSInfo::EVInsultCtrl::MergeFrom(from._internal_insultctrl());
  }
  if (from._internal_has_ipmsg()) {
    _internal_mutable_ipmsg()->::EVSInfo::IpMsg::MergeFrom(from._internal_ipmsg());
  }
  if (from._internal_evsid() != 0) {
    _internal_set_evsid(from._internal_evsid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVSSysCtrl::CopyFrom(const EVSSysCtrl& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gcu_evs_hmi.protobuf.EVSSysCtrl)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVSSysCtrl::IsInitialized() const {
  return true;
}

void EVSSysCtrl::InternalSwap(EVSSysCtrl* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVSSysCtrl, evsid_)
      + sizeof(EVSSysCtrl::evsid_)
      - PROTOBUF_FIELD_OFFSET(EVSSysCtrl, electricctrl_)>(
          reinterpret_cast<char*>(&electricctrl_),
          reinterpret_cast<char*>(&other->electricctrl_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVSSysCtrl::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fHMI_2eproto_getter, &descriptor_table_GCU_5fEVS_5fHMI_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fHMI_2eproto[6]);
}

// ===================================================================

class EVSStepMsgSet::_Internal {
 public:
  static const ::EVSInfo::EVSStepMsg& stepmsg(const EVSStepMsgSet* msg);
};

const ::EVSInfo::EVSStepMsg&
EVSStepMsgSet::_Internal::stepmsg(const EVSStepMsgSet* msg) {
  return *msg->stepmsg_;
}
void EVSStepMsgSet::clear_stepmsg() {
  if (GetArenaForAllocation() == nullptr && stepmsg_ != nullptr) {
    delete stepmsg_;
  }
  stepmsg_ = nullptr;
}
EVSStepMsgSet::EVSStepMsgSet(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:gcu_evs_hmi.protobuf.EVSStepMsgSet)
}
EVSStepMsgSet::EVSStepMsgSet(const EVSStepMsgSet& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_stepmsg()) {
    stepmsg_ = new ::EVSInfo::EVSStepMsg(*from.stepmsg_);
  } else {
    stepmsg_ = nullptr;
  }
  evsid_ = from.evsid_;
  // @@protoc_insertion_point(copy_constructor:gcu_evs_hmi.protobuf.EVSStepMsgSet)
}

inline void EVSStepMsgSet::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&stepmsg_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&evsid_) -
    reinterpret_cast<char*>(&stepmsg_)) + sizeof(evsid_));
}

EVSStepMsgSet::~EVSStepMsgSet() {
  // @@protoc_insertion_point(destructor:gcu_evs_hmi.protobuf.EVSStepMsgSet)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVSStepMsgSet::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete stepmsg_;
}

void EVSStepMsgSet::ArenaDtor(void* object) {
  EVSStepMsgSet* _this = reinterpret_cast< EVSStepMsgSet* >(object);
  (void)_this;
}
void EVSStepMsgSet::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVSStepMsgSet::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVSStepMsgSet::Clear() {
// @@protoc_insertion_point(message_clear_start:gcu_evs_hmi.protobuf.EVSStepMsgSet)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && stepmsg_ != nullptr) {
    delete stepmsg_;
  }
  stepmsg_ = nullptr;
  evsid_ = uint64_t{0u};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVSStepMsgSet::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 evsID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          evsid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .EVSInfo.EVSStepMsg stepMsg = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_stepmsg(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVSStepMsgSet::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gcu_evs_hmi.protobuf.EVSStepMsgSet)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 evsID = 1;
  if (this->_internal_evsid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_evsid(), target);
  }

  // .EVSInfo.EVSStepMsg stepMsg = 2;
  if (this->_internal_has_stepmsg()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::stepmsg(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gcu_evs_hmi.protobuf.EVSStepMsgSet)
  return target;
}

size_t EVSStepMsgSet::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gcu_evs_hmi.protobuf.EVSStepMsgSet)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .EVSInfo.EVSStepMsg stepMsg = 2;
  if (this->_internal_has_stepmsg()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *stepmsg_);
  }

  // uint64 evsID = 1;
  if (this->_internal_evsid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_evsid());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVSStepMsgSet::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVSStepMsgSet::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVSStepMsgSet::GetClassData() const { return &_class_data_; }

void EVSStepMsgSet::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVSStepMsgSet *>(to)->MergeFrom(
      static_cast<const EVSStepMsgSet &>(from));
}


void EVSStepMsgSet::MergeFrom(const EVSStepMsgSet& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gcu_evs_hmi.protobuf.EVSStepMsgSet)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_stepmsg()) {
    _internal_mutable_stepmsg()->::EVSInfo::EVSStepMsg::MergeFrom(from._internal_stepmsg());
  }
  if (from._internal_evsid() != 0) {
    _internal_set_evsid(from._internal_evsid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVSStepMsgSet::CopyFrom(const EVSStepMsgSet& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gcu_evs_hmi.protobuf.EVSStepMsgSet)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVSStepMsgSet::IsInitialized() const {
  return true;
}

void EVSStepMsgSet::InternalSwap(EVSStepMsgSet* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVSStepMsgSet, evsid_)
      + sizeof(EVSStepMsgSet::evsid_)
      - PROTOBUF_FIELD_OFFSET(EVSStepMsgSet, stepmsg_)>(
          reinterpret_cast<char*>(&stepmsg_),
          reinterpret_cast<char*>(&other->stepmsg_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVSStepMsgSet::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fHMI_2eproto_getter, &descriptor_table_GCU_5fEVS_5fHMI_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fHMI_2eproto[7]);
}

// ===================================================================

class EVSGetMsg::_Internal {
 public:
};

EVSGetMsg::EVSGetMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:gcu_evs_hmi.protobuf.EVSGetMsg)
}
EVSGetMsg::EVSGetMsg(const EVSGetMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&evsid_, &from.evsid_,
    static_cast<size_t>(reinterpret_cast<char*>(&msgid_) -
    reinterpret_cast<char*>(&evsid_)) + sizeof(msgid_));
  // @@protoc_insertion_point(copy_constructor:gcu_evs_hmi.protobuf.EVSGetMsg)
}

inline void EVSGetMsg::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&evsid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&msgid_) -
    reinterpret_cast<char*>(&evsid_)) + sizeof(msgid_));
}

EVSGetMsg::~EVSGetMsg() {
  // @@protoc_insertion_point(destructor:gcu_evs_hmi.protobuf.EVSGetMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVSGetMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void EVSGetMsg::ArenaDtor(void* object) {
  EVSGetMsg* _this = reinterpret_cast< EVSGetMsg* >(object);
  (void)_this;
}
void EVSGetMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVSGetMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVSGetMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:gcu_evs_hmi.protobuf.EVSGetMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&evsid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&msgid_) -
      reinterpret_cast<char*>(&evsid_)) + sizeof(msgid_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVSGetMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 evsID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          evsid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 msgID = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          msgid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint64 getTime = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          gettime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVSGetMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gcu_evs_hmi.protobuf.EVSGetMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 evsID = 1;
  if (this->_internal_evsid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_evsid(), target);
  }

  // uint32 msgID = 2;
  if (this->_internal_msgid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_msgid(), target);
  }

  // uint64 getTime = 3;
  if (this->_internal_gettime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(3, this->_internal_gettime(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gcu_evs_hmi.protobuf.EVSGetMsg)
  return target;
}

size_t EVSGetMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gcu_evs_hmi.protobuf.EVSGetMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint64 evsID = 1;
  if (this->_internal_evsid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_evsid());
  }

  // uint64 getTime = 3;
  if (this->_internal_gettime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_gettime());
  }

  // uint32 msgID = 2;
  if (this->_internal_msgid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_msgid());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVSGetMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVSGetMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVSGetMsg::GetClassData() const { return &_class_data_; }

void EVSGetMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVSGetMsg *>(to)->MergeFrom(
      static_cast<const EVSGetMsg &>(from));
}


void EVSGetMsg::MergeFrom(const EVSGetMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gcu_evs_hmi.protobuf.EVSGetMsg)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_evsid() != 0) {
    _internal_set_evsid(from._internal_evsid());
  }
  if (from._internal_gettime() != 0) {
    _internal_set_gettime(from._internal_gettime());
  }
  if (from._internal_msgid() != 0) {
    _internal_set_msgid(from._internal_msgid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVSGetMsg::CopyFrom(const EVSGetMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gcu_evs_hmi.protobuf.EVSGetMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVSGetMsg::IsInitialized() const {
  return true;
}

void EVSGetMsg::InternalSwap(EVSGetMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVSGetMsg, msgid_)
      + sizeof(EVSGetMsg::msgid_)
      - PROTOBUF_FIELD_OFFSET(EVSGetMsg, evsid_)>(
          reinterpret_cast<char*>(&evsid_),
          reinterpret_cast<char*>(&other->evsid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVSGetMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fHMI_2eproto_getter, &descriptor_table_GCU_5fEVS_5fHMI_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fHMI_2eproto[8]);
}

// ===================================================================

class EVSSetReply::_Internal {
 public:
};

EVSSetReply::EVSSetReply(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:gcu_evs_hmi.protobuf.EVSSetReply)
}
EVSSetReply::EVSSetReply(const EVSSetReply& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&evsid_, &from.evsid_,
    static_cast<size_t>(reinterpret_cast<char*>(&setack_) -
    reinterpret_cast<char*>(&evsid_)) + sizeof(setack_));
  // @@protoc_insertion_point(copy_constructor:gcu_evs_hmi.protobuf.EVSSetReply)
}

inline void EVSSetReply::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&evsid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&setack_) -
    reinterpret_cast<char*>(&evsid_)) + sizeof(setack_));
}

EVSSetReply::~EVSSetReply() {
  // @@protoc_insertion_point(destructor:gcu_evs_hmi.protobuf.EVSSetReply)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EVSSetReply::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void EVSSetReply::ArenaDtor(void* object) {
  EVSSetReply* _this = reinterpret_cast< EVSSetReply* >(object);
  (void)_this;
}
void EVSSetReply::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EVSSetReply::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EVSSetReply::Clear() {
// @@protoc_insertion_point(message_clear_start:gcu_evs_hmi.protobuf.EVSSetReply)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&evsid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&setack_) -
      reinterpret_cast<char*>(&evsid_)) + sizeof(setack_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EVSSetReply::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 evsID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          evsid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 msgID = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          msgid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 setAck = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          setack_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EVSSetReply::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gcu_evs_hmi.protobuf.EVSSetReply)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 evsID = 1;
  if (this->_internal_evsid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_evsid(), target);
  }

  // uint32 msgID = 2;
  if (this->_internal_msgid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_msgid(), target);
  }

  // uint32 setAck = 3;
  if (this->_internal_setack() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_setack(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gcu_evs_hmi.protobuf.EVSSetReply)
  return target;
}

size_t EVSSetReply::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gcu_evs_hmi.protobuf.EVSSetReply)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint64 evsID = 1;
  if (this->_internal_evsid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_evsid());
  }

  // uint32 msgID = 2;
  if (this->_internal_msgid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_msgid());
  }

  // uint32 setAck = 3;
  if (this->_internal_setack() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_setack());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EVSSetReply::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EVSSetReply::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EVSSetReply::GetClassData() const { return &_class_data_; }

void EVSSetReply::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<EVSSetReply *>(to)->MergeFrom(
      static_cast<const EVSSetReply &>(from));
}


void EVSSetReply::MergeFrom(const EVSSetReply& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gcu_evs_hmi.protobuf.EVSSetReply)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_evsid() != 0) {
    _internal_set_evsid(from._internal_evsid());
  }
  if (from._internal_msgid() != 0) {
    _internal_set_msgid(from._internal_msgid());
  }
  if (from._internal_setack() != 0) {
    _internal_set_setack(from._internal_setack());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EVSSetReply::CopyFrom(const EVSSetReply& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gcu_evs_hmi.protobuf.EVSSetReply)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EVSSetReply::IsInitialized() const {
  return true;
}

void EVSSetReply::InternalSwap(EVSSetReply* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EVSSetReply, setack_)
      + sizeof(EVSSetReply::setack_)
      - PROTOBUF_FIELD_OFFSET(EVSSetReply, evsid_)>(
          reinterpret_cast<char*>(&evsid_),
          reinterpret_cast<char*>(&other->evsid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EVSSetReply::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fEVS_5fHMI_2eproto_getter, &descriptor_table_GCU_5fEVS_5fHMI_2eproto_once,
      file_level_metadata_GCU_5fEVS_5fHMI_2eproto[9]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace protobuf
}  // namespace gcu_evs_hmi
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::gcu_evs_hmi::protobuf::EVSLogin* Arena::CreateMaybeMessage< ::gcu_evs_hmi::protobuf::EVSLogin >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gcu_evs_hmi::protobuf::EVSLogin >(arena);
}
template<> PROTOBUF_NOINLINE ::gcu_evs_hmi::protobuf::EVSLoginAns* Arena::CreateMaybeMessage< ::gcu_evs_hmi::protobuf::EVSLoginAns >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gcu_evs_hmi::protobuf::EVSLoginAns >(arena);
}
template<> PROTOBUF_NOINLINE ::gcu_evs_hmi::protobuf::EVSHb* Arena::CreateMaybeMessage< ::gcu_evs_hmi::protobuf::EVSHb >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gcu_evs_hmi::protobuf::EVSHb >(arena);
}
template<> PROTOBUF_NOINLINE ::gcu_evs_hmi::protobuf::EVSHbReply* Arena::CreateMaybeMessage< ::gcu_evs_hmi::protobuf::EVSHbReply >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gcu_evs_hmi::protobuf::EVSHbReply >(arena);
}
template<> PROTOBUF_NOINLINE ::gcu_evs_hmi::protobuf::EVSMsgSet* Arena::CreateMaybeMessage< ::gcu_evs_hmi::protobuf::EVSMsgSet >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gcu_evs_hmi::protobuf::EVSMsgSet >(arena);
}
template<> PROTOBUF_NOINLINE ::gcu_evs_hmi::protobuf::EVSMsgCtrl* Arena::CreateMaybeMessage< ::gcu_evs_hmi::protobuf::EVSMsgCtrl >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gcu_evs_hmi::protobuf::EVSMsgCtrl >(arena);
}
template<> PROTOBUF_NOINLINE ::gcu_evs_hmi::protobuf::EVSSysCtrl* Arena::CreateMaybeMessage< ::gcu_evs_hmi::protobuf::EVSSysCtrl >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gcu_evs_hmi::protobuf::EVSSysCtrl >(arena);
}
template<> PROTOBUF_NOINLINE ::gcu_evs_hmi::protobuf::EVSStepMsgSet* Arena::CreateMaybeMessage< ::gcu_evs_hmi::protobuf::EVSStepMsgSet >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gcu_evs_hmi::protobuf::EVSStepMsgSet >(arena);
}
template<> PROTOBUF_NOINLINE ::gcu_evs_hmi::protobuf::EVSGetMsg* Arena::CreateMaybeMessage< ::gcu_evs_hmi::protobuf::EVSGetMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gcu_evs_hmi::protobuf::EVSGetMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::gcu_evs_hmi::protobuf::EVSSetReply* Arena::CreateMaybeMessage< ::gcu_evs_hmi::protobuf::EVSSetReply >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gcu_evs_hmi::protobuf::EVSSetReply >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
