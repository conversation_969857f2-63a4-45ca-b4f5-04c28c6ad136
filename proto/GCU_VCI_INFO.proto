syntax = "proto3";
import public "GCU_AllFaultEnum.proto";
import public "GCU_BMS_INFO.proto";
import public "GCU_DMC_INFO.proto";
package VCIinfo;

//接触器状态枚举
enum ContactorStateEnum {
	UnKown = 0x00;					//	未自检，未知状态缺省值
	DriveFailure = 0x01;			//	故障，驱动失效
	ContactorAdhesion = 0x02;		//	故障，触点粘连
	ContactorReady = 0x05;			//	未吸合
	ContactorCharging = 0x06;		//	已吸合
	Aggregation = 0x07;				//	汇聚模式，主接触器专用
}

//电枪匹配状态机
enum gunMatchState {
    UnKownState = 0x00;            // 缺省值
    gunMatchStart = 0x1;            // 电枪匹配开始
    OutputVoltOk = 0x2;             // 输出电压完成
    VoltSampledOk = 0x3;          // 电压采集ok
    gunMatchOk = 0x4;               // 电枪匹配成功
    gunMatchFail = 0x5;              // 电枪匹配失败
    gunMatchTimeout = 0x6;     // 电枪匹配超时
    gunMatchEndOk =  0x7;       // 电枪匹配结束，成功
    gunMatchEndFail =  0x8;      // 电枪匹配结束，失败
}

//VCI枪头连接阶段状态信息
message GunConnectState {
    uint32 terminalID = 1;			// 终端ID，重复仅OHP填充
    uint32 gunID = 2;               // gun ID，重复仅OHP填充
    uint32 auxDriver = 3;           // BMS辅助电源驱动
    uint32 auxFeedback = 4;         // BMS辅助电源反馈
    uint32 eLockDriver = 5;         // 电子锁驱动
    uint32 eLockFeedback = 6;       // 电子锁反馈
    uint32 auxRtType = 7;           // 辅源当前类型
    float tempPositive = 8;         // 枪头正极温度
    float tempNegative = 9;         // 枪头负极温度
    uint32 ElockMode = 10;          // 电子锁工作模式
    uint32 positionedState = 11;    // 在位/归位状态,0表示未归位,1表示已归位
    uint32 dualState = 12;          // 双枪确认状态,0表示空闲,1表示已确认双枪
    uint32 linkState = 13;          // 插枪状态,0表示空闲,1表示已插枪
    DMCinfo.GunProEnum GbtProType = 14; // 国标类型
}

//VCI枪头连接阶段系统指令
message ConnectStageSysCtrl {
    uint32 terminalID = 1;			// 终端ID,重复仅OHP填充
	uint32 gunID = 2;               // gun ID,重复仅OHP填充
	uint32 elockCmd = 3;			// 电子锁指令(0x55表示解锁,0xAA表示上锁)
	uint32 startType = 4;			// 开机类型(0表示扫码开机,1表示骗取VIN开机，2表示刷卡开机，3表示云端开机,4表示VIN开机)
	uint32 AuxType = 5;				// 辅源类型(0表示12v,1表示切换24v)
	uint32 ElockMode = 6;			// 电子锁工作模式
}

//VCI解析BMS需求
message VCIsendBMSDemand {
    float VDemand = 1;				//	需求电压 
    float IDemand = 2;				//	需求电流
    float VPTPDemand = 3;			//	模块PTP开机电压 
    float IPTPDemand = 4;			//	模块PTP开机电流
    float batVoltage = 5;			//	输出接触器后级电压 
    float modVoltage = 6;			//	输出接触器前级电压
}

//充电线程恢复数据
message ChargerRevive {
    uint32 isCharging = 1;              // 充电机状态(开机/关机)(Daniel 1004)
    uint32 isVINStart = 2;              // 是否是VIN启机 (Daniel 1004)
    uint32 faultState1 = 3;             // 故障列表1 与枪故障状态按位一一对应
    uint32 faultState2 = 4;             // 故障列表2
    uint32 faultState3 = 5;             // 故障列表3
    
    uint32 bmsCommState = 6;            // BMS交互状态
    uint32 bmsRecvState = 7;            // 接收BMS报文状态
    uint32 bmsType = 8;                // BMS类型(实车/模拟器)
    uint32 bmsTimeoutCnt = 9;          // BMS超时次数
    
    uint32 elockState = 10;             // 电子锁状态(开/关)
    uint32 auxPowerState = 11;          // BMS辅源状态(开/关)
    uint32 insultState = 12;            // 绝缘检测状态
    uint32 insultResult = 13;           // 绝缘检测结果(Pass/Warn/Fail)
    
    float bmsCurrentMax = 14;           // BMS最大允许充电电流
    float bmsVoltageMax = 15;           // BMS最大允许充电电压
    float cellVoltageMax = 16;          // 单体允许最大电压
    float cellTempMax = 17;             // 单体允许最大温度
    float insultVoltage = 18;           // 绝缘检测电压
}

//输出接触器状态
message ContactorState {
    uint32 gunNum = 1;                                                      //   真实枪号
    ContactorStateEnum  ContactorState = 2;           //    接触器状态
}

//真实枪当前模块分配状态
message CurrModAllocState {
    uint32 gunNum = 1;                                                      //  真实枪号
    uint32 MainSize = 2;                                                     //   当前模块分配状态
}
