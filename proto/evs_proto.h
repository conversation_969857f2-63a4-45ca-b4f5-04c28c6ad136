/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class evs_charger Declaration here.
 */
#ifndef _EVS_PROTO_H
#define _EVS_PROTO_H

#include <iostream>
#include <fcntl.h>
#include <unistd.h>
#include <sched.h>
#include <time.h>
#include <pthread.h>
#include <sys/socket.h>
#include <sys/time.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <vector>
#include <string>
#include "evs_common.h"
#include <map>

#define PROTOCOL_FRAM_HEAD 0xFA
#define EVS_SOCKET_PORT 9955        // evs port
#define EVS_SOCKET_IP "**************" // evs ip
#define EVS_LOCAL_IP "*************"   //  本地IP

#define HMI_SOCKET_PORT 9966        // HMI port
#define HMI_SOCKET_IP "127.0.0.1"   //  HMI ip

#define MAX_SERVER_NUM   2

#define FRAME_BUFF_SIZE 4096
#define UPDATED 1
#define NOTUPDATE 0

#pragma pack(1)
typedef struct
{
    uint8_t head;    // 0xFA
    uint16_t len;    // data len (exclude header)
    uint8_t cmd;     // command
    uint8_t data[0]; // data
} Protocol_Buff_Fram;

typedef struct
{
    int32_t local_port;
    int32_t target_port;
    int32_t Socket_fd;
    std::string ip_addr;
    sockaddr_in Local_Socket_addr;
    sockaddr_in Target_Socket_addr;
} Socket_Param;

typedef union
{
    uint8_t data_buff[FRAME_BUFF_SIZE];
    struct
    {
        Protocol_Buff_Fram Fram;
        uint8_t reserve[FRAME_BUFF_SIZE - 4];
    } fram_buff;
} ProtocolFrameBuff;

/***********************evs通信帧枚举**************************/
typedef enum
{
    TYPE_EVSMODULE_REGISTER         = 0X00,     // EVS模块注册
    TYPE_EVSMODULE_REGISTER_ACK     = 0X10,     // EVS模块注册响应
    TYPE_CONTROL_CMD                = 0X11,     // 控制指令
    TYPE_CONTROL_CMD_RSP            = 0X01,     // 控制响应指令
    TYPE_VEHICLE_SIDE_MSG_FILL      = 0X02,     // 车辆报文填充
    TYPE_MSG_ATTR                   = 0X03,     // 报文属性
    TYPE_ELECTRI_CTRL               = 0X04,     // 电气控制
    TYPE_STEP_INFO                  = 0X05,     // 步进信息
    TYPE_GET_MSG                         = 0x1E,      // 报文查询
    TYPE_MSG_FILL_ACK               = 0X1F,     // 报文填充响应（0x02, 0x03, 0x04, 0x05）
} EvsFrameMessageEnum;

/*设备类型枚举*/
typedef enum
{
    E_DEV_EVS = 0x0,            // 设备类型为EVS
    E_DEV_HMI = 0x1,           //  设备类型为HMI
}DeviceTypeEnum;

/*********************evs_info******************************/
// //ProtoConferMsg-版本协商信息
typedef struct
{
    uint32_t canType;          // CAN类型 (默认0x00): CAN2.0: 0x00; CAN FD:0x01; CANXL:0x02
    std::string gbVersion;          // 车辆期望或协商一致的版本号(默认V2.0.0) GB2015(V1.0.0),GB2015p(V2.0.0) // 协议版本号 BYTE1:主版本号，BYTE2：次版本号，BYTE3：临时版本号
    uint32_t guidanceVersion;  // 车辆控制导引版本 默认0x01:GB2015p ; 0x02:GB2015
    uint32_t transportVersion; // 车辆传输层版本 默认 0x01: 2015+; 0xFF 其他
    uint32_t conferRes;        // 预留 默认 0xFF
} EVProtoConferMsgInfo;

// FunConfer-功能协商
typedef struct
{
    uint32_t configFDC;         // 车辆对配置功能支持情况 (默认0x01) FDC1:充电;  FDC2: 充放电
    uint32_t authenFDC;         // 车辆对鉴权功能支持情况 (默认0x01) FDC1:扫码/刷卡; FDC2 : EVIN鉴权;  FDC3: 云端鉴权-桩识别码
    uint32_t appointFDC;        // 车辆对预约功能支持情况 (默认0x00)
    uint32_t selfCheckFDC;      // 车辆对输出回路检测功能支持情况 (默认0x01)
    uint32_t powerSupplyFDC;    // 车辆对供电模式功能支持情况 (默认0x01)
    uint32_t energyTransferFDC; // 车辆对预充及能量传输功能支持情况 (默认0x01) FDC1:充电模式 FDC2:充放电模式
    uint32_t endFDC;            // 车辆对结束功能支持情况 (默认0x01)
} EVFunConferMsgInfo;

//车辆充电参数配置
typedef struct
{
    float currAllowMAX;        // 车辆最大允许充电电流   0A - 6500.0A
    float voltAllowMAX;        // 车辆最高允许充电电压   0V - 6500.0V
    float energyAllowMAX;   // 车辆最高允许输入总能量 0.1kw.h/位 0Kwh - 6500.0Kwh 0xFFFF:参数无效 0xFFFE：参数异常
    float nowSOC;              // 车辆荷电状态  0% - 100.0%
    float cellVoltAllowMAX; // 电池最小并联单元最高允许电压  0.01V/位 0V - 650.00V 0xFFFF:参数无效 0xFFFE：参数异常
    float batTempAllowMAX;  // 电池单体最高允许温度 1℃/位 -50 ℃偏移 -50℃ ~ 200℃  0xFF：参数无效 0xFE：数据异常
    uint32_t restarNum;        // 重新启动次数 0 - 200次 0xFE：次数不限， 0xFF：参数无效
} EVChgParaConfigMsgInfo;

// 鉴权信息
typedef struct
{
    uint32_t authenWaitTime; // 总鉴权等待时间  默认600s
    std::string eVIN;             // 车辆识别码EVIN
    uint32_t nextFDC;        // 重新鉴权的FDC: 0:无重新鉴权；1~8:FDC功能模块
} EVAuthenMsgInfo;

// 预约充电信息
typedef struct
{
    uint32_t bmsDesireStartTime;     // 车辆期望开始充电时间 min 0~65000min, 0xFFFF：充电机对此内容不做判定
    uint32_t bmsDesireLeaveTime;     // 车辆期望出发时间 min 0~65000min, 0xFFFF：充电机对此内容不做判定
    uint32_t reserveResult;          // 预约协商充电结果 0xAA:预约协商成功; 0xFF 预约协商失败;
    uint32_t immediateChargeSupport; // 立即充电支持 0x00 不支持, 0xAA:支持;
} EVReserveMsgInfo;

// 供电模式
typedef struct
{
    uint32_t supplyState;   // 车辆供电状态 0x00:未就绪，0x01 :就绪
    float supplyVolDesire;  // 供电电压需求 0.0 - 6500.0V
    float supplyCurrDesire; // 供电电流需求 0.0 - 6500.0A
    uint32_t supplyEnd;     // 供电模式结束请求:0xAA,结束供电;其他，继续供电
} EVPowerSupplyMsgInfo;

// 充电阶段信息
typedef struct
{
    uint32_t bmsReady;      // BMS就绪状态 0x00未就绪；0xAA就绪
    float volDemand;        // 需求电压0.0 - 6500.0V
    float curDemand;        // 需求电流0.0 - 6500.0A
    uint32_t chargeMode;    // 充电模式：0x01恒流，0x02恒压
    float socNow;           // 当前SOC
    uint32_t resChgTime;    // 剩余估算时间，0~65000min
    float cellBatVolMax; // 单体电池最高电压 0.01 V/位 ：0 V～650.00 V 0xFFFF：参数无效 0xFFFE：数据异常
    float cellBatVolMin; // 单体电池最低电压 0.01 V/位 ：0 V～650.00 V 0xFFFF：参数无效 0xFFFE：数据异常
    float celltempMax;   // 电池最高温度 1℃/位 -50 ℃偏移 -50℃ ~ 200℃  0xFF：参数无效 0xFE：数据异常
    float celltempMin;   // 电池最低温度 1℃/位 -50 ℃偏移 -50℃ ~ 200℃  0xFF：参数无效 0xFE：数据异常
} EVChargingMsgInfo;

// 充电结束信息
typedef struct
{
    uint32_t endType;            // 中止类型 默认0x00
    uint32_t endCode;            // 中止码 默认0x00
    uint32_t endReason;          // 中止原因 默认0x00
    uint32_t repeat;             // 请求重连 默认0x00：不请求重连，0xAA请求重连，0xFF无效
    uint32_t bmsStickCheckState; // 车辆粘连检测状态0:自动；1:检测中;2:异常中止;3:检测通过;4:检测失败;0xFF:本次不检测
} EVChargingEndMsgInfo;

// PhaseACK-阶段确认
typedef struct
{
    uint32_t funConferAck;      // 功能协商功能确认结果
    uint32_t configAck;         // 配置功能确认结果
    uint32_t authenAck;         // 鉴权功能确认结果
    uint32_t appointAck;        // 预约功能确认结果
    uint32_t selfCheckAck;      // 输出回路检测功能确认结果
    uint32_t powerSupplyAck;    // 供电模式功能确认结果
    uint32_t energyTransferAck; // 预充及能量传输功能确认结果
    uint32_t endAck;            // 结束功能确认结果
} EVFunConferAckMsgInfo;

// EVDeviceMsg 车辆电气控制
typedef struct
{
    uint32_t contactK5;   // K5状态  默认0x00:断开， 0xAA:闭合
    uint32_t contactK6;   // K6状态  默认0x00:断开， 0xAA:闭合
    uint32_t CC1_S2;      // S2状态  默认0x00:断开， 0xAA:闭合
    uint32_t CC2_S3;      // S3状态  默认0x00:断开， 0xAA:闭合
    uint32_t elockState;  // 车辆电子锁状态 默认未锁止0x00,锁止0xAA
    uint32_t canBus;      // CANBus状态  默认 0xAA:闭合， 0x00:断开
    uint32_t ev_PE;       // PE断线状态  默认 0xAA:闭合， 0x00:断开
    uint32_t evInsultOn;  // 车辆绝缘开启  默认0x00:关闭, 0xAA:打开
    uint32_t evContactK6; // 车辆绝缘检测周期  min 0:连续做，0~200min
    uint32_t evPause;     // 车辆暂停:0xAA 暂停，0x00 恢复
    uint32_t sysFan;        //  系统风机: 0 关机，0x1 开机
} EVElectricCtrlInfo;

// EVInsult 绝缘检测控制
typedef struct
{
    uint32_t insultPosRes; // DC+ 阻抗 kΩ
    uint32_t insultNegRes; // DC- 阻抗 kΩ
    float batVol;          // 电池电压 0.0 - 6500.0V
    uint32_t connectType;  // 连接方式：默认 0x00 正接； 0x01 反接； 0x02 开路
} EVInsultCtrlInfo;

typedef struct
{
    std::string      locolIP;              //本地IP
    uint32_t locolPort;         //本地端口
    std::string      serverIP;           //serverIP
    uint32_t serverPort;      //server端口
} EVIPConfig;

// EVState 车辆状态
typedef struct
{
    float batVol;      // 电池电压
    float batCur;      // 电池电流
    float nowSOC;      // 当前SOC
    uint32_t nowFC;    // 当前FC 0x0: 版本协商; 0x10:功能协商; 0x20 参数配置;  0x30鉴权; 0x40 预约; 0x50 输出回路检测 0x60 供电模式; 0x70 预充及能量传输; 0x80 结束;
    uint32_t nowFDC;   // 当前FDC
    uint32_t chgMode;  // 充电模式 （恒压 恒流）
    float insultPosR;  // DC+ 阻抗 kΩ
    float insultNegR;  // DC- 阻抗 kΩ
    float cc1Volt;
    uint32_t workMode; // 工作模式： 1 正常模式；2 步进模式
    uint32_t stopCode; // 中止码
    float cc2Volt;             //  CC2电压值
} EVStateMsgInfo;

// EVStepMsg 车辆步进参数
typedef struct
{
    uint64_t startTime;    // 开始时间 *s ( 0:立马生效，10: 10s后生效)
    uint64_t intervalTime; // 间隔时间 *s
    float startValue;      // 起始值
    float minValue;        // 最小值
    float maxValue;        // 最大值
    float stepValue;       // 步进值
    uint32_t cycleMode;    // 循环模式： 0x00 关闭 0xAA 打开
    uint32_t stepMode;        // 步进模式(默认 0x00)： 0x00 关闭 0xAA 打开

} StepParaBuff;

// EVSStepMsg
typedef struct
{
    StepParaBuff needVolStep;  // 需求电压步进
    StepParaBuff needCurrStep; // 需求电流步进
    StepParaBuff socStep;      // soc步进
    StepParaBuff cellVolStep;  // 单体电压步进
    StepParaBuff cellTempStep; // 单体温度步进
} EVSStepMsgInfo;

// 报文状态设置
typedef struct
{
    uint32_t x2_0x02_State;  // 车辆确认结果报文
    uint32_t x4_0x04_State;  // 车辆中止报文
    uint32_t x6_0x06_State;  // 车辆充电回路接触器状态报文
    uint32_t x9_0x09_State;  // 车辆唤醒报文
    uint32_t b2_0x12_State;  // 车辆功能协商确认结果报文
    uint32_t c2_0x22_State;  // 车辆充电参数报文（FDC = 1）
    uint32_t c4_0x24_State;  // 车辆充放电参数报文（FDC = 2）
    uint32_t d2_0x32_State;  // 车辆鉴权等待报文（FDC = 1）
    uint32_t d4_0x34_State;  // 车辆鉴权参数报文（FDC = 2）
    uint32_t d6_0x36_State;  // 重新鉴权请求报文（FDC = 2）
    uint32_t d7_0x37_State;  // 车辆鉴权参数报文（FDC = 3）
    uint32_t d9_0x39_State;  // 鉴权结果报文（FDC = 3）
    uint32_t d10_0x3A_State; // 重新鉴权请求报文（FDC = 3）
    uint32_t e2_0x42_State;  // 车辆预约充电信息报文（FDC )
    uint32_t e4_0x44_State;  // 车辆预约充电协商报文（FDC = 1）
    uint32_t f2_0x52_State;  // 检测确认报文（FDC = 1）
    uint32_t g2_0x62_State;  // 车辆供电状态报文（FDC = 1）
    uint32_t g3_0x63_State;  // 车辆供电需求报文（FDC = 1）
    uint32_t g5_0x65_State;  // 车辆供电完成报文
    uint32_t h2_0x72_State;  // 车辆就绪状态报文（FDC = 1）
    uint32_t h3_0x73_State;  // 车辆充电需求报文（FDC = 1）
    uint32_t h4_0x74_State;  // 车辆充电基本信息报文（FDC = 1）
    uint32_t h7_0x77_State;  // 车辆充电电池基本信息（FDC = 1）
    uint32_t h9_0x79_State;  // 车辆暂停报文（FDC = 1）
    uint32_t h11_0x82_State; // 车辆就绪状态报文（FDC = 2）
    uint32_t h13_0x84_State; // 车辆动态输出能力报文（FDC = 2）
    uint32_t h14_0x85_State; // 车辆充电需求报文（FDC = 2）
    uint32_t h16_0x87_State; // 车辆充放电基本信息报文（fDC = 2）
    uint32_t h18_0x89_State; // 车辆充放电电池基本信息（FDC = 2）
    uint32_t h20_0x8B_State; // 车辆暂停报文（FDC = 2）
    uint32_t i1_0x91_State;  // 车辆粘连检测报文（FDC = 1）
    uint32_t i4_0x94_State;  // 车辆统计报文（FDC = 1）
} EVMsgCtrlInfo;

/***********************evs通信帧**************************/
typedef struct{
    EVProtoConferMsgInfo protoConferM;  // 版本协商信息
    EVFunConferMsgInfo funConferM;      // 功能协商
    EVChgParaConfigMsgInfo paraConfigM; // 车辆充电参数配置
    EVAuthenMsgInfo authenM;            // 鉴权信息
    EVReserveMsgInfo reserveM;          // 预约充电信息
    EVPowerSupplyMsgInfo powerSupplyM;  // 供电模式
    EVChargingMsgInfo chargingM;        // 充电阶段信息
    EVChargingEndMsgInfo chargingEndM;  // 充电结束信息
    EVIPConfig   ipConfig;                                    //  IP配置信息
}StorageParaDef;

// EVS注册帧(0x0) evs-> hmi
typedef struct
{
    uint64_t evsID;                     // 模拟器ID
    uint32_t interval;                  // 心跳信息间隔（毫秒）
    EVProtoConferMsgInfo protoConferM;  // 版本协商信息
    EVFunConferMsgInfo funConferM;      // 功能协商
    EVChgParaConfigMsgInfo paraConfigM; // 车辆充电参数配置
    EVAuthenMsgInfo authenM;            // 鉴权信息
    EVReserveMsgInfo reserveM;          // 预约充电信息
    EVPowerSupplyMsgInfo powerSupplyM;  // 供电模式
    EVChargingMsgInfo chargingM;        // 充电阶段信息
    EVChargingEndMsgInfo chargingEndM;  // 充电结束信息
} EvsModuleRegister_t;

// 上位机注册回复帧.响应(0x10)  hmi->evs
typedef struct
{
    uint64_t evsID;    // 模拟器ID
    uint32_t serverID;  //服务器ID  1:上位机  2:HMI
    uint32_t interval; // 心跳信息间隔（毫秒）
} EvsModuleRegisterAck_t;

// EVS心跳(0x1)  evs ->hmi
typedef struct
{
    uint64_t evsID;          // 模拟器ID
    uint32_t heartbeatCnt;   // 心跳计数
    uint32_t onOffState;  //  EVS 状态: 0 关机 1 开机
    EVStateMsgInfo evsState; // 车辆实时状态

} ControlCmdRsp_t;

// EVS心跳响应(0x11)  hmi->evs
typedef struct
{
    uint64_t evsID;        // 模拟器ID
    uint32_t serverID;  //服务器ID  1:上位机  2:HMI
    uint32_t heartbeatCnt; //  心跳计数
    uint32_t evsCmd;       //  控制指令: 0x00 自动 0x01 开机 0x02 关机
    uint32_t offlineMode;  //  控制指令: 0x00 离线模式  0xAA 联网模式
    uint32_t evsReset;        //  复位指令: 0xAA 参数重置，其他无效

} ControlCmd_t;

// EVS报文填充(0x2) hmi->evs
typedef struct
{
    uint64_t evsID;                     // 模拟器ID
    EVProtoConferMsgInfo protoConferM;  // 版本协商信息
    EVFunConferMsgInfo funConferM;      // 功能协商
    EVChgParaConfigMsgInfo paraConfigM; // 车辆充电参数配置
    EVAuthenMsgInfo authenM;            // 鉴权信息
    EVReserveMsgInfo reserveM;          // 预约充电信息
    EVPowerSupplyMsgInfo powerSupplyM;  // 供电模式
    EVChargingMsgInfo chargingM;        // 充电阶段信息
    EVChargingEndMsgInfo chargingEndM;  // 充电结束信息
} VehicleSideMsgFill_t;

// EVS报文填充.响应(0x1F)  evs->hmi    报文填充响应（0x02, 0x03, 0x04, 0x05）
typedef struct
{
    uint64_t evsID;  //  模拟器ID
    uint32_t msgID;  //  消息ID
    uint32_t setAck; //  设置结果 0xAA 成功，0x00 失败
} MsgFillRsp_t;

// EVS报文控制(0x3) hmi->evs
typedef struct
{
    uint64_t evsID;                     // 模拟器ID
    EVMsgCtrlInfo msgCtrl;              // 是否发送 0：不发送，1 发送（默认值）
    EVMsgCtrlInfo msgCycleTime;         // 报文发送周期 ms
    EVMsgCtrlInfo msgMaxSendTIme;       // 报文最长发送时间 ms
    EVFunConferAckMsgInfo funConferAck; // 阶段确认报文设置
} MsgCtrl_t;

// EVS电气状态控制(0x4) hmi->evs
typedef struct
{
    uint64_t evsID;                  // 模拟器ID
    EVElectricCtrlInfo electricCtrl; // 车辆电气控制
    EVInsultCtrlInfo insultCtrl;     // 绝缘参数设置
    EVIPConfig   ipConfig;              //  IP配置
} ElectriCtrl_t;

// EVS步进信息设置(0x5) hmi->evs
typedef struct
{
    uint64_t evsID;         // 模拟器ID
    EVSStepMsgInfo stepMsg; // 步进信息

} StepMsgInfo_t;

// EVS信息查询(0x1E) hmi->evs
typedef struct
{
    uint64_t evsID;         //模拟器ID
    uint32_t MsgID;       //消息ID
    uint64_t getTime;   //查询计数
} GetMsgInfo_t;




class  EVSManageData{

public:
    EVSManageData();
    ~EVSManageData();
    EvsModuleRegister_t evsModuleRegisterManageBuff;
    ControlCmdRsp_t controlCmdRspManageBuff;
    MsgFillRsp_t msgFillRspManageBuff;

    EvsModuleRegisterAck_t evsModuleRegisterAckManageBuff;
    ControlCmd_t controlCmdManageBuff;
    VehicleSideMsgFill_t vehicleSideMsgFillManageBuff;//data send to DMC  (0x1E reply)
    MsgCtrl_t msgCtrlInfoManageBuff;
    ElectriCtrl_t electriCtrlManageBuff; // data send to  hmi
    StepMsgInfo_t stepMsgInfoManageBuff;
    GetMsgInfo_t   getMsgInfoManageBuff;

    VehicleSideMsgFill_t registerResetManageBuff;

    std::map<EvsFrameMessageEnum, uint8_t> upDateFlagMap = {
        {TYPE_EVSMODULE_REGISTER_ACK, 0},
        {TYPE_CONTROL_CMD, 1},
        {TYPE_VEHICLE_SIDE_MSG_FILL, 2},
        {TYPE_MSG_ATTR, 3},
        {TYPE_ELECTRI_CTRL, 4},
        {TYPE_STEP_INFO, 5},
        {TYPE_GET_MSG,6}
    };
    uint8_t upDateFlag = 0;
    uint64_t evsID = 0;
public:
    int32_t MsgStateInit(void);
    int32_t ElectStateInit(void);
    int32_t MsgInfoUpdate(void);
    int32_t EvsDataInit(void);



public:
    void setEvsModuleRegisterManageData(EvsModuleRegister_t);
    void setControlCmdRspManageData(ControlCmdRsp_t);
    void setMsgFillRspManageData(MsgFillRsp_t);

    void getEvsModuleRegisterAckManageData(EvsModuleRegisterAck_t &);
    void getControlCmdManageData(ControlCmd_t &);
    void getVehicleSideMsgFillManageData(VehicleSideMsgFill_t &);
    void getMsgAttrInfoManageData(MsgCtrl_t &);
    void getElectriCtrlManageData(ElectriCtrl_t &);
    void getStepMsgInfoManageData(StepMsgInfo_t &);



    void storeRegisterResetManageData(VehicleSideMsgFill_t &);
    void getRegisterResetManageData(VehicleSideMsgFill_t &);
    void setEvsModuleRegisterManageData(uint32_t interval = 0);

    //更新消息的标志位
    uint8_t  getUpdateUpDateFlag();


    //更新evsId
    void updataEvsId();

    //心跳数据帧 msgh 回填
    void updataHeartBeatData(bool heartbeatCntFlag = 1, bool onOffState = 1); 

    //0x02-0x05消息的 ack msg data回填

    void setAckMsgData(EvsFrameMessageEnum  msgType, uint32_t data = 0xAA);   //0x02 ~ 0x05



    bool isAckMsgUpdata(EvsFrameMessageEnum  msgType);

};



#pragma pack()
class EVSProto
{
public:
    EVSProto();
    ~EVSProto();

private:
    int32_t thisID = 0;
    Socket_Param SocketManage[MAX_SERVER_NUM];

public:
    int32_t ProtoInit(void);
    int32_t ProtoProcess(void);
    

private:
    uint8_t receive_frame[FRAME_BUFF_SIZE] = {0};
    uint8_t receive_frame_buff[FRAME_BUFF_SIZE] = {0};
    uint32_t receive_offset = 0;
    uint64_t receive_cnt = 1;
    uint32_t scan_max_cnt = 0;
    uint32_t scan_offset = 0;
    uint64_t get_cnt = 0;
    char str_buff[FRAME_BUFF_SIZE*2] = {0};

public:
    std::string serverIP;    //上位机地址
    uint32_t  serverPort; // 上位机端口
    std::string hmiIP;        // HMI屏幕ip地址
    bool  socket_reInit = false;    //socket 重新初始化标志
    bool  socket_ready = false;
    bool  socket_initok = false;
    uint8_t protocolWorkState[MAX_SERVER_NUM]={0};
    uint8_t protocolCounter[MAX_SERVER_NUM]={0};

    /***********************evs通信帧**************************/
    EVSManageData  eVSManageData;
    
    EvsModuleRegister_t evsModuleRegisterbuff;
    EvsModuleRegisterAck_t evsModuleRegisterAckBuff;
    ControlCmdRsp_t controlCmdRspBuff;
    ControlCmd_t controlCmdBuff;
    VehicleSideMsgFill_t vehicleSideMsgFillBuff;
    MsgFillRsp_t msgFillRspBuff;
    MsgCtrl_t msgAttrInfoBuff;
    ElectriCtrl_t electriCtrlBuff;   // data from hmi proto
    StepMsgInfo_t stepMsgInfoBuff;
    GetMsgInfo_t   getMsgInfoBuff;

    std::vector <ProtocolFrameBuff> frame_terminal_buff[MAX_SERVER_NUM];

public:
    int32_t Send_EvsModuleRegister(DeviceTypeEnum dev);
    int32_t Send_ControlCmdRsp(DeviceTypeEnum dev);
    int32_t Send_MsgFillRsp(DeviceTypeEnum dev);
    int32_t Send_MessageIntface(DeviceTypeEnum dev,EvsFrameMessageEnum msgType);
    /*发送0x1E查询0x02车辆侧报文上报*/
    int32_t Send_VehicleMsgReport(DeviceTypeEnum dev);
    /*发送0x1E查询0x03报文属性上报*/
    int32_t Send_MsgAttrReport(DeviceTypeEnum dev);
    /*发送0x1E查询0x04电器控制参数上报*/
    int32_t Send_ElecContrlReport(DeviceTypeEnum dev);
    /*发送0x1E查询0x05步进信息上报*/
    int32_t Send_StepInfoReport(DeviceTypeEnum dev);

  



    int32_t Recv_EvsModuleRegisterAck(uint8_t *buff,uint32_t size);
    int32_t Recv_ControlCmd(uint8_t *buff,uint32_t size);
    int32_t Recv_VehicleSideMsgFill(uint8_t *buff,uint32_t size);
    int32_t Recv_MsgAttrInfo(uint8_t *buff,uint32_t size);
    int32_t Recv_ElectriCtrl(uint8_t *buff,uint32_t size);
    int32_t Recv_StepMsgInfo(uint8_t *buff,uint32_t size);
    int32_t Recv_getMsgInfo(uint8_t *buff,uint32_t size);

    int32_t SocketInit(void);
    int32_t SendMsg( DeviceTypeEnum  dev, uint8_t cmd, uint8_t *data, uint32_t len);
    int32_t read_socket(DeviceTypeEnum  dev);
    int32_t ReceiveMsg(DeviceTypeEnum  *dev);
    int32_t get_package_proto(uint8_t *frame_data, int32_t *len);
    int32_t get_frame_data(DeviceTypeEnum  *dev,ProtocolFrameBuff *data);


    /*****************************数据处理******************************/

public:
    // 对发送数据赋值
    int32_t getEvsToHmiRegisterData(void);
    int32_t getEvsToHmiControlCmdRspData(void);
    int32_t getEvsToHmiMsgFillRspData(void);

    // 对接收数据赋值
    int32_t setHmiToEvsRegisterAckData(void);
    int32_t setHmiToEvsControlCmdData(void);
    int32_t setHmiToEvsVehicleSideMsgFillData(void);
    int32_t setHmiToEvsMsgAttrInfoData(void);
    int32_t setHmiToEvsElectriCtrlData(void);
    int32_t setHmiToEvsStepMsgInfoData(void);
    int32_t setHmiToEvsGetMsgInfoData(void);

};


#endif //_EVS_PROTO_H
