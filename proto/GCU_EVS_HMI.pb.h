// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GCU_EVS_HMI.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_GCU_5fEVS_5fHMI_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_GCU_5fEVS_5fHMI_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3017000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3017003 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "GCU_EVS_INFO.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_GCU_5fEVS_5fHMI_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_GCU_5fEVS_5fHMI_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[10]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GCU_5fEVS_5fHMI_2eproto;
namespace gcu_evs_hmi {
namespace protobuf {
class EVSGetMsg;
struct EVSGetMsgDefaultTypeInternal;
extern EVSGetMsgDefaultTypeInternal _EVSGetMsg_default_instance_;
class EVSHb;
struct EVSHbDefaultTypeInternal;
extern EVSHbDefaultTypeInternal _EVSHb_default_instance_;
class EVSHbReply;
struct EVSHbReplyDefaultTypeInternal;
extern EVSHbReplyDefaultTypeInternal _EVSHbReply_default_instance_;
class EVSLogin;
struct EVSLoginDefaultTypeInternal;
extern EVSLoginDefaultTypeInternal _EVSLogin_default_instance_;
class EVSLoginAns;
struct EVSLoginAnsDefaultTypeInternal;
extern EVSLoginAnsDefaultTypeInternal _EVSLoginAns_default_instance_;
class EVSMsgCtrl;
struct EVSMsgCtrlDefaultTypeInternal;
extern EVSMsgCtrlDefaultTypeInternal _EVSMsgCtrl_default_instance_;
class EVSMsgSet;
struct EVSMsgSetDefaultTypeInternal;
extern EVSMsgSetDefaultTypeInternal _EVSMsgSet_default_instance_;
class EVSSetReply;
struct EVSSetReplyDefaultTypeInternal;
extern EVSSetReplyDefaultTypeInternal _EVSSetReply_default_instance_;
class EVSStepMsgSet;
struct EVSStepMsgSetDefaultTypeInternal;
extern EVSStepMsgSetDefaultTypeInternal _EVSStepMsgSet_default_instance_;
class EVSSysCtrl;
struct EVSSysCtrlDefaultTypeInternal;
extern EVSSysCtrlDefaultTypeInternal _EVSSysCtrl_default_instance_;
}  // namespace protobuf
}  // namespace gcu_evs_hmi
PROTOBUF_NAMESPACE_OPEN
template<> ::gcu_evs_hmi::protobuf::EVSGetMsg* Arena::CreateMaybeMessage<::gcu_evs_hmi::protobuf::EVSGetMsg>(Arena*);
template<> ::gcu_evs_hmi::protobuf::EVSHb* Arena::CreateMaybeMessage<::gcu_evs_hmi::protobuf::EVSHb>(Arena*);
template<> ::gcu_evs_hmi::protobuf::EVSHbReply* Arena::CreateMaybeMessage<::gcu_evs_hmi::protobuf::EVSHbReply>(Arena*);
template<> ::gcu_evs_hmi::protobuf::EVSLogin* Arena::CreateMaybeMessage<::gcu_evs_hmi::protobuf::EVSLogin>(Arena*);
template<> ::gcu_evs_hmi::protobuf::EVSLoginAns* Arena::CreateMaybeMessage<::gcu_evs_hmi::protobuf::EVSLoginAns>(Arena*);
template<> ::gcu_evs_hmi::protobuf::EVSMsgCtrl* Arena::CreateMaybeMessage<::gcu_evs_hmi::protobuf::EVSMsgCtrl>(Arena*);
template<> ::gcu_evs_hmi::protobuf::EVSMsgSet* Arena::CreateMaybeMessage<::gcu_evs_hmi::protobuf::EVSMsgSet>(Arena*);
template<> ::gcu_evs_hmi::protobuf::EVSSetReply* Arena::CreateMaybeMessage<::gcu_evs_hmi::protobuf::EVSSetReply>(Arena*);
template<> ::gcu_evs_hmi::protobuf::EVSStepMsgSet* Arena::CreateMaybeMessage<::gcu_evs_hmi::protobuf::EVSStepMsgSet>(Arena*);
template<> ::gcu_evs_hmi::protobuf::EVSSysCtrl* Arena::CreateMaybeMessage<::gcu_evs_hmi::protobuf::EVSSysCtrl>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace gcu_evs_hmi {
namespace protobuf {

// ===================================================================

class EVSLogin final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gcu_evs_hmi.protobuf.EVSLogin) */ {
 public:
  inline EVSLogin() : EVSLogin(nullptr) {}
  ~EVSLogin() override;
  explicit constexpr EVSLogin(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVSLogin(const EVSLogin& from);
  EVSLogin(EVSLogin&& from) noexcept
    : EVSLogin() {
    *this = ::std::move(from);
  }

  inline EVSLogin& operator=(const EVSLogin& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVSLogin& operator=(EVSLogin&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVSLogin& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVSLogin* internal_default_instance() {
    return reinterpret_cast<const EVSLogin*>(
               &_EVSLogin_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(EVSLogin& a, EVSLogin& b) {
    a.Swap(&b);
  }
  inline void Swap(EVSLogin* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVSLogin* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVSLogin* New() const final {
    return new EVSLogin();
  }

  EVSLogin* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVSLogin>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVSLogin& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVSLogin& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVSLogin* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gcu_evs_hmi.protobuf.EVSLogin";
  }
  protected:
  explicit EVSLogin(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEvsIDFieldNumber = 1,
    kIntervalFieldNumber = 2,
  };
  // uint64 evsID = 1;
  void clear_evsid();
  ::PROTOBUF_NAMESPACE_ID::uint64 evsid() const;
  void set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_evsid() const;
  void _internal_set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // uint32 interval = 2;
  void clear_interval();
  ::PROTOBUF_NAMESPACE_ID::uint32 interval() const;
  void set_interval(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_interval() const;
  void _internal_set_interval(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:gcu_evs_hmi.protobuf.EVSLogin)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint64 evsid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 interval_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fHMI_2eproto;
};
// -------------------------------------------------------------------

class EVSLoginAns final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gcu_evs_hmi.protobuf.EVSLoginAns) */ {
 public:
  inline EVSLoginAns() : EVSLoginAns(nullptr) {}
  ~EVSLoginAns() override;
  explicit constexpr EVSLoginAns(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVSLoginAns(const EVSLoginAns& from);
  EVSLoginAns(EVSLoginAns&& from) noexcept
    : EVSLoginAns() {
    *this = ::std::move(from);
  }

  inline EVSLoginAns& operator=(const EVSLoginAns& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVSLoginAns& operator=(EVSLoginAns&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVSLoginAns& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVSLoginAns* internal_default_instance() {
    return reinterpret_cast<const EVSLoginAns*>(
               &_EVSLoginAns_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(EVSLoginAns& a, EVSLoginAns& b) {
    a.Swap(&b);
  }
  inline void Swap(EVSLoginAns* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVSLoginAns* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVSLoginAns* New() const final {
    return new EVSLoginAns();
  }

  EVSLoginAns* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVSLoginAns>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVSLoginAns& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVSLoginAns& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVSLoginAns* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gcu_evs_hmi.protobuf.EVSLoginAns";
  }
  protected:
  explicit EVSLoginAns(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEvsIDFieldNumber = 1,
    kServerIDFieldNumber = 2,
    kIntervalFieldNumber = 3,
  };
  // uint64 evsID = 1;
  void clear_evsid();
  ::PROTOBUF_NAMESPACE_ID::uint64 evsid() const;
  void set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_evsid() const;
  void _internal_set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // uint32 serverID = 2;
  void clear_serverid();
  ::PROTOBUF_NAMESPACE_ID::uint32 serverid() const;
  void set_serverid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_serverid() const;
  void _internal_set_serverid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 interval = 3;
  void clear_interval();
  ::PROTOBUF_NAMESPACE_ID::uint32 interval() const;
  void set_interval(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_interval() const;
  void _internal_set_interval(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:gcu_evs_hmi.protobuf.EVSLoginAns)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint64 evsid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 serverid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 interval_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fHMI_2eproto;
};
// -------------------------------------------------------------------

class EVSHb final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gcu_evs_hmi.protobuf.EVSHb) */ {
 public:
  inline EVSHb() : EVSHb(nullptr) {}
  ~EVSHb() override;
  explicit constexpr EVSHb(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVSHb(const EVSHb& from);
  EVSHb(EVSHb&& from) noexcept
    : EVSHb() {
    *this = ::std::move(from);
  }

  inline EVSHb& operator=(const EVSHb& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVSHb& operator=(EVSHb&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVSHb& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVSHb* internal_default_instance() {
    return reinterpret_cast<const EVSHb*>(
               &_EVSHb_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(EVSHb& a, EVSHb& b) {
    a.Swap(&b);
  }
  inline void Swap(EVSHb* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVSHb* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVSHb* New() const final {
    return new EVSHb();
  }

  EVSHb* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVSHb>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVSHb& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVSHb& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVSHb* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gcu_evs_hmi.protobuf.EVSHb";
  }
  protected:
  explicit EVSHb(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEvsStateFieldNumber = 4,
    kEvsIDFieldNumber = 1,
    kHeartbeatCntFieldNumber = 2,
    kOnOffStateFieldNumber = 3,
  };
  // .EVSInfo.EVStateMsg evsState = 4;
  bool has_evsstate() const;
  private:
  bool _internal_has_evsstate() const;
  public:
  void clear_evsstate();
  const ::EVSInfo::EVStateMsg& evsstate() const;
  PROTOBUF_MUST_USE_RESULT ::EVSInfo::EVStateMsg* release_evsstate();
  ::EVSInfo::EVStateMsg* mutable_evsstate();
  void set_allocated_evsstate(::EVSInfo::EVStateMsg* evsstate);
  private:
  const ::EVSInfo::EVStateMsg& _internal_evsstate() const;
  ::EVSInfo::EVStateMsg* _internal_mutable_evsstate();
  public:
  void unsafe_arena_set_allocated_evsstate(
      ::EVSInfo::EVStateMsg* evsstate);
  ::EVSInfo::EVStateMsg* unsafe_arena_release_evsstate();

  // uint64 evsID = 1;
  void clear_evsid();
  ::PROTOBUF_NAMESPACE_ID::uint64 evsid() const;
  void set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_evsid() const;
  void _internal_set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // uint32 heartbeatCnt = 2;
  void clear_heartbeatcnt();
  ::PROTOBUF_NAMESPACE_ID::uint32 heartbeatcnt() const;
  void set_heartbeatcnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_heartbeatcnt() const;
  void _internal_set_heartbeatcnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 onOffState = 3;
  void clear_onoffstate();
  ::PROTOBUF_NAMESPACE_ID::uint32 onoffstate() const;
  void set_onoffstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_onoffstate() const;
  void _internal_set_onoffstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:gcu_evs_hmi.protobuf.EVSHb)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::EVSInfo::EVStateMsg* evsstate_;
  ::PROTOBUF_NAMESPACE_ID::uint64 evsid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 heartbeatcnt_;
  ::PROTOBUF_NAMESPACE_ID::uint32 onoffstate_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fHMI_2eproto;
};
// -------------------------------------------------------------------

class EVSHbReply final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gcu_evs_hmi.protobuf.EVSHbReply) */ {
 public:
  inline EVSHbReply() : EVSHbReply(nullptr) {}
  ~EVSHbReply() override;
  explicit constexpr EVSHbReply(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVSHbReply(const EVSHbReply& from);
  EVSHbReply(EVSHbReply&& from) noexcept
    : EVSHbReply() {
    *this = ::std::move(from);
  }

  inline EVSHbReply& operator=(const EVSHbReply& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVSHbReply& operator=(EVSHbReply&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVSHbReply& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVSHbReply* internal_default_instance() {
    return reinterpret_cast<const EVSHbReply*>(
               &_EVSHbReply_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(EVSHbReply& a, EVSHbReply& b) {
    a.Swap(&b);
  }
  inline void Swap(EVSHbReply* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVSHbReply* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVSHbReply* New() const final {
    return new EVSHbReply();
  }

  EVSHbReply* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVSHbReply>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVSHbReply& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVSHbReply& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVSHbReply* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gcu_evs_hmi.protobuf.EVSHbReply";
  }
  protected:
  explicit EVSHbReply(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEvsIDFieldNumber = 1,
    kServerIDFieldNumber = 2,
    kHeartbeatCntFieldNumber = 3,
    kEvsCmdFieldNumber = 4,
    kOfflineModeFieldNumber = 5,
    kEvsResetFieldNumber = 6,
  };
  // uint64 evsID = 1;
  void clear_evsid();
  ::PROTOBUF_NAMESPACE_ID::uint64 evsid() const;
  void set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_evsid() const;
  void _internal_set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // uint32 serverID = 2;
  void clear_serverid();
  ::PROTOBUF_NAMESPACE_ID::uint32 serverid() const;
  void set_serverid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_serverid() const;
  void _internal_set_serverid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 heartbeatCnt = 3;
  void clear_heartbeatcnt();
  ::PROTOBUF_NAMESPACE_ID::uint32 heartbeatcnt() const;
  void set_heartbeatcnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_heartbeatcnt() const;
  void _internal_set_heartbeatcnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 evsCmd = 4;
  void clear_evscmd();
  ::PROTOBUF_NAMESPACE_ID::uint32 evscmd() const;
  void set_evscmd(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_evscmd() const;
  void _internal_set_evscmd(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 offlineMode = 5;
  void clear_offlinemode();
  ::PROTOBUF_NAMESPACE_ID::uint32 offlinemode() const;
  void set_offlinemode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_offlinemode() const;
  void _internal_set_offlinemode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 evsReset = 6;
  void clear_evsreset();
  ::PROTOBUF_NAMESPACE_ID::uint32 evsreset() const;
  void set_evsreset(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_evsreset() const;
  void _internal_set_evsreset(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:gcu_evs_hmi.protobuf.EVSHbReply)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint64 evsid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 serverid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 heartbeatcnt_;
  ::PROTOBUF_NAMESPACE_ID::uint32 evscmd_;
  ::PROTOBUF_NAMESPACE_ID::uint32 offlinemode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 evsreset_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fHMI_2eproto;
};
// -------------------------------------------------------------------

class EVSMsgSet final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gcu_evs_hmi.protobuf.EVSMsgSet) */ {
 public:
  inline EVSMsgSet() : EVSMsgSet(nullptr) {}
  ~EVSMsgSet() override;
  explicit constexpr EVSMsgSet(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVSMsgSet(const EVSMsgSet& from);
  EVSMsgSet(EVSMsgSet&& from) noexcept
    : EVSMsgSet() {
    *this = ::std::move(from);
  }

  inline EVSMsgSet& operator=(const EVSMsgSet& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVSMsgSet& operator=(EVSMsgSet&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVSMsgSet& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVSMsgSet* internal_default_instance() {
    return reinterpret_cast<const EVSMsgSet*>(
               &_EVSMsgSet_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(EVSMsgSet& a, EVSMsgSet& b) {
    a.Swap(&b);
  }
  inline void Swap(EVSMsgSet* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVSMsgSet* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVSMsgSet* New() const final {
    return new EVSMsgSet();
  }

  EVSMsgSet* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVSMsgSet>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVSMsgSet& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVSMsgSet& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVSMsgSet* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gcu_evs_hmi.protobuf.EVSMsgSet";
  }
  protected:
  explicit EVSMsgSet(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kProtoConferMFieldNumber = 2,
    kFunConferMFieldNumber = 3,
    kParaConfigMFieldNumber = 4,
    kAuthenMFieldNumber = 5,
    kReserveMFieldNumber = 6,
    kPowerSupplyMFieldNumber = 7,
    kChargingMFieldNumber = 8,
    kChargingEndMFieldNumber = 9,
    kEvsIDFieldNumber = 1,
  };
  // .EVSInfo.EVProtoConferMsg protoConferM = 2;
  bool has_protoconferm() const;
  private:
  bool _internal_has_protoconferm() const;
  public:
  void clear_protoconferm();
  const ::EVSInfo::EVProtoConferMsg& protoconferm() const;
  PROTOBUF_MUST_USE_RESULT ::EVSInfo::EVProtoConferMsg* release_protoconferm();
  ::EVSInfo::EVProtoConferMsg* mutable_protoconferm();
  void set_allocated_protoconferm(::EVSInfo::EVProtoConferMsg* protoconferm);
  private:
  const ::EVSInfo::EVProtoConferMsg& _internal_protoconferm() const;
  ::EVSInfo::EVProtoConferMsg* _internal_mutable_protoconferm();
  public:
  void unsafe_arena_set_allocated_protoconferm(
      ::EVSInfo::EVProtoConferMsg* protoconferm);
  ::EVSInfo::EVProtoConferMsg* unsafe_arena_release_protoconferm();

  // .EVSInfo.EVFunConferMsg funConferM = 3;
  bool has_funconferm() const;
  private:
  bool _internal_has_funconferm() const;
  public:
  void clear_funconferm();
  const ::EVSInfo::EVFunConferMsg& funconferm() const;
  PROTOBUF_MUST_USE_RESULT ::EVSInfo::EVFunConferMsg* release_funconferm();
  ::EVSInfo::EVFunConferMsg* mutable_funconferm();
  void set_allocated_funconferm(::EVSInfo::EVFunConferMsg* funconferm);
  private:
  const ::EVSInfo::EVFunConferMsg& _internal_funconferm() const;
  ::EVSInfo::EVFunConferMsg* _internal_mutable_funconferm();
  public:
  void unsafe_arena_set_allocated_funconferm(
      ::EVSInfo::EVFunConferMsg* funconferm);
  ::EVSInfo::EVFunConferMsg* unsafe_arena_release_funconferm();

  // .EVSInfo.EVChgParaConfigMsg paraConfigM = 4;
  bool has_paraconfigm() const;
  private:
  bool _internal_has_paraconfigm() const;
  public:
  void clear_paraconfigm();
  const ::EVSInfo::EVChgParaConfigMsg& paraconfigm() const;
  PROTOBUF_MUST_USE_RESULT ::EVSInfo::EVChgParaConfigMsg* release_paraconfigm();
  ::EVSInfo::EVChgParaConfigMsg* mutable_paraconfigm();
  void set_allocated_paraconfigm(::EVSInfo::EVChgParaConfigMsg* paraconfigm);
  private:
  const ::EVSInfo::EVChgParaConfigMsg& _internal_paraconfigm() const;
  ::EVSInfo::EVChgParaConfigMsg* _internal_mutable_paraconfigm();
  public:
  void unsafe_arena_set_allocated_paraconfigm(
      ::EVSInfo::EVChgParaConfigMsg* paraconfigm);
  ::EVSInfo::EVChgParaConfigMsg* unsafe_arena_release_paraconfigm();

  // .EVSInfo.EVAuthenMsg authenM = 5;
  bool has_authenm() const;
  private:
  bool _internal_has_authenm() const;
  public:
  void clear_authenm();
  const ::EVSInfo::EVAuthenMsg& authenm() const;
  PROTOBUF_MUST_USE_RESULT ::EVSInfo::EVAuthenMsg* release_authenm();
  ::EVSInfo::EVAuthenMsg* mutable_authenm();
  void set_allocated_authenm(::EVSInfo::EVAuthenMsg* authenm);
  private:
  const ::EVSInfo::EVAuthenMsg& _internal_authenm() const;
  ::EVSInfo::EVAuthenMsg* _internal_mutable_authenm();
  public:
  void unsafe_arena_set_allocated_authenm(
      ::EVSInfo::EVAuthenMsg* authenm);
  ::EVSInfo::EVAuthenMsg* unsafe_arena_release_authenm();

  // .EVSInfo.EVReserveMsg reserveM = 6;
  bool has_reservem() const;
  private:
  bool _internal_has_reservem() const;
  public:
  void clear_reservem();
  const ::EVSInfo::EVReserveMsg& reservem() const;
  PROTOBUF_MUST_USE_RESULT ::EVSInfo::EVReserveMsg* release_reservem();
  ::EVSInfo::EVReserveMsg* mutable_reservem();
  void set_allocated_reservem(::EVSInfo::EVReserveMsg* reservem);
  private:
  const ::EVSInfo::EVReserveMsg& _internal_reservem() const;
  ::EVSInfo::EVReserveMsg* _internal_mutable_reservem();
  public:
  void unsafe_arena_set_allocated_reservem(
      ::EVSInfo::EVReserveMsg* reservem);
  ::EVSInfo::EVReserveMsg* unsafe_arena_release_reservem();

  // .EVSInfo.EVPowerSupplyMsg powerSupplyM = 7;
  bool has_powersupplym() const;
  private:
  bool _internal_has_powersupplym() const;
  public:
  void clear_powersupplym();
  const ::EVSInfo::EVPowerSupplyMsg& powersupplym() const;
  PROTOBUF_MUST_USE_RESULT ::EVSInfo::EVPowerSupplyMsg* release_powersupplym();
  ::EVSInfo::EVPowerSupplyMsg* mutable_powersupplym();
  void set_allocated_powersupplym(::EVSInfo::EVPowerSupplyMsg* powersupplym);
  private:
  const ::EVSInfo::EVPowerSupplyMsg& _internal_powersupplym() const;
  ::EVSInfo::EVPowerSupplyMsg* _internal_mutable_powersupplym();
  public:
  void unsafe_arena_set_allocated_powersupplym(
      ::EVSInfo::EVPowerSupplyMsg* powersupplym);
  ::EVSInfo::EVPowerSupplyMsg* unsafe_arena_release_powersupplym();

  // .EVSInfo.EVChargingMsg chargingM = 8;
  bool has_chargingm() const;
  private:
  bool _internal_has_chargingm() const;
  public:
  void clear_chargingm();
  const ::EVSInfo::EVChargingMsg& chargingm() const;
  PROTOBUF_MUST_USE_RESULT ::EVSInfo::EVChargingMsg* release_chargingm();
  ::EVSInfo::EVChargingMsg* mutable_chargingm();
  void set_allocated_chargingm(::EVSInfo::EVChargingMsg* chargingm);
  private:
  const ::EVSInfo::EVChargingMsg& _internal_chargingm() const;
  ::EVSInfo::EVChargingMsg* _internal_mutable_chargingm();
  public:
  void unsafe_arena_set_allocated_chargingm(
      ::EVSInfo::EVChargingMsg* chargingm);
  ::EVSInfo::EVChargingMsg* unsafe_arena_release_chargingm();

  // .EVSInfo.EVChargingEndMsg chargingEndM = 9;
  bool has_chargingendm() const;
  private:
  bool _internal_has_chargingendm() const;
  public:
  void clear_chargingendm();
  const ::EVSInfo::EVChargingEndMsg& chargingendm() const;
  PROTOBUF_MUST_USE_RESULT ::EVSInfo::EVChargingEndMsg* release_chargingendm();
  ::EVSInfo::EVChargingEndMsg* mutable_chargingendm();
  void set_allocated_chargingendm(::EVSInfo::EVChargingEndMsg* chargingendm);
  private:
  const ::EVSInfo::EVChargingEndMsg& _internal_chargingendm() const;
  ::EVSInfo::EVChargingEndMsg* _internal_mutable_chargingendm();
  public:
  void unsafe_arena_set_allocated_chargingendm(
      ::EVSInfo::EVChargingEndMsg* chargingendm);
  ::EVSInfo::EVChargingEndMsg* unsafe_arena_release_chargingendm();

  // uint64 evsID = 1;
  void clear_evsid();
  ::PROTOBUF_NAMESPACE_ID::uint64 evsid() const;
  void set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_evsid() const;
  void _internal_set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // @@protoc_insertion_point(class_scope:gcu_evs_hmi.protobuf.EVSMsgSet)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::EVSInfo::EVProtoConferMsg* protoconferm_;
  ::EVSInfo::EVFunConferMsg* funconferm_;
  ::EVSInfo::EVChgParaConfigMsg* paraconfigm_;
  ::EVSInfo::EVAuthenMsg* authenm_;
  ::EVSInfo::EVReserveMsg* reservem_;
  ::EVSInfo::EVPowerSupplyMsg* powersupplym_;
  ::EVSInfo::EVChargingMsg* chargingm_;
  ::EVSInfo::EVChargingEndMsg* chargingendm_;
  ::PROTOBUF_NAMESPACE_ID::uint64 evsid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fHMI_2eproto;
};
// -------------------------------------------------------------------

class EVSMsgCtrl final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gcu_evs_hmi.protobuf.EVSMsgCtrl) */ {
 public:
  inline EVSMsgCtrl() : EVSMsgCtrl(nullptr) {}
  ~EVSMsgCtrl() override;
  explicit constexpr EVSMsgCtrl(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVSMsgCtrl(const EVSMsgCtrl& from);
  EVSMsgCtrl(EVSMsgCtrl&& from) noexcept
    : EVSMsgCtrl() {
    *this = ::std::move(from);
  }

  inline EVSMsgCtrl& operator=(const EVSMsgCtrl& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVSMsgCtrl& operator=(EVSMsgCtrl&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVSMsgCtrl& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVSMsgCtrl* internal_default_instance() {
    return reinterpret_cast<const EVSMsgCtrl*>(
               &_EVSMsgCtrl_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(EVSMsgCtrl& a, EVSMsgCtrl& b) {
    a.Swap(&b);
  }
  inline void Swap(EVSMsgCtrl* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVSMsgCtrl* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVSMsgCtrl* New() const final {
    return new EVSMsgCtrl();
  }

  EVSMsgCtrl* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVSMsgCtrl>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVSMsgCtrl& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVSMsgCtrl& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVSMsgCtrl* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gcu_evs_hmi.protobuf.EVSMsgCtrl";
  }
  protected:
  explicit EVSMsgCtrl(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMsgCtrlFieldNumber = 2,
    kMsgCycleTimeFieldNumber = 3,
    kMsgMaxSendTImeFieldNumber = 4,
    kFunConferAckFieldNumber = 5,
    kEvsIDFieldNumber = 1,
  };
  // .EVSInfo.EVMsgCtrl msgCtrl = 2;
  bool has_msgctrl() const;
  private:
  bool _internal_has_msgctrl() const;
  public:
  void clear_msgctrl();
  const ::EVSInfo::EVMsgCtrl& msgctrl() const;
  PROTOBUF_MUST_USE_RESULT ::EVSInfo::EVMsgCtrl* release_msgctrl();
  ::EVSInfo::EVMsgCtrl* mutable_msgctrl();
  void set_allocated_msgctrl(::EVSInfo::EVMsgCtrl* msgctrl);
  private:
  const ::EVSInfo::EVMsgCtrl& _internal_msgctrl() const;
  ::EVSInfo::EVMsgCtrl* _internal_mutable_msgctrl();
  public:
  void unsafe_arena_set_allocated_msgctrl(
      ::EVSInfo::EVMsgCtrl* msgctrl);
  ::EVSInfo::EVMsgCtrl* unsafe_arena_release_msgctrl();

  // .EVSInfo.EVMsgCtrl msgCycleTime = 3;
  bool has_msgcycletime() const;
  private:
  bool _internal_has_msgcycletime() const;
  public:
  void clear_msgcycletime();
  const ::EVSInfo::EVMsgCtrl& msgcycletime() const;
  PROTOBUF_MUST_USE_RESULT ::EVSInfo::EVMsgCtrl* release_msgcycletime();
  ::EVSInfo::EVMsgCtrl* mutable_msgcycletime();
  void set_allocated_msgcycletime(::EVSInfo::EVMsgCtrl* msgcycletime);
  private:
  const ::EVSInfo::EVMsgCtrl& _internal_msgcycletime() const;
  ::EVSInfo::EVMsgCtrl* _internal_mutable_msgcycletime();
  public:
  void unsafe_arena_set_allocated_msgcycletime(
      ::EVSInfo::EVMsgCtrl* msgcycletime);
  ::EVSInfo::EVMsgCtrl* unsafe_arena_release_msgcycletime();

  // .EVSInfo.EVMsgCtrl msgMaxSendTIme = 4;
  bool has_msgmaxsendtime() const;
  private:
  bool _internal_has_msgmaxsendtime() const;
  public:
  void clear_msgmaxsendtime();
  const ::EVSInfo::EVMsgCtrl& msgmaxsendtime() const;
  PROTOBUF_MUST_USE_RESULT ::EVSInfo::EVMsgCtrl* release_msgmaxsendtime();
  ::EVSInfo::EVMsgCtrl* mutable_msgmaxsendtime();
  void set_allocated_msgmaxsendtime(::EVSInfo::EVMsgCtrl* msgmaxsendtime);
  private:
  const ::EVSInfo::EVMsgCtrl& _internal_msgmaxsendtime() const;
  ::EVSInfo::EVMsgCtrl* _internal_mutable_msgmaxsendtime();
  public:
  void unsafe_arena_set_allocated_msgmaxsendtime(
      ::EVSInfo::EVMsgCtrl* msgmaxsendtime);
  ::EVSInfo::EVMsgCtrl* unsafe_arena_release_msgmaxsendtime();

  // .EVSInfo.EVFunConferAckMsg funConferAck = 5;
  bool has_funconferack() const;
  private:
  bool _internal_has_funconferack() const;
  public:
  void clear_funconferack();
  const ::EVSInfo::EVFunConferAckMsg& funconferack() const;
  PROTOBUF_MUST_USE_RESULT ::EVSInfo::EVFunConferAckMsg* release_funconferack();
  ::EVSInfo::EVFunConferAckMsg* mutable_funconferack();
  void set_allocated_funconferack(::EVSInfo::EVFunConferAckMsg* funconferack);
  private:
  const ::EVSInfo::EVFunConferAckMsg& _internal_funconferack() const;
  ::EVSInfo::EVFunConferAckMsg* _internal_mutable_funconferack();
  public:
  void unsafe_arena_set_allocated_funconferack(
      ::EVSInfo::EVFunConferAckMsg* funconferack);
  ::EVSInfo::EVFunConferAckMsg* unsafe_arena_release_funconferack();

  // uint64 evsID = 1;
  void clear_evsid();
  ::PROTOBUF_NAMESPACE_ID::uint64 evsid() const;
  void set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_evsid() const;
  void _internal_set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // @@protoc_insertion_point(class_scope:gcu_evs_hmi.protobuf.EVSMsgCtrl)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::EVSInfo::EVMsgCtrl* msgctrl_;
  ::EVSInfo::EVMsgCtrl* msgcycletime_;
  ::EVSInfo::EVMsgCtrl* msgmaxsendtime_;
  ::EVSInfo::EVFunConferAckMsg* funconferack_;
  ::PROTOBUF_NAMESPACE_ID::uint64 evsid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fHMI_2eproto;
};
// -------------------------------------------------------------------

class EVSSysCtrl final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gcu_evs_hmi.protobuf.EVSSysCtrl) */ {
 public:
  inline EVSSysCtrl() : EVSSysCtrl(nullptr) {}
  ~EVSSysCtrl() override;
  explicit constexpr EVSSysCtrl(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVSSysCtrl(const EVSSysCtrl& from);
  EVSSysCtrl(EVSSysCtrl&& from) noexcept
    : EVSSysCtrl() {
    *this = ::std::move(from);
  }

  inline EVSSysCtrl& operator=(const EVSSysCtrl& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVSSysCtrl& operator=(EVSSysCtrl&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVSSysCtrl& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVSSysCtrl* internal_default_instance() {
    return reinterpret_cast<const EVSSysCtrl*>(
               &_EVSSysCtrl_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(EVSSysCtrl& a, EVSSysCtrl& b) {
    a.Swap(&b);
  }
  inline void Swap(EVSSysCtrl* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVSSysCtrl* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVSSysCtrl* New() const final {
    return new EVSSysCtrl();
  }

  EVSSysCtrl* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVSSysCtrl>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVSSysCtrl& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVSSysCtrl& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVSSysCtrl* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gcu_evs_hmi.protobuf.EVSSysCtrl";
  }
  protected:
  explicit EVSSysCtrl(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kElectricCtrlFieldNumber = 2,
    kInsultCtrlFieldNumber = 3,
    kIpMsgFieldNumber = 4,
    kEvsIDFieldNumber = 1,
  };
  // .EVSInfo.EVElectricCtrl electricCtrl = 2;
  bool has_electricctrl() const;
  private:
  bool _internal_has_electricctrl() const;
  public:
  void clear_electricctrl();
  const ::EVSInfo::EVElectricCtrl& electricctrl() const;
  PROTOBUF_MUST_USE_RESULT ::EVSInfo::EVElectricCtrl* release_electricctrl();
  ::EVSInfo::EVElectricCtrl* mutable_electricctrl();
  void set_allocated_electricctrl(::EVSInfo::EVElectricCtrl* electricctrl);
  private:
  const ::EVSInfo::EVElectricCtrl& _internal_electricctrl() const;
  ::EVSInfo::EVElectricCtrl* _internal_mutable_electricctrl();
  public:
  void unsafe_arena_set_allocated_electricctrl(
      ::EVSInfo::EVElectricCtrl* electricctrl);
  ::EVSInfo::EVElectricCtrl* unsafe_arena_release_electricctrl();

  // .EVSInfo.EVInsultCtrl insultCtrl = 3;
  bool has_insultctrl() const;
  private:
  bool _internal_has_insultctrl() const;
  public:
  void clear_insultctrl();
  const ::EVSInfo::EVInsultCtrl& insultctrl() const;
  PROTOBUF_MUST_USE_RESULT ::EVSInfo::EVInsultCtrl* release_insultctrl();
  ::EVSInfo::EVInsultCtrl* mutable_insultctrl();
  void set_allocated_insultctrl(::EVSInfo::EVInsultCtrl* insultctrl);
  private:
  const ::EVSInfo::EVInsultCtrl& _internal_insultctrl() const;
  ::EVSInfo::EVInsultCtrl* _internal_mutable_insultctrl();
  public:
  void unsafe_arena_set_allocated_insultctrl(
      ::EVSInfo::EVInsultCtrl* insultctrl);
  ::EVSInfo::EVInsultCtrl* unsafe_arena_release_insultctrl();

  // .EVSInfo.IpMsg ipMsg = 4;
  bool has_ipmsg() const;
  private:
  bool _internal_has_ipmsg() const;
  public:
  void clear_ipmsg();
  const ::EVSInfo::IpMsg& ipmsg() const;
  PROTOBUF_MUST_USE_RESULT ::EVSInfo::IpMsg* release_ipmsg();
  ::EVSInfo::IpMsg* mutable_ipmsg();
  void set_allocated_ipmsg(::EVSInfo::IpMsg* ipmsg);
  private:
  const ::EVSInfo::IpMsg& _internal_ipmsg() const;
  ::EVSInfo::IpMsg* _internal_mutable_ipmsg();
  public:
  void unsafe_arena_set_allocated_ipmsg(
      ::EVSInfo::IpMsg* ipmsg);
  ::EVSInfo::IpMsg* unsafe_arena_release_ipmsg();

  // uint64 evsID = 1;
  void clear_evsid();
  ::PROTOBUF_NAMESPACE_ID::uint64 evsid() const;
  void set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_evsid() const;
  void _internal_set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // @@protoc_insertion_point(class_scope:gcu_evs_hmi.protobuf.EVSSysCtrl)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::EVSInfo::EVElectricCtrl* electricctrl_;
  ::EVSInfo::EVInsultCtrl* insultctrl_;
  ::EVSInfo::IpMsg* ipmsg_;
  ::PROTOBUF_NAMESPACE_ID::uint64 evsid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fHMI_2eproto;
};
// -------------------------------------------------------------------

class EVSStepMsgSet final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gcu_evs_hmi.protobuf.EVSStepMsgSet) */ {
 public:
  inline EVSStepMsgSet() : EVSStepMsgSet(nullptr) {}
  ~EVSStepMsgSet() override;
  explicit constexpr EVSStepMsgSet(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVSStepMsgSet(const EVSStepMsgSet& from);
  EVSStepMsgSet(EVSStepMsgSet&& from) noexcept
    : EVSStepMsgSet() {
    *this = ::std::move(from);
  }

  inline EVSStepMsgSet& operator=(const EVSStepMsgSet& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVSStepMsgSet& operator=(EVSStepMsgSet&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVSStepMsgSet& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVSStepMsgSet* internal_default_instance() {
    return reinterpret_cast<const EVSStepMsgSet*>(
               &_EVSStepMsgSet_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(EVSStepMsgSet& a, EVSStepMsgSet& b) {
    a.Swap(&b);
  }
  inline void Swap(EVSStepMsgSet* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVSStepMsgSet* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVSStepMsgSet* New() const final {
    return new EVSStepMsgSet();
  }

  EVSStepMsgSet* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVSStepMsgSet>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVSStepMsgSet& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVSStepMsgSet& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVSStepMsgSet* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gcu_evs_hmi.protobuf.EVSStepMsgSet";
  }
  protected:
  explicit EVSStepMsgSet(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStepMsgFieldNumber = 2,
    kEvsIDFieldNumber = 1,
  };
  // .EVSInfo.EVSStepMsg stepMsg = 2;
  bool has_stepmsg() const;
  private:
  bool _internal_has_stepmsg() const;
  public:
  void clear_stepmsg();
  const ::EVSInfo::EVSStepMsg& stepmsg() const;
  PROTOBUF_MUST_USE_RESULT ::EVSInfo::EVSStepMsg* release_stepmsg();
  ::EVSInfo::EVSStepMsg* mutable_stepmsg();
  void set_allocated_stepmsg(::EVSInfo::EVSStepMsg* stepmsg);
  private:
  const ::EVSInfo::EVSStepMsg& _internal_stepmsg() const;
  ::EVSInfo::EVSStepMsg* _internal_mutable_stepmsg();
  public:
  void unsafe_arena_set_allocated_stepmsg(
      ::EVSInfo::EVSStepMsg* stepmsg);
  ::EVSInfo::EVSStepMsg* unsafe_arena_release_stepmsg();

  // uint64 evsID = 1;
  void clear_evsid();
  ::PROTOBUF_NAMESPACE_ID::uint64 evsid() const;
  void set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_evsid() const;
  void _internal_set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // @@protoc_insertion_point(class_scope:gcu_evs_hmi.protobuf.EVSStepMsgSet)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::EVSInfo::EVSStepMsg* stepmsg_;
  ::PROTOBUF_NAMESPACE_ID::uint64 evsid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fHMI_2eproto;
};
// -------------------------------------------------------------------

class EVSGetMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gcu_evs_hmi.protobuf.EVSGetMsg) */ {
 public:
  inline EVSGetMsg() : EVSGetMsg(nullptr) {}
  ~EVSGetMsg() override;
  explicit constexpr EVSGetMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVSGetMsg(const EVSGetMsg& from);
  EVSGetMsg(EVSGetMsg&& from) noexcept
    : EVSGetMsg() {
    *this = ::std::move(from);
  }

  inline EVSGetMsg& operator=(const EVSGetMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVSGetMsg& operator=(EVSGetMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVSGetMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVSGetMsg* internal_default_instance() {
    return reinterpret_cast<const EVSGetMsg*>(
               &_EVSGetMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(EVSGetMsg& a, EVSGetMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(EVSGetMsg* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVSGetMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVSGetMsg* New() const final {
    return new EVSGetMsg();
  }

  EVSGetMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVSGetMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVSGetMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVSGetMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVSGetMsg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gcu_evs_hmi.protobuf.EVSGetMsg";
  }
  protected:
  explicit EVSGetMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEvsIDFieldNumber = 1,
    kGetTimeFieldNumber = 3,
    kMsgIDFieldNumber = 2,
  };
  // uint64 evsID = 1;
  void clear_evsid();
  ::PROTOBUF_NAMESPACE_ID::uint64 evsid() const;
  void set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_evsid() const;
  void _internal_set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // uint64 getTime = 3;
  void clear_gettime();
  ::PROTOBUF_NAMESPACE_ID::uint64 gettime() const;
  void set_gettime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_gettime() const;
  void _internal_set_gettime(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // uint32 msgID = 2;
  void clear_msgid();
  ::PROTOBUF_NAMESPACE_ID::uint32 msgid() const;
  void set_msgid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_msgid() const;
  void _internal_set_msgid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:gcu_evs_hmi.protobuf.EVSGetMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint64 evsid_;
  ::PROTOBUF_NAMESPACE_ID::uint64 gettime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 msgid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fHMI_2eproto;
};
// -------------------------------------------------------------------

class EVSSetReply final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gcu_evs_hmi.protobuf.EVSSetReply) */ {
 public:
  inline EVSSetReply() : EVSSetReply(nullptr) {}
  ~EVSSetReply() override;
  explicit constexpr EVSSetReply(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EVSSetReply(const EVSSetReply& from);
  EVSSetReply(EVSSetReply&& from) noexcept
    : EVSSetReply() {
    *this = ::std::move(from);
  }

  inline EVSSetReply& operator=(const EVSSetReply& from) {
    CopyFrom(from);
    return *this;
  }
  inline EVSSetReply& operator=(EVSSetReply&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EVSSetReply& default_instance() {
    return *internal_default_instance();
  }
  static inline const EVSSetReply* internal_default_instance() {
    return reinterpret_cast<const EVSSetReply*>(
               &_EVSSetReply_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(EVSSetReply& a, EVSSetReply& b) {
    a.Swap(&b);
  }
  inline void Swap(EVSSetReply* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EVSSetReply* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EVSSetReply* New() const final {
    return new EVSSetReply();
  }

  EVSSetReply* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EVSSetReply>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EVSSetReply& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EVSSetReply& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EVSSetReply* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gcu_evs_hmi.protobuf.EVSSetReply";
  }
  protected:
  explicit EVSSetReply(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEvsIDFieldNumber = 1,
    kMsgIDFieldNumber = 2,
    kSetAckFieldNumber = 3,
  };
  // uint64 evsID = 1;
  void clear_evsid();
  ::PROTOBUF_NAMESPACE_ID::uint64 evsid() const;
  void set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_evsid() const;
  void _internal_set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // uint32 msgID = 2;
  void clear_msgid();
  ::PROTOBUF_NAMESPACE_ID::uint32 msgid() const;
  void set_msgid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_msgid() const;
  void _internal_set_msgid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 setAck = 3;
  void clear_setack();
  ::PROTOBUF_NAMESPACE_ID::uint32 setack() const;
  void set_setack(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_setack() const;
  void _internal_set_setack(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:gcu_evs_hmi.protobuf.EVSSetReply)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint64 evsid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 msgid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 setack_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fEVS_5fHMI_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// EVSLogin

// uint64 evsID = 1;
inline void EVSLogin::clear_evsid() {
  evsid_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EVSLogin::_internal_evsid() const {
  return evsid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EVSLogin::evsid() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSLogin.evsID)
  return _internal_evsid();
}
inline void EVSLogin::_internal_set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  evsid_ = value;
}
inline void EVSLogin::set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_evsid(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSLogin.evsID)
}

// uint32 interval = 2;
inline void EVSLogin::clear_interval() {
  interval_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSLogin::_internal_interval() const {
  return interval_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSLogin::interval() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSLogin.interval)
  return _internal_interval();
}
inline void EVSLogin::_internal_set_interval(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  interval_ = value;
}
inline void EVSLogin::set_interval(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_interval(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSLogin.interval)
}

// -------------------------------------------------------------------

// EVSLoginAns

// uint64 evsID = 1;
inline void EVSLoginAns::clear_evsid() {
  evsid_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EVSLoginAns::_internal_evsid() const {
  return evsid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EVSLoginAns::evsid() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSLoginAns.evsID)
  return _internal_evsid();
}
inline void EVSLoginAns::_internal_set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  evsid_ = value;
}
inline void EVSLoginAns::set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_evsid(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSLoginAns.evsID)
}

// uint32 serverID = 2;
inline void EVSLoginAns::clear_serverid() {
  serverid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSLoginAns::_internal_serverid() const {
  return serverid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSLoginAns::serverid() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSLoginAns.serverID)
  return _internal_serverid();
}
inline void EVSLoginAns::_internal_set_serverid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  serverid_ = value;
}
inline void EVSLoginAns::set_serverid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_serverid(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSLoginAns.serverID)
}

// uint32 interval = 3;
inline void EVSLoginAns::clear_interval() {
  interval_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSLoginAns::_internal_interval() const {
  return interval_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSLoginAns::interval() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSLoginAns.interval)
  return _internal_interval();
}
inline void EVSLoginAns::_internal_set_interval(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  interval_ = value;
}
inline void EVSLoginAns::set_interval(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_interval(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSLoginAns.interval)
}

// -------------------------------------------------------------------

// EVSHb

// uint64 evsID = 1;
inline void EVSHb::clear_evsid() {
  evsid_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EVSHb::_internal_evsid() const {
  return evsid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EVSHb::evsid() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSHb.evsID)
  return _internal_evsid();
}
inline void EVSHb::_internal_set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  evsid_ = value;
}
inline void EVSHb::set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_evsid(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSHb.evsID)
}

// uint32 heartbeatCnt = 2;
inline void EVSHb::clear_heartbeatcnt() {
  heartbeatcnt_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSHb::_internal_heartbeatcnt() const {
  return heartbeatcnt_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSHb::heartbeatcnt() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSHb.heartbeatCnt)
  return _internal_heartbeatcnt();
}
inline void EVSHb::_internal_set_heartbeatcnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  heartbeatcnt_ = value;
}
inline void EVSHb::set_heartbeatcnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_heartbeatcnt(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSHb.heartbeatCnt)
}

// uint32 onOffState = 3;
inline void EVSHb::clear_onoffstate() {
  onoffstate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSHb::_internal_onoffstate() const {
  return onoffstate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSHb::onoffstate() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSHb.onOffState)
  return _internal_onoffstate();
}
inline void EVSHb::_internal_set_onoffstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  onoffstate_ = value;
}
inline void EVSHb::set_onoffstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_onoffstate(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSHb.onOffState)
}

// .EVSInfo.EVStateMsg evsState = 4;
inline bool EVSHb::_internal_has_evsstate() const {
  return this != internal_default_instance() && evsstate_ != nullptr;
}
inline bool EVSHb::has_evsstate() const {
  return _internal_has_evsstate();
}
inline const ::EVSInfo::EVStateMsg& EVSHb::_internal_evsstate() const {
  const ::EVSInfo::EVStateMsg* p = evsstate_;
  return p != nullptr ? *p : reinterpret_cast<const ::EVSInfo::EVStateMsg&>(
      ::EVSInfo::_EVStateMsg_default_instance_);
}
inline const ::EVSInfo::EVStateMsg& EVSHb::evsstate() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSHb.evsState)
  return _internal_evsstate();
}
inline void EVSHb::unsafe_arena_set_allocated_evsstate(
    ::EVSInfo::EVStateMsg* evsstate) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(evsstate_);
  }
  evsstate_ = evsstate;
  if (evsstate) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gcu_evs_hmi.protobuf.EVSHb.evsState)
}
inline ::EVSInfo::EVStateMsg* EVSHb::release_evsstate() {
  
  ::EVSInfo::EVStateMsg* temp = evsstate_;
  evsstate_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::EVSInfo::EVStateMsg* EVSHb::unsafe_arena_release_evsstate() {
  // @@protoc_insertion_point(field_release:gcu_evs_hmi.protobuf.EVSHb.evsState)
  
  ::EVSInfo::EVStateMsg* temp = evsstate_;
  evsstate_ = nullptr;
  return temp;
}
inline ::EVSInfo::EVStateMsg* EVSHb::_internal_mutable_evsstate() {
  
  if (evsstate_ == nullptr) {
    auto* p = CreateMaybeMessage<::EVSInfo::EVStateMsg>(GetArenaForAllocation());
    evsstate_ = p;
  }
  return evsstate_;
}
inline ::EVSInfo::EVStateMsg* EVSHb::mutable_evsstate() {
  ::EVSInfo::EVStateMsg* _msg = _internal_mutable_evsstate();
  // @@protoc_insertion_point(field_mutable:gcu_evs_hmi.protobuf.EVSHb.evsState)
  return _msg;
}
inline void EVSHb::set_allocated_evsstate(::EVSInfo::EVStateMsg* evsstate) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(evsstate_);
  }
  if (evsstate) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(evsstate));
    if (message_arena != submessage_arena) {
      evsstate = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, evsstate, submessage_arena);
    }
    
  } else {
    
  }
  evsstate_ = evsstate;
  // @@protoc_insertion_point(field_set_allocated:gcu_evs_hmi.protobuf.EVSHb.evsState)
}

// -------------------------------------------------------------------

// EVSHbReply

// uint64 evsID = 1;
inline void EVSHbReply::clear_evsid() {
  evsid_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EVSHbReply::_internal_evsid() const {
  return evsid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EVSHbReply::evsid() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSHbReply.evsID)
  return _internal_evsid();
}
inline void EVSHbReply::_internal_set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  evsid_ = value;
}
inline void EVSHbReply::set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_evsid(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSHbReply.evsID)
}

// uint32 serverID = 2;
inline void EVSHbReply::clear_serverid() {
  serverid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSHbReply::_internal_serverid() const {
  return serverid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSHbReply::serverid() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSHbReply.serverID)
  return _internal_serverid();
}
inline void EVSHbReply::_internal_set_serverid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  serverid_ = value;
}
inline void EVSHbReply::set_serverid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_serverid(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSHbReply.serverID)
}

// uint32 heartbeatCnt = 3;
inline void EVSHbReply::clear_heartbeatcnt() {
  heartbeatcnt_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSHbReply::_internal_heartbeatcnt() const {
  return heartbeatcnt_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSHbReply::heartbeatcnt() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSHbReply.heartbeatCnt)
  return _internal_heartbeatcnt();
}
inline void EVSHbReply::_internal_set_heartbeatcnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  heartbeatcnt_ = value;
}
inline void EVSHbReply::set_heartbeatcnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_heartbeatcnt(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSHbReply.heartbeatCnt)
}

// uint32 evsCmd = 4;
inline void EVSHbReply::clear_evscmd() {
  evscmd_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSHbReply::_internal_evscmd() const {
  return evscmd_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSHbReply::evscmd() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSHbReply.evsCmd)
  return _internal_evscmd();
}
inline void EVSHbReply::_internal_set_evscmd(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  evscmd_ = value;
}
inline void EVSHbReply::set_evscmd(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_evscmd(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSHbReply.evsCmd)
}

// uint32 offlineMode = 5;
inline void EVSHbReply::clear_offlinemode() {
  offlinemode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSHbReply::_internal_offlinemode() const {
  return offlinemode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSHbReply::offlinemode() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSHbReply.offlineMode)
  return _internal_offlinemode();
}
inline void EVSHbReply::_internal_set_offlinemode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  offlinemode_ = value;
}
inline void EVSHbReply::set_offlinemode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_offlinemode(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSHbReply.offlineMode)
}

// uint32 evsReset = 6;
inline void EVSHbReply::clear_evsreset() {
  evsreset_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSHbReply::_internal_evsreset() const {
  return evsreset_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSHbReply::evsreset() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSHbReply.evsReset)
  return _internal_evsreset();
}
inline void EVSHbReply::_internal_set_evsreset(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  evsreset_ = value;
}
inline void EVSHbReply::set_evsreset(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_evsreset(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSHbReply.evsReset)
}

// -------------------------------------------------------------------

// EVSMsgSet

// uint64 evsID = 1;
inline void EVSMsgSet::clear_evsid() {
  evsid_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EVSMsgSet::_internal_evsid() const {
  return evsid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EVSMsgSet::evsid() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSMsgSet.evsID)
  return _internal_evsid();
}
inline void EVSMsgSet::_internal_set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  evsid_ = value;
}
inline void EVSMsgSet::set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_evsid(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSMsgSet.evsID)
}

// .EVSInfo.EVProtoConferMsg protoConferM = 2;
inline bool EVSMsgSet::_internal_has_protoconferm() const {
  return this != internal_default_instance() && protoconferm_ != nullptr;
}
inline bool EVSMsgSet::has_protoconferm() const {
  return _internal_has_protoconferm();
}
inline const ::EVSInfo::EVProtoConferMsg& EVSMsgSet::_internal_protoconferm() const {
  const ::EVSInfo::EVProtoConferMsg* p = protoconferm_;
  return p != nullptr ? *p : reinterpret_cast<const ::EVSInfo::EVProtoConferMsg&>(
      ::EVSInfo::_EVProtoConferMsg_default_instance_);
}
inline const ::EVSInfo::EVProtoConferMsg& EVSMsgSet::protoconferm() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSMsgSet.protoConferM)
  return _internal_protoconferm();
}
inline void EVSMsgSet::unsafe_arena_set_allocated_protoconferm(
    ::EVSInfo::EVProtoConferMsg* protoconferm) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(protoconferm_);
  }
  protoconferm_ = protoconferm;
  if (protoconferm) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gcu_evs_hmi.protobuf.EVSMsgSet.protoConferM)
}
inline ::EVSInfo::EVProtoConferMsg* EVSMsgSet::release_protoconferm() {
  
  ::EVSInfo::EVProtoConferMsg* temp = protoconferm_;
  protoconferm_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::EVSInfo::EVProtoConferMsg* EVSMsgSet::unsafe_arena_release_protoconferm() {
  // @@protoc_insertion_point(field_release:gcu_evs_hmi.protobuf.EVSMsgSet.protoConferM)
  
  ::EVSInfo::EVProtoConferMsg* temp = protoconferm_;
  protoconferm_ = nullptr;
  return temp;
}
inline ::EVSInfo::EVProtoConferMsg* EVSMsgSet::_internal_mutable_protoconferm() {
  
  if (protoconferm_ == nullptr) {
    auto* p = CreateMaybeMessage<::EVSInfo::EVProtoConferMsg>(GetArenaForAllocation());
    protoconferm_ = p;
  }
  return protoconferm_;
}
inline ::EVSInfo::EVProtoConferMsg* EVSMsgSet::mutable_protoconferm() {
  ::EVSInfo::EVProtoConferMsg* _msg = _internal_mutable_protoconferm();
  // @@protoc_insertion_point(field_mutable:gcu_evs_hmi.protobuf.EVSMsgSet.protoConferM)
  return _msg;
}
inline void EVSMsgSet::set_allocated_protoconferm(::EVSInfo::EVProtoConferMsg* protoconferm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(protoconferm_);
  }
  if (protoconferm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(protoconferm));
    if (message_arena != submessage_arena) {
      protoconferm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, protoconferm, submessage_arena);
    }
    
  } else {
    
  }
  protoconferm_ = protoconferm;
  // @@protoc_insertion_point(field_set_allocated:gcu_evs_hmi.protobuf.EVSMsgSet.protoConferM)
}

// .EVSInfo.EVFunConferMsg funConferM = 3;
inline bool EVSMsgSet::_internal_has_funconferm() const {
  return this != internal_default_instance() && funconferm_ != nullptr;
}
inline bool EVSMsgSet::has_funconferm() const {
  return _internal_has_funconferm();
}
inline const ::EVSInfo::EVFunConferMsg& EVSMsgSet::_internal_funconferm() const {
  const ::EVSInfo::EVFunConferMsg* p = funconferm_;
  return p != nullptr ? *p : reinterpret_cast<const ::EVSInfo::EVFunConferMsg&>(
      ::EVSInfo::_EVFunConferMsg_default_instance_);
}
inline const ::EVSInfo::EVFunConferMsg& EVSMsgSet::funconferm() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSMsgSet.funConferM)
  return _internal_funconferm();
}
inline void EVSMsgSet::unsafe_arena_set_allocated_funconferm(
    ::EVSInfo::EVFunConferMsg* funconferm) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(funconferm_);
  }
  funconferm_ = funconferm;
  if (funconferm) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gcu_evs_hmi.protobuf.EVSMsgSet.funConferM)
}
inline ::EVSInfo::EVFunConferMsg* EVSMsgSet::release_funconferm() {
  
  ::EVSInfo::EVFunConferMsg* temp = funconferm_;
  funconferm_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::EVSInfo::EVFunConferMsg* EVSMsgSet::unsafe_arena_release_funconferm() {
  // @@protoc_insertion_point(field_release:gcu_evs_hmi.protobuf.EVSMsgSet.funConferM)
  
  ::EVSInfo::EVFunConferMsg* temp = funconferm_;
  funconferm_ = nullptr;
  return temp;
}
inline ::EVSInfo::EVFunConferMsg* EVSMsgSet::_internal_mutable_funconferm() {
  
  if (funconferm_ == nullptr) {
    auto* p = CreateMaybeMessage<::EVSInfo::EVFunConferMsg>(GetArenaForAllocation());
    funconferm_ = p;
  }
  return funconferm_;
}
inline ::EVSInfo::EVFunConferMsg* EVSMsgSet::mutable_funconferm() {
  ::EVSInfo::EVFunConferMsg* _msg = _internal_mutable_funconferm();
  // @@protoc_insertion_point(field_mutable:gcu_evs_hmi.protobuf.EVSMsgSet.funConferM)
  return _msg;
}
inline void EVSMsgSet::set_allocated_funconferm(::EVSInfo::EVFunConferMsg* funconferm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(funconferm_);
  }
  if (funconferm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(funconferm));
    if (message_arena != submessage_arena) {
      funconferm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, funconferm, submessage_arena);
    }
    
  } else {
    
  }
  funconferm_ = funconferm;
  // @@protoc_insertion_point(field_set_allocated:gcu_evs_hmi.protobuf.EVSMsgSet.funConferM)
}

// .EVSInfo.EVChgParaConfigMsg paraConfigM = 4;
inline bool EVSMsgSet::_internal_has_paraconfigm() const {
  return this != internal_default_instance() && paraconfigm_ != nullptr;
}
inline bool EVSMsgSet::has_paraconfigm() const {
  return _internal_has_paraconfigm();
}
inline const ::EVSInfo::EVChgParaConfigMsg& EVSMsgSet::_internal_paraconfigm() const {
  const ::EVSInfo::EVChgParaConfigMsg* p = paraconfigm_;
  return p != nullptr ? *p : reinterpret_cast<const ::EVSInfo::EVChgParaConfigMsg&>(
      ::EVSInfo::_EVChgParaConfigMsg_default_instance_);
}
inline const ::EVSInfo::EVChgParaConfigMsg& EVSMsgSet::paraconfigm() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSMsgSet.paraConfigM)
  return _internal_paraconfigm();
}
inline void EVSMsgSet::unsafe_arena_set_allocated_paraconfigm(
    ::EVSInfo::EVChgParaConfigMsg* paraconfigm) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(paraconfigm_);
  }
  paraconfigm_ = paraconfigm;
  if (paraconfigm) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gcu_evs_hmi.protobuf.EVSMsgSet.paraConfigM)
}
inline ::EVSInfo::EVChgParaConfigMsg* EVSMsgSet::release_paraconfigm() {
  
  ::EVSInfo::EVChgParaConfigMsg* temp = paraconfigm_;
  paraconfigm_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::EVSInfo::EVChgParaConfigMsg* EVSMsgSet::unsafe_arena_release_paraconfigm() {
  // @@protoc_insertion_point(field_release:gcu_evs_hmi.protobuf.EVSMsgSet.paraConfigM)
  
  ::EVSInfo::EVChgParaConfigMsg* temp = paraconfigm_;
  paraconfigm_ = nullptr;
  return temp;
}
inline ::EVSInfo::EVChgParaConfigMsg* EVSMsgSet::_internal_mutable_paraconfigm() {
  
  if (paraconfigm_ == nullptr) {
    auto* p = CreateMaybeMessage<::EVSInfo::EVChgParaConfigMsg>(GetArenaForAllocation());
    paraconfigm_ = p;
  }
  return paraconfigm_;
}
inline ::EVSInfo::EVChgParaConfigMsg* EVSMsgSet::mutable_paraconfigm() {
  ::EVSInfo::EVChgParaConfigMsg* _msg = _internal_mutable_paraconfigm();
  // @@protoc_insertion_point(field_mutable:gcu_evs_hmi.protobuf.EVSMsgSet.paraConfigM)
  return _msg;
}
inline void EVSMsgSet::set_allocated_paraconfigm(::EVSInfo::EVChgParaConfigMsg* paraconfigm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(paraconfigm_);
  }
  if (paraconfigm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(paraconfigm));
    if (message_arena != submessage_arena) {
      paraconfigm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, paraconfigm, submessage_arena);
    }
    
  } else {
    
  }
  paraconfigm_ = paraconfigm;
  // @@protoc_insertion_point(field_set_allocated:gcu_evs_hmi.protobuf.EVSMsgSet.paraConfigM)
}

// .EVSInfo.EVAuthenMsg authenM = 5;
inline bool EVSMsgSet::_internal_has_authenm() const {
  return this != internal_default_instance() && authenm_ != nullptr;
}
inline bool EVSMsgSet::has_authenm() const {
  return _internal_has_authenm();
}
inline const ::EVSInfo::EVAuthenMsg& EVSMsgSet::_internal_authenm() const {
  const ::EVSInfo::EVAuthenMsg* p = authenm_;
  return p != nullptr ? *p : reinterpret_cast<const ::EVSInfo::EVAuthenMsg&>(
      ::EVSInfo::_EVAuthenMsg_default_instance_);
}
inline const ::EVSInfo::EVAuthenMsg& EVSMsgSet::authenm() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSMsgSet.authenM)
  return _internal_authenm();
}
inline void EVSMsgSet::unsafe_arena_set_allocated_authenm(
    ::EVSInfo::EVAuthenMsg* authenm) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(authenm_);
  }
  authenm_ = authenm;
  if (authenm) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gcu_evs_hmi.protobuf.EVSMsgSet.authenM)
}
inline ::EVSInfo::EVAuthenMsg* EVSMsgSet::release_authenm() {
  
  ::EVSInfo::EVAuthenMsg* temp = authenm_;
  authenm_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::EVSInfo::EVAuthenMsg* EVSMsgSet::unsafe_arena_release_authenm() {
  // @@protoc_insertion_point(field_release:gcu_evs_hmi.protobuf.EVSMsgSet.authenM)
  
  ::EVSInfo::EVAuthenMsg* temp = authenm_;
  authenm_ = nullptr;
  return temp;
}
inline ::EVSInfo::EVAuthenMsg* EVSMsgSet::_internal_mutable_authenm() {
  
  if (authenm_ == nullptr) {
    auto* p = CreateMaybeMessage<::EVSInfo::EVAuthenMsg>(GetArenaForAllocation());
    authenm_ = p;
  }
  return authenm_;
}
inline ::EVSInfo::EVAuthenMsg* EVSMsgSet::mutable_authenm() {
  ::EVSInfo::EVAuthenMsg* _msg = _internal_mutable_authenm();
  // @@protoc_insertion_point(field_mutable:gcu_evs_hmi.protobuf.EVSMsgSet.authenM)
  return _msg;
}
inline void EVSMsgSet::set_allocated_authenm(::EVSInfo::EVAuthenMsg* authenm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(authenm_);
  }
  if (authenm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(authenm));
    if (message_arena != submessage_arena) {
      authenm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, authenm, submessage_arena);
    }
    
  } else {
    
  }
  authenm_ = authenm;
  // @@protoc_insertion_point(field_set_allocated:gcu_evs_hmi.protobuf.EVSMsgSet.authenM)
}

// .EVSInfo.EVReserveMsg reserveM = 6;
inline bool EVSMsgSet::_internal_has_reservem() const {
  return this != internal_default_instance() && reservem_ != nullptr;
}
inline bool EVSMsgSet::has_reservem() const {
  return _internal_has_reservem();
}
inline const ::EVSInfo::EVReserveMsg& EVSMsgSet::_internal_reservem() const {
  const ::EVSInfo::EVReserveMsg* p = reservem_;
  return p != nullptr ? *p : reinterpret_cast<const ::EVSInfo::EVReserveMsg&>(
      ::EVSInfo::_EVReserveMsg_default_instance_);
}
inline const ::EVSInfo::EVReserveMsg& EVSMsgSet::reservem() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSMsgSet.reserveM)
  return _internal_reservem();
}
inline void EVSMsgSet::unsafe_arena_set_allocated_reservem(
    ::EVSInfo::EVReserveMsg* reservem) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(reservem_);
  }
  reservem_ = reservem;
  if (reservem) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gcu_evs_hmi.protobuf.EVSMsgSet.reserveM)
}
inline ::EVSInfo::EVReserveMsg* EVSMsgSet::release_reservem() {
  
  ::EVSInfo::EVReserveMsg* temp = reservem_;
  reservem_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::EVSInfo::EVReserveMsg* EVSMsgSet::unsafe_arena_release_reservem() {
  // @@protoc_insertion_point(field_release:gcu_evs_hmi.protobuf.EVSMsgSet.reserveM)
  
  ::EVSInfo::EVReserveMsg* temp = reservem_;
  reservem_ = nullptr;
  return temp;
}
inline ::EVSInfo::EVReserveMsg* EVSMsgSet::_internal_mutable_reservem() {
  
  if (reservem_ == nullptr) {
    auto* p = CreateMaybeMessage<::EVSInfo::EVReserveMsg>(GetArenaForAllocation());
    reservem_ = p;
  }
  return reservem_;
}
inline ::EVSInfo::EVReserveMsg* EVSMsgSet::mutable_reservem() {
  ::EVSInfo::EVReserveMsg* _msg = _internal_mutable_reservem();
  // @@protoc_insertion_point(field_mutable:gcu_evs_hmi.protobuf.EVSMsgSet.reserveM)
  return _msg;
}
inline void EVSMsgSet::set_allocated_reservem(::EVSInfo::EVReserveMsg* reservem) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(reservem_);
  }
  if (reservem) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(reservem));
    if (message_arena != submessage_arena) {
      reservem = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, reservem, submessage_arena);
    }
    
  } else {
    
  }
  reservem_ = reservem;
  // @@protoc_insertion_point(field_set_allocated:gcu_evs_hmi.protobuf.EVSMsgSet.reserveM)
}

// .EVSInfo.EVPowerSupplyMsg powerSupplyM = 7;
inline bool EVSMsgSet::_internal_has_powersupplym() const {
  return this != internal_default_instance() && powersupplym_ != nullptr;
}
inline bool EVSMsgSet::has_powersupplym() const {
  return _internal_has_powersupplym();
}
inline const ::EVSInfo::EVPowerSupplyMsg& EVSMsgSet::_internal_powersupplym() const {
  const ::EVSInfo::EVPowerSupplyMsg* p = powersupplym_;
  return p != nullptr ? *p : reinterpret_cast<const ::EVSInfo::EVPowerSupplyMsg&>(
      ::EVSInfo::_EVPowerSupplyMsg_default_instance_);
}
inline const ::EVSInfo::EVPowerSupplyMsg& EVSMsgSet::powersupplym() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSMsgSet.powerSupplyM)
  return _internal_powersupplym();
}
inline void EVSMsgSet::unsafe_arena_set_allocated_powersupplym(
    ::EVSInfo::EVPowerSupplyMsg* powersupplym) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(powersupplym_);
  }
  powersupplym_ = powersupplym;
  if (powersupplym) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gcu_evs_hmi.protobuf.EVSMsgSet.powerSupplyM)
}
inline ::EVSInfo::EVPowerSupplyMsg* EVSMsgSet::release_powersupplym() {
  
  ::EVSInfo::EVPowerSupplyMsg* temp = powersupplym_;
  powersupplym_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::EVSInfo::EVPowerSupplyMsg* EVSMsgSet::unsafe_arena_release_powersupplym() {
  // @@protoc_insertion_point(field_release:gcu_evs_hmi.protobuf.EVSMsgSet.powerSupplyM)
  
  ::EVSInfo::EVPowerSupplyMsg* temp = powersupplym_;
  powersupplym_ = nullptr;
  return temp;
}
inline ::EVSInfo::EVPowerSupplyMsg* EVSMsgSet::_internal_mutable_powersupplym() {
  
  if (powersupplym_ == nullptr) {
    auto* p = CreateMaybeMessage<::EVSInfo::EVPowerSupplyMsg>(GetArenaForAllocation());
    powersupplym_ = p;
  }
  return powersupplym_;
}
inline ::EVSInfo::EVPowerSupplyMsg* EVSMsgSet::mutable_powersupplym() {
  ::EVSInfo::EVPowerSupplyMsg* _msg = _internal_mutable_powersupplym();
  // @@protoc_insertion_point(field_mutable:gcu_evs_hmi.protobuf.EVSMsgSet.powerSupplyM)
  return _msg;
}
inline void EVSMsgSet::set_allocated_powersupplym(::EVSInfo::EVPowerSupplyMsg* powersupplym) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(powersupplym_);
  }
  if (powersupplym) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(powersupplym));
    if (message_arena != submessage_arena) {
      powersupplym = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, powersupplym, submessage_arena);
    }
    
  } else {
    
  }
  powersupplym_ = powersupplym;
  // @@protoc_insertion_point(field_set_allocated:gcu_evs_hmi.protobuf.EVSMsgSet.powerSupplyM)
}

// .EVSInfo.EVChargingMsg chargingM = 8;
inline bool EVSMsgSet::_internal_has_chargingm() const {
  return this != internal_default_instance() && chargingm_ != nullptr;
}
inline bool EVSMsgSet::has_chargingm() const {
  return _internal_has_chargingm();
}
inline const ::EVSInfo::EVChargingMsg& EVSMsgSet::_internal_chargingm() const {
  const ::EVSInfo::EVChargingMsg* p = chargingm_;
  return p != nullptr ? *p : reinterpret_cast<const ::EVSInfo::EVChargingMsg&>(
      ::EVSInfo::_EVChargingMsg_default_instance_);
}
inline const ::EVSInfo::EVChargingMsg& EVSMsgSet::chargingm() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSMsgSet.chargingM)
  return _internal_chargingm();
}
inline void EVSMsgSet::unsafe_arena_set_allocated_chargingm(
    ::EVSInfo::EVChargingMsg* chargingm) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(chargingm_);
  }
  chargingm_ = chargingm;
  if (chargingm) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gcu_evs_hmi.protobuf.EVSMsgSet.chargingM)
}
inline ::EVSInfo::EVChargingMsg* EVSMsgSet::release_chargingm() {
  
  ::EVSInfo::EVChargingMsg* temp = chargingm_;
  chargingm_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::EVSInfo::EVChargingMsg* EVSMsgSet::unsafe_arena_release_chargingm() {
  // @@protoc_insertion_point(field_release:gcu_evs_hmi.protobuf.EVSMsgSet.chargingM)
  
  ::EVSInfo::EVChargingMsg* temp = chargingm_;
  chargingm_ = nullptr;
  return temp;
}
inline ::EVSInfo::EVChargingMsg* EVSMsgSet::_internal_mutable_chargingm() {
  
  if (chargingm_ == nullptr) {
    auto* p = CreateMaybeMessage<::EVSInfo::EVChargingMsg>(GetArenaForAllocation());
    chargingm_ = p;
  }
  return chargingm_;
}
inline ::EVSInfo::EVChargingMsg* EVSMsgSet::mutable_chargingm() {
  ::EVSInfo::EVChargingMsg* _msg = _internal_mutable_chargingm();
  // @@protoc_insertion_point(field_mutable:gcu_evs_hmi.protobuf.EVSMsgSet.chargingM)
  return _msg;
}
inline void EVSMsgSet::set_allocated_chargingm(::EVSInfo::EVChargingMsg* chargingm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(chargingm_);
  }
  if (chargingm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(chargingm));
    if (message_arena != submessage_arena) {
      chargingm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, chargingm, submessage_arena);
    }
    
  } else {
    
  }
  chargingm_ = chargingm;
  // @@protoc_insertion_point(field_set_allocated:gcu_evs_hmi.protobuf.EVSMsgSet.chargingM)
}

// .EVSInfo.EVChargingEndMsg chargingEndM = 9;
inline bool EVSMsgSet::_internal_has_chargingendm() const {
  return this != internal_default_instance() && chargingendm_ != nullptr;
}
inline bool EVSMsgSet::has_chargingendm() const {
  return _internal_has_chargingendm();
}
inline const ::EVSInfo::EVChargingEndMsg& EVSMsgSet::_internal_chargingendm() const {
  const ::EVSInfo::EVChargingEndMsg* p = chargingendm_;
  return p != nullptr ? *p : reinterpret_cast<const ::EVSInfo::EVChargingEndMsg&>(
      ::EVSInfo::_EVChargingEndMsg_default_instance_);
}
inline const ::EVSInfo::EVChargingEndMsg& EVSMsgSet::chargingendm() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSMsgSet.chargingEndM)
  return _internal_chargingendm();
}
inline void EVSMsgSet::unsafe_arena_set_allocated_chargingendm(
    ::EVSInfo::EVChargingEndMsg* chargingendm) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(chargingendm_);
  }
  chargingendm_ = chargingendm;
  if (chargingendm) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gcu_evs_hmi.protobuf.EVSMsgSet.chargingEndM)
}
inline ::EVSInfo::EVChargingEndMsg* EVSMsgSet::release_chargingendm() {
  
  ::EVSInfo::EVChargingEndMsg* temp = chargingendm_;
  chargingendm_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::EVSInfo::EVChargingEndMsg* EVSMsgSet::unsafe_arena_release_chargingendm() {
  // @@protoc_insertion_point(field_release:gcu_evs_hmi.protobuf.EVSMsgSet.chargingEndM)
  
  ::EVSInfo::EVChargingEndMsg* temp = chargingendm_;
  chargingendm_ = nullptr;
  return temp;
}
inline ::EVSInfo::EVChargingEndMsg* EVSMsgSet::_internal_mutable_chargingendm() {
  
  if (chargingendm_ == nullptr) {
    auto* p = CreateMaybeMessage<::EVSInfo::EVChargingEndMsg>(GetArenaForAllocation());
    chargingendm_ = p;
  }
  return chargingendm_;
}
inline ::EVSInfo::EVChargingEndMsg* EVSMsgSet::mutable_chargingendm() {
  ::EVSInfo::EVChargingEndMsg* _msg = _internal_mutable_chargingendm();
  // @@protoc_insertion_point(field_mutable:gcu_evs_hmi.protobuf.EVSMsgSet.chargingEndM)
  return _msg;
}
inline void EVSMsgSet::set_allocated_chargingendm(::EVSInfo::EVChargingEndMsg* chargingendm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(chargingendm_);
  }
  if (chargingendm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(chargingendm));
    if (message_arena != submessage_arena) {
      chargingendm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, chargingendm, submessage_arena);
    }
    
  } else {
    
  }
  chargingendm_ = chargingendm;
  // @@protoc_insertion_point(field_set_allocated:gcu_evs_hmi.protobuf.EVSMsgSet.chargingEndM)
}

// -------------------------------------------------------------------

// EVSMsgCtrl

// uint64 evsID = 1;
inline void EVSMsgCtrl::clear_evsid() {
  evsid_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EVSMsgCtrl::_internal_evsid() const {
  return evsid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EVSMsgCtrl::evsid() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSMsgCtrl.evsID)
  return _internal_evsid();
}
inline void EVSMsgCtrl::_internal_set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  evsid_ = value;
}
inline void EVSMsgCtrl::set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_evsid(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSMsgCtrl.evsID)
}

// .EVSInfo.EVMsgCtrl msgCtrl = 2;
inline bool EVSMsgCtrl::_internal_has_msgctrl() const {
  return this != internal_default_instance() && msgctrl_ != nullptr;
}
inline bool EVSMsgCtrl::has_msgctrl() const {
  return _internal_has_msgctrl();
}
inline const ::EVSInfo::EVMsgCtrl& EVSMsgCtrl::_internal_msgctrl() const {
  const ::EVSInfo::EVMsgCtrl* p = msgctrl_;
  return p != nullptr ? *p : reinterpret_cast<const ::EVSInfo::EVMsgCtrl&>(
      ::EVSInfo::_EVMsgCtrl_default_instance_);
}
inline const ::EVSInfo::EVMsgCtrl& EVSMsgCtrl::msgctrl() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSMsgCtrl.msgCtrl)
  return _internal_msgctrl();
}
inline void EVSMsgCtrl::unsafe_arena_set_allocated_msgctrl(
    ::EVSInfo::EVMsgCtrl* msgctrl) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(msgctrl_);
  }
  msgctrl_ = msgctrl;
  if (msgctrl) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gcu_evs_hmi.protobuf.EVSMsgCtrl.msgCtrl)
}
inline ::EVSInfo::EVMsgCtrl* EVSMsgCtrl::release_msgctrl() {
  
  ::EVSInfo::EVMsgCtrl* temp = msgctrl_;
  msgctrl_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::EVSInfo::EVMsgCtrl* EVSMsgCtrl::unsafe_arena_release_msgctrl() {
  // @@protoc_insertion_point(field_release:gcu_evs_hmi.protobuf.EVSMsgCtrl.msgCtrl)
  
  ::EVSInfo::EVMsgCtrl* temp = msgctrl_;
  msgctrl_ = nullptr;
  return temp;
}
inline ::EVSInfo::EVMsgCtrl* EVSMsgCtrl::_internal_mutable_msgctrl() {
  
  if (msgctrl_ == nullptr) {
    auto* p = CreateMaybeMessage<::EVSInfo::EVMsgCtrl>(GetArenaForAllocation());
    msgctrl_ = p;
  }
  return msgctrl_;
}
inline ::EVSInfo::EVMsgCtrl* EVSMsgCtrl::mutable_msgctrl() {
  ::EVSInfo::EVMsgCtrl* _msg = _internal_mutable_msgctrl();
  // @@protoc_insertion_point(field_mutable:gcu_evs_hmi.protobuf.EVSMsgCtrl.msgCtrl)
  return _msg;
}
inline void EVSMsgCtrl::set_allocated_msgctrl(::EVSInfo::EVMsgCtrl* msgctrl) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(msgctrl_);
  }
  if (msgctrl) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(msgctrl));
    if (message_arena != submessage_arena) {
      msgctrl = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, msgctrl, submessage_arena);
    }
    
  } else {
    
  }
  msgctrl_ = msgctrl;
  // @@protoc_insertion_point(field_set_allocated:gcu_evs_hmi.protobuf.EVSMsgCtrl.msgCtrl)
}

// .EVSInfo.EVMsgCtrl msgCycleTime = 3;
inline bool EVSMsgCtrl::_internal_has_msgcycletime() const {
  return this != internal_default_instance() && msgcycletime_ != nullptr;
}
inline bool EVSMsgCtrl::has_msgcycletime() const {
  return _internal_has_msgcycletime();
}
inline const ::EVSInfo::EVMsgCtrl& EVSMsgCtrl::_internal_msgcycletime() const {
  const ::EVSInfo::EVMsgCtrl* p = msgcycletime_;
  return p != nullptr ? *p : reinterpret_cast<const ::EVSInfo::EVMsgCtrl&>(
      ::EVSInfo::_EVMsgCtrl_default_instance_);
}
inline const ::EVSInfo::EVMsgCtrl& EVSMsgCtrl::msgcycletime() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSMsgCtrl.msgCycleTime)
  return _internal_msgcycletime();
}
inline void EVSMsgCtrl::unsafe_arena_set_allocated_msgcycletime(
    ::EVSInfo::EVMsgCtrl* msgcycletime) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(msgcycletime_);
  }
  msgcycletime_ = msgcycletime;
  if (msgcycletime) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gcu_evs_hmi.protobuf.EVSMsgCtrl.msgCycleTime)
}
inline ::EVSInfo::EVMsgCtrl* EVSMsgCtrl::release_msgcycletime() {
  
  ::EVSInfo::EVMsgCtrl* temp = msgcycletime_;
  msgcycletime_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::EVSInfo::EVMsgCtrl* EVSMsgCtrl::unsafe_arena_release_msgcycletime() {
  // @@protoc_insertion_point(field_release:gcu_evs_hmi.protobuf.EVSMsgCtrl.msgCycleTime)
  
  ::EVSInfo::EVMsgCtrl* temp = msgcycletime_;
  msgcycletime_ = nullptr;
  return temp;
}
inline ::EVSInfo::EVMsgCtrl* EVSMsgCtrl::_internal_mutable_msgcycletime() {
  
  if (msgcycletime_ == nullptr) {
    auto* p = CreateMaybeMessage<::EVSInfo::EVMsgCtrl>(GetArenaForAllocation());
    msgcycletime_ = p;
  }
  return msgcycletime_;
}
inline ::EVSInfo::EVMsgCtrl* EVSMsgCtrl::mutable_msgcycletime() {
  ::EVSInfo::EVMsgCtrl* _msg = _internal_mutable_msgcycletime();
  // @@protoc_insertion_point(field_mutable:gcu_evs_hmi.protobuf.EVSMsgCtrl.msgCycleTime)
  return _msg;
}
inline void EVSMsgCtrl::set_allocated_msgcycletime(::EVSInfo::EVMsgCtrl* msgcycletime) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(msgcycletime_);
  }
  if (msgcycletime) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(msgcycletime));
    if (message_arena != submessage_arena) {
      msgcycletime = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, msgcycletime, submessage_arena);
    }
    
  } else {
    
  }
  msgcycletime_ = msgcycletime;
  // @@protoc_insertion_point(field_set_allocated:gcu_evs_hmi.protobuf.EVSMsgCtrl.msgCycleTime)
}

// .EVSInfo.EVMsgCtrl msgMaxSendTIme = 4;
inline bool EVSMsgCtrl::_internal_has_msgmaxsendtime() const {
  return this != internal_default_instance() && msgmaxsendtime_ != nullptr;
}
inline bool EVSMsgCtrl::has_msgmaxsendtime() const {
  return _internal_has_msgmaxsendtime();
}
inline const ::EVSInfo::EVMsgCtrl& EVSMsgCtrl::_internal_msgmaxsendtime() const {
  const ::EVSInfo::EVMsgCtrl* p = msgmaxsendtime_;
  return p != nullptr ? *p : reinterpret_cast<const ::EVSInfo::EVMsgCtrl&>(
      ::EVSInfo::_EVMsgCtrl_default_instance_);
}
inline const ::EVSInfo::EVMsgCtrl& EVSMsgCtrl::msgmaxsendtime() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSMsgCtrl.msgMaxSendTIme)
  return _internal_msgmaxsendtime();
}
inline void EVSMsgCtrl::unsafe_arena_set_allocated_msgmaxsendtime(
    ::EVSInfo::EVMsgCtrl* msgmaxsendtime) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(msgmaxsendtime_);
  }
  msgmaxsendtime_ = msgmaxsendtime;
  if (msgmaxsendtime) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gcu_evs_hmi.protobuf.EVSMsgCtrl.msgMaxSendTIme)
}
inline ::EVSInfo::EVMsgCtrl* EVSMsgCtrl::release_msgmaxsendtime() {
  
  ::EVSInfo::EVMsgCtrl* temp = msgmaxsendtime_;
  msgmaxsendtime_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::EVSInfo::EVMsgCtrl* EVSMsgCtrl::unsafe_arena_release_msgmaxsendtime() {
  // @@protoc_insertion_point(field_release:gcu_evs_hmi.protobuf.EVSMsgCtrl.msgMaxSendTIme)
  
  ::EVSInfo::EVMsgCtrl* temp = msgmaxsendtime_;
  msgmaxsendtime_ = nullptr;
  return temp;
}
inline ::EVSInfo::EVMsgCtrl* EVSMsgCtrl::_internal_mutable_msgmaxsendtime() {
  
  if (msgmaxsendtime_ == nullptr) {
    auto* p = CreateMaybeMessage<::EVSInfo::EVMsgCtrl>(GetArenaForAllocation());
    msgmaxsendtime_ = p;
  }
  return msgmaxsendtime_;
}
inline ::EVSInfo::EVMsgCtrl* EVSMsgCtrl::mutable_msgmaxsendtime() {
  ::EVSInfo::EVMsgCtrl* _msg = _internal_mutable_msgmaxsendtime();
  // @@protoc_insertion_point(field_mutable:gcu_evs_hmi.protobuf.EVSMsgCtrl.msgMaxSendTIme)
  return _msg;
}
inline void EVSMsgCtrl::set_allocated_msgmaxsendtime(::EVSInfo::EVMsgCtrl* msgmaxsendtime) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(msgmaxsendtime_);
  }
  if (msgmaxsendtime) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(msgmaxsendtime));
    if (message_arena != submessage_arena) {
      msgmaxsendtime = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, msgmaxsendtime, submessage_arena);
    }
    
  } else {
    
  }
  msgmaxsendtime_ = msgmaxsendtime;
  // @@protoc_insertion_point(field_set_allocated:gcu_evs_hmi.protobuf.EVSMsgCtrl.msgMaxSendTIme)
}

// .EVSInfo.EVFunConferAckMsg funConferAck = 5;
inline bool EVSMsgCtrl::_internal_has_funconferack() const {
  return this != internal_default_instance() && funconferack_ != nullptr;
}
inline bool EVSMsgCtrl::has_funconferack() const {
  return _internal_has_funconferack();
}
inline const ::EVSInfo::EVFunConferAckMsg& EVSMsgCtrl::_internal_funconferack() const {
  const ::EVSInfo::EVFunConferAckMsg* p = funconferack_;
  return p != nullptr ? *p : reinterpret_cast<const ::EVSInfo::EVFunConferAckMsg&>(
      ::EVSInfo::_EVFunConferAckMsg_default_instance_);
}
inline const ::EVSInfo::EVFunConferAckMsg& EVSMsgCtrl::funconferack() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSMsgCtrl.funConferAck)
  return _internal_funconferack();
}
inline void EVSMsgCtrl::unsafe_arena_set_allocated_funconferack(
    ::EVSInfo::EVFunConferAckMsg* funconferack) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(funconferack_);
  }
  funconferack_ = funconferack;
  if (funconferack) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gcu_evs_hmi.protobuf.EVSMsgCtrl.funConferAck)
}
inline ::EVSInfo::EVFunConferAckMsg* EVSMsgCtrl::release_funconferack() {
  
  ::EVSInfo::EVFunConferAckMsg* temp = funconferack_;
  funconferack_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::EVSInfo::EVFunConferAckMsg* EVSMsgCtrl::unsafe_arena_release_funconferack() {
  // @@protoc_insertion_point(field_release:gcu_evs_hmi.protobuf.EVSMsgCtrl.funConferAck)
  
  ::EVSInfo::EVFunConferAckMsg* temp = funconferack_;
  funconferack_ = nullptr;
  return temp;
}
inline ::EVSInfo::EVFunConferAckMsg* EVSMsgCtrl::_internal_mutable_funconferack() {
  
  if (funconferack_ == nullptr) {
    auto* p = CreateMaybeMessage<::EVSInfo::EVFunConferAckMsg>(GetArenaForAllocation());
    funconferack_ = p;
  }
  return funconferack_;
}
inline ::EVSInfo::EVFunConferAckMsg* EVSMsgCtrl::mutable_funconferack() {
  ::EVSInfo::EVFunConferAckMsg* _msg = _internal_mutable_funconferack();
  // @@protoc_insertion_point(field_mutable:gcu_evs_hmi.protobuf.EVSMsgCtrl.funConferAck)
  return _msg;
}
inline void EVSMsgCtrl::set_allocated_funconferack(::EVSInfo::EVFunConferAckMsg* funconferack) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(funconferack_);
  }
  if (funconferack) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(funconferack));
    if (message_arena != submessage_arena) {
      funconferack = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, funconferack, submessage_arena);
    }
    
  } else {
    
  }
  funconferack_ = funconferack;
  // @@protoc_insertion_point(field_set_allocated:gcu_evs_hmi.protobuf.EVSMsgCtrl.funConferAck)
}

// -------------------------------------------------------------------

// EVSSysCtrl

// uint64 evsID = 1;
inline void EVSSysCtrl::clear_evsid() {
  evsid_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EVSSysCtrl::_internal_evsid() const {
  return evsid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EVSSysCtrl::evsid() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSSysCtrl.evsID)
  return _internal_evsid();
}
inline void EVSSysCtrl::_internal_set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  evsid_ = value;
}
inline void EVSSysCtrl::set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_evsid(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSSysCtrl.evsID)
}

// .EVSInfo.EVElectricCtrl electricCtrl = 2;
inline bool EVSSysCtrl::_internal_has_electricctrl() const {
  return this != internal_default_instance() && electricctrl_ != nullptr;
}
inline bool EVSSysCtrl::has_electricctrl() const {
  return _internal_has_electricctrl();
}
inline const ::EVSInfo::EVElectricCtrl& EVSSysCtrl::_internal_electricctrl() const {
  const ::EVSInfo::EVElectricCtrl* p = electricctrl_;
  return p != nullptr ? *p : reinterpret_cast<const ::EVSInfo::EVElectricCtrl&>(
      ::EVSInfo::_EVElectricCtrl_default_instance_);
}
inline const ::EVSInfo::EVElectricCtrl& EVSSysCtrl::electricctrl() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSSysCtrl.electricCtrl)
  return _internal_electricctrl();
}
inline void EVSSysCtrl::unsafe_arena_set_allocated_electricctrl(
    ::EVSInfo::EVElectricCtrl* electricctrl) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(electricctrl_);
  }
  electricctrl_ = electricctrl;
  if (electricctrl) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gcu_evs_hmi.protobuf.EVSSysCtrl.electricCtrl)
}
inline ::EVSInfo::EVElectricCtrl* EVSSysCtrl::release_electricctrl() {
  
  ::EVSInfo::EVElectricCtrl* temp = electricctrl_;
  electricctrl_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::EVSInfo::EVElectricCtrl* EVSSysCtrl::unsafe_arena_release_electricctrl() {
  // @@protoc_insertion_point(field_release:gcu_evs_hmi.protobuf.EVSSysCtrl.electricCtrl)
  
  ::EVSInfo::EVElectricCtrl* temp = electricctrl_;
  electricctrl_ = nullptr;
  return temp;
}
inline ::EVSInfo::EVElectricCtrl* EVSSysCtrl::_internal_mutable_electricctrl() {
  
  if (electricctrl_ == nullptr) {
    auto* p = CreateMaybeMessage<::EVSInfo::EVElectricCtrl>(GetArenaForAllocation());
    electricctrl_ = p;
  }
  return electricctrl_;
}
inline ::EVSInfo::EVElectricCtrl* EVSSysCtrl::mutable_electricctrl() {
  ::EVSInfo::EVElectricCtrl* _msg = _internal_mutable_electricctrl();
  // @@protoc_insertion_point(field_mutable:gcu_evs_hmi.protobuf.EVSSysCtrl.electricCtrl)
  return _msg;
}
inline void EVSSysCtrl::set_allocated_electricctrl(::EVSInfo::EVElectricCtrl* electricctrl) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(electricctrl_);
  }
  if (electricctrl) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(electricctrl));
    if (message_arena != submessage_arena) {
      electricctrl = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, electricctrl, submessage_arena);
    }
    
  } else {
    
  }
  electricctrl_ = electricctrl;
  // @@protoc_insertion_point(field_set_allocated:gcu_evs_hmi.protobuf.EVSSysCtrl.electricCtrl)
}

// .EVSInfo.EVInsultCtrl insultCtrl = 3;
inline bool EVSSysCtrl::_internal_has_insultctrl() const {
  return this != internal_default_instance() && insultctrl_ != nullptr;
}
inline bool EVSSysCtrl::has_insultctrl() const {
  return _internal_has_insultctrl();
}
inline const ::EVSInfo::EVInsultCtrl& EVSSysCtrl::_internal_insultctrl() const {
  const ::EVSInfo::EVInsultCtrl* p = insultctrl_;
  return p != nullptr ? *p : reinterpret_cast<const ::EVSInfo::EVInsultCtrl&>(
      ::EVSInfo::_EVInsultCtrl_default_instance_);
}
inline const ::EVSInfo::EVInsultCtrl& EVSSysCtrl::insultctrl() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSSysCtrl.insultCtrl)
  return _internal_insultctrl();
}
inline void EVSSysCtrl::unsafe_arena_set_allocated_insultctrl(
    ::EVSInfo::EVInsultCtrl* insultctrl) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(insultctrl_);
  }
  insultctrl_ = insultctrl;
  if (insultctrl) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gcu_evs_hmi.protobuf.EVSSysCtrl.insultCtrl)
}
inline ::EVSInfo::EVInsultCtrl* EVSSysCtrl::release_insultctrl() {
  
  ::EVSInfo::EVInsultCtrl* temp = insultctrl_;
  insultctrl_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::EVSInfo::EVInsultCtrl* EVSSysCtrl::unsafe_arena_release_insultctrl() {
  // @@protoc_insertion_point(field_release:gcu_evs_hmi.protobuf.EVSSysCtrl.insultCtrl)
  
  ::EVSInfo::EVInsultCtrl* temp = insultctrl_;
  insultctrl_ = nullptr;
  return temp;
}
inline ::EVSInfo::EVInsultCtrl* EVSSysCtrl::_internal_mutable_insultctrl() {
  
  if (insultctrl_ == nullptr) {
    auto* p = CreateMaybeMessage<::EVSInfo::EVInsultCtrl>(GetArenaForAllocation());
    insultctrl_ = p;
  }
  return insultctrl_;
}
inline ::EVSInfo::EVInsultCtrl* EVSSysCtrl::mutable_insultctrl() {
  ::EVSInfo::EVInsultCtrl* _msg = _internal_mutable_insultctrl();
  // @@protoc_insertion_point(field_mutable:gcu_evs_hmi.protobuf.EVSSysCtrl.insultCtrl)
  return _msg;
}
inline void EVSSysCtrl::set_allocated_insultctrl(::EVSInfo::EVInsultCtrl* insultctrl) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(insultctrl_);
  }
  if (insultctrl) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(insultctrl));
    if (message_arena != submessage_arena) {
      insultctrl = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, insultctrl, submessage_arena);
    }
    
  } else {
    
  }
  insultctrl_ = insultctrl;
  // @@protoc_insertion_point(field_set_allocated:gcu_evs_hmi.protobuf.EVSSysCtrl.insultCtrl)
}

// .EVSInfo.IpMsg ipMsg = 4;
inline bool EVSSysCtrl::_internal_has_ipmsg() const {
  return this != internal_default_instance() && ipmsg_ != nullptr;
}
inline bool EVSSysCtrl::has_ipmsg() const {
  return _internal_has_ipmsg();
}
inline const ::EVSInfo::IpMsg& EVSSysCtrl::_internal_ipmsg() const {
  const ::EVSInfo::IpMsg* p = ipmsg_;
  return p != nullptr ? *p : reinterpret_cast<const ::EVSInfo::IpMsg&>(
      ::EVSInfo::_IpMsg_default_instance_);
}
inline const ::EVSInfo::IpMsg& EVSSysCtrl::ipmsg() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSSysCtrl.ipMsg)
  return _internal_ipmsg();
}
inline void EVSSysCtrl::unsafe_arena_set_allocated_ipmsg(
    ::EVSInfo::IpMsg* ipmsg) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ipmsg_);
  }
  ipmsg_ = ipmsg;
  if (ipmsg) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gcu_evs_hmi.protobuf.EVSSysCtrl.ipMsg)
}
inline ::EVSInfo::IpMsg* EVSSysCtrl::release_ipmsg() {
  
  ::EVSInfo::IpMsg* temp = ipmsg_;
  ipmsg_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::EVSInfo::IpMsg* EVSSysCtrl::unsafe_arena_release_ipmsg() {
  // @@protoc_insertion_point(field_release:gcu_evs_hmi.protobuf.EVSSysCtrl.ipMsg)
  
  ::EVSInfo::IpMsg* temp = ipmsg_;
  ipmsg_ = nullptr;
  return temp;
}
inline ::EVSInfo::IpMsg* EVSSysCtrl::_internal_mutable_ipmsg() {
  
  if (ipmsg_ == nullptr) {
    auto* p = CreateMaybeMessage<::EVSInfo::IpMsg>(GetArenaForAllocation());
    ipmsg_ = p;
  }
  return ipmsg_;
}
inline ::EVSInfo::IpMsg* EVSSysCtrl::mutable_ipmsg() {
  ::EVSInfo::IpMsg* _msg = _internal_mutable_ipmsg();
  // @@protoc_insertion_point(field_mutable:gcu_evs_hmi.protobuf.EVSSysCtrl.ipMsg)
  return _msg;
}
inline void EVSSysCtrl::set_allocated_ipmsg(::EVSInfo::IpMsg* ipmsg) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ipmsg_);
  }
  if (ipmsg) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ipmsg));
    if (message_arena != submessage_arena) {
      ipmsg = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ipmsg, submessage_arena);
    }
    
  } else {
    
  }
  ipmsg_ = ipmsg;
  // @@protoc_insertion_point(field_set_allocated:gcu_evs_hmi.protobuf.EVSSysCtrl.ipMsg)
}

// -------------------------------------------------------------------

// EVSStepMsgSet

// uint64 evsID = 1;
inline void EVSStepMsgSet::clear_evsid() {
  evsid_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EVSStepMsgSet::_internal_evsid() const {
  return evsid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EVSStepMsgSet::evsid() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSStepMsgSet.evsID)
  return _internal_evsid();
}
inline void EVSStepMsgSet::_internal_set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  evsid_ = value;
}
inline void EVSStepMsgSet::set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_evsid(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSStepMsgSet.evsID)
}

// .EVSInfo.EVSStepMsg stepMsg = 2;
inline bool EVSStepMsgSet::_internal_has_stepmsg() const {
  return this != internal_default_instance() && stepmsg_ != nullptr;
}
inline bool EVSStepMsgSet::has_stepmsg() const {
  return _internal_has_stepmsg();
}
inline const ::EVSInfo::EVSStepMsg& EVSStepMsgSet::_internal_stepmsg() const {
  const ::EVSInfo::EVSStepMsg* p = stepmsg_;
  return p != nullptr ? *p : reinterpret_cast<const ::EVSInfo::EVSStepMsg&>(
      ::EVSInfo::_EVSStepMsg_default_instance_);
}
inline const ::EVSInfo::EVSStepMsg& EVSStepMsgSet::stepmsg() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSStepMsgSet.stepMsg)
  return _internal_stepmsg();
}
inline void EVSStepMsgSet::unsafe_arena_set_allocated_stepmsg(
    ::EVSInfo::EVSStepMsg* stepmsg) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(stepmsg_);
  }
  stepmsg_ = stepmsg;
  if (stepmsg) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gcu_evs_hmi.protobuf.EVSStepMsgSet.stepMsg)
}
inline ::EVSInfo::EVSStepMsg* EVSStepMsgSet::release_stepmsg() {
  
  ::EVSInfo::EVSStepMsg* temp = stepmsg_;
  stepmsg_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::EVSInfo::EVSStepMsg* EVSStepMsgSet::unsafe_arena_release_stepmsg() {
  // @@protoc_insertion_point(field_release:gcu_evs_hmi.protobuf.EVSStepMsgSet.stepMsg)
  
  ::EVSInfo::EVSStepMsg* temp = stepmsg_;
  stepmsg_ = nullptr;
  return temp;
}
inline ::EVSInfo::EVSStepMsg* EVSStepMsgSet::_internal_mutable_stepmsg() {
  
  if (stepmsg_ == nullptr) {
    auto* p = CreateMaybeMessage<::EVSInfo::EVSStepMsg>(GetArenaForAllocation());
    stepmsg_ = p;
  }
  return stepmsg_;
}
inline ::EVSInfo::EVSStepMsg* EVSStepMsgSet::mutable_stepmsg() {
  ::EVSInfo::EVSStepMsg* _msg = _internal_mutable_stepmsg();
  // @@protoc_insertion_point(field_mutable:gcu_evs_hmi.protobuf.EVSStepMsgSet.stepMsg)
  return _msg;
}
inline void EVSStepMsgSet::set_allocated_stepmsg(::EVSInfo::EVSStepMsg* stepmsg) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(stepmsg_);
  }
  if (stepmsg) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(stepmsg));
    if (message_arena != submessage_arena) {
      stepmsg = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, stepmsg, submessage_arena);
    }
    
  } else {
    
  }
  stepmsg_ = stepmsg;
  // @@protoc_insertion_point(field_set_allocated:gcu_evs_hmi.protobuf.EVSStepMsgSet.stepMsg)
}

// -------------------------------------------------------------------

// EVSGetMsg

// uint64 evsID = 1;
inline void EVSGetMsg::clear_evsid() {
  evsid_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EVSGetMsg::_internal_evsid() const {
  return evsid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EVSGetMsg::evsid() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSGetMsg.evsID)
  return _internal_evsid();
}
inline void EVSGetMsg::_internal_set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  evsid_ = value;
}
inline void EVSGetMsg::set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_evsid(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSGetMsg.evsID)
}

// uint32 msgID = 2;
inline void EVSGetMsg::clear_msgid() {
  msgid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSGetMsg::_internal_msgid() const {
  return msgid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSGetMsg::msgid() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSGetMsg.msgID)
  return _internal_msgid();
}
inline void EVSGetMsg::_internal_set_msgid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  msgid_ = value;
}
inline void EVSGetMsg::set_msgid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_msgid(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSGetMsg.msgID)
}

// uint64 getTime = 3;
inline void EVSGetMsg::clear_gettime() {
  gettime_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EVSGetMsg::_internal_gettime() const {
  return gettime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EVSGetMsg::gettime() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSGetMsg.getTime)
  return _internal_gettime();
}
inline void EVSGetMsg::_internal_set_gettime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  gettime_ = value;
}
inline void EVSGetMsg::set_gettime(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_gettime(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSGetMsg.getTime)
}

// -------------------------------------------------------------------

// EVSSetReply

// uint64 evsID = 1;
inline void EVSSetReply::clear_evsid() {
  evsid_ = uint64_t{0u};
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EVSSetReply::_internal_evsid() const {
  return evsid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EVSSetReply::evsid() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSSetReply.evsID)
  return _internal_evsid();
}
inline void EVSSetReply::_internal_set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  evsid_ = value;
}
inline void EVSSetReply::set_evsid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_evsid(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSSetReply.evsID)
}

// uint32 msgID = 2;
inline void EVSSetReply::clear_msgid() {
  msgid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSSetReply::_internal_msgid() const {
  return msgid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSSetReply::msgid() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSSetReply.msgID)
  return _internal_msgid();
}
inline void EVSSetReply::_internal_set_msgid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  msgid_ = value;
}
inline void EVSSetReply::set_msgid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_msgid(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSSetReply.msgID)
}

// uint32 setAck = 3;
inline void EVSSetReply::clear_setack() {
  setack_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSSetReply::_internal_setack() const {
  return setack_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 EVSSetReply::setack() const {
  // @@protoc_insertion_point(field_get:gcu_evs_hmi.protobuf.EVSSetReply.setAck)
  return _internal_setack();
}
inline void EVSSetReply::_internal_set_setack(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  setack_ = value;
}
inline void EVSSetReply::set_setack(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_setack(value);
  // @@protoc_insertion_point(field_set:gcu_evs_hmi.protobuf.EVSSetReply.setAck)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf
}  // namespace gcu_evs_hmi

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_GCU_5fEVS_5fHMI_2eproto
