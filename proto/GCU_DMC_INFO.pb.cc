// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GCU_DMC_INFO.proto

#include "GCU_DMC_INFO.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace DMCinfo {
constexpr PUBInfo::PUBInfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : gunmachinfo_()
  , terminalid_(0u)
  , gunamount_(0u)
  , guntype_(0)

  , sysvolmax_(0)
  , syscurmax_(0)
  , syscurmin_(0)
  , sysminvolcv_(0)
  , sysminvolcc_(0)
  , erroraswarning_(0u)
  , adfixmode_(0u)
  , gunmatchmode_(0u)
  , lqdcooloiladdcmd_(0u)
  , runmode_(0)

  , guncode_(0u)
  , multgunparallmode_(0u){}
struct PUBInfoDefaultTypeInternal {
  constexpr PUBInfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PUBInfoDefaultTypeInternal() {}
  union {
    PUBInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PUBInfoDefaultTypeInternal _PUBInfo_default_instance_;
constexpr VCIInfo::VCIInfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : guninfo_()
  , terminalid_(0u)
  , cooperatemode_(0u)
  , dcfanenable_(0u)
  , smokeenable_(0u)
  , toppleandfallenable_(0u)
  , waterenable_(0u)
  , lightningenable_(0u)
  , dustproofenable_(0u)
  , emergencystoptype_(0u){}
struct VCIInfoDefaultTypeInternal {
  constexpr VCIInfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~VCIInfoDefaultTypeInternal() {}
  union {
    VCIInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT VCIInfoDefaultTypeInternal _VCIInfo_default_instance_;
constexpr GUNInfo::GUNInfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : activeprotpara_(nullptr)
  , gunid_(0u)
  , gbtprototype_(0)

  , emetertype_(0u)
  , elockmode_(0u)
  , temperaturemode_(0u)
  , guncurrent_(0u)
  , maincontactorcurrent_(0u)
  , guntempenable_(0u)
  , auxtype_(0u){}
struct GUNInfoDefaultTypeInternal {
  constexpr GUNInfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GUNInfoDefaultTypeInternal() {}
  union {
    GUNInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GUNInfoDefaultTypeInternal _GUNInfo_default_instance_;
constexpr ADModuleInfo::ADModuleInfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : dcmodulesn_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , softversion_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , hardversion_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , id_(0u)
  , currmax_(0)
  , limitpower_(0)
  , volmax_(0)
  , volmin_(0)
  , ratedvol_(0)
  , ratedpower_(0)
  , ratedcurr_(0)
  , ratedinputvol_(0){}
struct ADModuleInfoDefaultTypeInternal {
  constexpr ADModuleInfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ADModuleInfoDefaultTypeInternal() {}
  union {
    ADModuleInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ADModuleInfoDefaultTypeInternal _ADModuleInfo_default_instance_;
constexpr PMMInfo::PMMInfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : admodule_()
  , matrixtype_(0u)
  , matrixcontactorcurrent_(0u)
  , contactorac_(0u)
  , emergencystoptype_(0u)
  , limitpower_(0u)
  , admoduletype_(0)

  , tempenable_(0u)
  , acfanenable_(0u)
  , smokeenable_(0u)
  , toppleandfallenable_(0u)
  , waterenable_(0u)
  , lightningenable_(0u)
  , breakerenable_(0u)
  , dustproofenable_(0u)
  , acfannum_(0u)
  , modulesilencemode_(0)

  , pdutype_(0u)
  , coolingtype_(0u)
  , liquiddevicetype_(0u)
  , liquidcooladdliquid_(0u){}
struct PMMInfoDefaultTypeInternal {
  constexpr PMMInfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PMMInfoDefaultTypeInternal() {}
  union {
    PMMInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PMMInfoDefaultTypeInternal _PMMInfo_default_instance_;
constexpr ServerInfo::ServerInfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : platname_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , oscprocess_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , platform_(0u)
  , qrcode_(0u)
  , vin_(0u)
  , card_(0u)
  , wifiofflinechgmode_(0u){}
struct ServerInfoDefaultTypeInternal {
  constexpr ServerInfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ServerInfoDefaultTypeInternal() {}
  union {
    ServerInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ServerInfoDefaultTypeInternal _ServerInfo_default_instance_;
constexpr OHPInfo::OHPInfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : servers_()
  , guninfo_()
  , macip_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , wifiip_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , productmodel_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ccid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , terminalid_(0u)
  , nettype_(0u)
  , vin_(0u)
  , adminmode_(0u)
  , auxvisiable_(0u)
  , cooperatemode_(0u)
  , orderhistory_(0u)
  , faultshistory_(0u)
  , stoptype_(0u)
  , ratetype_(0u)
  , standbylogotype_(0u)
  , ledtype_(0u)
  , stopchgsoctype_(0u)
  , hmiconfigenable_(0u)
  , netofflinewifienable_(0u)
  , vlprenable_(0u)
  , ratedpower_(0u)
  , maxcurrent_(0u){}
struct OHPInfoDefaultTypeInternal {
  constexpr OHPInfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~OHPInfoDefaultTypeInternal() {}
  union {
    OHPInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT OHPInfoDefaultTypeInternal _OHPInfo_default_instance_;
constexpr HmcChargingStrategyM::HmcChargingStrategyM(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : chargestrategy_(0)

  , chargestar_(0)

  , starttime_(0u)
  , endtime_(0u){}
struct HmcChargingStrategyMDefaultTypeInternal {
  constexpr HmcChargingStrategyMDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~HmcChargingStrategyMDefaultTypeInternal() {}
  union {
    HmcChargingStrategyM _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT HmcChargingStrategyMDefaultTypeInternal _HmcChargingStrategyM_default_instance_;
constexpr HMCInfo::HMCInfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : platinfo_()
  , strategy_()
  , terminalcode_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , terminalid_(0u)
  , lcrtype_(0)

  , soc_(0u){}
struct HMCInfoDefaultTypeInternal {
  constexpr HMCInfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~HMCInfoDefaultTypeInternal() {}
  union {
    HMCInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT HMCInfoDefaultTypeInternal _HMCInfo_default_instance_;
constexpr GunLoadConstraint::GunLoadConstraint(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : gunnum_()
  , _gunnum_cached_byte_size_(0)
  , terminalid_(0u)
  , gunid_(0u)
  , gunlimitpower_(0u){}
struct GunLoadConstraintDefaultTypeInternal {
  constexpr GunLoadConstraintDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GunLoadConstraintDefaultTypeInternal() {}
  union {
    GunLoadConstraint _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GunLoadConstraintDefaultTypeInternal _GunLoadConstraint_default_instance_;
constexpr GunMatchInfo::GunMatchInfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : gunnum_()
  , _gunnum_cached_byte_size_(0)
  , gunid_(0u)
  , isadjacent_(0u){}
struct GunMatchInfoDefaultTypeInternal {
  constexpr GunMatchInfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GunMatchInfoDefaultTypeInternal() {}
  union {
    GunMatchInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GunMatchInfoDefaultTypeInternal _GunMatchInfo_default_instance_;
constexpr CurrentBalance::CurrentBalance(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : equalchgcurrcoef_(0)
  , allowequalchgtime_(0u){}
struct CurrentBalanceDefaultTypeInternal {
  constexpr CurrentBalanceDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CurrentBalanceDefaultTypeInternal() {}
  union {
    CurrentBalance _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CurrentBalanceDefaultTypeInternal _CurrentBalance_default_instance_;
constexpr HotRunaway::HotRunaway(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : hotrunthreshold_(0)
  , hotrunconfiretime_(0u)
  , hotrunprotdisabled_(0u){}
struct HotRunawayDefaultTypeInternal {
  constexpr HotRunawayDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~HotRunawayDefaultTypeInternal() {}
  union {
    HotRunaway _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT HotRunawayDefaultTypeInternal _HotRunaway_default_instance_;
constexpr CellOverVolt::CellOverVolt(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : lifepo4cellallowedchgvoltmax_(0)
  , limnnicocellallowedchgvoltmax_(0)
  , lititanatecellallowedchgvoltmax_(0)
  , limanganatecellallowedchgvoltmax_(0)
  , cellovervoltconfiretime_(0u)
  , cellovervoltprotdisabled_(0u){}
struct CellOverVoltDefaultTypeInternal {
  constexpr CellOverVoltDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CellOverVoltDefaultTypeInternal() {}
  union {
    CellOverVolt _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CellOverVoltDefaultTypeInternal _CellOverVolt_default_instance_;
constexpr PackOverVolt::PackOverVolt(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : packallowedchgvoltmax_(0)
  , packovervoltconfiretime_(0u)
  , packovervoltprotdisabled_(0u){}
struct PackOverVoltDefaultTypeInternal {
  constexpr PackOverVoltDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PackOverVoltDefaultTypeInternal() {}
  union {
    PackOverVolt _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PackOverVoltDefaultTypeInternal _PackOverVolt_default_instance_;
constexpr OverCurrent::OverCurrent(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : overcurrthreshold_(0)
  , overcurrconfiretime_(0u)
  , overcurrprotdisabled_(0u){}
struct OverCurrentDefaultTypeInternal {
  constexpr OverCurrentDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~OverCurrentDefaultTypeInternal() {}
  union {
    OverCurrent _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT OverCurrentDefaultTypeInternal _OverCurrent_default_instance_;
constexpr CellOverTemp::CellOverTemp(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : lifepo4overtempthreshold_(0)
  , limnnicoovertempthreshold_(0)
  , lititanateovertempthreshold_(0)
  , limanganateovertempthreshold_(0)
  , overtempconfiretime_(0u)
  , overtempprotdisabled_(0u){}
struct CellOverTempDefaultTypeInternal {
  constexpr CellOverTempDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CellOverTempDefaultTypeInternal() {}
  union {
    CellOverTemp _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CellOverTempDefaultTypeInternal _CellOverTemp_default_instance_;
constexpr LowTemp::LowTemp(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : lowtempthreshold_(0)
  , lowtempconfiretime_(0u)
  , lowtempprotdisabled_(0u){}
struct LowTempDefaultTypeInternal {
  constexpr LowTempDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LowTempDefaultTypeInternal() {}
  union {
    LowTemp _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LowTempDefaultTypeInternal _LowTemp_default_instance_;
constexpr BMSRlyStick::BMSRlyStick(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : bmsrlystickvoltthreshold_(0)
  , bmsrlystickconfiretime_(0u)
  , bmsrlystickdisabled_(0u){}
struct BMSRlyStickDefaultTypeInternal {
  constexpr BMSRlyStickDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~BMSRlyStickDefaultTypeInternal() {}
  union {
    BMSRlyStick _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT BMSRlyStickDefaultTypeInternal _BMSRlyStick_default_instance_;
constexpr BMSRlyOC::BMSRlyOC(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : bmsrlyocvoltthreshold_(0)
  , bmsrlyoccurrentthreshold_(0)
  , bmsrlyocconfiretime_(0u)
  , bmsrlyocdisabled_(0u){}
struct BMSRlyOCDefaultTypeInternal {
  constexpr BMSRlyOCDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~BMSRlyOCDefaultTypeInternal() {}
  union {
    BMSRlyOC _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT BMSRlyOCDefaultTypeInternal _BMSRlyOC_default_instance_;
constexpr OverCharge::OverCharge(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : overchgcoef_(0)
  , overchgah_(0)
  , overchgconfiretime_(0u)
  , overchgprotdisabled_(0u){}
struct OverChargeDefaultTypeInternal {
  constexpr OverChargeDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~OverChargeDefaultTypeInternal() {}
  union {
    OverCharge _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT OverChargeDefaultTypeInternal _OverCharge_default_instance_;
constexpr BMSDataRepeat::BMSDataRepeat(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : bmsdatarepeatconfiretime_(0u)
  , bmbmsdataerrorprotdisabled_(0u)
  , bmsdataerrorprotdisabled_(0u){}
struct BMSDataRepeatDefaultTypeInternal {
  constexpr BMSDataRepeatDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~BMSDataRepeatDefaultTypeInternal() {}
  union {
    BMSDataRepeat _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT BMSDataRepeatDefaultTypeInternal _BMSDataRepeat_default_instance_;
constexpr ActiveProtectParam::ActiveProtectParam(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : currbalance_(nullptr)
  , hotrunaway_(nullptr)
  , cellovervolt_(nullptr)
  , packovervolt_(nullptr)
  , overcurrent_(nullptr)
  , cellovertemp_(nullptr)
  , lowtemp_(nullptr)
  , bmsrlystick_(nullptr)
  , bmsrlyoc_(nullptr)
  , overcharge_(nullptr)
  , bmsdatarepeat_(nullptr){}
struct ActiveProtectParamDefaultTypeInternal {
  constexpr ActiveProtectParamDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ActiveProtectParamDefaultTypeInternal() {}
  union {
    ActiveProtectParam _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ActiveProtectParamDefaultTypeInternal _ActiveProtectParam_default_instance_;
}  // namespace DMCinfo
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_GCU_5fDMC_5fINFO_2eproto[23];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_GCU_5fDMC_5fINFO_2eproto[8];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_GCU_5fDMC_5fINFO_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_GCU_5fDMC_5fINFO_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PUBInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PUBInfo, terminalid_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PUBInfo, gunamount_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PUBInfo, guntype_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PUBInfo, sysvolmax_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PUBInfo, syscurmax_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PUBInfo, syscurmin_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PUBInfo, sysminvolcv_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PUBInfo, sysminvolcc_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PUBInfo, erroraswarning_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PUBInfo, adfixmode_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PUBInfo, gunmatchmode_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PUBInfo, lqdcooloiladdcmd_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PUBInfo, runmode_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PUBInfo, guncode_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PUBInfo, gunmachinfo_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PUBInfo, multgunparallmode_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::VCIInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::VCIInfo, terminalid_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::VCIInfo, cooperatemode_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::VCIInfo, dcfanenable_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::VCIInfo, smokeenable_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::VCIInfo, toppleandfallenable_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::VCIInfo, waterenable_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::VCIInfo, lightningenable_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::VCIInfo, dustproofenable_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::VCIInfo, emergencystoptype_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::VCIInfo, guninfo_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::GUNInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::GUNInfo, gunid_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::GUNInfo, gbtprototype_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::GUNInfo, emetertype_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::GUNInfo, elockmode_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::GUNInfo, temperaturemode_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::GUNInfo, guncurrent_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::GUNInfo, maincontactorcurrent_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::GUNInfo, guntempenable_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::GUNInfo, auxtype_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::GUNInfo, activeprotpara_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ADModuleInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ADModuleInfo, id_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ADModuleInfo, currmax_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ADModuleInfo, limitpower_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ADModuleInfo, volmax_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ADModuleInfo, volmin_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ADModuleInfo, ratedvol_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ADModuleInfo, ratedpower_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ADModuleInfo, ratedcurr_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ADModuleInfo, ratedinputvol_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ADModuleInfo, dcmodulesn_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ADModuleInfo, softversion_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ADModuleInfo, hardversion_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PMMInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PMMInfo, matrixtype_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PMMInfo, matrixcontactorcurrent_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PMMInfo, contactorac_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PMMInfo, emergencystoptype_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PMMInfo, limitpower_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PMMInfo, admoduletype_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PMMInfo, admodule_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PMMInfo, tempenable_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PMMInfo, acfanenable_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PMMInfo, smokeenable_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PMMInfo, toppleandfallenable_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PMMInfo, waterenable_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PMMInfo, lightningenable_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PMMInfo, breakerenable_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PMMInfo, dustproofenable_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PMMInfo, acfannum_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PMMInfo, modulesilencemode_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PMMInfo, pdutype_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PMMInfo, coolingtype_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PMMInfo, liquiddevicetype_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PMMInfo, liquidcooladdliquid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ServerInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ServerInfo, platform_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ServerInfo, qrcode_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ServerInfo, vin_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ServerInfo, card_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ServerInfo, platname_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ServerInfo, wifiofflinechgmode_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ServerInfo, oscprocess_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, terminalid_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, nettype_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, vin_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, adminmode_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, auxvisiable_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, cooperatemode_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, macip_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, wifiip_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, servers_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, orderhistory_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, faultshistory_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, stoptype_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, ratetype_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, standbylogotype_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, ledtype_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, stopchgsoctype_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, hmiconfigenable_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, netofflinewifienable_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, vlprenable_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, guninfo_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, ratedpower_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, productmodel_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, ccid_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OHPInfo, maxcurrent_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::HmcChargingStrategyM, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::HmcChargingStrategyM, chargestrategy_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::HmcChargingStrategyM, chargestar_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::HmcChargingStrategyM, starttime_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::HmcChargingStrategyM, endtime_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::HMCInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::HMCInfo, terminalid_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::HMCInfo, lcrtype_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::HMCInfo, terminalcode_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::HMCInfo, soc_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::HMCInfo, platinfo_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::HMCInfo, strategy_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::GunLoadConstraint, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::GunLoadConstraint, terminalid_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::GunLoadConstraint, gunid_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::GunLoadConstraint, gunnum_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::GunLoadConstraint, gunlimitpower_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::GunMatchInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::GunMatchInfo, gunid_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::GunMatchInfo, gunnum_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::GunMatchInfo, isadjacent_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::CurrentBalance, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::CurrentBalance, equalchgcurrcoef_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::CurrentBalance, allowequalchgtime_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::HotRunaway, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::HotRunaway, hotrunthreshold_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::HotRunaway, hotrunconfiretime_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::HotRunaway, hotrunprotdisabled_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::CellOverVolt, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::CellOverVolt, lifepo4cellallowedchgvoltmax_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::CellOverVolt, limnnicocellallowedchgvoltmax_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::CellOverVolt, lititanatecellallowedchgvoltmax_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::CellOverVolt, limanganatecellallowedchgvoltmax_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::CellOverVolt, cellovervoltconfiretime_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::CellOverVolt, cellovervoltprotdisabled_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PackOverVolt, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PackOverVolt, packallowedchgvoltmax_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PackOverVolt, packovervoltconfiretime_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::PackOverVolt, packovervoltprotdisabled_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OverCurrent, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OverCurrent, overcurrthreshold_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OverCurrent, overcurrconfiretime_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OverCurrent, overcurrprotdisabled_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::CellOverTemp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::CellOverTemp, lifepo4overtempthreshold_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::CellOverTemp, limnnicoovertempthreshold_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::CellOverTemp, lititanateovertempthreshold_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::CellOverTemp, limanganateovertempthreshold_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::CellOverTemp, overtempconfiretime_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::CellOverTemp, overtempprotdisabled_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::LowTemp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::LowTemp, lowtempthreshold_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::LowTemp, lowtempconfiretime_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::LowTemp, lowtempprotdisabled_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::BMSRlyStick, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::BMSRlyStick, bmsrlystickvoltthreshold_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::BMSRlyStick, bmsrlystickconfiretime_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::BMSRlyStick, bmsrlystickdisabled_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::BMSRlyOC, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::BMSRlyOC, bmsrlyocvoltthreshold_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::BMSRlyOC, bmsrlyoccurrentthreshold_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::BMSRlyOC, bmsrlyocconfiretime_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::BMSRlyOC, bmsrlyocdisabled_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OverCharge, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OverCharge, overchgcoef_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OverCharge, overchgah_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OverCharge, overchgconfiretime_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::OverCharge, overchgprotdisabled_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::BMSDataRepeat, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::BMSDataRepeat, bmsdatarepeatconfiretime_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::BMSDataRepeat, bmbmsdataerrorprotdisabled_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::BMSDataRepeat, bmsdataerrorprotdisabled_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ActiveProtectParam, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ActiveProtectParam, currbalance_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ActiveProtectParam, hotrunaway_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ActiveProtectParam, cellovervolt_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ActiveProtectParam, packovervolt_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ActiveProtectParam, overcurrent_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ActiveProtectParam, cellovertemp_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ActiveProtectParam, lowtemp_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ActiveProtectParam, bmsrlystick_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ActiveProtectParam, bmsrlyoc_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ActiveProtectParam, overcharge_),
  PROTOBUF_FIELD_OFFSET(::DMCinfo::ActiveProtectParam, bmsdatarepeat_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::DMCinfo::PUBInfo)},
  { 21, -1, sizeof(::DMCinfo::VCIInfo)},
  { 36, -1, sizeof(::DMCinfo::GUNInfo)},
  { 51, -1, sizeof(::DMCinfo::ADModuleInfo)},
  { 68, -1, sizeof(::DMCinfo::PMMInfo)},
  { 94, -1, sizeof(::DMCinfo::ServerInfo)},
  { 106, -1, sizeof(::DMCinfo::OHPInfo)},
  { 135, -1, sizeof(::DMCinfo::HmcChargingStrategyM)},
  { 144, -1, sizeof(::DMCinfo::HMCInfo)},
  { 155, -1, sizeof(::DMCinfo::GunLoadConstraint)},
  { 164, -1, sizeof(::DMCinfo::GunMatchInfo)},
  { 172, -1, sizeof(::DMCinfo::CurrentBalance)},
  { 179, -1, sizeof(::DMCinfo::HotRunaway)},
  { 187, -1, sizeof(::DMCinfo::CellOverVolt)},
  { 198, -1, sizeof(::DMCinfo::PackOverVolt)},
  { 206, -1, sizeof(::DMCinfo::OverCurrent)},
  { 214, -1, sizeof(::DMCinfo::CellOverTemp)},
  { 225, -1, sizeof(::DMCinfo::LowTemp)},
  { 233, -1, sizeof(::DMCinfo::BMSRlyStick)},
  { 241, -1, sizeof(::DMCinfo::BMSRlyOC)},
  { 250, -1, sizeof(::DMCinfo::OverCharge)},
  { 259, -1, sizeof(::DMCinfo::BMSDataRepeat)},
  { 267, -1, sizeof(::DMCinfo::ActiveProtectParam)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_PUBInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_VCIInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_GUNInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_ADModuleInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_PMMInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_ServerInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_OHPInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_HmcChargingStrategyM_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_HMCInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_GunLoadConstraint_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_GunMatchInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_CurrentBalance_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_HotRunaway_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_CellOverVolt_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_PackOverVolt_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_OverCurrent_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_CellOverTemp_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_LowTemp_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_BMSRlyStick_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_BMSRlyOC_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_OverCharge_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_BMSDataRepeat_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DMCinfo::_ActiveProtectParam_default_instance_),
};

const char descriptor_table_protodef_GCU_5fDMC_5fINFO_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\022GCU_DMC_INFO.proto\022\007DMCinfo\"\224\003\n\007PUBInf"
  "o\022\022\n\nterminalID\030\001 \001(\r\022\021\n\tGunAmount\030\002 \001(\r"
  "\022%\n\007GunType\030\003 \001(\0162\024.DMCinfo.GunTypeEnum\022"
  "\021\n\tsysVolMax\030\004 \001(\002\022\021\n\tsysCurMax\030\005 \001(\002\022\021\n"
  "\tsysCurMin\030\006 \001(\002\022\023\n\013sysMinVolCV\030\007 \001(\002\022\023\n"
  "\013sysMinVolCC\030\010 \001(\002\022\026\n\016ErrorAsWarning\030\t \001"
  "(\r\022\021\n\tADFixMode\030\n \001(\r\022\024\n\014gunMatchMode\030\013 "
  "\001(\r\022\030\n\020lqdCoolOilAddCmd\030\014 \001(\r\022%\n\007runMode"
  "\030\r \001(\0162\024.DMCinfo.RunModeEnum\022\017\n\007GunCode\030"
  "\016 \001(\r\022*\n\013gunMachInfo\030\017 \003(\0132\025.DMCinfo.Gun"
  "MatchInfo\022\031\n\021multGunParallMode\030\020 \001(\r\"\200\002\n"
  "\007VCIInfo\022\022\n\nterminalID\030\001 \001(\r\022\025\n\rCooperat"
  "eMode\030\002 \001(\r\022\023\n\013DCFanEnable\030\003 \001(\r\022\023\n\013Smok"
  "eEnable\030\004 \001(\r\022\033\n\023ToppleAndFallEnable\030\005 \001"
  "(\r\022\023\n\013WaterEnable\030\006 \001(\r\022\027\n\017LightningEnab"
  "le\030\007 \001(\r\022\027\n\017DustproofEnable\030\010 \001(\r\022\031\n\021Eme"
  "rgencyStopType\030\t \001(\r\022!\n\007gunInfo\030\n \003(\0132\020."
  "DMCinfo.GUNInfo\"\222\002\n\007GUNInfo\022\r\n\005gunID\030\001 \001"
  "(\r\022)\n\014GbtProtoType\030\002 \001(\0162\023.DMCinfo.GunPr"
  "oEnum\022\022\n\nEmeterType\030\003 \001(\r\022\021\n\tElockMode\030\004"
  " \001(\r\022\027\n\017TemperatureMode\030\005 \001(\r\022\022\n\nGunCurr"
  "ent\030\006 \001(\r\022\034\n\024MainContactorCurrent\030\007 \001(\r\022"
  "\025\n\rgunTempEnable\030\010 \001(\r\022\017\n\007AuxType\030\t \001(\r\022"
  "3\n\016ActiveProtPara\030\n \001(\0132\033.DMCinfo.Active"
  "ProtectParam\"\355\001\n\014ADModuleInfo\022\n\n\002ID\030\001 \001("
  "\r\022\017\n\007currMax\030\002 \001(\002\022\022\n\nlimitPower\030\003 \001(\002\022\016"
  "\n\006volMax\030\004 \001(\002\022\016\n\006volMin\030\005 \001(\002\022\020\n\010ratedV"
  "ol\030\006 \001(\002\022\022\n\nratedPower\030\007 \001(\002\022\021\n\tratedCur"
  "r\030\010 \001(\002\022\025\n\rratedInputVol\030\t \001(\002\022\022\n\nDCModu"
  "leSN\030\n \001(\014\022\023\n\013softVersion\030\013 \001(\014\022\023\n\013hardV"
  "ersion\030\014 \001(\014\"\270\004\n\007PMMInfo\022\022\n\nMatrixType\030\001"
  " \001(\r\022\036\n\026MatrixContactorCurrent\030\002 \001(\r\022\023\n\013"
  "ContactorAc\030\003 \001(\r\022\031\n\021EmergencyStopType\030\004"
  " \001(\r\022\022\n\nlimitPower\030\005 \001(\r\022/\n\014ADModuleType"
  "\030\006 \001(\0162\031.DMCinfo.ADModuleTypeEnum\022\'\n\010ADM"
  "odule\030\007 \003(\0132\025.DMCinfo.ADModuleInfo\022\022\n\nTe"
  "mpEnable\030\010 \001(\r\022\023\n\013ACFanEnable\030\t \001(\r\022\023\n\013S"
  "mokeEnable\030\n \001(\r\022\033\n\023ToppleAndFallEnable\030"
  "\013 \001(\r\022\023\n\013WaterEnable\030\014 \001(\r\022\027\n\017LightningE"
  "nable\030\r \001(\r\022\025\n\rBreakerEnable\030\016 \001(\r\022\027\n\017Du"
  "stproofEnable\030\017 \001(\r\022\020\n\010ACFanNum\030\020 \001(\r\0223\n"
  "\021ModuleSilenceMode\030\021 \001(\0162\030.DMCinfo.HvdcS"
  "ilenceMode\022\017\n\007PduType\030\022 \001(\r\022\023\n\013coolingTy"
  "pe\030\023 \001(\r\022\030\n\020liquidDeviceType\030\024 \001(\r\022\033\n\023li"
  "quidCoolAddLiquid\030\025 \001(\r\"\213\001\n\nServerInfo\022\020"
  "\n\010platform\030\001 \001(\r\022\016\n\006qrcode\030\002 \001(\r\022\013\n\003vin\030"
  "\003 \001(\r\022\014\n\004card\030\004 \001(\r\022\020\n\010platName\030\005 \001(\t\022\032\n"
  "\022wifiofflineChgMode\030\006 \001(\r\022\022\n\noscProcess\030"
  "\007 \001(\t\"\214\004\n\007OHPInfo\022\022\n\nterminalID\030\001 \001(\r\022\017\n"
  "\007NetType\030\002 \001(\r\022\013\n\003Vin\030\003 \001(\r\022\021\n\tAdminMode"
  "\030\004 \001(\r\022\023\n\013AuxVisiable\030\005 \001(\r\022\025\n\rCooperate"
  "Mode\030\006 \001(\r\022\r\n\005MACip\030\007 \001(\014\022\016\n\006WiFiip\030\010 \001("
  "\014\022$\n\007servers\030\t \003(\0132\023.DMCinfo.ServerInfo\022"
  "\024\n\014OrderHistory\030\n \001(\r\022\025\n\rFaultsHistory\030\013"
  " \001(\r\022\020\n\010stopType\030\014 \001(\r\022\020\n\010RateType\030\r \001(\r"
  "\022\027\n\017standbylogoType\030\016 \001(\r\022\017\n\007ledType\030\017 \001"
  "(\r\022\026\n\016stopChgSocType\030\020 \001(\r\022\027\n\017hmiConfigE"
  "nable\030\021 \001(\r\022\034\n\024netOfflineWifiEnable\030\022 \001("
  "\r\022\022\n\nVLPREnable\030\023 \001(\r\022!\n\007gunInfo\030\024 \003(\0132\020"
  ".DMCinfo.GUNInfo\022\022\n\nratedPower\030\025 \001(\r\022\024\n\014"
  "productModel\030\026 \001(\t\022\014\n\004ccid\030\027 \001(\t\022\022\n\nmaxC"
  "urrent\030\030 \001(\r\"\242\001\n\024HmcChargingStrategyM\0226\n"
  "\016chargeStrategy\030\001 \001(\0162\036.DMCinfo.HmcCharg"
  "eStrategyEnum\022.\n\nchargeStar\030\002 \001(\0162\032.DMCi"
  "nfo.HmcChargeModeEnum\022\021\n\tstartTime\030\003 \001(\r"
  "\022\017\n\007endTime\030\004 \001(\r\"\276\001\n\007HMCInfo\022\022\n\ntermina"
  "lID\030\001 \001(\r\022$\n\007lcrType\030\002 \001(\0162\023.DMCinfo.Hmc"
  "LcrEnum\022\024\n\014terminalCode\030\003 \001(\t\022\013\n\003soc\030\004 \001"
  "(\r\022%\n\010platInfo\030\005 \003(\0132\023.DMCinfo.ServerInf"
  "o\022/\n\010strategy\030\006 \003(\0132\035.DMCinfo.HmcChargin"
  "gStrategyM\"]\n\021GunLoadConstraint\022\022\n\ntermi"
  "nalID\030\001 \001(\r\022\r\n\005gunID\030\002 \001(\r\022\016\n\006gunNum\030\003 \003"
  "(\r\022\025\n\rGunlimitPower\030\004 \001(\r\"A\n\014GunMatchInf"
  "o\022\r\n\005gunID\030\001 \001(\r\022\016\n\006gunNum\030\002 \003(\r\022\022\n\nisAd"
  "jacent\030\003 \001(\r\"E\n\016CurrentBalance\022\030\n\020EqualC"
  "hgCurrCoef\030\001 \001(\002\022\031\n\021AllowEqualChgTime\030\002 "
  "\001(\r\"\\\n\nHotRunaway\022\027\n\017HotRunThreshold\030\001 \001"
  "(\002\022\031\n\021HotRunConfireTime\030\002 \001(\r\022\032\n\022HotRunP"
  "rotDisabled\030\003 \001(\r\"\361\001\n\014CellOverVolt\022$\n\034Li"
  "FePO4CellAllowedChgVoltMax\030\001 \001(\002\022%\n\035LiMn"
  "NiCoCellAllowedChgVoltMax\030\002 \001(\002\022\'\n\037LiTit"
  "anateCellAllowedChgVoltMax\030\003 \001(\002\022(\n LiMa"
  "nganateCellAllowedChgVoltMax\030\004 \001(\002\022\037\n\027Ce"
  "llOverVoltConfireTime\030\005 \001(\r\022 \n\030CellOverV"
  "oltProtDisabled\030\006 \001(\r\"p\n\014PackOverVolt\022\035\n"
  "\025PackAllowedChgVoltMax\030\001 \001(\002\022\037\n\027PackOver"
  "VoltConfireTime\030\002 \001(\r\022 \n\030PackOverVoltPro"
  "tDisabled\030\003 \001(\r\"c\n\013OverCurrent\022\031\n\021OverCu"
  "rrThreshold\030\001 \001(\002\022\033\n\023OverCurrConfireTime"
  "\030\002 \001(\r\022\034\n\024OverCurrProtDisabled\030\003 \001(\r\"\331\001\n"
  "\014CellOverTemp\022 \n\030LiFePO4OverTempThreshol"
  "d\030\001 \001(\002\022!\n\031LiMnNiCoOverTempThreshold\030\002 \001"
  "(\002\022#\n\033LiTitanateOverTempThreshold\030\003 \001(\002\022"
  "$\n\034LiManganateOverTempThreshold\030\004 \001(\002\022\033\n"
  "\023OverTempConfireTime\030\005 \001(\r\022\034\n\024OverTempPr"
  "otDisabled\030\006 \001(\r\"\\\n\007LowTemp\022\030\n\020LowTempTh"
  "reshold\030\001 \001(\002\022\032\n\022LowTempConfireTime\030\002 \001("
  "\r\022\033\n\023LowTempProtDisabled\030\003 \001(\r\"l\n\013BMSRly"
  "Stick\022 \n\030BMSRlyStickVoltThreshold\030\001 \001(\002\022"
  "\036\n\026BMSRlyStickConfireTime\030\002 \001(\r\022\033\n\023BMSRl"
  "yStickDisabled\030\003 \001(\r\"\202\001\n\010BMSRlyOC\022\035\n\025BMS"
  "RlyOCVoltThreshold\030\001 \001(\002\022 \n\030BMSRlyOCCurr"
  "entThreshold\030\002 \001(\002\022\033\n\023BMSRlyOCConfireTim"
  "e\030\003 \001(\r\022\030\n\020BMSRlyOCDisabled\030\004 \001(\r\"m\n\nOve"
  "rCharge\022\023\n\013OverChgCoef\030\001 \001(\002\022\021\n\tOverChgA"
  "H\030\002 \001(\002\022\032\n\022OverChgConfireTime\030\003 \001(\r\022\033\n\023O"
  "verChgProtDisabled\030\004 \001(\r\"w\n\rBMSDataRepea"
  "t\022 \n\030BMSDataRepeatConfireTime\030\001 \001(\r\022\"\n\032B"
  "MBMSDataErrorProtDisabled\030\002 \001(\r\022 \n\030BMSDa"
  "taErrorProtDisabled\030\003 \001(\r\"\350\003\n\022ActiveProt"
  "ectParam\022,\n\013currBalance\030\001 \001(\0132\027.DMCinfo."
  "CurrentBalance\022\'\n\nhotRunaway\030\002 \001(\0132\023.DMC"
  "info.HotRunaway\022+\n\014cellOverVolt\030\003 \001(\0132\025."
  "DMCinfo.CellOverVolt\022+\n\014packOverVolt\030\004 \001"
  "(\0132\025.DMCinfo.PackOverVolt\022)\n\013overCurrent"
  "\030\005 \001(\0132\024.DMCinfo.OverCurrent\022+\n\014cellOver"
  "Temp\030\006 \001(\0132\025.DMCinfo.CellOverTemp\022!\n\007low"
  "Temp\030\007 \001(\0132\020.DMCinfo.LowTemp\022)\n\013bmsRlySt"
  "ick\030\010 \001(\0132\024.DMCinfo.BMSRlyStick\022#\n\010bmsRl"
  "yOC\030\t \001(\0132\021.DMCinfo.BMSRlyOC\022\'\n\noverChar"
  "ge\030\n \001(\0132\023.DMCinfo.OverCharge\022-\n\rbmsData"
  "Repeat\030\013 \001(\0132\026.DMCinfo.BMSDataRepeat*I\n\n"
  "GunProEnum\022\t\n\005GBT11\020\000\022\t\n\005GBT15\020\001\022\013\n\007GB20"
  "15P\020\002\022\013\n\007GB2023A\020\003\022\013\n\007GB2023B\020\004*<\n\013RunMo"
  "deEnum\022\017\n\013operateMode\020\000\022\016\n\nauthenMode\020\001\022"
  "\014\n\010DCsource\020\002*\203\001\n\013GunTypeEnum\022\014\n\010StandGu"
  "n\020\000\022\n\n\006Chaoji\020\001\022\014\n\010GBT2023B\020\002\022\010\n\004CCS1\020\003\022"
  "\013\n\007CHAdeMO\020\004\022\010\n\004CCS2\020\005\022\007\n\003Bow\020\006\022\007\n\003SCD\020\007"
  "\022\t\n\005FanHP\020\010\022\016\n\nGunGB2015p\020\t*\267\001\n\020ADModule"
  "TypeEnum\022\014\n\010XYBR30KW\020\000\022\014\n\010XYBR40KW\020\001\022\013\n\007"
  "YFY20KW\020\002\022\013\n\007YFY30KW\020\003\022\013\n\007YFY40KW\020\004\022\017\n\013m"
  "egmeet30KW\020\005\022\017\n\013megmeet40KW\020\006\022\r\n\tTonHe30"
  "KW\020\007\022\r\n\tTonHe40KW\020\010\022\017\n\013uugreen30KW\020\t\022\017\n\013"
  "uugreen40KW\020\n*J\n\025HmcChargeStrategyEnum\022\021"
  "\n\rhmccs_default\020\000\022\017\n\013hmccs_allow\020\001\022\r\n\thm"
  "ccs_off\020\002*E\n\021HmcChargeModeEnum\022\021\n\rhmccm_"
  "default\020\000\022\r\n\thmccm_vin\020\001\022\016\n\nhmccm_card\020\002"
  "*t\n\nHmcLcrEnum\022\022\n\016hmclcr_default\020\000\022\016\n\nhm"
  "clcr_soc\020\001\022\022\n\016hmclcr_station\020\002\022\023\n\017hmclcr"
  "_termcode\020\003\022\031\n\025hmclcr_timingStrategy\020\004*."
  "\n\017HvdcSilenceMode\022\013\n\007e_nomal\020\000\022\016\n\ne_lowN"
  "oise\020\001b\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_GCU_5fDMC_5fINFO_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GCU_5fDMC_5fINFO_2eproto = {
  false, false, 5654, descriptor_table_protodef_GCU_5fDMC_5fINFO_2eproto, "GCU_DMC_INFO.proto", 
  &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once, nullptr, 0, 23,
  schemas, file_default_instances, TableStruct_GCU_5fDMC_5fINFO_2eproto::offsets,
  file_level_metadata_GCU_5fDMC_5fINFO_2eproto, file_level_enum_descriptors_GCU_5fDMC_5fINFO_2eproto, file_level_service_descriptors_GCU_5fDMC_5fINFO_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter() {
  return &descriptor_table_GCU_5fDMC_5fINFO_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_GCU_5fDMC_5fINFO_2eproto(&descriptor_table_GCU_5fDMC_5fINFO_2eproto);
namespace DMCinfo {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* GunProEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fDMC_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fDMC_5fINFO_2eproto[0];
}
bool GunProEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RunModeEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fDMC_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fDMC_5fINFO_2eproto[1];
}
bool RunModeEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* GunTypeEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fDMC_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fDMC_5fINFO_2eproto[2];
}
bool GunTypeEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ADModuleTypeEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fDMC_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fDMC_5fINFO_2eproto[3];
}
bool ADModuleTypeEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* HmcChargeStrategyEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fDMC_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fDMC_5fINFO_2eproto[4];
}
bool HmcChargeStrategyEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* HmcChargeModeEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fDMC_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fDMC_5fINFO_2eproto[5];
}
bool HmcChargeModeEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* HmcLcrEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fDMC_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fDMC_5fINFO_2eproto[6];
}
bool HmcLcrEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* HvdcSilenceMode_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fDMC_5fINFO_2eproto);
  return file_level_enum_descriptors_GCU_5fDMC_5fINFO_2eproto[7];
}
bool HvdcSilenceMode_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class PUBInfo::_Internal {
 public:
};

PUBInfo::PUBInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  gunmachinfo_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.PUBInfo)
}
PUBInfo::PUBInfo(const PUBInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      gunmachinfo_(from.gunmachinfo_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&terminalid_, &from.terminalid_,
    static_cast<size_t>(reinterpret_cast<char*>(&multgunparallmode_) -
    reinterpret_cast<char*>(&terminalid_)) + sizeof(multgunparallmode_));
  // @@protoc_insertion_point(copy_constructor:DMCinfo.PUBInfo)
}

inline void PUBInfo::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&terminalid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&multgunparallmode_) -
    reinterpret_cast<char*>(&terminalid_)) + sizeof(multgunparallmode_));
}

PUBInfo::~PUBInfo() {
  // @@protoc_insertion_point(destructor:DMCinfo.PUBInfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PUBInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void PUBInfo::ArenaDtor(void* object) {
  PUBInfo* _this = reinterpret_cast< PUBInfo* >(object);
  (void)_this;
}
void PUBInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PUBInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PUBInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.PUBInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  gunmachinfo_.Clear();
  ::memset(&terminalid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&multgunparallmode_) -
      reinterpret_cast<char*>(&terminalid_)) + sizeof(multgunparallmode_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PUBInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 terminalID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          terminalid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 GunAmount = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          gunamount_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DMCinfo.GunTypeEnum GunType = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_guntype(static_cast<::DMCinfo::GunTypeEnum>(val));
        } else goto handle_unusual;
        continue;
      // float sysVolMax = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 37)) {
          sysvolmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float sysCurMax = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 45)) {
          syscurmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float sysCurMin = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 53)) {
          syscurmin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float sysMinVolCV = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 61)) {
          sysminvolcv_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float sysMinVolCC = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 69)) {
          sysminvolcc_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 ErrorAsWarning = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72)) {
          erroraswarning_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 ADFixMode = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          adfixmode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 gunMatchMode = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          gunmatchmode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 lqdCoolOilAddCmd = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 96)) {
          lqdcooloiladdcmd_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DMCinfo.RunModeEnum runMode = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 104)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_runmode(static_cast<::DMCinfo::RunModeEnum>(val));
        } else goto handle_unusual;
        continue;
      // uint32 GunCode = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 112)) {
          guncode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .DMCinfo.GunMatchInfo gunMachInfo = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 122)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_gunmachinfo(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<122>(ptr));
        } else goto handle_unusual;
        continue;
      // uint32 multGunParallMode = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 128)) {
          multgunparallmode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* PUBInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.PUBInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 terminalID = 1;
  if (this->_internal_terminalid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_terminalid(), target);
  }

  // uint32 GunAmount = 2;
  if (this->_internal_gunamount() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_gunamount(), target);
  }

  // .DMCinfo.GunTypeEnum GunType = 3;
  if (this->_internal_guntype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_guntype(), target);
  }

  // float sysVolMax = 4;
  if (!(this->_internal_sysvolmax() <= 0 && this->_internal_sysvolmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_sysvolmax(), target);
  }

  // float sysCurMax = 5;
  if (!(this->_internal_syscurmax() <= 0 && this->_internal_syscurmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(5, this->_internal_syscurmax(), target);
  }

  // float sysCurMin = 6;
  if (!(this->_internal_syscurmin() <= 0 && this->_internal_syscurmin() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(6, this->_internal_syscurmin(), target);
  }

  // float sysMinVolCV = 7;
  if (!(this->_internal_sysminvolcv() <= 0 && this->_internal_sysminvolcv() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(7, this->_internal_sysminvolcv(), target);
  }

  // float sysMinVolCC = 8;
  if (!(this->_internal_sysminvolcc() <= 0 && this->_internal_sysminvolcc() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(8, this->_internal_sysminvolcc(), target);
  }

  // uint32 ErrorAsWarning = 9;
  if (this->_internal_erroraswarning() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(9, this->_internal_erroraswarning(), target);
  }

  // uint32 ADFixMode = 10;
  if (this->_internal_adfixmode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(10, this->_internal_adfixmode(), target);
  }

  // uint32 gunMatchMode = 11;
  if (this->_internal_gunmatchmode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(11, this->_internal_gunmatchmode(), target);
  }

  // uint32 lqdCoolOilAddCmd = 12;
  if (this->_internal_lqdcooloiladdcmd() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(12, this->_internal_lqdcooloiladdcmd(), target);
  }

  // .DMCinfo.RunModeEnum runMode = 13;
  if (this->_internal_runmode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      13, this->_internal_runmode(), target);
  }

  // uint32 GunCode = 14;
  if (this->_internal_guncode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(14, this->_internal_guncode(), target);
  }

  // repeated .DMCinfo.GunMatchInfo gunMachInfo = 15;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_gunmachinfo_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(15, this->_internal_gunmachinfo(i), target, stream);
  }

  // uint32 multGunParallMode = 16;
  if (this->_internal_multgunparallmode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(16, this->_internal_multgunparallmode(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.PUBInfo)
  return target;
}

size_t PUBInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.PUBInfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .DMCinfo.GunMatchInfo gunMachInfo = 15;
  total_size += 1UL * this->_internal_gunmachinfo_size();
  for (const auto& msg : this->gunmachinfo_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // uint32 terminalID = 1;
  if (this->_internal_terminalid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_terminalid());
  }

  // uint32 GunAmount = 2;
  if (this->_internal_gunamount() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_gunamount());
  }

  // .DMCinfo.GunTypeEnum GunType = 3;
  if (this->_internal_guntype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_guntype());
  }

  // float sysVolMax = 4;
  if (!(this->_internal_sysvolmax() <= 0 && this->_internal_sysvolmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float sysCurMax = 5;
  if (!(this->_internal_syscurmax() <= 0 && this->_internal_syscurmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float sysCurMin = 6;
  if (!(this->_internal_syscurmin() <= 0 && this->_internal_syscurmin() >= 0)) {
    total_size += 1 + 4;
  }

  // float sysMinVolCV = 7;
  if (!(this->_internal_sysminvolcv() <= 0 && this->_internal_sysminvolcv() >= 0)) {
    total_size += 1 + 4;
  }

  // float sysMinVolCC = 8;
  if (!(this->_internal_sysminvolcc() <= 0 && this->_internal_sysminvolcc() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 ErrorAsWarning = 9;
  if (this->_internal_erroraswarning() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_erroraswarning());
  }

  // uint32 ADFixMode = 10;
  if (this->_internal_adfixmode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_adfixmode());
  }

  // uint32 gunMatchMode = 11;
  if (this->_internal_gunmatchmode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_gunmatchmode());
  }

  // uint32 lqdCoolOilAddCmd = 12;
  if (this->_internal_lqdcooloiladdcmd() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_lqdcooloiladdcmd());
  }

  // .DMCinfo.RunModeEnum runMode = 13;
  if (this->_internal_runmode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_runmode());
  }

  // uint32 GunCode = 14;
  if (this->_internal_guncode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_guncode());
  }

  // uint32 multGunParallMode = 16;
  if (this->_internal_multgunparallmode() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_multgunparallmode());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PUBInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PUBInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PUBInfo::GetClassData() const { return &_class_data_; }

void PUBInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<PUBInfo *>(to)->MergeFrom(
      static_cast<const PUBInfo &>(from));
}


void PUBInfo::MergeFrom(const PUBInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.PUBInfo)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  gunmachinfo_.MergeFrom(from.gunmachinfo_);
  if (from._internal_terminalid() != 0) {
    _internal_set_terminalid(from._internal_terminalid());
  }
  if (from._internal_gunamount() != 0) {
    _internal_set_gunamount(from._internal_gunamount());
  }
  if (from._internal_guntype() != 0) {
    _internal_set_guntype(from._internal_guntype());
  }
  if (!(from._internal_sysvolmax() <= 0 && from._internal_sysvolmax() >= 0)) {
    _internal_set_sysvolmax(from._internal_sysvolmax());
  }
  if (!(from._internal_syscurmax() <= 0 && from._internal_syscurmax() >= 0)) {
    _internal_set_syscurmax(from._internal_syscurmax());
  }
  if (!(from._internal_syscurmin() <= 0 && from._internal_syscurmin() >= 0)) {
    _internal_set_syscurmin(from._internal_syscurmin());
  }
  if (!(from._internal_sysminvolcv() <= 0 && from._internal_sysminvolcv() >= 0)) {
    _internal_set_sysminvolcv(from._internal_sysminvolcv());
  }
  if (!(from._internal_sysminvolcc() <= 0 && from._internal_sysminvolcc() >= 0)) {
    _internal_set_sysminvolcc(from._internal_sysminvolcc());
  }
  if (from._internal_erroraswarning() != 0) {
    _internal_set_erroraswarning(from._internal_erroraswarning());
  }
  if (from._internal_adfixmode() != 0) {
    _internal_set_adfixmode(from._internal_adfixmode());
  }
  if (from._internal_gunmatchmode() != 0) {
    _internal_set_gunmatchmode(from._internal_gunmatchmode());
  }
  if (from._internal_lqdcooloiladdcmd() != 0) {
    _internal_set_lqdcooloiladdcmd(from._internal_lqdcooloiladdcmd());
  }
  if (from._internal_runmode() != 0) {
    _internal_set_runmode(from._internal_runmode());
  }
  if (from._internal_guncode() != 0) {
    _internal_set_guncode(from._internal_guncode());
  }
  if (from._internal_multgunparallmode() != 0) {
    _internal_set_multgunparallmode(from._internal_multgunparallmode());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PUBInfo::CopyFrom(const PUBInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.PUBInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PUBInfo::IsInitialized() const {
  return true;
}

void PUBInfo::InternalSwap(PUBInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  gunmachinfo_.InternalSwap(&other->gunmachinfo_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PUBInfo, multgunparallmode_)
      + sizeof(PUBInfo::multgunparallmode_)
      - PROTOBUF_FIELD_OFFSET(PUBInfo, terminalid_)>(
          reinterpret_cast<char*>(&terminalid_),
          reinterpret_cast<char*>(&other->terminalid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PUBInfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[0]);
}

// ===================================================================

class VCIInfo::_Internal {
 public:
};

VCIInfo::VCIInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  guninfo_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.VCIInfo)
}
VCIInfo::VCIInfo(const VCIInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      guninfo_(from.guninfo_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&terminalid_, &from.terminalid_,
    static_cast<size_t>(reinterpret_cast<char*>(&emergencystoptype_) -
    reinterpret_cast<char*>(&terminalid_)) + sizeof(emergencystoptype_));
  // @@protoc_insertion_point(copy_constructor:DMCinfo.VCIInfo)
}

inline void VCIInfo::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&terminalid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&emergencystoptype_) -
    reinterpret_cast<char*>(&terminalid_)) + sizeof(emergencystoptype_));
}

VCIInfo::~VCIInfo() {
  // @@protoc_insertion_point(destructor:DMCinfo.VCIInfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void VCIInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void VCIInfo::ArenaDtor(void* object) {
  VCIInfo* _this = reinterpret_cast< VCIInfo* >(object);
  (void)_this;
}
void VCIInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void VCIInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void VCIInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.VCIInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  guninfo_.Clear();
  ::memset(&terminalid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&emergencystoptype_) -
      reinterpret_cast<char*>(&terminalid_)) + sizeof(emergencystoptype_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* VCIInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 terminalID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          terminalid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 CooperateMode = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          cooperatemode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 DCFanEnable = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          dcfanenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 SmokeEnable = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          smokeenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 ToppleAndFallEnable = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          toppleandfallenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 WaterEnable = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          waterenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 LightningEnable = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          lightningenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 DustproofEnable = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          dustproofenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 EmergencyStopType = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72)) {
          emergencystoptype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .DMCinfo.GUNInfo gunInfo = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 82)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_guninfo(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<82>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* VCIInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.VCIInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 terminalID = 1;
  if (this->_internal_terminalid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_terminalid(), target);
  }

  // uint32 CooperateMode = 2;
  if (this->_internal_cooperatemode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_cooperatemode(), target);
  }

  // uint32 DCFanEnable = 3;
  if (this->_internal_dcfanenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_dcfanenable(), target);
  }

  // uint32 SmokeEnable = 4;
  if (this->_internal_smokeenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_smokeenable(), target);
  }

  // uint32 ToppleAndFallEnable = 5;
  if (this->_internal_toppleandfallenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_toppleandfallenable(), target);
  }

  // uint32 WaterEnable = 6;
  if (this->_internal_waterenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_waterenable(), target);
  }

  // uint32 LightningEnable = 7;
  if (this->_internal_lightningenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(7, this->_internal_lightningenable(), target);
  }

  // uint32 DustproofEnable = 8;
  if (this->_internal_dustproofenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_dustproofenable(), target);
  }

  // uint32 EmergencyStopType = 9;
  if (this->_internal_emergencystoptype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(9, this->_internal_emergencystoptype(), target);
  }

  // repeated .DMCinfo.GUNInfo gunInfo = 10;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_guninfo_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(10, this->_internal_guninfo(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.VCIInfo)
  return target;
}

size_t VCIInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.VCIInfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .DMCinfo.GUNInfo gunInfo = 10;
  total_size += 1UL * this->_internal_guninfo_size();
  for (const auto& msg : this->guninfo_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // uint32 terminalID = 1;
  if (this->_internal_terminalid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_terminalid());
  }

  // uint32 CooperateMode = 2;
  if (this->_internal_cooperatemode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_cooperatemode());
  }

  // uint32 DCFanEnable = 3;
  if (this->_internal_dcfanenable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_dcfanenable());
  }

  // uint32 SmokeEnable = 4;
  if (this->_internal_smokeenable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_smokeenable());
  }

  // uint32 ToppleAndFallEnable = 5;
  if (this->_internal_toppleandfallenable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_toppleandfallenable());
  }

  // uint32 WaterEnable = 6;
  if (this->_internal_waterenable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_waterenable());
  }

  // uint32 LightningEnable = 7;
  if (this->_internal_lightningenable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_lightningenable());
  }

  // uint32 DustproofEnable = 8;
  if (this->_internal_dustproofenable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_dustproofenable());
  }

  // uint32 EmergencyStopType = 9;
  if (this->_internal_emergencystoptype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_emergencystoptype());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData VCIInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    VCIInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*VCIInfo::GetClassData() const { return &_class_data_; }

void VCIInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<VCIInfo *>(to)->MergeFrom(
      static_cast<const VCIInfo &>(from));
}


void VCIInfo::MergeFrom(const VCIInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.VCIInfo)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  guninfo_.MergeFrom(from.guninfo_);
  if (from._internal_terminalid() != 0) {
    _internal_set_terminalid(from._internal_terminalid());
  }
  if (from._internal_cooperatemode() != 0) {
    _internal_set_cooperatemode(from._internal_cooperatemode());
  }
  if (from._internal_dcfanenable() != 0) {
    _internal_set_dcfanenable(from._internal_dcfanenable());
  }
  if (from._internal_smokeenable() != 0) {
    _internal_set_smokeenable(from._internal_smokeenable());
  }
  if (from._internal_toppleandfallenable() != 0) {
    _internal_set_toppleandfallenable(from._internal_toppleandfallenable());
  }
  if (from._internal_waterenable() != 0) {
    _internal_set_waterenable(from._internal_waterenable());
  }
  if (from._internal_lightningenable() != 0) {
    _internal_set_lightningenable(from._internal_lightningenable());
  }
  if (from._internal_dustproofenable() != 0) {
    _internal_set_dustproofenable(from._internal_dustproofenable());
  }
  if (from._internal_emergencystoptype() != 0) {
    _internal_set_emergencystoptype(from._internal_emergencystoptype());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void VCIInfo::CopyFrom(const VCIInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.VCIInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VCIInfo::IsInitialized() const {
  return true;
}

void VCIInfo::InternalSwap(VCIInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  guninfo_.InternalSwap(&other->guninfo_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(VCIInfo, emergencystoptype_)
      + sizeof(VCIInfo::emergencystoptype_)
      - PROTOBUF_FIELD_OFFSET(VCIInfo, terminalid_)>(
          reinterpret_cast<char*>(&terminalid_),
          reinterpret_cast<char*>(&other->terminalid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata VCIInfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[1]);
}

// ===================================================================

class GUNInfo::_Internal {
 public:
  static const ::DMCinfo::ActiveProtectParam& activeprotpara(const GUNInfo* msg);
};

const ::DMCinfo::ActiveProtectParam&
GUNInfo::_Internal::activeprotpara(const GUNInfo* msg) {
  return *msg->activeprotpara_;
}
GUNInfo::GUNInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.GUNInfo)
}
GUNInfo::GUNInfo(const GUNInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_activeprotpara()) {
    activeprotpara_ = new ::DMCinfo::ActiveProtectParam(*from.activeprotpara_);
  } else {
    activeprotpara_ = nullptr;
  }
  ::memcpy(&gunid_, &from.gunid_,
    static_cast<size_t>(reinterpret_cast<char*>(&auxtype_) -
    reinterpret_cast<char*>(&gunid_)) + sizeof(auxtype_));
  // @@protoc_insertion_point(copy_constructor:DMCinfo.GUNInfo)
}

inline void GUNInfo::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&activeprotpara_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&auxtype_) -
    reinterpret_cast<char*>(&activeprotpara_)) + sizeof(auxtype_));
}

GUNInfo::~GUNInfo() {
  // @@protoc_insertion_point(destructor:DMCinfo.GUNInfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GUNInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete activeprotpara_;
}

void GUNInfo::ArenaDtor(void* object) {
  GUNInfo* _this = reinterpret_cast< GUNInfo* >(object);
  (void)_this;
}
void GUNInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GUNInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GUNInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.GUNInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && activeprotpara_ != nullptr) {
    delete activeprotpara_;
  }
  activeprotpara_ = nullptr;
  ::memset(&gunid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&auxtype_) -
      reinterpret_cast<char*>(&gunid_)) + sizeof(auxtype_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GUNInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 gunID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          gunid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DMCinfo.GunProEnum GbtProtoType = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_gbtprototype(static_cast<::DMCinfo::GunProEnum>(val));
        } else goto handle_unusual;
        continue;
      // uint32 EmeterType = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          emetertype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 ElockMode = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          elockmode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 TemperatureMode = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          temperaturemode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 GunCurrent = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          guncurrent_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 MainContactorCurrent = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          maincontactorcurrent_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 gunTempEnable = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          guntempenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 AuxType = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72)) {
          auxtype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DMCinfo.ActiveProtectParam ActiveProtPara = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 82)) {
          ptr = ctx->ParseMessage(_internal_mutable_activeprotpara(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* GUNInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.GUNInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 gunID = 1;
  if (this->_internal_gunid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_gunid(), target);
  }

  // .DMCinfo.GunProEnum GbtProtoType = 2;
  if (this->_internal_gbtprototype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_gbtprototype(), target);
  }

  // uint32 EmeterType = 3;
  if (this->_internal_emetertype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_emetertype(), target);
  }

  // uint32 ElockMode = 4;
  if (this->_internal_elockmode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_elockmode(), target);
  }

  // uint32 TemperatureMode = 5;
  if (this->_internal_temperaturemode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_temperaturemode(), target);
  }

  // uint32 GunCurrent = 6;
  if (this->_internal_guncurrent() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_guncurrent(), target);
  }

  // uint32 MainContactorCurrent = 7;
  if (this->_internal_maincontactorcurrent() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(7, this->_internal_maincontactorcurrent(), target);
  }

  // uint32 gunTempEnable = 8;
  if (this->_internal_guntempenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_guntempenable(), target);
  }

  // uint32 AuxType = 9;
  if (this->_internal_auxtype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(9, this->_internal_auxtype(), target);
  }

  // .DMCinfo.ActiveProtectParam ActiveProtPara = 10;
  if (this->_internal_has_activeprotpara()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        10, _Internal::activeprotpara(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.GUNInfo)
  return target;
}

size_t GUNInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.GUNInfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .DMCinfo.ActiveProtectParam ActiveProtPara = 10;
  if (this->_internal_has_activeprotpara()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *activeprotpara_);
  }

  // uint32 gunID = 1;
  if (this->_internal_gunid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_gunid());
  }

  // .DMCinfo.GunProEnum GbtProtoType = 2;
  if (this->_internal_gbtprototype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_gbtprototype());
  }

  // uint32 EmeterType = 3;
  if (this->_internal_emetertype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_emetertype());
  }

  // uint32 ElockMode = 4;
  if (this->_internal_elockmode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_elockmode());
  }

  // uint32 TemperatureMode = 5;
  if (this->_internal_temperaturemode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_temperaturemode());
  }

  // uint32 GunCurrent = 6;
  if (this->_internal_guncurrent() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_guncurrent());
  }

  // uint32 MainContactorCurrent = 7;
  if (this->_internal_maincontactorcurrent() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_maincontactorcurrent());
  }

  // uint32 gunTempEnable = 8;
  if (this->_internal_guntempenable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_guntempenable());
  }

  // uint32 AuxType = 9;
  if (this->_internal_auxtype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_auxtype());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GUNInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GUNInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GUNInfo::GetClassData() const { return &_class_data_; }

void GUNInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<GUNInfo *>(to)->MergeFrom(
      static_cast<const GUNInfo &>(from));
}


void GUNInfo::MergeFrom(const GUNInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.GUNInfo)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_activeprotpara()) {
    _internal_mutable_activeprotpara()->::DMCinfo::ActiveProtectParam::MergeFrom(from._internal_activeprotpara());
  }
  if (from._internal_gunid() != 0) {
    _internal_set_gunid(from._internal_gunid());
  }
  if (from._internal_gbtprototype() != 0) {
    _internal_set_gbtprototype(from._internal_gbtprototype());
  }
  if (from._internal_emetertype() != 0) {
    _internal_set_emetertype(from._internal_emetertype());
  }
  if (from._internal_elockmode() != 0) {
    _internal_set_elockmode(from._internal_elockmode());
  }
  if (from._internal_temperaturemode() != 0) {
    _internal_set_temperaturemode(from._internal_temperaturemode());
  }
  if (from._internal_guncurrent() != 0) {
    _internal_set_guncurrent(from._internal_guncurrent());
  }
  if (from._internal_maincontactorcurrent() != 0) {
    _internal_set_maincontactorcurrent(from._internal_maincontactorcurrent());
  }
  if (from._internal_guntempenable() != 0) {
    _internal_set_guntempenable(from._internal_guntempenable());
  }
  if (from._internal_auxtype() != 0) {
    _internal_set_auxtype(from._internal_auxtype());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GUNInfo::CopyFrom(const GUNInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.GUNInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GUNInfo::IsInitialized() const {
  return true;
}

void GUNInfo::InternalSwap(GUNInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GUNInfo, auxtype_)
      + sizeof(GUNInfo::auxtype_)
      - PROTOBUF_FIELD_OFFSET(GUNInfo, activeprotpara_)>(
          reinterpret_cast<char*>(&activeprotpara_),
          reinterpret_cast<char*>(&other->activeprotpara_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GUNInfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[2]);
}

// ===================================================================

class ADModuleInfo::_Internal {
 public:
};

ADModuleInfo::ADModuleInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.ADModuleInfo)
}
ADModuleInfo::ADModuleInfo(const ADModuleInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  dcmodulesn_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_dcmodulesn().empty()) {
    dcmodulesn_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_dcmodulesn(), 
      GetArenaForAllocation());
  }
  softversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_softversion().empty()) {
    softversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_softversion(), 
      GetArenaForAllocation());
  }
  hardversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_hardversion().empty()) {
    hardversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_hardversion(), 
      GetArenaForAllocation());
  }
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&ratedinputvol_) -
    reinterpret_cast<char*>(&id_)) + sizeof(ratedinputvol_));
  // @@protoc_insertion_point(copy_constructor:DMCinfo.ADModuleInfo)
}

inline void ADModuleInfo::SharedCtor() {
dcmodulesn_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
softversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
hardversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&ratedinputvol_) -
    reinterpret_cast<char*>(&id_)) + sizeof(ratedinputvol_));
}

ADModuleInfo::~ADModuleInfo() {
  // @@protoc_insertion_point(destructor:DMCinfo.ADModuleInfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ADModuleInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  dcmodulesn_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  softversion_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  hardversion_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ADModuleInfo::ArenaDtor(void* object) {
  ADModuleInfo* _this = reinterpret_cast< ADModuleInfo* >(object);
  (void)_this;
}
void ADModuleInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ADModuleInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ADModuleInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.ADModuleInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  dcmodulesn_.ClearToEmpty();
  softversion_.ClearToEmpty();
  hardversion_.ClearToEmpty();
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&ratedinputvol_) -
      reinterpret_cast<char*>(&id_)) + sizeof(ratedinputvol_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ADModuleInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 ID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float currMax = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 21)) {
          currmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float limitPower = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          limitpower_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float volMax = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 37)) {
          volmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float volMin = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 45)) {
          volmin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float ratedVol = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 53)) {
          ratedvol_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float ratedPower = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 61)) {
          ratedpower_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float ratedCurr = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 69)) {
          ratedcurr_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float ratedInputVol = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 77)) {
          ratedinputvol_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // bytes DCModuleSN = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 82)) {
          auto str = _internal_mutable_dcmodulesn();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes softVersion = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 90)) {
          auto str = _internal_mutable_softversion();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes hardVersion = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 98)) {
          auto str = _internal_mutable_hardversion();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ADModuleInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.ADModuleInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 ID = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_id(), target);
  }

  // float currMax = 2;
  if (!(this->_internal_currmax() <= 0 && this->_internal_currmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_currmax(), target);
  }

  // float limitPower = 3;
  if (!(this->_internal_limitpower() <= 0 && this->_internal_limitpower() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_limitpower(), target);
  }

  // float volMax = 4;
  if (!(this->_internal_volmax() <= 0 && this->_internal_volmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_volmax(), target);
  }

  // float volMin = 5;
  if (!(this->_internal_volmin() <= 0 && this->_internal_volmin() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(5, this->_internal_volmin(), target);
  }

  // float ratedVol = 6;
  if (!(this->_internal_ratedvol() <= 0 && this->_internal_ratedvol() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(6, this->_internal_ratedvol(), target);
  }

  // float ratedPower = 7;
  if (!(this->_internal_ratedpower() <= 0 && this->_internal_ratedpower() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(7, this->_internal_ratedpower(), target);
  }

  // float ratedCurr = 8;
  if (!(this->_internal_ratedcurr() <= 0 && this->_internal_ratedcurr() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(8, this->_internal_ratedcurr(), target);
  }

  // float ratedInputVol = 9;
  if (!(this->_internal_ratedinputvol() <= 0 && this->_internal_ratedinputvol() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(9, this->_internal_ratedinputvol(), target);
  }

  // bytes DCModuleSN = 10;
  if (!this->_internal_dcmodulesn().empty()) {
    target = stream->WriteBytesMaybeAliased(
        10, this->_internal_dcmodulesn(), target);
  }

  // bytes softVersion = 11;
  if (!this->_internal_softversion().empty()) {
    target = stream->WriteBytesMaybeAliased(
        11, this->_internal_softversion(), target);
  }

  // bytes hardVersion = 12;
  if (!this->_internal_hardversion().empty()) {
    target = stream->WriteBytesMaybeAliased(
        12, this->_internal_hardversion(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.ADModuleInfo)
  return target;
}

size_t ADModuleInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.ADModuleInfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes DCModuleSN = 10;
  if (!this->_internal_dcmodulesn().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_dcmodulesn());
  }

  // bytes softVersion = 11;
  if (!this->_internal_softversion().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_softversion());
  }

  // bytes hardVersion = 12;
  if (!this->_internal_hardversion().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_hardversion());
  }

  // uint32 ID = 1;
  if (this->_internal_id() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_id());
  }

  // float currMax = 2;
  if (!(this->_internal_currmax() <= 0 && this->_internal_currmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float limitPower = 3;
  if (!(this->_internal_limitpower() <= 0 && this->_internal_limitpower() >= 0)) {
    total_size += 1 + 4;
  }

  // float volMax = 4;
  if (!(this->_internal_volmax() <= 0 && this->_internal_volmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float volMin = 5;
  if (!(this->_internal_volmin() <= 0 && this->_internal_volmin() >= 0)) {
    total_size += 1 + 4;
  }

  // float ratedVol = 6;
  if (!(this->_internal_ratedvol() <= 0 && this->_internal_ratedvol() >= 0)) {
    total_size += 1 + 4;
  }

  // float ratedPower = 7;
  if (!(this->_internal_ratedpower() <= 0 && this->_internal_ratedpower() >= 0)) {
    total_size += 1 + 4;
  }

  // float ratedCurr = 8;
  if (!(this->_internal_ratedcurr() <= 0 && this->_internal_ratedcurr() >= 0)) {
    total_size += 1 + 4;
  }

  // float ratedInputVol = 9;
  if (!(this->_internal_ratedinputvol() <= 0 && this->_internal_ratedinputvol() >= 0)) {
    total_size += 1 + 4;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ADModuleInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ADModuleInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ADModuleInfo::GetClassData() const { return &_class_data_; }

void ADModuleInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<ADModuleInfo *>(to)->MergeFrom(
      static_cast<const ADModuleInfo &>(from));
}


void ADModuleInfo::MergeFrom(const ADModuleInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.ADModuleInfo)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_dcmodulesn().empty()) {
    _internal_set_dcmodulesn(from._internal_dcmodulesn());
  }
  if (!from._internal_softversion().empty()) {
    _internal_set_softversion(from._internal_softversion());
  }
  if (!from._internal_hardversion().empty()) {
    _internal_set_hardversion(from._internal_hardversion());
  }
  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  if (!(from._internal_currmax() <= 0 && from._internal_currmax() >= 0)) {
    _internal_set_currmax(from._internal_currmax());
  }
  if (!(from._internal_limitpower() <= 0 && from._internal_limitpower() >= 0)) {
    _internal_set_limitpower(from._internal_limitpower());
  }
  if (!(from._internal_volmax() <= 0 && from._internal_volmax() >= 0)) {
    _internal_set_volmax(from._internal_volmax());
  }
  if (!(from._internal_volmin() <= 0 && from._internal_volmin() >= 0)) {
    _internal_set_volmin(from._internal_volmin());
  }
  if (!(from._internal_ratedvol() <= 0 && from._internal_ratedvol() >= 0)) {
    _internal_set_ratedvol(from._internal_ratedvol());
  }
  if (!(from._internal_ratedpower() <= 0 && from._internal_ratedpower() >= 0)) {
    _internal_set_ratedpower(from._internal_ratedpower());
  }
  if (!(from._internal_ratedcurr() <= 0 && from._internal_ratedcurr() >= 0)) {
    _internal_set_ratedcurr(from._internal_ratedcurr());
  }
  if (!(from._internal_ratedinputvol() <= 0 && from._internal_ratedinputvol() >= 0)) {
    _internal_set_ratedinputvol(from._internal_ratedinputvol());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ADModuleInfo::CopyFrom(const ADModuleInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.ADModuleInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ADModuleInfo::IsInitialized() const {
  return true;
}

void ADModuleInfo::InternalSwap(ADModuleInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &dcmodulesn_, GetArenaForAllocation(),
      &other->dcmodulesn_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &softversion_, GetArenaForAllocation(),
      &other->softversion_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &hardversion_, GetArenaForAllocation(),
      &other->hardversion_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ADModuleInfo, ratedinputvol_)
      + sizeof(ADModuleInfo::ratedinputvol_)
      - PROTOBUF_FIELD_OFFSET(ADModuleInfo, id_)>(
          reinterpret_cast<char*>(&id_),
          reinterpret_cast<char*>(&other->id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ADModuleInfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[3]);
}

// ===================================================================

class PMMInfo::_Internal {
 public:
};

PMMInfo::PMMInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  admodule_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.PMMInfo)
}
PMMInfo::PMMInfo(const PMMInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      admodule_(from.admodule_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&matrixtype_, &from.matrixtype_,
    static_cast<size_t>(reinterpret_cast<char*>(&liquidcooladdliquid_) -
    reinterpret_cast<char*>(&matrixtype_)) + sizeof(liquidcooladdliquid_));
  // @@protoc_insertion_point(copy_constructor:DMCinfo.PMMInfo)
}

inline void PMMInfo::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&matrixtype_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&liquidcooladdliquid_) -
    reinterpret_cast<char*>(&matrixtype_)) + sizeof(liquidcooladdliquid_));
}

PMMInfo::~PMMInfo() {
  // @@protoc_insertion_point(destructor:DMCinfo.PMMInfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PMMInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void PMMInfo::ArenaDtor(void* object) {
  PMMInfo* _this = reinterpret_cast< PMMInfo* >(object);
  (void)_this;
}
void PMMInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PMMInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PMMInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.PMMInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  admodule_.Clear();
  ::memset(&matrixtype_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&liquidcooladdliquid_) -
      reinterpret_cast<char*>(&matrixtype_)) + sizeof(liquidcooladdliquid_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PMMInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 MatrixType = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          matrixtype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 MatrixContactorCurrent = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          matrixcontactorcurrent_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 ContactorAc = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          contactorac_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 EmergencyStopType = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          emergencystoptype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 limitPower = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          limitpower_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DMCinfo.ADModuleTypeEnum ADModuleType = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_admoduletype(static_cast<::DMCinfo::ADModuleTypeEnum>(val));
        } else goto handle_unusual;
        continue;
      // repeated .DMCinfo.ADModuleInfo ADModule = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_admodule(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<58>(ptr));
        } else goto handle_unusual;
        continue;
      // uint32 TempEnable = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          tempenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 ACFanEnable = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72)) {
          acfanenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 SmokeEnable = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          smokeenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 ToppleAndFallEnable = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          toppleandfallenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 WaterEnable = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 96)) {
          waterenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 LightningEnable = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 104)) {
          lightningenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 BreakerEnable = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 112)) {
          breakerenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 DustproofEnable = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 120)) {
          dustproofenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 ACFanNum = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 128)) {
          acfannum_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DMCinfo.HvdcSilenceMode ModuleSilenceMode = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 136)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_modulesilencemode(static_cast<::DMCinfo::HvdcSilenceMode>(val));
        } else goto handle_unusual;
        continue;
      // uint32 PduType = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 144)) {
          pdutype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 coolingType = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 152)) {
          coolingtype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 liquidDeviceType = 20;
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 160)) {
          liquiddevicetype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 liquidCoolAddLiquid = 21;
      case 21:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 168)) {
          liquidcooladdliquid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* PMMInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.PMMInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 MatrixType = 1;
  if (this->_internal_matrixtype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_matrixtype(), target);
  }

  // uint32 MatrixContactorCurrent = 2;
  if (this->_internal_matrixcontactorcurrent() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_matrixcontactorcurrent(), target);
  }

  // uint32 ContactorAc = 3;
  if (this->_internal_contactorac() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_contactorac(), target);
  }

  // uint32 EmergencyStopType = 4;
  if (this->_internal_emergencystoptype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_emergencystoptype(), target);
  }

  // uint32 limitPower = 5;
  if (this->_internal_limitpower() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_limitpower(), target);
  }

  // .DMCinfo.ADModuleTypeEnum ADModuleType = 6;
  if (this->_internal_admoduletype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      6, this->_internal_admoduletype(), target);
  }

  // repeated .DMCinfo.ADModuleInfo ADModule = 7;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_admodule_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(7, this->_internal_admodule(i), target, stream);
  }

  // uint32 TempEnable = 8;
  if (this->_internal_tempenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_tempenable(), target);
  }

  // uint32 ACFanEnable = 9;
  if (this->_internal_acfanenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(9, this->_internal_acfanenable(), target);
  }

  // uint32 SmokeEnable = 10;
  if (this->_internal_smokeenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(10, this->_internal_smokeenable(), target);
  }

  // uint32 ToppleAndFallEnable = 11;
  if (this->_internal_toppleandfallenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(11, this->_internal_toppleandfallenable(), target);
  }

  // uint32 WaterEnable = 12;
  if (this->_internal_waterenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(12, this->_internal_waterenable(), target);
  }

  // uint32 LightningEnable = 13;
  if (this->_internal_lightningenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(13, this->_internal_lightningenable(), target);
  }

  // uint32 BreakerEnable = 14;
  if (this->_internal_breakerenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(14, this->_internal_breakerenable(), target);
  }

  // uint32 DustproofEnable = 15;
  if (this->_internal_dustproofenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(15, this->_internal_dustproofenable(), target);
  }

  // uint32 ACFanNum = 16;
  if (this->_internal_acfannum() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(16, this->_internal_acfannum(), target);
  }

  // .DMCinfo.HvdcSilenceMode ModuleSilenceMode = 17;
  if (this->_internal_modulesilencemode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      17, this->_internal_modulesilencemode(), target);
  }

  // uint32 PduType = 18;
  if (this->_internal_pdutype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(18, this->_internal_pdutype(), target);
  }

  // uint32 coolingType = 19;
  if (this->_internal_coolingtype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(19, this->_internal_coolingtype(), target);
  }

  // uint32 liquidDeviceType = 20;
  if (this->_internal_liquiddevicetype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(20, this->_internal_liquiddevicetype(), target);
  }

  // uint32 liquidCoolAddLiquid = 21;
  if (this->_internal_liquidcooladdliquid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(21, this->_internal_liquidcooladdliquid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.PMMInfo)
  return target;
}

size_t PMMInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.PMMInfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .DMCinfo.ADModuleInfo ADModule = 7;
  total_size += 1UL * this->_internal_admodule_size();
  for (const auto& msg : this->admodule_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // uint32 MatrixType = 1;
  if (this->_internal_matrixtype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_matrixtype());
  }

  // uint32 MatrixContactorCurrent = 2;
  if (this->_internal_matrixcontactorcurrent() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_matrixcontactorcurrent());
  }

  // uint32 ContactorAc = 3;
  if (this->_internal_contactorac() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_contactorac());
  }

  // uint32 EmergencyStopType = 4;
  if (this->_internal_emergencystoptype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_emergencystoptype());
  }

  // uint32 limitPower = 5;
  if (this->_internal_limitpower() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_limitpower());
  }

  // .DMCinfo.ADModuleTypeEnum ADModuleType = 6;
  if (this->_internal_admoduletype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_admoduletype());
  }

  // uint32 TempEnable = 8;
  if (this->_internal_tempenable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_tempenable());
  }

  // uint32 ACFanEnable = 9;
  if (this->_internal_acfanenable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_acfanenable());
  }

  // uint32 SmokeEnable = 10;
  if (this->_internal_smokeenable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_smokeenable());
  }

  // uint32 ToppleAndFallEnable = 11;
  if (this->_internal_toppleandfallenable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_toppleandfallenable());
  }

  // uint32 WaterEnable = 12;
  if (this->_internal_waterenable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_waterenable());
  }

  // uint32 LightningEnable = 13;
  if (this->_internal_lightningenable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_lightningenable());
  }

  // uint32 BreakerEnable = 14;
  if (this->_internal_breakerenable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_breakerenable());
  }

  // uint32 DustproofEnable = 15;
  if (this->_internal_dustproofenable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_dustproofenable());
  }

  // uint32 ACFanNum = 16;
  if (this->_internal_acfannum() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_acfannum());
  }

  // .DMCinfo.HvdcSilenceMode ModuleSilenceMode = 17;
  if (this->_internal_modulesilencemode() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_modulesilencemode());
  }

  // uint32 PduType = 18;
  if (this->_internal_pdutype() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_pdutype());
  }

  // uint32 coolingType = 19;
  if (this->_internal_coolingtype() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_coolingtype());
  }

  // uint32 liquidDeviceType = 20;
  if (this->_internal_liquiddevicetype() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_liquiddevicetype());
  }

  // uint32 liquidCoolAddLiquid = 21;
  if (this->_internal_liquidcooladdliquid() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_liquidcooladdliquid());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PMMInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PMMInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PMMInfo::GetClassData() const { return &_class_data_; }

void PMMInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<PMMInfo *>(to)->MergeFrom(
      static_cast<const PMMInfo &>(from));
}


void PMMInfo::MergeFrom(const PMMInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.PMMInfo)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  admodule_.MergeFrom(from.admodule_);
  if (from._internal_matrixtype() != 0) {
    _internal_set_matrixtype(from._internal_matrixtype());
  }
  if (from._internal_matrixcontactorcurrent() != 0) {
    _internal_set_matrixcontactorcurrent(from._internal_matrixcontactorcurrent());
  }
  if (from._internal_contactorac() != 0) {
    _internal_set_contactorac(from._internal_contactorac());
  }
  if (from._internal_emergencystoptype() != 0) {
    _internal_set_emergencystoptype(from._internal_emergencystoptype());
  }
  if (from._internal_limitpower() != 0) {
    _internal_set_limitpower(from._internal_limitpower());
  }
  if (from._internal_admoduletype() != 0) {
    _internal_set_admoduletype(from._internal_admoduletype());
  }
  if (from._internal_tempenable() != 0) {
    _internal_set_tempenable(from._internal_tempenable());
  }
  if (from._internal_acfanenable() != 0) {
    _internal_set_acfanenable(from._internal_acfanenable());
  }
  if (from._internal_smokeenable() != 0) {
    _internal_set_smokeenable(from._internal_smokeenable());
  }
  if (from._internal_toppleandfallenable() != 0) {
    _internal_set_toppleandfallenable(from._internal_toppleandfallenable());
  }
  if (from._internal_waterenable() != 0) {
    _internal_set_waterenable(from._internal_waterenable());
  }
  if (from._internal_lightningenable() != 0) {
    _internal_set_lightningenable(from._internal_lightningenable());
  }
  if (from._internal_breakerenable() != 0) {
    _internal_set_breakerenable(from._internal_breakerenable());
  }
  if (from._internal_dustproofenable() != 0) {
    _internal_set_dustproofenable(from._internal_dustproofenable());
  }
  if (from._internal_acfannum() != 0) {
    _internal_set_acfannum(from._internal_acfannum());
  }
  if (from._internal_modulesilencemode() != 0) {
    _internal_set_modulesilencemode(from._internal_modulesilencemode());
  }
  if (from._internal_pdutype() != 0) {
    _internal_set_pdutype(from._internal_pdutype());
  }
  if (from._internal_coolingtype() != 0) {
    _internal_set_coolingtype(from._internal_coolingtype());
  }
  if (from._internal_liquiddevicetype() != 0) {
    _internal_set_liquiddevicetype(from._internal_liquiddevicetype());
  }
  if (from._internal_liquidcooladdliquid() != 0) {
    _internal_set_liquidcooladdliquid(from._internal_liquidcooladdliquid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PMMInfo::CopyFrom(const PMMInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.PMMInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PMMInfo::IsInitialized() const {
  return true;
}

void PMMInfo::InternalSwap(PMMInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  admodule_.InternalSwap(&other->admodule_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PMMInfo, liquidcooladdliquid_)
      + sizeof(PMMInfo::liquidcooladdliquid_)
      - PROTOBUF_FIELD_OFFSET(PMMInfo, matrixtype_)>(
          reinterpret_cast<char*>(&matrixtype_),
          reinterpret_cast<char*>(&other->matrixtype_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PMMInfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[4]);
}

// ===================================================================

class ServerInfo::_Internal {
 public:
};

ServerInfo::ServerInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.ServerInfo)
}
ServerInfo::ServerInfo(const ServerInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  platname_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_platname().empty()) {
    platname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_platname(), 
      GetArenaForAllocation());
  }
  oscprocess_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_oscprocess().empty()) {
    oscprocess_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_oscprocess(), 
      GetArenaForAllocation());
  }
  ::memcpy(&platform_, &from.platform_,
    static_cast<size_t>(reinterpret_cast<char*>(&wifiofflinechgmode_) -
    reinterpret_cast<char*>(&platform_)) + sizeof(wifiofflinechgmode_));
  // @@protoc_insertion_point(copy_constructor:DMCinfo.ServerInfo)
}

inline void ServerInfo::SharedCtor() {
platname_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
oscprocess_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&platform_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&wifiofflinechgmode_) -
    reinterpret_cast<char*>(&platform_)) + sizeof(wifiofflinechgmode_));
}

ServerInfo::~ServerInfo() {
  // @@protoc_insertion_point(destructor:DMCinfo.ServerInfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ServerInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  platname_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  oscprocess_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ServerInfo::ArenaDtor(void* object) {
  ServerInfo* _this = reinterpret_cast< ServerInfo* >(object);
  (void)_this;
}
void ServerInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ServerInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ServerInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.ServerInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  platname_.ClearToEmpty();
  oscprocess_.ClearToEmpty();
  ::memset(&platform_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&wifiofflinechgmode_) -
      reinterpret_cast<char*>(&platform_)) + sizeof(wifiofflinechgmode_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ServerInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 platform = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          platform_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 qrcode = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          qrcode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 vin = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          vin_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 card = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          card_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string platName = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          auto str = _internal_mutable_platname();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DMCinfo.ServerInfo.platName"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 wifiofflineChgMode = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          wifiofflinechgmode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string oscProcess = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          auto str = _internal_mutable_oscprocess();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DMCinfo.ServerInfo.oscProcess"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ServerInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.ServerInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 platform = 1;
  if (this->_internal_platform() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_platform(), target);
  }

  // uint32 qrcode = 2;
  if (this->_internal_qrcode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_qrcode(), target);
  }

  // uint32 vin = 3;
  if (this->_internal_vin() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_vin(), target);
  }

  // uint32 card = 4;
  if (this->_internal_card() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_card(), target);
  }

  // string platName = 5;
  if (!this->_internal_platname().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_platname().data(), static_cast<int>(this->_internal_platname().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DMCinfo.ServerInfo.platName");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_platname(), target);
  }

  // uint32 wifiofflineChgMode = 6;
  if (this->_internal_wifiofflinechgmode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_wifiofflinechgmode(), target);
  }

  // string oscProcess = 7;
  if (!this->_internal_oscprocess().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_oscprocess().data(), static_cast<int>(this->_internal_oscprocess().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DMCinfo.ServerInfo.oscProcess");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_oscprocess(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.ServerInfo)
  return target;
}

size_t ServerInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.ServerInfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string platName = 5;
  if (!this->_internal_platname().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_platname());
  }

  // string oscProcess = 7;
  if (!this->_internal_oscprocess().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_oscprocess());
  }

  // uint32 platform = 1;
  if (this->_internal_platform() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_platform());
  }

  // uint32 qrcode = 2;
  if (this->_internal_qrcode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_qrcode());
  }

  // uint32 vin = 3;
  if (this->_internal_vin() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_vin());
  }

  // uint32 card = 4;
  if (this->_internal_card() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_card());
  }

  // uint32 wifiofflineChgMode = 6;
  if (this->_internal_wifiofflinechgmode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_wifiofflinechgmode());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ServerInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ServerInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ServerInfo::GetClassData() const { return &_class_data_; }

void ServerInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<ServerInfo *>(to)->MergeFrom(
      static_cast<const ServerInfo &>(from));
}


void ServerInfo::MergeFrom(const ServerInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.ServerInfo)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_platname().empty()) {
    _internal_set_platname(from._internal_platname());
  }
  if (!from._internal_oscprocess().empty()) {
    _internal_set_oscprocess(from._internal_oscprocess());
  }
  if (from._internal_platform() != 0) {
    _internal_set_platform(from._internal_platform());
  }
  if (from._internal_qrcode() != 0) {
    _internal_set_qrcode(from._internal_qrcode());
  }
  if (from._internal_vin() != 0) {
    _internal_set_vin(from._internal_vin());
  }
  if (from._internal_card() != 0) {
    _internal_set_card(from._internal_card());
  }
  if (from._internal_wifiofflinechgmode() != 0) {
    _internal_set_wifiofflinechgmode(from._internal_wifiofflinechgmode());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ServerInfo::CopyFrom(const ServerInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.ServerInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ServerInfo::IsInitialized() const {
  return true;
}

void ServerInfo::InternalSwap(ServerInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &platname_, GetArenaForAllocation(),
      &other->platname_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &oscprocess_, GetArenaForAllocation(),
      &other->oscprocess_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ServerInfo, wifiofflinechgmode_)
      + sizeof(ServerInfo::wifiofflinechgmode_)
      - PROTOBUF_FIELD_OFFSET(ServerInfo, platform_)>(
          reinterpret_cast<char*>(&platform_),
          reinterpret_cast<char*>(&other->platform_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ServerInfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[5]);
}

// ===================================================================

class OHPInfo::_Internal {
 public:
};

OHPInfo::OHPInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  servers_(arena),
  guninfo_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.OHPInfo)
}
OHPInfo::OHPInfo(const OHPInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      servers_(from.servers_),
      guninfo_(from.guninfo_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  macip_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_macip().empty()) {
    macip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_macip(), 
      GetArenaForAllocation());
  }
  wifiip_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_wifiip().empty()) {
    wifiip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_wifiip(), 
      GetArenaForAllocation());
  }
  productmodel_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_productmodel().empty()) {
    productmodel_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_productmodel(), 
      GetArenaForAllocation());
  }
  ccid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_ccid().empty()) {
    ccid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_ccid(), 
      GetArenaForAllocation());
  }
  ::memcpy(&terminalid_, &from.terminalid_,
    static_cast<size_t>(reinterpret_cast<char*>(&maxcurrent_) -
    reinterpret_cast<char*>(&terminalid_)) + sizeof(maxcurrent_));
  // @@protoc_insertion_point(copy_constructor:DMCinfo.OHPInfo)
}

inline void OHPInfo::SharedCtor() {
macip_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
wifiip_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
productmodel_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
ccid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&terminalid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&maxcurrent_) -
    reinterpret_cast<char*>(&terminalid_)) + sizeof(maxcurrent_));
}

OHPInfo::~OHPInfo() {
  // @@protoc_insertion_point(destructor:DMCinfo.OHPInfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void OHPInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  macip_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  wifiip_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  productmodel_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ccid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void OHPInfo::ArenaDtor(void* object) {
  OHPInfo* _this = reinterpret_cast< OHPInfo* >(object);
  (void)_this;
}
void OHPInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void OHPInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void OHPInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.OHPInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  servers_.Clear();
  guninfo_.Clear();
  macip_.ClearToEmpty();
  wifiip_.ClearToEmpty();
  productmodel_.ClearToEmpty();
  ccid_.ClearToEmpty();
  ::memset(&terminalid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&maxcurrent_) -
      reinterpret_cast<char*>(&terminalid_)) + sizeof(maxcurrent_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* OHPInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 terminalID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          terminalid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 NetType = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          nettype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 Vin = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          vin_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 AdminMode = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          adminmode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 AuxVisiable = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          auxvisiable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 CooperateMode = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          cooperatemode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes MACip = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          auto str = _internal_mutable_macip();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes WiFiip = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          auto str = _internal_mutable_wifiip();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .DMCinfo.ServerInfo servers = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_servers(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<74>(ptr));
        } else goto handle_unusual;
        continue;
      // uint32 OrderHistory = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          orderhistory_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 FaultsHistory = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          faultshistory_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 stopType = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 96)) {
          stoptype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 RateType = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 104)) {
          ratetype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 standbylogoType = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 112)) {
          standbylogotype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 ledType = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 120)) {
          ledtype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 stopChgSocType = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 128)) {
          stopchgsoctype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 hmiConfigEnable = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 136)) {
          hmiconfigenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 netOfflineWifiEnable = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 144)) {
          netofflinewifienable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 VLPREnable = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 152)) {
          vlprenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .DMCinfo.GUNInfo gunInfo = 20;
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 162)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_guninfo(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<162>(ptr));
        } else goto handle_unusual;
        continue;
      // uint32 ratedPower = 21;
      case 21:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 168)) {
          ratedpower_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string productModel = 22;
      case 22:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 178)) {
          auto str = _internal_mutable_productmodel();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DMCinfo.OHPInfo.productModel"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string ccid = 23;
      case 23:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 186)) {
          auto str = _internal_mutable_ccid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DMCinfo.OHPInfo.ccid"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 maxCurrent = 24;
      case 24:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 192)) {
          maxcurrent_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* OHPInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.OHPInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 terminalID = 1;
  if (this->_internal_terminalid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_terminalid(), target);
  }

  // uint32 NetType = 2;
  if (this->_internal_nettype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_nettype(), target);
  }

  // uint32 Vin = 3;
  if (this->_internal_vin() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_vin(), target);
  }

  // uint32 AdminMode = 4;
  if (this->_internal_adminmode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_adminmode(), target);
  }

  // uint32 AuxVisiable = 5;
  if (this->_internal_auxvisiable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_auxvisiable(), target);
  }

  // uint32 CooperateMode = 6;
  if (this->_internal_cooperatemode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_cooperatemode(), target);
  }

  // bytes MACip = 7;
  if (!this->_internal_macip().empty()) {
    target = stream->WriteBytesMaybeAliased(
        7, this->_internal_macip(), target);
  }

  // bytes WiFiip = 8;
  if (!this->_internal_wifiip().empty()) {
    target = stream->WriteBytesMaybeAliased(
        8, this->_internal_wifiip(), target);
  }

  // repeated .DMCinfo.ServerInfo servers = 9;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_servers_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(9, this->_internal_servers(i), target, stream);
  }

  // uint32 OrderHistory = 10;
  if (this->_internal_orderhistory() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(10, this->_internal_orderhistory(), target);
  }

  // uint32 FaultsHistory = 11;
  if (this->_internal_faultshistory() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(11, this->_internal_faultshistory(), target);
  }

  // uint32 stopType = 12;
  if (this->_internal_stoptype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(12, this->_internal_stoptype(), target);
  }

  // uint32 RateType = 13;
  if (this->_internal_ratetype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(13, this->_internal_ratetype(), target);
  }

  // uint32 standbylogoType = 14;
  if (this->_internal_standbylogotype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(14, this->_internal_standbylogotype(), target);
  }

  // uint32 ledType = 15;
  if (this->_internal_ledtype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(15, this->_internal_ledtype(), target);
  }

  // uint32 stopChgSocType = 16;
  if (this->_internal_stopchgsoctype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(16, this->_internal_stopchgsoctype(), target);
  }

  // uint32 hmiConfigEnable = 17;
  if (this->_internal_hmiconfigenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(17, this->_internal_hmiconfigenable(), target);
  }

  // uint32 netOfflineWifiEnable = 18;
  if (this->_internal_netofflinewifienable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(18, this->_internal_netofflinewifienable(), target);
  }

  // uint32 VLPREnable = 19;
  if (this->_internal_vlprenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(19, this->_internal_vlprenable(), target);
  }

  // repeated .DMCinfo.GUNInfo gunInfo = 20;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_guninfo_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(20, this->_internal_guninfo(i), target, stream);
  }

  // uint32 ratedPower = 21;
  if (this->_internal_ratedpower() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(21, this->_internal_ratedpower(), target);
  }

  // string productModel = 22;
  if (!this->_internal_productmodel().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_productmodel().data(), static_cast<int>(this->_internal_productmodel().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DMCinfo.OHPInfo.productModel");
    target = stream->WriteStringMaybeAliased(
        22, this->_internal_productmodel(), target);
  }

  // string ccid = 23;
  if (!this->_internal_ccid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_ccid().data(), static_cast<int>(this->_internal_ccid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DMCinfo.OHPInfo.ccid");
    target = stream->WriteStringMaybeAliased(
        23, this->_internal_ccid(), target);
  }

  // uint32 maxCurrent = 24;
  if (this->_internal_maxcurrent() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(24, this->_internal_maxcurrent(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.OHPInfo)
  return target;
}

size_t OHPInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.OHPInfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .DMCinfo.ServerInfo servers = 9;
  total_size += 1UL * this->_internal_servers_size();
  for (const auto& msg : this->servers_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .DMCinfo.GUNInfo gunInfo = 20;
  total_size += 2UL * this->_internal_guninfo_size();
  for (const auto& msg : this->guninfo_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // bytes MACip = 7;
  if (!this->_internal_macip().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_macip());
  }

  // bytes WiFiip = 8;
  if (!this->_internal_wifiip().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_wifiip());
  }

  // string productModel = 22;
  if (!this->_internal_productmodel().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_productmodel());
  }

  // string ccid = 23;
  if (!this->_internal_ccid().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_ccid());
  }

  // uint32 terminalID = 1;
  if (this->_internal_terminalid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_terminalid());
  }

  // uint32 NetType = 2;
  if (this->_internal_nettype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_nettype());
  }

  // uint32 Vin = 3;
  if (this->_internal_vin() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_vin());
  }

  // uint32 AdminMode = 4;
  if (this->_internal_adminmode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_adminmode());
  }

  // uint32 AuxVisiable = 5;
  if (this->_internal_auxvisiable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_auxvisiable());
  }

  // uint32 CooperateMode = 6;
  if (this->_internal_cooperatemode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_cooperatemode());
  }

  // uint32 OrderHistory = 10;
  if (this->_internal_orderhistory() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_orderhistory());
  }

  // uint32 FaultsHistory = 11;
  if (this->_internal_faultshistory() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_faultshistory());
  }

  // uint32 stopType = 12;
  if (this->_internal_stoptype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_stoptype());
  }

  // uint32 RateType = 13;
  if (this->_internal_ratetype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_ratetype());
  }

  // uint32 standbylogoType = 14;
  if (this->_internal_standbylogotype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_standbylogotype());
  }

  // uint32 ledType = 15;
  if (this->_internal_ledtype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_ledtype());
  }

  // uint32 stopChgSocType = 16;
  if (this->_internal_stopchgsoctype() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_stopchgsoctype());
  }

  // uint32 hmiConfigEnable = 17;
  if (this->_internal_hmiconfigenable() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_hmiconfigenable());
  }

  // uint32 netOfflineWifiEnable = 18;
  if (this->_internal_netofflinewifienable() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_netofflinewifienable());
  }

  // uint32 VLPREnable = 19;
  if (this->_internal_vlprenable() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_vlprenable());
  }

  // uint32 ratedPower = 21;
  if (this->_internal_ratedpower() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_ratedpower());
  }

  // uint32 maxCurrent = 24;
  if (this->_internal_maxcurrent() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_maxcurrent());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData OHPInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    OHPInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*OHPInfo::GetClassData() const { return &_class_data_; }

void OHPInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<OHPInfo *>(to)->MergeFrom(
      static_cast<const OHPInfo &>(from));
}


void OHPInfo::MergeFrom(const OHPInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.OHPInfo)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  servers_.MergeFrom(from.servers_);
  guninfo_.MergeFrom(from.guninfo_);
  if (!from._internal_macip().empty()) {
    _internal_set_macip(from._internal_macip());
  }
  if (!from._internal_wifiip().empty()) {
    _internal_set_wifiip(from._internal_wifiip());
  }
  if (!from._internal_productmodel().empty()) {
    _internal_set_productmodel(from._internal_productmodel());
  }
  if (!from._internal_ccid().empty()) {
    _internal_set_ccid(from._internal_ccid());
  }
  if (from._internal_terminalid() != 0) {
    _internal_set_terminalid(from._internal_terminalid());
  }
  if (from._internal_nettype() != 0) {
    _internal_set_nettype(from._internal_nettype());
  }
  if (from._internal_vin() != 0) {
    _internal_set_vin(from._internal_vin());
  }
  if (from._internal_adminmode() != 0) {
    _internal_set_adminmode(from._internal_adminmode());
  }
  if (from._internal_auxvisiable() != 0) {
    _internal_set_auxvisiable(from._internal_auxvisiable());
  }
  if (from._internal_cooperatemode() != 0) {
    _internal_set_cooperatemode(from._internal_cooperatemode());
  }
  if (from._internal_orderhistory() != 0) {
    _internal_set_orderhistory(from._internal_orderhistory());
  }
  if (from._internal_faultshistory() != 0) {
    _internal_set_faultshistory(from._internal_faultshistory());
  }
  if (from._internal_stoptype() != 0) {
    _internal_set_stoptype(from._internal_stoptype());
  }
  if (from._internal_ratetype() != 0) {
    _internal_set_ratetype(from._internal_ratetype());
  }
  if (from._internal_standbylogotype() != 0) {
    _internal_set_standbylogotype(from._internal_standbylogotype());
  }
  if (from._internal_ledtype() != 0) {
    _internal_set_ledtype(from._internal_ledtype());
  }
  if (from._internal_stopchgsoctype() != 0) {
    _internal_set_stopchgsoctype(from._internal_stopchgsoctype());
  }
  if (from._internal_hmiconfigenable() != 0) {
    _internal_set_hmiconfigenable(from._internal_hmiconfigenable());
  }
  if (from._internal_netofflinewifienable() != 0) {
    _internal_set_netofflinewifienable(from._internal_netofflinewifienable());
  }
  if (from._internal_vlprenable() != 0) {
    _internal_set_vlprenable(from._internal_vlprenable());
  }
  if (from._internal_ratedpower() != 0) {
    _internal_set_ratedpower(from._internal_ratedpower());
  }
  if (from._internal_maxcurrent() != 0) {
    _internal_set_maxcurrent(from._internal_maxcurrent());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void OHPInfo::CopyFrom(const OHPInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.OHPInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OHPInfo::IsInitialized() const {
  return true;
}

void OHPInfo::InternalSwap(OHPInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  servers_.InternalSwap(&other->servers_);
  guninfo_.InternalSwap(&other->guninfo_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &macip_, GetArenaForAllocation(),
      &other->macip_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &wifiip_, GetArenaForAllocation(),
      &other->wifiip_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &productmodel_, GetArenaForAllocation(),
      &other->productmodel_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &ccid_, GetArenaForAllocation(),
      &other->ccid_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(OHPInfo, maxcurrent_)
      + sizeof(OHPInfo::maxcurrent_)
      - PROTOBUF_FIELD_OFFSET(OHPInfo, terminalid_)>(
          reinterpret_cast<char*>(&terminalid_),
          reinterpret_cast<char*>(&other->terminalid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata OHPInfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[6]);
}

// ===================================================================

class HmcChargingStrategyM::_Internal {
 public:
};

HmcChargingStrategyM::HmcChargingStrategyM(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.HmcChargingStrategyM)
}
HmcChargingStrategyM::HmcChargingStrategyM(const HmcChargingStrategyM& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&chargestrategy_, &from.chargestrategy_,
    static_cast<size_t>(reinterpret_cast<char*>(&endtime_) -
    reinterpret_cast<char*>(&chargestrategy_)) + sizeof(endtime_));
  // @@protoc_insertion_point(copy_constructor:DMCinfo.HmcChargingStrategyM)
}

inline void HmcChargingStrategyM::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&chargestrategy_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&endtime_) -
    reinterpret_cast<char*>(&chargestrategy_)) + sizeof(endtime_));
}

HmcChargingStrategyM::~HmcChargingStrategyM() {
  // @@protoc_insertion_point(destructor:DMCinfo.HmcChargingStrategyM)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void HmcChargingStrategyM::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void HmcChargingStrategyM::ArenaDtor(void* object) {
  HmcChargingStrategyM* _this = reinterpret_cast< HmcChargingStrategyM* >(object);
  (void)_this;
}
void HmcChargingStrategyM::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void HmcChargingStrategyM::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void HmcChargingStrategyM::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.HmcChargingStrategyM)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&chargestrategy_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&endtime_) -
      reinterpret_cast<char*>(&chargestrategy_)) + sizeof(endtime_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* HmcChargingStrategyM::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .DMCinfo.HmcChargeStrategyEnum chargeStrategy = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_chargestrategy(static_cast<::DMCinfo::HmcChargeStrategyEnum>(val));
        } else goto handle_unusual;
        continue;
      // .DMCinfo.HmcChargeModeEnum chargeStar = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_chargestar(static_cast<::DMCinfo::HmcChargeModeEnum>(val));
        } else goto handle_unusual;
        continue;
      // uint32 startTime = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          starttime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 endTime = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          endtime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* HmcChargingStrategyM::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.HmcChargingStrategyM)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .DMCinfo.HmcChargeStrategyEnum chargeStrategy = 1;
  if (this->_internal_chargestrategy() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_chargestrategy(), target);
  }

  // .DMCinfo.HmcChargeModeEnum chargeStar = 2;
  if (this->_internal_chargestar() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_chargestar(), target);
  }

  // uint32 startTime = 3;
  if (this->_internal_starttime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_starttime(), target);
  }

  // uint32 endTime = 4;
  if (this->_internal_endtime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_endtime(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.HmcChargingStrategyM)
  return target;
}

size_t HmcChargingStrategyM::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.HmcChargingStrategyM)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .DMCinfo.HmcChargeStrategyEnum chargeStrategy = 1;
  if (this->_internal_chargestrategy() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_chargestrategy());
  }

  // .DMCinfo.HmcChargeModeEnum chargeStar = 2;
  if (this->_internal_chargestar() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_chargestar());
  }

  // uint32 startTime = 3;
  if (this->_internal_starttime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_starttime());
  }

  // uint32 endTime = 4;
  if (this->_internal_endtime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_endtime());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData HmcChargingStrategyM::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    HmcChargingStrategyM::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*HmcChargingStrategyM::GetClassData() const { return &_class_data_; }

void HmcChargingStrategyM::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<HmcChargingStrategyM *>(to)->MergeFrom(
      static_cast<const HmcChargingStrategyM &>(from));
}


void HmcChargingStrategyM::MergeFrom(const HmcChargingStrategyM& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.HmcChargingStrategyM)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_chargestrategy() != 0) {
    _internal_set_chargestrategy(from._internal_chargestrategy());
  }
  if (from._internal_chargestar() != 0) {
    _internal_set_chargestar(from._internal_chargestar());
  }
  if (from._internal_starttime() != 0) {
    _internal_set_starttime(from._internal_starttime());
  }
  if (from._internal_endtime() != 0) {
    _internal_set_endtime(from._internal_endtime());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void HmcChargingStrategyM::CopyFrom(const HmcChargingStrategyM& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.HmcChargingStrategyM)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HmcChargingStrategyM::IsInitialized() const {
  return true;
}

void HmcChargingStrategyM::InternalSwap(HmcChargingStrategyM* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(HmcChargingStrategyM, endtime_)
      + sizeof(HmcChargingStrategyM::endtime_)
      - PROTOBUF_FIELD_OFFSET(HmcChargingStrategyM, chargestrategy_)>(
          reinterpret_cast<char*>(&chargestrategy_),
          reinterpret_cast<char*>(&other->chargestrategy_));
}

::PROTOBUF_NAMESPACE_ID::Metadata HmcChargingStrategyM::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[7]);
}

// ===================================================================

class HMCInfo::_Internal {
 public:
};

HMCInfo::HMCInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  platinfo_(arena),
  strategy_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.HMCInfo)
}
HMCInfo::HMCInfo(const HMCInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      platinfo_(from.platinfo_),
      strategy_(from.strategy_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  terminalcode_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_terminalcode().empty()) {
    terminalcode_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_terminalcode(), 
      GetArenaForAllocation());
  }
  ::memcpy(&terminalid_, &from.terminalid_,
    static_cast<size_t>(reinterpret_cast<char*>(&soc_) -
    reinterpret_cast<char*>(&terminalid_)) + sizeof(soc_));
  // @@protoc_insertion_point(copy_constructor:DMCinfo.HMCInfo)
}

inline void HMCInfo::SharedCtor() {
terminalcode_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&terminalid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&soc_) -
    reinterpret_cast<char*>(&terminalid_)) + sizeof(soc_));
}

HMCInfo::~HMCInfo() {
  // @@protoc_insertion_point(destructor:DMCinfo.HMCInfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void HMCInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  terminalcode_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void HMCInfo::ArenaDtor(void* object) {
  HMCInfo* _this = reinterpret_cast< HMCInfo* >(object);
  (void)_this;
}
void HMCInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void HMCInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void HMCInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.HMCInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  platinfo_.Clear();
  strategy_.Clear();
  terminalcode_.ClearToEmpty();
  ::memset(&terminalid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&soc_) -
      reinterpret_cast<char*>(&terminalid_)) + sizeof(soc_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* HMCInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 terminalID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          terminalid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DMCinfo.HmcLcrEnum lcrType = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_lcrtype(static_cast<::DMCinfo::HmcLcrEnum>(val));
        } else goto handle_unusual;
        continue;
      // string terminalCode = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_terminalcode();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DMCinfo.HMCInfo.terminalCode"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 soc = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          soc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .DMCinfo.ServerInfo platInfo = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_platinfo(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated .DMCinfo.HmcChargingStrategyM strategy = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_strategy(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<50>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* HMCInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.HMCInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 terminalID = 1;
  if (this->_internal_terminalid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_terminalid(), target);
  }

  // .DMCinfo.HmcLcrEnum lcrType = 2;
  if (this->_internal_lcrtype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_lcrtype(), target);
  }

  // string terminalCode = 3;
  if (!this->_internal_terminalcode().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_terminalcode().data(), static_cast<int>(this->_internal_terminalcode().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DMCinfo.HMCInfo.terminalCode");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_terminalcode(), target);
  }

  // uint32 soc = 4;
  if (this->_internal_soc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_soc(), target);
  }

  // repeated .DMCinfo.ServerInfo platInfo = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_platinfo_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, this->_internal_platinfo(i), target, stream);
  }

  // repeated .DMCinfo.HmcChargingStrategyM strategy = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_strategy_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(6, this->_internal_strategy(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.HMCInfo)
  return target;
}

size_t HMCInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.HMCInfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .DMCinfo.ServerInfo platInfo = 5;
  total_size += 1UL * this->_internal_platinfo_size();
  for (const auto& msg : this->platinfo_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .DMCinfo.HmcChargingStrategyM strategy = 6;
  total_size += 1UL * this->_internal_strategy_size();
  for (const auto& msg : this->strategy_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string terminalCode = 3;
  if (!this->_internal_terminalcode().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_terminalcode());
  }

  // uint32 terminalID = 1;
  if (this->_internal_terminalid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_terminalid());
  }

  // .DMCinfo.HmcLcrEnum lcrType = 2;
  if (this->_internal_lcrtype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_lcrtype());
  }

  // uint32 soc = 4;
  if (this->_internal_soc() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_soc());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData HMCInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    HMCInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*HMCInfo::GetClassData() const { return &_class_data_; }

void HMCInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<HMCInfo *>(to)->MergeFrom(
      static_cast<const HMCInfo &>(from));
}


void HMCInfo::MergeFrom(const HMCInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.HMCInfo)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  platinfo_.MergeFrom(from.platinfo_);
  strategy_.MergeFrom(from.strategy_);
  if (!from._internal_terminalcode().empty()) {
    _internal_set_terminalcode(from._internal_terminalcode());
  }
  if (from._internal_terminalid() != 0) {
    _internal_set_terminalid(from._internal_terminalid());
  }
  if (from._internal_lcrtype() != 0) {
    _internal_set_lcrtype(from._internal_lcrtype());
  }
  if (from._internal_soc() != 0) {
    _internal_set_soc(from._internal_soc());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void HMCInfo::CopyFrom(const HMCInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.HMCInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HMCInfo::IsInitialized() const {
  return true;
}

void HMCInfo::InternalSwap(HMCInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  platinfo_.InternalSwap(&other->platinfo_);
  strategy_.InternalSwap(&other->strategy_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &terminalcode_, GetArenaForAllocation(),
      &other->terminalcode_, other->GetArenaForAllocation()
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(HMCInfo, soc_)
      + sizeof(HMCInfo::soc_)
      - PROTOBUF_FIELD_OFFSET(HMCInfo, terminalid_)>(
          reinterpret_cast<char*>(&terminalid_),
          reinterpret_cast<char*>(&other->terminalid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata HMCInfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[8]);
}

// ===================================================================

class GunLoadConstraint::_Internal {
 public:
};

GunLoadConstraint::GunLoadConstraint(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  gunnum_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.GunLoadConstraint)
}
GunLoadConstraint::GunLoadConstraint(const GunLoadConstraint& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      gunnum_(from.gunnum_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&terminalid_, &from.terminalid_,
    static_cast<size_t>(reinterpret_cast<char*>(&gunlimitpower_) -
    reinterpret_cast<char*>(&terminalid_)) + sizeof(gunlimitpower_));
  // @@protoc_insertion_point(copy_constructor:DMCinfo.GunLoadConstraint)
}

inline void GunLoadConstraint::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&terminalid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&gunlimitpower_) -
    reinterpret_cast<char*>(&terminalid_)) + sizeof(gunlimitpower_));
}

GunLoadConstraint::~GunLoadConstraint() {
  // @@protoc_insertion_point(destructor:DMCinfo.GunLoadConstraint)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GunLoadConstraint::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GunLoadConstraint::ArenaDtor(void* object) {
  GunLoadConstraint* _this = reinterpret_cast< GunLoadConstraint* >(object);
  (void)_this;
}
void GunLoadConstraint::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GunLoadConstraint::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GunLoadConstraint::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.GunLoadConstraint)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  gunnum_.Clear();
  ::memset(&terminalid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&gunlimitpower_) -
      reinterpret_cast<char*>(&terminalid_)) + sizeof(gunlimitpower_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GunLoadConstraint::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 terminalID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          terminalid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 gunID = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          gunid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated uint32 gunNum = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedUInt32Parser(_internal_mutable_gunnum(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24) {
          _internal_add_gunnum(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 GunlimitPower = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          gunlimitpower_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* GunLoadConstraint::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.GunLoadConstraint)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 terminalID = 1;
  if (this->_internal_terminalid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_terminalid(), target);
  }

  // uint32 gunID = 2;
  if (this->_internal_gunid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_gunid(), target);
  }

  // repeated uint32 gunNum = 3;
  {
    int byte_size = _gunnum_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteUInt32Packed(
          3, _internal_gunnum(), byte_size, target);
    }
  }

  // uint32 GunlimitPower = 4;
  if (this->_internal_gunlimitpower() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_gunlimitpower(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.GunLoadConstraint)
  return target;
}

size_t GunLoadConstraint::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.GunLoadConstraint)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated uint32 gunNum = 3;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      UInt32Size(this->gunnum_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _gunnum_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // uint32 terminalID = 1;
  if (this->_internal_terminalid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_terminalid());
  }

  // uint32 gunID = 2;
  if (this->_internal_gunid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_gunid());
  }

  // uint32 GunlimitPower = 4;
  if (this->_internal_gunlimitpower() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_gunlimitpower());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GunLoadConstraint::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GunLoadConstraint::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GunLoadConstraint::GetClassData() const { return &_class_data_; }

void GunLoadConstraint::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<GunLoadConstraint *>(to)->MergeFrom(
      static_cast<const GunLoadConstraint &>(from));
}


void GunLoadConstraint::MergeFrom(const GunLoadConstraint& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.GunLoadConstraint)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  gunnum_.MergeFrom(from.gunnum_);
  if (from._internal_terminalid() != 0) {
    _internal_set_terminalid(from._internal_terminalid());
  }
  if (from._internal_gunid() != 0) {
    _internal_set_gunid(from._internal_gunid());
  }
  if (from._internal_gunlimitpower() != 0) {
    _internal_set_gunlimitpower(from._internal_gunlimitpower());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GunLoadConstraint::CopyFrom(const GunLoadConstraint& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.GunLoadConstraint)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GunLoadConstraint::IsInitialized() const {
  return true;
}

void GunLoadConstraint::InternalSwap(GunLoadConstraint* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  gunnum_.InternalSwap(&other->gunnum_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GunLoadConstraint, gunlimitpower_)
      + sizeof(GunLoadConstraint::gunlimitpower_)
      - PROTOBUF_FIELD_OFFSET(GunLoadConstraint, terminalid_)>(
          reinterpret_cast<char*>(&terminalid_),
          reinterpret_cast<char*>(&other->terminalid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GunLoadConstraint::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[9]);
}

// ===================================================================

class GunMatchInfo::_Internal {
 public:
};

GunMatchInfo::GunMatchInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  gunnum_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.GunMatchInfo)
}
GunMatchInfo::GunMatchInfo(const GunMatchInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      gunnum_(from.gunnum_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&gunid_, &from.gunid_,
    static_cast<size_t>(reinterpret_cast<char*>(&isadjacent_) -
    reinterpret_cast<char*>(&gunid_)) + sizeof(isadjacent_));
  // @@protoc_insertion_point(copy_constructor:DMCinfo.GunMatchInfo)
}

inline void GunMatchInfo::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&gunid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&isadjacent_) -
    reinterpret_cast<char*>(&gunid_)) + sizeof(isadjacent_));
}

GunMatchInfo::~GunMatchInfo() {
  // @@protoc_insertion_point(destructor:DMCinfo.GunMatchInfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GunMatchInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GunMatchInfo::ArenaDtor(void* object) {
  GunMatchInfo* _this = reinterpret_cast< GunMatchInfo* >(object);
  (void)_this;
}
void GunMatchInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GunMatchInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GunMatchInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.GunMatchInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  gunnum_.Clear();
  ::memset(&gunid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&isadjacent_) -
      reinterpret_cast<char*>(&gunid_)) + sizeof(isadjacent_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GunMatchInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 gunID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          gunid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated uint32 gunNum = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedUInt32Parser(_internal_mutable_gunnum(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16) {
          _internal_add_gunnum(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 isAdjacent = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          isadjacent_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* GunMatchInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.GunMatchInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 gunID = 1;
  if (this->_internal_gunid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_gunid(), target);
  }

  // repeated uint32 gunNum = 2;
  {
    int byte_size = _gunnum_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteUInt32Packed(
          2, _internal_gunnum(), byte_size, target);
    }
  }

  // uint32 isAdjacent = 3;
  if (this->_internal_isadjacent() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_isadjacent(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.GunMatchInfo)
  return target;
}

size_t GunMatchInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.GunMatchInfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated uint32 gunNum = 2;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      UInt32Size(this->gunnum_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _gunnum_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // uint32 gunID = 1;
  if (this->_internal_gunid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_gunid());
  }

  // uint32 isAdjacent = 3;
  if (this->_internal_isadjacent() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_isadjacent());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GunMatchInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GunMatchInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GunMatchInfo::GetClassData() const { return &_class_data_; }

void GunMatchInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<GunMatchInfo *>(to)->MergeFrom(
      static_cast<const GunMatchInfo &>(from));
}


void GunMatchInfo::MergeFrom(const GunMatchInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.GunMatchInfo)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  gunnum_.MergeFrom(from.gunnum_);
  if (from._internal_gunid() != 0) {
    _internal_set_gunid(from._internal_gunid());
  }
  if (from._internal_isadjacent() != 0) {
    _internal_set_isadjacent(from._internal_isadjacent());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GunMatchInfo::CopyFrom(const GunMatchInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.GunMatchInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GunMatchInfo::IsInitialized() const {
  return true;
}

void GunMatchInfo::InternalSwap(GunMatchInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  gunnum_.InternalSwap(&other->gunnum_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GunMatchInfo, isadjacent_)
      + sizeof(GunMatchInfo::isadjacent_)
      - PROTOBUF_FIELD_OFFSET(GunMatchInfo, gunid_)>(
          reinterpret_cast<char*>(&gunid_),
          reinterpret_cast<char*>(&other->gunid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GunMatchInfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[10]);
}

// ===================================================================

class CurrentBalance::_Internal {
 public:
};

CurrentBalance::CurrentBalance(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.CurrentBalance)
}
CurrentBalance::CurrentBalance(const CurrentBalance& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&equalchgcurrcoef_, &from.equalchgcurrcoef_,
    static_cast<size_t>(reinterpret_cast<char*>(&allowequalchgtime_) -
    reinterpret_cast<char*>(&equalchgcurrcoef_)) + sizeof(allowequalchgtime_));
  // @@protoc_insertion_point(copy_constructor:DMCinfo.CurrentBalance)
}

inline void CurrentBalance::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&equalchgcurrcoef_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&allowequalchgtime_) -
    reinterpret_cast<char*>(&equalchgcurrcoef_)) + sizeof(allowequalchgtime_));
}

CurrentBalance::~CurrentBalance() {
  // @@protoc_insertion_point(destructor:DMCinfo.CurrentBalance)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CurrentBalance::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void CurrentBalance::ArenaDtor(void* object) {
  CurrentBalance* _this = reinterpret_cast< CurrentBalance* >(object);
  (void)_this;
}
void CurrentBalance::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CurrentBalance::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CurrentBalance::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.CurrentBalance)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&equalchgcurrcoef_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&allowequalchgtime_) -
      reinterpret_cast<char*>(&equalchgcurrcoef_)) + sizeof(allowequalchgtime_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CurrentBalance::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float EqualChgCurrCoef = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          equalchgcurrcoef_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 AllowEqualChgTime = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          allowequalchgtime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* CurrentBalance::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.CurrentBalance)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float EqualChgCurrCoef = 1;
  if (!(this->_internal_equalchgcurrcoef() <= 0 && this->_internal_equalchgcurrcoef() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_equalchgcurrcoef(), target);
  }

  // uint32 AllowEqualChgTime = 2;
  if (this->_internal_allowequalchgtime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_allowequalchgtime(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.CurrentBalance)
  return target;
}

size_t CurrentBalance::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.CurrentBalance)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float EqualChgCurrCoef = 1;
  if (!(this->_internal_equalchgcurrcoef() <= 0 && this->_internal_equalchgcurrcoef() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 AllowEqualChgTime = 2;
  if (this->_internal_allowequalchgtime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_allowequalchgtime());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CurrentBalance::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CurrentBalance::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CurrentBalance::GetClassData() const { return &_class_data_; }

void CurrentBalance::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<CurrentBalance *>(to)->MergeFrom(
      static_cast<const CurrentBalance &>(from));
}


void CurrentBalance::MergeFrom(const CurrentBalance& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.CurrentBalance)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from._internal_equalchgcurrcoef() <= 0 && from._internal_equalchgcurrcoef() >= 0)) {
    _internal_set_equalchgcurrcoef(from._internal_equalchgcurrcoef());
  }
  if (from._internal_allowequalchgtime() != 0) {
    _internal_set_allowequalchgtime(from._internal_allowequalchgtime());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CurrentBalance::CopyFrom(const CurrentBalance& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.CurrentBalance)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CurrentBalance::IsInitialized() const {
  return true;
}

void CurrentBalance::InternalSwap(CurrentBalance* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CurrentBalance, allowequalchgtime_)
      + sizeof(CurrentBalance::allowequalchgtime_)
      - PROTOBUF_FIELD_OFFSET(CurrentBalance, equalchgcurrcoef_)>(
          reinterpret_cast<char*>(&equalchgcurrcoef_),
          reinterpret_cast<char*>(&other->equalchgcurrcoef_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CurrentBalance::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[11]);
}

// ===================================================================

class HotRunaway::_Internal {
 public:
};

HotRunaway::HotRunaway(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.HotRunaway)
}
HotRunaway::HotRunaway(const HotRunaway& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&hotrunthreshold_, &from.hotrunthreshold_,
    static_cast<size_t>(reinterpret_cast<char*>(&hotrunprotdisabled_) -
    reinterpret_cast<char*>(&hotrunthreshold_)) + sizeof(hotrunprotdisabled_));
  // @@protoc_insertion_point(copy_constructor:DMCinfo.HotRunaway)
}

inline void HotRunaway::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&hotrunthreshold_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&hotrunprotdisabled_) -
    reinterpret_cast<char*>(&hotrunthreshold_)) + sizeof(hotrunprotdisabled_));
}

HotRunaway::~HotRunaway() {
  // @@protoc_insertion_point(destructor:DMCinfo.HotRunaway)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void HotRunaway::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void HotRunaway::ArenaDtor(void* object) {
  HotRunaway* _this = reinterpret_cast< HotRunaway* >(object);
  (void)_this;
}
void HotRunaway::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void HotRunaway::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void HotRunaway::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.HotRunaway)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&hotrunthreshold_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&hotrunprotdisabled_) -
      reinterpret_cast<char*>(&hotrunthreshold_)) + sizeof(hotrunprotdisabled_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* HotRunaway::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float HotRunThreshold = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          hotrunthreshold_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 HotRunConfireTime = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          hotrunconfiretime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 HotRunProtDisabled = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          hotrunprotdisabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* HotRunaway::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.HotRunaway)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float HotRunThreshold = 1;
  if (!(this->_internal_hotrunthreshold() <= 0 && this->_internal_hotrunthreshold() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_hotrunthreshold(), target);
  }

  // uint32 HotRunConfireTime = 2;
  if (this->_internal_hotrunconfiretime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_hotrunconfiretime(), target);
  }

  // uint32 HotRunProtDisabled = 3;
  if (this->_internal_hotrunprotdisabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_hotrunprotdisabled(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.HotRunaway)
  return target;
}

size_t HotRunaway::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.HotRunaway)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float HotRunThreshold = 1;
  if (!(this->_internal_hotrunthreshold() <= 0 && this->_internal_hotrunthreshold() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 HotRunConfireTime = 2;
  if (this->_internal_hotrunconfiretime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_hotrunconfiretime());
  }

  // uint32 HotRunProtDisabled = 3;
  if (this->_internal_hotrunprotdisabled() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_hotrunprotdisabled());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData HotRunaway::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    HotRunaway::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*HotRunaway::GetClassData() const { return &_class_data_; }

void HotRunaway::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<HotRunaway *>(to)->MergeFrom(
      static_cast<const HotRunaway &>(from));
}


void HotRunaway::MergeFrom(const HotRunaway& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.HotRunaway)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from._internal_hotrunthreshold() <= 0 && from._internal_hotrunthreshold() >= 0)) {
    _internal_set_hotrunthreshold(from._internal_hotrunthreshold());
  }
  if (from._internal_hotrunconfiretime() != 0) {
    _internal_set_hotrunconfiretime(from._internal_hotrunconfiretime());
  }
  if (from._internal_hotrunprotdisabled() != 0) {
    _internal_set_hotrunprotdisabled(from._internal_hotrunprotdisabled());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void HotRunaway::CopyFrom(const HotRunaway& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.HotRunaway)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HotRunaway::IsInitialized() const {
  return true;
}

void HotRunaway::InternalSwap(HotRunaway* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(HotRunaway, hotrunprotdisabled_)
      + sizeof(HotRunaway::hotrunprotdisabled_)
      - PROTOBUF_FIELD_OFFSET(HotRunaway, hotrunthreshold_)>(
          reinterpret_cast<char*>(&hotrunthreshold_),
          reinterpret_cast<char*>(&other->hotrunthreshold_));
}

::PROTOBUF_NAMESPACE_ID::Metadata HotRunaway::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[12]);
}

// ===================================================================

class CellOverVolt::_Internal {
 public:
};

CellOverVolt::CellOverVolt(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.CellOverVolt)
}
CellOverVolt::CellOverVolt(const CellOverVolt& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&lifepo4cellallowedchgvoltmax_, &from.lifepo4cellallowedchgvoltmax_,
    static_cast<size_t>(reinterpret_cast<char*>(&cellovervoltprotdisabled_) -
    reinterpret_cast<char*>(&lifepo4cellallowedchgvoltmax_)) + sizeof(cellovervoltprotdisabled_));
  // @@protoc_insertion_point(copy_constructor:DMCinfo.CellOverVolt)
}

inline void CellOverVolt::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&lifepo4cellallowedchgvoltmax_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&cellovervoltprotdisabled_) -
    reinterpret_cast<char*>(&lifepo4cellallowedchgvoltmax_)) + sizeof(cellovervoltprotdisabled_));
}

CellOverVolt::~CellOverVolt() {
  // @@protoc_insertion_point(destructor:DMCinfo.CellOverVolt)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CellOverVolt::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void CellOverVolt::ArenaDtor(void* object) {
  CellOverVolt* _this = reinterpret_cast< CellOverVolt* >(object);
  (void)_this;
}
void CellOverVolt::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CellOverVolt::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CellOverVolt::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.CellOverVolt)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&lifepo4cellallowedchgvoltmax_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&cellovervoltprotdisabled_) -
      reinterpret_cast<char*>(&lifepo4cellallowedchgvoltmax_)) + sizeof(cellovervoltprotdisabled_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CellOverVolt::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float LiFePO4CellAllowedChgVoltMax = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          lifepo4cellallowedchgvoltmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float LiMnNiCoCellAllowedChgVoltMax = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 21)) {
          limnnicocellallowedchgvoltmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float LiTitanateCellAllowedChgVoltMax = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          lititanatecellallowedchgvoltmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float LiManganateCellAllowedChgVoltMax = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 37)) {
          limanganatecellallowedchgvoltmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 CellOverVoltConfireTime = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          cellovervoltconfiretime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 CellOverVoltProtDisabled = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          cellovervoltprotdisabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* CellOverVolt::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.CellOverVolt)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float LiFePO4CellAllowedChgVoltMax = 1;
  if (!(this->_internal_lifepo4cellallowedchgvoltmax() <= 0 && this->_internal_lifepo4cellallowedchgvoltmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_lifepo4cellallowedchgvoltmax(), target);
  }

  // float LiMnNiCoCellAllowedChgVoltMax = 2;
  if (!(this->_internal_limnnicocellallowedchgvoltmax() <= 0 && this->_internal_limnnicocellallowedchgvoltmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_limnnicocellallowedchgvoltmax(), target);
  }

  // float LiTitanateCellAllowedChgVoltMax = 3;
  if (!(this->_internal_lititanatecellallowedchgvoltmax() <= 0 && this->_internal_lititanatecellallowedchgvoltmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_lititanatecellallowedchgvoltmax(), target);
  }

  // float LiManganateCellAllowedChgVoltMax = 4;
  if (!(this->_internal_limanganatecellallowedchgvoltmax() <= 0 && this->_internal_limanganatecellallowedchgvoltmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_limanganatecellallowedchgvoltmax(), target);
  }

  // uint32 CellOverVoltConfireTime = 5;
  if (this->_internal_cellovervoltconfiretime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_cellovervoltconfiretime(), target);
  }

  // uint32 CellOverVoltProtDisabled = 6;
  if (this->_internal_cellovervoltprotdisabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_cellovervoltprotdisabled(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.CellOverVolt)
  return target;
}

size_t CellOverVolt::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.CellOverVolt)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float LiFePO4CellAllowedChgVoltMax = 1;
  if (!(this->_internal_lifepo4cellallowedchgvoltmax() <= 0 && this->_internal_lifepo4cellallowedchgvoltmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float LiMnNiCoCellAllowedChgVoltMax = 2;
  if (!(this->_internal_limnnicocellallowedchgvoltmax() <= 0 && this->_internal_limnnicocellallowedchgvoltmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float LiTitanateCellAllowedChgVoltMax = 3;
  if (!(this->_internal_lititanatecellallowedchgvoltmax() <= 0 && this->_internal_lititanatecellallowedchgvoltmax() >= 0)) {
    total_size += 1 + 4;
  }

  // float LiManganateCellAllowedChgVoltMax = 4;
  if (!(this->_internal_limanganatecellallowedchgvoltmax() <= 0 && this->_internal_limanganatecellallowedchgvoltmax() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 CellOverVoltConfireTime = 5;
  if (this->_internal_cellovervoltconfiretime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_cellovervoltconfiretime());
  }

  // uint32 CellOverVoltProtDisabled = 6;
  if (this->_internal_cellovervoltprotdisabled() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_cellovervoltprotdisabled());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CellOverVolt::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CellOverVolt::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CellOverVolt::GetClassData() const { return &_class_data_; }

void CellOverVolt::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<CellOverVolt *>(to)->MergeFrom(
      static_cast<const CellOverVolt &>(from));
}


void CellOverVolt::MergeFrom(const CellOverVolt& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.CellOverVolt)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from._internal_lifepo4cellallowedchgvoltmax() <= 0 && from._internal_lifepo4cellallowedchgvoltmax() >= 0)) {
    _internal_set_lifepo4cellallowedchgvoltmax(from._internal_lifepo4cellallowedchgvoltmax());
  }
  if (!(from._internal_limnnicocellallowedchgvoltmax() <= 0 && from._internal_limnnicocellallowedchgvoltmax() >= 0)) {
    _internal_set_limnnicocellallowedchgvoltmax(from._internal_limnnicocellallowedchgvoltmax());
  }
  if (!(from._internal_lititanatecellallowedchgvoltmax() <= 0 && from._internal_lititanatecellallowedchgvoltmax() >= 0)) {
    _internal_set_lititanatecellallowedchgvoltmax(from._internal_lititanatecellallowedchgvoltmax());
  }
  if (!(from._internal_limanganatecellallowedchgvoltmax() <= 0 && from._internal_limanganatecellallowedchgvoltmax() >= 0)) {
    _internal_set_limanganatecellallowedchgvoltmax(from._internal_limanganatecellallowedchgvoltmax());
  }
  if (from._internal_cellovervoltconfiretime() != 0) {
    _internal_set_cellovervoltconfiretime(from._internal_cellovervoltconfiretime());
  }
  if (from._internal_cellovervoltprotdisabled() != 0) {
    _internal_set_cellovervoltprotdisabled(from._internal_cellovervoltprotdisabled());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CellOverVolt::CopyFrom(const CellOverVolt& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.CellOverVolt)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CellOverVolt::IsInitialized() const {
  return true;
}

void CellOverVolt::InternalSwap(CellOverVolt* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CellOverVolt, cellovervoltprotdisabled_)
      + sizeof(CellOverVolt::cellovervoltprotdisabled_)
      - PROTOBUF_FIELD_OFFSET(CellOverVolt, lifepo4cellallowedchgvoltmax_)>(
          reinterpret_cast<char*>(&lifepo4cellallowedchgvoltmax_),
          reinterpret_cast<char*>(&other->lifepo4cellallowedchgvoltmax_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CellOverVolt::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[13]);
}

// ===================================================================

class PackOverVolt::_Internal {
 public:
};

PackOverVolt::PackOverVolt(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.PackOverVolt)
}
PackOverVolt::PackOverVolt(const PackOverVolt& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&packallowedchgvoltmax_, &from.packallowedchgvoltmax_,
    static_cast<size_t>(reinterpret_cast<char*>(&packovervoltprotdisabled_) -
    reinterpret_cast<char*>(&packallowedchgvoltmax_)) + sizeof(packovervoltprotdisabled_));
  // @@protoc_insertion_point(copy_constructor:DMCinfo.PackOverVolt)
}

inline void PackOverVolt::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&packallowedchgvoltmax_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&packovervoltprotdisabled_) -
    reinterpret_cast<char*>(&packallowedchgvoltmax_)) + sizeof(packovervoltprotdisabled_));
}

PackOverVolt::~PackOverVolt() {
  // @@protoc_insertion_point(destructor:DMCinfo.PackOverVolt)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PackOverVolt::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void PackOverVolt::ArenaDtor(void* object) {
  PackOverVolt* _this = reinterpret_cast< PackOverVolt* >(object);
  (void)_this;
}
void PackOverVolt::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PackOverVolt::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PackOverVolt::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.PackOverVolt)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&packallowedchgvoltmax_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&packovervoltprotdisabled_) -
      reinterpret_cast<char*>(&packallowedchgvoltmax_)) + sizeof(packovervoltprotdisabled_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PackOverVolt::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float PackAllowedChgVoltMax = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          packallowedchgvoltmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 PackOverVoltConfireTime = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          packovervoltconfiretime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 PackOverVoltProtDisabled = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          packovervoltprotdisabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* PackOverVolt::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.PackOverVolt)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float PackAllowedChgVoltMax = 1;
  if (!(this->_internal_packallowedchgvoltmax() <= 0 && this->_internal_packallowedchgvoltmax() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_packallowedchgvoltmax(), target);
  }

  // uint32 PackOverVoltConfireTime = 2;
  if (this->_internal_packovervoltconfiretime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_packovervoltconfiretime(), target);
  }

  // uint32 PackOverVoltProtDisabled = 3;
  if (this->_internal_packovervoltprotdisabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_packovervoltprotdisabled(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.PackOverVolt)
  return target;
}

size_t PackOverVolt::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.PackOverVolt)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float PackAllowedChgVoltMax = 1;
  if (!(this->_internal_packallowedchgvoltmax() <= 0 && this->_internal_packallowedchgvoltmax() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 PackOverVoltConfireTime = 2;
  if (this->_internal_packovervoltconfiretime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_packovervoltconfiretime());
  }

  // uint32 PackOverVoltProtDisabled = 3;
  if (this->_internal_packovervoltprotdisabled() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_packovervoltprotdisabled());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PackOverVolt::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PackOverVolt::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PackOverVolt::GetClassData() const { return &_class_data_; }

void PackOverVolt::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<PackOverVolt *>(to)->MergeFrom(
      static_cast<const PackOverVolt &>(from));
}


void PackOverVolt::MergeFrom(const PackOverVolt& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.PackOverVolt)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from._internal_packallowedchgvoltmax() <= 0 && from._internal_packallowedchgvoltmax() >= 0)) {
    _internal_set_packallowedchgvoltmax(from._internal_packallowedchgvoltmax());
  }
  if (from._internal_packovervoltconfiretime() != 0) {
    _internal_set_packovervoltconfiretime(from._internal_packovervoltconfiretime());
  }
  if (from._internal_packovervoltprotdisabled() != 0) {
    _internal_set_packovervoltprotdisabled(from._internal_packovervoltprotdisabled());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PackOverVolt::CopyFrom(const PackOverVolt& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.PackOverVolt)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PackOverVolt::IsInitialized() const {
  return true;
}

void PackOverVolt::InternalSwap(PackOverVolt* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PackOverVolt, packovervoltprotdisabled_)
      + sizeof(PackOverVolt::packovervoltprotdisabled_)
      - PROTOBUF_FIELD_OFFSET(PackOverVolt, packallowedchgvoltmax_)>(
          reinterpret_cast<char*>(&packallowedchgvoltmax_),
          reinterpret_cast<char*>(&other->packallowedchgvoltmax_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PackOverVolt::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[14]);
}

// ===================================================================

class OverCurrent::_Internal {
 public:
};

OverCurrent::OverCurrent(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.OverCurrent)
}
OverCurrent::OverCurrent(const OverCurrent& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&overcurrthreshold_, &from.overcurrthreshold_,
    static_cast<size_t>(reinterpret_cast<char*>(&overcurrprotdisabled_) -
    reinterpret_cast<char*>(&overcurrthreshold_)) + sizeof(overcurrprotdisabled_));
  // @@protoc_insertion_point(copy_constructor:DMCinfo.OverCurrent)
}

inline void OverCurrent::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&overcurrthreshold_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&overcurrprotdisabled_) -
    reinterpret_cast<char*>(&overcurrthreshold_)) + sizeof(overcurrprotdisabled_));
}

OverCurrent::~OverCurrent() {
  // @@protoc_insertion_point(destructor:DMCinfo.OverCurrent)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void OverCurrent::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void OverCurrent::ArenaDtor(void* object) {
  OverCurrent* _this = reinterpret_cast< OverCurrent* >(object);
  (void)_this;
}
void OverCurrent::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void OverCurrent::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void OverCurrent::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.OverCurrent)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&overcurrthreshold_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&overcurrprotdisabled_) -
      reinterpret_cast<char*>(&overcurrthreshold_)) + sizeof(overcurrprotdisabled_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* OverCurrent::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float OverCurrThreshold = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          overcurrthreshold_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 OverCurrConfireTime = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          overcurrconfiretime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 OverCurrProtDisabled = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          overcurrprotdisabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* OverCurrent::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.OverCurrent)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float OverCurrThreshold = 1;
  if (!(this->_internal_overcurrthreshold() <= 0 && this->_internal_overcurrthreshold() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_overcurrthreshold(), target);
  }

  // uint32 OverCurrConfireTime = 2;
  if (this->_internal_overcurrconfiretime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_overcurrconfiretime(), target);
  }

  // uint32 OverCurrProtDisabled = 3;
  if (this->_internal_overcurrprotdisabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_overcurrprotdisabled(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.OverCurrent)
  return target;
}

size_t OverCurrent::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.OverCurrent)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float OverCurrThreshold = 1;
  if (!(this->_internal_overcurrthreshold() <= 0 && this->_internal_overcurrthreshold() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 OverCurrConfireTime = 2;
  if (this->_internal_overcurrconfiretime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_overcurrconfiretime());
  }

  // uint32 OverCurrProtDisabled = 3;
  if (this->_internal_overcurrprotdisabled() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_overcurrprotdisabled());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData OverCurrent::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    OverCurrent::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*OverCurrent::GetClassData() const { return &_class_data_; }

void OverCurrent::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<OverCurrent *>(to)->MergeFrom(
      static_cast<const OverCurrent &>(from));
}


void OverCurrent::MergeFrom(const OverCurrent& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.OverCurrent)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from._internal_overcurrthreshold() <= 0 && from._internal_overcurrthreshold() >= 0)) {
    _internal_set_overcurrthreshold(from._internal_overcurrthreshold());
  }
  if (from._internal_overcurrconfiretime() != 0) {
    _internal_set_overcurrconfiretime(from._internal_overcurrconfiretime());
  }
  if (from._internal_overcurrprotdisabled() != 0) {
    _internal_set_overcurrprotdisabled(from._internal_overcurrprotdisabled());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void OverCurrent::CopyFrom(const OverCurrent& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.OverCurrent)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OverCurrent::IsInitialized() const {
  return true;
}

void OverCurrent::InternalSwap(OverCurrent* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(OverCurrent, overcurrprotdisabled_)
      + sizeof(OverCurrent::overcurrprotdisabled_)
      - PROTOBUF_FIELD_OFFSET(OverCurrent, overcurrthreshold_)>(
          reinterpret_cast<char*>(&overcurrthreshold_),
          reinterpret_cast<char*>(&other->overcurrthreshold_));
}

::PROTOBUF_NAMESPACE_ID::Metadata OverCurrent::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[15]);
}

// ===================================================================

class CellOverTemp::_Internal {
 public:
};

CellOverTemp::CellOverTemp(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.CellOverTemp)
}
CellOverTemp::CellOverTemp(const CellOverTemp& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&lifepo4overtempthreshold_, &from.lifepo4overtempthreshold_,
    static_cast<size_t>(reinterpret_cast<char*>(&overtempprotdisabled_) -
    reinterpret_cast<char*>(&lifepo4overtempthreshold_)) + sizeof(overtempprotdisabled_));
  // @@protoc_insertion_point(copy_constructor:DMCinfo.CellOverTemp)
}

inline void CellOverTemp::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&lifepo4overtempthreshold_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&overtempprotdisabled_) -
    reinterpret_cast<char*>(&lifepo4overtempthreshold_)) + sizeof(overtempprotdisabled_));
}

CellOverTemp::~CellOverTemp() {
  // @@protoc_insertion_point(destructor:DMCinfo.CellOverTemp)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CellOverTemp::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void CellOverTemp::ArenaDtor(void* object) {
  CellOverTemp* _this = reinterpret_cast< CellOverTemp* >(object);
  (void)_this;
}
void CellOverTemp::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CellOverTemp::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CellOverTemp::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.CellOverTemp)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&lifepo4overtempthreshold_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&overtempprotdisabled_) -
      reinterpret_cast<char*>(&lifepo4overtempthreshold_)) + sizeof(overtempprotdisabled_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CellOverTemp::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float LiFePO4OverTempThreshold = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          lifepo4overtempthreshold_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float LiMnNiCoOverTempThreshold = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 21)) {
          limnnicoovertempthreshold_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float LiTitanateOverTempThreshold = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          lititanateovertempthreshold_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float LiManganateOverTempThreshold = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 37)) {
          limanganateovertempthreshold_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 OverTempConfireTime = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          overtempconfiretime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 OverTempProtDisabled = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          overtempprotdisabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* CellOverTemp::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.CellOverTemp)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float LiFePO4OverTempThreshold = 1;
  if (!(this->_internal_lifepo4overtempthreshold() <= 0 && this->_internal_lifepo4overtempthreshold() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_lifepo4overtempthreshold(), target);
  }

  // float LiMnNiCoOverTempThreshold = 2;
  if (!(this->_internal_limnnicoovertempthreshold() <= 0 && this->_internal_limnnicoovertempthreshold() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_limnnicoovertempthreshold(), target);
  }

  // float LiTitanateOverTempThreshold = 3;
  if (!(this->_internal_lititanateovertempthreshold() <= 0 && this->_internal_lititanateovertempthreshold() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_lititanateovertempthreshold(), target);
  }

  // float LiManganateOverTempThreshold = 4;
  if (!(this->_internal_limanganateovertempthreshold() <= 0 && this->_internal_limanganateovertempthreshold() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_limanganateovertempthreshold(), target);
  }

  // uint32 OverTempConfireTime = 5;
  if (this->_internal_overtempconfiretime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_overtempconfiretime(), target);
  }

  // uint32 OverTempProtDisabled = 6;
  if (this->_internal_overtempprotdisabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_overtempprotdisabled(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.CellOverTemp)
  return target;
}

size_t CellOverTemp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.CellOverTemp)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float LiFePO4OverTempThreshold = 1;
  if (!(this->_internal_lifepo4overtempthreshold() <= 0 && this->_internal_lifepo4overtempthreshold() >= 0)) {
    total_size += 1 + 4;
  }

  // float LiMnNiCoOverTempThreshold = 2;
  if (!(this->_internal_limnnicoovertempthreshold() <= 0 && this->_internal_limnnicoovertempthreshold() >= 0)) {
    total_size += 1 + 4;
  }

  // float LiTitanateOverTempThreshold = 3;
  if (!(this->_internal_lititanateovertempthreshold() <= 0 && this->_internal_lititanateovertempthreshold() >= 0)) {
    total_size += 1 + 4;
  }

  // float LiManganateOverTempThreshold = 4;
  if (!(this->_internal_limanganateovertempthreshold() <= 0 && this->_internal_limanganateovertempthreshold() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 OverTempConfireTime = 5;
  if (this->_internal_overtempconfiretime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_overtempconfiretime());
  }

  // uint32 OverTempProtDisabled = 6;
  if (this->_internal_overtempprotdisabled() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_overtempprotdisabled());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CellOverTemp::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CellOverTemp::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CellOverTemp::GetClassData() const { return &_class_data_; }

void CellOverTemp::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<CellOverTemp *>(to)->MergeFrom(
      static_cast<const CellOverTemp &>(from));
}


void CellOverTemp::MergeFrom(const CellOverTemp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.CellOverTemp)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from._internal_lifepo4overtempthreshold() <= 0 && from._internal_lifepo4overtempthreshold() >= 0)) {
    _internal_set_lifepo4overtempthreshold(from._internal_lifepo4overtempthreshold());
  }
  if (!(from._internal_limnnicoovertempthreshold() <= 0 && from._internal_limnnicoovertempthreshold() >= 0)) {
    _internal_set_limnnicoovertempthreshold(from._internal_limnnicoovertempthreshold());
  }
  if (!(from._internal_lititanateovertempthreshold() <= 0 && from._internal_lititanateovertempthreshold() >= 0)) {
    _internal_set_lititanateovertempthreshold(from._internal_lititanateovertempthreshold());
  }
  if (!(from._internal_limanganateovertempthreshold() <= 0 && from._internal_limanganateovertempthreshold() >= 0)) {
    _internal_set_limanganateovertempthreshold(from._internal_limanganateovertempthreshold());
  }
  if (from._internal_overtempconfiretime() != 0) {
    _internal_set_overtempconfiretime(from._internal_overtempconfiretime());
  }
  if (from._internal_overtempprotdisabled() != 0) {
    _internal_set_overtempprotdisabled(from._internal_overtempprotdisabled());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CellOverTemp::CopyFrom(const CellOverTemp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.CellOverTemp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CellOverTemp::IsInitialized() const {
  return true;
}

void CellOverTemp::InternalSwap(CellOverTemp* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CellOverTemp, overtempprotdisabled_)
      + sizeof(CellOverTemp::overtempprotdisabled_)
      - PROTOBUF_FIELD_OFFSET(CellOverTemp, lifepo4overtempthreshold_)>(
          reinterpret_cast<char*>(&lifepo4overtempthreshold_),
          reinterpret_cast<char*>(&other->lifepo4overtempthreshold_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CellOverTemp::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[16]);
}

// ===================================================================

class LowTemp::_Internal {
 public:
};

LowTemp::LowTemp(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.LowTemp)
}
LowTemp::LowTemp(const LowTemp& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&lowtempthreshold_, &from.lowtempthreshold_,
    static_cast<size_t>(reinterpret_cast<char*>(&lowtempprotdisabled_) -
    reinterpret_cast<char*>(&lowtempthreshold_)) + sizeof(lowtempprotdisabled_));
  // @@protoc_insertion_point(copy_constructor:DMCinfo.LowTemp)
}

inline void LowTemp::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&lowtempthreshold_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&lowtempprotdisabled_) -
    reinterpret_cast<char*>(&lowtempthreshold_)) + sizeof(lowtempprotdisabled_));
}

LowTemp::~LowTemp() {
  // @@protoc_insertion_point(destructor:DMCinfo.LowTemp)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LowTemp::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void LowTemp::ArenaDtor(void* object) {
  LowTemp* _this = reinterpret_cast< LowTemp* >(object);
  (void)_this;
}
void LowTemp::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LowTemp::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LowTemp::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.LowTemp)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&lowtempthreshold_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&lowtempprotdisabled_) -
      reinterpret_cast<char*>(&lowtempthreshold_)) + sizeof(lowtempprotdisabled_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LowTemp::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float LowTempThreshold = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          lowtempthreshold_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 LowTempConfireTime = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          lowtempconfiretime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 LowTempProtDisabled = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          lowtempprotdisabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* LowTemp::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.LowTemp)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float LowTempThreshold = 1;
  if (!(this->_internal_lowtempthreshold() <= 0 && this->_internal_lowtempthreshold() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_lowtempthreshold(), target);
  }

  // uint32 LowTempConfireTime = 2;
  if (this->_internal_lowtempconfiretime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_lowtempconfiretime(), target);
  }

  // uint32 LowTempProtDisabled = 3;
  if (this->_internal_lowtempprotdisabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_lowtempprotdisabled(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.LowTemp)
  return target;
}

size_t LowTemp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.LowTemp)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float LowTempThreshold = 1;
  if (!(this->_internal_lowtempthreshold() <= 0 && this->_internal_lowtempthreshold() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 LowTempConfireTime = 2;
  if (this->_internal_lowtempconfiretime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_lowtempconfiretime());
  }

  // uint32 LowTempProtDisabled = 3;
  if (this->_internal_lowtempprotdisabled() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_lowtempprotdisabled());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LowTemp::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LowTemp::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LowTemp::GetClassData() const { return &_class_data_; }

void LowTemp::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<LowTemp *>(to)->MergeFrom(
      static_cast<const LowTemp &>(from));
}


void LowTemp::MergeFrom(const LowTemp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.LowTemp)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from._internal_lowtempthreshold() <= 0 && from._internal_lowtempthreshold() >= 0)) {
    _internal_set_lowtempthreshold(from._internal_lowtempthreshold());
  }
  if (from._internal_lowtempconfiretime() != 0) {
    _internal_set_lowtempconfiretime(from._internal_lowtempconfiretime());
  }
  if (from._internal_lowtempprotdisabled() != 0) {
    _internal_set_lowtempprotdisabled(from._internal_lowtempprotdisabled());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LowTemp::CopyFrom(const LowTemp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.LowTemp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LowTemp::IsInitialized() const {
  return true;
}

void LowTemp::InternalSwap(LowTemp* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LowTemp, lowtempprotdisabled_)
      + sizeof(LowTemp::lowtempprotdisabled_)
      - PROTOBUF_FIELD_OFFSET(LowTemp, lowtempthreshold_)>(
          reinterpret_cast<char*>(&lowtempthreshold_),
          reinterpret_cast<char*>(&other->lowtempthreshold_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LowTemp::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[17]);
}

// ===================================================================

class BMSRlyStick::_Internal {
 public:
};

BMSRlyStick::BMSRlyStick(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.BMSRlyStick)
}
BMSRlyStick::BMSRlyStick(const BMSRlyStick& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&bmsrlystickvoltthreshold_, &from.bmsrlystickvoltthreshold_,
    static_cast<size_t>(reinterpret_cast<char*>(&bmsrlystickdisabled_) -
    reinterpret_cast<char*>(&bmsrlystickvoltthreshold_)) + sizeof(bmsrlystickdisabled_));
  // @@protoc_insertion_point(copy_constructor:DMCinfo.BMSRlyStick)
}

inline void BMSRlyStick::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&bmsrlystickvoltthreshold_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&bmsrlystickdisabled_) -
    reinterpret_cast<char*>(&bmsrlystickvoltthreshold_)) + sizeof(bmsrlystickdisabled_));
}

BMSRlyStick::~BMSRlyStick() {
  // @@protoc_insertion_point(destructor:DMCinfo.BMSRlyStick)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void BMSRlyStick::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void BMSRlyStick::ArenaDtor(void* object) {
  BMSRlyStick* _this = reinterpret_cast< BMSRlyStick* >(object);
  (void)_this;
}
void BMSRlyStick::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void BMSRlyStick::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void BMSRlyStick::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.BMSRlyStick)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&bmsrlystickvoltthreshold_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&bmsrlystickdisabled_) -
      reinterpret_cast<char*>(&bmsrlystickvoltthreshold_)) + sizeof(bmsrlystickdisabled_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* BMSRlyStick::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float BMSRlyStickVoltThreshold = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          bmsrlystickvoltthreshold_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 BMSRlyStickConfireTime = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          bmsrlystickconfiretime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 BMSRlyStickDisabled = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          bmsrlystickdisabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* BMSRlyStick::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.BMSRlyStick)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float BMSRlyStickVoltThreshold = 1;
  if (!(this->_internal_bmsrlystickvoltthreshold() <= 0 && this->_internal_bmsrlystickvoltthreshold() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_bmsrlystickvoltthreshold(), target);
  }

  // uint32 BMSRlyStickConfireTime = 2;
  if (this->_internal_bmsrlystickconfiretime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_bmsrlystickconfiretime(), target);
  }

  // uint32 BMSRlyStickDisabled = 3;
  if (this->_internal_bmsrlystickdisabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_bmsrlystickdisabled(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.BMSRlyStick)
  return target;
}

size_t BMSRlyStick::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.BMSRlyStick)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float BMSRlyStickVoltThreshold = 1;
  if (!(this->_internal_bmsrlystickvoltthreshold() <= 0 && this->_internal_bmsrlystickvoltthreshold() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 BMSRlyStickConfireTime = 2;
  if (this->_internal_bmsrlystickconfiretime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsrlystickconfiretime());
  }

  // uint32 BMSRlyStickDisabled = 3;
  if (this->_internal_bmsrlystickdisabled() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsrlystickdisabled());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData BMSRlyStick::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    BMSRlyStick::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*BMSRlyStick::GetClassData() const { return &_class_data_; }

void BMSRlyStick::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<BMSRlyStick *>(to)->MergeFrom(
      static_cast<const BMSRlyStick &>(from));
}


void BMSRlyStick::MergeFrom(const BMSRlyStick& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.BMSRlyStick)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from._internal_bmsrlystickvoltthreshold() <= 0 && from._internal_bmsrlystickvoltthreshold() >= 0)) {
    _internal_set_bmsrlystickvoltthreshold(from._internal_bmsrlystickvoltthreshold());
  }
  if (from._internal_bmsrlystickconfiretime() != 0) {
    _internal_set_bmsrlystickconfiretime(from._internal_bmsrlystickconfiretime());
  }
  if (from._internal_bmsrlystickdisabled() != 0) {
    _internal_set_bmsrlystickdisabled(from._internal_bmsrlystickdisabled());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void BMSRlyStick::CopyFrom(const BMSRlyStick& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.BMSRlyStick)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BMSRlyStick::IsInitialized() const {
  return true;
}

void BMSRlyStick::InternalSwap(BMSRlyStick* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(BMSRlyStick, bmsrlystickdisabled_)
      + sizeof(BMSRlyStick::bmsrlystickdisabled_)
      - PROTOBUF_FIELD_OFFSET(BMSRlyStick, bmsrlystickvoltthreshold_)>(
          reinterpret_cast<char*>(&bmsrlystickvoltthreshold_),
          reinterpret_cast<char*>(&other->bmsrlystickvoltthreshold_));
}

::PROTOBUF_NAMESPACE_ID::Metadata BMSRlyStick::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[18]);
}

// ===================================================================

class BMSRlyOC::_Internal {
 public:
};

BMSRlyOC::BMSRlyOC(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.BMSRlyOC)
}
BMSRlyOC::BMSRlyOC(const BMSRlyOC& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&bmsrlyocvoltthreshold_, &from.bmsrlyocvoltthreshold_,
    static_cast<size_t>(reinterpret_cast<char*>(&bmsrlyocdisabled_) -
    reinterpret_cast<char*>(&bmsrlyocvoltthreshold_)) + sizeof(bmsrlyocdisabled_));
  // @@protoc_insertion_point(copy_constructor:DMCinfo.BMSRlyOC)
}

inline void BMSRlyOC::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&bmsrlyocvoltthreshold_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&bmsrlyocdisabled_) -
    reinterpret_cast<char*>(&bmsrlyocvoltthreshold_)) + sizeof(bmsrlyocdisabled_));
}

BMSRlyOC::~BMSRlyOC() {
  // @@protoc_insertion_point(destructor:DMCinfo.BMSRlyOC)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void BMSRlyOC::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void BMSRlyOC::ArenaDtor(void* object) {
  BMSRlyOC* _this = reinterpret_cast< BMSRlyOC* >(object);
  (void)_this;
}
void BMSRlyOC::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void BMSRlyOC::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void BMSRlyOC::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.BMSRlyOC)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&bmsrlyocvoltthreshold_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&bmsrlyocdisabled_) -
      reinterpret_cast<char*>(&bmsrlyocvoltthreshold_)) + sizeof(bmsrlyocdisabled_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* BMSRlyOC::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float BMSRlyOCVoltThreshold = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          bmsrlyocvoltthreshold_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float BMSRlyOCCurrentThreshold = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 21)) {
          bmsrlyoccurrentthreshold_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 BMSRlyOCConfireTime = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          bmsrlyocconfiretime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 BMSRlyOCDisabled = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          bmsrlyocdisabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* BMSRlyOC::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.BMSRlyOC)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float BMSRlyOCVoltThreshold = 1;
  if (!(this->_internal_bmsrlyocvoltthreshold() <= 0 && this->_internal_bmsrlyocvoltthreshold() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_bmsrlyocvoltthreshold(), target);
  }

  // float BMSRlyOCCurrentThreshold = 2;
  if (!(this->_internal_bmsrlyoccurrentthreshold() <= 0 && this->_internal_bmsrlyoccurrentthreshold() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_bmsrlyoccurrentthreshold(), target);
  }

  // uint32 BMSRlyOCConfireTime = 3;
  if (this->_internal_bmsrlyocconfiretime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_bmsrlyocconfiretime(), target);
  }

  // uint32 BMSRlyOCDisabled = 4;
  if (this->_internal_bmsrlyocdisabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_bmsrlyocdisabled(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.BMSRlyOC)
  return target;
}

size_t BMSRlyOC::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.BMSRlyOC)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float BMSRlyOCVoltThreshold = 1;
  if (!(this->_internal_bmsrlyocvoltthreshold() <= 0 && this->_internal_bmsrlyocvoltthreshold() >= 0)) {
    total_size += 1 + 4;
  }

  // float BMSRlyOCCurrentThreshold = 2;
  if (!(this->_internal_bmsrlyoccurrentthreshold() <= 0 && this->_internal_bmsrlyoccurrentthreshold() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 BMSRlyOCConfireTime = 3;
  if (this->_internal_bmsrlyocconfiretime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsrlyocconfiretime());
  }

  // uint32 BMSRlyOCDisabled = 4;
  if (this->_internal_bmsrlyocdisabled() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsrlyocdisabled());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData BMSRlyOC::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    BMSRlyOC::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*BMSRlyOC::GetClassData() const { return &_class_data_; }

void BMSRlyOC::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<BMSRlyOC *>(to)->MergeFrom(
      static_cast<const BMSRlyOC &>(from));
}


void BMSRlyOC::MergeFrom(const BMSRlyOC& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.BMSRlyOC)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from._internal_bmsrlyocvoltthreshold() <= 0 && from._internal_bmsrlyocvoltthreshold() >= 0)) {
    _internal_set_bmsrlyocvoltthreshold(from._internal_bmsrlyocvoltthreshold());
  }
  if (!(from._internal_bmsrlyoccurrentthreshold() <= 0 && from._internal_bmsrlyoccurrentthreshold() >= 0)) {
    _internal_set_bmsrlyoccurrentthreshold(from._internal_bmsrlyoccurrentthreshold());
  }
  if (from._internal_bmsrlyocconfiretime() != 0) {
    _internal_set_bmsrlyocconfiretime(from._internal_bmsrlyocconfiretime());
  }
  if (from._internal_bmsrlyocdisabled() != 0) {
    _internal_set_bmsrlyocdisabled(from._internal_bmsrlyocdisabled());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void BMSRlyOC::CopyFrom(const BMSRlyOC& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.BMSRlyOC)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BMSRlyOC::IsInitialized() const {
  return true;
}

void BMSRlyOC::InternalSwap(BMSRlyOC* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(BMSRlyOC, bmsrlyocdisabled_)
      + sizeof(BMSRlyOC::bmsrlyocdisabled_)
      - PROTOBUF_FIELD_OFFSET(BMSRlyOC, bmsrlyocvoltthreshold_)>(
          reinterpret_cast<char*>(&bmsrlyocvoltthreshold_),
          reinterpret_cast<char*>(&other->bmsrlyocvoltthreshold_));
}

::PROTOBUF_NAMESPACE_ID::Metadata BMSRlyOC::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[19]);
}

// ===================================================================

class OverCharge::_Internal {
 public:
};

OverCharge::OverCharge(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.OverCharge)
}
OverCharge::OverCharge(const OverCharge& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&overchgcoef_, &from.overchgcoef_,
    static_cast<size_t>(reinterpret_cast<char*>(&overchgprotdisabled_) -
    reinterpret_cast<char*>(&overchgcoef_)) + sizeof(overchgprotdisabled_));
  // @@protoc_insertion_point(copy_constructor:DMCinfo.OverCharge)
}

inline void OverCharge::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&overchgcoef_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&overchgprotdisabled_) -
    reinterpret_cast<char*>(&overchgcoef_)) + sizeof(overchgprotdisabled_));
}

OverCharge::~OverCharge() {
  // @@protoc_insertion_point(destructor:DMCinfo.OverCharge)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void OverCharge::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void OverCharge::ArenaDtor(void* object) {
  OverCharge* _this = reinterpret_cast< OverCharge* >(object);
  (void)_this;
}
void OverCharge::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void OverCharge::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void OverCharge::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.OverCharge)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&overchgcoef_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&overchgprotdisabled_) -
      reinterpret_cast<char*>(&overchgcoef_)) + sizeof(overchgprotdisabled_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* OverCharge::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float OverChgCoef = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          overchgcoef_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float OverChgAH = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 21)) {
          overchgah_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // uint32 OverChgConfireTime = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          overchgconfiretime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 OverChgProtDisabled = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          overchgprotdisabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* OverCharge::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.OverCharge)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float OverChgCoef = 1;
  if (!(this->_internal_overchgcoef() <= 0 && this->_internal_overchgcoef() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_overchgcoef(), target);
  }

  // float OverChgAH = 2;
  if (!(this->_internal_overchgah() <= 0 && this->_internal_overchgah() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_overchgah(), target);
  }

  // uint32 OverChgConfireTime = 3;
  if (this->_internal_overchgconfiretime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_overchgconfiretime(), target);
  }

  // uint32 OverChgProtDisabled = 4;
  if (this->_internal_overchgprotdisabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_overchgprotdisabled(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.OverCharge)
  return target;
}

size_t OverCharge::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.OverCharge)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float OverChgCoef = 1;
  if (!(this->_internal_overchgcoef() <= 0 && this->_internal_overchgcoef() >= 0)) {
    total_size += 1 + 4;
  }

  // float OverChgAH = 2;
  if (!(this->_internal_overchgah() <= 0 && this->_internal_overchgah() >= 0)) {
    total_size += 1 + 4;
  }

  // uint32 OverChgConfireTime = 3;
  if (this->_internal_overchgconfiretime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_overchgconfiretime());
  }

  // uint32 OverChgProtDisabled = 4;
  if (this->_internal_overchgprotdisabled() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_overchgprotdisabled());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData OverCharge::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    OverCharge::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*OverCharge::GetClassData() const { return &_class_data_; }

void OverCharge::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<OverCharge *>(to)->MergeFrom(
      static_cast<const OverCharge &>(from));
}


void OverCharge::MergeFrom(const OverCharge& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.OverCharge)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from._internal_overchgcoef() <= 0 && from._internal_overchgcoef() >= 0)) {
    _internal_set_overchgcoef(from._internal_overchgcoef());
  }
  if (!(from._internal_overchgah() <= 0 && from._internal_overchgah() >= 0)) {
    _internal_set_overchgah(from._internal_overchgah());
  }
  if (from._internal_overchgconfiretime() != 0) {
    _internal_set_overchgconfiretime(from._internal_overchgconfiretime());
  }
  if (from._internal_overchgprotdisabled() != 0) {
    _internal_set_overchgprotdisabled(from._internal_overchgprotdisabled());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void OverCharge::CopyFrom(const OverCharge& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.OverCharge)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OverCharge::IsInitialized() const {
  return true;
}

void OverCharge::InternalSwap(OverCharge* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(OverCharge, overchgprotdisabled_)
      + sizeof(OverCharge::overchgprotdisabled_)
      - PROTOBUF_FIELD_OFFSET(OverCharge, overchgcoef_)>(
          reinterpret_cast<char*>(&overchgcoef_),
          reinterpret_cast<char*>(&other->overchgcoef_));
}

::PROTOBUF_NAMESPACE_ID::Metadata OverCharge::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[20]);
}

// ===================================================================

class BMSDataRepeat::_Internal {
 public:
};

BMSDataRepeat::BMSDataRepeat(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.BMSDataRepeat)
}
BMSDataRepeat::BMSDataRepeat(const BMSDataRepeat& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&bmsdatarepeatconfiretime_, &from.bmsdatarepeatconfiretime_,
    static_cast<size_t>(reinterpret_cast<char*>(&bmsdataerrorprotdisabled_) -
    reinterpret_cast<char*>(&bmsdatarepeatconfiretime_)) + sizeof(bmsdataerrorprotdisabled_));
  // @@protoc_insertion_point(copy_constructor:DMCinfo.BMSDataRepeat)
}

inline void BMSDataRepeat::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&bmsdatarepeatconfiretime_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&bmsdataerrorprotdisabled_) -
    reinterpret_cast<char*>(&bmsdatarepeatconfiretime_)) + sizeof(bmsdataerrorprotdisabled_));
}

BMSDataRepeat::~BMSDataRepeat() {
  // @@protoc_insertion_point(destructor:DMCinfo.BMSDataRepeat)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void BMSDataRepeat::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void BMSDataRepeat::ArenaDtor(void* object) {
  BMSDataRepeat* _this = reinterpret_cast< BMSDataRepeat* >(object);
  (void)_this;
}
void BMSDataRepeat::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void BMSDataRepeat::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void BMSDataRepeat::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.BMSDataRepeat)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&bmsdatarepeatconfiretime_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&bmsdataerrorprotdisabled_) -
      reinterpret_cast<char*>(&bmsdatarepeatconfiretime_)) + sizeof(bmsdataerrorprotdisabled_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* BMSDataRepeat::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 BMSDataRepeatConfireTime = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          bmsdatarepeatconfiretime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 BMBMSDataErrorProtDisabled = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          bmbmsdataerrorprotdisabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 BMSDataErrorProtDisabled = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          bmsdataerrorprotdisabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* BMSDataRepeat::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.BMSDataRepeat)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 BMSDataRepeatConfireTime = 1;
  if (this->_internal_bmsdatarepeatconfiretime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_bmsdatarepeatconfiretime(), target);
  }

  // uint32 BMBMSDataErrorProtDisabled = 2;
  if (this->_internal_bmbmsdataerrorprotdisabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_bmbmsdataerrorprotdisabled(), target);
  }

  // uint32 BMSDataErrorProtDisabled = 3;
  if (this->_internal_bmsdataerrorprotdisabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_bmsdataerrorprotdisabled(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.BMSDataRepeat)
  return target;
}

size_t BMSDataRepeat::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.BMSDataRepeat)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 BMSDataRepeatConfireTime = 1;
  if (this->_internal_bmsdatarepeatconfiretime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsdatarepeatconfiretime());
  }

  // uint32 BMBMSDataErrorProtDisabled = 2;
  if (this->_internal_bmbmsdataerrorprotdisabled() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmbmsdataerrorprotdisabled());
  }

  // uint32 BMSDataErrorProtDisabled = 3;
  if (this->_internal_bmsdataerrorprotdisabled() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_bmsdataerrorprotdisabled());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData BMSDataRepeat::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    BMSDataRepeat::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*BMSDataRepeat::GetClassData() const { return &_class_data_; }

void BMSDataRepeat::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<BMSDataRepeat *>(to)->MergeFrom(
      static_cast<const BMSDataRepeat &>(from));
}


void BMSDataRepeat::MergeFrom(const BMSDataRepeat& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.BMSDataRepeat)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_bmsdatarepeatconfiretime() != 0) {
    _internal_set_bmsdatarepeatconfiretime(from._internal_bmsdatarepeatconfiretime());
  }
  if (from._internal_bmbmsdataerrorprotdisabled() != 0) {
    _internal_set_bmbmsdataerrorprotdisabled(from._internal_bmbmsdataerrorprotdisabled());
  }
  if (from._internal_bmsdataerrorprotdisabled() != 0) {
    _internal_set_bmsdataerrorprotdisabled(from._internal_bmsdataerrorprotdisabled());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void BMSDataRepeat::CopyFrom(const BMSDataRepeat& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.BMSDataRepeat)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BMSDataRepeat::IsInitialized() const {
  return true;
}

void BMSDataRepeat::InternalSwap(BMSDataRepeat* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(BMSDataRepeat, bmsdataerrorprotdisabled_)
      + sizeof(BMSDataRepeat::bmsdataerrorprotdisabled_)
      - PROTOBUF_FIELD_OFFSET(BMSDataRepeat, bmsdatarepeatconfiretime_)>(
          reinterpret_cast<char*>(&bmsdatarepeatconfiretime_),
          reinterpret_cast<char*>(&other->bmsdatarepeatconfiretime_));
}

::PROTOBUF_NAMESPACE_ID::Metadata BMSDataRepeat::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[21]);
}

// ===================================================================

class ActiveProtectParam::_Internal {
 public:
  static const ::DMCinfo::CurrentBalance& currbalance(const ActiveProtectParam* msg);
  static const ::DMCinfo::HotRunaway& hotrunaway(const ActiveProtectParam* msg);
  static const ::DMCinfo::CellOverVolt& cellovervolt(const ActiveProtectParam* msg);
  static const ::DMCinfo::PackOverVolt& packovervolt(const ActiveProtectParam* msg);
  static const ::DMCinfo::OverCurrent& overcurrent(const ActiveProtectParam* msg);
  static const ::DMCinfo::CellOverTemp& cellovertemp(const ActiveProtectParam* msg);
  static const ::DMCinfo::LowTemp& lowtemp(const ActiveProtectParam* msg);
  static const ::DMCinfo::BMSRlyStick& bmsrlystick(const ActiveProtectParam* msg);
  static const ::DMCinfo::BMSRlyOC& bmsrlyoc(const ActiveProtectParam* msg);
  static const ::DMCinfo::OverCharge& overcharge(const ActiveProtectParam* msg);
  static const ::DMCinfo::BMSDataRepeat& bmsdatarepeat(const ActiveProtectParam* msg);
};

const ::DMCinfo::CurrentBalance&
ActiveProtectParam::_Internal::currbalance(const ActiveProtectParam* msg) {
  return *msg->currbalance_;
}
const ::DMCinfo::HotRunaway&
ActiveProtectParam::_Internal::hotrunaway(const ActiveProtectParam* msg) {
  return *msg->hotrunaway_;
}
const ::DMCinfo::CellOverVolt&
ActiveProtectParam::_Internal::cellovervolt(const ActiveProtectParam* msg) {
  return *msg->cellovervolt_;
}
const ::DMCinfo::PackOverVolt&
ActiveProtectParam::_Internal::packovervolt(const ActiveProtectParam* msg) {
  return *msg->packovervolt_;
}
const ::DMCinfo::OverCurrent&
ActiveProtectParam::_Internal::overcurrent(const ActiveProtectParam* msg) {
  return *msg->overcurrent_;
}
const ::DMCinfo::CellOverTemp&
ActiveProtectParam::_Internal::cellovertemp(const ActiveProtectParam* msg) {
  return *msg->cellovertemp_;
}
const ::DMCinfo::LowTemp&
ActiveProtectParam::_Internal::lowtemp(const ActiveProtectParam* msg) {
  return *msg->lowtemp_;
}
const ::DMCinfo::BMSRlyStick&
ActiveProtectParam::_Internal::bmsrlystick(const ActiveProtectParam* msg) {
  return *msg->bmsrlystick_;
}
const ::DMCinfo::BMSRlyOC&
ActiveProtectParam::_Internal::bmsrlyoc(const ActiveProtectParam* msg) {
  return *msg->bmsrlyoc_;
}
const ::DMCinfo::OverCharge&
ActiveProtectParam::_Internal::overcharge(const ActiveProtectParam* msg) {
  return *msg->overcharge_;
}
const ::DMCinfo::BMSDataRepeat&
ActiveProtectParam::_Internal::bmsdatarepeat(const ActiveProtectParam* msg) {
  return *msg->bmsdatarepeat_;
}
ActiveProtectParam::ActiveProtectParam(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:DMCinfo.ActiveProtectParam)
}
ActiveProtectParam::ActiveProtectParam(const ActiveProtectParam& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_currbalance()) {
    currbalance_ = new ::DMCinfo::CurrentBalance(*from.currbalance_);
  } else {
    currbalance_ = nullptr;
  }
  if (from._internal_has_hotrunaway()) {
    hotrunaway_ = new ::DMCinfo::HotRunaway(*from.hotrunaway_);
  } else {
    hotrunaway_ = nullptr;
  }
  if (from._internal_has_cellovervolt()) {
    cellovervolt_ = new ::DMCinfo::CellOverVolt(*from.cellovervolt_);
  } else {
    cellovervolt_ = nullptr;
  }
  if (from._internal_has_packovervolt()) {
    packovervolt_ = new ::DMCinfo::PackOverVolt(*from.packovervolt_);
  } else {
    packovervolt_ = nullptr;
  }
  if (from._internal_has_overcurrent()) {
    overcurrent_ = new ::DMCinfo::OverCurrent(*from.overcurrent_);
  } else {
    overcurrent_ = nullptr;
  }
  if (from._internal_has_cellovertemp()) {
    cellovertemp_ = new ::DMCinfo::CellOverTemp(*from.cellovertemp_);
  } else {
    cellovertemp_ = nullptr;
  }
  if (from._internal_has_lowtemp()) {
    lowtemp_ = new ::DMCinfo::LowTemp(*from.lowtemp_);
  } else {
    lowtemp_ = nullptr;
  }
  if (from._internal_has_bmsrlystick()) {
    bmsrlystick_ = new ::DMCinfo::BMSRlyStick(*from.bmsrlystick_);
  } else {
    bmsrlystick_ = nullptr;
  }
  if (from._internal_has_bmsrlyoc()) {
    bmsrlyoc_ = new ::DMCinfo::BMSRlyOC(*from.bmsrlyoc_);
  } else {
    bmsrlyoc_ = nullptr;
  }
  if (from._internal_has_overcharge()) {
    overcharge_ = new ::DMCinfo::OverCharge(*from.overcharge_);
  } else {
    overcharge_ = nullptr;
  }
  if (from._internal_has_bmsdatarepeat()) {
    bmsdatarepeat_ = new ::DMCinfo::BMSDataRepeat(*from.bmsdatarepeat_);
  } else {
    bmsdatarepeat_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:DMCinfo.ActiveProtectParam)
}

inline void ActiveProtectParam::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&currbalance_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&bmsdatarepeat_) -
    reinterpret_cast<char*>(&currbalance_)) + sizeof(bmsdatarepeat_));
}

ActiveProtectParam::~ActiveProtectParam() {
  // @@protoc_insertion_point(destructor:DMCinfo.ActiveProtectParam)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ActiveProtectParam::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete currbalance_;
  if (this != internal_default_instance()) delete hotrunaway_;
  if (this != internal_default_instance()) delete cellovervolt_;
  if (this != internal_default_instance()) delete packovervolt_;
  if (this != internal_default_instance()) delete overcurrent_;
  if (this != internal_default_instance()) delete cellovertemp_;
  if (this != internal_default_instance()) delete lowtemp_;
  if (this != internal_default_instance()) delete bmsrlystick_;
  if (this != internal_default_instance()) delete bmsrlyoc_;
  if (this != internal_default_instance()) delete overcharge_;
  if (this != internal_default_instance()) delete bmsdatarepeat_;
}

void ActiveProtectParam::ArenaDtor(void* object) {
  ActiveProtectParam* _this = reinterpret_cast< ActiveProtectParam* >(object);
  (void)_this;
}
void ActiveProtectParam::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ActiveProtectParam::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ActiveProtectParam::Clear() {
// @@protoc_insertion_point(message_clear_start:DMCinfo.ActiveProtectParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && currbalance_ != nullptr) {
    delete currbalance_;
  }
  currbalance_ = nullptr;
  if (GetArenaForAllocation() == nullptr && hotrunaway_ != nullptr) {
    delete hotrunaway_;
  }
  hotrunaway_ = nullptr;
  if (GetArenaForAllocation() == nullptr && cellovervolt_ != nullptr) {
    delete cellovervolt_;
  }
  cellovervolt_ = nullptr;
  if (GetArenaForAllocation() == nullptr && packovervolt_ != nullptr) {
    delete packovervolt_;
  }
  packovervolt_ = nullptr;
  if (GetArenaForAllocation() == nullptr && overcurrent_ != nullptr) {
    delete overcurrent_;
  }
  overcurrent_ = nullptr;
  if (GetArenaForAllocation() == nullptr && cellovertemp_ != nullptr) {
    delete cellovertemp_;
  }
  cellovertemp_ = nullptr;
  if (GetArenaForAllocation() == nullptr && lowtemp_ != nullptr) {
    delete lowtemp_;
  }
  lowtemp_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bmsrlystick_ != nullptr) {
    delete bmsrlystick_;
  }
  bmsrlystick_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bmsrlyoc_ != nullptr) {
    delete bmsrlyoc_;
  }
  bmsrlyoc_ = nullptr;
  if (GetArenaForAllocation() == nullptr && overcharge_ != nullptr) {
    delete overcharge_;
  }
  overcharge_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bmsdatarepeat_ != nullptr) {
    delete bmsdatarepeat_;
  }
  bmsdatarepeat_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ActiveProtectParam::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .DMCinfo.CurrentBalance currBalance = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_currbalance(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DMCinfo.HotRunaway hotRunaway = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_hotrunaway(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DMCinfo.CellOverVolt cellOverVolt = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_cellovervolt(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DMCinfo.PackOverVolt packOverVolt = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_packovervolt(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DMCinfo.OverCurrent overCurrent = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_overcurrent(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DMCinfo.CellOverTemp cellOverTemp = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_cellovertemp(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DMCinfo.LowTemp lowTemp = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_lowtemp(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DMCinfo.BMSRlyStick bmsRlyStick = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          ptr = ctx->ParseMessage(_internal_mutable_bmsrlystick(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DMCinfo.BMSRlyOC bmsRlyOC = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          ptr = ctx->ParseMessage(_internal_mutable_bmsrlyoc(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DMCinfo.OverCharge overCharge = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 82)) {
          ptr = ctx->ParseMessage(_internal_mutable_overcharge(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DMCinfo.BMSDataRepeat bmsDataRepeat = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 90)) {
          ptr = ctx->ParseMessage(_internal_mutable_bmsdatarepeat(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ActiveProtectParam::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DMCinfo.ActiveProtectParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .DMCinfo.CurrentBalance currBalance = 1;
  if (this->_internal_has_currbalance()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::currbalance(this), target, stream);
  }

  // .DMCinfo.HotRunaway hotRunaway = 2;
  if (this->_internal_has_hotrunaway()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::hotrunaway(this), target, stream);
  }

  // .DMCinfo.CellOverVolt cellOverVolt = 3;
  if (this->_internal_has_cellovervolt()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::cellovervolt(this), target, stream);
  }

  // .DMCinfo.PackOverVolt packOverVolt = 4;
  if (this->_internal_has_packovervolt()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::packovervolt(this), target, stream);
  }

  // .DMCinfo.OverCurrent overCurrent = 5;
  if (this->_internal_has_overcurrent()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::overcurrent(this), target, stream);
  }

  // .DMCinfo.CellOverTemp cellOverTemp = 6;
  if (this->_internal_has_cellovertemp()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::cellovertemp(this), target, stream);
  }

  // .DMCinfo.LowTemp lowTemp = 7;
  if (this->_internal_has_lowtemp()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::lowtemp(this), target, stream);
  }

  // .DMCinfo.BMSRlyStick bmsRlyStick = 8;
  if (this->_internal_has_bmsrlystick()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        8, _Internal::bmsrlystick(this), target, stream);
  }

  // .DMCinfo.BMSRlyOC bmsRlyOC = 9;
  if (this->_internal_has_bmsrlyoc()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        9, _Internal::bmsrlyoc(this), target, stream);
  }

  // .DMCinfo.OverCharge overCharge = 10;
  if (this->_internal_has_overcharge()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        10, _Internal::overcharge(this), target, stream);
  }

  // .DMCinfo.BMSDataRepeat bmsDataRepeat = 11;
  if (this->_internal_has_bmsdatarepeat()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        11, _Internal::bmsdatarepeat(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DMCinfo.ActiveProtectParam)
  return target;
}

size_t ActiveProtectParam::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DMCinfo.ActiveProtectParam)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .DMCinfo.CurrentBalance currBalance = 1;
  if (this->_internal_has_currbalance()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *currbalance_);
  }

  // .DMCinfo.HotRunaway hotRunaway = 2;
  if (this->_internal_has_hotrunaway()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *hotrunaway_);
  }

  // .DMCinfo.CellOverVolt cellOverVolt = 3;
  if (this->_internal_has_cellovervolt()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *cellovervolt_);
  }

  // .DMCinfo.PackOverVolt packOverVolt = 4;
  if (this->_internal_has_packovervolt()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *packovervolt_);
  }

  // .DMCinfo.OverCurrent overCurrent = 5;
  if (this->_internal_has_overcurrent()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *overcurrent_);
  }

  // .DMCinfo.CellOverTemp cellOverTemp = 6;
  if (this->_internal_has_cellovertemp()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *cellovertemp_);
  }

  // .DMCinfo.LowTemp lowTemp = 7;
  if (this->_internal_has_lowtemp()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *lowtemp_);
  }

  // .DMCinfo.BMSRlyStick bmsRlyStick = 8;
  if (this->_internal_has_bmsrlystick()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bmsrlystick_);
  }

  // .DMCinfo.BMSRlyOC bmsRlyOC = 9;
  if (this->_internal_has_bmsrlyoc()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bmsrlyoc_);
  }

  // .DMCinfo.OverCharge overCharge = 10;
  if (this->_internal_has_overcharge()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *overcharge_);
  }

  // .DMCinfo.BMSDataRepeat bmsDataRepeat = 11;
  if (this->_internal_has_bmsdatarepeat()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bmsdatarepeat_);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ActiveProtectParam::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ActiveProtectParam::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ActiveProtectParam::GetClassData() const { return &_class_data_; }

void ActiveProtectParam::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<ActiveProtectParam *>(to)->MergeFrom(
      static_cast<const ActiveProtectParam &>(from));
}


void ActiveProtectParam::MergeFrom(const ActiveProtectParam& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DMCinfo.ActiveProtectParam)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_currbalance()) {
    _internal_mutable_currbalance()->::DMCinfo::CurrentBalance::MergeFrom(from._internal_currbalance());
  }
  if (from._internal_has_hotrunaway()) {
    _internal_mutable_hotrunaway()->::DMCinfo::HotRunaway::MergeFrom(from._internal_hotrunaway());
  }
  if (from._internal_has_cellovervolt()) {
    _internal_mutable_cellovervolt()->::DMCinfo::CellOverVolt::MergeFrom(from._internal_cellovervolt());
  }
  if (from._internal_has_packovervolt()) {
    _internal_mutable_packovervolt()->::DMCinfo::PackOverVolt::MergeFrom(from._internal_packovervolt());
  }
  if (from._internal_has_overcurrent()) {
    _internal_mutable_overcurrent()->::DMCinfo::OverCurrent::MergeFrom(from._internal_overcurrent());
  }
  if (from._internal_has_cellovertemp()) {
    _internal_mutable_cellovertemp()->::DMCinfo::CellOverTemp::MergeFrom(from._internal_cellovertemp());
  }
  if (from._internal_has_lowtemp()) {
    _internal_mutable_lowtemp()->::DMCinfo::LowTemp::MergeFrom(from._internal_lowtemp());
  }
  if (from._internal_has_bmsrlystick()) {
    _internal_mutable_bmsrlystick()->::DMCinfo::BMSRlyStick::MergeFrom(from._internal_bmsrlystick());
  }
  if (from._internal_has_bmsrlyoc()) {
    _internal_mutable_bmsrlyoc()->::DMCinfo::BMSRlyOC::MergeFrom(from._internal_bmsrlyoc());
  }
  if (from._internal_has_overcharge()) {
    _internal_mutable_overcharge()->::DMCinfo::OverCharge::MergeFrom(from._internal_overcharge());
  }
  if (from._internal_has_bmsdatarepeat()) {
    _internal_mutable_bmsdatarepeat()->::DMCinfo::BMSDataRepeat::MergeFrom(from._internal_bmsdatarepeat());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ActiveProtectParam::CopyFrom(const ActiveProtectParam& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DMCinfo.ActiveProtectParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ActiveProtectParam::IsInitialized() const {
  return true;
}

void ActiveProtectParam::InternalSwap(ActiveProtectParam* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ActiveProtectParam, bmsdatarepeat_)
      + sizeof(ActiveProtectParam::bmsdatarepeat_)
      - PROTOBUF_FIELD_OFFSET(ActiveProtectParam, currbalance_)>(
          reinterpret_cast<char*>(&currbalance_),
          reinterpret_cast<char*>(&other->currbalance_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ActiveProtectParam::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fDMC_5fINFO_2eproto_getter, &descriptor_table_GCU_5fDMC_5fINFO_2eproto_once,
      file_level_metadata_GCU_5fDMC_5fINFO_2eproto[22]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace DMCinfo
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::DMCinfo::PUBInfo* Arena::CreateMaybeMessage< ::DMCinfo::PUBInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::PUBInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::DMCinfo::VCIInfo* Arena::CreateMaybeMessage< ::DMCinfo::VCIInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::VCIInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::DMCinfo::GUNInfo* Arena::CreateMaybeMessage< ::DMCinfo::GUNInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::GUNInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::DMCinfo::ADModuleInfo* Arena::CreateMaybeMessage< ::DMCinfo::ADModuleInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::ADModuleInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::DMCinfo::PMMInfo* Arena::CreateMaybeMessage< ::DMCinfo::PMMInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::PMMInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::DMCinfo::ServerInfo* Arena::CreateMaybeMessage< ::DMCinfo::ServerInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::ServerInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::DMCinfo::OHPInfo* Arena::CreateMaybeMessage< ::DMCinfo::OHPInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::OHPInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::DMCinfo::HmcChargingStrategyM* Arena::CreateMaybeMessage< ::DMCinfo::HmcChargingStrategyM >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::HmcChargingStrategyM >(arena);
}
template<> PROTOBUF_NOINLINE ::DMCinfo::HMCInfo* Arena::CreateMaybeMessage< ::DMCinfo::HMCInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::HMCInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::DMCinfo::GunLoadConstraint* Arena::CreateMaybeMessage< ::DMCinfo::GunLoadConstraint >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::GunLoadConstraint >(arena);
}
template<> PROTOBUF_NOINLINE ::DMCinfo::GunMatchInfo* Arena::CreateMaybeMessage< ::DMCinfo::GunMatchInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::GunMatchInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::DMCinfo::CurrentBalance* Arena::CreateMaybeMessage< ::DMCinfo::CurrentBalance >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::CurrentBalance >(arena);
}
template<> PROTOBUF_NOINLINE ::DMCinfo::HotRunaway* Arena::CreateMaybeMessage< ::DMCinfo::HotRunaway >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::HotRunaway >(arena);
}
template<> PROTOBUF_NOINLINE ::DMCinfo::CellOverVolt* Arena::CreateMaybeMessage< ::DMCinfo::CellOverVolt >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::CellOverVolt >(arena);
}
template<> PROTOBUF_NOINLINE ::DMCinfo::PackOverVolt* Arena::CreateMaybeMessage< ::DMCinfo::PackOverVolt >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::PackOverVolt >(arena);
}
template<> PROTOBUF_NOINLINE ::DMCinfo::OverCurrent* Arena::CreateMaybeMessage< ::DMCinfo::OverCurrent >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::OverCurrent >(arena);
}
template<> PROTOBUF_NOINLINE ::DMCinfo::CellOverTemp* Arena::CreateMaybeMessage< ::DMCinfo::CellOverTemp >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::CellOverTemp >(arena);
}
template<> PROTOBUF_NOINLINE ::DMCinfo::LowTemp* Arena::CreateMaybeMessage< ::DMCinfo::LowTemp >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::LowTemp >(arena);
}
template<> PROTOBUF_NOINLINE ::DMCinfo::BMSRlyStick* Arena::CreateMaybeMessage< ::DMCinfo::BMSRlyStick >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::BMSRlyStick >(arena);
}
template<> PROTOBUF_NOINLINE ::DMCinfo::BMSRlyOC* Arena::CreateMaybeMessage< ::DMCinfo::BMSRlyOC >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::BMSRlyOC >(arena);
}
template<> PROTOBUF_NOINLINE ::DMCinfo::OverCharge* Arena::CreateMaybeMessage< ::DMCinfo::OverCharge >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::OverCharge >(arena);
}
template<> PROTOBUF_NOINLINE ::DMCinfo::BMSDataRepeat* Arena::CreateMaybeMessage< ::DMCinfo::BMSDataRepeat >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::BMSDataRepeat >(arena);
}
template<> PROTOBUF_NOINLINE ::DMCinfo::ActiveProtectParam* Arena::CreateMaybeMessage< ::DMCinfo::ActiveProtectParam >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DMCinfo::ActiveProtectParam >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
