// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GCU_AllFaultEnum.proto

#include "GCU_AllFaultEnum.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace AllFaultEnum {
constexpr VCIFaultState::VCIFaultState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : faulttype_(0)

  , faultstate_(0)

  , faultraisetime_(uint64_t{0u})
  , faultdowntime_(uint64_t{0u})
  , shutdowntype_(0u)
  , showtype_(0u){}
struct VCIFaultStateDefaultTypeInternal {
  constexpr VCIFaultStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~VCIFaultStateDefaultTypeInternal() {}
  union {
    VCIFaultState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT VCIFaultStateDefaultTypeInternal _VCIFaultState_default_instance_;
constexpr PMMFaultState::PMMFaultState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : faulttype_(0)

  , faultstate_(0)

  , faultraisetime_(uint64_t{0u})
  , faultdowntime_(uint64_t{0u})
  , shutdowntype_(0u)
  , showtype_(0u){}
struct PMMFaultStateDefaultTypeInternal {
  constexpr PMMFaultStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PMMFaultStateDefaultTypeInternal() {}
  union {
    PMMFaultState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PMMFaultStateDefaultTypeInternal _PMMFaultState_default_instance_;
constexpr OHPFaultState::OHPFaultState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : faulttype_(0)

  , faultstate_(0)

  , faultraisetime_(uint64_t{0u})
  , faultdowntime_(uint64_t{0u})
  , shutdowntype_(0u)
  , showtype_(0u){}
struct OHPFaultStateDefaultTypeInternal {
  constexpr OHPFaultStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~OHPFaultStateDefaultTypeInternal() {}
  union {
    OHPFaultState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT OHPFaultStateDefaultTypeInternal _OHPFaultState_default_instance_;
constexpr DMCFaultState::DMCFaultState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : faulttype_(0)

  , faultstate_(0)

  , faultraisetime_(uint64_t{0u})
  , faultdowntime_(uint64_t{0u})
  , shutdowntype_(0u)
  , showtype_(0u){}
struct DMCFaultStateDefaultTypeInternal {
  constexpr DMCFaultStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DMCFaultStateDefaultTypeInternal() {}
  union {
    DMCFaultState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DMCFaultStateDefaultTypeInternal _DMCFaultState_default_instance_;
constexpr MatrixStatus::MatrixStatus(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : matrixcontactorid_(0u)
  , alarmanslist_(0)

  , alarmattr_(0u)
  , realfaultcontid_(0u){}
struct MatrixStatusDefaultTypeInternal {
  constexpr MatrixStatusDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MatrixStatusDefaultTypeInternal() {}
  union {
    MatrixStatus _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MatrixStatusDefaultTypeInternal _MatrixStatus_default_instance_;
constexpr ADModuleAlarm::ADModuleAlarm(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : admodulelist_()
  , admoduleid_(0u){}
struct ADModuleAlarmDefaultTypeInternal {
  constexpr ADModuleAlarmDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ADModuleAlarmDefaultTypeInternal() {}
  union {
    ADModuleAlarm _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ADModuleAlarmDefaultTypeInternal _ADModuleAlarm_default_instance_;
constexpr VCIContactorFaultSend::VCIContactorFaultSend(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : vcifault_()
  , gunnum_(0u){}
struct VCIContactorFaultSendDefaultTypeInternal {
  constexpr VCIContactorFaultSendDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~VCIContactorFaultSendDefaultTypeInternal() {}
  union {
    VCIContactorFaultSend _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT VCIContactorFaultSendDefaultTypeInternal _VCIContactorFaultSend_default_instance_;
constexpr PMMContactorFaultSend::PMMContactorFaultSend(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : pmmfault_()
  , gunnum_(0u){}
struct PMMContactorFaultSendDefaultTypeInternal {
  constexpr PMMContactorFaultSendDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PMMContactorFaultSendDefaultTypeInternal() {}
  union {
    PMMContactorFaultSend _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PMMContactorFaultSendDefaultTypeInternal _PMMContactorFaultSend_default_instance_;
constexpr OHPContactorFaultSend::OHPContactorFaultSend(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ohpfault_()
  , gunnum_(0u){}
struct OHPContactorFaultSendDefaultTypeInternal {
  constexpr OHPContactorFaultSendDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~OHPContactorFaultSendDefaultTypeInternal() {}
  union {
    OHPContactorFaultSend _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT OHPContactorFaultSendDefaultTypeInternal _OHPContactorFaultSend_default_instance_;
constexpr DMCContactorFaultSend::DMCContactorFaultSend(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : dmcfault_()
  , gunnum_(0u){}
struct DMCContactorFaultSendDefaultTypeInternal {
  constexpr DMCContactorFaultSendDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DMCContactorFaultSendDefaultTypeInternal() {}
  union {
    DMCContactorFaultSend _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DMCContactorFaultSendDefaultTypeInternal _DMCContactorFaultSend_default_instance_;
}  // namespace AllFaultEnum
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_GCU_5fAllFaultEnum_2eproto[10];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_GCU_5fAllFaultEnum_2eproto[5];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_GCU_5fAllFaultEnum_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_GCU_5fAllFaultEnum_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::VCIFaultState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::VCIFaultState, faulttype_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::VCIFaultState, faultstate_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::VCIFaultState, faultraisetime_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::VCIFaultState, faultdowntime_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::VCIFaultState, shutdowntype_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::VCIFaultState, showtype_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::PMMFaultState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::PMMFaultState, faulttype_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::PMMFaultState, faultstate_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::PMMFaultState, faultraisetime_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::PMMFaultState, faultdowntime_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::PMMFaultState, shutdowntype_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::PMMFaultState, showtype_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::OHPFaultState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::OHPFaultState, faulttype_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::OHPFaultState, faultstate_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::OHPFaultState, faultraisetime_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::OHPFaultState, faultdowntime_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::OHPFaultState, shutdowntype_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::OHPFaultState, showtype_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::DMCFaultState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::DMCFaultState, faulttype_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::DMCFaultState, faultstate_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::DMCFaultState, faultraisetime_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::DMCFaultState, faultdowntime_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::DMCFaultState, shutdowntype_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::DMCFaultState, showtype_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::MatrixStatus, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::MatrixStatus, matrixcontactorid_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::MatrixStatus, alarmanslist_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::MatrixStatus, alarmattr_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::MatrixStatus, realfaultcontid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::ADModuleAlarm, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::ADModuleAlarm, admoduleid_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::ADModuleAlarm, admodulelist_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::VCIContactorFaultSend, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::VCIContactorFaultSend, gunnum_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::VCIContactorFaultSend, vcifault_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::PMMContactorFaultSend, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::PMMContactorFaultSend, gunnum_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::PMMContactorFaultSend, pmmfault_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::OHPContactorFaultSend, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::OHPContactorFaultSend, gunnum_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::OHPContactorFaultSend, ohpfault_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::DMCContactorFaultSend, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::DMCContactorFaultSend, gunnum_),
  PROTOBUF_FIELD_OFFSET(::AllFaultEnum::DMCContactorFaultSend, dmcfault_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::AllFaultEnum::VCIFaultState)},
  { 11, -1, sizeof(::AllFaultEnum::PMMFaultState)},
  { 22, -1, sizeof(::AllFaultEnum::OHPFaultState)},
  { 33, -1, sizeof(::AllFaultEnum::DMCFaultState)},
  { 44, -1, sizeof(::AllFaultEnum::MatrixStatus)},
  { 53, -1, sizeof(::AllFaultEnum::ADModuleAlarm)},
  { 60, -1, sizeof(::AllFaultEnum::VCIContactorFaultSend)},
  { 67, -1, sizeof(::AllFaultEnum::PMMContactorFaultSend)},
  { 74, -1, sizeof(::AllFaultEnum::OHPContactorFaultSend)},
  { 81, -1, sizeof(::AllFaultEnum::DMCContactorFaultSend)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::AllFaultEnum::_VCIFaultState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::AllFaultEnum::_PMMFaultState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::AllFaultEnum::_OHPFaultState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::AllFaultEnum::_DMCFaultState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::AllFaultEnum::_MatrixStatus_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::AllFaultEnum::_ADModuleAlarm_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::AllFaultEnum::_VCIContactorFaultSend_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::AllFaultEnum::_PMMContactorFaultSend_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::AllFaultEnum::_OHPContactorFaultSend_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::AllFaultEnum::_DMCContactorFaultSend_default_instance_),
};

const char descriptor_table_protodef_GCU_5fAllFaultEnum_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\026GCU_AllFaultEnum.proto\022\014AllFaultEnum\"\307"
  "\001\n\rVCIFaultState\022-\n\tfaultType\030\001 \001(\0162\032.Al"
  "lFaultEnum.VCIFaultEnum\0220\n\nfaultState\030\002 "
  "\001(\0162\034.AllFaultEnum.AlarmStateEnum\022\026\n\016fau"
  "ltRaiseTime\030\003 \001(\004\022\025\n\rfaultDownTime\030\004 \001(\004"
  "\022\024\n\014ShutDownType\030\005 \001(\r\022\020\n\010ShowType\030\006 \001(\r"
  "\"\307\001\n\rPMMFaultState\022-\n\tfaultType\030\001 \001(\0162\032."
  "AllFaultEnum.PMMFaultEnum\0220\n\nfaultState\030"
  "\002 \001(\0162\034.AllFaultEnum.AlarmStateEnum\022\026\n\016f"
  "aultRaiseTime\030\003 \001(\004\022\025\n\rfaultDownTime\030\004 \001"
  "(\004\022\024\n\014ShutDownType\030\005 \001(\r\022\020\n\010ShowType\030\006 \001"
  "(\r\"\307\001\n\rOHPFaultState\022-\n\tfaultType\030\001 \001(\0162"
  "\032.AllFaultEnum.OHPFaultEnum\0220\n\nfaultStat"
  "e\030\002 \001(\0162\034.AllFaultEnum.AlarmStateEnum\022\026\n"
  "\016faultRaiseTime\030\003 \001(\004\022\025\n\rfaultDownTime\030\004"
  " \001(\004\022\024\n\014ShutDownType\030\005 \001(\r\022\020\n\010ShowType\030\006"
  " \001(\r\"\307\001\n\rDMCFaultState\022-\n\tfaultType\030\001 \001("
  "\0162\032.AllFaultEnum.DMCFaultEnum\0220\n\nfaultSt"
  "ate\030\002 \001(\0162\034.AllFaultEnum.AlarmStateEnum\022"
  "\026\n\016faultRaiseTime\030\003 \001(\004\022\025\n\rfaultDownTime"
  "\030\004 \001(\004\022\024\n\014ShutDownType\030\005 \001(\r\022\020\n\010ShowType"
  "\030\006 \001(\r\"\207\001\n\014MatrixStatus\022\031\n\021MatrixContact"
  "orID\030\001 \001(\r\0220\n\014AlarmAnsList\030\002 \001(\0162\032.AllFa"
  "ultEnum.PMMFaultEnum\022\021\n\tAlarmAttr\030\003 \001(\r\022"
  "\027\n\017realFaultContID\030\004 \001(\r\"V\n\rADModuleAlar"
  "m\022\022\n\nADModuleID\030\001 \001(\r\0221\n\014ADModuleList\030\002 "
  "\003(\0132\033.AllFaultEnum.PMMFaultState\"V\n\025VCIC"
  "ontactorFaultSend\022\016\n\006gunNum\030\001 \001(\r\022-\n\010VCI"
  "Fault\030\002 \003(\0132\033.AllFaultEnum.VCIFaultState"
  "\"V\n\025PMMContactorFaultSend\022\016\n\006gunNum\030\001 \001("
  "\r\022-\n\010PMMFault\030\002 \003(\0132\033.AllFaultEnum.PMMFa"
  "ultState\"V\n\025OHPContactorFaultSend\022\016\n\006gun"
  "Num\030\001 \001(\r\022-\n\010OHPFault\030\002 \003(\0132\033.AllFaultEn"
  "um.OHPFaultState\"V\n\025DMCContactorFaultSen"
  "d\022\016\n\006gunNum\030\001 \001(\r\022-\n\010DMCFault\030\002 \003(\0132\033.Al"
  "lFaultEnum.DMCFaultState*\363$\n\014VCIFaultEnu"
  "m\022\023\n\017DefaultVCIFault\020\000\022\023\n\017gunShortCircui"
  "t\020\001\022\022\n\016BMSCellVolOver\020\002\022\022\n\016BMSPackVolOve"
  "r\020\003\022\023\n\017BMSPackCurrOver\020\004\022\023\n\017BMSCellTempO"
  "ver\020\005\022\025\n\021BMSTempOutControl\020\006\022\024\n\020BMSRelay"
  "Adhesion\020\007\022\021\n\rBMSOverCharge\020\010\022\027\n\023BMSAuxP"
  "owerAbnormal\020\t\022\034\n\030BMSInnerRelayOpenCircu"
  "it\020\n\022\031\n\025BMSDemandCurrAbnormal\020\013\022\030\n\024BMSDe"
  "mandVolAbnormal\020\014\022\025\n\021BMSBatVolAbnormal\020\r"
  "\022\031\n\025BMSDataUpdateABNORMAL\020\016\022\026\n\022BMSBCSCur"
  "rABNORMAL\020\017\022\022\n\016ServiceOffline\020\020\022\023\n\017CC1Vo"
  "ltAbnormal\020\021\022\024\n\020GunStateAbnormal\020\022\022\021\n\rEl"
  "ockAbnormal\020\023\022\025\n\021GunReserveConnect\020\024\022\024\n\020"
  "AuxPowerAbnormal\020\025\022\021\n\rInsultVolHigh\020\026\022\020\n"
  "\014InsultVolLow\020\027\022\016\n\nInsultWarn\020\030\022\022\n\016Insul"
  "tAbnormal\020\031\022\016\n\nDisChgFail\020\032\022\020\n\014StartTime"
  "out\020\033\022\022\n\016BatAllowVolLow\020\034\022\021\n\rStartVoltHi"
  "gh\020\035\022\017\n\013ElockUnlock\020\036\022\016\n\nBatVolHigh\020 \022\r\n"
  "\tBatVolLow\020!\022\025\n\021BatVolDiffFromBCP\020\"\022\021\n\rP"
  "reChgVolHigh\020#\022\020\n\014PreChgVolLow\020$\022\022\n\016Fuse"
  "BreakFault\020%\022\017\n\013OutOverCurr\020&\022\016\n\nOutOver"
  "Vol\020\'\022\025\n\021MeterCurrAbnormal\020(\022\024\n\020MeterVol"
  "Abnormal\020)\022\017\n\013GunTempWarn\020*\022\017\n\013GunOverTe"
  "mp\020+\022\020\n\014NoActiveCurr\020,\022\013\n\007SOCFull\020-\022\024\n\020C"
  "hgForbidTimeout\020.\022\022\n\016ChgSysAbnormal\020/\022\017\n"
  "\013BCSCurrDiff\0200\022\016\n\nBCSVolDiff\0201\022\020\n\014GunNot"
  "AtHome\0202\022\017\n\013GunLifeWarn\0203\022\023\n\017VINStartTim"
  "eOut\0204\022\030\n\024BATVOLsimpleABNORMAL\0205\022\017\n\013OutO"
  "verVOLT\0206\022\016\n\nOutLowVOLT\0207\022\017\n\013OutCurrHIGH"
  "\0208\022\026\n\022GunTempSensorFault\0209\022\023\n\017BmsDemandV"
  "olLow\020:\022\022\n\016InnerCommFault\020;\022\023\n\017ElockErrD"
  "rawGun\020<\022\017\n\013UserDrawGun\020=\022\016\n\nBSMTimeout\020"
  ">\022\023\n\017BSTParaMisMatch\020\?\022\026\n\022BMSProtoVerUnM"
  "atch\020@\022\016\n\nBRMTimeout\020A\022\017\n\013BRMAbnormal\020B\022"
  "\016\n\nBCPTimeout\020C\022\017\n\013BCPAbnormal\020D\022\016\n\nBROT"
  "imeout\020E\022\020\n\014BROaaTimeout\020F\022\016\n\nBCLTimeout"
  "\020G\022\016\n\nBCSTimeout\020H\022\021\n\rBSMCellVolLow\020I\022\016\n"
  "\nBSMSocHigh\020J\022\r\n\tBSMSocLow\020K\022\023\n\017BSMPackO"
  "verCurr\020L\022\022\n\016BSMBatTempHigh\020M\022\025\n\021BSMInsu"
  "ltAbnormal\020N\022\030\n\024BSMContactorAbnormal\020O\022\022"
  "\n\016BSMCellVolHigh\020P\022\016\n\nBSTTimeout\020Q\022\016\n\nBS"
  "TMeetSOC\020R\022\016\n\nBSTMeetVol\020S\022\022\n\016BSTMeetCel"
  "lvol\020T\022\022\n\016BSTInsultFault\020U\022\033\n\027BSTOutConn"
  "ectorOverTemp\020V\022\031\n\025BSTCommponentOverTemp"
  "\020W\022\033\n\027BSTChgConnectorAbnormal\020X\022\022\n\016BSTBa"
  "tOverTemp\020Y\022\030\n\024BSTHighVolRelayFault\020Z\022\017\n"
  "\013BSTcc2Fault\020[\022\021\n\rBSTOtherFault\020\\\022\017\n\013BST"
  "OverCurr\020]\022\022\n\016BSTVolAbnormal\020^\022\017\n\013BSTNoR"
  "eason\020_\022\016\n\nBSDTimeout\020`\022\016\n\nDoorSensor\020a\022"
  "\022\n\016EmergencyStopV\020b\022\026\n\022ContactorOpenFaul"
  "t\020c\022\027\n\023ContactorCloseFault\020d\022\014\n\010FANerror"
  "\020e\022\020\n\014PMMlockFault\020f\022\025\n\021ContactorPEDrive"
  "r\020g\022\025\n\021ContactorNEDriver\020h\022\024\n\020ContactorP"
  "EStick\020i\022\024\n\020ContactorNEStick\020j\022\020\n\014CanSen"
  "dFault\020k\022\021\n\rCanBufferFull\020l\022\017\n\013BMSNotRep"
  "ly\020m\022\020\n\014PMMnoADmodle\020n\022\022\n\016BMSDemandLow3A"
  "\020o\022\021\n\rInsultLow700k\020p\022\014\n\010ADFixing\020q\022\013\n\007A"
  "DnoFix\020r\022\022\n\016MccbBreakFault\020s\022\026\n\022PreCharg"
  "eBatVolLow\020t\022\021\n\rBCSCurrBigBCL\020v\022\016\n\nBCSCu"
  "rrLow\020w\022\017\n\013BCSCurrHigh\020x\022\030\n\024CooperDiffer"
  "Terminal\020y\022\023\n\017CooperStepStart\020z\022\017\n\013SOCHi"
  "ghStop\020{\022\017\n\013BMSstateErr\020|\022\016\n\nBSTNullErr\020"
  "}\022\025\n\021BSTotherFaultStop\020~\022\021\n\rInsultSampEr"
  "r\020\177\022\n\n\005Smoke\020\200\001\022\n\n\005Water\020\201\001\022\017\n\nToppleFal"
  "l\020\202\001\022\016\n\tLightning\020\203\001\022\016\n\tDustproof\020\204\001\022\024\n\017"
  "CooperGunNumErr\020\205\001\022\022\n\rCurrOffsetErr\020\206\001\022\023"
  "\n\016BCSCurrOverBCP\020\207\001\022\020\n\013LC_OverVolt\020\220\001\022\017\n"
  "\nLC_LowVolt\020\221\001\022\026\n\021LC_LowLiquidLevel\020\222\001\022\024"
  "\n\017LC_PumpOverCurr\020\223\001\022\024\n\017LC_PumpLowPress\020"
  "\224\001\022\025\n\020LC_PumpOverPress\020\225\001\022\024\n\017LC_PumpOver"
  "Temp\020\226\001\022\026\n\021LC_PumpLockDrotor\020\227\001\022\023\n\016LC_Pu"
  "mpCommErr\020\230\001\022\017\n\nLC_LowFlow\020\231\001\022\020\n\013LC_High"
  "Flow\020\232\001\022\023\n\016LC_FanOverCurr\020\233\001\022\023\n\016LC_DevCo"
  "mmuErr\020\234\001\022\023\n\016LC_ParamSetErr\020\235\001\022\030\n\023CtAppo"
  "intPlanChange\020\240\001\022\031\n\024CtAppointTimeNotCome"
  "\020\241\001\022\021\n\014CtChgLeakage\020\242\001\022\021\n\014CtConnectErr\020\243"
  "\001\022\024\n\017CtVehiElockOpen\020\244\001\022\021\n\014CtVehiS2Open\020"
  "\245\001\022\033\n\026CtChgFuncConferTimeout\020\246\001\022\033\n\026CtChg"
  "ParaConfigTimeout\020\247\001\022\027\n\022CtChgAuthenTimeo"
  "ut\020\250\001\022\030\n\023CtChgAppointTimeout\020\251\001\022\032\n\025CtChg"
  "SelfCheckTimeout\020\252\001\022\034\n\027CtChgPowerSupplyT"
  "imeout\020\253\001\022\027\n\022CtChgPreChgTimeout\020\254\001\022\034\n\027Ct"
  "ChgEnergyTransTimeout\020\255\001\022\024\n\017CtChgEndTime"
  "out\020\256\001\022\027\n\022CtBmsNecessFunLack\020\257\001\022\027\n\022CtBms"
  "AuthenFunLack\020\260\001\022\025\n\020CtBmsParaNoMatch\020\261\001\022"
  "\024\n\017CtChgAuthenFail\020\262\001\022\031\n\024CtBmsNotAllowAp"
  "point\020\263\001\022\024\n\017CtBmsWakeUpFail\020\264\001\022\023\n\016CtPaus"
  "eTimeout\020\265\001\022\023\n\016CtPauseCntOver\020\266\001\022\022\n\rCtPa"
  "useRepeat\020\267\001\022\021\n\014BtNormalStop\020\300\001\022\017\n\nBtUse"
  "rStop\020\301\001\022\030\n\023BtAppointPlanChange\020\302\001\022\021\n\014Bt"
  "InsultFail\020\303\001\022\021\n\014BtConnectErr\020\304\001\022\r\n\010BtPE"
  "Open\020\305\001\022\r\n\010BtCC2Err\020\306\001\022\r\n\010BtCC3Err\020\307\001\022\017\n"
  "\nBtElockErr\020\310\001\022\020\n\013BtFaultStop\020\311\001\022\024\n\017BtEm"
  "ergencyStop\020\312\001\022\021\n\014BtS1OpenStop\020\313\001\022\022\n\rBtC"
  "hargerStop\020\314\001\022\033\n\026BtChgFuncConferTimeout\020"
  "\315\001\022\033\n\026BtChgParaConfigTimeout\020\316\001\022\027\n\022BtChg"
  "AuthenTimeout\020\317\001\022\030\n\023BtChgAppointTimeout\020"
  "\320\001\022\032\n\025BtChgSelfCheckTimeout\020\321\001\022\034\n\027BtChgP"
  "owerSupplyTimeout\020\322\001\022\027\n\022BtChgPreChgTimeo"
  "ut\020\323\001\022\034\n\027BtChgEnergyTransTimeout\020\324\001\022\024\n\017B"
  "tChgEndTimeout\020\325\001\022\024\n\017BtNecessFunLack\020\326\001\022"
  "\024\n\017BtFunConferFail\020\327\001\022\022\n\rBtParaNoMatch\020\330"
  "\001\022\021\n\014BtAuthenFail\020\331\001\022\026\n\021BtNotAllowAppoin"
  "t\020\332\001\022\021\n\014BtWakeUpFail\020\333\001\022\024\n\017BtSupplyVolHi"
  "gh\020\334\001\022\025\n\020BtSupplyCurrHigh\020\335\001\022\023\n\016BtSupply"
  "VolErr\020\336\001\022\024\n\017BtSupplyCurrErr\020\337\001\022\032\n\025BtSup"
  "plyModSwitchFail\020\340\001\022\023\n\016BtTransVolHigh\020\341\001"
  "\022\024\n\017BtTransCurrHigh\020\342\001\022\022\n\rBtTransVolErr\020"
  "\343\001\022\023\n\016BtTransCurrErr\020\344\001\022\022\n\rBtGunOverTemp"
  "\020\345\001\022\026\n\021BtChargerStopFail\020\346\001\022\023\n\016BtPauseTi"
  "meout\020\347\001\022\023\n\016BtPauseCntOver\020\350\001\022\022\n\rBtPause"
  "Repeat\020\351\001\022\026\n\021BtStageEnsureFail\020\352\001\022\032\n\025BtS"
  "tageRequestTimeout\020\353\001\022\030\n\023StageConfirmTim"
  "eout\020\354\001\022\022\n\rEvStopTimeout\020\355\001\022\024\n\017EvWakeupT"
  "imeout\020\356\001\022\025\n\020FunConferTimeout\020\357\001\022\024\n\017EvCo"
  "nfigTimeout\020\360\001\022\024\n\017EvAuthenTimeout\020\361\001\022\030\n\023"
  "EvAppointMsgTimeout\020\362\001\022\033\n\026EvAppointConfe"
  "rTimeout\020\363\001\022\021\n\014EvBCLTimeout\020\364\001\022\023\n\016EvPaus"
  "eTimeout\020\365\001\022\021\n\014EvBSDTimeout\020\366\001*\266\013\n\014PMMFa"
  "ultEnum\022\023\n\017DefaultPMMFault\020\000\022\021\n\rHvdcStar"
  "tFail\020\001\022\021\n\rHvdcGroupFail\020\002\022\017\n\013HvdcOfflin"
  "e\020\003\022\020\n\014HvdcStopFail\020\004\022\017\n\013OutOverVolt\020\020\022\014"
  "\n\010OverTemp\020\021\022\014\n\010FanFault\020\022\022\017\n\013EEPROMFaul"
  "t\020\023\022\n\n\006CANErr\020\024\022\r\n\tACLowVolt\020\025\022\017\n\013ACLack"
  "Phase\020\026\022\022\n\016SerUnblncdCurr\020\027\022\014\n\010IDRepeat\020"
  "\030\022\016\n\nACOverVolt\020\031\022\016\n\nPFCProtect\020\032\022\025\n\021Sli"
  "ghtUnblncdCurr\020\033\022\017\n\013DischgAlarm\020\034\022\013\n\007SCF"
  "ault\020\035\022\021\n\rInnerComAlarm\020\036\022\022\n\016ElectGridAb"
  "nor\020 \022\021\n\rModRelayFault\020!\022\022\n\016OutCaplifeWa"
  "rn\020\"\022\t\n\005ACCut\020#\022\t\n\005DCOCP\020$\022\024\n\020Boardcorre"
  "ctFail\020%\022\021\n\rDiodeTempWarn\020&\022\017\n\013MOSTempWa"
  "rn\020\'\022\017\n\013PFCTempWarn\020(\022\014\n\010NoChgMod\0201\022\020\n\014M"
  "odStartFail\0202\022\023\n\017OutShortCircuit\0203\022\023\n\017Co"
  "ntactPosStick\0204\022\023\n\017ContactNegStick\0205\022\025\n\021"
  "ContactPosDiscnct\0206\022\025\n\021ContactNegDiscnct"
  "\0207\022\023\n\017InputACOverVolt\0208\022\022\n\016InputACLowVol"
  "t\0209\022\024\n\020InputACLakePhase\020:\022\035\n\031ArrayContac"
  "torCmdAbnormal\020;\022\033\n\027OutContactorCmdAbnor"
  "mal\020<\022\027\n\023ArrayContactorStick\020=\022\031\n\025ArrayC"
  "ontactorDiscnct\020>\022\022\n\016EmergencyStopP\020@\022\017\n"
  "\013GateMagnetP\020A\022\022\n\016ADModuleHybrid\020B\022\r\n\tSP"
  "DFaultP\020C\022\020\n\014SmokeSensorP\020D\022\020\n\014WaterSens"
  "orP\020E\022\017\n\013TiltSensorP\020F\022\032\n\026InContactorCmd"
  "Abnormal\020G\022\024\n\020ACContactorStick\020H\022\026\n\022ACCo"
  "ntactorDiscnct\020I\022\027\n\023Systerm_DC_FanFault\020"
  "J\022\027\n\023Systerm_AC_FanFault\020K\022\027\n\023Systerm_Me"
  "ter_Fault\020L\022\016\n\nPDUOffline\020M\022\033\n\027LinkArray"
  "ContactorStick\020N\022\023\n\017Gun_NoChgModule\020O\022\022\n"
  "\016HvdcAllMissing\020P\022\022\n\016HvdcOutOfRange\020Q\022\023\n"
  "\017HvdcAddrTrample\020R\022\017\n\013HvdcAddrErr\020S\022\020\n\014H"
  "vdcAddrNull\020T\022\026\n\022SystermConfigFault\020U\022\017\n"
  "\013MatrixFault\020V\022\037\n\033GunLinkModulePowerOffF"
  "ailed\020W\022\024\n\020SocketVciOffline\020X\022\024\n\020SocketO"
  "hpOffline\020Y\022\024\n\020SocketDmcOffline\020Z\022\024\n\020Soc"
  "ketFsmOffline\020[\022\024\n\020DustproofBlocked\020`\022\020\n"
  "\013PMMFaultMax\020\377\001*\306\013\n\014OHPFaultEnum\022\023\n\017Defa"
  "ultOHPFault\020\000\022\014\n\010LTEFault\020\001\022\r\n\tWiFiFault"
  "\020\002\022\r\n\tGmacFault\020\003\022\014\n\010NetFault\020\004\022\030\n\024Settl"
  "ementCloudFault\020\005\022\027\n\023SettlementCloudStop"
  "\020\006\022\022\n\016StartUpTimeOut\020\020\022\036\n\032PeriodicCommun"
  "icationFault\020\021\022\030\n\024OrderSettlementFault\020\022"
  "\022\024\n\020OrderVinTimeStop\020\023\022\025\n\021SerialNumberFa"
  "ult\020 \022\r\n\tUUIDFault\020!\022\r\n\tFRAMFault\020\"\022\023\n\017M"
  "eterPowerFault\020#\022\022\n\016MeterCurrFault\020$\022\020\n\014"
  "MeterOffLine\020%\022\021\n\rMeterVolFault\020&\022\025\n\021Met"
  "erDevStartFail\020\'\022\013\n\007DBFault\020(\022\031\n\025MeterPo"
  "werForwardJump\020)\022\031\n\025MeterPowerReverseJum"
  "p\020*\022\016\n\nGateMagnet\0200\022\021\n\rEmergencyStop\0201\022\014"
  "\n\010SPDFault\0202\022\017\n\013SysFanFault\0203\022\026\n\022SysOver"
  "Temperature\0204\022\027\n\023CtrlOverTemperature\0205\022\017"
  "\n\013SmokeSensor\0206\022\017\n\013WaterSensor\0207\022\016\n\nTilt"
  "Sensor\0208\022\016\n\nHMIOffLine\0209\022\016\n\nSOCOffLine\020:"
  "\022\016\n\nNFCOffLine\020;\022\020\n\014SpeakerFault\020<\022\021\n\rMi"
  "cphoneFault\020=\022\017\n\013CameraFault\020>\022\016\n\nLPROff"
  "line\020\?\022\020\n\014FSMnoService\020@\022\020\n\014VCInoService"
  "\020A\022\020\n\014PMMnoService\020B\022\020\n\014LCRnoService\020C\022\020"
  "\n\014DMCnoService\020D\022\020\n\014LOSnoService\020E\022\020\n\014Os"
  "cnoService\020F\022\023\n\017OrderStartFault\020G\022\035\n\031Sta"
  "rtUpCheckOhpEnableFail\020`\022\035\n\031StartUpCheck"
  "SysEnableFail\020a\022\030\n\024StartUpGetOrderFault\020"
  "b\022\030\n\024StartUpFramWriteFail\020c\022\026\n\022StartUpDb"
  "WriteFail\020d\022\020\n\014StartTimeOut\020e\022\017\n\013AuthTim"
  "eOut\020f\022\023\n\017PreStartTimeOut\020g\022\026\n\022PreStartG"
  "etVinFail\020h\022\014\n\010AuthFail\020i\022\020\n\014AdFixTimeOu"
  "t\020p\022\023\n\017DrawGunAbnormal\020q\022\023\n\017SystemTimeFa"
  "ult\020r\022\037\n\032StartCheckOSCRateParamFail\020\201\001\022\034"
  "\n\027StartCheckOSCParamEmpty\020\202\001\022\032\n\025StartChe"
  "ckSysTimeFail\020\203\001\022\036\n\031StartSelfErrPipeline"
  "State\020\204\001\022\034\n\027StartCheckPipelineStart\020\205\001\022\023"
  "\n\016StartCheckIdle\020\206\001\022\024\n\017StartCheckFault\020\207"
  "\001\022\033\n\026StartFailNotFoundOrder\020\220\001\022\025\n\020FRAMRe"
  "adFFFFFail\020\221\001\022\024\n\017MultiOscOffline\020\222\001\022\023\n\016P"
  "anicOhpCreate\020\223\001*G\n\014DMCFaultEnum\022\023\n\017Defa"
  "ultDMCFault\020\000\022\020\n\014PendingFault\020\001\022\020\n\014Dispu"
  "teFault\020\002*K\n\016AlarmStateEnum\022\020\n\014DefaultAl"
  "arm\020\000\022\022\n\016ADModuleNormal\020\001\022\023\n\017ADModuleFau"
  "ltNo\020\002b\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_GCU_5fAllFaultEnum_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GCU_5fAllFaultEnum_2eproto = {
  false, false, 9254, descriptor_table_protodef_GCU_5fAllFaultEnum_2eproto, "GCU_AllFaultEnum.proto", 
  &descriptor_table_GCU_5fAllFaultEnum_2eproto_once, nullptr, 0, 10,
  schemas, file_default_instances, TableStruct_GCU_5fAllFaultEnum_2eproto::offsets,
  file_level_metadata_GCU_5fAllFaultEnum_2eproto, file_level_enum_descriptors_GCU_5fAllFaultEnum_2eproto, file_level_service_descriptors_GCU_5fAllFaultEnum_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_GCU_5fAllFaultEnum_2eproto_getter() {
  return &descriptor_table_GCU_5fAllFaultEnum_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_GCU_5fAllFaultEnum_2eproto(&descriptor_table_GCU_5fAllFaultEnum_2eproto);
namespace AllFaultEnum {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* VCIFaultEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fAllFaultEnum_2eproto);
  return file_level_enum_descriptors_GCU_5fAllFaultEnum_2eproto[0];
}
bool VCIFaultEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    case 14:
    case 15:
    case 16:
    case 17:
    case 18:
    case 19:
    case 20:
    case 21:
    case 22:
    case 23:
    case 24:
    case 25:
    case 26:
    case 27:
    case 28:
    case 29:
    case 30:
    case 32:
    case 33:
    case 34:
    case 35:
    case 36:
    case 37:
    case 38:
    case 39:
    case 40:
    case 41:
    case 42:
    case 43:
    case 44:
    case 45:
    case 46:
    case 47:
    case 48:
    case 49:
    case 50:
    case 51:
    case 52:
    case 53:
    case 54:
    case 55:
    case 56:
    case 57:
    case 58:
    case 59:
    case 60:
    case 61:
    case 62:
    case 63:
    case 64:
    case 65:
    case 66:
    case 67:
    case 68:
    case 69:
    case 70:
    case 71:
    case 72:
    case 73:
    case 74:
    case 75:
    case 76:
    case 77:
    case 78:
    case 79:
    case 80:
    case 81:
    case 82:
    case 83:
    case 84:
    case 85:
    case 86:
    case 87:
    case 88:
    case 89:
    case 90:
    case 91:
    case 92:
    case 93:
    case 94:
    case 95:
    case 96:
    case 97:
    case 98:
    case 99:
    case 100:
    case 101:
    case 102:
    case 103:
    case 104:
    case 105:
    case 106:
    case 107:
    case 108:
    case 109:
    case 110:
    case 111:
    case 112:
    case 113:
    case 114:
    case 115:
    case 116:
    case 118:
    case 119:
    case 120:
    case 121:
    case 122:
    case 123:
    case 124:
    case 125:
    case 126:
    case 127:
    case 128:
    case 129:
    case 130:
    case 131:
    case 132:
    case 133:
    case 134:
    case 135:
    case 144:
    case 145:
    case 146:
    case 147:
    case 148:
    case 149:
    case 150:
    case 151:
    case 152:
    case 153:
    case 154:
    case 155:
    case 156:
    case 157:
    case 160:
    case 161:
    case 162:
    case 163:
    case 164:
    case 165:
    case 166:
    case 167:
    case 168:
    case 169:
    case 170:
    case 171:
    case 172:
    case 173:
    case 174:
    case 175:
    case 176:
    case 177:
    case 178:
    case 179:
    case 180:
    case 181:
    case 182:
    case 183:
    case 192:
    case 193:
    case 194:
    case 195:
    case 196:
    case 197:
    case 198:
    case 199:
    case 200:
    case 201:
    case 202:
    case 203:
    case 204:
    case 205:
    case 206:
    case 207:
    case 208:
    case 209:
    case 210:
    case 211:
    case 212:
    case 213:
    case 214:
    case 215:
    case 216:
    case 217:
    case 218:
    case 219:
    case 220:
    case 221:
    case 222:
    case 223:
    case 224:
    case 225:
    case 226:
    case 227:
    case 228:
    case 229:
    case 230:
    case 231:
    case 232:
    case 233:
    case 234:
    case 235:
    case 236:
    case 237:
    case 238:
    case 239:
    case 240:
    case 241:
    case 242:
    case 243:
    case 244:
    case 245:
    case 246:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PMMFaultEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fAllFaultEnum_2eproto);
  return file_level_enum_descriptors_GCU_5fAllFaultEnum_2eproto[1];
}
bool PMMFaultEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 16:
    case 17:
    case 18:
    case 19:
    case 20:
    case 21:
    case 22:
    case 23:
    case 24:
    case 25:
    case 26:
    case 27:
    case 28:
    case 29:
    case 30:
    case 32:
    case 33:
    case 34:
    case 35:
    case 36:
    case 37:
    case 38:
    case 39:
    case 40:
    case 49:
    case 50:
    case 51:
    case 52:
    case 53:
    case 54:
    case 55:
    case 56:
    case 57:
    case 58:
    case 59:
    case 60:
    case 61:
    case 62:
    case 64:
    case 65:
    case 66:
    case 67:
    case 68:
    case 69:
    case 70:
    case 71:
    case 72:
    case 73:
    case 74:
    case 75:
    case 76:
    case 77:
    case 78:
    case 79:
    case 80:
    case 81:
    case 82:
    case 83:
    case 84:
    case 85:
    case 86:
    case 87:
    case 88:
    case 89:
    case 90:
    case 91:
    case 96:
    case 255:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OHPFaultEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fAllFaultEnum_2eproto);
  return file_level_enum_descriptors_GCU_5fAllFaultEnum_2eproto[2];
}
bool OHPFaultEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 16:
    case 17:
    case 18:
    case 19:
    case 32:
    case 33:
    case 34:
    case 35:
    case 36:
    case 37:
    case 38:
    case 39:
    case 40:
    case 41:
    case 42:
    case 48:
    case 49:
    case 50:
    case 51:
    case 52:
    case 53:
    case 54:
    case 55:
    case 56:
    case 57:
    case 58:
    case 59:
    case 60:
    case 61:
    case 62:
    case 63:
    case 64:
    case 65:
    case 66:
    case 67:
    case 68:
    case 69:
    case 70:
    case 71:
    case 96:
    case 97:
    case 98:
    case 99:
    case 100:
    case 101:
    case 102:
    case 103:
    case 104:
    case 105:
    case 112:
    case 113:
    case 114:
    case 129:
    case 130:
    case 131:
    case 132:
    case 133:
    case 134:
    case 135:
    case 144:
    case 145:
    case 146:
    case 147:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DMCFaultEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fAllFaultEnum_2eproto);
  return file_level_enum_descriptors_GCU_5fAllFaultEnum_2eproto[3];
}
bool DMCFaultEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* AlarmStateEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_GCU_5fAllFaultEnum_2eproto);
  return file_level_enum_descriptors_GCU_5fAllFaultEnum_2eproto[4];
}
bool AlarmStateEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class VCIFaultState::_Internal {
 public:
};

VCIFaultState::VCIFaultState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:AllFaultEnum.VCIFaultState)
}
VCIFaultState::VCIFaultState(const VCIFaultState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&faulttype_, &from.faulttype_,
    static_cast<size_t>(reinterpret_cast<char*>(&showtype_) -
    reinterpret_cast<char*>(&faulttype_)) + sizeof(showtype_));
  // @@protoc_insertion_point(copy_constructor:AllFaultEnum.VCIFaultState)
}

inline void VCIFaultState::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&faulttype_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&showtype_) -
    reinterpret_cast<char*>(&faulttype_)) + sizeof(showtype_));
}

VCIFaultState::~VCIFaultState() {
  // @@protoc_insertion_point(destructor:AllFaultEnum.VCIFaultState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void VCIFaultState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void VCIFaultState::ArenaDtor(void* object) {
  VCIFaultState* _this = reinterpret_cast< VCIFaultState* >(object);
  (void)_this;
}
void VCIFaultState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void VCIFaultState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void VCIFaultState::Clear() {
// @@protoc_insertion_point(message_clear_start:AllFaultEnum.VCIFaultState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&faulttype_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&showtype_) -
      reinterpret_cast<char*>(&faulttype_)) + sizeof(showtype_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* VCIFaultState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .AllFaultEnum.VCIFaultEnum faultType = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_faulttype(static_cast<::AllFaultEnum::VCIFaultEnum>(val));
        } else goto handle_unusual;
        continue;
      // .AllFaultEnum.AlarmStateEnum faultState = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_faultstate(static_cast<::AllFaultEnum::AlarmStateEnum>(val));
        } else goto handle_unusual;
        continue;
      // uint64 faultRaiseTime = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          faultraisetime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint64 faultDownTime = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          faultdowntime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 ShutDownType = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          shutdowntype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 ShowType = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          showtype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* VCIFaultState::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:AllFaultEnum.VCIFaultState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .AllFaultEnum.VCIFaultEnum faultType = 1;
  if (this->_internal_faulttype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_faulttype(), target);
  }

  // .AllFaultEnum.AlarmStateEnum faultState = 2;
  if (this->_internal_faultstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_faultstate(), target);
  }

  // uint64 faultRaiseTime = 3;
  if (this->_internal_faultraisetime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(3, this->_internal_faultraisetime(), target);
  }

  // uint64 faultDownTime = 4;
  if (this->_internal_faultdowntime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(4, this->_internal_faultdowntime(), target);
  }

  // uint32 ShutDownType = 5;
  if (this->_internal_shutdowntype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_shutdowntype(), target);
  }

  // uint32 ShowType = 6;
  if (this->_internal_showtype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_showtype(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:AllFaultEnum.VCIFaultState)
  return target;
}

size_t VCIFaultState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:AllFaultEnum.VCIFaultState)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .AllFaultEnum.VCIFaultEnum faultType = 1;
  if (this->_internal_faulttype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_faulttype());
  }

  // .AllFaultEnum.AlarmStateEnum faultState = 2;
  if (this->_internal_faultstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_faultstate());
  }

  // uint64 faultRaiseTime = 3;
  if (this->_internal_faultraisetime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_faultraisetime());
  }

  // uint64 faultDownTime = 4;
  if (this->_internal_faultdowntime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_faultdowntime());
  }

  // uint32 ShutDownType = 5;
  if (this->_internal_shutdowntype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_shutdowntype());
  }

  // uint32 ShowType = 6;
  if (this->_internal_showtype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_showtype());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData VCIFaultState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    VCIFaultState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*VCIFaultState::GetClassData() const { return &_class_data_; }

void VCIFaultState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<VCIFaultState *>(to)->MergeFrom(
      static_cast<const VCIFaultState &>(from));
}


void VCIFaultState::MergeFrom(const VCIFaultState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:AllFaultEnum.VCIFaultState)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_faulttype() != 0) {
    _internal_set_faulttype(from._internal_faulttype());
  }
  if (from._internal_faultstate() != 0) {
    _internal_set_faultstate(from._internal_faultstate());
  }
  if (from._internal_faultraisetime() != 0) {
    _internal_set_faultraisetime(from._internal_faultraisetime());
  }
  if (from._internal_faultdowntime() != 0) {
    _internal_set_faultdowntime(from._internal_faultdowntime());
  }
  if (from._internal_shutdowntype() != 0) {
    _internal_set_shutdowntype(from._internal_shutdowntype());
  }
  if (from._internal_showtype() != 0) {
    _internal_set_showtype(from._internal_showtype());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void VCIFaultState::CopyFrom(const VCIFaultState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:AllFaultEnum.VCIFaultState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VCIFaultState::IsInitialized() const {
  return true;
}

void VCIFaultState::InternalSwap(VCIFaultState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(VCIFaultState, showtype_)
      + sizeof(VCIFaultState::showtype_)
      - PROTOBUF_FIELD_OFFSET(VCIFaultState, faulttype_)>(
          reinterpret_cast<char*>(&faulttype_),
          reinterpret_cast<char*>(&other->faulttype_));
}

::PROTOBUF_NAMESPACE_ID::Metadata VCIFaultState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fAllFaultEnum_2eproto_getter, &descriptor_table_GCU_5fAllFaultEnum_2eproto_once,
      file_level_metadata_GCU_5fAllFaultEnum_2eproto[0]);
}

// ===================================================================

class PMMFaultState::_Internal {
 public:
};

PMMFaultState::PMMFaultState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:AllFaultEnum.PMMFaultState)
}
PMMFaultState::PMMFaultState(const PMMFaultState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&faulttype_, &from.faulttype_,
    static_cast<size_t>(reinterpret_cast<char*>(&showtype_) -
    reinterpret_cast<char*>(&faulttype_)) + sizeof(showtype_));
  // @@protoc_insertion_point(copy_constructor:AllFaultEnum.PMMFaultState)
}

inline void PMMFaultState::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&faulttype_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&showtype_) -
    reinterpret_cast<char*>(&faulttype_)) + sizeof(showtype_));
}

PMMFaultState::~PMMFaultState() {
  // @@protoc_insertion_point(destructor:AllFaultEnum.PMMFaultState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PMMFaultState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void PMMFaultState::ArenaDtor(void* object) {
  PMMFaultState* _this = reinterpret_cast< PMMFaultState* >(object);
  (void)_this;
}
void PMMFaultState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PMMFaultState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PMMFaultState::Clear() {
// @@protoc_insertion_point(message_clear_start:AllFaultEnum.PMMFaultState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&faulttype_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&showtype_) -
      reinterpret_cast<char*>(&faulttype_)) + sizeof(showtype_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PMMFaultState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .AllFaultEnum.PMMFaultEnum faultType = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_faulttype(static_cast<::AllFaultEnum::PMMFaultEnum>(val));
        } else goto handle_unusual;
        continue;
      // .AllFaultEnum.AlarmStateEnum faultState = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_faultstate(static_cast<::AllFaultEnum::AlarmStateEnum>(val));
        } else goto handle_unusual;
        continue;
      // uint64 faultRaiseTime = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          faultraisetime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint64 faultDownTime = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          faultdowntime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 ShutDownType = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          shutdowntype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 ShowType = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          showtype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* PMMFaultState::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:AllFaultEnum.PMMFaultState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .AllFaultEnum.PMMFaultEnum faultType = 1;
  if (this->_internal_faulttype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_faulttype(), target);
  }

  // .AllFaultEnum.AlarmStateEnum faultState = 2;
  if (this->_internal_faultstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_faultstate(), target);
  }

  // uint64 faultRaiseTime = 3;
  if (this->_internal_faultraisetime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(3, this->_internal_faultraisetime(), target);
  }

  // uint64 faultDownTime = 4;
  if (this->_internal_faultdowntime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(4, this->_internal_faultdowntime(), target);
  }

  // uint32 ShutDownType = 5;
  if (this->_internal_shutdowntype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_shutdowntype(), target);
  }

  // uint32 ShowType = 6;
  if (this->_internal_showtype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_showtype(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:AllFaultEnum.PMMFaultState)
  return target;
}

size_t PMMFaultState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:AllFaultEnum.PMMFaultState)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .AllFaultEnum.PMMFaultEnum faultType = 1;
  if (this->_internal_faulttype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_faulttype());
  }

  // .AllFaultEnum.AlarmStateEnum faultState = 2;
  if (this->_internal_faultstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_faultstate());
  }

  // uint64 faultRaiseTime = 3;
  if (this->_internal_faultraisetime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_faultraisetime());
  }

  // uint64 faultDownTime = 4;
  if (this->_internal_faultdowntime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_faultdowntime());
  }

  // uint32 ShutDownType = 5;
  if (this->_internal_shutdowntype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_shutdowntype());
  }

  // uint32 ShowType = 6;
  if (this->_internal_showtype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_showtype());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PMMFaultState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PMMFaultState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PMMFaultState::GetClassData() const { return &_class_data_; }

void PMMFaultState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<PMMFaultState *>(to)->MergeFrom(
      static_cast<const PMMFaultState &>(from));
}


void PMMFaultState::MergeFrom(const PMMFaultState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:AllFaultEnum.PMMFaultState)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_faulttype() != 0) {
    _internal_set_faulttype(from._internal_faulttype());
  }
  if (from._internal_faultstate() != 0) {
    _internal_set_faultstate(from._internal_faultstate());
  }
  if (from._internal_faultraisetime() != 0) {
    _internal_set_faultraisetime(from._internal_faultraisetime());
  }
  if (from._internal_faultdowntime() != 0) {
    _internal_set_faultdowntime(from._internal_faultdowntime());
  }
  if (from._internal_shutdowntype() != 0) {
    _internal_set_shutdowntype(from._internal_shutdowntype());
  }
  if (from._internal_showtype() != 0) {
    _internal_set_showtype(from._internal_showtype());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PMMFaultState::CopyFrom(const PMMFaultState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:AllFaultEnum.PMMFaultState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PMMFaultState::IsInitialized() const {
  return true;
}

void PMMFaultState::InternalSwap(PMMFaultState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PMMFaultState, showtype_)
      + sizeof(PMMFaultState::showtype_)
      - PROTOBUF_FIELD_OFFSET(PMMFaultState, faulttype_)>(
          reinterpret_cast<char*>(&faulttype_),
          reinterpret_cast<char*>(&other->faulttype_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PMMFaultState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fAllFaultEnum_2eproto_getter, &descriptor_table_GCU_5fAllFaultEnum_2eproto_once,
      file_level_metadata_GCU_5fAllFaultEnum_2eproto[1]);
}

// ===================================================================

class OHPFaultState::_Internal {
 public:
};

OHPFaultState::OHPFaultState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:AllFaultEnum.OHPFaultState)
}
OHPFaultState::OHPFaultState(const OHPFaultState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&faulttype_, &from.faulttype_,
    static_cast<size_t>(reinterpret_cast<char*>(&showtype_) -
    reinterpret_cast<char*>(&faulttype_)) + sizeof(showtype_));
  // @@protoc_insertion_point(copy_constructor:AllFaultEnum.OHPFaultState)
}

inline void OHPFaultState::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&faulttype_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&showtype_) -
    reinterpret_cast<char*>(&faulttype_)) + sizeof(showtype_));
}

OHPFaultState::~OHPFaultState() {
  // @@protoc_insertion_point(destructor:AllFaultEnum.OHPFaultState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void OHPFaultState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void OHPFaultState::ArenaDtor(void* object) {
  OHPFaultState* _this = reinterpret_cast< OHPFaultState* >(object);
  (void)_this;
}
void OHPFaultState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void OHPFaultState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void OHPFaultState::Clear() {
// @@protoc_insertion_point(message_clear_start:AllFaultEnum.OHPFaultState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&faulttype_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&showtype_) -
      reinterpret_cast<char*>(&faulttype_)) + sizeof(showtype_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* OHPFaultState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .AllFaultEnum.OHPFaultEnum faultType = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_faulttype(static_cast<::AllFaultEnum::OHPFaultEnum>(val));
        } else goto handle_unusual;
        continue;
      // .AllFaultEnum.AlarmStateEnum faultState = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_faultstate(static_cast<::AllFaultEnum::AlarmStateEnum>(val));
        } else goto handle_unusual;
        continue;
      // uint64 faultRaiseTime = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          faultraisetime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint64 faultDownTime = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          faultdowntime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 ShutDownType = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          shutdowntype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 ShowType = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          showtype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* OHPFaultState::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:AllFaultEnum.OHPFaultState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .AllFaultEnum.OHPFaultEnum faultType = 1;
  if (this->_internal_faulttype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_faulttype(), target);
  }

  // .AllFaultEnum.AlarmStateEnum faultState = 2;
  if (this->_internal_faultstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_faultstate(), target);
  }

  // uint64 faultRaiseTime = 3;
  if (this->_internal_faultraisetime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(3, this->_internal_faultraisetime(), target);
  }

  // uint64 faultDownTime = 4;
  if (this->_internal_faultdowntime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(4, this->_internal_faultdowntime(), target);
  }

  // uint32 ShutDownType = 5;
  if (this->_internal_shutdowntype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_shutdowntype(), target);
  }

  // uint32 ShowType = 6;
  if (this->_internal_showtype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_showtype(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:AllFaultEnum.OHPFaultState)
  return target;
}

size_t OHPFaultState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:AllFaultEnum.OHPFaultState)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .AllFaultEnum.OHPFaultEnum faultType = 1;
  if (this->_internal_faulttype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_faulttype());
  }

  // .AllFaultEnum.AlarmStateEnum faultState = 2;
  if (this->_internal_faultstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_faultstate());
  }

  // uint64 faultRaiseTime = 3;
  if (this->_internal_faultraisetime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_faultraisetime());
  }

  // uint64 faultDownTime = 4;
  if (this->_internal_faultdowntime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_faultdowntime());
  }

  // uint32 ShutDownType = 5;
  if (this->_internal_shutdowntype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_shutdowntype());
  }

  // uint32 ShowType = 6;
  if (this->_internal_showtype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_showtype());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData OHPFaultState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    OHPFaultState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*OHPFaultState::GetClassData() const { return &_class_data_; }

void OHPFaultState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<OHPFaultState *>(to)->MergeFrom(
      static_cast<const OHPFaultState &>(from));
}


void OHPFaultState::MergeFrom(const OHPFaultState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:AllFaultEnum.OHPFaultState)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_faulttype() != 0) {
    _internal_set_faulttype(from._internal_faulttype());
  }
  if (from._internal_faultstate() != 0) {
    _internal_set_faultstate(from._internal_faultstate());
  }
  if (from._internal_faultraisetime() != 0) {
    _internal_set_faultraisetime(from._internal_faultraisetime());
  }
  if (from._internal_faultdowntime() != 0) {
    _internal_set_faultdowntime(from._internal_faultdowntime());
  }
  if (from._internal_shutdowntype() != 0) {
    _internal_set_shutdowntype(from._internal_shutdowntype());
  }
  if (from._internal_showtype() != 0) {
    _internal_set_showtype(from._internal_showtype());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void OHPFaultState::CopyFrom(const OHPFaultState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:AllFaultEnum.OHPFaultState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OHPFaultState::IsInitialized() const {
  return true;
}

void OHPFaultState::InternalSwap(OHPFaultState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(OHPFaultState, showtype_)
      + sizeof(OHPFaultState::showtype_)
      - PROTOBUF_FIELD_OFFSET(OHPFaultState, faulttype_)>(
          reinterpret_cast<char*>(&faulttype_),
          reinterpret_cast<char*>(&other->faulttype_));
}

::PROTOBUF_NAMESPACE_ID::Metadata OHPFaultState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fAllFaultEnum_2eproto_getter, &descriptor_table_GCU_5fAllFaultEnum_2eproto_once,
      file_level_metadata_GCU_5fAllFaultEnum_2eproto[2]);
}

// ===================================================================

class DMCFaultState::_Internal {
 public:
};

DMCFaultState::DMCFaultState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:AllFaultEnum.DMCFaultState)
}
DMCFaultState::DMCFaultState(const DMCFaultState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&faulttype_, &from.faulttype_,
    static_cast<size_t>(reinterpret_cast<char*>(&showtype_) -
    reinterpret_cast<char*>(&faulttype_)) + sizeof(showtype_));
  // @@protoc_insertion_point(copy_constructor:AllFaultEnum.DMCFaultState)
}

inline void DMCFaultState::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&faulttype_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&showtype_) -
    reinterpret_cast<char*>(&faulttype_)) + sizeof(showtype_));
}

DMCFaultState::~DMCFaultState() {
  // @@protoc_insertion_point(destructor:AllFaultEnum.DMCFaultState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DMCFaultState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void DMCFaultState::ArenaDtor(void* object) {
  DMCFaultState* _this = reinterpret_cast< DMCFaultState* >(object);
  (void)_this;
}
void DMCFaultState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DMCFaultState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DMCFaultState::Clear() {
// @@protoc_insertion_point(message_clear_start:AllFaultEnum.DMCFaultState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&faulttype_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&showtype_) -
      reinterpret_cast<char*>(&faulttype_)) + sizeof(showtype_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DMCFaultState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .AllFaultEnum.DMCFaultEnum faultType = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_faulttype(static_cast<::AllFaultEnum::DMCFaultEnum>(val));
        } else goto handle_unusual;
        continue;
      // .AllFaultEnum.AlarmStateEnum faultState = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_faultstate(static_cast<::AllFaultEnum::AlarmStateEnum>(val));
        } else goto handle_unusual;
        continue;
      // uint64 faultRaiseTime = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          faultraisetime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint64 faultDownTime = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          faultdowntime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 ShutDownType = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          shutdowntype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 ShowType = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          showtype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* DMCFaultState::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:AllFaultEnum.DMCFaultState)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .AllFaultEnum.DMCFaultEnum faultType = 1;
  if (this->_internal_faulttype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_faulttype(), target);
  }

  // .AllFaultEnum.AlarmStateEnum faultState = 2;
  if (this->_internal_faultstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_faultstate(), target);
  }

  // uint64 faultRaiseTime = 3;
  if (this->_internal_faultraisetime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(3, this->_internal_faultraisetime(), target);
  }

  // uint64 faultDownTime = 4;
  if (this->_internal_faultdowntime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(4, this->_internal_faultdowntime(), target);
  }

  // uint32 ShutDownType = 5;
  if (this->_internal_shutdowntype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_shutdowntype(), target);
  }

  // uint32 ShowType = 6;
  if (this->_internal_showtype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_showtype(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:AllFaultEnum.DMCFaultState)
  return target;
}

size_t DMCFaultState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:AllFaultEnum.DMCFaultState)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .AllFaultEnum.DMCFaultEnum faultType = 1;
  if (this->_internal_faulttype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_faulttype());
  }

  // .AllFaultEnum.AlarmStateEnum faultState = 2;
  if (this->_internal_faultstate() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_faultstate());
  }

  // uint64 faultRaiseTime = 3;
  if (this->_internal_faultraisetime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_faultraisetime());
  }

  // uint64 faultDownTime = 4;
  if (this->_internal_faultdowntime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_faultdowntime());
  }

  // uint32 ShutDownType = 5;
  if (this->_internal_shutdowntype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_shutdowntype());
  }

  // uint32 ShowType = 6;
  if (this->_internal_showtype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_showtype());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DMCFaultState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DMCFaultState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DMCFaultState::GetClassData() const { return &_class_data_; }

void DMCFaultState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<DMCFaultState *>(to)->MergeFrom(
      static_cast<const DMCFaultState &>(from));
}


void DMCFaultState::MergeFrom(const DMCFaultState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:AllFaultEnum.DMCFaultState)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_faulttype() != 0) {
    _internal_set_faulttype(from._internal_faulttype());
  }
  if (from._internal_faultstate() != 0) {
    _internal_set_faultstate(from._internal_faultstate());
  }
  if (from._internal_faultraisetime() != 0) {
    _internal_set_faultraisetime(from._internal_faultraisetime());
  }
  if (from._internal_faultdowntime() != 0) {
    _internal_set_faultdowntime(from._internal_faultdowntime());
  }
  if (from._internal_shutdowntype() != 0) {
    _internal_set_shutdowntype(from._internal_shutdowntype());
  }
  if (from._internal_showtype() != 0) {
    _internal_set_showtype(from._internal_showtype());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DMCFaultState::CopyFrom(const DMCFaultState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:AllFaultEnum.DMCFaultState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DMCFaultState::IsInitialized() const {
  return true;
}

void DMCFaultState::InternalSwap(DMCFaultState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DMCFaultState, showtype_)
      + sizeof(DMCFaultState::showtype_)
      - PROTOBUF_FIELD_OFFSET(DMCFaultState, faulttype_)>(
          reinterpret_cast<char*>(&faulttype_),
          reinterpret_cast<char*>(&other->faulttype_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DMCFaultState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fAllFaultEnum_2eproto_getter, &descriptor_table_GCU_5fAllFaultEnum_2eproto_once,
      file_level_metadata_GCU_5fAllFaultEnum_2eproto[3]);
}

// ===================================================================

class MatrixStatus::_Internal {
 public:
};

MatrixStatus::MatrixStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:AllFaultEnum.MatrixStatus)
}
MatrixStatus::MatrixStatus(const MatrixStatus& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&matrixcontactorid_, &from.matrixcontactorid_,
    static_cast<size_t>(reinterpret_cast<char*>(&realfaultcontid_) -
    reinterpret_cast<char*>(&matrixcontactorid_)) + sizeof(realfaultcontid_));
  // @@protoc_insertion_point(copy_constructor:AllFaultEnum.MatrixStatus)
}

inline void MatrixStatus::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&matrixcontactorid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&realfaultcontid_) -
    reinterpret_cast<char*>(&matrixcontactorid_)) + sizeof(realfaultcontid_));
}

MatrixStatus::~MatrixStatus() {
  // @@protoc_insertion_point(destructor:AllFaultEnum.MatrixStatus)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MatrixStatus::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void MatrixStatus::ArenaDtor(void* object) {
  MatrixStatus* _this = reinterpret_cast< MatrixStatus* >(object);
  (void)_this;
}
void MatrixStatus::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MatrixStatus::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MatrixStatus::Clear() {
// @@protoc_insertion_point(message_clear_start:AllFaultEnum.MatrixStatus)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&matrixcontactorid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&realfaultcontid_) -
      reinterpret_cast<char*>(&matrixcontactorid_)) + sizeof(realfaultcontid_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MatrixStatus::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 MatrixContactorID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          matrixcontactorid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .AllFaultEnum.PMMFaultEnum AlarmAnsList = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_alarmanslist(static_cast<::AllFaultEnum::PMMFaultEnum>(val));
        } else goto handle_unusual;
        continue;
      // uint32 AlarmAttr = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          alarmattr_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 realFaultContID = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          realfaultcontid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* MatrixStatus::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:AllFaultEnum.MatrixStatus)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 MatrixContactorID = 1;
  if (this->_internal_matrixcontactorid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_matrixcontactorid(), target);
  }

  // .AllFaultEnum.PMMFaultEnum AlarmAnsList = 2;
  if (this->_internal_alarmanslist() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_alarmanslist(), target);
  }

  // uint32 AlarmAttr = 3;
  if (this->_internal_alarmattr() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_alarmattr(), target);
  }

  // uint32 realFaultContID = 4;
  if (this->_internal_realfaultcontid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_realfaultcontid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:AllFaultEnum.MatrixStatus)
  return target;
}

size_t MatrixStatus::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:AllFaultEnum.MatrixStatus)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 MatrixContactorID = 1;
  if (this->_internal_matrixcontactorid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_matrixcontactorid());
  }

  // .AllFaultEnum.PMMFaultEnum AlarmAnsList = 2;
  if (this->_internal_alarmanslist() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_alarmanslist());
  }

  // uint32 AlarmAttr = 3;
  if (this->_internal_alarmattr() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_alarmattr());
  }

  // uint32 realFaultContID = 4;
  if (this->_internal_realfaultcontid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_realfaultcontid());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MatrixStatus::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MatrixStatus::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MatrixStatus::GetClassData() const { return &_class_data_; }

void MatrixStatus::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<MatrixStatus *>(to)->MergeFrom(
      static_cast<const MatrixStatus &>(from));
}


void MatrixStatus::MergeFrom(const MatrixStatus& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:AllFaultEnum.MatrixStatus)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_matrixcontactorid() != 0) {
    _internal_set_matrixcontactorid(from._internal_matrixcontactorid());
  }
  if (from._internal_alarmanslist() != 0) {
    _internal_set_alarmanslist(from._internal_alarmanslist());
  }
  if (from._internal_alarmattr() != 0) {
    _internal_set_alarmattr(from._internal_alarmattr());
  }
  if (from._internal_realfaultcontid() != 0) {
    _internal_set_realfaultcontid(from._internal_realfaultcontid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MatrixStatus::CopyFrom(const MatrixStatus& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:AllFaultEnum.MatrixStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MatrixStatus::IsInitialized() const {
  return true;
}

void MatrixStatus::InternalSwap(MatrixStatus* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(MatrixStatus, realfaultcontid_)
      + sizeof(MatrixStatus::realfaultcontid_)
      - PROTOBUF_FIELD_OFFSET(MatrixStatus, matrixcontactorid_)>(
          reinterpret_cast<char*>(&matrixcontactorid_),
          reinterpret_cast<char*>(&other->matrixcontactorid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata MatrixStatus::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fAllFaultEnum_2eproto_getter, &descriptor_table_GCU_5fAllFaultEnum_2eproto_once,
      file_level_metadata_GCU_5fAllFaultEnum_2eproto[4]);
}

// ===================================================================

class ADModuleAlarm::_Internal {
 public:
};

ADModuleAlarm::ADModuleAlarm(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  admodulelist_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:AllFaultEnum.ADModuleAlarm)
}
ADModuleAlarm::ADModuleAlarm(const ADModuleAlarm& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      admodulelist_(from.admodulelist_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  admoduleid_ = from.admoduleid_;
  // @@protoc_insertion_point(copy_constructor:AllFaultEnum.ADModuleAlarm)
}

inline void ADModuleAlarm::SharedCtor() {
admoduleid_ = 0u;
}

ADModuleAlarm::~ADModuleAlarm() {
  // @@protoc_insertion_point(destructor:AllFaultEnum.ADModuleAlarm)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ADModuleAlarm::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void ADModuleAlarm::ArenaDtor(void* object) {
  ADModuleAlarm* _this = reinterpret_cast< ADModuleAlarm* >(object);
  (void)_this;
}
void ADModuleAlarm::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ADModuleAlarm::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ADModuleAlarm::Clear() {
// @@protoc_insertion_point(message_clear_start:AllFaultEnum.ADModuleAlarm)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  admodulelist_.Clear();
  admoduleid_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ADModuleAlarm::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 ADModuleID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          admoduleid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .AllFaultEnum.PMMFaultState ADModuleList = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_admodulelist(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ADModuleAlarm::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:AllFaultEnum.ADModuleAlarm)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 ADModuleID = 1;
  if (this->_internal_admoduleid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_admoduleid(), target);
  }

  // repeated .AllFaultEnum.PMMFaultState ADModuleList = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_admodulelist_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_admodulelist(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:AllFaultEnum.ADModuleAlarm)
  return target;
}

size_t ADModuleAlarm::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:AllFaultEnum.ADModuleAlarm)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .AllFaultEnum.PMMFaultState ADModuleList = 2;
  total_size += 1UL * this->_internal_admodulelist_size();
  for (const auto& msg : this->admodulelist_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // uint32 ADModuleID = 1;
  if (this->_internal_admoduleid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_admoduleid());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ADModuleAlarm::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ADModuleAlarm::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ADModuleAlarm::GetClassData() const { return &_class_data_; }

void ADModuleAlarm::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<ADModuleAlarm *>(to)->MergeFrom(
      static_cast<const ADModuleAlarm &>(from));
}


void ADModuleAlarm::MergeFrom(const ADModuleAlarm& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:AllFaultEnum.ADModuleAlarm)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  admodulelist_.MergeFrom(from.admodulelist_);
  if (from._internal_admoduleid() != 0) {
    _internal_set_admoduleid(from._internal_admoduleid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ADModuleAlarm::CopyFrom(const ADModuleAlarm& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:AllFaultEnum.ADModuleAlarm)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ADModuleAlarm::IsInitialized() const {
  return true;
}

void ADModuleAlarm::InternalSwap(ADModuleAlarm* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  admodulelist_.InternalSwap(&other->admodulelist_);
  swap(admoduleid_, other->admoduleid_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ADModuleAlarm::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fAllFaultEnum_2eproto_getter, &descriptor_table_GCU_5fAllFaultEnum_2eproto_once,
      file_level_metadata_GCU_5fAllFaultEnum_2eproto[5]);
}

// ===================================================================

class VCIContactorFaultSend::_Internal {
 public:
};

VCIContactorFaultSend::VCIContactorFaultSend(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  vcifault_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:AllFaultEnum.VCIContactorFaultSend)
}
VCIContactorFaultSend::VCIContactorFaultSend(const VCIContactorFaultSend& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      vcifault_(from.vcifault_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  gunnum_ = from.gunnum_;
  // @@protoc_insertion_point(copy_constructor:AllFaultEnum.VCIContactorFaultSend)
}

inline void VCIContactorFaultSend::SharedCtor() {
gunnum_ = 0u;
}

VCIContactorFaultSend::~VCIContactorFaultSend() {
  // @@protoc_insertion_point(destructor:AllFaultEnum.VCIContactorFaultSend)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void VCIContactorFaultSend::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void VCIContactorFaultSend::ArenaDtor(void* object) {
  VCIContactorFaultSend* _this = reinterpret_cast< VCIContactorFaultSend* >(object);
  (void)_this;
}
void VCIContactorFaultSend::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void VCIContactorFaultSend::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void VCIContactorFaultSend::Clear() {
// @@protoc_insertion_point(message_clear_start:AllFaultEnum.VCIContactorFaultSend)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  vcifault_.Clear();
  gunnum_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* VCIContactorFaultSend::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 gunNum = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          gunnum_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .AllFaultEnum.VCIFaultState VCIFault = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_vcifault(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* VCIContactorFaultSend::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:AllFaultEnum.VCIContactorFaultSend)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 gunNum = 1;
  if (this->_internal_gunnum() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_gunnum(), target);
  }

  // repeated .AllFaultEnum.VCIFaultState VCIFault = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_vcifault_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_vcifault(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:AllFaultEnum.VCIContactorFaultSend)
  return target;
}

size_t VCIContactorFaultSend::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:AllFaultEnum.VCIContactorFaultSend)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .AllFaultEnum.VCIFaultState VCIFault = 2;
  total_size += 1UL * this->_internal_vcifault_size();
  for (const auto& msg : this->vcifault_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // uint32 gunNum = 1;
  if (this->_internal_gunnum() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_gunnum());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData VCIContactorFaultSend::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    VCIContactorFaultSend::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*VCIContactorFaultSend::GetClassData() const { return &_class_data_; }

void VCIContactorFaultSend::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<VCIContactorFaultSend *>(to)->MergeFrom(
      static_cast<const VCIContactorFaultSend &>(from));
}


void VCIContactorFaultSend::MergeFrom(const VCIContactorFaultSend& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:AllFaultEnum.VCIContactorFaultSend)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  vcifault_.MergeFrom(from.vcifault_);
  if (from._internal_gunnum() != 0) {
    _internal_set_gunnum(from._internal_gunnum());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void VCIContactorFaultSend::CopyFrom(const VCIContactorFaultSend& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:AllFaultEnum.VCIContactorFaultSend)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VCIContactorFaultSend::IsInitialized() const {
  return true;
}

void VCIContactorFaultSend::InternalSwap(VCIContactorFaultSend* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  vcifault_.InternalSwap(&other->vcifault_);
  swap(gunnum_, other->gunnum_);
}

::PROTOBUF_NAMESPACE_ID::Metadata VCIContactorFaultSend::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fAllFaultEnum_2eproto_getter, &descriptor_table_GCU_5fAllFaultEnum_2eproto_once,
      file_level_metadata_GCU_5fAllFaultEnum_2eproto[6]);
}

// ===================================================================

class PMMContactorFaultSend::_Internal {
 public:
};

PMMContactorFaultSend::PMMContactorFaultSend(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  pmmfault_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:AllFaultEnum.PMMContactorFaultSend)
}
PMMContactorFaultSend::PMMContactorFaultSend(const PMMContactorFaultSend& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      pmmfault_(from.pmmfault_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  gunnum_ = from.gunnum_;
  // @@protoc_insertion_point(copy_constructor:AllFaultEnum.PMMContactorFaultSend)
}

inline void PMMContactorFaultSend::SharedCtor() {
gunnum_ = 0u;
}

PMMContactorFaultSend::~PMMContactorFaultSend() {
  // @@protoc_insertion_point(destructor:AllFaultEnum.PMMContactorFaultSend)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PMMContactorFaultSend::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void PMMContactorFaultSend::ArenaDtor(void* object) {
  PMMContactorFaultSend* _this = reinterpret_cast< PMMContactorFaultSend* >(object);
  (void)_this;
}
void PMMContactorFaultSend::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PMMContactorFaultSend::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PMMContactorFaultSend::Clear() {
// @@protoc_insertion_point(message_clear_start:AllFaultEnum.PMMContactorFaultSend)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  pmmfault_.Clear();
  gunnum_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PMMContactorFaultSend::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 gunNum = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          gunnum_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .AllFaultEnum.PMMFaultState PMMFault = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_pmmfault(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* PMMContactorFaultSend::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:AllFaultEnum.PMMContactorFaultSend)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 gunNum = 1;
  if (this->_internal_gunnum() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_gunnum(), target);
  }

  // repeated .AllFaultEnum.PMMFaultState PMMFault = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_pmmfault_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_pmmfault(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:AllFaultEnum.PMMContactorFaultSend)
  return target;
}

size_t PMMContactorFaultSend::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:AllFaultEnum.PMMContactorFaultSend)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .AllFaultEnum.PMMFaultState PMMFault = 2;
  total_size += 1UL * this->_internal_pmmfault_size();
  for (const auto& msg : this->pmmfault_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // uint32 gunNum = 1;
  if (this->_internal_gunnum() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_gunnum());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PMMContactorFaultSend::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PMMContactorFaultSend::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PMMContactorFaultSend::GetClassData() const { return &_class_data_; }

void PMMContactorFaultSend::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<PMMContactorFaultSend *>(to)->MergeFrom(
      static_cast<const PMMContactorFaultSend &>(from));
}


void PMMContactorFaultSend::MergeFrom(const PMMContactorFaultSend& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:AllFaultEnum.PMMContactorFaultSend)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  pmmfault_.MergeFrom(from.pmmfault_);
  if (from._internal_gunnum() != 0) {
    _internal_set_gunnum(from._internal_gunnum());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PMMContactorFaultSend::CopyFrom(const PMMContactorFaultSend& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:AllFaultEnum.PMMContactorFaultSend)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PMMContactorFaultSend::IsInitialized() const {
  return true;
}

void PMMContactorFaultSend::InternalSwap(PMMContactorFaultSend* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  pmmfault_.InternalSwap(&other->pmmfault_);
  swap(gunnum_, other->gunnum_);
}

::PROTOBUF_NAMESPACE_ID::Metadata PMMContactorFaultSend::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fAllFaultEnum_2eproto_getter, &descriptor_table_GCU_5fAllFaultEnum_2eproto_once,
      file_level_metadata_GCU_5fAllFaultEnum_2eproto[7]);
}

// ===================================================================

class OHPContactorFaultSend::_Internal {
 public:
};

OHPContactorFaultSend::OHPContactorFaultSend(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  ohpfault_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:AllFaultEnum.OHPContactorFaultSend)
}
OHPContactorFaultSend::OHPContactorFaultSend(const OHPContactorFaultSend& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      ohpfault_(from.ohpfault_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  gunnum_ = from.gunnum_;
  // @@protoc_insertion_point(copy_constructor:AllFaultEnum.OHPContactorFaultSend)
}

inline void OHPContactorFaultSend::SharedCtor() {
gunnum_ = 0u;
}

OHPContactorFaultSend::~OHPContactorFaultSend() {
  // @@protoc_insertion_point(destructor:AllFaultEnum.OHPContactorFaultSend)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void OHPContactorFaultSend::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void OHPContactorFaultSend::ArenaDtor(void* object) {
  OHPContactorFaultSend* _this = reinterpret_cast< OHPContactorFaultSend* >(object);
  (void)_this;
}
void OHPContactorFaultSend::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void OHPContactorFaultSend::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void OHPContactorFaultSend::Clear() {
// @@protoc_insertion_point(message_clear_start:AllFaultEnum.OHPContactorFaultSend)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ohpfault_.Clear();
  gunnum_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* OHPContactorFaultSend::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 gunNum = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          gunnum_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .AllFaultEnum.OHPFaultState OHPFault = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_ohpfault(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* OHPContactorFaultSend::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:AllFaultEnum.OHPContactorFaultSend)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 gunNum = 1;
  if (this->_internal_gunnum() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_gunnum(), target);
  }

  // repeated .AllFaultEnum.OHPFaultState OHPFault = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_ohpfault_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_ohpfault(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:AllFaultEnum.OHPContactorFaultSend)
  return target;
}

size_t OHPContactorFaultSend::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:AllFaultEnum.OHPContactorFaultSend)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .AllFaultEnum.OHPFaultState OHPFault = 2;
  total_size += 1UL * this->_internal_ohpfault_size();
  for (const auto& msg : this->ohpfault_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // uint32 gunNum = 1;
  if (this->_internal_gunnum() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_gunnum());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData OHPContactorFaultSend::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    OHPContactorFaultSend::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*OHPContactorFaultSend::GetClassData() const { return &_class_data_; }

void OHPContactorFaultSend::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<OHPContactorFaultSend *>(to)->MergeFrom(
      static_cast<const OHPContactorFaultSend &>(from));
}


void OHPContactorFaultSend::MergeFrom(const OHPContactorFaultSend& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:AllFaultEnum.OHPContactorFaultSend)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  ohpfault_.MergeFrom(from.ohpfault_);
  if (from._internal_gunnum() != 0) {
    _internal_set_gunnum(from._internal_gunnum());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void OHPContactorFaultSend::CopyFrom(const OHPContactorFaultSend& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:AllFaultEnum.OHPContactorFaultSend)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OHPContactorFaultSend::IsInitialized() const {
  return true;
}

void OHPContactorFaultSend::InternalSwap(OHPContactorFaultSend* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ohpfault_.InternalSwap(&other->ohpfault_);
  swap(gunnum_, other->gunnum_);
}

::PROTOBUF_NAMESPACE_ID::Metadata OHPContactorFaultSend::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fAllFaultEnum_2eproto_getter, &descriptor_table_GCU_5fAllFaultEnum_2eproto_once,
      file_level_metadata_GCU_5fAllFaultEnum_2eproto[8]);
}

// ===================================================================

class DMCContactorFaultSend::_Internal {
 public:
};

DMCContactorFaultSend::DMCContactorFaultSend(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  dmcfault_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:AllFaultEnum.DMCContactorFaultSend)
}
DMCContactorFaultSend::DMCContactorFaultSend(const DMCContactorFaultSend& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      dmcfault_(from.dmcfault_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  gunnum_ = from.gunnum_;
  // @@protoc_insertion_point(copy_constructor:AllFaultEnum.DMCContactorFaultSend)
}

inline void DMCContactorFaultSend::SharedCtor() {
gunnum_ = 0u;
}

DMCContactorFaultSend::~DMCContactorFaultSend() {
  // @@protoc_insertion_point(destructor:AllFaultEnum.DMCContactorFaultSend)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DMCContactorFaultSend::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void DMCContactorFaultSend::ArenaDtor(void* object) {
  DMCContactorFaultSend* _this = reinterpret_cast< DMCContactorFaultSend* >(object);
  (void)_this;
}
void DMCContactorFaultSend::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DMCContactorFaultSend::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DMCContactorFaultSend::Clear() {
// @@protoc_insertion_point(message_clear_start:AllFaultEnum.DMCContactorFaultSend)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  dmcfault_.Clear();
  gunnum_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DMCContactorFaultSend::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 gunNum = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          gunnum_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .AllFaultEnum.DMCFaultState DMCFault = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_dmcfault(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag == 0) || ((tag & 7) == 4)) {
          CHK_(ptr);
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* DMCContactorFaultSend::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:AllFaultEnum.DMCContactorFaultSend)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 gunNum = 1;
  if (this->_internal_gunnum() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_gunnum(), target);
  }

  // repeated .AllFaultEnum.DMCFaultState DMCFault = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_dmcfault_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_dmcfault(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:AllFaultEnum.DMCContactorFaultSend)
  return target;
}

size_t DMCContactorFaultSend::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:AllFaultEnum.DMCContactorFaultSend)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .AllFaultEnum.DMCFaultState DMCFault = 2;
  total_size += 1UL * this->_internal_dmcfault_size();
  for (const auto& msg : this->dmcfault_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // uint32 gunNum = 1;
  if (this->_internal_gunnum() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_gunnum());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DMCContactorFaultSend::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DMCContactorFaultSend::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DMCContactorFaultSend::GetClassData() const { return &_class_data_; }

void DMCContactorFaultSend::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to,
                      const ::PROTOBUF_NAMESPACE_ID::Message&from) {
  static_cast<DMCContactorFaultSend *>(to)->MergeFrom(
      static_cast<const DMCContactorFaultSend &>(from));
}


void DMCContactorFaultSend::MergeFrom(const DMCContactorFaultSend& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:AllFaultEnum.DMCContactorFaultSend)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  dmcfault_.MergeFrom(from.dmcfault_);
  if (from._internal_gunnum() != 0) {
    _internal_set_gunnum(from._internal_gunnum());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DMCContactorFaultSend::CopyFrom(const DMCContactorFaultSend& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:AllFaultEnum.DMCContactorFaultSend)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DMCContactorFaultSend::IsInitialized() const {
  return true;
}

void DMCContactorFaultSend::InternalSwap(DMCContactorFaultSend* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  dmcfault_.InternalSwap(&other->dmcfault_);
  swap(gunnum_, other->gunnum_);
}

::PROTOBUF_NAMESPACE_ID::Metadata DMCContactorFaultSend::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GCU_5fAllFaultEnum_2eproto_getter, &descriptor_table_GCU_5fAllFaultEnum_2eproto_once,
      file_level_metadata_GCU_5fAllFaultEnum_2eproto[9]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace AllFaultEnum
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::AllFaultEnum::VCIFaultState* Arena::CreateMaybeMessage< ::AllFaultEnum::VCIFaultState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::AllFaultEnum::VCIFaultState >(arena);
}
template<> PROTOBUF_NOINLINE ::AllFaultEnum::PMMFaultState* Arena::CreateMaybeMessage< ::AllFaultEnum::PMMFaultState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::AllFaultEnum::PMMFaultState >(arena);
}
template<> PROTOBUF_NOINLINE ::AllFaultEnum::OHPFaultState* Arena::CreateMaybeMessage< ::AllFaultEnum::OHPFaultState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::AllFaultEnum::OHPFaultState >(arena);
}
template<> PROTOBUF_NOINLINE ::AllFaultEnum::DMCFaultState* Arena::CreateMaybeMessage< ::AllFaultEnum::DMCFaultState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::AllFaultEnum::DMCFaultState >(arena);
}
template<> PROTOBUF_NOINLINE ::AllFaultEnum::MatrixStatus* Arena::CreateMaybeMessage< ::AllFaultEnum::MatrixStatus >(Arena* arena) {
  return Arena::CreateMessageInternal< ::AllFaultEnum::MatrixStatus >(arena);
}
template<> PROTOBUF_NOINLINE ::AllFaultEnum::ADModuleAlarm* Arena::CreateMaybeMessage< ::AllFaultEnum::ADModuleAlarm >(Arena* arena) {
  return Arena::CreateMessageInternal< ::AllFaultEnum::ADModuleAlarm >(arena);
}
template<> PROTOBUF_NOINLINE ::AllFaultEnum::VCIContactorFaultSend* Arena::CreateMaybeMessage< ::AllFaultEnum::VCIContactorFaultSend >(Arena* arena) {
  return Arena::CreateMessageInternal< ::AllFaultEnum::VCIContactorFaultSend >(arena);
}
template<> PROTOBUF_NOINLINE ::AllFaultEnum::PMMContactorFaultSend* Arena::CreateMaybeMessage< ::AllFaultEnum::PMMContactorFaultSend >(Arena* arena) {
  return Arena::CreateMessageInternal< ::AllFaultEnum::PMMContactorFaultSend >(arena);
}
template<> PROTOBUF_NOINLINE ::AllFaultEnum::OHPContactorFaultSend* Arena::CreateMaybeMessage< ::AllFaultEnum::OHPContactorFaultSend >(Arena* arena) {
  return Arena::CreateMessageInternal< ::AllFaultEnum::OHPContactorFaultSend >(arena);
}
template<> PROTOBUF_NOINLINE ::AllFaultEnum::DMCContactorFaultSend* Arena::CreateMaybeMessage< ::AllFaultEnum::DMCContactorFaultSend >(Arena* arena) {
  return Arena::CreateMessageInternal< ::AllFaultEnum::DMCContactorFaultSend >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
