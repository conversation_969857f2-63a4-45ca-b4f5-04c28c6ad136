syntax = "proto3";
package BMSinfo;

//充电类型
enum ChargingMode {
	DefaultChargingMode = 0x00;		//	缺省值
	VoltageStable = 0x01;			//	恒压充电
	CurrentStable = 0x02;			//	恒流充电
}

//BmsShakehands-握手阶段BMS信息
message BMSHandShake {
    float bmsVolMaxAllowed = 1;    // 最高允许充电总电压
    bytes gbtProtoVersion = 2;     // GBT27930协议版本号 
}

//BmsIdentify-辨识阶段BMS信息
message BMSVerification {
    uint32 batteryType = 1;           // 电池类型
    uint32 batterySN = 2;             // 电池组序号
    uint32 propertyRight = 3;         // 电池组产权标识
    uint32 machineVerifyResult = 4;   // 充电机辨识结果
    uint32 machineNumber = 5;         // 充电桩编号
    uint32 batterChargeCnt = 6;       // 电池组充电次数
    float capacityRated = 7;          // 整车动力蓄电池系统额定容量(AH)
    float voltageRated = 8;           // 整车动力蓄电池额定总电压
    bytes batProducer = 9;           // 电池生产厂商名称
    bytes batProduceDate = 10;       // 电池组生产日期
    bytes bmsVersion = 11;           // BMS软件版本号
    bytes bmsVIN = 12;               // BMS和车辆VIN
    bytes chargerArea = 13;          // 充电机所在区域
}

//BmsConfig-参数配置阶段BMS信息
message BMSConfig {
    float monoVolMaxAllowed = 1;      // 单体动力蓄电池最高允许充电电压
    float curAllowedMax = 2;          // 最高允许充电电流
    float totalNominalEnergy = 3;     // 动力蓄电池标称总能量 
    float volAllowedMax = 4;          // 最高允许充电总电压
    float tempAllowedMax = 5;         // 最高允许温度
    float startSOC = 6;               // 整车动力蓄电池荷电状态(SOC)
    float volBatNow = 7;              // 整车动力蓄电池当前电池电压
    float volChargerMax = 8;          // 充电机最高输出电压
    float volChargerMin = 9;          // 充电机最低输出电压
    float curChargerMax = 10;         // 充电机最大输出电流
    float curChargerMin = 11;         // 充电机最小输出电流
    uint32 bmsReady = 12;             // BMS就绪状态
    uint32 chargerReady = 13;         // 充电机就绪状态
}

//BmsChargeFinish-充电结束阶段BMS信息
message BMSChargingEnd {
    float endSOC = 1;                 //  中止荷电状态SOC(%)
    float monoBatVolMin = 2;          //  动力蓄电池单体最低电压
    float monoBatVolMax = 3;          //  动力蓄电池单体最高电压
    float batTempMin = 4;             //  动力蓄电池最低温度
    float batTempMax = 5;             //  动力蓄电池最高温度
    uint32 bmsStopReason = 6;         //  BMS中止充电原因
    uint32 bmsFaultReason = 7;        //  BMS中止充电故障原因
    uint32 bmsErrorReason = 8;        //  BMS中止充电错误原因
    uint32 chargerStopReason = 9;     //  充电机中止充电原因
    uint32 chargerFaultReason = 10;   //  充电机中止充电故障原因
    uint32 chargerErrorReason = 11;   //  充电机中止充电错误原因
}

//BmsCharging-充电阶段BMS信息
message BMSCharging {
    ChargingMode chargeMode = 1;        // 充电模式(恒压充/恒流充)
    uint32 heatMode = 2;                // 加热模式(0:正常模式,1:充电加热模式,2:加热模式,3:保留)
    uint32 totalChgTime = 3;            // 充电总时间(分钟)
    uint32 remainChgTime = 4;           // 估算剩余充电时间(分钟)
    uint32 monoBatVolMaxCode = 5;       // 单体电池最高电压所在编号
    uint32 monoBatVolMinCode = 6;       // 单体电池最低电压所在编号
    uint32 tempMaxCode = 7;             // 电池最高温度检测点编号
    uint32 tempMinCode = 8;             // 电池最低温度检测点编号
    uint32 volMaxGroupNum = 9;          // 最高单体动力蓄电池电压所在组号

    uint32 monoBatVolOver = 10;         // 单体动力蓄电池电压是否过高
    uint32 monoBatVolUnder = 11;        // 单体动力蓄电池电压是否过低
    uint32 socOver = 12;                // 整车动力蓄电池荷电状态SOC是否过高
    uint32 socUnder = 13;               // 整车动力蓄电池荷电状态SOC是否过低
    uint32 batCurOver = 14;             // 动力蓄电池充电是否过电流
    uint32 batTempOver = 15;            // 动力蓄电池温度是否过高
    uint32 insulationAbnormal = 16;     // 动力蓄电池绝缘状态是否绝缘
    uint32 outConnectAbnormal = 17;     // 动力蓄电池输出连接器连接状态是否连接
    uint32 bmsAllow = 18;               // BMS是否允许充电
    uint32 chargerAllow = 19;           // 充电机是否允许充电

    float socNow = 20;                  // 当前SOC
    float volDemand = 21;               // 需求电压
    float curDemand = 22;               // 需求电流
    float volMeasured = 23;             // 充电电压测量值
    float curMeasured = 24;             // 充电电流测量值
    float monoBatVolMax = 25;           // 单体电池最高电压
    float monoBatVolMin = 26;           // 单体电池最低电压
    float tempMax = 27;                 // 电池最高温度
    float tempMin = 28;                 // 电池最低温度
}

//BMS 超时阶段报文
message BMSTimeout {
    uint32 bmsErrorFrame = 1;           // BMS错误报文 
    uint32 chargerErrorFrame = 2;       // 充电机错误报文
}

//BMS超时类型枚举
enum BMSTimeoutEnum {
	DefaultTimeout = 0x00;			//  缺省值
	BHM = 0x01;						//	超时报文
	BRM = 0x02;						//	超时报文
	BCP = 0x03;						//	超时报文
	BCS = 0x04;						//	超时报文
	BCL = 0x05;						//	超时报文
	BST = 0x06;						//	超时报文
	BSD = 0x07;						//	超时报文
}

// BMS重连事件
message BMSReconnectEvent {
    uint32 timeoutState = 1;            // 超时事件(ACK 发生/NACK 未发生)
    BMSTimeoutEnum bmsTimeoutType = 2;  // 超时类型(BHM/BRM/BCP/BCS/BCL/BST/BSD)
    uint32 reconnectCnt = 3;            // 超时次数
    uint32 nextState = 4;               // 下一个重连状态 
}
//车桩交互状态机
enum ChargeState {
    DefaultState = 0x00;            // 缺省值
    ChargeCreate = 0x1;             // 充电线程创建
    Handshake = 0x2;                // 握手阶段
    Identify = 0x3;                 // 辨识阶段
    ParamConfig = 0x4;              // 参数配置阶段
    Charing = 0x5;                  // 充电中
    ChgStatic = 0x6;                // 充电统计阶段
    ChgTimeout = 0x7;               // 报文超时阶段
    ChgEnd = 0x8;                   // 充电结束阶段
    ChgDestory = 0x9;               // 充电线程销毁
}
//  2015 bms信息
message bms2015Msg{
    ChargeState bmsState = 3;						//	车桩交互状态
    BMSHandShake BmshandShakeM = 4;					//	握手阶段
    BMSVerification BmsVerifyM = 5;					//	辨识阶段
    BMSConfig BmsConfigM = 6;						//	参数配置阶段
    BMSCharging BmsChargingM = 7;					//	充电中
    BMSChargingEnd BmsChargeFinishM = 8;			//	充电结束阶段
}

