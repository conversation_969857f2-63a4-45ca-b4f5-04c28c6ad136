// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GCU_BMS_INFO.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_GCU_5fBMS_5fINFO_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_GCU_5fBMS_5fINFO_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3017000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3017003 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_GCU_5fBMS_5fINFO_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_GCU_5fBMS_5fINFO_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[8]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GCU_5fBMS_5fINFO_2eproto;
namespace BMSinfo {
class BMSCharging;
struct BMSChargingDefaultTypeInternal;
extern BMSChargingDefaultTypeInternal _BMSCharging_default_instance_;
class BMSChargingEnd;
struct BMSChargingEndDefaultTypeInternal;
extern BMSChargingEndDefaultTypeInternal _BMSChargingEnd_default_instance_;
class BMSConfig;
struct BMSConfigDefaultTypeInternal;
extern BMSConfigDefaultTypeInternal _BMSConfig_default_instance_;
class BMSHandShake;
struct BMSHandShakeDefaultTypeInternal;
extern BMSHandShakeDefaultTypeInternal _BMSHandShake_default_instance_;
class BMSReconnectEvent;
struct BMSReconnectEventDefaultTypeInternal;
extern BMSReconnectEventDefaultTypeInternal _BMSReconnectEvent_default_instance_;
class BMSTimeout;
struct BMSTimeoutDefaultTypeInternal;
extern BMSTimeoutDefaultTypeInternal _BMSTimeout_default_instance_;
class BMSVerification;
struct BMSVerificationDefaultTypeInternal;
extern BMSVerificationDefaultTypeInternal _BMSVerification_default_instance_;
class bms2015Msg;
struct bms2015MsgDefaultTypeInternal;
extern bms2015MsgDefaultTypeInternal _bms2015Msg_default_instance_;
}  // namespace BMSinfo
PROTOBUF_NAMESPACE_OPEN
template<> ::BMSinfo::BMSCharging* Arena::CreateMaybeMessage<::BMSinfo::BMSCharging>(Arena*);
template<> ::BMSinfo::BMSChargingEnd* Arena::CreateMaybeMessage<::BMSinfo::BMSChargingEnd>(Arena*);
template<> ::BMSinfo::BMSConfig* Arena::CreateMaybeMessage<::BMSinfo::BMSConfig>(Arena*);
template<> ::BMSinfo::BMSHandShake* Arena::CreateMaybeMessage<::BMSinfo::BMSHandShake>(Arena*);
template<> ::BMSinfo::BMSReconnectEvent* Arena::CreateMaybeMessage<::BMSinfo::BMSReconnectEvent>(Arena*);
template<> ::BMSinfo::BMSTimeout* Arena::CreateMaybeMessage<::BMSinfo::BMSTimeout>(Arena*);
template<> ::BMSinfo::BMSVerification* Arena::CreateMaybeMessage<::BMSinfo::BMSVerification>(Arena*);
template<> ::BMSinfo::bms2015Msg* Arena::CreateMaybeMessage<::BMSinfo::bms2015Msg>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace BMSinfo {

enum ChargingMode : int {
  DefaultChargingMode = 0,
  VoltageStable = 1,
  CurrentStable = 2,
  ChargingMode_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  ChargingMode_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool ChargingMode_IsValid(int value);
constexpr ChargingMode ChargingMode_MIN = DefaultChargingMode;
constexpr ChargingMode ChargingMode_MAX = CurrentStable;
constexpr int ChargingMode_ARRAYSIZE = ChargingMode_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ChargingMode_descriptor();
template<typename T>
inline const std::string& ChargingMode_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ChargingMode>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ChargingMode_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ChargingMode_descriptor(), enum_t_value);
}
inline bool ChargingMode_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ChargingMode* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ChargingMode>(
    ChargingMode_descriptor(), name, value);
}
enum BMSTimeoutEnum : int {
  DefaultTimeout = 0,
  BHM = 1,
  BRM = 2,
  BCP = 3,
  BCS = 4,
  BCL = 5,
  BST = 6,
  BSD = 7,
  BMSTimeoutEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  BMSTimeoutEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool BMSTimeoutEnum_IsValid(int value);
constexpr BMSTimeoutEnum BMSTimeoutEnum_MIN = DefaultTimeout;
constexpr BMSTimeoutEnum BMSTimeoutEnum_MAX = BSD;
constexpr int BMSTimeoutEnum_ARRAYSIZE = BMSTimeoutEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* BMSTimeoutEnum_descriptor();
template<typename T>
inline const std::string& BMSTimeoutEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, BMSTimeoutEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function BMSTimeoutEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    BMSTimeoutEnum_descriptor(), enum_t_value);
}
inline bool BMSTimeoutEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, BMSTimeoutEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<BMSTimeoutEnum>(
    BMSTimeoutEnum_descriptor(), name, value);
}
enum ChargeState : int {
  DefaultState = 0,
  ChargeCreate = 1,
  Handshake = 2,
  Identify = 3,
  ParamConfig = 4,
  Charing = 5,
  ChgStatic = 6,
  ChgTimeout = 7,
  ChgEnd = 8,
  ChgDestory = 9,
  ChargeState_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  ChargeState_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool ChargeState_IsValid(int value);
constexpr ChargeState ChargeState_MIN = DefaultState;
constexpr ChargeState ChargeState_MAX = ChgDestory;
constexpr int ChargeState_ARRAYSIZE = ChargeState_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ChargeState_descriptor();
template<typename T>
inline const std::string& ChargeState_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ChargeState>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ChargeState_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ChargeState_descriptor(), enum_t_value);
}
inline bool ChargeState_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ChargeState* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ChargeState>(
    ChargeState_descriptor(), name, value);
}
// ===================================================================

class BMSHandShake final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:BMSinfo.BMSHandShake) */ {
 public:
  inline BMSHandShake() : BMSHandShake(nullptr) {}
  ~BMSHandShake() override;
  explicit constexpr BMSHandShake(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BMSHandShake(const BMSHandShake& from);
  BMSHandShake(BMSHandShake&& from) noexcept
    : BMSHandShake() {
    *this = ::std::move(from);
  }

  inline BMSHandShake& operator=(const BMSHandShake& from) {
    CopyFrom(from);
    return *this;
  }
  inline BMSHandShake& operator=(BMSHandShake&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BMSHandShake& default_instance() {
    return *internal_default_instance();
  }
  static inline const BMSHandShake* internal_default_instance() {
    return reinterpret_cast<const BMSHandShake*>(
               &_BMSHandShake_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(BMSHandShake& a, BMSHandShake& b) {
    a.Swap(&b);
  }
  inline void Swap(BMSHandShake* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BMSHandShake* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BMSHandShake* New() const final {
    return new BMSHandShake();
  }

  BMSHandShake* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BMSHandShake>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BMSHandShake& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const BMSHandShake& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BMSHandShake* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "BMSinfo.BMSHandShake";
  }
  protected:
  explicit BMSHandShake(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGbtProtoVersionFieldNumber = 2,
    kBmsVolMaxAllowedFieldNumber = 1,
  };
  // bytes gbtProtoVersion = 2;
  void clear_gbtprotoversion();
  const std::string& gbtprotoversion() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_gbtprotoversion(ArgT0&& arg0, ArgT... args);
  std::string* mutable_gbtprotoversion();
  PROTOBUF_MUST_USE_RESULT std::string* release_gbtprotoversion();
  void set_allocated_gbtprotoversion(std::string* gbtprotoversion);
  private:
  const std::string& _internal_gbtprotoversion() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_gbtprotoversion(const std::string& value);
  std::string* _internal_mutable_gbtprotoversion();
  public:

  // float bmsVolMaxAllowed = 1;
  void clear_bmsvolmaxallowed();
  float bmsvolmaxallowed() const;
  void set_bmsvolmaxallowed(float value);
  private:
  float _internal_bmsvolmaxallowed() const;
  void _internal_set_bmsvolmaxallowed(float value);
  public:

  // @@protoc_insertion_point(class_scope:BMSinfo.BMSHandShake)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr gbtprotoversion_;
  float bmsvolmaxallowed_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fBMS_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class BMSVerification final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:BMSinfo.BMSVerification) */ {
 public:
  inline BMSVerification() : BMSVerification(nullptr) {}
  ~BMSVerification() override;
  explicit constexpr BMSVerification(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BMSVerification(const BMSVerification& from);
  BMSVerification(BMSVerification&& from) noexcept
    : BMSVerification() {
    *this = ::std::move(from);
  }

  inline BMSVerification& operator=(const BMSVerification& from) {
    CopyFrom(from);
    return *this;
  }
  inline BMSVerification& operator=(BMSVerification&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BMSVerification& default_instance() {
    return *internal_default_instance();
  }
  static inline const BMSVerification* internal_default_instance() {
    return reinterpret_cast<const BMSVerification*>(
               &_BMSVerification_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(BMSVerification& a, BMSVerification& b) {
    a.Swap(&b);
  }
  inline void Swap(BMSVerification* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BMSVerification* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BMSVerification* New() const final {
    return new BMSVerification();
  }

  BMSVerification* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BMSVerification>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BMSVerification& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const BMSVerification& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BMSVerification* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "BMSinfo.BMSVerification";
  }
  protected:
  explicit BMSVerification(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBatProducerFieldNumber = 9,
    kBatProduceDateFieldNumber = 10,
    kBmsVersionFieldNumber = 11,
    kBmsVINFieldNumber = 12,
    kChargerAreaFieldNumber = 13,
    kBatteryTypeFieldNumber = 1,
    kBatterySNFieldNumber = 2,
    kPropertyRightFieldNumber = 3,
    kMachineVerifyResultFieldNumber = 4,
    kMachineNumberFieldNumber = 5,
    kBatterChargeCntFieldNumber = 6,
    kCapacityRatedFieldNumber = 7,
    kVoltageRatedFieldNumber = 8,
  };
  // bytes batProducer = 9;
  void clear_batproducer();
  const std::string& batproducer() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_batproducer(ArgT0&& arg0, ArgT... args);
  std::string* mutable_batproducer();
  PROTOBUF_MUST_USE_RESULT std::string* release_batproducer();
  void set_allocated_batproducer(std::string* batproducer);
  private:
  const std::string& _internal_batproducer() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_batproducer(const std::string& value);
  std::string* _internal_mutable_batproducer();
  public:

  // bytes batProduceDate = 10;
  void clear_batproducedate();
  const std::string& batproducedate() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_batproducedate(ArgT0&& arg0, ArgT... args);
  std::string* mutable_batproducedate();
  PROTOBUF_MUST_USE_RESULT std::string* release_batproducedate();
  void set_allocated_batproducedate(std::string* batproducedate);
  private:
  const std::string& _internal_batproducedate() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_batproducedate(const std::string& value);
  std::string* _internal_mutable_batproducedate();
  public:

  // bytes bmsVersion = 11;
  void clear_bmsversion();
  const std::string& bmsversion() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_bmsversion(ArgT0&& arg0, ArgT... args);
  std::string* mutable_bmsversion();
  PROTOBUF_MUST_USE_RESULT std::string* release_bmsversion();
  void set_allocated_bmsversion(std::string* bmsversion);
  private:
  const std::string& _internal_bmsversion() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_bmsversion(const std::string& value);
  std::string* _internal_mutable_bmsversion();
  public:

  // bytes bmsVIN = 12;
  void clear_bmsvin();
  const std::string& bmsvin() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_bmsvin(ArgT0&& arg0, ArgT... args);
  std::string* mutable_bmsvin();
  PROTOBUF_MUST_USE_RESULT std::string* release_bmsvin();
  void set_allocated_bmsvin(std::string* bmsvin);
  private:
  const std::string& _internal_bmsvin() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_bmsvin(const std::string& value);
  std::string* _internal_mutable_bmsvin();
  public:

  // bytes chargerArea = 13;
  void clear_chargerarea();
  const std::string& chargerarea() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_chargerarea(ArgT0&& arg0, ArgT... args);
  std::string* mutable_chargerarea();
  PROTOBUF_MUST_USE_RESULT std::string* release_chargerarea();
  void set_allocated_chargerarea(std::string* chargerarea);
  private:
  const std::string& _internal_chargerarea() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_chargerarea(const std::string& value);
  std::string* _internal_mutable_chargerarea();
  public:

  // uint32 batteryType = 1;
  void clear_batterytype();
  ::PROTOBUF_NAMESPACE_ID::uint32 batterytype() const;
  void set_batterytype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_batterytype() const;
  void _internal_set_batterytype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 batterySN = 2;
  void clear_batterysn();
  ::PROTOBUF_NAMESPACE_ID::uint32 batterysn() const;
  void set_batterysn(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_batterysn() const;
  void _internal_set_batterysn(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 propertyRight = 3;
  void clear_propertyright();
  ::PROTOBUF_NAMESPACE_ID::uint32 propertyright() const;
  void set_propertyright(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_propertyright() const;
  void _internal_set_propertyright(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 machineVerifyResult = 4;
  void clear_machineverifyresult();
  ::PROTOBUF_NAMESPACE_ID::uint32 machineverifyresult() const;
  void set_machineverifyresult(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_machineverifyresult() const;
  void _internal_set_machineverifyresult(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 machineNumber = 5;
  void clear_machinenumber();
  ::PROTOBUF_NAMESPACE_ID::uint32 machinenumber() const;
  void set_machinenumber(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_machinenumber() const;
  void _internal_set_machinenumber(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 batterChargeCnt = 6;
  void clear_batterchargecnt();
  ::PROTOBUF_NAMESPACE_ID::uint32 batterchargecnt() const;
  void set_batterchargecnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_batterchargecnt() const;
  void _internal_set_batterchargecnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // float capacityRated = 7;
  void clear_capacityrated();
  float capacityrated() const;
  void set_capacityrated(float value);
  private:
  float _internal_capacityrated() const;
  void _internal_set_capacityrated(float value);
  public:

  // float voltageRated = 8;
  void clear_voltagerated();
  float voltagerated() const;
  void set_voltagerated(float value);
  private:
  float _internal_voltagerated() const;
  void _internal_set_voltagerated(float value);
  public:

  // @@protoc_insertion_point(class_scope:BMSinfo.BMSVerification)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr batproducer_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr batproducedate_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr bmsversion_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr bmsvin_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr chargerarea_;
  ::PROTOBUF_NAMESPACE_ID::uint32 batterytype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 batterysn_;
  ::PROTOBUF_NAMESPACE_ID::uint32 propertyright_;
  ::PROTOBUF_NAMESPACE_ID::uint32 machineverifyresult_;
  ::PROTOBUF_NAMESPACE_ID::uint32 machinenumber_;
  ::PROTOBUF_NAMESPACE_ID::uint32 batterchargecnt_;
  float capacityrated_;
  float voltagerated_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fBMS_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class BMSConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:BMSinfo.BMSConfig) */ {
 public:
  inline BMSConfig() : BMSConfig(nullptr) {}
  ~BMSConfig() override;
  explicit constexpr BMSConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BMSConfig(const BMSConfig& from);
  BMSConfig(BMSConfig&& from) noexcept
    : BMSConfig() {
    *this = ::std::move(from);
  }

  inline BMSConfig& operator=(const BMSConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline BMSConfig& operator=(BMSConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BMSConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const BMSConfig* internal_default_instance() {
    return reinterpret_cast<const BMSConfig*>(
               &_BMSConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(BMSConfig& a, BMSConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(BMSConfig* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BMSConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BMSConfig* New() const final {
    return new BMSConfig();
  }

  BMSConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BMSConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BMSConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const BMSConfig& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BMSConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "BMSinfo.BMSConfig";
  }
  protected:
  explicit BMSConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMonoVolMaxAllowedFieldNumber = 1,
    kCurAllowedMaxFieldNumber = 2,
    kTotalNominalEnergyFieldNumber = 3,
    kVolAllowedMaxFieldNumber = 4,
    kTempAllowedMaxFieldNumber = 5,
    kStartSOCFieldNumber = 6,
    kVolBatNowFieldNumber = 7,
    kVolChargerMaxFieldNumber = 8,
    kVolChargerMinFieldNumber = 9,
    kCurChargerMaxFieldNumber = 10,
    kCurChargerMinFieldNumber = 11,
    kBmsReadyFieldNumber = 12,
    kChargerReadyFieldNumber = 13,
  };
  // float monoVolMaxAllowed = 1;
  void clear_monovolmaxallowed();
  float monovolmaxallowed() const;
  void set_monovolmaxallowed(float value);
  private:
  float _internal_monovolmaxallowed() const;
  void _internal_set_monovolmaxallowed(float value);
  public:

  // float curAllowedMax = 2;
  void clear_curallowedmax();
  float curallowedmax() const;
  void set_curallowedmax(float value);
  private:
  float _internal_curallowedmax() const;
  void _internal_set_curallowedmax(float value);
  public:

  // float totalNominalEnergy = 3;
  void clear_totalnominalenergy();
  float totalnominalenergy() const;
  void set_totalnominalenergy(float value);
  private:
  float _internal_totalnominalenergy() const;
  void _internal_set_totalnominalenergy(float value);
  public:

  // float volAllowedMax = 4;
  void clear_volallowedmax();
  float volallowedmax() const;
  void set_volallowedmax(float value);
  private:
  float _internal_volallowedmax() const;
  void _internal_set_volallowedmax(float value);
  public:

  // float tempAllowedMax = 5;
  void clear_tempallowedmax();
  float tempallowedmax() const;
  void set_tempallowedmax(float value);
  private:
  float _internal_tempallowedmax() const;
  void _internal_set_tempallowedmax(float value);
  public:

  // float startSOC = 6;
  void clear_startsoc();
  float startsoc() const;
  void set_startsoc(float value);
  private:
  float _internal_startsoc() const;
  void _internal_set_startsoc(float value);
  public:

  // float volBatNow = 7;
  void clear_volbatnow();
  float volbatnow() const;
  void set_volbatnow(float value);
  private:
  float _internal_volbatnow() const;
  void _internal_set_volbatnow(float value);
  public:

  // float volChargerMax = 8;
  void clear_volchargermax();
  float volchargermax() const;
  void set_volchargermax(float value);
  private:
  float _internal_volchargermax() const;
  void _internal_set_volchargermax(float value);
  public:

  // float volChargerMin = 9;
  void clear_volchargermin();
  float volchargermin() const;
  void set_volchargermin(float value);
  private:
  float _internal_volchargermin() const;
  void _internal_set_volchargermin(float value);
  public:

  // float curChargerMax = 10;
  void clear_curchargermax();
  float curchargermax() const;
  void set_curchargermax(float value);
  private:
  float _internal_curchargermax() const;
  void _internal_set_curchargermax(float value);
  public:

  // float curChargerMin = 11;
  void clear_curchargermin();
  float curchargermin() const;
  void set_curchargermin(float value);
  private:
  float _internal_curchargermin() const;
  void _internal_set_curchargermin(float value);
  public:

  // uint32 bmsReady = 12;
  void clear_bmsready();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsready() const;
  void set_bmsready(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsready() const;
  void _internal_set_bmsready(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 chargerReady = 13;
  void clear_chargerready();
  ::PROTOBUF_NAMESPACE_ID::uint32 chargerready() const;
  void set_chargerready(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chargerready() const;
  void _internal_set_chargerready(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:BMSinfo.BMSConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float monovolmaxallowed_;
  float curallowedmax_;
  float totalnominalenergy_;
  float volallowedmax_;
  float tempallowedmax_;
  float startsoc_;
  float volbatnow_;
  float volchargermax_;
  float volchargermin_;
  float curchargermax_;
  float curchargermin_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsready_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chargerready_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fBMS_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class BMSChargingEnd final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:BMSinfo.BMSChargingEnd) */ {
 public:
  inline BMSChargingEnd() : BMSChargingEnd(nullptr) {}
  ~BMSChargingEnd() override;
  explicit constexpr BMSChargingEnd(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BMSChargingEnd(const BMSChargingEnd& from);
  BMSChargingEnd(BMSChargingEnd&& from) noexcept
    : BMSChargingEnd() {
    *this = ::std::move(from);
  }

  inline BMSChargingEnd& operator=(const BMSChargingEnd& from) {
    CopyFrom(from);
    return *this;
  }
  inline BMSChargingEnd& operator=(BMSChargingEnd&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BMSChargingEnd& default_instance() {
    return *internal_default_instance();
  }
  static inline const BMSChargingEnd* internal_default_instance() {
    return reinterpret_cast<const BMSChargingEnd*>(
               &_BMSChargingEnd_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(BMSChargingEnd& a, BMSChargingEnd& b) {
    a.Swap(&b);
  }
  inline void Swap(BMSChargingEnd* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BMSChargingEnd* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BMSChargingEnd* New() const final {
    return new BMSChargingEnd();
  }

  BMSChargingEnd* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BMSChargingEnd>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BMSChargingEnd& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const BMSChargingEnd& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BMSChargingEnd* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "BMSinfo.BMSChargingEnd";
  }
  protected:
  explicit BMSChargingEnd(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEndSOCFieldNumber = 1,
    kMonoBatVolMinFieldNumber = 2,
    kMonoBatVolMaxFieldNumber = 3,
    kBatTempMinFieldNumber = 4,
    kBatTempMaxFieldNumber = 5,
    kBmsStopReasonFieldNumber = 6,
    kBmsFaultReasonFieldNumber = 7,
    kBmsErrorReasonFieldNumber = 8,
    kChargerStopReasonFieldNumber = 9,
    kChargerFaultReasonFieldNumber = 10,
    kChargerErrorReasonFieldNumber = 11,
  };
  // float endSOC = 1;
  void clear_endsoc();
  float endsoc() const;
  void set_endsoc(float value);
  private:
  float _internal_endsoc() const;
  void _internal_set_endsoc(float value);
  public:

  // float monoBatVolMin = 2;
  void clear_monobatvolmin();
  float monobatvolmin() const;
  void set_monobatvolmin(float value);
  private:
  float _internal_monobatvolmin() const;
  void _internal_set_monobatvolmin(float value);
  public:

  // float monoBatVolMax = 3;
  void clear_monobatvolmax();
  float monobatvolmax() const;
  void set_monobatvolmax(float value);
  private:
  float _internal_monobatvolmax() const;
  void _internal_set_monobatvolmax(float value);
  public:

  // float batTempMin = 4;
  void clear_battempmin();
  float battempmin() const;
  void set_battempmin(float value);
  private:
  float _internal_battempmin() const;
  void _internal_set_battempmin(float value);
  public:

  // float batTempMax = 5;
  void clear_battempmax();
  float battempmax() const;
  void set_battempmax(float value);
  private:
  float _internal_battempmax() const;
  void _internal_set_battempmax(float value);
  public:

  // uint32 bmsStopReason = 6;
  void clear_bmsstopreason();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsstopreason() const;
  void set_bmsstopreason(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsstopreason() const;
  void _internal_set_bmsstopreason(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 bmsFaultReason = 7;
  void clear_bmsfaultreason();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsfaultreason() const;
  void set_bmsfaultreason(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsfaultreason() const;
  void _internal_set_bmsfaultreason(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 bmsErrorReason = 8;
  void clear_bmserrorreason();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmserrorreason() const;
  void set_bmserrorreason(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmserrorreason() const;
  void _internal_set_bmserrorreason(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 chargerStopReason = 9;
  void clear_chargerstopreason();
  ::PROTOBUF_NAMESPACE_ID::uint32 chargerstopreason() const;
  void set_chargerstopreason(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chargerstopreason() const;
  void _internal_set_chargerstopreason(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 chargerFaultReason = 10;
  void clear_chargerfaultreason();
  ::PROTOBUF_NAMESPACE_ID::uint32 chargerfaultreason() const;
  void set_chargerfaultreason(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chargerfaultreason() const;
  void _internal_set_chargerfaultreason(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 chargerErrorReason = 11;
  void clear_chargererrorreason();
  ::PROTOBUF_NAMESPACE_ID::uint32 chargererrorreason() const;
  void set_chargererrorreason(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chargererrorreason() const;
  void _internal_set_chargererrorreason(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:BMSinfo.BMSChargingEnd)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float endsoc_;
  float monobatvolmin_;
  float monobatvolmax_;
  float battempmin_;
  float battempmax_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsstopreason_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsfaultreason_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmserrorreason_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chargerstopreason_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chargerfaultreason_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chargererrorreason_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fBMS_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class BMSCharging final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:BMSinfo.BMSCharging) */ {
 public:
  inline BMSCharging() : BMSCharging(nullptr) {}
  ~BMSCharging() override;
  explicit constexpr BMSCharging(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BMSCharging(const BMSCharging& from);
  BMSCharging(BMSCharging&& from) noexcept
    : BMSCharging() {
    *this = ::std::move(from);
  }

  inline BMSCharging& operator=(const BMSCharging& from) {
    CopyFrom(from);
    return *this;
  }
  inline BMSCharging& operator=(BMSCharging&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BMSCharging& default_instance() {
    return *internal_default_instance();
  }
  static inline const BMSCharging* internal_default_instance() {
    return reinterpret_cast<const BMSCharging*>(
               &_BMSCharging_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(BMSCharging& a, BMSCharging& b) {
    a.Swap(&b);
  }
  inline void Swap(BMSCharging* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BMSCharging* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BMSCharging* New() const final {
    return new BMSCharging();
  }

  BMSCharging* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BMSCharging>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BMSCharging& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const BMSCharging& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BMSCharging* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "BMSinfo.BMSCharging";
  }
  protected:
  explicit BMSCharging(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kChargeModeFieldNumber = 1,
    kHeatModeFieldNumber = 2,
    kTotalChgTimeFieldNumber = 3,
    kRemainChgTimeFieldNumber = 4,
    kMonoBatVolMaxCodeFieldNumber = 5,
    kMonoBatVolMinCodeFieldNumber = 6,
    kTempMaxCodeFieldNumber = 7,
    kTempMinCodeFieldNumber = 8,
    kVolMaxGroupNumFieldNumber = 9,
    kMonoBatVolOverFieldNumber = 10,
    kMonoBatVolUnderFieldNumber = 11,
    kSocOverFieldNumber = 12,
    kSocUnderFieldNumber = 13,
    kBatCurOverFieldNumber = 14,
    kBatTempOverFieldNumber = 15,
    kInsulationAbnormalFieldNumber = 16,
    kOutConnectAbnormalFieldNumber = 17,
    kBmsAllowFieldNumber = 18,
    kChargerAllowFieldNumber = 19,
    kSocNowFieldNumber = 20,
    kVolDemandFieldNumber = 21,
    kCurDemandFieldNumber = 22,
    kVolMeasuredFieldNumber = 23,
    kCurMeasuredFieldNumber = 24,
    kMonoBatVolMaxFieldNumber = 25,
    kMonoBatVolMinFieldNumber = 26,
    kTempMaxFieldNumber = 27,
    kTempMinFieldNumber = 28,
  };
  // .BMSinfo.ChargingMode chargeMode = 1;
  void clear_chargemode();
  ::BMSinfo::ChargingMode chargemode() const;
  void set_chargemode(::BMSinfo::ChargingMode value);
  private:
  ::BMSinfo::ChargingMode _internal_chargemode() const;
  void _internal_set_chargemode(::BMSinfo::ChargingMode value);
  public:

  // uint32 heatMode = 2;
  void clear_heatmode();
  ::PROTOBUF_NAMESPACE_ID::uint32 heatmode() const;
  void set_heatmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_heatmode() const;
  void _internal_set_heatmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 totalChgTime = 3;
  void clear_totalchgtime();
  ::PROTOBUF_NAMESPACE_ID::uint32 totalchgtime() const;
  void set_totalchgtime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_totalchgtime() const;
  void _internal_set_totalchgtime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 remainChgTime = 4;
  void clear_remainchgtime();
  ::PROTOBUF_NAMESPACE_ID::uint32 remainchgtime() const;
  void set_remainchgtime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_remainchgtime() const;
  void _internal_set_remainchgtime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 monoBatVolMaxCode = 5;
  void clear_monobatvolmaxcode();
  ::PROTOBUF_NAMESPACE_ID::uint32 monobatvolmaxcode() const;
  void set_monobatvolmaxcode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_monobatvolmaxcode() const;
  void _internal_set_monobatvolmaxcode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 monoBatVolMinCode = 6;
  void clear_monobatvolmincode();
  ::PROTOBUF_NAMESPACE_ID::uint32 monobatvolmincode() const;
  void set_monobatvolmincode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_monobatvolmincode() const;
  void _internal_set_monobatvolmincode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 tempMaxCode = 7;
  void clear_tempmaxcode();
  ::PROTOBUF_NAMESPACE_ID::uint32 tempmaxcode() const;
  void set_tempmaxcode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_tempmaxcode() const;
  void _internal_set_tempmaxcode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 tempMinCode = 8;
  void clear_tempmincode();
  ::PROTOBUF_NAMESPACE_ID::uint32 tempmincode() const;
  void set_tempmincode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_tempmincode() const;
  void _internal_set_tempmincode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 volMaxGroupNum = 9;
  void clear_volmaxgroupnum();
  ::PROTOBUF_NAMESPACE_ID::uint32 volmaxgroupnum() const;
  void set_volmaxgroupnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_volmaxgroupnum() const;
  void _internal_set_volmaxgroupnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 monoBatVolOver = 10;
  void clear_monobatvolover();
  ::PROTOBUF_NAMESPACE_ID::uint32 monobatvolover() const;
  void set_monobatvolover(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_monobatvolover() const;
  void _internal_set_monobatvolover(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 monoBatVolUnder = 11;
  void clear_monobatvolunder();
  ::PROTOBUF_NAMESPACE_ID::uint32 monobatvolunder() const;
  void set_monobatvolunder(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_monobatvolunder() const;
  void _internal_set_monobatvolunder(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 socOver = 12;
  void clear_socover();
  ::PROTOBUF_NAMESPACE_ID::uint32 socover() const;
  void set_socover(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_socover() const;
  void _internal_set_socover(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 socUnder = 13;
  void clear_socunder();
  ::PROTOBUF_NAMESPACE_ID::uint32 socunder() const;
  void set_socunder(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_socunder() const;
  void _internal_set_socunder(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 batCurOver = 14;
  void clear_batcurover();
  ::PROTOBUF_NAMESPACE_ID::uint32 batcurover() const;
  void set_batcurover(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_batcurover() const;
  void _internal_set_batcurover(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 batTempOver = 15;
  void clear_battempover();
  ::PROTOBUF_NAMESPACE_ID::uint32 battempover() const;
  void set_battempover(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_battempover() const;
  void _internal_set_battempover(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 insulationAbnormal = 16;
  void clear_insulationabnormal();
  ::PROTOBUF_NAMESPACE_ID::uint32 insulationabnormal() const;
  void set_insulationabnormal(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_insulationabnormal() const;
  void _internal_set_insulationabnormal(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 outConnectAbnormal = 17;
  void clear_outconnectabnormal();
  ::PROTOBUF_NAMESPACE_ID::uint32 outconnectabnormal() const;
  void set_outconnectabnormal(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_outconnectabnormal() const;
  void _internal_set_outconnectabnormal(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 bmsAllow = 18;
  void clear_bmsallow();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsallow() const;
  void set_bmsallow(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsallow() const;
  void _internal_set_bmsallow(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 chargerAllow = 19;
  void clear_chargerallow();
  ::PROTOBUF_NAMESPACE_ID::uint32 chargerallow() const;
  void set_chargerallow(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chargerallow() const;
  void _internal_set_chargerallow(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // float socNow = 20;
  void clear_socnow();
  float socnow() const;
  void set_socnow(float value);
  private:
  float _internal_socnow() const;
  void _internal_set_socnow(float value);
  public:

  // float volDemand = 21;
  void clear_voldemand();
  float voldemand() const;
  void set_voldemand(float value);
  private:
  float _internal_voldemand() const;
  void _internal_set_voldemand(float value);
  public:

  // float curDemand = 22;
  void clear_curdemand();
  float curdemand() const;
  void set_curdemand(float value);
  private:
  float _internal_curdemand() const;
  void _internal_set_curdemand(float value);
  public:

  // float volMeasured = 23;
  void clear_volmeasured();
  float volmeasured() const;
  void set_volmeasured(float value);
  private:
  float _internal_volmeasured() const;
  void _internal_set_volmeasured(float value);
  public:

  // float curMeasured = 24;
  void clear_curmeasured();
  float curmeasured() const;
  void set_curmeasured(float value);
  private:
  float _internal_curmeasured() const;
  void _internal_set_curmeasured(float value);
  public:

  // float monoBatVolMax = 25;
  void clear_monobatvolmax();
  float monobatvolmax() const;
  void set_monobatvolmax(float value);
  private:
  float _internal_monobatvolmax() const;
  void _internal_set_monobatvolmax(float value);
  public:

  // float monoBatVolMin = 26;
  void clear_monobatvolmin();
  float monobatvolmin() const;
  void set_monobatvolmin(float value);
  private:
  float _internal_monobatvolmin() const;
  void _internal_set_monobatvolmin(float value);
  public:

  // float tempMax = 27;
  void clear_tempmax();
  float tempmax() const;
  void set_tempmax(float value);
  private:
  float _internal_tempmax() const;
  void _internal_set_tempmax(float value);
  public:

  // float tempMin = 28;
  void clear_tempmin();
  float tempmin() const;
  void set_tempmin(float value);
  private:
  float _internal_tempmin() const;
  void _internal_set_tempmin(float value);
  public:

  // @@protoc_insertion_point(class_scope:BMSinfo.BMSCharging)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int chargemode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 heatmode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 totalchgtime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 remainchgtime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 monobatvolmaxcode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 monobatvolmincode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 tempmaxcode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 tempmincode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 volmaxgroupnum_;
  ::PROTOBUF_NAMESPACE_ID::uint32 monobatvolover_;
  ::PROTOBUF_NAMESPACE_ID::uint32 monobatvolunder_;
  ::PROTOBUF_NAMESPACE_ID::uint32 socover_;
  ::PROTOBUF_NAMESPACE_ID::uint32 socunder_;
  ::PROTOBUF_NAMESPACE_ID::uint32 batcurover_;
  ::PROTOBUF_NAMESPACE_ID::uint32 battempover_;
  ::PROTOBUF_NAMESPACE_ID::uint32 insulationabnormal_;
  ::PROTOBUF_NAMESPACE_ID::uint32 outconnectabnormal_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsallow_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chargerallow_;
  float socnow_;
  float voldemand_;
  float curdemand_;
  float volmeasured_;
  float curmeasured_;
  float monobatvolmax_;
  float monobatvolmin_;
  float tempmax_;
  float tempmin_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fBMS_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class BMSTimeout final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:BMSinfo.BMSTimeout) */ {
 public:
  inline BMSTimeout() : BMSTimeout(nullptr) {}
  ~BMSTimeout() override;
  explicit constexpr BMSTimeout(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BMSTimeout(const BMSTimeout& from);
  BMSTimeout(BMSTimeout&& from) noexcept
    : BMSTimeout() {
    *this = ::std::move(from);
  }

  inline BMSTimeout& operator=(const BMSTimeout& from) {
    CopyFrom(from);
    return *this;
  }
  inline BMSTimeout& operator=(BMSTimeout&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BMSTimeout& default_instance() {
    return *internal_default_instance();
  }
  static inline const BMSTimeout* internal_default_instance() {
    return reinterpret_cast<const BMSTimeout*>(
               &_BMSTimeout_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(BMSTimeout& a, BMSTimeout& b) {
    a.Swap(&b);
  }
  inline void Swap(BMSTimeout* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BMSTimeout* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BMSTimeout* New() const final {
    return new BMSTimeout();
  }

  BMSTimeout* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BMSTimeout>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BMSTimeout& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const BMSTimeout& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BMSTimeout* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "BMSinfo.BMSTimeout";
  }
  protected:
  explicit BMSTimeout(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBmsErrorFrameFieldNumber = 1,
    kChargerErrorFrameFieldNumber = 2,
  };
  // uint32 bmsErrorFrame = 1;
  void clear_bmserrorframe();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmserrorframe() const;
  void set_bmserrorframe(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmserrorframe() const;
  void _internal_set_bmserrorframe(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 chargerErrorFrame = 2;
  void clear_chargererrorframe();
  ::PROTOBUF_NAMESPACE_ID::uint32 chargererrorframe() const;
  void set_chargererrorframe(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_chargererrorframe() const;
  void _internal_set_chargererrorframe(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:BMSinfo.BMSTimeout)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmserrorframe_;
  ::PROTOBUF_NAMESPACE_ID::uint32 chargererrorframe_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fBMS_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class BMSReconnectEvent final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:BMSinfo.BMSReconnectEvent) */ {
 public:
  inline BMSReconnectEvent() : BMSReconnectEvent(nullptr) {}
  ~BMSReconnectEvent() override;
  explicit constexpr BMSReconnectEvent(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BMSReconnectEvent(const BMSReconnectEvent& from);
  BMSReconnectEvent(BMSReconnectEvent&& from) noexcept
    : BMSReconnectEvent() {
    *this = ::std::move(from);
  }

  inline BMSReconnectEvent& operator=(const BMSReconnectEvent& from) {
    CopyFrom(from);
    return *this;
  }
  inline BMSReconnectEvent& operator=(BMSReconnectEvent&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BMSReconnectEvent& default_instance() {
    return *internal_default_instance();
  }
  static inline const BMSReconnectEvent* internal_default_instance() {
    return reinterpret_cast<const BMSReconnectEvent*>(
               &_BMSReconnectEvent_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(BMSReconnectEvent& a, BMSReconnectEvent& b) {
    a.Swap(&b);
  }
  inline void Swap(BMSReconnectEvent* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BMSReconnectEvent* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BMSReconnectEvent* New() const final {
    return new BMSReconnectEvent();
  }

  BMSReconnectEvent* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BMSReconnectEvent>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BMSReconnectEvent& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const BMSReconnectEvent& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BMSReconnectEvent* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "BMSinfo.BMSReconnectEvent";
  }
  protected:
  explicit BMSReconnectEvent(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTimeoutStateFieldNumber = 1,
    kBmsTimeoutTypeFieldNumber = 2,
    kReconnectCntFieldNumber = 3,
    kNextStateFieldNumber = 4,
  };
  // uint32 timeoutState = 1;
  void clear_timeoutstate();
  ::PROTOBUF_NAMESPACE_ID::uint32 timeoutstate() const;
  void set_timeoutstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_timeoutstate() const;
  void _internal_set_timeoutstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // .BMSinfo.BMSTimeoutEnum bmsTimeoutType = 2;
  void clear_bmstimeouttype();
  ::BMSinfo::BMSTimeoutEnum bmstimeouttype() const;
  void set_bmstimeouttype(::BMSinfo::BMSTimeoutEnum value);
  private:
  ::BMSinfo::BMSTimeoutEnum _internal_bmstimeouttype() const;
  void _internal_set_bmstimeouttype(::BMSinfo::BMSTimeoutEnum value);
  public:

  // uint32 reconnectCnt = 3;
  void clear_reconnectcnt();
  ::PROTOBUF_NAMESPACE_ID::uint32 reconnectcnt() const;
  void set_reconnectcnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_reconnectcnt() const;
  void _internal_set_reconnectcnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 nextState = 4;
  void clear_nextstate();
  ::PROTOBUF_NAMESPACE_ID::uint32 nextstate() const;
  void set_nextstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_nextstate() const;
  void _internal_set_nextstate(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:BMSinfo.BMSReconnectEvent)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 timeoutstate_;
  int bmstimeouttype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 reconnectcnt_;
  ::PROTOBUF_NAMESPACE_ID::uint32 nextstate_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fBMS_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class bms2015Msg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:BMSinfo.bms2015Msg) */ {
 public:
  inline bms2015Msg() : bms2015Msg(nullptr) {}
  ~bms2015Msg() override;
  explicit constexpr bms2015Msg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  bms2015Msg(const bms2015Msg& from);
  bms2015Msg(bms2015Msg&& from) noexcept
    : bms2015Msg() {
    *this = ::std::move(from);
  }

  inline bms2015Msg& operator=(const bms2015Msg& from) {
    CopyFrom(from);
    return *this;
  }
  inline bms2015Msg& operator=(bms2015Msg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const bms2015Msg& default_instance() {
    return *internal_default_instance();
  }
  static inline const bms2015Msg* internal_default_instance() {
    return reinterpret_cast<const bms2015Msg*>(
               &_bms2015Msg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(bms2015Msg& a, bms2015Msg& b) {
    a.Swap(&b);
  }
  inline void Swap(bms2015Msg* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(bms2015Msg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline bms2015Msg* New() const final {
    return new bms2015Msg();
  }

  bms2015Msg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<bms2015Msg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const bms2015Msg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const bms2015Msg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(bms2015Msg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "BMSinfo.bms2015Msg";
  }
  protected:
  explicit bms2015Msg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBmshandShakeMFieldNumber = 4,
    kBmsVerifyMFieldNumber = 5,
    kBmsConfigMFieldNumber = 6,
    kBmsChargingMFieldNumber = 7,
    kBmsChargeFinishMFieldNumber = 8,
    kBmsStateFieldNumber = 3,
  };
  // .BMSinfo.BMSHandShake BmshandShakeM = 4;
  bool has_bmshandshakem() const;
  private:
  bool _internal_has_bmshandshakem() const;
  public:
  void clear_bmshandshakem();
  const ::BMSinfo::BMSHandShake& bmshandshakem() const;
  PROTOBUF_MUST_USE_RESULT ::BMSinfo::BMSHandShake* release_bmshandshakem();
  ::BMSinfo::BMSHandShake* mutable_bmshandshakem();
  void set_allocated_bmshandshakem(::BMSinfo::BMSHandShake* bmshandshakem);
  private:
  const ::BMSinfo::BMSHandShake& _internal_bmshandshakem() const;
  ::BMSinfo::BMSHandShake* _internal_mutable_bmshandshakem();
  public:
  void unsafe_arena_set_allocated_bmshandshakem(
      ::BMSinfo::BMSHandShake* bmshandshakem);
  ::BMSinfo::BMSHandShake* unsafe_arena_release_bmshandshakem();

  // .BMSinfo.BMSVerification BmsVerifyM = 5;
  bool has_bmsverifym() const;
  private:
  bool _internal_has_bmsverifym() const;
  public:
  void clear_bmsverifym();
  const ::BMSinfo::BMSVerification& bmsverifym() const;
  PROTOBUF_MUST_USE_RESULT ::BMSinfo::BMSVerification* release_bmsverifym();
  ::BMSinfo::BMSVerification* mutable_bmsverifym();
  void set_allocated_bmsverifym(::BMSinfo::BMSVerification* bmsverifym);
  private:
  const ::BMSinfo::BMSVerification& _internal_bmsverifym() const;
  ::BMSinfo::BMSVerification* _internal_mutable_bmsverifym();
  public:
  void unsafe_arena_set_allocated_bmsverifym(
      ::BMSinfo::BMSVerification* bmsverifym);
  ::BMSinfo::BMSVerification* unsafe_arena_release_bmsverifym();

  // .BMSinfo.BMSConfig BmsConfigM = 6;
  bool has_bmsconfigm() const;
  private:
  bool _internal_has_bmsconfigm() const;
  public:
  void clear_bmsconfigm();
  const ::BMSinfo::BMSConfig& bmsconfigm() const;
  PROTOBUF_MUST_USE_RESULT ::BMSinfo::BMSConfig* release_bmsconfigm();
  ::BMSinfo::BMSConfig* mutable_bmsconfigm();
  void set_allocated_bmsconfigm(::BMSinfo::BMSConfig* bmsconfigm);
  private:
  const ::BMSinfo::BMSConfig& _internal_bmsconfigm() const;
  ::BMSinfo::BMSConfig* _internal_mutable_bmsconfigm();
  public:
  void unsafe_arena_set_allocated_bmsconfigm(
      ::BMSinfo::BMSConfig* bmsconfigm);
  ::BMSinfo::BMSConfig* unsafe_arena_release_bmsconfigm();

  // .BMSinfo.BMSCharging BmsChargingM = 7;
  bool has_bmschargingm() const;
  private:
  bool _internal_has_bmschargingm() const;
  public:
  void clear_bmschargingm();
  const ::BMSinfo::BMSCharging& bmschargingm() const;
  PROTOBUF_MUST_USE_RESULT ::BMSinfo::BMSCharging* release_bmschargingm();
  ::BMSinfo::BMSCharging* mutable_bmschargingm();
  void set_allocated_bmschargingm(::BMSinfo::BMSCharging* bmschargingm);
  private:
  const ::BMSinfo::BMSCharging& _internal_bmschargingm() const;
  ::BMSinfo::BMSCharging* _internal_mutable_bmschargingm();
  public:
  void unsafe_arena_set_allocated_bmschargingm(
      ::BMSinfo::BMSCharging* bmschargingm);
  ::BMSinfo::BMSCharging* unsafe_arena_release_bmschargingm();

  // .BMSinfo.BMSChargingEnd BmsChargeFinishM = 8;
  bool has_bmschargefinishm() const;
  private:
  bool _internal_has_bmschargefinishm() const;
  public:
  void clear_bmschargefinishm();
  const ::BMSinfo::BMSChargingEnd& bmschargefinishm() const;
  PROTOBUF_MUST_USE_RESULT ::BMSinfo::BMSChargingEnd* release_bmschargefinishm();
  ::BMSinfo::BMSChargingEnd* mutable_bmschargefinishm();
  void set_allocated_bmschargefinishm(::BMSinfo::BMSChargingEnd* bmschargefinishm);
  private:
  const ::BMSinfo::BMSChargingEnd& _internal_bmschargefinishm() const;
  ::BMSinfo::BMSChargingEnd* _internal_mutable_bmschargefinishm();
  public:
  void unsafe_arena_set_allocated_bmschargefinishm(
      ::BMSinfo::BMSChargingEnd* bmschargefinishm);
  ::BMSinfo::BMSChargingEnd* unsafe_arena_release_bmschargefinishm();

  // .BMSinfo.ChargeState bmsState = 3;
  void clear_bmsstate();
  ::BMSinfo::ChargeState bmsstate() const;
  void set_bmsstate(::BMSinfo::ChargeState value);
  private:
  ::BMSinfo::ChargeState _internal_bmsstate() const;
  void _internal_set_bmsstate(::BMSinfo::ChargeState value);
  public:

  // @@protoc_insertion_point(class_scope:BMSinfo.bms2015Msg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::BMSinfo::BMSHandShake* bmshandshakem_;
  ::BMSinfo::BMSVerification* bmsverifym_;
  ::BMSinfo::BMSConfig* bmsconfigm_;
  ::BMSinfo::BMSCharging* bmschargingm_;
  ::BMSinfo::BMSChargingEnd* bmschargefinishm_;
  int bmsstate_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fBMS_5fINFO_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// BMSHandShake

// float bmsVolMaxAllowed = 1;
inline void BMSHandShake::clear_bmsvolmaxallowed() {
  bmsvolmaxallowed_ = 0;
}
inline float BMSHandShake::_internal_bmsvolmaxallowed() const {
  return bmsvolmaxallowed_;
}
inline float BMSHandShake::bmsvolmaxallowed() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSHandShake.bmsVolMaxAllowed)
  return _internal_bmsvolmaxallowed();
}
inline void BMSHandShake::_internal_set_bmsvolmaxallowed(float value) {
  
  bmsvolmaxallowed_ = value;
}
inline void BMSHandShake::set_bmsvolmaxallowed(float value) {
  _internal_set_bmsvolmaxallowed(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSHandShake.bmsVolMaxAllowed)
}

// bytes gbtProtoVersion = 2;
inline void BMSHandShake::clear_gbtprotoversion() {
  gbtprotoversion_.ClearToEmpty();
}
inline const std::string& BMSHandShake::gbtprotoversion() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSHandShake.gbtProtoVersion)
  return _internal_gbtprotoversion();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void BMSHandShake::set_gbtprotoversion(ArgT0&& arg0, ArgT... args) {
 
 gbtprotoversion_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:BMSinfo.BMSHandShake.gbtProtoVersion)
}
inline std::string* BMSHandShake::mutable_gbtprotoversion() {
  std::string* _s = _internal_mutable_gbtprotoversion();
  // @@protoc_insertion_point(field_mutable:BMSinfo.BMSHandShake.gbtProtoVersion)
  return _s;
}
inline const std::string& BMSHandShake::_internal_gbtprotoversion() const {
  return gbtprotoversion_.Get();
}
inline void BMSHandShake::_internal_set_gbtprotoversion(const std::string& value) {
  
  gbtprotoversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* BMSHandShake::_internal_mutable_gbtprotoversion() {
  
  return gbtprotoversion_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* BMSHandShake::release_gbtprotoversion() {
  // @@protoc_insertion_point(field_release:BMSinfo.BMSHandShake.gbtProtoVersion)
  return gbtprotoversion_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void BMSHandShake::set_allocated_gbtprotoversion(std::string* gbtprotoversion) {
  if (gbtprotoversion != nullptr) {
    
  } else {
    
  }
  gbtprotoversion_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), gbtprotoversion,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:BMSinfo.BMSHandShake.gbtProtoVersion)
}

// -------------------------------------------------------------------

// BMSVerification

// uint32 batteryType = 1;
inline void BMSVerification::clear_batterytype() {
  batterytype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSVerification::_internal_batterytype() const {
  return batterytype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSVerification::batterytype() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSVerification.batteryType)
  return _internal_batterytype();
}
inline void BMSVerification::_internal_set_batterytype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  batterytype_ = value;
}
inline void BMSVerification::set_batterytype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_batterytype(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSVerification.batteryType)
}

// uint32 batterySN = 2;
inline void BMSVerification::clear_batterysn() {
  batterysn_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSVerification::_internal_batterysn() const {
  return batterysn_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSVerification::batterysn() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSVerification.batterySN)
  return _internal_batterysn();
}
inline void BMSVerification::_internal_set_batterysn(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  batterysn_ = value;
}
inline void BMSVerification::set_batterysn(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_batterysn(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSVerification.batterySN)
}

// uint32 propertyRight = 3;
inline void BMSVerification::clear_propertyright() {
  propertyright_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSVerification::_internal_propertyright() const {
  return propertyright_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSVerification::propertyright() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSVerification.propertyRight)
  return _internal_propertyright();
}
inline void BMSVerification::_internal_set_propertyright(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  propertyright_ = value;
}
inline void BMSVerification::set_propertyright(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_propertyright(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSVerification.propertyRight)
}

// uint32 machineVerifyResult = 4;
inline void BMSVerification::clear_machineverifyresult() {
  machineverifyresult_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSVerification::_internal_machineverifyresult() const {
  return machineverifyresult_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSVerification::machineverifyresult() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSVerification.machineVerifyResult)
  return _internal_machineverifyresult();
}
inline void BMSVerification::_internal_set_machineverifyresult(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  machineverifyresult_ = value;
}
inline void BMSVerification::set_machineverifyresult(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_machineverifyresult(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSVerification.machineVerifyResult)
}

// uint32 machineNumber = 5;
inline void BMSVerification::clear_machinenumber() {
  machinenumber_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSVerification::_internal_machinenumber() const {
  return machinenumber_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSVerification::machinenumber() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSVerification.machineNumber)
  return _internal_machinenumber();
}
inline void BMSVerification::_internal_set_machinenumber(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  machinenumber_ = value;
}
inline void BMSVerification::set_machinenumber(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_machinenumber(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSVerification.machineNumber)
}

// uint32 batterChargeCnt = 6;
inline void BMSVerification::clear_batterchargecnt() {
  batterchargecnt_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSVerification::_internal_batterchargecnt() const {
  return batterchargecnt_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSVerification::batterchargecnt() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSVerification.batterChargeCnt)
  return _internal_batterchargecnt();
}
inline void BMSVerification::_internal_set_batterchargecnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  batterchargecnt_ = value;
}
inline void BMSVerification::set_batterchargecnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_batterchargecnt(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSVerification.batterChargeCnt)
}

// float capacityRated = 7;
inline void BMSVerification::clear_capacityrated() {
  capacityrated_ = 0;
}
inline float BMSVerification::_internal_capacityrated() const {
  return capacityrated_;
}
inline float BMSVerification::capacityrated() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSVerification.capacityRated)
  return _internal_capacityrated();
}
inline void BMSVerification::_internal_set_capacityrated(float value) {
  
  capacityrated_ = value;
}
inline void BMSVerification::set_capacityrated(float value) {
  _internal_set_capacityrated(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSVerification.capacityRated)
}

// float voltageRated = 8;
inline void BMSVerification::clear_voltagerated() {
  voltagerated_ = 0;
}
inline float BMSVerification::_internal_voltagerated() const {
  return voltagerated_;
}
inline float BMSVerification::voltagerated() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSVerification.voltageRated)
  return _internal_voltagerated();
}
inline void BMSVerification::_internal_set_voltagerated(float value) {
  
  voltagerated_ = value;
}
inline void BMSVerification::set_voltagerated(float value) {
  _internal_set_voltagerated(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSVerification.voltageRated)
}

// bytes batProducer = 9;
inline void BMSVerification::clear_batproducer() {
  batproducer_.ClearToEmpty();
}
inline const std::string& BMSVerification::batproducer() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSVerification.batProducer)
  return _internal_batproducer();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void BMSVerification::set_batproducer(ArgT0&& arg0, ArgT... args) {
 
 batproducer_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:BMSinfo.BMSVerification.batProducer)
}
inline std::string* BMSVerification::mutable_batproducer() {
  std::string* _s = _internal_mutable_batproducer();
  // @@protoc_insertion_point(field_mutable:BMSinfo.BMSVerification.batProducer)
  return _s;
}
inline const std::string& BMSVerification::_internal_batproducer() const {
  return batproducer_.Get();
}
inline void BMSVerification::_internal_set_batproducer(const std::string& value) {
  
  batproducer_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* BMSVerification::_internal_mutable_batproducer() {
  
  return batproducer_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* BMSVerification::release_batproducer() {
  // @@protoc_insertion_point(field_release:BMSinfo.BMSVerification.batProducer)
  return batproducer_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void BMSVerification::set_allocated_batproducer(std::string* batproducer) {
  if (batproducer != nullptr) {
    
  } else {
    
  }
  batproducer_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), batproducer,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:BMSinfo.BMSVerification.batProducer)
}

// bytes batProduceDate = 10;
inline void BMSVerification::clear_batproducedate() {
  batproducedate_.ClearToEmpty();
}
inline const std::string& BMSVerification::batproducedate() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSVerification.batProduceDate)
  return _internal_batproducedate();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void BMSVerification::set_batproducedate(ArgT0&& arg0, ArgT... args) {
 
 batproducedate_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:BMSinfo.BMSVerification.batProduceDate)
}
inline std::string* BMSVerification::mutable_batproducedate() {
  std::string* _s = _internal_mutable_batproducedate();
  // @@protoc_insertion_point(field_mutable:BMSinfo.BMSVerification.batProduceDate)
  return _s;
}
inline const std::string& BMSVerification::_internal_batproducedate() const {
  return batproducedate_.Get();
}
inline void BMSVerification::_internal_set_batproducedate(const std::string& value) {
  
  batproducedate_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* BMSVerification::_internal_mutable_batproducedate() {
  
  return batproducedate_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* BMSVerification::release_batproducedate() {
  // @@protoc_insertion_point(field_release:BMSinfo.BMSVerification.batProduceDate)
  return batproducedate_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void BMSVerification::set_allocated_batproducedate(std::string* batproducedate) {
  if (batproducedate != nullptr) {
    
  } else {
    
  }
  batproducedate_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), batproducedate,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:BMSinfo.BMSVerification.batProduceDate)
}

// bytes bmsVersion = 11;
inline void BMSVerification::clear_bmsversion() {
  bmsversion_.ClearToEmpty();
}
inline const std::string& BMSVerification::bmsversion() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSVerification.bmsVersion)
  return _internal_bmsversion();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void BMSVerification::set_bmsversion(ArgT0&& arg0, ArgT... args) {
 
 bmsversion_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:BMSinfo.BMSVerification.bmsVersion)
}
inline std::string* BMSVerification::mutable_bmsversion() {
  std::string* _s = _internal_mutable_bmsversion();
  // @@protoc_insertion_point(field_mutable:BMSinfo.BMSVerification.bmsVersion)
  return _s;
}
inline const std::string& BMSVerification::_internal_bmsversion() const {
  return bmsversion_.Get();
}
inline void BMSVerification::_internal_set_bmsversion(const std::string& value) {
  
  bmsversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* BMSVerification::_internal_mutable_bmsversion() {
  
  return bmsversion_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* BMSVerification::release_bmsversion() {
  // @@protoc_insertion_point(field_release:BMSinfo.BMSVerification.bmsVersion)
  return bmsversion_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void BMSVerification::set_allocated_bmsversion(std::string* bmsversion) {
  if (bmsversion != nullptr) {
    
  } else {
    
  }
  bmsversion_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), bmsversion,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:BMSinfo.BMSVerification.bmsVersion)
}

// bytes bmsVIN = 12;
inline void BMSVerification::clear_bmsvin() {
  bmsvin_.ClearToEmpty();
}
inline const std::string& BMSVerification::bmsvin() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSVerification.bmsVIN)
  return _internal_bmsvin();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void BMSVerification::set_bmsvin(ArgT0&& arg0, ArgT... args) {
 
 bmsvin_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:BMSinfo.BMSVerification.bmsVIN)
}
inline std::string* BMSVerification::mutable_bmsvin() {
  std::string* _s = _internal_mutable_bmsvin();
  // @@protoc_insertion_point(field_mutable:BMSinfo.BMSVerification.bmsVIN)
  return _s;
}
inline const std::string& BMSVerification::_internal_bmsvin() const {
  return bmsvin_.Get();
}
inline void BMSVerification::_internal_set_bmsvin(const std::string& value) {
  
  bmsvin_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* BMSVerification::_internal_mutable_bmsvin() {
  
  return bmsvin_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* BMSVerification::release_bmsvin() {
  // @@protoc_insertion_point(field_release:BMSinfo.BMSVerification.bmsVIN)
  return bmsvin_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void BMSVerification::set_allocated_bmsvin(std::string* bmsvin) {
  if (bmsvin != nullptr) {
    
  } else {
    
  }
  bmsvin_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), bmsvin,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:BMSinfo.BMSVerification.bmsVIN)
}

// bytes chargerArea = 13;
inline void BMSVerification::clear_chargerarea() {
  chargerarea_.ClearToEmpty();
}
inline const std::string& BMSVerification::chargerarea() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSVerification.chargerArea)
  return _internal_chargerarea();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void BMSVerification::set_chargerarea(ArgT0&& arg0, ArgT... args) {
 
 chargerarea_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:BMSinfo.BMSVerification.chargerArea)
}
inline std::string* BMSVerification::mutable_chargerarea() {
  std::string* _s = _internal_mutable_chargerarea();
  // @@protoc_insertion_point(field_mutable:BMSinfo.BMSVerification.chargerArea)
  return _s;
}
inline const std::string& BMSVerification::_internal_chargerarea() const {
  return chargerarea_.Get();
}
inline void BMSVerification::_internal_set_chargerarea(const std::string& value) {
  
  chargerarea_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* BMSVerification::_internal_mutable_chargerarea() {
  
  return chargerarea_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* BMSVerification::release_chargerarea() {
  // @@protoc_insertion_point(field_release:BMSinfo.BMSVerification.chargerArea)
  return chargerarea_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void BMSVerification::set_allocated_chargerarea(std::string* chargerarea) {
  if (chargerarea != nullptr) {
    
  } else {
    
  }
  chargerarea_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), chargerarea,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:BMSinfo.BMSVerification.chargerArea)
}

// -------------------------------------------------------------------

// BMSConfig

// float monoVolMaxAllowed = 1;
inline void BMSConfig::clear_monovolmaxallowed() {
  monovolmaxallowed_ = 0;
}
inline float BMSConfig::_internal_monovolmaxallowed() const {
  return monovolmaxallowed_;
}
inline float BMSConfig::monovolmaxallowed() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSConfig.monoVolMaxAllowed)
  return _internal_monovolmaxallowed();
}
inline void BMSConfig::_internal_set_monovolmaxallowed(float value) {
  
  monovolmaxallowed_ = value;
}
inline void BMSConfig::set_monovolmaxallowed(float value) {
  _internal_set_monovolmaxallowed(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSConfig.monoVolMaxAllowed)
}

// float curAllowedMax = 2;
inline void BMSConfig::clear_curallowedmax() {
  curallowedmax_ = 0;
}
inline float BMSConfig::_internal_curallowedmax() const {
  return curallowedmax_;
}
inline float BMSConfig::curallowedmax() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSConfig.curAllowedMax)
  return _internal_curallowedmax();
}
inline void BMSConfig::_internal_set_curallowedmax(float value) {
  
  curallowedmax_ = value;
}
inline void BMSConfig::set_curallowedmax(float value) {
  _internal_set_curallowedmax(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSConfig.curAllowedMax)
}

// float totalNominalEnergy = 3;
inline void BMSConfig::clear_totalnominalenergy() {
  totalnominalenergy_ = 0;
}
inline float BMSConfig::_internal_totalnominalenergy() const {
  return totalnominalenergy_;
}
inline float BMSConfig::totalnominalenergy() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSConfig.totalNominalEnergy)
  return _internal_totalnominalenergy();
}
inline void BMSConfig::_internal_set_totalnominalenergy(float value) {
  
  totalnominalenergy_ = value;
}
inline void BMSConfig::set_totalnominalenergy(float value) {
  _internal_set_totalnominalenergy(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSConfig.totalNominalEnergy)
}

// float volAllowedMax = 4;
inline void BMSConfig::clear_volallowedmax() {
  volallowedmax_ = 0;
}
inline float BMSConfig::_internal_volallowedmax() const {
  return volallowedmax_;
}
inline float BMSConfig::volallowedmax() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSConfig.volAllowedMax)
  return _internal_volallowedmax();
}
inline void BMSConfig::_internal_set_volallowedmax(float value) {
  
  volallowedmax_ = value;
}
inline void BMSConfig::set_volallowedmax(float value) {
  _internal_set_volallowedmax(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSConfig.volAllowedMax)
}

// float tempAllowedMax = 5;
inline void BMSConfig::clear_tempallowedmax() {
  tempallowedmax_ = 0;
}
inline float BMSConfig::_internal_tempallowedmax() const {
  return tempallowedmax_;
}
inline float BMSConfig::tempallowedmax() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSConfig.tempAllowedMax)
  return _internal_tempallowedmax();
}
inline void BMSConfig::_internal_set_tempallowedmax(float value) {
  
  tempallowedmax_ = value;
}
inline void BMSConfig::set_tempallowedmax(float value) {
  _internal_set_tempallowedmax(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSConfig.tempAllowedMax)
}

// float startSOC = 6;
inline void BMSConfig::clear_startsoc() {
  startsoc_ = 0;
}
inline float BMSConfig::_internal_startsoc() const {
  return startsoc_;
}
inline float BMSConfig::startsoc() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSConfig.startSOC)
  return _internal_startsoc();
}
inline void BMSConfig::_internal_set_startsoc(float value) {
  
  startsoc_ = value;
}
inline void BMSConfig::set_startsoc(float value) {
  _internal_set_startsoc(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSConfig.startSOC)
}

// float volBatNow = 7;
inline void BMSConfig::clear_volbatnow() {
  volbatnow_ = 0;
}
inline float BMSConfig::_internal_volbatnow() const {
  return volbatnow_;
}
inline float BMSConfig::volbatnow() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSConfig.volBatNow)
  return _internal_volbatnow();
}
inline void BMSConfig::_internal_set_volbatnow(float value) {
  
  volbatnow_ = value;
}
inline void BMSConfig::set_volbatnow(float value) {
  _internal_set_volbatnow(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSConfig.volBatNow)
}

// float volChargerMax = 8;
inline void BMSConfig::clear_volchargermax() {
  volchargermax_ = 0;
}
inline float BMSConfig::_internal_volchargermax() const {
  return volchargermax_;
}
inline float BMSConfig::volchargermax() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSConfig.volChargerMax)
  return _internal_volchargermax();
}
inline void BMSConfig::_internal_set_volchargermax(float value) {
  
  volchargermax_ = value;
}
inline void BMSConfig::set_volchargermax(float value) {
  _internal_set_volchargermax(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSConfig.volChargerMax)
}

// float volChargerMin = 9;
inline void BMSConfig::clear_volchargermin() {
  volchargermin_ = 0;
}
inline float BMSConfig::_internal_volchargermin() const {
  return volchargermin_;
}
inline float BMSConfig::volchargermin() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSConfig.volChargerMin)
  return _internal_volchargermin();
}
inline void BMSConfig::_internal_set_volchargermin(float value) {
  
  volchargermin_ = value;
}
inline void BMSConfig::set_volchargermin(float value) {
  _internal_set_volchargermin(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSConfig.volChargerMin)
}

// float curChargerMax = 10;
inline void BMSConfig::clear_curchargermax() {
  curchargermax_ = 0;
}
inline float BMSConfig::_internal_curchargermax() const {
  return curchargermax_;
}
inline float BMSConfig::curchargermax() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSConfig.curChargerMax)
  return _internal_curchargermax();
}
inline void BMSConfig::_internal_set_curchargermax(float value) {
  
  curchargermax_ = value;
}
inline void BMSConfig::set_curchargermax(float value) {
  _internal_set_curchargermax(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSConfig.curChargerMax)
}

// float curChargerMin = 11;
inline void BMSConfig::clear_curchargermin() {
  curchargermin_ = 0;
}
inline float BMSConfig::_internal_curchargermin() const {
  return curchargermin_;
}
inline float BMSConfig::curchargermin() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSConfig.curChargerMin)
  return _internal_curchargermin();
}
inline void BMSConfig::_internal_set_curchargermin(float value) {
  
  curchargermin_ = value;
}
inline void BMSConfig::set_curchargermin(float value) {
  _internal_set_curchargermin(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSConfig.curChargerMin)
}

// uint32 bmsReady = 12;
inline void BMSConfig::clear_bmsready() {
  bmsready_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSConfig::_internal_bmsready() const {
  return bmsready_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSConfig::bmsready() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSConfig.bmsReady)
  return _internal_bmsready();
}
inline void BMSConfig::_internal_set_bmsready(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsready_ = value;
}
inline void BMSConfig::set_bmsready(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsready(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSConfig.bmsReady)
}

// uint32 chargerReady = 13;
inline void BMSConfig::clear_chargerready() {
  chargerready_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSConfig::_internal_chargerready() const {
  return chargerready_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSConfig::chargerready() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSConfig.chargerReady)
  return _internal_chargerready();
}
inline void BMSConfig::_internal_set_chargerready(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chargerready_ = value;
}
inline void BMSConfig::set_chargerready(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chargerready(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSConfig.chargerReady)
}

// -------------------------------------------------------------------

// BMSChargingEnd

// float endSOC = 1;
inline void BMSChargingEnd::clear_endsoc() {
  endsoc_ = 0;
}
inline float BMSChargingEnd::_internal_endsoc() const {
  return endsoc_;
}
inline float BMSChargingEnd::endsoc() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSChargingEnd.endSOC)
  return _internal_endsoc();
}
inline void BMSChargingEnd::_internal_set_endsoc(float value) {
  
  endsoc_ = value;
}
inline void BMSChargingEnd::set_endsoc(float value) {
  _internal_set_endsoc(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSChargingEnd.endSOC)
}

// float monoBatVolMin = 2;
inline void BMSChargingEnd::clear_monobatvolmin() {
  monobatvolmin_ = 0;
}
inline float BMSChargingEnd::_internal_monobatvolmin() const {
  return monobatvolmin_;
}
inline float BMSChargingEnd::monobatvolmin() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSChargingEnd.monoBatVolMin)
  return _internal_monobatvolmin();
}
inline void BMSChargingEnd::_internal_set_monobatvolmin(float value) {
  
  monobatvolmin_ = value;
}
inline void BMSChargingEnd::set_monobatvolmin(float value) {
  _internal_set_monobatvolmin(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSChargingEnd.monoBatVolMin)
}

// float monoBatVolMax = 3;
inline void BMSChargingEnd::clear_monobatvolmax() {
  monobatvolmax_ = 0;
}
inline float BMSChargingEnd::_internal_monobatvolmax() const {
  return monobatvolmax_;
}
inline float BMSChargingEnd::monobatvolmax() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSChargingEnd.monoBatVolMax)
  return _internal_monobatvolmax();
}
inline void BMSChargingEnd::_internal_set_monobatvolmax(float value) {
  
  monobatvolmax_ = value;
}
inline void BMSChargingEnd::set_monobatvolmax(float value) {
  _internal_set_monobatvolmax(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSChargingEnd.monoBatVolMax)
}

// float batTempMin = 4;
inline void BMSChargingEnd::clear_battempmin() {
  battempmin_ = 0;
}
inline float BMSChargingEnd::_internal_battempmin() const {
  return battempmin_;
}
inline float BMSChargingEnd::battempmin() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSChargingEnd.batTempMin)
  return _internal_battempmin();
}
inline void BMSChargingEnd::_internal_set_battempmin(float value) {
  
  battempmin_ = value;
}
inline void BMSChargingEnd::set_battempmin(float value) {
  _internal_set_battempmin(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSChargingEnd.batTempMin)
}

// float batTempMax = 5;
inline void BMSChargingEnd::clear_battempmax() {
  battempmax_ = 0;
}
inline float BMSChargingEnd::_internal_battempmax() const {
  return battempmax_;
}
inline float BMSChargingEnd::battempmax() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSChargingEnd.batTempMax)
  return _internal_battempmax();
}
inline void BMSChargingEnd::_internal_set_battempmax(float value) {
  
  battempmax_ = value;
}
inline void BMSChargingEnd::set_battempmax(float value) {
  _internal_set_battempmax(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSChargingEnd.batTempMax)
}

// uint32 bmsStopReason = 6;
inline void BMSChargingEnd::clear_bmsstopreason() {
  bmsstopreason_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::_internal_bmsstopreason() const {
  return bmsstopreason_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::bmsstopreason() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSChargingEnd.bmsStopReason)
  return _internal_bmsstopreason();
}
inline void BMSChargingEnd::_internal_set_bmsstopreason(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsstopreason_ = value;
}
inline void BMSChargingEnd::set_bmsstopreason(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsstopreason(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSChargingEnd.bmsStopReason)
}

// uint32 bmsFaultReason = 7;
inline void BMSChargingEnd::clear_bmsfaultreason() {
  bmsfaultreason_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::_internal_bmsfaultreason() const {
  return bmsfaultreason_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::bmsfaultreason() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSChargingEnd.bmsFaultReason)
  return _internal_bmsfaultreason();
}
inline void BMSChargingEnd::_internal_set_bmsfaultreason(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsfaultreason_ = value;
}
inline void BMSChargingEnd::set_bmsfaultreason(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsfaultreason(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSChargingEnd.bmsFaultReason)
}

// uint32 bmsErrorReason = 8;
inline void BMSChargingEnd::clear_bmserrorreason() {
  bmserrorreason_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::_internal_bmserrorreason() const {
  return bmserrorreason_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::bmserrorreason() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSChargingEnd.bmsErrorReason)
  return _internal_bmserrorreason();
}
inline void BMSChargingEnd::_internal_set_bmserrorreason(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmserrorreason_ = value;
}
inline void BMSChargingEnd::set_bmserrorreason(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmserrorreason(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSChargingEnd.bmsErrorReason)
}

// uint32 chargerStopReason = 9;
inline void BMSChargingEnd::clear_chargerstopreason() {
  chargerstopreason_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::_internal_chargerstopreason() const {
  return chargerstopreason_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::chargerstopreason() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSChargingEnd.chargerStopReason)
  return _internal_chargerstopreason();
}
inline void BMSChargingEnd::_internal_set_chargerstopreason(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chargerstopreason_ = value;
}
inline void BMSChargingEnd::set_chargerstopreason(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chargerstopreason(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSChargingEnd.chargerStopReason)
}

// uint32 chargerFaultReason = 10;
inline void BMSChargingEnd::clear_chargerfaultreason() {
  chargerfaultreason_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::_internal_chargerfaultreason() const {
  return chargerfaultreason_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::chargerfaultreason() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSChargingEnd.chargerFaultReason)
  return _internal_chargerfaultreason();
}
inline void BMSChargingEnd::_internal_set_chargerfaultreason(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chargerfaultreason_ = value;
}
inline void BMSChargingEnd::set_chargerfaultreason(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chargerfaultreason(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSChargingEnd.chargerFaultReason)
}

// uint32 chargerErrorReason = 11;
inline void BMSChargingEnd::clear_chargererrorreason() {
  chargererrorreason_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::_internal_chargererrorreason() const {
  return chargererrorreason_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSChargingEnd::chargererrorreason() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSChargingEnd.chargerErrorReason)
  return _internal_chargererrorreason();
}
inline void BMSChargingEnd::_internal_set_chargererrorreason(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chargererrorreason_ = value;
}
inline void BMSChargingEnd::set_chargererrorreason(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chargererrorreason(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSChargingEnd.chargerErrorReason)
}

// -------------------------------------------------------------------

// BMSCharging

// .BMSinfo.ChargingMode chargeMode = 1;
inline void BMSCharging::clear_chargemode() {
  chargemode_ = 0;
}
inline ::BMSinfo::ChargingMode BMSCharging::_internal_chargemode() const {
  return static_cast< ::BMSinfo::ChargingMode >(chargemode_);
}
inline ::BMSinfo::ChargingMode BMSCharging::chargemode() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.chargeMode)
  return _internal_chargemode();
}
inline void BMSCharging::_internal_set_chargemode(::BMSinfo::ChargingMode value) {
  
  chargemode_ = value;
}
inline void BMSCharging::set_chargemode(::BMSinfo::ChargingMode value) {
  _internal_set_chargemode(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.chargeMode)
}

// uint32 heatMode = 2;
inline void BMSCharging::clear_heatmode() {
  heatmode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_heatmode() const {
  return heatmode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::heatmode() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.heatMode)
  return _internal_heatmode();
}
inline void BMSCharging::_internal_set_heatmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  heatmode_ = value;
}
inline void BMSCharging::set_heatmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_heatmode(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.heatMode)
}

// uint32 totalChgTime = 3;
inline void BMSCharging::clear_totalchgtime() {
  totalchgtime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_totalchgtime() const {
  return totalchgtime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::totalchgtime() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.totalChgTime)
  return _internal_totalchgtime();
}
inline void BMSCharging::_internal_set_totalchgtime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  totalchgtime_ = value;
}
inline void BMSCharging::set_totalchgtime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_totalchgtime(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.totalChgTime)
}

// uint32 remainChgTime = 4;
inline void BMSCharging::clear_remainchgtime() {
  remainchgtime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_remainchgtime() const {
  return remainchgtime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::remainchgtime() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.remainChgTime)
  return _internal_remainchgtime();
}
inline void BMSCharging::_internal_set_remainchgtime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  remainchgtime_ = value;
}
inline void BMSCharging::set_remainchgtime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_remainchgtime(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.remainChgTime)
}

// uint32 monoBatVolMaxCode = 5;
inline void BMSCharging::clear_monobatvolmaxcode() {
  monobatvolmaxcode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_monobatvolmaxcode() const {
  return monobatvolmaxcode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::monobatvolmaxcode() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.monoBatVolMaxCode)
  return _internal_monobatvolmaxcode();
}
inline void BMSCharging::_internal_set_monobatvolmaxcode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  monobatvolmaxcode_ = value;
}
inline void BMSCharging::set_monobatvolmaxcode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_monobatvolmaxcode(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.monoBatVolMaxCode)
}

// uint32 monoBatVolMinCode = 6;
inline void BMSCharging::clear_monobatvolmincode() {
  monobatvolmincode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_monobatvolmincode() const {
  return monobatvolmincode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::monobatvolmincode() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.monoBatVolMinCode)
  return _internal_monobatvolmincode();
}
inline void BMSCharging::_internal_set_monobatvolmincode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  monobatvolmincode_ = value;
}
inline void BMSCharging::set_monobatvolmincode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_monobatvolmincode(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.monoBatVolMinCode)
}

// uint32 tempMaxCode = 7;
inline void BMSCharging::clear_tempmaxcode() {
  tempmaxcode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_tempmaxcode() const {
  return tempmaxcode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::tempmaxcode() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.tempMaxCode)
  return _internal_tempmaxcode();
}
inline void BMSCharging::_internal_set_tempmaxcode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  tempmaxcode_ = value;
}
inline void BMSCharging::set_tempmaxcode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_tempmaxcode(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.tempMaxCode)
}

// uint32 tempMinCode = 8;
inline void BMSCharging::clear_tempmincode() {
  tempmincode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_tempmincode() const {
  return tempmincode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::tempmincode() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.tempMinCode)
  return _internal_tempmincode();
}
inline void BMSCharging::_internal_set_tempmincode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  tempmincode_ = value;
}
inline void BMSCharging::set_tempmincode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_tempmincode(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.tempMinCode)
}

// uint32 volMaxGroupNum = 9;
inline void BMSCharging::clear_volmaxgroupnum() {
  volmaxgroupnum_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_volmaxgroupnum() const {
  return volmaxgroupnum_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::volmaxgroupnum() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.volMaxGroupNum)
  return _internal_volmaxgroupnum();
}
inline void BMSCharging::_internal_set_volmaxgroupnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  volmaxgroupnum_ = value;
}
inline void BMSCharging::set_volmaxgroupnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_volmaxgroupnum(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.volMaxGroupNum)
}

// uint32 monoBatVolOver = 10;
inline void BMSCharging::clear_monobatvolover() {
  monobatvolover_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_monobatvolover() const {
  return monobatvolover_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::monobatvolover() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.monoBatVolOver)
  return _internal_monobatvolover();
}
inline void BMSCharging::_internal_set_monobatvolover(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  monobatvolover_ = value;
}
inline void BMSCharging::set_monobatvolover(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_monobatvolover(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.monoBatVolOver)
}

// uint32 monoBatVolUnder = 11;
inline void BMSCharging::clear_monobatvolunder() {
  monobatvolunder_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_monobatvolunder() const {
  return monobatvolunder_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::monobatvolunder() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.monoBatVolUnder)
  return _internal_monobatvolunder();
}
inline void BMSCharging::_internal_set_monobatvolunder(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  monobatvolunder_ = value;
}
inline void BMSCharging::set_monobatvolunder(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_monobatvolunder(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.monoBatVolUnder)
}

// uint32 socOver = 12;
inline void BMSCharging::clear_socover() {
  socover_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_socover() const {
  return socover_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::socover() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.socOver)
  return _internal_socover();
}
inline void BMSCharging::_internal_set_socover(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  socover_ = value;
}
inline void BMSCharging::set_socover(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_socover(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.socOver)
}

// uint32 socUnder = 13;
inline void BMSCharging::clear_socunder() {
  socunder_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_socunder() const {
  return socunder_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::socunder() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.socUnder)
  return _internal_socunder();
}
inline void BMSCharging::_internal_set_socunder(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  socunder_ = value;
}
inline void BMSCharging::set_socunder(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_socunder(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.socUnder)
}

// uint32 batCurOver = 14;
inline void BMSCharging::clear_batcurover() {
  batcurover_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_batcurover() const {
  return batcurover_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::batcurover() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.batCurOver)
  return _internal_batcurover();
}
inline void BMSCharging::_internal_set_batcurover(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  batcurover_ = value;
}
inline void BMSCharging::set_batcurover(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_batcurover(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.batCurOver)
}

// uint32 batTempOver = 15;
inline void BMSCharging::clear_battempover() {
  battempover_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_battempover() const {
  return battempover_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::battempover() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.batTempOver)
  return _internal_battempover();
}
inline void BMSCharging::_internal_set_battempover(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  battempover_ = value;
}
inline void BMSCharging::set_battempover(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_battempover(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.batTempOver)
}

// uint32 insulationAbnormal = 16;
inline void BMSCharging::clear_insulationabnormal() {
  insulationabnormal_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_insulationabnormal() const {
  return insulationabnormal_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::insulationabnormal() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.insulationAbnormal)
  return _internal_insulationabnormal();
}
inline void BMSCharging::_internal_set_insulationabnormal(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  insulationabnormal_ = value;
}
inline void BMSCharging::set_insulationabnormal(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_insulationabnormal(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.insulationAbnormal)
}

// uint32 outConnectAbnormal = 17;
inline void BMSCharging::clear_outconnectabnormal() {
  outconnectabnormal_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_outconnectabnormal() const {
  return outconnectabnormal_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::outconnectabnormal() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.outConnectAbnormal)
  return _internal_outconnectabnormal();
}
inline void BMSCharging::_internal_set_outconnectabnormal(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  outconnectabnormal_ = value;
}
inline void BMSCharging::set_outconnectabnormal(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_outconnectabnormal(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.outConnectAbnormal)
}

// uint32 bmsAllow = 18;
inline void BMSCharging::clear_bmsallow() {
  bmsallow_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_bmsallow() const {
  return bmsallow_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::bmsallow() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.bmsAllow)
  return _internal_bmsallow();
}
inline void BMSCharging::_internal_set_bmsallow(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsallow_ = value;
}
inline void BMSCharging::set_bmsallow(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsallow(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.bmsAllow)
}

// uint32 chargerAllow = 19;
inline void BMSCharging::clear_chargerallow() {
  chargerallow_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::_internal_chargerallow() const {
  return chargerallow_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSCharging::chargerallow() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.chargerAllow)
  return _internal_chargerallow();
}
inline void BMSCharging::_internal_set_chargerallow(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chargerallow_ = value;
}
inline void BMSCharging::set_chargerallow(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chargerallow(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.chargerAllow)
}

// float socNow = 20;
inline void BMSCharging::clear_socnow() {
  socnow_ = 0;
}
inline float BMSCharging::_internal_socnow() const {
  return socnow_;
}
inline float BMSCharging::socnow() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.socNow)
  return _internal_socnow();
}
inline void BMSCharging::_internal_set_socnow(float value) {
  
  socnow_ = value;
}
inline void BMSCharging::set_socnow(float value) {
  _internal_set_socnow(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.socNow)
}

// float volDemand = 21;
inline void BMSCharging::clear_voldemand() {
  voldemand_ = 0;
}
inline float BMSCharging::_internal_voldemand() const {
  return voldemand_;
}
inline float BMSCharging::voldemand() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.volDemand)
  return _internal_voldemand();
}
inline void BMSCharging::_internal_set_voldemand(float value) {
  
  voldemand_ = value;
}
inline void BMSCharging::set_voldemand(float value) {
  _internal_set_voldemand(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.volDemand)
}

// float curDemand = 22;
inline void BMSCharging::clear_curdemand() {
  curdemand_ = 0;
}
inline float BMSCharging::_internal_curdemand() const {
  return curdemand_;
}
inline float BMSCharging::curdemand() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.curDemand)
  return _internal_curdemand();
}
inline void BMSCharging::_internal_set_curdemand(float value) {
  
  curdemand_ = value;
}
inline void BMSCharging::set_curdemand(float value) {
  _internal_set_curdemand(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.curDemand)
}

// float volMeasured = 23;
inline void BMSCharging::clear_volmeasured() {
  volmeasured_ = 0;
}
inline float BMSCharging::_internal_volmeasured() const {
  return volmeasured_;
}
inline float BMSCharging::volmeasured() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.volMeasured)
  return _internal_volmeasured();
}
inline void BMSCharging::_internal_set_volmeasured(float value) {
  
  volmeasured_ = value;
}
inline void BMSCharging::set_volmeasured(float value) {
  _internal_set_volmeasured(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.volMeasured)
}

// float curMeasured = 24;
inline void BMSCharging::clear_curmeasured() {
  curmeasured_ = 0;
}
inline float BMSCharging::_internal_curmeasured() const {
  return curmeasured_;
}
inline float BMSCharging::curmeasured() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.curMeasured)
  return _internal_curmeasured();
}
inline void BMSCharging::_internal_set_curmeasured(float value) {
  
  curmeasured_ = value;
}
inline void BMSCharging::set_curmeasured(float value) {
  _internal_set_curmeasured(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.curMeasured)
}

// float monoBatVolMax = 25;
inline void BMSCharging::clear_monobatvolmax() {
  monobatvolmax_ = 0;
}
inline float BMSCharging::_internal_monobatvolmax() const {
  return monobatvolmax_;
}
inline float BMSCharging::monobatvolmax() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.monoBatVolMax)
  return _internal_monobatvolmax();
}
inline void BMSCharging::_internal_set_monobatvolmax(float value) {
  
  monobatvolmax_ = value;
}
inline void BMSCharging::set_monobatvolmax(float value) {
  _internal_set_monobatvolmax(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.monoBatVolMax)
}

// float monoBatVolMin = 26;
inline void BMSCharging::clear_monobatvolmin() {
  monobatvolmin_ = 0;
}
inline float BMSCharging::_internal_monobatvolmin() const {
  return monobatvolmin_;
}
inline float BMSCharging::monobatvolmin() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.monoBatVolMin)
  return _internal_monobatvolmin();
}
inline void BMSCharging::_internal_set_monobatvolmin(float value) {
  
  monobatvolmin_ = value;
}
inline void BMSCharging::set_monobatvolmin(float value) {
  _internal_set_monobatvolmin(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.monoBatVolMin)
}

// float tempMax = 27;
inline void BMSCharging::clear_tempmax() {
  tempmax_ = 0;
}
inline float BMSCharging::_internal_tempmax() const {
  return tempmax_;
}
inline float BMSCharging::tempmax() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.tempMax)
  return _internal_tempmax();
}
inline void BMSCharging::_internal_set_tempmax(float value) {
  
  tempmax_ = value;
}
inline void BMSCharging::set_tempmax(float value) {
  _internal_set_tempmax(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.tempMax)
}

// float tempMin = 28;
inline void BMSCharging::clear_tempmin() {
  tempmin_ = 0;
}
inline float BMSCharging::_internal_tempmin() const {
  return tempmin_;
}
inline float BMSCharging::tempmin() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSCharging.tempMin)
  return _internal_tempmin();
}
inline void BMSCharging::_internal_set_tempmin(float value) {
  
  tempmin_ = value;
}
inline void BMSCharging::set_tempmin(float value) {
  _internal_set_tempmin(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSCharging.tempMin)
}

// -------------------------------------------------------------------

// BMSTimeout

// uint32 bmsErrorFrame = 1;
inline void BMSTimeout::clear_bmserrorframe() {
  bmserrorframe_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSTimeout::_internal_bmserrorframe() const {
  return bmserrorframe_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSTimeout::bmserrorframe() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSTimeout.bmsErrorFrame)
  return _internal_bmserrorframe();
}
inline void BMSTimeout::_internal_set_bmserrorframe(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmserrorframe_ = value;
}
inline void BMSTimeout::set_bmserrorframe(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmserrorframe(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSTimeout.bmsErrorFrame)
}

// uint32 chargerErrorFrame = 2;
inline void BMSTimeout::clear_chargererrorframe() {
  chargererrorframe_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSTimeout::_internal_chargererrorframe() const {
  return chargererrorframe_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSTimeout::chargererrorframe() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSTimeout.chargerErrorFrame)
  return _internal_chargererrorframe();
}
inline void BMSTimeout::_internal_set_chargererrorframe(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  chargererrorframe_ = value;
}
inline void BMSTimeout::set_chargererrorframe(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_chargererrorframe(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSTimeout.chargerErrorFrame)
}

// -------------------------------------------------------------------

// BMSReconnectEvent

// uint32 timeoutState = 1;
inline void BMSReconnectEvent::clear_timeoutstate() {
  timeoutstate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSReconnectEvent::_internal_timeoutstate() const {
  return timeoutstate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSReconnectEvent::timeoutstate() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSReconnectEvent.timeoutState)
  return _internal_timeoutstate();
}
inline void BMSReconnectEvent::_internal_set_timeoutstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  timeoutstate_ = value;
}
inline void BMSReconnectEvent::set_timeoutstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_timeoutstate(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSReconnectEvent.timeoutState)
}

// .BMSinfo.BMSTimeoutEnum bmsTimeoutType = 2;
inline void BMSReconnectEvent::clear_bmstimeouttype() {
  bmstimeouttype_ = 0;
}
inline ::BMSinfo::BMSTimeoutEnum BMSReconnectEvent::_internal_bmstimeouttype() const {
  return static_cast< ::BMSinfo::BMSTimeoutEnum >(bmstimeouttype_);
}
inline ::BMSinfo::BMSTimeoutEnum BMSReconnectEvent::bmstimeouttype() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSReconnectEvent.bmsTimeoutType)
  return _internal_bmstimeouttype();
}
inline void BMSReconnectEvent::_internal_set_bmstimeouttype(::BMSinfo::BMSTimeoutEnum value) {
  
  bmstimeouttype_ = value;
}
inline void BMSReconnectEvent::set_bmstimeouttype(::BMSinfo::BMSTimeoutEnum value) {
  _internal_set_bmstimeouttype(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSReconnectEvent.bmsTimeoutType)
}

// uint32 reconnectCnt = 3;
inline void BMSReconnectEvent::clear_reconnectcnt() {
  reconnectcnt_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSReconnectEvent::_internal_reconnectcnt() const {
  return reconnectcnt_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSReconnectEvent::reconnectcnt() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSReconnectEvent.reconnectCnt)
  return _internal_reconnectcnt();
}
inline void BMSReconnectEvent::_internal_set_reconnectcnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  reconnectcnt_ = value;
}
inline void BMSReconnectEvent::set_reconnectcnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_reconnectcnt(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSReconnectEvent.reconnectCnt)
}

// uint32 nextState = 4;
inline void BMSReconnectEvent::clear_nextstate() {
  nextstate_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSReconnectEvent::_internal_nextstate() const {
  return nextstate_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSReconnectEvent::nextstate() const {
  // @@protoc_insertion_point(field_get:BMSinfo.BMSReconnectEvent.nextState)
  return _internal_nextstate();
}
inline void BMSReconnectEvent::_internal_set_nextstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  nextstate_ = value;
}
inline void BMSReconnectEvent::set_nextstate(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_nextstate(value);
  // @@protoc_insertion_point(field_set:BMSinfo.BMSReconnectEvent.nextState)
}

// -------------------------------------------------------------------

// bms2015Msg

// .BMSinfo.ChargeState bmsState = 3;
inline void bms2015Msg::clear_bmsstate() {
  bmsstate_ = 0;
}
inline ::BMSinfo::ChargeState bms2015Msg::_internal_bmsstate() const {
  return static_cast< ::BMSinfo::ChargeState >(bmsstate_);
}
inline ::BMSinfo::ChargeState bms2015Msg::bmsstate() const {
  // @@protoc_insertion_point(field_get:BMSinfo.bms2015Msg.bmsState)
  return _internal_bmsstate();
}
inline void bms2015Msg::_internal_set_bmsstate(::BMSinfo::ChargeState value) {
  
  bmsstate_ = value;
}
inline void bms2015Msg::set_bmsstate(::BMSinfo::ChargeState value) {
  _internal_set_bmsstate(value);
  // @@protoc_insertion_point(field_set:BMSinfo.bms2015Msg.bmsState)
}

// .BMSinfo.BMSHandShake BmshandShakeM = 4;
inline bool bms2015Msg::_internal_has_bmshandshakem() const {
  return this != internal_default_instance() && bmshandshakem_ != nullptr;
}
inline bool bms2015Msg::has_bmshandshakem() const {
  return _internal_has_bmshandshakem();
}
inline void bms2015Msg::clear_bmshandshakem() {
  if (GetArenaForAllocation() == nullptr && bmshandshakem_ != nullptr) {
    delete bmshandshakem_;
  }
  bmshandshakem_ = nullptr;
}
inline const ::BMSinfo::BMSHandShake& bms2015Msg::_internal_bmshandshakem() const {
  const ::BMSinfo::BMSHandShake* p = bmshandshakem_;
  return p != nullptr ? *p : reinterpret_cast<const ::BMSinfo::BMSHandShake&>(
      ::BMSinfo::_BMSHandShake_default_instance_);
}
inline const ::BMSinfo::BMSHandShake& bms2015Msg::bmshandshakem() const {
  // @@protoc_insertion_point(field_get:BMSinfo.bms2015Msg.BmshandShakeM)
  return _internal_bmshandshakem();
}
inline void bms2015Msg::unsafe_arena_set_allocated_bmshandshakem(
    ::BMSinfo::BMSHandShake* bmshandshakem) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmshandshakem_);
  }
  bmshandshakem_ = bmshandshakem;
  if (bmshandshakem) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:BMSinfo.bms2015Msg.BmshandShakeM)
}
inline ::BMSinfo::BMSHandShake* bms2015Msg::release_bmshandshakem() {
  
  ::BMSinfo::BMSHandShake* temp = bmshandshakem_;
  bmshandshakem_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::BMSinfo::BMSHandShake* bms2015Msg::unsafe_arena_release_bmshandshakem() {
  // @@protoc_insertion_point(field_release:BMSinfo.bms2015Msg.BmshandShakeM)
  
  ::BMSinfo::BMSHandShake* temp = bmshandshakem_;
  bmshandshakem_ = nullptr;
  return temp;
}
inline ::BMSinfo::BMSHandShake* bms2015Msg::_internal_mutable_bmshandshakem() {
  
  if (bmshandshakem_ == nullptr) {
    auto* p = CreateMaybeMessage<::BMSinfo::BMSHandShake>(GetArenaForAllocation());
    bmshandshakem_ = p;
  }
  return bmshandshakem_;
}
inline ::BMSinfo::BMSHandShake* bms2015Msg::mutable_bmshandshakem() {
  ::BMSinfo::BMSHandShake* _msg = _internal_mutable_bmshandshakem();
  // @@protoc_insertion_point(field_mutable:BMSinfo.bms2015Msg.BmshandShakeM)
  return _msg;
}
inline void bms2015Msg::set_allocated_bmshandshakem(::BMSinfo::BMSHandShake* bmshandshakem) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete bmshandshakem_;
  }
  if (bmshandshakem) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::BMSinfo::BMSHandShake>::GetOwningArena(bmshandshakem);
    if (message_arena != submessage_arena) {
      bmshandshakem = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bmshandshakem, submessage_arena);
    }
    
  } else {
    
  }
  bmshandshakem_ = bmshandshakem;
  // @@protoc_insertion_point(field_set_allocated:BMSinfo.bms2015Msg.BmshandShakeM)
}

// .BMSinfo.BMSVerification BmsVerifyM = 5;
inline bool bms2015Msg::_internal_has_bmsverifym() const {
  return this != internal_default_instance() && bmsverifym_ != nullptr;
}
inline bool bms2015Msg::has_bmsverifym() const {
  return _internal_has_bmsverifym();
}
inline void bms2015Msg::clear_bmsverifym() {
  if (GetArenaForAllocation() == nullptr && bmsverifym_ != nullptr) {
    delete bmsverifym_;
  }
  bmsverifym_ = nullptr;
}
inline const ::BMSinfo::BMSVerification& bms2015Msg::_internal_bmsverifym() const {
  const ::BMSinfo::BMSVerification* p = bmsverifym_;
  return p != nullptr ? *p : reinterpret_cast<const ::BMSinfo::BMSVerification&>(
      ::BMSinfo::_BMSVerification_default_instance_);
}
inline const ::BMSinfo::BMSVerification& bms2015Msg::bmsverifym() const {
  // @@protoc_insertion_point(field_get:BMSinfo.bms2015Msg.BmsVerifyM)
  return _internal_bmsverifym();
}
inline void bms2015Msg::unsafe_arena_set_allocated_bmsverifym(
    ::BMSinfo::BMSVerification* bmsverifym) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmsverifym_);
  }
  bmsverifym_ = bmsverifym;
  if (bmsverifym) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:BMSinfo.bms2015Msg.BmsVerifyM)
}
inline ::BMSinfo::BMSVerification* bms2015Msg::release_bmsverifym() {
  
  ::BMSinfo::BMSVerification* temp = bmsverifym_;
  bmsverifym_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::BMSinfo::BMSVerification* bms2015Msg::unsafe_arena_release_bmsverifym() {
  // @@protoc_insertion_point(field_release:BMSinfo.bms2015Msg.BmsVerifyM)
  
  ::BMSinfo::BMSVerification* temp = bmsverifym_;
  bmsverifym_ = nullptr;
  return temp;
}
inline ::BMSinfo::BMSVerification* bms2015Msg::_internal_mutable_bmsverifym() {
  
  if (bmsverifym_ == nullptr) {
    auto* p = CreateMaybeMessage<::BMSinfo::BMSVerification>(GetArenaForAllocation());
    bmsverifym_ = p;
  }
  return bmsverifym_;
}
inline ::BMSinfo::BMSVerification* bms2015Msg::mutable_bmsverifym() {
  ::BMSinfo::BMSVerification* _msg = _internal_mutable_bmsverifym();
  // @@protoc_insertion_point(field_mutable:BMSinfo.bms2015Msg.BmsVerifyM)
  return _msg;
}
inline void bms2015Msg::set_allocated_bmsverifym(::BMSinfo::BMSVerification* bmsverifym) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete bmsverifym_;
  }
  if (bmsverifym) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::BMSinfo::BMSVerification>::GetOwningArena(bmsverifym);
    if (message_arena != submessage_arena) {
      bmsverifym = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bmsverifym, submessage_arena);
    }
    
  } else {
    
  }
  bmsverifym_ = bmsverifym;
  // @@protoc_insertion_point(field_set_allocated:BMSinfo.bms2015Msg.BmsVerifyM)
}

// .BMSinfo.BMSConfig BmsConfigM = 6;
inline bool bms2015Msg::_internal_has_bmsconfigm() const {
  return this != internal_default_instance() && bmsconfigm_ != nullptr;
}
inline bool bms2015Msg::has_bmsconfigm() const {
  return _internal_has_bmsconfigm();
}
inline void bms2015Msg::clear_bmsconfigm() {
  if (GetArenaForAllocation() == nullptr && bmsconfigm_ != nullptr) {
    delete bmsconfigm_;
  }
  bmsconfigm_ = nullptr;
}
inline const ::BMSinfo::BMSConfig& bms2015Msg::_internal_bmsconfigm() const {
  const ::BMSinfo::BMSConfig* p = bmsconfigm_;
  return p != nullptr ? *p : reinterpret_cast<const ::BMSinfo::BMSConfig&>(
      ::BMSinfo::_BMSConfig_default_instance_);
}
inline const ::BMSinfo::BMSConfig& bms2015Msg::bmsconfigm() const {
  // @@protoc_insertion_point(field_get:BMSinfo.bms2015Msg.BmsConfigM)
  return _internal_bmsconfigm();
}
inline void bms2015Msg::unsafe_arena_set_allocated_bmsconfigm(
    ::BMSinfo::BMSConfig* bmsconfigm) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmsconfigm_);
  }
  bmsconfigm_ = bmsconfigm;
  if (bmsconfigm) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:BMSinfo.bms2015Msg.BmsConfigM)
}
inline ::BMSinfo::BMSConfig* bms2015Msg::release_bmsconfigm() {
  
  ::BMSinfo::BMSConfig* temp = bmsconfigm_;
  bmsconfigm_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::BMSinfo::BMSConfig* bms2015Msg::unsafe_arena_release_bmsconfigm() {
  // @@protoc_insertion_point(field_release:BMSinfo.bms2015Msg.BmsConfigM)
  
  ::BMSinfo::BMSConfig* temp = bmsconfigm_;
  bmsconfigm_ = nullptr;
  return temp;
}
inline ::BMSinfo::BMSConfig* bms2015Msg::_internal_mutable_bmsconfigm() {
  
  if (bmsconfigm_ == nullptr) {
    auto* p = CreateMaybeMessage<::BMSinfo::BMSConfig>(GetArenaForAllocation());
    bmsconfigm_ = p;
  }
  return bmsconfigm_;
}
inline ::BMSinfo::BMSConfig* bms2015Msg::mutable_bmsconfigm() {
  ::BMSinfo::BMSConfig* _msg = _internal_mutable_bmsconfigm();
  // @@protoc_insertion_point(field_mutable:BMSinfo.bms2015Msg.BmsConfigM)
  return _msg;
}
inline void bms2015Msg::set_allocated_bmsconfigm(::BMSinfo::BMSConfig* bmsconfigm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete bmsconfigm_;
  }
  if (bmsconfigm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::BMSinfo::BMSConfig>::GetOwningArena(bmsconfigm);
    if (message_arena != submessage_arena) {
      bmsconfigm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bmsconfigm, submessage_arena);
    }
    
  } else {
    
  }
  bmsconfigm_ = bmsconfigm;
  // @@protoc_insertion_point(field_set_allocated:BMSinfo.bms2015Msg.BmsConfigM)
}

// .BMSinfo.BMSCharging BmsChargingM = 7;
inline bool bms2015Msg::_internal_has_bmschargingm() const {
  return this != internal_default_instance() && bmschargingm_ != nullptr;
}
inline bool bms2015Msg::has_bmschargingm() const {
  return _internal_has_bmschargingm();
}
inline void bms2015Msg::clear_bmschargingm() {
  if (GetArenaForAllocation() == nullptr && bmschargingm_ != nullptr) {
    delete bmschargingm_;
  }
  bmschargingm_ = nullptr;
}
inline const ::BMSinfo::BMSCharging& bms2015Msg::_internal_bmschargingm() const {
  const ::BMSinfo::BMSCharging* p = bmschargingm_;
  return p != nullptr ? *p : reinterpret_cast<const ::BMSinfo::BMSCharging&>(
      ::BMSinfo::_BMSCharging_default_instance_);
}
inline const ::BMSinfo::BMSCharging& bms2015Msg::bmschargingm() const {
  // @@protoc_insertion_point(field_get:BMSinfo.bms2015Msg.BmsChargingM)
  return _internal_bmschargingm();
}
inline void bms2015Msg::unsafe_arena_set_allocated_bmschargingm(
    ::BMSinfo::BMSCharging* bmschargingm) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmschargingm_);
  }
  bmschargingm_ = bmschargingm;
  if (bmschargingm) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:BMSinfo.bms2015Msg.BmsChargingM)
}
inline ::BMSinfo::BMSCharging* bms2015Msg::release_bmschargingm() {
  
  ::BMSinfo::BMSCharging* temp = bmschargingm_;
  bmschargingm_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::BMSinfo::BMSCharging* bms2015Msg::unsafe_arena_release_bmschargingm() {
  // @@protoc_insertion_point(field_release:BMSinfo.bms2015Msg.BmsChargingM)
  
  ::BMSinfo::BMSCharging* temp = bmschargingm_;
  bmschargingm_ = nullptr;
  return temp;
}
inline ::BMSinfo::BMSCharging* bms2015Msg::_internal_mutable_bmschargingm() {
  
  if (bmschargingm_ == nullptr) {
    auto* p = CreateMaybeMessage<::BMSinfo::BMSCharging>(GetArenaForAllocation());
    bmschargingm_ = p;
  }
  return bmschargingm_;
}
inline ::BMSinfo::BMSCharging* bms2015Msg::mutable_bmschargingm() {
  ::BMSinfo::BMSCharging* _msg = _internal_mutable_bmschargingm();
  // @@protoc_insertion_point(field_mutable:BMSinfo.bms2015Msg.BmsChargingM)
  return _msg;
}
inline void bms2015Msg::set_allocated_bmschargingm(::BMSinfo::BMSCharging* bmschargingm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete bmschargingm_;
  }
  if (bmschargingm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::BMSinfo::BMSCharging>::GetOwningArena(bmschargingm);
    if (message_arena != submessage_arena) {
      bmschargingm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bmschargingm, submessage_arena);
    }
    
  } else {
    
  }
  bmschargingm_ = bmschargingm;
  // @@protoc_insertion_point(field_set_allocated:BMSinfo.bms2015Msg.BmsChargingM)
}

// .BMSinfo.BMSChargingEnd BmsChargeFinishM = 8;
inline bool bms2015Msg::_internal_has_bmschargefinishm() const {
  return this != internal_default_instance() && bmschargefinishm_ != nullptr;
}
inline bool bms2015Msg::has_bmschargefinishm() const {
  return _internal_has_bmschargefinishm();
}
inline void bms2015Msg::clear_bmschargefinishm() {
  if (GetArenaForAllocation() == nullptr && bmschargefinishm_ != nullptr) {
    delete bmschargefinishm_;
  }
  bmschargefinishm_ = nullptr;
}
inline const ::BMSinfo::BMSChargingEnd& bms2015Msg::_internal_bmschargefinishm() const {
  const ::BMSinfo::BMSChargingEnd* p = bmschargefinishm_;
  return p != nullptr ? *p : reinterpret_cast<const ::BMSinfo::BMSChargingEnd&>(
      ::BMSinfo::_BMSChargingEnd_default_instance_);
}
inline const ::BMSinfo::BMSChargingEnd& bms2015Msg::bmschargefinishm() const {
  // @@protoc_insertion_point(field_get:BMSinfo.bms2015Msg.BmsChargeFinishM)
  return _internal_bmschargefinishm();
}
inline void bms2015Msg::unsafe_arena_set_allocated_bmschargefinishm(
    ::BMSinfo::BMSChargingEnd* bmschargefinishm) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmschargefinishm_);
  }
  bmschargefinishm_ = bmschargefinishm;
  if (bmschargefinishm) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:BMSinfo.bms2015Msg.BmsChargeFinishM)
}
inline ::BMSinfo::BMSChargingEnd* bms2015Msg::release_bmschargefinishm() {
  
  ::BMSinfo::BMSChargingEnd* temp = bmschargefinishm_;
  bmschargefinishm_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::BMSinfo::BMSChargingEnd* bms2015Msg::unsafe_arena_release_bmschargefinishm() {
  // @@protoc_insertion_point(field_release:BMSinfo.bms2015Msg.BmsChargeFinishM)
  
  ::BMSinfo::BMSChargingEnd* temp = bmschargefinishm_;
  bmschargefinishm_ = nullptr;
  return temp;
}
inline ::BMSinfo::BMSChargingEnd* bms2015Msg::_internal_mutable_bmschargefinishm() {
  
  if (bmschargefinishm_ == nullptr) {
    auto* p = CreateMaybeMessage<::BMSinfo::BMSChargingEnd>(GetArenaForAllocation());
    bmschargefinishm_ = p;
  }
  return bmschargefinishm_;
}
inline ::BMSinfo::BMSChargingEnd* bms2015Msg::mutable_bmschargefinishm() {
  ::BMSinfo::BMSChargingEnd* _msg = _internal_mutable_bmschargefinishm();
  // @@protoc_insertion_point(field_mutable:BMSinfo.bms2015Msg.BmsChargeFinishM)
  return _msg;
}
inline void bms2015Msg::set_allocated_bmschargefinishm(::BMSinfo::BMSChargingEnd* bmschargefinishm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete bmschargefinishm_;
  }
  if (bmschargefinishm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::BMSinfo::BMSChargingEnd>::GetOwningArena(bmschargefinishm);
    if (message_arena != submessage_arena) {
      bmschargefinishm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bmschargefinishm, submessage_arena);
    }
    
  } else {
    
  }
  bmschargefinishm_ = bmschargefinishm;
  // @@protoc_insertion_point(field_set_allocated:BMSinfo.bms2015Msg.BmsChargeFinishM)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace BMSinfo

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::BMSinfo::ChargingMode> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::BMSinfo::ChargingMode>() {
  return ::BMSinfo::ChargingMode_descriptor();
}
template <> struct is_proto_enum< ::BMSinfo::BMSTimeoutEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::BMSinfo::BMSTimeoutEnum>() {
  return ::BMSinfo::BMSTimeoutEnum_descriptor();
}
template <> struct is_proto_enum< ::BMSinfo::ChargeState> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::BMSinfo::ChargeState>() {
  return ::BMSinfo::ChargeState_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_GCU_5fBMS_5fINFO_2eproto
