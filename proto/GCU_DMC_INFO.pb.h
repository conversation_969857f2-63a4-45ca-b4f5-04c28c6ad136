// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GCU_DMC_INFO.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_GCU_5fDMC_5fINFO_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_GCU_5fDMC_5fINFO_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3017000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3017003 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_GCU_5fDMC_5fINFO_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_GCU_5fDMC_5fINFO_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[23]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GCU_5fDMC_5fINFO_2eproto;
namespace DMCinfo {
class ADModuleInfo;
struct ADModuleInfoDefaultTypeInternal;
extern ADModuleInfoDefaultTypeInternal _ADModuleInfo_default_instance_;
class ActiveProtectParam;
struct ActiveProtectParamDefaultTypeInternal;
extern ActiveProtectParamDefaultTypeInternal _ActiveProtectParam_default_instance_;
class BMSDataRepeat;
struct BMSDataRepeatDefaultTypeInternal;
extern BMSDataRepeatDefaultTypeInternal _BMSDataRepeat_default_instance_;
class BMSRlyOC;
struct BMSRlyOCDefaultTypeInternal;
extern BMSRlyOCDefaultTypeInternal _BMSRlyOC_default_instance_;
class BMSRlyStick;
struct BMSRlyStickDefaultTypeInternal;
extern BMSRlyStickDefaultTypeInternal _BMSRlyStick_default_instance_;
class CellOverTemp;
struct CellOverTempDefaultTypeInternal;
extern CellOverTempDefaultTypeInternal _CellOverTemp_default_instance_;
class CellOverVolt;
struct CellOverVoltDefaultTypeInternal;
extern CellOverVoltDefaultTypeInternal _CellOverVolt_default_instance_;
class CurrentBalance;
struct CurrentBalanceDefaultTypeInternal;
extern CurrentBalanceDefaultTypeInternal _CurrentBalance_default_instance_;
class GUNInfo;
struct GUNInfoDefaultTypeInternal;
extern GUNInfoDefaultTypeInternal _GUNInfo_default_instance_;
class GunLoadConstraint;
struct GunLoadConstraintDefaultTypeInternal;
extern GunLoadConstraintDefaultTypeInternal _GunLoadConstraint_default_instance_;
class GunMatchInfo;
struct GunMatchInfoDefaultTypeInternal;
extern GunMatchInfoDefaultTypeInternal _GunMatchInfo_default_instance_;
class HMCInfo;
struct HMCInfoDefaultTypeInternal;
extern HMCInfoDefaultTypeInternal _HMCInfo_default_instance_;
class HmcChargingStrategyM;
struct HmcChargingStrategyMDefaultTypeInternal;
extern HmcChargingStrategyMDefaultTypeInternal _HmcChargingStrategyM_default_instance_;
class HotRunaway;
struct HotRunawayDefaultTypeInternal;
extern HotRunawayDefaultTypeInternal _HotRunaway_default_instance_;
class LowTemp;
struct LowTempDefaultTypeInternal;
extern LowTempDefaultTypeInternal _LowTemp_default_instance_;
class OHPInfo;
struct OHPInfoDefaultTypeInternal;
extern OHPInfoDefaultTypeInternal _OHPInfo_default_instance_;
class OverCharge;
struct OverChargeDefaultTypeInternal;
extern OverChargeDefaultTypeInternal _OverCharge_default_instance_;
class OverCurrent;
struct OverCurrentDefaultTypeInternal;
extern OverCurrentDefaultTypeInternal _OverCurrent_default_instance_;
class PMMInfo;
struct PMMInfoDefaultTypeInternal;
extern PMMInfoDefaultTypeInternal _PMMInfo_default_instance_;
class PUBInfo;
struct PUBInfoDefaultTypeInternal;
extern PUBInfoDefaultTypeInternal _PUBInfo_default_instance_;
class PackOverVolt;
struct PackOverVoltDefaultTypeInternal;
extern PackOverVoltDefaultTypeInternal _PackOverVolt_default_instance_;
class ServerInfo;
struct ServerInfoDefaultTypeInternal;
extern ServerInfoDefaultTypeInternal _ServerInfo_default_instance_;
class VCIInfo;
struct VCIInfoDefaultTypeInternal;
extern VCIInfoDefaultTypeInternal _VCIInfo_default_instance_;
}  // namespace DMCinfo
PROTOBUF_NAMESPACE_OPEN
template<> ::DMCinfo::ADModuleInfo* Arena::CreateMaybeMessage<::DMCinfo::ADModuleInfo>(Arena*);
template<> ::DMCinfo::ActiveProtectParam* Arena::CreateMaybeMessage<::DMCinfo::ActiveProtectParam>(Arena*);
template<> ::DMCinfo::BMSDataRepeat* Arena::CreateMaybeMessage<::DMCinfo::BMSDataRepeat>(Arena*);
template<> ::DMCinfo::BMSRlyOC* Arena::CreateMaybeMessage<::DMCinfo::BMSRlyOC>(Arena*);
template<> ::DMCinfo::BMSRlyStick* Arena::CreateMaybeMessage<::DMCinfo::BMSRlyStick>(Arena*);
template<> ::DMCinfo::CellOverTemp* Arena::CreateMaybeMessage<::DMCinfo::CellOverTemp>(Arena*);
template<> ::DMCinfo::CellOverVolt* Arena::CreateMaybeMessage<::DMCinfo::CellOverVolt>(Arena*);
template<> ::DMCinfo::CurrentBalance* Arena::CreateMaybeMessage<::DMCinfo::CurrentBalance>(Arena*);
template<> ::DMCinfo::GUNInfo* Arena::CreateMaybeMessage<::DMCinfo::GUNInfo>(Arena*);
template<> ::DMCinfo::GunLoadConstraint* Arena::CreateMaybeMessage<::DMCinfo::GunLoadConstraint>(Arena*);
template<> ::DMCinfo::GunMatchInfo* Arena::CreateMaybeMessage<::DMCinfo::GunMatchInfo>(Arena*);
template<> ::DMCinfo::HMCInfo* Arena::CreateMaybeMessage<::DMCinfo::HMCInfo>(Arena*);
template<> ::DMCinfo::HmcChargingStrategyM* Arena::CreateMaybeMessage<::DMCinfo::HmcChargingStrategyM>(Arena*);
template<> ::DMCinfo::HotRunaway* Arena::CreateMaybeMessage<::DMCinfo::HotRunaway>(Arena*);
template<> ::DMCinfo::LowTemp* Arena::CreateMaybeMessage<::DMCinfo::LowTemp>(Arena*);
template<> ::DMCinfo::OHPInfo* Arena::CreateMaybeMessage<::DMCinfo::OHPInfo>(Arena*);
template<> ::DMCinfo::OverCharge* Arena::CreateMaybeMessage<::DMCinfo::OverCharge>(Arena*);
template<> ::DMCinfo::OverCurrent* Arena::CreateMaybeMessage<::DMCinfo::OverCurrent>(Arena*);
template<> ::DMCinfo::PMMInfo* Arena::CreateMaybeMessage<::DMCinfo::PMMInfo>(Arena*);
template<> ::DMCinfo::PUBInfo* Arena::CreateMaybeMessage<::DMCinfo::PUBInfo>(Arena*);
template<> ::DMCinfo::PackOverVolt* Arena::CreateMaybeMessage<::DMCinfo::PackOverVolt>(Arena*);
template<> ::DMCinfo::ServerInfo* Arena::CreateMaybeMessage<::DMCinfo::ServerInfo>(Arena*);
template<> ::DMCinfo::VCIInfo* Arena::CreateMaybeMessage<::DMCinfo::VCIInfo>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace DMCinfo {

enum GunProEnum : int {
  GBT11 = 0,
  GBT15 = 1,
  GB2015P = 2,
  GB2023A = 3,
  GB2023B = 4,
  GunProEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  GunProEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool GunProEnum_IsValid(int value);
constexpr GunProEnum GunProEnum_MIN = GBT11;
constexpr GunProEnum GunProEnum_MAX = GB2023B;
constexpr int GunProEnum_ARRAYSIZE = GunProEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* GunProEnum_descriptor();
template<typename T>
inline const std::string& GunProEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, GunProEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function GunProEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    GunProEnum_descriptor(), enum_t_value);
}
inline bool GunProEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, GunProEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<GunProEnum>(
    GunProEnum_descriptor(), name, value);
}
enum RunModeEnum : int {
  operateMode = 0,
  authenMode = 1,
  DCsource = 2,
  RunModeEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  RunModeEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool RunModeEnum_IsValid(int value);
constexpr RunModeEnum RunModeEnum_MIN = operateMode;
constexpr RunModeEnum RunModeEnum_MAX = DCsource;
constexpr int RunModeEnum_ARRAYSIZE = RunModeEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RunModeEnum_descriptor();
template<typename T>
inline const std::string& RunModeEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RunModeEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RunModeEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RunModeEnum_descriptor(), enum_t_value);
}
inline bool RunModeEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, RunModeEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RunModeEnum>(
    RunModeEnum_descriptor(), name, value);
}
enum GunTypeEnum : int {
  StandGun = 0,
  Chaoji = 1,
  GBT2023B = 2,
  CCS1 = 3,
  CHAdeMO = 4,
  CCS2 = 5,
  Bow = 6,
  SCD = 7,
  FanHP = 8,
  GunGB2015p = 9,
  GunTypeEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  GunTypeEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool GunTypeEnum_IsValid(int value);
constexpr GunTypeEnum GunTypeEnum_MIN = StandGun;
constexpr GunTypeEnum GunTypeEnum_MAX = GunGB2015p;
constexpr int GunTypeEnum_ARRAYSIZE = GunTypeEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* GunTypeEnum_descriptor();
template<typename T>
inline const std::string& GunTypeEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, GunTypeEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function GunTypeEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    GunTypeEnum_descriptor(), enum_t_value);
}
inline bool GunTypeEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, GunTypeEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<GunTypeEnum>(
    GunTypeEnum_descriptor(), name, value);
}
enum ADModuleTypeEnum : int {
  XYBR30KW = 0,
  XYBR40KW = 1,
  YFY20KW = 2,
  YFY30KW = 3,
  YFY40KW = 4,
  megmeet30KW = 5,
  megmeet40KW = 6,
  TonHe30KW = 7,
  TonHe40KW = 8,
  uugreen30KW = 9,
  uugreen40KW = 10,
  ADModuleTypeEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  ADModuleTypeEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool ADModuleTypeEnum_IsValid(int value);
constexpr ADModuleTypeEnum ADModuleTypeEnum_MIN = XYBR30KW;
constexpr ADModuleTypeEnum ADModuleTypeEnum_MAX = uugreen40KW;
constexpr int ADModuleTypeEnum_ARRAYSIZE = ADModuleTypeEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ADModuleTypeEnum_descriptor();
template<typename T>
inline const std::string& ADModuleTypeEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ADModuleTypeEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ADModuleTypeEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ADModuleTypeEnum_descriptor(), enum_t_value);
}
inline bool ADModuleTypeEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ADModuleTypeEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ADModuleTypeEnum>(
    ADModuleTypeEnum_descriptor(), name, value);
}
enum HmcChargeStrategyEnum : int {
  hmccs_default = 0,
  hmccs_allow = 1,
  hmccs_off = 2,
  HmcChargeStrategyEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  HmcChargeStrategyEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool HmcChargeStrategyEnum_IsValid(int value);
constexpr HmcChargeStrategyEnum HmcChargeStrategyEnum_MIN = hmccs_default;
constexpr HmcChargeStrategyEnum HmcChargeStrategyEnum_MAX = hmccs_off;
constexpr int HmcChargeStrategyEnum_ARRAYSIZE = HmcChargeStrategyEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* HmcChargeStrategyEnum_descriptor();
template<typename T>
inline const std::string& HmcChargeStrategyEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, HmcChargeStrategyEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function HmcChargeStrategyEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    HmcChargeStrategyEnum_descriptor(), enum_t_value);
}
inline bool HmcChargeStrategyEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, HmcChargeStrategyEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<HmcChargeStrategyEnum>(
    HmcChargeStrategyEnum_descriptor(), name, value);
}
enum HmcChargeModeEnum : int {
  hmccm_default = 0,
  hmccm_vin = 1,
  hmccm_card = 2,
  HmcChargeModeEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  HmcChargeModeEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool HmcChargeModeEnum_IsValid(int value);
constexpr HmcChargeModeEnum HmcChargeModeEnum_MIN = hmccm_default;
constexpr HmcChargeModeEnum HmcChargeModeEnum_MAX = hmccm_card;
constexpr int HmcChargeModeEnum_ARRAYSIZE = HmcChargeModeEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* HmcChargeModeEnum_descriptor();
template<typename T>
inline const std::string& HmcChargeModeEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, HmcChargeModeEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function HmcChargeModeEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    HmcChargeModeEnum_descriptor(), enum_t_value);
}
inline bool HmcChargeModeEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, HmcChargeModeEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<HmcChargeModeEnum>(
    HmcChargeModeEnum_descriptor(), name, value);
}
enum HmcLcrEnum : int {
  hmclcr_default = 0,
  hmclcr_soc = 1,
  hmclcr_station = 2,
  hmclcr_termcode = 3,
  hmclcr_timingStrategy = 4,
  HmcLcrEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  HmcLcrEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool HmcLcrEnum_IsValid(int value);
constexpr HmcLcrEnum HmcLcrEnum_MIN = hmclcr_default;
constexpr HmcLcrEnum HmcLcrEnum_MAX = hmclcr_timingStrategy;
constexpr int HmcLcrEnum_ARRAYSIZE = HmcLcrEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* HmcLcrEnum_descriptor();
template<typename T>
inline const std::string& HmcLcrEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, HmcLcrEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function HmcLcrEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    HmcLcrEnum_descriptor(), enum_t_value);
}
inline bool HmcLcrEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, HmcLcrEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<HmcLcrEnum>(
    HmcLcrEnum_descriptor(), name, value);
}
enum HvdcSilenceMode : int {
  e_nomal = 0,
  e_lowNoise = 1,
  HvdcSilenceMode_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  HvdcSilenceMode_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool HvdcSilenceMode_IsValid(int value);
constexpr HvdcSilenceMode HvdcSilenceMode_MIN = e_nomal;
constexpr HvdcSilenceMode HvdcSilenceMode_MAX = e_lowNoise;
constexpr int HvdcSilenceMode_ARRAYSIZE = HvdcSilenceMode_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* HvdcSilenceMode_descriptor();
template<typename T>
inline const std::string& HvdcSilenceMode_Name(T enum_t_value) {
  static_assert(::std::is_same<T, HvdcSilenceMode>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function HvdcSilenceMode_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    HvdcSilenceMode_descriptor(), enum_t_value);
}
inline bool HvdcSilenceMode_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, HvdcSilenceMode* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<HvdcSilenceMode>(
    HvdcSilenceMode_descriptor(), name, value);
}
// ===================================================================

class PUBInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.PUBInfo) */ {
 public:
  inline PUBInfo() : PUBInfo(nullptr) {}
  ~PUBInfo() override;
  explicit constexpr PUBInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PUBInfo(const PUBInfo& from);
  PUBInfo(PUBInfo&& from) noexcept
    : PUBInfo() {
    *this = ::std::move(from);
  }

  inline PUBInfo& operator=(const PUBInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline PUBInfo& operator=(PUBInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PUBInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const PUBInfo* internal_default_instance() {
    return reinterpret_cast<const PUBInfo*>(
               &_PUBInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(PUBInfo& a, PUBInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(PUBInfo* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PUBInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline PUBInfo* New() const final {
    return new PUBInfo();
  }

  PUBInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<PUBInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PUBInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PUBInfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PUBInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.PUBInfo";
  }
  protected:
  explicit PUBInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGunMachInfoFieldNumber = 15,
    kTerminalIDFieldNumber = 1,
    kGunAmountFieldNumber = 2,
    kGunTypeFieldNumber = 3,
    kSysVolMaxFieldNumber = 4,
    kSysCurMaxFieldNumber = 5,
    kSysCurMinFieldNumber = 6,
    kSysMinVolCVFieldNumber = 7,
    kSysMinVolCCFieldNumber = 8,
    kErrorAsWarningFieldNumber = 9,
    kADFixModeFieldNumber = 10,
    kGunMatchModeFieldNumber = 11,
    kLqdCoolOilAddCmdFieldNumber = 12,
    kRunModeFieldNumber = 13,
    kGunCodeFieldNumber = 14,
    kMultGunParallModeFieldNumber = 16,
  };
  // repeated .DMCinfo.GunMatchInfo gunMachInfo = 15;
  int gunmachinfo_size() const;
  private:
  int _internal_gunmachinfo_size() const;
  public:
  void clear_gunmachinfo();
  ::DMCinfo::GunMatchInfo* mutable_gunmachinfo(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::GunMatchInfo >*
      mutable_gunmachinfo();
  private:
  const ::DMCinfo::GunMatchInfo& _internal_gunmachinfo(int index) const;
  ::DMCinfo::GunMatchInfo* _internal_add_gunmachinfo();
  public:
  const ::DMCinfo::GunMatchInfo& gunmachinfo(int index) const;
  ::DMCinfo::GunMatchInfo* add_gunmachinfo();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::GunMatchInfo >&
      gunmachinfo() const;

  // uint32 terminalID = 1;
  void clear_terminalid();
  ::PROTOBUF_NAMESPACE_ID::uint32 terminalid() const;
  void set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_terminalid() const;
  void _internal_set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 GunAmount = 2;
  void clear_gunamount();
  ::PROTOBUF_NAMESPACE_ID::uint32 gunamount() const;
  void set_gunamount(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_gunamount() const;
  void _internal_set_gunamount(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // .DMCinfo.GunTypeEnum GunType = 3;
  void clear_guntype();
  ::DMCinfo::GunTypeEnum guntype() const;
  void set_guntype(::DMCinfo::GunTypeEnum value);
  private:
  ::DMCinfo::GunTypeEnum _internal_guntype() const;
  void _internal_set_guntype(::DMCinfo::GunTypeEnum value);
  public:

  // float sysVolMax = 4;
  void clear_sysvolmax();
  float sysvolmax() const;
  void set_sysvolmax(float value);
  private:
  float _internal_sysvolmax() const;
  void _internal_set_sysvolmax(float value);
  public:

  // float sysCurMax = 5;
  void clear_syscurmax();
  float syscurmax() const;
  void set_syscurmax(float value);
  private:
  float _internal_syscurmax() const;
  void _internal_set_syscurmax(float value);
  public:

  // float sysCurMin = 6;
  void clear_syscurmin();
  float syscurmin() const;
  void set_syscurmin(float value);
  private:
  float _internal_syscurmin() const;
  void _internal_set_syscurmin(float value);
  public:

  // float sysMinVolCV = 7;
  void clear_sysminvolcv();
  float sysminvolcv() const;
  void set_sysminvolcv(float value);
  private:
  float _internal_sysminvolcv() const;
  void _internal_set_sysminvolcv(float value);
  public:

  // float sysMinVolCC = 8;
  void clear_sysminvolcc();
  float sysminvolcc() const;
  void set_sysminvolcc(float value);
  private:
  float _internal_sysminvolcc() const;
  void _internal_set_sysminvolcc(float value);
  public:

  // uint32 ErrorAsWarning = 9;
  void clear_erroraswarning();
  ::PROTOBUF_NAMESPACE_ID::uint32 erroraswarning() const;
  void set_erroraswarning(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_erroraswarning() const;
  void _internal_set_erroraswarning(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 ADFixMode = 10;
  void clear_adfixmode();
  ::PROTOBUF_NAMESPACE_ID::uint32 adfixmode() const;
  void set_adfixmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_adfixmode() const;
  void _internal_set_adfixmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 gunMatchMode = 11;
  void clear_gunmatchmode();
  ::PROTOBUF_NAMESPACE_ID::uint32 gunmatchmode() const;
  void set_gunmatchmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_gunmatchmode() const;
  void _internal_set_gunmatchmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 lqdCoolOilAddCmd = 12;
  void clear_lqdcooloiladdcmd();
  ::PROTOBUF_NAMESPACE_ID::uint32 lqdcooloiladdcmd() const;
  void set_lqdcooloiladdcmd(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_lqdcooloiladdcmd() const;
  void _internal_set_lqdcooloiladdcmd(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // .DMCinfo.RunModeEnum runMode = 13;
  void clear_runmode();
  ::DMCinfo::RunModeEnum runmode() const;
  void set_runmode(::DMCinfo::RunModeEnum value);
  private:
  ::DMCinfo::RunModeEnum _internal_runmode() const;
  void _internal_set_runmode(::DMCinfo::RunModeEnum value);
  public:

  // uint32 GunCode = 14;
  void clear_guncode();
  ::PROTOBUF_NAMESPACE_ID::uint32 guncode() const;
  void set_guncode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_guncode() const;
  void _internal_set_guncode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 multGunParallMode = 16;
  void clear_multgunparallmode();
  ::PROTOBUF_NAMESPACE_ID::uint32 multgunparallmode() const;
  void set_multgunparallmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_multgunparallmode() const;
  void _internal_set_multgunparallmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:DMCinfo.PUBInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::GunMatchInfo > gunmachinfo_;
  ::PROTOBUF_NAMESPACE_ID::uint32 terminalid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 gunamount_;
  int guntype_;
  float sysvolmax_;
  float syscurmax_;
  float syscurmin_;
  float sysminvolcv_;
  float sysminvolcc_;
  ::PROTOBUF_NAMESPACE_ID::uint32 erroraswarning_;
  ::PROTOBUF_NAMESPACE_ID::uint32 adfixmode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 gunmatchmode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 lqdcooloiladdcmd_;
  int runmode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 guncode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 multgunparallmode_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class VCIInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.VCIInfo) */ {
 public:
  inline VCIInfo() : VCIInfo(nullptr) {}
  ~VCIInfo() override;
  explicit constexpr VCIInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  VCIInfo(const VCIInfo& from);
  VCIInfo(VCIInfo&& from) noexcept
    : VCIInfo() {
    *this = ::std::move(from);
  }

  inline VCIInfo& operator=(const VCIInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline VCIInfo& operator=(VCIInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const VCIInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const VCIInfo* internal_default_instance() {
    return reinterpret_cast<const VCIInfo*>(
               &_VCIInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(VCIInfo& a, VCIInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(VCIInfo* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VCIInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline VCIInfo* New() const final {
    return new VCIInfo();
  }

  VCIInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<VCIInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const VCIInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const VCIInfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VCIInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.VCIInfo";
  }
  protected:
  explicit VCIInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGunInfoFieldNumber = 10,
    kTerminalIDFieldNumber = 1,
    kCooperateModeFieldNumber = 2,
    kDCFanEnableFieldNumber = 3,
    kSmokeEnableFieldNumber = 4,
    kToppleAndFallEnableFieldNumber = 5,
    kWaterEnableFieldNumber = 6,
    kLightningEnableFieldNumber = 7,
    kDustproofEnableFieldNumber = 8,
    kEmergencyStopTypeFieldNumber = 9,
  };
  // repeated .DMCinfo.GUNInfo gunInfo = 10;
  int guninfo_size() const;
  private:
  int _internal_guninfo_size() const;
  public:
  void clear_guninfo();
  ::DMCinfo::GUNInfo* mutable_guninfo(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::GUNInfo >*
      mutable_guninfo();
  private:
  const ::DMCinfo::GUNInfo& _internal_guninfo(int index) const;
  ::DMCinfo::GUNInfo* _internal_add_guninfo();
  public:
  const ::DMCinfo::GUNInfo& guninfo(int index) const;
  ::DMCinfo::GUNInfo* add_guninfo();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::GUNInfo >&
      guninfo() const;

  // uint32 terminalID = 1;
  void clear_terminalid();
  ::PROTOBUF_NAMESPACE_ID::uint32 terminalid() const;
  void set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_terminalid() const;
  void _internal_set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 CooperateMode = 2;
  void clear_cooperatemode();
  ::PROTOBUF_NAMESPACE_ID::uint32 cooperatemode() const;
  void set_cooperatemode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_cooperatemode() const;
  void _internal_set_cooperatemode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 DCFanEnable = 3;
  void clear_dcfanenable();
  ::PROTOBUF_NAMESPACE_ID::uint32 dcfanenable() const;
  void set_dcfanenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_dcfanenable() const;
  void _internal_set_dcfanenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 SmokeEnable = 4;
  void clear_smokeenable();
  ::PROTOBUF_NAMESPACE_ID::uint32 smokeenable() const;
  void set_smokeenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_smokeenable() const;
  void _internal_set_smokeenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 ToppleAndFallEnable = 5;
  void clear_toppleandfallenable();
  ::PROTOBUF_NAMESPACE_ID::uint32 toppleandfallenable() const;
  void set_toppleandfallenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_toppleandfallenable() const;
  void _internal_set_toppleandfallenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 WaterEnable = 6;
  void clear_waterenable();
  ::PROTOBUF_NAMESPACE_ID::uint32 waterenable() const;
  void set_waterenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_waterenable() const;
  void _internal_set_waterenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 LightningEnable = 7;
  void clear_lightningenable();
  ::PROTOBUF_NAMESPACE_ID::uint32 lightningenable() const;
  void set_lightningenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_lightningenable() const;
  void _internal_set_lightningenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 DustproofEnable = 8;
  void clear_dustproofenable();
  ::PROTOBUF_NAMESPACE_ID::uint32 dustproofenable() const;
  void set_dustproofenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_dustproofenable() const;
  void _internal_set_dustproofenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 EmergencyStopType = 9;
  void clear_emergencystoptype();
  ::PROTOBUF_NAMESPACE_ID::uint32 emergencystoptype() const;
  void set_emergencystoptype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_emergencystoptype() const;
  void _internal_set_emergencystoptype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:DMCinfo.VCIInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::GUNInfo > guninfo_;
  ::PROTOBUF_NAMESPACE_ID::uint32 terminalid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 cooperatemode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 dcfanenable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 smokeenable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 toppleandfallenable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 waterenable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 lightningenable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 dustproofenable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 emergencystoptype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class GUNInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.GUNInfo) */ {
 public:
  inline GUNInfo() : GUNInfo(nullptr) {}
  ~GUNInfo() override;
  explicit constexpr GUNInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GUNInfo(const GUNInfo& from);
  GUNInfo(GUNInfo&& from) noexcept
    : GUNInfo() {
    *this = ::std::move(from);
  }

  inline GUNInfo& operator=(const GUNInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline GUNInfo& operator=(GUNInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GUNInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const GUNInfo* internal_default_instance() {
    return reinterpret_cast<const GUNInfo*>(
               &_GUNInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(GUNInfo& a, GUNInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(GUNInfo* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GUNInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GUNInfo* New() const final {
    return new GUNInfo();
  }

  GUNInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GUNInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GUNInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GUNInfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GUNInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.GUNInfo";
  }
  protected:
  explicit GUNInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kActiveProtParaFieldNumber = 10,
    kGunIDFieldNumber = 1,
    kGbtProtoTypeFieldNumber = 2,
    kEmeterTypeFieldNumber = 3,
    kElockModeFieldNumber = 4,
    kTemperatureModeFieldNumber = 5,
    kGunCurrentFieldNumber = 6,
    kMainContactorCurrentFieldNumber = 7,
    kGunTempEnableFieldNumber = 8,
    kAuxTypeFieldNumber = 9,
  };
  // .DMCinfo.ActiveProtectParam ActiveProtPara = 10;
  bool has_activeprotpara() const;
  private:
  bool _internal_has_activeprotpara() const;
  public:
  void clear_activeprotpara();
  const ::DMCinfo::ActiveProtectParam& activeprotpara() const;
  PROTOBUF_MUST_USE_RESULT ::DMCinfo::ActiveProtectParam* release_activeprotpara();
  ::DMCinfo::ActiveProtectParam* mutable_activeprotpara();
  void set_allocated_activeprotpara(::DMCinfo::ActiveProtectParam* activeprotpara);
  private:
  const ::DMCinfo::ActiveProtectParam& _internal_activeprotpara() const;
  ::DMCinfo::ActiveProtectParam* _internal_mutable_activeprotpara();
  public:
  void unsafe_arena_set_allocated_activeprotpara(
      ::DMCinfo::ActiveProtectParam* activeprotpara);
  ::DMCinfo::ActiveProtectParam* unsafe_arena_release_activeprotpara();

  // uint32 gunID = 1;
  void clear_gunid();
  ::PROTOBUF_NAMESPACE_ID::uint32 gunid() const;
  void set_gunid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_gunid() const;
  void _internal_set_gunid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // .DMCinfo.GunProEnum GbtProtoType = 2;
  void clear_gbtprototype();
  ::DMCinfo::GunProEnum gbtprototype() const;
  void set_gbtprototype(::DMCinfo::GunProEnum value);
  private:
  ::DMCinfo::GunProEnum _internal_gbtprototype() const;
  void _internal_set_gbtprototype(::DMCinfo::GunProEnum value);
  public:

  // uint32 EmeterType = 3;
  void clear_emetertype();
  ::PROTOBUF_NAMESPACE_ID::uint32 emetertype() const;
  void set_emetertype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_emetertype() const;
  void _internal_set_emetertype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 ElockMode = 4;
  void clear_elockmode();
  ::PROTOBUF_NAMESPACE_ID::uint32 elockmode() const;
  void set_elockmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_elockmode() const;
  void _internal_set_elockmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 TemperatureMode = 5;
  void clear_temperaturemode();
  ::PROTOBUF_NAMESPACE_ID::uint32 temperaturemode() const;
  void set_temperaturemode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_temperaturemode() const;
  void _internal_set_temperaturemode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 GunCurrent = 6;
  void clear_guncurrent();
  ::PROTOBUF_NAMESPACE_ID::uint32 guncurrent() const;
  void set_guncurrent(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_guncurrent() const;
  void _internal_set_guncurrent(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 MainContactorCurrent = 7;
  void clear_maincontactorcurrent();
  ::PROTOBUF_NAMESPACE_ID::uint32 maincontactorcurrent() const;
  void set_maincontactorcurrent(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_maincontactorcurrent() const;
  void _internal_set_maincontactorcurrent(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 gunTempEnable = 8;
  void clear_guntempenable();
  ::PROTOBUF_NAMESPACE_ID::uint32 guntempenable() const;
  void set_guntempenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_guntempenable() const;
  void _internal_set_guntempenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 AuxType = 9;
  void clear_auxtype();
  ::PROTOBUF_NAMESPACE_ID::uint32 auxtype() const;
  void set_auxtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_auxtype() const;
  void _internal_set_auxtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:DMCinfo.GUNInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::DMCinfo::ActiveProtectParam* activeprotpara_;
  ::PROTOBUF_NAMESPACE_ID::uint32 gunid_;
  int gbtprototype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 emetertype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 elockmode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 temperaturemode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 guncurrent_;
  ::PROTOBUF_NAMESPACE_ID::uint32 maincontactorcurrent_;
  ::PROTOBUF_NAMESPACE_ID::uint32 guntempenable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 auxtype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class ADModuleInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.ADModuleInfo) */ {
 public:
  inline ADModuleInfo() : ADModuleInfo(nullptr) {}
  ~ADModuleInfo() override;
  explicit constexpr ADModuleInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ADModuleInfo(const ADModuleInfo& from);
  ADModuleInfo(ADModuleInfo&& from) noexcept
    : ADModuleInfo() {
    *this = ::std::move(from);
  }

  inline ADModuleInfo& operator=(const ADModuleInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline ADModuleInfo& operator=(ADModuleInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ADModuleInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const ADModuleInfo* internal_default_instance() {
    return reinterpret_cast<const ADModuleInfo*>(
               &_ADModuleInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(ADModuleInfo& a, ADModuleInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(ADModuleInfo* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ADModuleInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ADModuleInfo* New() const final {
    return new ADModuleInfo();
  }

  ADModuleInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ADModuleInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ADModuleInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ADModuleInfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ADModuleInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.ADModuleInfo";
  }
  protected:
  explicit ADModuleInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDCModuleSNFieldNumber = 10,
    kSoftVersionFieldNumber = 11,
    kHardVersionFieldNumber = 12,
    kIDFieldNumber = 1,
    kCurrMaxFieldNumber = 2,
    kLimitPowerFieldNumber = 3,
    kVolMaxFieldNumber = 4,
    kVolMinFieldNumber = 5,
    kRatedVolFieldNumber = 6,
    kRatedPowerFieldNumber = 7,
    kRatedCurrFieldNumber = 8,
    kRatedInputVolFieldNumber = 9,
  };
  // bytes DCModuleSN = 10;
  void clear_dcmodulesn();
  const std::string& dcmodulesn() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_dcmodulesn(ArgT0&& arg0, ArgT... args);
  std::string* mutable_dcmodulesn();
  PROTOBUF_MUST_USE_RESULT std::string* release_dcmodulesn();
  void set_allocated_dcmodulesn(std::string* dcmodulesn);
  private:
  const std::string& _internal_dcmodulesn() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_dcmodulesn(const std::string& value);
  std::string* _internal_mutable_dcmodulesn();
  public:

  // bytes softVersion = 11;
  void clear_softversion();
  const std::string& softversion() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_softversion(ArgT0&& arg0, ArgT... args);
  std::string* mutable_softversion();
  PROTOBUF_MUST_USE_RESULT std::string* release_softversion();
  void set_allocated_softversion(std::string* softversion);
  private:
  const std::string& _internal_softversion() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_softversion(const std::string& value);
  std::string* _internal_mutable_softversion();
  public:

  // bytes hardVersion = 12;
  void clear_hardversion();
  const std::string& hardversion() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_hardversion(ArgT0&& arg0, ArgT... args);
  std::string* mutable_hardversion();
  PROTOBUF_MUST_USE_RESULT std::string* release_hardversion();
  void set_allocated_hardversion(std::string* hardversion);
  private:
  const std::string& _internal_hardversion() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_hardversion(const std::string& value);
  std::string* _internal_mutable_hardversion();
  public:

  // uint32 ID = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::uint32 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_id() const;
  void _internal_set_id(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // float currMax = 2;
  void clear_currmax();
  float currmax() const;
  void set_currmax(float value);
  private:
  float _internal_currmax() const;
  void _internal_set_currmax(float value);
  public:

  // float limitPower = 3;
  void clear_limitpower();
  float limitpower() const;
  void set_limitpower(float value);
  private:
  float _internal_limitpower() const;
  void _internal_set_limitpower(float value);
  public:

  // float volMax = 4;
  void clear_volmax();
  float volmax() const;
  void set_volmax(float value);
  private:
  float _internal_volmax() const;
  void _internal_set_volmax(float value);
  public:

  // float volMin = 5;
  void clear_volmin();
  float volmin() const;
  void set_volmin(float value);
  private:
  float _internal_volmin() const;
  void _internal_set_volmin(float value);
  public:

  // float ratedVol = 6;
  void clear_ratedvol();
  float ratedvol() const;
  void set_ratedvol(float value);
  private:
  float _internal_ratedvol() const;
  void _internal_set_ratedvol(float value);
  public:

  // float ratedPower = 7;
  void clear_ratedpower();
  float ratedpower() const;
  void set_ratedpower(float value);
  private:
  float _internal_ratedpower() const;
  void _internal_set_ratedpower(float value);
  public:

  // float ratedCurr = 8;
  void clear_ratedcurr();
  float ratedcurr() const;
  void set_ratedcurr(float value);
  private:
  float _internal_ratedcurr() const;
  void _internal_set_ratedcurr(float value);
  public:

  // float ratedInputVol = 9;
  void clear_ratedinputvol();
  float ratedinputvol() const;
  void set_ratedinputvol(float value);
  private:
  float _internal_ratedinputvol() const;
  void _internal_set_ratedinputvol(float value);
  public:

  // @@protoc_insertion_point(class_scope:DMCinfo.ADModuleInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr dcmodulesn_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr softversion_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr hardversion_;
  ::PROTOBUF_NAMESPACE_ID::uint32 id_;
  float currmax_;
  float limitpower_;
  float volmax_;
  float volmin_;
  float ratedvol_;
  float ratedpower_;
  float ratedcurr_;
  float ratedinputvol_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class PMMInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.PMMInfo) */ {
 public:
  inline PMMInfo() : PMMInfo(nullptr) {}
  ~PMMInfo() override;
  explicit constexpr PMMInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PMMInfo(const PMMInfo& from);
  PMMInfo(PMMInfo&& from) noexcept
    : PMMInfo() {
    *this = ::std::move(from);
  }

  inline PMMInfo& operator=(const PMMInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline PMMInfo& operator=(PMMInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PMMInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const PMMInfo* internal_default_instance() {
    return reinterpret_cast<const PMMInfo*>(
               &_PMMInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(PMMInfo& a, PMMInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(PMMInfo* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PMMInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline PMMInfo* New() const final {
    return new PMMInfo();
  }

  PMMInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<PMMInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PMMInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PMMInfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PMMInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.PMMInfo";
  }
  protected:
  explicit PMMInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kADModuleFieldNumber = 7,
    kMatrixTypeFieldNumber = 1,
    kMatrixContactorCurrentFieldNumber = 2,
    kContactorAcFieldNumber = 3,
    kEmergencyStopTypeFieldNumber = 4,
    kLimitPowerFieldNumber = 5,
    kADModuleTypeFieldNumber = 6,
    kTempEnableFieldNumber = 8,
    kACFanEnableFieldNumber = 9,
    kSmokeEnableFieldNumber = 10,
    kToppleAndFallEnableFieldNumber = 11,
    kWaterEnableFieldNumber = 12,
    kLightningEnableFieldNumber = 13,
    kBreakerEnableFieldNumber = 14,
    kDustproofEnableFieldNumber = 15,
    kACFanNumFieldNumber = 16,
    kModuleSilenceModeFieldNumber = 17,
    kPduTypeFieldNumber = 18,
    kCoolingTypeFieldNumber = 19,
    kLiquidDeviceTypeFieldNumber = 20,
    kLiquidCoolAddLiquidFieldNumber = 21,
  };
  // repeated .DMCinfo.ADModuleInfo ADModule = 7;
  int admodule_size() const;
  private:
  int _internal_admodule_size() const;
  public:
  void clear_admodule();
  ::DMCinfo::ADModuleInfo* mutable_admodule(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::ADModuleInfo >*
      mutable_admodule();
  private:
  const ::DMCinfo::ADModuleInfo& _internal_admodule(int index) const;
  ::DMCinfo::ADModuleInfo* _internal_add_admodule();
  public:
  const ::DMCinfo::ADModuleInfo& admodule(int index) const;
  ::DMCinfo::ADModuleInfo* add_admodule();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::ADModuleInfo >&
      admodule() const;

  // uint32 MatrixType = 1;
  void clear_matrixtype();
  ::PROTOBUF_NAMESPACE_ID::uint32 matrixtype() const;
  void set_matrixtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_matrixtype() const;
  void _internal_set_matrixtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 MatrixContactorCurrent = 2;
  void clear_matrixcontactorcurrent();
  ::PROTOBUF_NAMESPACE_ID::uint32 matrixcontactorcurrent() const;
  void set_matrixcontactorcurrent(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_matrixcontactorcurrent() const;
  void _internal_set_matrixcontactorcurrent(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 ContactorAc = 3;
  void clear_contactorac();
  ::PROTOBUF_NAMESPACE_ID::uint32 contactorac() const;
  void set_contactorac(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_contactorac() const;
  void _internal_set_contactorac(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 EmergencyStopType = 4;
  void clear_emergencystoptype();
  ::PROTOBUF_NAMESPACE_ID::uint32 emergencystoptype() const;
  void set_emergencystoptype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_emergencystoptype() const;
  void _internal_set_emergencystoptype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 limitPower = 5;
  void clear_limitpower();
  ::PROTOBUF_NAMESPACE_ID::uint32 limitpower() const;
  void set_limitpower(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_limitpower() const;
  void _internal_set_limitpower(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // .DMCinfo.ADModuleTypeEnum ADModuleType = 6;
  void clear_admoduletype();
  ::DMCinfo::ADModuleTypeEnum admoduletype() const;
  void set_admoduletype(::DMCinfo::ADModuleTypeEnum value);
  private:
  ::DMCinfo::ADModuleTypeEnum _internal_admoduletype() const;
  void _internal_set_admoduletype(::DMCinfo::ADModuleTypeEnum value);
  public:

  // uint32 TempEnable = 8;
  void clear_tempenable();
  ::PROTOBUF_NAMESPACE_ID::uint32 tempenable() const;
  void set_tempenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_tempenable() const;
  void _internal_set_tempenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 ACFanEnable = 9;
  void clear_acfanenable();
  ::PROTOBUF_NAMESPACE_ID::uint32 acfanenable() const;
  void set_acfanenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_acfanenable() const;
  void _internal_set_acfanenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 SmokeEnable = 10;
  void clear_smokeenable();
  ::PROTOBUF_NAMESPACE_ID::uint32 smokeenable() const;
  void set_smokeenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_smokeenable() const;
  void _internal_set_smokeenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 ToppleAndFallEnable = 11;
  void clear_toppleandfallenable();
  ::PROTOBUF_NAMESPACE_ID::uint32 toppleandfallenable() const;
  void set_toppleandfallenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_toppleandfallenable() const;
  void _internal_set_toppleandfallenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 WaterEnable = 12;
  void clear_waterenable();
  ::PROTOBUF_NAMESPACE_ID::uint32 waterenable() const;
  void set_waterenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_waterenable() const;
  void _internal_set_waterenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 LightningEnable = 13;
  void clear_lightningenable();
  ::PROTOBUF_NAMESPACE_ID::uint32 lightningenable() const;
  void set_lightningenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_lightningenable() const;
  void _internal_set_lightningenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 BreakerEnable = 14;
  void clear_breakerenable();
  ::PROTOBUF_NAMESPACE_ID::uint32 breakerenable() const;
  void set_breakerenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_breakerenable() const;
  void _internal_set_breakerenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 DustproofEnable = 15;
  void clear_dustproofenable();
  ::PROTOBUF_NAMESPACE_ID::uint32 dustproofenable() const;
  void set_dustproofenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_dustproofenable() const;
  void _internal_set_dustproofenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 ACFanNum = 16;
  void clear_acfannum();
  ::PROTOBUF_NAMESPACE_ID::uint32 acfannum() const;
  void set_acfannum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_acfannum() const;
  void _internal_set_acfannum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // .DMCinfo.HvdcSilenceMode ModuleSilenceMode = 17;
  void clear_modulesilencemode();
  ::DMCinfo::HvdcSilenceMode modulesilencemode() const;
  void set_modulesilencemode(::DMCinfo::HvdcSilenceMode value);
  private:
  ::DMCinfo::HvdcSilenceMode _internal_modulesilencemode() const;
  void _internal_set_modulesilencemode(::DMCinfo::HvdcSilenceMode value);
  public:

  // uint32 PduType = 18;
  void clear_pdutype();
  ::PROTOBUF_NAMESPACE_ID::uint32 pdutype() const;
  void set_pdutype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_pdutype() const;
  void _internal_set_pdutype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 coolingType = 19;
  void clear_coolingtype();
  ::PROTOBUF_NAMESPACE_ID::uint32 coolingtype() const;
  void set_coolingtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_coolingtype() const;
  void _internal_set_coolingtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 liquidDeviceType = 20;
  void clear_liquiddevicetype();
  ::PROTOBUF_NAMESPACE_ID::uint32 liquiddevicetype() const;
  void set_liquiddevicetype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_liquiddevicetype() const;
  void _internal_set_liquiddevicetype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 liquidCoolAddLiquid = 21;
  void clear_liquidcooladdliquid();
  ::PROTOBUF_NAMESPACE_ID::uint32 liquidcooladdliquid() const;
  void set_liquidcooladdliquid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_liquidcooladdliquid() const;
  void _internal_set_liquidcooladdliquid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:DMCinfo.PMMInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::ADModuleInfo > admodule_;
  ::PROTOBUF_NAMESPACE_ID::uint32 matrixtype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 matrixcontactorcurrent_;
  ::PROTOBUF_NAMESPACE_ID::uint32 contactorac_;
  ::PROTOBUF_NAMESPACE_ID::uint32 emergencystoptype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 limitpower_;
  int admoduletype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 tempenable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 acfanenable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 smokeenable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 toppleandfallenable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 waterenable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 lightningenable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 breakerenable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 dustproofenable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 acfannum_;
  int modulesilencemode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 pdutype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 coolingtype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 liquiddevicetype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 liquidcooladdliquid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class ServerInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.ServerInfo) */ {
 public:
  inline ServerInfo() : ServerInfo(nullptr) {}
  ~ServerInfo() override;
  explicit constexpr ServerInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ServerInfo(const ServerInfo& from);
  ServerInfo(ServerInfo&& from) noexcept
    : ServerInfo() {
    *this = ::std::move(from);
  }

  inline ServerInfo& operator=(const ServerInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline ServerInfo& operator=(ServerInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ServerInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const ServerInfo* internal_default_instance() {
    return reinterpret_cast<const ServerInfo*>(
               &_ServerInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(ServerInfo& a, ServerInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(ServerInfo* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ServerInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ServerInfo* New() const final {
    return new ServerInfo();
  }

  ServerInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ServerInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ServerInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ServerInfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ServerInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.ServerInfo";
  }
  protected:
  explicit ServerInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPlatNameFieldNumber = 5,
    kOscProcessFieldNumber = 7,
    kPlatformFieldNumber = 1,
    kQrcodeFieldNumber = 2,
    kVinFieldNumber = 3,
    kCardFieldNumber = 4,
    kWifiofflineChgModeFieldNumber = 6,
  };
  // string platName = 5;
  void clear_platname();
  const std::string& platname() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_platname(ArgT0&& arg0, ArgT... args);
  std::string* mutable_platname();
  PROTOBUF_MUST_USE_RESULT std::string* release_platname();
  void set_allocated_platname(std::string* platname);
  private:
  const std::string& _internal_platname() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_platname(const std::string& value);
  std::string* _internal_mutable_platname();
  public:

  // string oscProcess = 7;
  void clear_oscprocess();
  const std::string& oscprocess() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_oscprocess(ArgT0&& arg0, ArgT... args);
  std::string* mutable_oscprocess();
  PROTOBUF_MUST_USE_RESULT std::string* release_oscprocess();
  void set_allocated_oscprocess(std::string* oscprocess);
  private:
  const std::string& _internal_oscprocess() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_oscprocess(const std::string& value);
  std::string* _internal_mutable_oscprocess();
  public:

  // uint32 platform = 1;
  void clear_platform();
  ::PROTOBUF_NAMESPACE_ID::uint32 platform() const;
  void set_platform(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_platform() const;
  void _internal_set_platform(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 qrcode = 2;
  void clear_qrcode();
  ::PROTOBUF_NAMESPACE_ID::uint32 qrcode() const;
  void set_qrcode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_qrcode() const;
  void _internal_set_qrcode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 vin = 3;
  void clear_vin();
  ::PROTOBUF_NAMESPACE_ID::uint32 vin() const;
  void set_vin(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_vin() const;
  void _internal_set_vin(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 card = 4;
  void clear_card();
  ::PROTOBUF_NAMESPACE_ID::uint32 card() const;
  void set_card(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_card() const;
  void _internal_set_card(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 wifiofflineChgMode = 6;
  void clear_wifiofflinechgmode();
  ::PROTOBUF_NAMESPACE_ID::uint32 wifiofflinechgmode() const;
  void set_wifiofflinechgmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_wifiofflinechgmode() const;
  void _internal_set_wifiofflinechgmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:DMCinfo.ServerInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr platname_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr oscprocess_;
  ::PROTOBUF_NAMESPACE_ID::uint32 platform_;
  ::PROTOBUF_NAMESPACE_ID::uint32 qrcode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 vin_;
  ::PROTOBUF_NAMESPACE_ID::uint32 card_;
  ::PROTOBUF_NAMESPACE_ID::uint32 wifiofflinechgmode_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class OHPInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.OHPInfo) */ {
 public:
  inline OHPInfo() : OHPInfo(nullptr) {}
  ~OHPInfo() override;
  explicit constexpr OHPInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OHPInfo(const OHPInfo& from);
  OHPInfo(OHPInfo&& from) noexcept
    : OHPInfo() {
    *this = ::std::move(from);
  }

  inline OHPInfo& operator=(const OHPInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline OHPInfo& operator=(OHPInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OHPInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const OHPInfo* internal_default_instance() {
    return reinterpret_cast<const OHPInfo*>(
               &_OHPInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(OHPInfo& a, OHPInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(OHPInfo* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OHPInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OHPInfo* New() const final {
    return new OHPInfo();
  }

  OHPInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OHPInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OHPInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const OHPInfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OHPInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.OHPInfo";
  }
  protected:
  explicit OHPInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kServersFieldNumber = 9,
    kGunInfoFieldNumber = 20,
    kMACipFieldNumber = 7,
    kWiFiipFieldNumber = 8,
    kProductModelFieldNumber = 22,
    kCcidFieldNumber = 23,
    kTerminalIDFieldNumber = 1,
    kNetTypeFieldNumber = 2,
    kVinFieldNumber = 3,
    kAdminModeFieldNumber = 4,
    kAuxVisiableFieldNumber = 5,
    kCooperateModeFieldNumber = 6,
    kOrderHistoryFieldNumber = 10,
    kFaultsHistoryFieldNumber = 11,
    kStopTypeFieldNumber = 12,
    kRateTypeFieldNumber = 13,
    kStandbylogoTypeFieldNumber = 14,
    kLedTypeFieldNumber = 15,
    kStopChgSocTypeFieldNumber = 16,
    kHmiConfigEnableFieldNumber = 17,
    kNetOfflineWifiEnableFieldNumber = 18,
    kVLPREnableFieldNumber = 19,
    kRatedPowerFieldNumber = 21,
    kMaxCurrentFieldNumber = 24,
  };
  // repeated .DMCinfo.ServerInfo servers = 9;
  int servers_size() const;
  private:
  int _internal_servers_size() const;
  public:
  void clear_servers();
  ::DMCinfo::ServerInfo* mutable_servers(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::ServerInfo >*
      mutable_servers();
  private:
  const ::DMCinfo::ServerInfo& _internal_servers(int index) const;
  ::DMCinfo::ServerInfo* _internal_add_servers();
  public:
  const ::DMCinfo::ServerInfo& servers(int index) const;
  ::DMCinfo::ServerInfo* add_servers();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::ServerInfo >&
      servers() const;

  // repeated .DMCinfo.GUNInfo gunInfo = 20;
  int guninfo_size() const;
  private:
  int _internal_guninfo_size() const;
  public:
  void clear_guninfo();
  ::DMCinfo::GUNInfo* mutable_guninfo(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::GUNInfo >*
      mutable_guninfo();
  private:
  const ::DMCinfo::GUNInfo& _internal_guninfo(int index) const;
  ::DMCinfo::GUNInfo* _internal_add_guninfo();
  public:
  const ::DMCinfo::GUNInfo& guninfo(int index) const;
  ::DMCinfo::GUNInfo* add_guninfo();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::GUNInfo >&
      guninfo() const;

  // bytes MACip = 7;
  void clear_macip();
  const std::string& macip() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_macip(ArgT0&& arg0, ArgT... args);
  std::string* mutable_macip();
  PROTOBUF_MUST_USE_RESULT std::string* release_macip();
  void set_allocated_macip(std::string* macip);
  private:
  const std::string& _internal_macip() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_macip(const std::string& value);
  std::string* _internal_mutable_macip();
  public:

  // bytes WiFiip = 8;
  void clear_wifiip();
  const std::string& wifiip() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_wifiip(ArgT0&& arg0, ArgT... args);
  std::string* mutable_wifiip();
  PROTOBUF_MUST_USE_RESULT std::string* release_wifiip();
  void set_allocated_wifiip(std::string* wifiip);
  private:
  const std::string& _internal_wifiip() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_wifiip(const std::string& value);
  std::string* _internal_mutable_wifiip();
  public:

  // string productModel = 22;
  void clear_productmodel();
  const std::string& productmodel() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_productmodel(ArgT0&& arg0, ArgT... args);
  std::string* mutable_productmodel();
  PROTOBUF_MUST_USE_RESULT std::string* release_productmodel();
  void set_allocated_productmodel(std::string* productmodel);
  private:
  const std::string& _internal_productmodel() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_productmodel(const std::string& value);
  std::string* _internal_mutable_productmodel();
  public:

  // string ccid = 23;
  void clear_ccid();
  const std::string& ccid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_ccid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_ccid();
  PROTOBUF_MUST_USE_RESULT std::string* release_ccid();
  void set_allocated_ccid(std::string* ccid);
  private:
  const std::string& _internal_ccid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_ccid(const std::string& value);
  std::string* _internal_mutable_ccid();
  public:

  // uint32 terminalID = 1;
  void clear_terminalid();
  ::PROTOBUF_NAMESPACE_ID::uint32 terminalid() const;
  void set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_terminalid() const;
  void _internal_set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 NetType = 2;
  void clear_nettype();
  ::PROTOBUF_NAMESPACE_ID::uint32 nettype() const;
  void set_nettype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_nettype() const;
  void _internal_set_nettype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 Vin = 3;
  void clear_vin();
  ::PROTOBUF_NAMESPACE_ID::uint32 vin() const;
  void set_vin(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_vin() const;
  void _internal_set_vin(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 AdminMode = 4;
  void clear_adminmode();
  ::PROTOBUF_NAMESPACE_ID::uint32 adminmode() const;
  void set_adminmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_adminmode() const;
  void _internal_set_adminmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 AuxVisiable = 5;
  void clear_auxvisiable();
  ::PROTOBUF_NAMESPACE_ID::uint32 auxvisiable() const;
  void set_auxvisiable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_auxvisiable() const;
  void _internal_set_auxvisiable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 CooperateMode = 6;
  void clear_cooperatemode();
  ::PROTOBUF_NAMESPACE_ID::uint32 cooperatemode() const;
  void set_cooperatemode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_cooperatemode() const;
  void _internal_set_cooperatemode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 OrderHistory = 10;
  void clear_orderhistory();
  ::PROTOBUF_NAMESPACE_ID::uint32 orderhistory() const;
  void set_orderhistory(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_orderhistory() const;
  void _internal_set_orderhistory(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 FaultsHistory = 11;
  void clear_faultshistory();
  ::PROTOBUF_NAMESPACE_ID::uint32 faultshistory() const;
  void set_faultshistory(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_faultshistory() const;
  void _internal_set_faultshistory(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 stopType = 12;
  void clear_stoptype();
  ::PROTOBUF_NAMESPACE_ID::uint32 stoptype() const;
  void set_stoptype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_stoptype() const;
  void _internal_set_stoptype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 RateType = 13;
  void clear_ratetype();
  ::PROTOBUF_NAMESPACE_ID::uint32 ratetype() const;
  void set_ratetype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_ratetype() const;
  void _internal_set_ratetype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 standbylogoType = 14;
  void clear_standbylogotype();
  ::PROTOBUF_NAMESPACE_ID::uint32 standbylogotype() const;
  void set_standbylogotype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_standbylogotype() const;
  void _internal_set_standbylogotype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 ledType = 15;
  void clear_ledtype();
  ::PROTOBUF_NAMESPACE_ID::uint32 ledtype() const;
  void set_ledtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_ledtype() const;
  void _internal_set_ledtype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 stopChgSocType = 16;
  void clear_stopchgsoctype();
  ::PROTOBUF_NAMESPACE_ID::uint32 stopchgsoctype() const;
  void set_stopchgsoctype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_stopchgsoctype() const;
  void _internal_set_stopchgsoctype(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 hmiConfigEnable = 17;
  void clear_hmiconfigenable();
  ::PROTOBUF_NAMESPACE_ID::uint32 hmiconfigenable() const;
  void set_hmiconfigenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_hmiconfigenable() const;
  void _internal_set_hmiconfigenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 netOfflineWifiEnable = 18;
  void clear_netofflinewifienable();
  ::PROTOBUF_NAMESPACE_ID::uint32 netofflinewifienable() const;
  void set_netofflinewifienable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_netofflinewifienable() const;
  void _internal_set_netofflinewifienable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 VLPREnable = 19;
  void clear_vlprenable();
  ::PROTOBUF_NAMESPACE_ID::uint32 vlprenable() const;
  void set_vlprenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_vlprenable() const;
  void _internal_set_vlprenable(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 ratedPower = 21;
  void clear_ratedpower();
  ::PROTOBUF_NAMESPACE_ID::uint32 ratedpower() const;
  void set_ratedpower(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_ratedpower() const;
  void _internal_set_ratedpower(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 maxCurrent = 24;
  void clear_maxcurrent();
  ::PROTOBUF_NAMESPACE_ID::uint32 maxcurrent() const;
  void set_maxcurrent(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_maxcurrent() const;
  void _internal_set_maxcurrent(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:DMCinfo.OHPInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::ServerInfo > servers_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::GUNInfo > guninfo_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr macip_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr wifiip_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr productmodel_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr ccid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 terminalid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 nettype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 vin_;
  ::PROTOBUF_NAMESPACE_ID::uint32 adminmode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 auxvisiable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 cooperatemode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 orderhistory_;
  ::PROTOBUF_NAMESPACE_ID::uint32 faultshistory_;
  ::PROTOBUF_NAMESPACE_ID::uint32 stoptype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 ratetype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 standbylogotype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 ledtype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 stopchgsoctype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 hmiconfigenable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 netofflinewifienable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 vlprenable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 ratedpower_;
  ::PROTOBUF_NAMESPACE_ID::uint32 maxcurrent_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class HmcChargingStrategyM final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.HmcChargingStrategyM) */ {
 public:
  inline HmcChargingStrategyM() : HmcChargingStrategyM(nullptr) {}
  ~HmcChargingStrategyM() override;
  explicit constexpr HmcChargingStrategyM(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HmcChargingStrategyM(const HmcChargingStrategyM& from);
  HmcChargingStrategyM(HmcChargingStrategyM&& from) noexcept
    : HmcChargingStrategyM() {
    *this = ::std::move(from);
  }

  inline HmcChargingStrategyM& operator=(const HmcChargingStrategyM& from) {
    CopyFrom(from);
    return *this;
  }
  inline HmcChargingStrategyM& operator=(HmcChargingStrategyM&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HmcChargingStrategyM& default_instance() {
    return *internal_default_instance();
  }
  static inline const HmcChargingStrategyM* internal_default_instance() {
    return reinterpret_cast<const HmcChargingStrategyM*>(
               &_HmcChargingStrategyM_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(HmcChargingStrategyM& a, HmcChargingStrategyM& b) {
    a.Swap(&b);
  }
  inline void Swap(HmcChargingStrategyM* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HmcChargingStrategyM* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HmcChargingStrategyM* New() const final {
    return new HmcChargingStrategyM();
  }

  HmcChargingStrategyM* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HmcChargingStrategyM>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HmcChargingStrategyM& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const HmcChargingStrategyM& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HmcChargingStrategyM* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.HmcChargingStrategyM";
  }
  protected:
  explicit HmcChargingStrategyM(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kChargeStrategyFieldNumber = 1,
    kChargeStarFieldNumber = 2,
    kStartTimeFieldNumber = 3,
    kEndTimeFieldNumber = 4,
  };
  // .DMCinfo.HmcChargeStrategyEnum chargeStrategy = 1;
  void clear_chargestrategy();
  ::DMCinfo::HmcChargeStrategyEnum chargestrategy() const;
  void set_chargestrategy(::DMCinfo::HmcChargeStrategyEnum value);
  private:
  ::DMCinfo::HmcChargeStrategyEnum _internal_chargestrategy() const;
  void _internal_set_chargestrategy(::DMCinfo::HmcChargeStrategyEnum value);
  public:

  // .DMCinfo.HmcChargeModeEnum chargeStar = 2;
  void clear_chargestar();
  ::DMCinfo::HmcChargeModeEnum chargestar() const;
  void set_chargestar(::DMCinfo::HmcChargeModeEnum value);
  private:
  ::DMCinfo::HmcChargeModeEnum _internal_chargestar() const;
  void _internal_set_chargestar(::DMCinfo::HmcChargeModeEnum value);
  public:

  // uint32 startTime = 3;
  void clear_starttime();
  ::PROTOBUF_NAMESPACE_ID::uint32 starttime() const;
  void set_starttime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_starttime() const;
  void _internal_set_starttime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 endTime = 4;
  void clear_endtime();
  ::PROTOBUF_NAMESPACE_ID::uint32 endtime() const;
  void set_endtime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_endtime() const;
  void _internal_set_endtime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:DMCinfo.HmcChargingStrategyM)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int chargestrategy_;
  int chargestar_;
  ::PROTOBUF_NAMESPACE_ID::uint32 starttime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 endtime_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class HMCInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.HMCInfo) */ {
 public:
  inline HMCInfo() : HMCInfo(nullptr) {}
  ~HMCInfo() override;
  explicit constexpr HMCInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HMCInfo(const HMCInfo& from);
  HMCInfo(HMCInfo&& from) noexcept
    : HMCInfo() {
    *this = ::std::move(from);
  }

  inline HMCInfo& operator=(const HMCInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline HMCInfo& operator=(HMCInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HMCInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const HMCInfo* internal_default_instance() {
    return reinterpret_cast<const HMCInfo*>(
               &_HMCInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(HMCInfo& a, HMCInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(HMCInfo* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HMCInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HMCInfo* New() const final {
    return new HMCInfo();
  }

  HMCInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HMCInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HMCInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const HMCInfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HMCInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.HMCInfo";
  }
  protected:
  explicit HMCInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPlatInfoFieldNumber = 5,
    kStrategyFieldNumber = 6,
    kTerminalCodeFieldNumber = 3,
    kTerminalIDFieldNumber = 1,
    kLcrTypeFieldNumber = 2,
    kSocFieldNumber = 4,
  };
  // repeated .DMCinfo.ServerInfo platInfo = 5;
  int platinfo_size() const;
  private:
  int _internal_platinfo_size() const;
  public:
  void clear_platinfo();
  ::DMCinfo::ServerInfo* mutable_platinfo(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::ServerInfo >*
      mutable_platinfo();
  private:
  const ::DMCinfo::ServerInfo& _internal_platinfo(int index) const;
  ::DMCinfo::ServerInfo* _internal_add_platinfo();
  public:
  const ::DMCinfo::ServerInfo& platinfo(int index) const;
  ::DMCinfo::ServerInfo* add_platinfo();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::ServerInfo >&
      platinfo() const;

  // repeated .DMCinfo.HmcChargingStrategyM strategy = 6;
  int strategy_size() const;
  private:
  int _internal_strategy_size() const;
  public:
  void clear_strategy();
  ::DMCinfo::HmcChargingStrategyM* mutable_strategy(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::HmcChargingStrategyM >*
      mutable_strategy();
  private:
  const ::DMCinfo::HmcChargingStrategyM& _internal_strategy(int index) const;
  ::DMCinfo::HmcChargingStrategyM* _internal_add_strategy();
  public:
  const ::DMCinfo::HmcChargingStrategyM& strategy(int index) const;
  ::DMCinfo::HmcChargingStrategyM* add_strategy();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::HmcChargingStrategyM >&
      strategy() const;

  // string terminalCode = 3;
  void clear_terminalcode();
  const std::string& terminalcode() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_terminalcode(ArgT0&& arg0, ArgT... args);
  std::string* mutable_terminalcode();
  PROTOBUF_MUST_USE_RESULT std::string* release_terminalcode();
  void set_allocated_terminalcode(std::string* terminalcode);
  private:
  const std::string& _internal_terminalcode() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_terminalcode(const std::string& value);
  std::string* _internal_mutable_terminalcode();
  public:

  // uint32 terminalID = 1;
  void clear_terminalid();
  ::PROTOBUF_NAMESPACE_ID::uint32 terminalid() const;
  void set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_terminalid() const;
  void _internal_set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // .DMCinfo.HmcLcrEnum lcrType = 2;
  void clear_lcrtype();
  ::DMCinfo::HmcLcrEnum lcrtype() const;
  void set_lcrtype(::DMCinfo::HmcLcrEnum value);
  private:
  ::DMCinfo::HmcLcrEnum _internal_lcrtype() const;
  void _internal_set_lcrtype(::DMCinfo::HmcLcrEnum value);
  public:

  // uint32 soc = 4;
  void clear_soc();
  ::PROTOBUF_NAMESPACE_ID::uint32 soc() const;
  void set_soc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_soc() const;
  void _internal_set_soc(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:DMCinfo.HMCInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::ServerInfo > platinfo_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::HmcChargingStrategyM > strategy_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr terminalcode_;
  ::PROTOBUF_NAMESPACE_ID::uint32 terminalid_;
  int lcrtype_;
  ::PROTOBUF_NAMESPACE_ID::uint32 soc_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class GunLoadConstraint final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.GunLoadConstraint) */ {
 public:
  inline GunLoadConstraint() : GunLoadConstraint(nullptr) {}
  ~GunLoadConstraint() override;
  explicit constexpr GunLoadConstraint(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GunLoadConstraint(const GunLoadConstraint& from);
  GunLoadConstraint(GunLoadConstraint&& from) noexcept
    : GunLoadConstraint() {
    *this = ::std::move(from);
  }

  inline GunLoadConstraint& operator=(const GunLoadConstraint& from) {
    CopyFrom(from);
    return *this;
  }
  inline GunLoadConstraint& operator=(GunLoadConstraint&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GunLoadConstraint& default_instance() {
    return *internal_default_instance();
  }
  static inline const GunLoadConstraint* internal_default_instance() {
    return reinterpret_cast<const GunLoadConstraint*>(
               &_GunLoadConstraint_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(GunLoadConstraint& a, GunLoadConstraint& b) {
    a.Swap(&b);
  }
  inline void Swap(GunLoadConstraint* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GunLoadConstraint* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GunLoadConstraint* New() const final {
    return new GunLoadConstraint();
  }

  GunLoadConstraint* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GunLoadConstraint>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GunLoadConstraint& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GunLoadConstraint& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GunLoadConstraint* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.GunLoadConstraint";
  }
  protected:
  explicit GunLoadConstraint(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGunNumFieldNumber = 3,
    kTerminalIDFieldNumber = 1,
    kGunIDFieldNumber = 2,
    kGunlimitPowerFieldNumber = 4,
  };
  // repeated uint32 gunNum = 3;
  int gunnum_size() const;
  private:
  int _internal_gunnum_size() const;
  public:
  void clear_gunnum();
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_gunnum(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >&
      _internal_gunnum() const;
  void _internal_add_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >*
      _internal_mutable_gunnum();
  public:
  ::PROTOBUF_NAMESPACE_ID::uint32 gunnum(int index) const;
  void set_gunnum(int index, ::PROTOBUF_NAMESPACE_ID::uint32 value);
  void add_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >&
      gunnum() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >*
      mutable_gunnum();

  // uint32 terminalID = 1;
  void clear_terminalid();
  ::PROTOBUF_NAMESPACE_ID::uint32 terminalid() const;
  void set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_terminalid() const;
  void _internal_set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 gunID = 2;
  void clear_gunid();
  ::PROTOBUF_NAMESPACE_ID::uint32 gunid() const;
  void set_gunid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_gunid() const;
  void _internal_set_gunid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 GunlimitPower = 4;
  void clear_gunlimitpower();
  ::PROTOBUF_NAMESPACE_ID::uint32 gunlimitpower() const;
  void set_gunlimitpower(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_gunlimitpower() const;
  void _internal_set_gunlimitpower(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:DMCinfo.GunLoadConstraint)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 > gunnum_;
  mutable std::atomic<int> _gunnum_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 terminalid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 gunid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 gunlimitpower_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class GunMatchInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.GunMatchInfo) */ {
 public:
  inline GunMatchInfo() : GunMatchInfo(nullptr) {}
  ~GunMatchInfo() override;
  explicit constexpr GunMatchInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GunMatchInfo(const GunMatchInfo& from);
  GunMatchInfo(GunMatchInfo&& from) noexcept
    : GunMatchInfo() {
    *this = ::std::move(from);
  }

  inline GunMatchInfo& operator=(const GunMatchInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline GunMatchInfo& operator=(GunMatchInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GunMatchInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const GunMatchInfo* internal_default_instance() {
    return reinterpret_cast<const GunMatchInfo*>(
               &_GunMatchInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(GunMatchInfo& a, GunMatchInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(GunMatchInfo* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GunMatchInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GunMatchInfo* New() const final {
    return new GunMatchInfo();
  }

  GunMatchInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GunMatchInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GunMatchInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GunMatchInfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GunMatchInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.GunMatchInfo";
  }
  protected:
  explicit GunMatchInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGunNumFieldNumber = 2,
    kGunIDFieldNumber = 1,
    kIsAdjacentFieldNumber = 3,
  };
  // repeated uint32 gunNum = 2;
  int gunnum_size() const;
  private:
  int _internal_gunnum_size() const;
  public:
  void clear_gunnum();
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_gunnum(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >&
      _internal_gunnum() const;
  void _internal_add_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >*
      _internal_mutable_gunnum();
  public:
  ::PROTOBUF_NAMESPACE_ID::uint32 gunnum(int index) const;
  void set_gunnum(int index, ::PROTOBUF_NAMESPACE_ID::uint32 value);
  void add_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >&
      gunnum() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >*
      mutable_gunnum();

  // uint32 gunID = 1;
  void clear_gunid();
  ::PROTOBUF_NAMESPACE_ID::uint32 gunid() const;
  void set_gunid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_gunid() const;
  void _internal_set_gunid(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 isAdjacent = 3;
  void clear_isadjacent();
  ::PROTOBUF_NAMESPACE_ID::uint32 isadjacent() const;
  void set_isadjacent(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_isadjacent() const;
  void _internal_set_isadjacent(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:DMCinfo.GunMatchInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 > gunnum_;
  mutable std::atomic<int> _gunnum_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 gunid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 isadjacent_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class CurrentBalance final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.CurrentBalance) */ {
 public:
  inline CurrentBalance() : CurrentBalance(nullptr) {}
  ~CurrentBalance() override;
  explicit constexpr CurrentBalance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CurrentBalance(const CurrentBalance& from);
  CurrentBalance(CurrentBalance&& from) noexcept
    : CurrentBalance() {
    *this = ::std::move(from);
  }

  inline CurrentBalance& operator=(const CurrentBalance& from) {
    CopyFrom(from);
    return *this;
  }
  inline CurrentBalance& operator=(CurrentBalance&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CurrentBalance& default_instance() {
    return *internal_default_instance();
  }
  static inline const CurrentBalance* internal_default_instance() {
    return reinterpret_cast<const CurrentBalance*>(
               &_CurrentBalance_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(CurrentBalance& a, CurrentBalance& b) {
    a.Swap(&b);
  }
  inline void Swap(CurrentBalance* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CurrentBalance* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CurrentBalance* New() const final {
    return new CurrentBalance();
  }

  CurrentBalance* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CurrentBalance>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CurrentBalance& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CurrentBalance& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CurrentBalance* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.CurrentBalance";
  }
  protected:
  explicit CurrentBalance(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEqualChgCurrCoefFieldNumber = 1,
    kAllowEqualChgTimeFieldNumber = 2,
  };
  // float EqualChgCurrCoef = 1;
  void clear_equalchgcurrcoef();
  float equalchgcurrcoef() const;
  void set_equalchgcurrcoef(float value);
  private:
  float _internal_equalchgcurrcoef() const;
  void _internal_set_equalchgcurrcoef(float value);
  public:

  // uint32 AllowEqualChgTime = 2;
  void clear_allowequalchgtime();
  ::PROTOBUF_NAMESPACE_ID::uint32 allowequalchgtime() const;
  void set_allowequalchgtime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_allowequalchgtime() const;
  void _internal_set_allowequalchgtime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:DMCinfo.CurrentBalance)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float equalchgcurrcoef_;
  ::PROTOBUF_NAMESPACE_ID::uint32 allowequalchgtime_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class HotRunaway final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.HotRunaway) */ {
 public:
  inline HotRunaway() : HotRunaway(nullptr) {}
  ~HotRunaway() override;
  explicit constexpr HotRunaway(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HotRunaway(const HotRunaway& from);
  HotRunaway(HotRunaway&& from) noexcept
    : HotRunaway() {
    *this = ::std::move(from);
  }

  inline HotRunaway& operator=(const HotRunaway& from) {
    CopyFrom(from);
    return *this;
  }
  inline HotRunaway& operator=(HotRunaway&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HotRunaway& default_instance() {
    return *internal_default_instance();
  }
  static inline const HotRunaway* internal_default_instance() {
    return reinterpret_cast<const HotRunaway*>(
               &_HotRunaway_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(HotRunaway& a, HotRunaway& b) {
    a.Swap(&b);
  }
  inline void Swap(HotRunaway* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HotRunaway* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HotRunaway* New() const final {
    return new HotRunaway();
  }

  HotRunaway* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HotRunaway>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HotRunaway& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const HotRunaway& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HotRunaway* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.HotRunaway";
  }
  protected:
  explicit HotRunaway(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHotRunThresholdFieldNumber = 1,
    kHotRunConfireTimeFieldNumber = 2,
    kHotRunProtDisabledFieldNumber = 3,
  };
  // float HotRunThreshold = 1;
  void clear_hotrunthreshold();
  float hotrunthreshold() const;
  void set_hotrunthreshold(float value);
  private:
  float _internal_hotrunthreshold() const;
  void _internal_set_hotrunthreshold(float value);
  public:

  // uint32 HotRunConfireTime = 2;
  void clear_hotrunconfiretime();
  ::PROTOBUF_NAMESPACE_ID::uint32 hotrunconfiretime() const;
  void set_hotrunconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_hotrunconfiretime() const;
  void _internal_set_hotrunconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 HotRunProtDisabled = 3;
  void clear_hotrunprotdisabled();
  ::PROTOBUF_NAMESPACE_ID::uint32 hotrunprotdisabled() const;
  void set_hotrunprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_hotrunprotdisabled() const;
  void _internal_set_hotrunprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:DMCinfo.HotRunaway)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float hotrunthreshold_;
  ::PROTOBUF_NAMESPACE_ID::uint32 hotrunconfiretime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 hotrunprotdisabled_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class CellOverVolt final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.CellOverVolt) */ {
 public:
  inline CellOverVolt() : CellOverVolt(nullptr) {}
  ~CellOverVolt() override;
  explicit constexpr CellOverVolt(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CellOverVolt(const CellOverVolt& from);
  CellOverVolt(CellOverVolt&& from) noexcept
    : CellOverVolt() {
    *this = ::std::move(from);
  }

  inline CellOverVolt& operator=(const CellOverVolt& from) {
    CopyFrom(from);
    return *this;
  }
  inline CellOverVolt& operator=(CellOverVolt&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CellOverVolt& default_instance() {
    return *internal_default_instance();
  }
  static inline const CellOverVolt* internal_default_instance() {
    return reinterpret_cast<const CellOverVolt*>(
               &_CellOverVolt_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(CellOverVolt& a, CellOverVolt& b) {
    a.Swap(&b);
  }
  inline void Swap(CellOverVolt* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CellOverVolt* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CellOverVolt* New() const final {
    return new CellOverVolt();
  }

  CellOverVolt* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CellOverVolt>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CellOverVolt& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CellOverVolt& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CellOverVolt* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.CellOverVolt";
  }
  protected:
  explicit CellOverVolt(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLiFePO4CellAllowedChgVoltMaxFieldNumber = 1,
    kLiMnNiCoCellAllowedChgVoltMaxFieldNumber = 2,
    kLiTitanateCellAllowedChgVoltMaxFieldNumber = 3,
    kLiManganateCellAllowedChgVoltMaxFieldNumber = 4,
    kCellOverVoltConfireTimeFieldNumber = 5,
    kCellOverVoltProtDisabledFieldNumber = 6,
  };
  // float LiFePO4CellAllowedChgVoltMax = 1;
  void clear_lifepo4cellallowedchgvoltmax();
  float lifepo4cellallowedchgvoltmax() const;
  void set_lifepo4cellallowedchgvoltmax(float value);
  private:
  float _internal_lifepo4cellallowedchgvoltmax() const;
  void _internal_set_lifepo4cellallowedchgvoltmax(float value);
  public:

  // float LiMnNiCoCellAllowedChgVoltMax = 2;
  void clear_limnnicocellallowedchgvoltmax();
  float limnnicocellallowedchgvoltmax() const;
  void set_limnnicocellallowedchgvoltmax(float value);
  private:
  float _internal_limnnicocellallowedchgvoltmax() const;
  void _internal_set_limnnicocellallowedchgvoltmax(float value);
  public:

  // float LiTitanateCellAllowedChgVoltMax = 3;
  void clear_lititanatecellallowedchgvoltmax();
  float lititanatecellallowedchgvoltmax() const;
  void set_lititanatecellallowedchgvoltmax(float value);
  private:
  float _internal_lititanatecellallowedchgvoltmax() const;
  void _internal_set_lititanatecellallowedchgvoltmax(float value);
  public:

  // float LiManganateCellAllowedChgVoltMax = 4;
  void clear_limanganatecellallowedchgvoltmax();
  float limanganatecellallowedchgvoltmax() const;
  void set_limanganatecellallowedchgvoltmax(float value);
  private:
  float _internal_limanganatecellallowedchgvoltmax() const;
  void _internal_set_limanganatecellallowedchgvoltmax(float value);
  public:

  // uint32 CellOverVoltConfireTime = 5;
  void clear_cellovervoltconfiretime();
  ::PROTOBUF_NAMESPACE_ID::uint32 cellovervoltconfiretime() const;
  void set_cellovervoltconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_cellovervoltconfiretime() const;
  void _internal_set_cellovervoltconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 CellOverVoltProtDisabled = 6;
  void clear_cellovervoltprotdisabled();
  ::PROTOBUF_NAMESPACE_ID::uint32 cellovervoltprotdisabled() const;
  void set_cellovervoltprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_cellovervoltprotdisabled() const;
  void _internal_set_cellovervoltprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:DMCinfo.CellOverVolt)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float lifepo4cellallowedchgvoltmax_;
  float limnnicocellallowedchgvoltmax_;
  float lititanatecellallowedchgvoltmax_;
  float limanganatecellallowedchgvoltmax_;
  ::PROTOBUF_NAMESPACE_ID::uint32 cellovervoltconfiretime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 cellovervoltprotdisabled_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class PackOverVolt final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.PackOverVolt) */ {
 public:
  inline PackOverVolt() : PackOverVolt(nullptr) {}
  ~PackOverVolt() override;
  explicit constexpr PackOverVolt(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PackOverVolt(const PackOverVolt& from);
  PackOverVolt(PackOverVolt&& from) noexcept
    : PackOverVolt() {
    *this = ::std::move(from);
  }

  inline PackOverVolt& operator=(const PackOverVolt& from) {
    CopyFrom(from);
    return *this;
  }
  inline PackOverVolt& operator=(PackOverVolt&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PackOverVolt& default_instance() {
    return *internal_default_instance();
  }
  static inline const PackOverVolt* internal_default_instance() {
    return reinterpret_cast<const PackOverVolt*>(
               &_PackOverVolt_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(PackOverVolt& a, PackOverVolt& b) {
    a.Swap(&b);
  }
  inline void Swap(PackOverVolt* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PackOverVolt* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline PackOverVolt* New() const final {
    return new PackOverVolt();
  }

  PackOverVolt* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<PackOverVolt>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PackOverVolt& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PackOverVolt& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PackOverVolt* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.PackOverVolt";
  }
  protected:
  explicit PackOverVolt(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPackAllowedChgVoltMaxFieldNumber = 1,
    kPackOverVoltConfireTimeFieldNumber = 2,
    kPackOverVoltProtDisabledFieldNumber = 3,
  };
  // float PackAllowedChgVoltMax = 1;
  void clear_packallowedchgvoltmax();
  float packallowedchgvoltmax() const;
  void set_packallowedchgvoltmax(float value);
  private:
  float _internal_packallowedchgvoltmax() const;
  void _internal_set_packallowedchgvoltmax(float value);
  public:

  // uint32 PackOverVoltConfireTime = 2;
  void clear_packovervoltconfiretime();
  ::PROTOBUF_NAMESPACE_ID::uint32 packovervoltconfiretime() const;
  void set_packovervoltconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_packovervoltconfiretime() const;
  void _internal_set_packovervoltconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 PackOverVoltProtDisabled = 3;
  void clear_packovervoltprotdisabled();
  ::PROTOBUF_NAMESPACE_ID::uint32 packovervoltprotdisabled() const;
  void set_packovervoltprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_packovervoltprotdisabled() const;
  void _internal_set_packovervoltprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:DMCinfo.PackOverVolt)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float packallowedchgvoltmax_;
  ::PROTOBUF_NAMESPACE_ID::uint32 packovervoltconfiretime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 packovervoltprotdisabled_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class OverCurrent final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.OverCurrent) */ {
 public:
  inline OverCurrent() : OverCurrent(nullptr) {}
  ~OverCurrent() override;
  explicit constexpr OverCurrent(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OverCurrent(const OverCurrent& from);
  OverCurrent(OverCurrent&& from) noexcept
    : OverCurrent() {
    *this = ::std::move(from);
  }

  inline OverCurrent& operator=(const OverCurrent& from) {
    CopyFrom(from);
    return *this;
  }
  inline OverCurrent& operator=(OverCurrent&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OverCurrent& default_instance() {
    return *internal_default_instance();
  }
  static inline const OverCurrent* internal_default_instance() {
    return reinterpret_cast<const OverCurrent*>(
               &_OverCurrent_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(OverCurrent& a, OverCurrent& b) {
    a.Swap(&b);
  }
  inline void Swap(OverCurrent* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OverCurrent* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OverCurrent* New() const final {
    return new OverCurrent();
  }

  OverCurrent* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OverCurrent>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OverCurrent& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const OverCurrent& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OverCurrent* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.OverCurrent";
  }
  protected:
  explicit OverCurrent(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOverCurrThresholdFieldNumber = 1,
    kOverCurrConfireTimeFieldNumber = 2,
    kOverCurrProtDisabledFieldNumber = 3,
  };
  // float OverCurrThreshold = 1;
  void clear_overcurrthreshold();
  float overcurrthreshold() const;
  void set_overcurrthreshold(float value);
  private:
  float _internal_overcurrthreshold() const;
  void _internal_set_overcurrthreshold(float value);
  public:

  // uint32 OverCurrConfireTime = 2;
  void clear_overcurrconfiretime();
  ::PROTOBUF_NAMESPACE_ID::uint32 overcurrconfiretime() const;
  void set_overcurrconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_overcurrconfiretime() const;
  void _internal_set_overcurrconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 OverCurrProtDisabled = 3;
  void clear_overcurrprotdisabled();
  ::PROTOBUF_NAMESPACE_ID::uint32 overcurrprotdisabled() const;
  void set_overcurrprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_overcurrprotdisabled() const;
  void _internal_set_overcurrprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:DMCinfo.OverCurrent)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float overcurrthreshold_;
  ::PROTOBUF_NAMESPACE_ID::uint32 overcurrconfiretime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 overcurrprotdisabled_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class CellOverTemp final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.CellOverTemp) */ {
 public:
  inline CellOverTemp() : CellOverTemp(nullptr) {}
  ~CellOverTemp() override;
  explicit constexpr CellOverTemp(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CellOverTemp(const CellOverTemp& from);
  CellOverTemp(CellOverTemp&& from) noexcept
    : CellOverTemp() {
    *this = ::std::move(from);
  }

  inline CellOverTemp& operator=(const CellOverTemp& from) {
    CopyFrom(from);
    return *this;
  }
  inline CellOverTemp& operator=(CellOverTemp&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CellOverTemp& default_instance() {
    return *internal_default_instance();
  }
  static inline const CellOverTemp* internal_default_instance() {
    return reinterpret_cast<const CellOverTemp*>(
               &_CellOverTemp_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(CellOverTemp& a, CellOverTemp& b) {
    a.Swap(&b);
  }
  inline void Swap(CellOverTemp* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CellOverTemp* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CellOverTemp* New() const final {
    return new CellOverTemp();
  }

  CellOverTemp* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CellOverTemp>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CellOverTemp& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CellOverTemp& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CellOverTemp* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.CellOverTemp";
  }
  protected:
  explicit CellOverTemp(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLiFePO4OverTempThresholdFieldNumber = 1,
    kLiMnNiCoOverTempThresholdFieldNumber = 2,
    kLiTitanateOverTempThresholdFieldNumber = 3,
    kLiManganateOverTempThresholdFieldNumber = 4,
    kOverTempConfireTimeFieldNumber = 5,
    kOverTempProtDisabledFieldNumber = 6,
  };
  // float LiFePO4OverTempThreshold = 1;
  void clear_lifepo4overtempthreshold();
  float lifepo4overtempthreshold() const;
  void set_lifepo4overtempthreshold(float value);
  private:
  float _internal_lifepo4overtempthreshold() const;
  void _internal_set_lifepo4overtempthreshold(float value);
  public:

  // float LiMnNiCoOverTempThreshold = 2;
  void clear_limnnicoovertempthreshold();
  float limnnicoovertempthreshold() const;
  void set_limnnicoovertempthreshold(float value);
  private:
  float _internal_limnnicoovertempthreshold() const;
  void _internal_set_limnnicoovertempthreshold(float value);
  public:

  // float LiTitanateOverTempThreshold = 3;
  void clear_lititanateovertempthreshold();
  float lititanateovertempthreshold() const;
  void set_lititanateovertempthreshold(float value);
  private:
  float _internal_lititanateovertempthreshold() const;
  void _internal_set_lititanateovertempthreshold(float value);
  public:

  // float LiManganateOverTempThreshold = 4;
  void clear_limanganateovertempthreshold();
  float limanganateovertempthreshold() const;
  void set_limanganateovertempthreshold(float value);
  private:
  float _internal_limanganateovertempthreshold() const;
  void _internal_set_limanganateovertempthreshold(float value);
  public:

  // uint32 OverTempConfireTime = 5;
  void clear_overtempconfiretime();
  ::PROTOBUF_NAMESPACE_ID::uint32 overtempconfiretime() const;
  void set_overtempconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_overtempconfiretime() const;
  void _internal_set_overtempconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 OverTempProtDisabled = 6;
  void clear_overtempprotdisabled();
  ::PROTOBUF_NAMESPACE_ID::uint32 overtempprotdisabled() const;
  void set_overtempprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_overtempprotdisabled() const;
  void _internal_set_overtempprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:DMCinfo.CellOverTemp)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float lifepo4overtempthreshold_;
  float limnnicoovertempthreshold_;
  float lititanateovertempthreshold_;
  float limanganateovertempthreshold_;
  ::PROTOBUF_NAMESPACE_ID::uint32 overtempconfiretime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 overtempprotdisabled_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class LowTemp final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.LowTemp) */ {
 public:
  inline LowTemp() : LowTemp(nullptr) {}
  ~LowTemp() override;
  explicit constexpr LowTemp(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LowTemp(const LowTemp& from);
  LowTemp(LowTemp&& from) noexcept
    : LowTemp() {
    *this = ::std::move(from);
  }

  inline LowTemp& operator=(const LowTemp& from) {
    CopyFrom(from);
    return *this;
  }
  inline LowTemp& operator=(LowTemp&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LowTemp& default_instance() {
    return *internal_default_instance();
  }
  static inline const LowTemp* internal_default_instance() {
    return reinterpret_cast<const LowTemp*>(
               &_LowTemp_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(LowTemp& a, LowTemp& b) {
    a.Swap(&b);
  }
  inline void Swap(LowTemp* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LowTemp* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline LowTemp* New() const final {
    return new LowTemp();
  }

  LowTemp* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<LowTemp>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LowTemp& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LowTemp& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LowTemp* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.LowTemp";
  }
  protected:
  explicit LowTemp(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLowTempThresholdFieldNumber = 1,
    kLowTempConfireTimeFieldNumber = 2,
    kLowTempProtDisabledFieldNumber = 3,
  };
  // float LowTempThreshold = 1;
  void clear_lowtempthreshold();
  float lowtempthreshold() const;
  void set_lowtempthreshold(float value);
  private:
  float _internal_lowtempthreshold() const;
  void _internal_set_lowtempthreshold(float value);
  public:

  // uint32 LowTempConfireTime = 2;
  void clear_lowtempconfiretime();
  ::PROTOBUF_NAMESPACE_ID::uint32 lowtempconfiretime() const;
  void set_lowtempconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_lowtempconfiretime() const;
  void _internal_set_lowtempconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 LowTempProtDisabled = 3;
  void clear_lowtempprotdisabled();
  ::PROTOBUF_NAMESPACE_ID::uint32 lowtempprotdisabled() const;
  void set_lowtempprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_lowtempprotdisabled() const;
  void _internal_set_lowtempprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:DMCinfo.LowTemp)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float lowtempthreshold_;
  ::PROTOBUF_NAMESPACE_ID::uint32 lowtempconfiretime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 lowtempprotdisabled_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class BMSRlyStick final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.BMSRlyStick) */ {
 public:
  inline BMSRlyStick() : BMSRlyStick(nullptr) {}
  ~BMSRlyStick() override;
  explicit constexpr BMSRlyStick(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BMSRlyStick(const BMSRlyStick& from);
  BMSRlyStick(BMSRlyStick&& from) noexcept
    : BMSRlyStick() {
    *this = ::std::move(from);
  }

  inline BMSRlyStick& operator=(const BMSRlyStick& from) {
    CopyFrom(from);
    return *this;
  }
  inline BMSRlyStick& operator=(BMSRlyStick&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BMSRlyStick& default_instance() {
    return *internal_default_instance();
  }
  static inline const BMSRlyStick* internal_default_instance() {
    return reinterpret_cast<const BMSRlyStick*>(
               &_BMSRlyStick_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(BMSRlyStick& a, BMSRlyStick& b) {
    a.Swap(&b);
  }
  inline void Swap(BMSRlyStick* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BMSRlyStick* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BMSRlyStick* New() const final {
    return new BMSRlyStick();
  }

  BMSRlyStick* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BMSRlyStick>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BMSRlyStick& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const BMSRlyStick& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BMSRlyStick* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.BMSRlyStick";
  }
  protected:
  explicit BMSRlyStick(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBMSRlyStickVoltThresholdFieldNumber = 1,
    kBMSRlyStickConfireTimeFieldNumber = 2,
    kBMSRlyStickDisabledFieldNumber = 3,
  };
  // float BMSRlyStickVoltThreshold = 1;
  void clear_bmsrlystickvoltthreshold();
  float bmsrlystickvoltthreshold() const;
  void set_bmsrlystickvoltthreshold(float value);
  private:
  float _internal_bmsrlystickvoltthreshold() const;
  void _internal_set_bmsrlystickvoltthreshold(float value);
  public:

  // uint32 BMSRlyStickConfireTime = 2;
  void clear_bmsrlystickconfiretime();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsrlystickconfiretime() const;
  void set_bmsrlystickconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsrlystickconfiretime() const;
  void _internal_set_bmsrlystickconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 BMSRlyStickDisabled = 3;
  void clear_bmsrlystickdisabled();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsrlystickdisabled() const;
  void set_bmsrlystickdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsrlystickdisabled() const;
  void _internal_set_bmsrlystickdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:DMCinfo.BMSRlyStick)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float bmsrlystickvoltthreshold_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsrlystickconfiretime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsrlystickdisabled_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class BMSRlyOC final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.BMSRlyOC) */ {
 public:
  inline BMSRlyOC() : BMSRlyOC(nullptr) {}
  ~BMSRlyOC() override;
  explicit constexpr BMSRlyOC(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BMSRlyOC(const BMSRlyOC& from);
  BMSRlyOC(BMSRlyOC&& from) noexcept
    : BMSRlyOC() {
    *this = ::std::move(from);
  }

  inline BMSRlyOC& operator=(const BMSRlyOC& from) {
    CopyFrom(from);
    return *this;
  }
  inline BMSRlyOC& operator=(BMSRlyOC&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BMSRlyOC& default_instance() {
    return *internal_default_instance();
  }
  static inline const BMSRlyOC* internal_default_instance() {
    return reinterpret_cast<const BMSRlyOC*>(
               &_BMSRlyOC_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(BMSRlyOC& a, BMSRlyOC& b) {
    a.Swap(&b);
  }
  inline void Swap(BMSRlyOC* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BMSRlyOC* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BMSRlyOC* New() const final {
    return new BMSRlyOC();
  }

  BMSRlyOC* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BMSRlyOC>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BMSRlyOC& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const BMSRlyOC& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BMSRlyOC* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.BMSRlyOC";
  }
  protected:
  explicit BMSRlyOC(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBMSRlyOCVoltThresholdFieldNumber = 1,
    kBMSRlyOCCurrentThresholdFieldNumber = 2,
    kBMSRlyOCConfireTimeFieldNumber = 3,
    kBMSRlyOCDisabledFieldNumber = 4,
  };
  // float BMSRlyOCVoltThreshold = 1;
  void clear_bmsrlyocvoltthreshold();
  float bmsrlyocvoltthreshold() const;
  void set_bmsrlyocvoltthreshold(float value);
  private:
  float _internal_bmsrlyocvoltthreshold() const;
  void _internal_set_bmsrlyocvoltthreshold(float value);
  public:

  // float BMSRlyOCCurrentThreshold = 2;
  void clear_bmsrlyoccurrentthreshold();
  float bmsrlyoccurrentthreshold() const;
  void set_bmsrlyoccurrentthreshold(float value);
  private:
  float _internal_bmsrlyoccurrentthreshold() const;
  void _internal_set_bmsrlyoccurrentthreshold(float value);
  public:

  // uint32 BMSRlyOCConfireTime = 3;
  void clear_bmsrlyocconfiretime();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsrlyocconfiretime() const;
  void set_bmsrlyocconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsrlyocconfiretime() const;
  void _internal_set_bmsrlyocconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 BMSRlyOCDisabled = 4;
  void clear_bmsrlyocdisabled();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsrlyocdisabled() const;
  void set_bmsrlyocdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsrlyocdisabled() const;
  void _internal_set_bmsrlyocdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:DMCinfo.BMSRlyOC)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float bmsrlyocvoltthreshold_;
  float bmsrlyoccurrentthreshold_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsrlyocconfiretime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsrlyocdisabled_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class OverCharge final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.OverCharge) */ {
 public:
  inline OverCharge() : OverCharge(nullptr) {}
  ~OverCharge() override;
  explicit constexpr OverCharge(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OverCharge(const OverCharge& from);
  OverCharge(OverCharge&& from) noexcept
    : OverCharge() {
    *this = ::std::move(from);
  }

  inline OverCharge& operator=(const OverCharge& from) {
    CopyFrom(from);
    return *this;
  }
  inline OverCharge& operator=(OverCharge&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OverCharge& default_instance() {
    return *internal_default_instance();
  }
  static inline const OverCharge* internal_default_instance() {
    return reinterpret_cast<const OverCharge*>(
               &_OverCharge_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(OverCharge& a, OverCharge& b) {
    a.Swap(&b);
  }
  inline void Swap(OverCharge* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OverCharge* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OverCharge* New() const final {
    return new OverCharge();
  }

  OverCharge* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OverCharge>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OverCharge& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const OverCharge& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OverCharge* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.OverCharge";
  }
  protected:
  explicit OverCharge(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOverChgCoefFieldNumber = 1,
    kOverChgAHFieldNumber = 2,
    kOverChgConfireTimeFieldNumber = 3,
    kOverChgProtDisabledFieldNumber = 4,
  };
  // float OverChgCoef = 1;
  void clear_overchgcoef();
  float overchgcoef() const;
  void set_overchgcoef(float value);
  private:
  float _internal_overchgcoef() const;
  void _internal_set_overchgcoef(float value);
  public:

  // float OverChgAH = 2;
  void clear_overchgah();
  float overchgah() const;
  void set_overchgah(float value);
  private:
  float _internal_overchgah() const;
  void _internal_set_overchgah(float value);
  public:

  // uint32 OverChgConfireTime = 3;
  void clear_overchgconfiretime();
  ::PROTOBUF_NAMESPACE_ID::uint32 overchgconfiretime() const;
  void set_overchgconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_overchgconfiretime() const;
  void _internal_set_overchgconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 OverChgProtDisabled = 4;
  void clear_overchgprotdisabled();
  ::PROTOBUF_NAMESPACE_ID::uint32 overchgprotdisabled() const;
  void set_overchgprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_overchgprotdisabled() const;
  void _internal_set_overchgprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:DMCinfo.OverCharge)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float overchgcoef_;
  float overchgah_;
  ::PROTOBUF_NAMESPACE_ID::uint32 overchgconfiretime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 overchgprotdisabled_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class BMSDataRepeat final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.BMSDataRepeat) */ {
 public:
  inline BMSDataRepeat() : BMSDataRepeat(nullptr) {}
  ~BMSDataRepeat() override;
  explicit constexpr BMSDataRepeat(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BMSDataRepeat(const BMSDataRepeat& from);
  BMSDataRepeat(BMSDataRepeat&& from) noexcept
    : BMSDataRepeat() {
    *this = ::std::move(from);
  }

  inline BMSDataRepeat& operator=(const BMSDataRepeat& from) {
    CopyFrom(from);
    return *this;
  }
  inline BMSDataRepeat& operator=(BMSDataRepeat&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BMSDataRepeat& default_instance() {
    return *internal_default_instance();
  }
  static inline const BMSDataRepeat* internal_default_instance() {
    return reinterpret_cast<const BMSDataRepeat*>(
               &_BMSDataRepeat_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(BMSDataRepeat& a, BMSDataRepeat& b) {
    a.Swap(&b);
  }
  inline void Swap(BMSDataRepeat* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BMSDataRepeat* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BMSDataRepeat* New() const final {
    return new BMSDataRepeat();
  }

  BMSDataRepeat* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BMSDataRepeat>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BMSDataRepeat& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const BMSDataRepeat& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BMSDataRepeat* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.BMSDataRepeat";
  }
  protected:
  explicit BMSDataRepeat(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBMSDataRepeatConfireTimeFieldNumber = 1,
    kBMBMSDataErrorProtDisabledFieldNumber = 2,
    kBMSDataErrorProtDisabledFieldNumber = 3,
  };
  // uint32 BMSDataRepeatConfireTime = 1;
  void clear_bmsdatarepeatconfiretime();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsdatarepeatconfiretime() const;
  void set_bmsdatarepeatconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsdatarepeatconfiretime() const;
  void _internal_set_bmsdatarepeatconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 BMBMSDataErrorProtDisabled = 2;
  void clear_bmbmsdataerrorprotdisabled();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmbmsdataerrorprotdisabled() const;
  void set_bmbmsdataerrorprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmbmsdataerrorprotdisabled() const;
  void _internal_set_bmbmsdataerrorprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 BMSDataErrorProtDisabled = 3;
  void clear_bmsdataerrorprotdisabled();
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsdataerrorprotdisabled() const;
  void set_bmsdataerrorprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_bmsdataerrorprotdisabled() const;
  void _internal_set_bmsdataerrorprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:DMCinfo.BMSDataRepeat)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsdatarepeatconfiretime_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmbmsdataerrorprotdisabled_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bmsdataerrorprotdisabled_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// -------------------------------------------------------------------

class ActiveProtectParam final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:DMCinfo.ActiveProtectParam) */ {
 public:
  inline ActiveProtectParam() : ActiveProtectParam(nullptr) {}
  ~ActiveProtectParam() override;
  explicit constexpr ActiveProtectParam(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ActiveProtectParam(const ActiveProtectParam& from);
  ActiveProtectParam(ActiveProtectParam&& from) noexcept
    : ActiveProtectParam() {
    *this = ::std::move(from);
  }

  inline ActiveProtectParam& operator=(const ActiveProtectParam& from) {
    CopyFrom(from);
    return *this;
  }
  inline ActiveProtectParam& operator=(ActiveProtectParam&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ActiveProtectParam& default_instance() {
    return *internal_default_instance();
  }
  static inline const ActiveProtectParam* internal_default_instance() {
    return reinterpret_cast<const ActiveProtectParam*>(
               &_ActiveProtectParam_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(ActiveProtectParam& a, ActiveProtectParam& b) {
    a.Swap(&b);
  }
  inline void Swap(ActiveProtectParam* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ActiveProtectParam* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ActiveProtectParam* New() const final {
    return new ActiveProtectParam();
  }

  ActiveProtectParam* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ActiveProtectParam>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ActiveProtectParam& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ActiveProtectParam& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message*to, const ::PROTOBUF_NAMESPACE_ID::Message&from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ActiveProtectParam* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "DMCinfo.ActiveProtectParam";
  }
  protected:
  explicit ActiveProtectParam(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCurrBalanceFieldNumber = 1,
    kHotRunawayFieldNumber = 2,
    kCellOverVoltFieldNumber = 3,
    kPackOverVoltFieldNumber = 4,
    kOverCurrentFieldNumber = 5,
    kCellOverTempFieldNumber = 6,
    kLowTempFieldNumber = 7,
    kBmsRlyStickFieldNumber = 8,
    kBmsRlyOCFieldNumber = 9,
    kOverChargeFieldNumber = 10,
    kBmsDataRepeatFieldNumber = 11,
  };
  // .DMCinfo.CurrentBalance currBalance = 1;
  bool has_currbalance() const;
  private:
  bool _internal_has_currbalance() const;
  public:
  void clear_currbalance();
  const ::DMCinfo::CurrentBalance& currbalance() const;
  PROTOBUF_MUST_USE_RESULT ::DMCinfo::CurrentBalance* release_currbalance();
  ::DMCinfo::CurrentBalance* mutable_currbalance();
  void set_allocated_currbalance(::DMCinfo::CurrentBalance* currbalance);
  private:
  const ::DMCinfo::CurrentBalance& _internal_currbalance() const;
  ::DMCinfo::CurrentBalance* _internal_mutable_currbalance();
  public:
  void unsafe_arena_set_allocated_currbalance(
      ::DMCinfo::CurrentBalance* currbalance);
  ::DMCinfo::CurrentBalance* unsafe_arena_release_currbalance();

  // .DMCinfo.HotRunaway hotRunaway = 2;
  bool has_hotrunaway() const;
  private:
  bool _internal_has_hotrunaway() const;
  public:
  void clear_hotrunaway();
  const ::DMCinfo::HotRunaway& hotrunaway() const;
  PROTOBUF_MUST_USE_RESULT ::DMCinfo::HotRunaway* release_hotrunaway();
  ::DMCinfo::HotRunaway* mutable_hotrunaway();
  void set_allocated_hotrunaway(::DMCinfo::HotRunaway* hotrunaway);
  private:
  const ::DMCinfo::HotRunaway& _internal_hotrunaway() const;
  ::DMCinfo::HotRunaway* _internal_mutable_hotrunaway();
  public:
  void unsafe_arena_set_allocated_hotrunaway(
      ::DMCinfo::HotRunaway* hotrunaway);
  ::DMCinfo::HotRunaway* unsafe_arena_release_hotrunaway();

  // .DMCinfo.CellOverVolt cellOverVolt = 3;
  bool has_cellovervolt() const;
  private:
  bool _internal_has_cellovervolt() const;
  public:
  void clear_cellovervolt();
  const ::DMCinfo::CellOverVolt& cellovervolt() const;
  PROTOBUF_MUST_USE_RESULT ::DMCinfo::CellOverVolt* release_cellovervolt();
  ::DMCinfo::CellOverVolt* mutable_cellovervolt();
  void set_allocated_cellovervolt(::DMCinfo::CellOverVolt* cellovervolt);
  private:
  const ::DMCinfo::CellOverVolt& _internal_cellovervolt() const;
  ::DMCinfo::CellOverVolt* _internal_mutable_cellovervolt();
  public:
  void unsafe_arena_set_allocated_cellovervolt(
      ::DMCinfo::CellOverVolt* cellovervolt);
  ::DMCinfo::CellOverVolt* unsafe_arena_release_cellovervolt();

  // .DMCinfo.PackOverVolt packOverVolt = 4;
  bool has_packovervolt() const;
  private:
  bool _internal_has_packovervolt() const;
  public:
  void clear_packovervolt();
  const ::DMCinfo::PackOverVolt& packovervolt() const;
  PROTOBUF_MUST_USE_RESULT ::DMCinfo::PackOverVolt* release_packovervolt();
  ::DMCinfo::PackOverVolt* mutable_packovervolt();
  void set_allocated_packovervolt(::DMCinfo::PackOverVolt* packovervolt);
  private:
  const ::DMCinfo::PackOverVolt& _internal_packovervolt() const;
  ::DMCinfo::PackOverVolt* _internal_mutable_packovervolt();
  public:
  void unsafe_arena_set_allocated_packovervolt(
      ::DMCinfo::PackOverVolt* packovervolt);
  ::DMCinfo::PackOverVolt* unsafe_arena_release_packovervolt();

  // .DMCinfo.OverCurrent overCurrent = 5;
  bool has_overcurrent() const;
  private:
  bool _internal_has_overcurrent() const;
  public:
  void clear_overcurrent();
  const ::DMCinfo::OverCurrent& overcurrent() const;
  PROTOBUF_MUST_USE_RESULT ::DMCinfo::OverCurrent* release_overcurrent();
  ::DMCinfo::OverCurrent* mutable_overcurrent();
  void set_allocated_overcurrent(::DMCinfo::OverCurrent* overcurrent);
  private:
  const ::DMCinfo::OverCurrent& _internal_overcurrent() const;
  ::DMCinfo::OverCurrent* _internal_mutable_overcurrent();
  public:
  void unsafe_arena_set_allocated_overcurrent(
      ::DMCinfo::OverCurrent* overcurrent);
  ::DMCinfo::OverCurrent* unsafe_arena_release_overcurrent();

  // .DMCinfo.CellOverTemp cellOverTemp = 6;
  bool has_cellovertemp() const;
  private:
  bool _internal_has_cellovertemp() const;
  public:
  void clear_cellovertemp();
  const ::DMCinfo::CellOverTemp& cellovertemp() const;
  PROTOBUF_MUST_USE_RESULT ::DMCinfo::CellOverTemp* release_cellovertemp();
  ::DMCinfo::CellOverTemp* mutable_cellovertemp();
  void set_allocated_cellovertemp(::DMCinfo::CellOverTemp* cellovertemp);
  private:
  const ::DMCinfo::CellOverTemp& _internal_cellovertemp() const;
  ::DMCinfo::CellOverTemp* _internal_mutable_cellovertemp();
  public:
  void unsafe_arena_set_allocated_cellovertemp(
      ::DMCinfo::CellOverTemp* cellovertemp);
  ::DMCinfo::CellOverTemp* unsafe_arena_release_cellovertemp();

  // .DMCinfo.LowTemp lowTemp = 7;
  bool has_lowtemp() const;
  private:
  bool _internal_has_lowtemp() const;
  public:
  void clear_lowtemp();
  const ::DMCinfo::LowTemp& lowtemp() const;
  PROTOBUF_MUST_USE_RESULT ::DMCinfo::LowTemp* release_lowtemp();
  ::DMCinfo::LowTemp* mutable_lowtemp();
  void set_allocated_lowtemp(::DMCinfo::LowTemp* lowtemp);
  private:
  const ::DMCinfo::LowTemp& _internal_lowtemp() const;
  ::DMCinfo::LowTemp* _internal_mutable_lowtemp();
  public:
  void unsafe_arena_set_allocated_lowtemp(
      ::DMCinfo::LowTemp* lowtemp);
  ::DMCinfo::LowTemp* unsafe_arena_release_lowtemp();

  // .DMCinfo.BMSRlyStick bmsRlyStick = 8;
  bool has_bmsrlystick() const;
  private:
  bool _internal_has_bmsrlystick() const;
  public:
  void clear_bmsrlystick();
  const ::DMCinfo::BMSRlyStick& bmsrlystick() const;
  PROTOBUF_MUST_USE_RESULT ::DMCinfo::BMSRlyStick* release_bmsrlystick();
  ::DMCinfo::BMSRlyStick* mutable_bmsrlystick();
  void set_allocated_bmsrlystick(::DMCinfo::BMSRlyStick* bmsrlystick);
  private:
  const ::DMCinfo::BMSRlyStick& _internal_bmsrlystick() const;
  ::DMCinfo::BMSRlyStick* _internal_mutable_bmsrlystick();
  public:
  void unsafe_arena_set_allocated_bmsrlystick(
      ::DMCinfo::BMSRlyStick* bmsrlystick);
  ::DMCinfo::BMSRlyStick* unsafe_arena_release_bmsrlystick();

  // .DMCinfo.BMSRlyOC bmsRlyOC = 9;
  bool has_bmsrlyoc() const;
  private:
  bool _internal_has_bmsrlyoc() const;
  public:
  void clear_bmsrlyoc();
  const ::DMCinfo::BMSRlyOC& bmsrlyoc() const;
  PROTOBUF_MUST_USE_RESULT ::DMCinfo::BMSRlyOC* release_bmsrlyoc();
  ::DMCinfo::BMSRlyOC* mutable_bmsrlyoc();
  void set_allocated_bmsrlyoc(::DMCinfo::BMSRlyOC* bmsrlyoc);
  private:
  const ::DMCinfo::BMSRlyOC& _internal_bmsrlyoc() const;
  ::DMCinfo::BMSRlyOC* _internal_mutable_bmsrlyoc();
  public:
  void unsafe_arena_set_allocated_bmsrlyoc(
      ::DMCinfo::BMSRlyOC* bmsrlyoc);
  ::DMCinfo::BMSRlyOC* unsafe_arena_release_bmsrlyoc();

  // .DMCinfo.OverCharge overCharge = 10;
  bool has_overcharge() const;
  private:
  bool _internal_has_overcharge() const;
  public:
  void clear_overcharge();
  const ::DMCinfo::OverCharge& overcharge() const;
  PROTOBUF_MUST_USE_RESULT ::DMCinfo::OverCharge* release_overcharge();
  ::DMCinfo::OverCharge* mutable_overcharge();
  void set_allocated_overcharge(::DMCinfo::OverCharge* overcharge);
  private:
  const ::DMCinfo::OverCharge& _internal_overcharge() const;
  ::DMCinfo::OverCharge* _internal_mutable_overcharge();
  public:
  void unsafe_arena_set_allocated_overcharge(
      ::DMCinfo::OverCharge* overcharge);
  ::DMCinfo::OverCharge* unsafe_arena_release_overcharge();

  // .DMCinfo.BMSDataRepeat bmsDataRepeat = 11;
  bool has_bmsdatarepeat() const;
  private:
  bool _internal_has_bmsdatarepeat() const;
  public:
  void clear_bmsdatarepeat();
  const ::DMCinfo::BMSDataRepeat& bmsdatarepeat() const;
  PROTOBUF_MUST_USE_RESULT ::DMCinfo::BMSDataRepeat* release_bmsdatarepeat();
  ::DMCinfo::BMSDataRepeat* mutable_bmsdatarepeat();
  void set_allocated_bmsdatarepeat(::DMCinfo::BMSDataRepeat* bmsdatarepeat);
  private:
  const ::DMCinfo::BMSDataRepeat& _internal_bmsdatarepeat() const;
  ::DMCinfo::BMSDataRepeat* _internal_mutable_bmsdatarepeat();
  public:
  void unsafe_arena_set_allocated_bmsdatarepeat(
      ::DMCinfo::BMSDataRepeat* bmsdatarepeat);
  ::DMCinfo::BMSDataRepeat* unsafe_arena_release_bmsdatarepeat();

  // @@protoc_insertion_point(class_scope:DMCinfo.ActiveProtectParam)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::DMCinfo::CurrentBalance* currbalance_;
  ::DMCinfo::HotRunaway* hotrunaway_;
  ::DMCinfo::CellOverVolt* cellovervolt_;
  ::DMCinfo::PackOverVolt* packovervolt_;
  ::DMCinfo::OverCurrent* overcurrent_;
  ::DMCinfo::CellOverTemp* cellovertemp_;
  ::DMCinfo::LowTemp* lowtemp_;
  ::DMCinfo::BMSRlyStick* bmsrlystick_;
  ::DMCinfo::BMSRlyOC* bmsrlyoc_;
  ::DMCinfo::OverCharge* overcharge_;
  ::DMCinfo::BMSDataRepeat* bmsdatarepeat_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GCU_5fDMC_5fINFO_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// PUBInfo

// uint32 terminalID = 1;
inline void PUBInfo::clear_terminalid() {
  terminalid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PUBInfo::_internal_terminalid() const {
  return terminalid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PUBInfo::terminalid() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PUBInfo.terminalID)
  return _internal_terminalid();
}
inline void PUBInfo::_internal_set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  terminalid_ = value;
}
inline void PUBInfo::set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_terminalid(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PUBInfo.terminalID)
}

// uint32 GunAmount = 2;
inline void PUBInfo::clear_gunamount() {
  gunamount_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PUBInfo::_internal_gunamount() const {
  return gunamount_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PUBInfo::gunamount() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PUBInfo.GunAmount)
  return _internal_gunamount();
}
inline void PUBInfo::_internal_set_gunamount(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  gunamount_ = value;
}
inline void PUBInfo::set_gunamount(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_gunamount(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PUBInfo.GunAmount)
}

// .DMCinfo.GunTypeEnum GunType = 3;
inline void PUBInfo::clear_guntype() {
  guntype_ = 0;
}
inline ::DMCinfo::GunTypeEnum PUBInfo::_internal_guntype() const {
  return static_cast< ::DMCinfo::GunTypeEnum >(guntype_);
}
inline ::DMCinfo::GunTypeEnum PUBInfo::guntype() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PUBInfo.GunType)
  return _internal_guntype();
}
inline void PUBInfo::_internal_set_guntype(::DMCinfo::GunTypeEnum value) {
  
  guntype_ = value;
}
inline void PUBInfo::set_guntype(::DMCinfo::GunTypeEnum value) {
  _internal_set_guntype(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PUBInfo.GunType)
}

// float sysVolMax = 4;
inline void PUBInfo::clear_sysvolmax() {
  sysvolmax_ = 0;
}
inline float PUBInfo::_internal_sysvolmax() const {
  return sysvolmax_;
}
inline float PUBInfo::sysvolmax() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PUBInfo.sysVolMax)
  return _internal_sysvolmax();
}
inline void PUBInfo::_internal_set_sysvolmax(float value) {
  
  sysvolmax_ = value;
}
inline void PUBInfo::set_sysvolmax(float value) {
  _internal_set_sysvolmax(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PUBInfo.sysVolMax)
}

// float sysCurMax = 5;
inline void PUBInfo::clear_syscurmax() {
  syscurmax_ = 0;
}
inline float PUBInfo::_internal_syscurmax() const {
  return syscurmax_;
}
inline float PUBInfo::syscurmax() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PUBInfo.sysCurMax)
  return _internal_syscurmax();
}
inline void PUBInfo::_internal_set_syscurmax(float value) {
  
  syscurmax_ = value;
}
inline void PUBInfo::set_syscurmax(float value) {
  _internal_set_syscurmax(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PUBInfo.sysCurMax)
}

// float sysCurMin = 6;
inline void PUBInfo::clear_syscurmin() {
  syscurmin_ = 0;
}
inline float PUBInfo::_internal_syscurmin() const {
  return syscurmin_;
}
inline float PUBInfo::syscurmin() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PUBInfo.sysCurMin)
  return _internal_syscurmin();
}
inline void PUBInfo::_internal_set_syscurmin(float value) {
  
  syscurmin_ = value;
}
inline void PUBInfo::set_syscurmin(float value) {
  _internal_set_syscurmin(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PUBInfo.sysCurMin)
}

// float sysMinVolCV = 7;
inline void PUBInfo::clear_sysminvolcv() {
  sysminvolcv_ = 0;
}
inline float PUBInfo::_internal_sysminvolcv() const {
  return sysminvolcv_;
}
inline float PUBInfo::sysminvolcv() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PUBInfo.sysMinVolCV)
  return _internal_sysminvolcv();
}
inline void PUBInfo::_internal_set_sysminvolcv(float value) {
  
  sysminvolcv_ = value;
}
inline void PUBInfo::set_sysminvolcv(float value) {
  _internal_set_sysminvolcv(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PUBInfo.sysMinVolCV)
}

// float sysMinVolCC = 8;
inline void PUBInfo::clear_sysminvolcc() {
  sysminvolcc_ = 0;
}
inline float PUBInfo::_internal_sysminvolcc() const {
  return sysminvolcc_;
}
inline float PUBInfo::sysminvolcc() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PUBInfo.sysMinVolCC)
  return _internal_sysminvolcc();
}
inline void PUBInfo::_internal_set_sysminvolcc(float value) {
  
  sysminvolcc_ = value;
}
inline void PUBInfo::set_sysminvolcc(float value) {
  _internal_set_sysminvolcc(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PUBInfo.sysMinVolCC)
}

// uint32 ErrorAsWarning = 9;
inline void PUBInfo::clear_erroraswarning() {
  erroraswarning_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PUBInfo::_internal_erroraswarning() const {
  return erroraswarning_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PUBInfo::erroraswarning() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PUBInfo.ErrorAsWarning)
  return _internal_erroraswarning();
}
inline void PUBInfo::_internal_set_erroraswarning(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  erroraswarning_ = value;
}
inline void PUBInfo::set_erroraswarning(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_erroraswarning(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PUBInfo.ErrorAsWarning)
}

// uint32 ADFixMode = 10;
inline void PUBInfo::clear_adfixmode() {
  adfixmode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PUBInfo::_internal_adfixmode() const {
  return adfixmode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PUBInfo::adfixmode() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PUBInfo.ADFixMode)
  return _internal_adfixmode();
}
inline void PUBInfo::_internal_set_adfixmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  adfixmode_ = value;
}
inline void PUBInfo::set_adfixmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_adfixmode(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PUBInfo.ADFixMode)
}

// uint32 gunMatchMode = 11;
inline void PUBInfo::clear_gunmatchmode() {
  gunmatchmode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PUBInfo::_internal_gunmatchmode() const {
  return gunmatchmode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PUBInfo::gunmatchmode() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PUBInfo.gunMatchMode)
  return _internal_gunmatchmode();
}
inline void PUBInfo::_internal_set_gunmatchmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  gunmatchmode_ = value;
}
inline void PUBInfo::set_gunmatchmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_gunmatchmode(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PUBInfo.gunMatchMode)
}

// uint32 lqdCoolOilAddCmd = 12;
inline void PUBInfo::clear_lqdcooloiladdcmd() {
  lqdcooloiladdcmd_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PUBInfo::_internal_lqdcooloiladdcmd() const {
  return lqdcooloiladdcmd_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PUBInfo::lqdcooloiladdcmd() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PUBInfo.lqdCoolOilAddCmd)
  return _internal_lqdcooloiladdcmd();
}
inline void PUBInfo::_internal_set_lqdcooloiladdcmd(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  lqdcooloiladdcmd_ = value;
}
inline void PUBInfo::set_lqdcooloiladdcmd(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_lqdcooloiladdcmd(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PUBInfo.lqdCoolOilAddCmd)
}

// .DMCinfo.RunModeEnum runMode = 13;
inline void PUBInfo::clear_runmode() {
  runmode_ = 0;
}
inline ::DMCinfo::RunModeEnum PUBInfo::_internal_runmode() const {
  return static_cast< ::DMCinfo::RunModeEnum >(runmode_);
}
inline ::DMCinfo::RunModeEnum PUBInfo::runmode() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PUBInfo.runMode)
  return _internal_runmode();
}
inline void PUBInfo::_internal_set_runmode(::DMCinfo::RunModeEnum value) {
  
  runmode_ = value;
}
inline void PUBInfo::set_runmode(::DMCinfo::RunModeEnum value) {
  _internal_set_runmode(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PUBInfo.runMode)
}

// uint32 GunCode = 14;
inline void PUBInfo::clear_guncode() {
  guncode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PUBInfo::_internal_guncode() const {
  return guncode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PUBInfo::guncode() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PUBInfo.GunCode)
  return _internal_guncode();
}
inline void PUBInfo::_internal_set_guncode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  guncode_ = value;
}
inline void PUBInfo::set_guncode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_guncode(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PUBInfo.GunCode)
}

// repeated .DMCinfo.GunMatchInfo gunMachInfo = 15;
inline int PUBInfo::_internal_gunmachinfo_size() const {
  return gunmachinfo_.size();
}
inline int PUBInfo::gunmachinfo_size() const {
  return _internal_gunmachinfo_size();
}
inline void PUBInfo::clear_gunmachinfo() {
  gunmachinfo_.Clear();
}
inline ::DMCinfo::GunMatchInfo* PUBInfo::mutable_gunmachinfo(int index) {
  // @@protoc_insertion_point(field_mutable:DMCinfo.PUBInfo.gunMachInfo)
  return gunmachinfo_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::GunMatchInfo >*
PUBInfo::mutable_gunmachinfo() {
  // @@protoc_insertion_point(field_mutable_list:DMCinfo.PUBInfo.gunMachInfo)
  return &gunmachinfo_;
}
inline const ::DMCinfo::GunMatchInfo& PUBInfo::_internal_gunmachinfo(int index) const {
  return gunmachinfo_.Get(index);
}
inline const ::DMCinfo::GunMatchInfo& PUBInfo::gunmachinfo(int index) const {
  // @@protoc_insertion_point(field_get:DMCinfo.PUBInfo.gunMachInfo)
  return _internal_gunmachinfo(index);
}
inline ::DMCinfo::GunMatchInfo* PUBInfo::_internal_add_gunmachinfo() {
  return gunmachinfo_.Add();
}
inline ::DMCinfo::GunMatchInfo* PUBInfo::add_gunmachinfo() {
  ::DMCinfo::GunMatchInfo* _add = _internal_add_gunmachinfo();
  // @@protoc_insertion_point(field_add:DMCinfo.PUBInfo.gunMachInfo)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::GunMatchInfo >&
PUBInfo::gunmachinfo() const {
  // @@protoc_insertion_point(field_list:DMCinfo.PUBInfo.gunMachInfo)
  return gunmachinfo_;
}

// uint32 multGunParallMode = 16;
inline void PUBInfo::clear_multgunparallmode() {
  multgunparallmode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PUBInfo::_internal_multgunparallmode() const {
  return multgunparallmode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PUBInfo::multgunparallmode() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PUBInfo.multGunParallMode)
  return _internal_multgunparallmode();
}
inline void PUBInfo::_internal_set_multgunparallmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  multgunparallmode_ = value;
}
inline void PUBInfo::set_multgunparallmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_multgunparallmode(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PUBInfo.multGunParallMode)
}

// -------------------------------------------------------------------

// VCIInfo

// uint32 terminalID = 1;
inline void VCIInfo::clear_terminalid() {
  terminalid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIInfo::_internal_terminalid() const {
  return terminalid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIInfo::terminalid() const {
  // @@protoc_insertion_point(field_get:DMCinfo.VCIInfo.terminalID)
  return _internal_terminalid();
}
inline void VCIInfo::_internal_set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  terminalid_ = value;
}
inline void VCIInfo::set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_terminalid(value);
  // @@protoc_insertion_point(field_set:DMCinfo.VCIInfo.terminalID)
}

// uint32 CooperateMode = 2;
inline void VCIInfo::clear_cooperatemode() {
  cooperatemode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIInfo::_internal_cooperatemode() const {
  return cooperatemode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIInfo::cooperatemode() const {
  // @@protoc_insertion_point(field_get:DMCinfo.VCIInfo.CooperateMode)
  return _internal_cooperatemode();
}
inline void VCIInfo::_internal_set_cooperatemode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  cooperatemode_ = value;
}
inline void VCIInfo::set_cooperatemode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_cooperatemode(value);
  // @@protoc_insertion_point(field_set:DMCinfo.VCIInfo.CooperateMode)
}

// uint32 DCFanEnable = 3;
inline void VCIInfo::clear_dcfanenable() {
  dcfanenable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIInfo::_internal_dcfanenable() const {
  return dcfanenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIInfo::dcfanenable() const {
  // @@protoc_insertion_point(field_get:DMCinfo.VCIInfo.DCFanEnable)
  return _internal_dcfanenable();
}
inline void VCIInfo::_internal_set_dcfanenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  dcfanenable_ = value;
}
inline void VCIInfo::set_dcfanenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_dcfanenable(value);
  // @@protoc_insertion_point(field_set:DMCinfo.VCIInfo.DCFanEnable)
}

// uint32 SmokeEnable = 4;
inline void VCIInfo::clear_smokeenable() {
  smokeenable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIInfo::_internal_smokeenable() const {
  return smokeenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIInfo::smokeenable() const {
  // @@protoc_insertion_point(field_get:DMCinfo.VCIInfo.SmokeEnable)
  return _internal_smokeenable();
}
inline void VCIInfo::_internal_set_smokeenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  smokeenable_ = value;
}
inline void VCIInfo::set_smokeenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_smokeenable(value);
  // @@protoc_insertion_point(field_set:DMCinfo.VCIInfo.SmokeEnable)
}

// uint32 ToppleAndFallEnable = 5;
inline void VCIInfo::clear_toppleandfallenable() {
  toppleandfallenable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIInfo::_internal_toppleandfallenable() const {
  return toppleandfallenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIInfo::toppleandfallenable() const {
  // @@protoc_insertion_point(field_get:DMCinfo.VCIInfo.ToppleAndFallEnable)
  return _internal_toppleandfallenable();
}
inline void VCIInfo::_internal_set_toppleandfallenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  toppleandfallenable_ = value;
}
inline void VCIInfo::set_toppleandfallenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_toppleandfallenable(value);
  // @@protoc_insertion_point(field_set:DMCinfo.VCIInfo.ToppleAndFallEnable)
}

// uint32 WaterEnable = 6;
inline void VCIInfo::clear_waterenable() {
  waterenable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIInfo::_internal_waterenable() const {
  return waterenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIInfo::waterenable() const {
  // @@protoc_insertion_point(field_get:DMCinfo.VCIInfo.WaterEnable)
  return _internal_waterenable();
}
inline void VCIInfo::_internal_set_waterenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  waterenable_ = value;
}
inline void VCIInfo::set_waterenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_waterenable(value);
  // @@protoc_insertion_point(field_set:DMCinfo.VCIInfo.WaterEnable)
}

// uint32 LightningEnable = 7;
inline void VCIInfo::clear_lightningenable() {
  lightningenable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIInfo::_internal_lightningenable() const {
  return lightningenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIInfo::lightningenable() const {
  // @@protoc_insertion_point(field_get:DMCinfo.VCIInfo.LightningEnable)
  return _internal_lightningenable();
}
inline void VCIInfo::_internal_set_lightningenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  lightningenable_ = value;
}
inline void VCIInfo::set_lightningenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_lightningenable(value);
  // @@protoc_insertion_point(field_set:DMCinfo.VCIInfo.LightningEnable)
}

// uint32 DustproofEnable = 8;
inline void VCIInfo::clear_dustproofenable() {
  dustproofenable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIInfo::_internal_dustproofenable() const {
  return dustproofenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIInfo::dustproofenable() const {
  // @@protoc_insertion_point(field_get:DMCinfo.VCIInfo.DustproofEnable)
  return _internal_dustproofenable();
}
inline void VCIInfo::_internal_set_dustproofenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  dustproofenable_ = value;
}
inline void VCIInfo::set_dustproofenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_dustproofenable(value);
  // @@protoc_insertion_point(field_set:DMCinfo.VCIInfo.DustproofEnable)
}

// uint32 EmergencyStopType = 9;
inline void VCIInfo::clear_emergencystoptype() {
  emergencystoptype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIInfo::_internal_emergencystoptype() const {
  return emergencystoptype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 VCIInfo::emergencystoptype() const {
  // @@protoc_insertion_point(field_get:DMCinfo.VCIInfo.EmergencyStopType)
  return _internal_emergencystoptype();
}
inline void VCIInfo::_internal_set_emergencystoptype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  emergencystoptype_ = value;
}
inline void VCIInfo::set_emergencystoptype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_emergencystoptype(value);
  // @@protoc_insertion_point(field_set:DMCinfo.VCIInfo.EmergencyStopType)
}

// repeated .DMCinfo.GUNInfo gunInfo = 10;
inline int VCIInfo::_internal_guninfo_size() const {
  return guninfo_.size();
}
inline int VCIInfo::guninfo_size() const {
  return _internal_guninfo_size();
}
inline void VCIInfo::clear_guninfo() {
  guninfo_.Clear();
}
inline ::DMCinfo::GUNInfo* VCIInfo::mutable_guninfo(int index) {
  // @@protoc_insertion_point(field_mutable:DMCinfo.VCIInfo.gunInfo)
  return guninfo_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::GUNInfo >*
VCIInfo::mutable_guninfo() {
  // @@protoc_insertion_point(field_mutable_list:DMCinfo.VCIInfo.gunInfo)
  return &guninfo_;
}
inline const ::DMCinfo::GUNInfo& VCIInfo::_internal_guninfo(int index) const {
  return guninfo_.Get(index);
}
inline const ::DMCinfo::GUNInfo& VCIInfo::guninfo(int index) const {
  // @@protoc_insertion_point(field_get:DMCinfo.VCIInfo.gunInfo)
  return _internal_guninfo(index);
}
inline ::DMCinfo::GUNInfo* VCIInfo::_internal_add_guninfo() {
  return guninfo_.Add();
}
inline ::DMCinfo::GUNInfo* VCIInfo::add_guninfo() {
  ::DMCinfo::GUNInfo* _add = _internal_add_guninfo();
  // @@protoc_insertion_point(field_add:DMCinfo.VCIInfo.gunInfo)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::GUNInfo >&
VCIInfo::guninfo() const {
  // @@protoc_insertion_point(field_list:DMCinfo.VCIInfo.gunInfo)
  return guninfo_;
}

// -------------------------------------------------------------------

// GUNInfo

// uint32 gunID = 1;
inline void GUNInfo::clear_gunid() {
  gunid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GUNInfo::_internal_gunid() const {
  return gunid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GUNInfo::gunid() const {
  // @@protoc_insertion_point(field_get:DMCinfo.GUNInfo.gunID)
  return _internal_gunid();
}
inline void GUNInfo::_internal_set_gunid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  gunid_ = value;
}
inline void GUNInfo::set_gunid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_gunid(value);
  // @@protoc_insertion_point(field_set:DMCinfo.GUNInfo.gunID)
}

// .DMCinfo.GunProEnum GbtProtoType = 2;
inline void GUNInfo::clear_gbtprototype() {
  gbtprototype_ = 0;
}
inline ::DMCinfo::GunProEnum GUNInfo::_internal_gbtprototype() const {
  return static_cast< ::DMCinfo::GunProEnum >(gbtprototype_);
}
inline ::DMCinfo::GunProEnum GUNInfo::gbtprototype() const {
  // @@protoc_insertion_point(field_get:DMCinfo.GUNInfo.GbtProtoType)
  return _internal_gbtprototype();
}
inline void GUNInfo::_internal_set_gbtprototype(::DMCinfo::GunProEnum value) {
  
  gbtprototype_ = value;
}
inline void GUNInfo::set_gbtprototype(::DMCinfo::GunProEnum value) {
  _internal_set_gbtprototype(value);
  // @@protoc_insertion_point(field_set:DMCinfo.GUNInfo.GbtProtoType)
}

// uint32 EmeterType = 3;
inline void GUNInfo::clear_emetertype() {
  emetertype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GUNInfo::_internal_emetertype() const {
  return emetertype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GUNInfo::emetertype() const {
  // @@protoc_insertion_point(field_get:DMCinfo.GUNInfo.EmeterType)
  return _internal_emetertype();
}
inline void GUNInfo::_internal_set_emetertype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  emetertype_ = value;
}
inline void GUNInfo::set_emetertype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_emetertype(value);
  // @@protoc_insertion_point(field_set:DMCinfo.GUNInfo.EmeterType)
}

// uint32 ElockMode = 4;
inline void GUNInfo::clear_elockmode() {
  elockmode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GUNInfo::_internal_elockmode() const {
  return elockmode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GUNInfo::elockmode() const {
  // @@protoc_insertion_point(field_get:DMCinfo.GUNInfo.ElockMode)
  return _internal_elockmode();
}
inline void GUNInfo::_internal_set_elockmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  elockmode_ = value;
}
inline void GUNInfo::set_elockmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_elockmode(value);
  // @@protoc_insertion_point(field_set:DMCinfo.GUNInfo.ElockMode)
}

// uint32 TemperatureMode = 5;
inline void GUNInfo::clear_temperaturemode() {
  temperaturemode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GUNInfo::_internal_temperaturemode() const {
  return temperaturemode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GUNInfo::temperaturemode() const {
  // @@protoc_insertion_point(field_get:DMCinfo.GUNInfo.TemperatureMode)
  return _internal_temperaturemode();
}
inline void GUNInfo::_internal_set_temperaturemode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  temperaturemode_ = value;
}
inline void GUNInfo::set_temperaturemode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_temperaturemode(value);
  // @@protoc_insertion_point(field_set:DMCinfo.GUNInfo.TemperatureMode)
}

// uint32 GunCurrent = 6;
inline void GUNInfo::clear_guncurrent() {
  guncurrent_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GUNInfo::_internal_guncurrent() const {
  return guncurrent_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GUNInfo::guncurrent() const {
  // @@protoc_insertion_point(field_get:DMCinfo.GUNInfo.GunCurrent)
  return _internal_guncurrent();
}
inline void GUNInfo::_internal_set_guncurrent(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  guncurrent_ = value;
}
inline void GUNInfo::set_guncurrent(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_guncurrent(value);
  // @@protoc_insertion_point(field_set:DMCinfo.GUNInfo.GunCurrent)
}

// uint32 MainContactorCurrent = 7;
inline void GUNInfo::clear_maincontactorcurrent() {
  maincontactorcurrent_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GUNInfo::_internal_maincontactorcurrent() const {
  return maincontactorcurrent_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GUNInfo::maincontactorcurrent() const {
  // @@protoc_insertion_point(field_get:DMCinfo.GUNInfo.MainContactorCurrent)
  return _internal_maincontactorcurrent();
}
inline void GUNInfo::_internal_set_maincontactorcurrent(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  maincontactorcurrent_ = value;
}
inline void GUNInfo::set_maincontactorcurrent(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_maincontactorcurrent(value);
  // @@protoc_insertion_point(field_set:DMCinfo.GUNInfo.MainContactorCurrent)
}

// uint32 gunTempEnable = 8;
inline void GUNInfo::clear_guntempenable() {
  guntempenable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GUNInfo::_internal_guntempenable() const {
  return guntempenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GUNInfo::guntempenable() const {
  // @@protoc_insertion_point(field_get:DMCinfo.GUNInfo.gunTempEnable)
  return _internal_guntempenable();
}
inline void GUNInfo::_internal_set_guntempenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  guntempenable_ = value;
}
inline void GUNInfo::set_guntempenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_guntempenable(value);
  // @@protoc_insertion_point(field_set:DMCinfo.GUNInfo.gunTempEnable)
}

// uint32 AuxType = 9;
inline void GUNInfo::clear_auxtype() {
  auxtype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GUNInfo::_internal_auxtype() const {
  return auxtype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GUNInfo::auxtype() const {
  // @@protoc_insertion_point(field_get:DMCinfo.GUNInfo.AuxType)
  return _internal_auxtype();
}
inline void GUNInfo::_internal_set_auxtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  auxtype_ = value;
}
inline void GUNInfo::set_auxtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_auxtype(value);
  // @@protoc_insertion_point(field_set:DMCinfo.GUNInfo.AuxType)
}

// .DMCinfo.ActiveProtectParam ActiveProtPara = 10;
inline bool GUNInfo::_internal_has_activeprotpara() const {
  return this != internal_default_instance() && activeprotpara_ != nullptr;
}
inline bool GUNInfo::has_activeprotpara() const {
  return _internal_has_activeprotpara();
}
inline void GUNInfo::clear_activeprotpara() {
  if (GetArenaForAllocation() == nullptr && activeprotpara_ != nullptr) {
    delete activeprotpara_;
  }
  activeprotpara_ = nullptr;
}
inline const ::DMCinfo::ActiveProtectParam& GUNInfo::_internal_activeprotpara() const {
  const ::DMCinfo::ActiveProtectParam* p = activeprotpara_;
  return p != nullptr ? *p : reinterpret_cast<const ::DMCinfo::ActiveProtectParam&>(
      ::DMCinfo::_ActiveProtectParam_default_instance_);
}
inline const ::DMCinfo::ActiveProtectParam& GUNInfo::activeprotpara() const {
  // @@protoc_insertion_point(field_get:DMCinfo.GUNInfo.ActiveProtPara)
  return _internal_activeprotpara();
}
inline void GUNInfo::unsafe_arena_set_allocated_activeprotpara(
    ::DMCinfo::ActiveProtectParam* activeprotpara) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(activeprotpara_);
  }
  activeprotpara_ = activeprotpara;
  if (activeprotpara) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:DMCinfo.GUNInfo.ActiveProtPara)
}
inline ::DMCinfo::ActiveProtectParam* GUNInfo::release_activeprotpara() {
  
  ::DMCinfo::ActiveProtectParam* temp = activeprotpara_;
  activeprotpara_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::DMCinfo::ActiveProtectParam* GUNInfo::unsafe_arena_release_activeprotpara() {
  // @@protoc_insertion_point(field_release:DMCinfo.GUNInfo.ActiveProtPara)
  
  ::DMCinfo::ActiveProtectParam* temp = activeprotpara_;
  activeprotpara_ = nullptr;
  return temp;
}
inline ::DMCinfo::ActiveProtectParam* GUNInfo::_internal_mutable_activeprotpara() {
  
  if (activeprotpara_ == nullptr) {
    auto* p = CreateMaybeMessage<::DMCinfo::ActiveProtectParam>(GetArenaForAllocation());
    activeprotpara_ = p;
  }
  return activeprotpara_;
}
inline ::DMCinfo::ActiveProtectParam* GUNInfo::mutable_activeprotpara() {
  ::DMCinfo::ActiveProtectParam* _msg = _internal_mutable_activeprotpara();
  // @@protoc_insertion_point(field_mutable:DMCinfo.GUNInfo.ActiveProtPara)
  return _msg;
}
inline void GUNInfo::set_allocated_activeprotpara(::DMCinfo::ActiveProtectParam* activeprotpara) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete activeprotpara_;
  }
  if (activeprotpara) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::DMCinfo::ActiveProtectParam>::GetOwningArena(activeprotpara);
    if (message_arena != submessage_arena) {
      activeprotpara = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, activeprotpara, submessage_arena);
    }
    
  } else {
    
  }
  activeprotpara_ = activeprotpara;
  // @@protoc_insertion_point(field_set_allocated:DMCinfo.GUNInfo.ActiveProtPara)
}

// -------------------------------------------------------------------

// ADModuleInfo

// uint32 ID = 1;
inline void ADModuleInfo::clear_id() {
  id_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ADModuleInfo::_internal_id() const {
  return id_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ADModuleInfo::id() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ADModuleInfo.ID)
  return _internal_id();
}
inline void ADModuleInfo::_internal_set_id(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  id_ = value;
}
inline void ADModuleInfo::set_id(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:DMCinfo.ADModuleInfo.ID)
}

// float currMax = 2;
inline void ADModuleInfo::clear_currmax() {
  currmax_ = 0;
}
inline float ADModuleInfo::_internal_currmax() const {
  return currmax_;
}
inline float ADModuleInfo::currmax() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ADModuleInfo.currMax)
  return _internal_currmax();
}
inline void ADModuleInfo::_internal_set_currmax(float value) {
  
  currmax_ = value;
}
inline void ADModuleInfo::set_currmax(float value) {
  _internal_set_currmax(value);
  // @@protoc_insertion_point(field_set:DMCinfo.ADModuleInfo.currMax)
}

// float limitPower = 3;
inline void ADModuleInfo::clear_limitpower() {
  limitpower_ = 0;
}
inline float ADModuleInfo::_internal_limitpower() const {
  return limitpower_;
}
inline float ADModuleInfo::limitpower() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ADModuleInfo.limitPower)
  return _internal_limitpower();
}
inline void ADModuleInfo::_internal_set_limitpower(float value) {
  
  limitpower_ = value;
}
inline void ADModuleInfo::set_limitpower(float value) {
  _internal_set_limitpower(value);
  // @@protoc_insertion_point(field_set:DMCinfo.ADModuleInfo.limitPower)
}

// float volMax = 4;
inline void ADModuleInfo::clear_volmax() {
  volmax_ = 0;
}
inline float ADModuleInfo::_internal_volmax() const {
  return volmax_;
}
inline float ADModuleInfo::volmax() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ADModuleInfo.volMax)
  return _internal_volmax();
}
inline void ADModuleInfo::_internal_set_volmax(float value) {
  
  volmax_ = value;
}
inline void ADModuleInfo::set_volmax(float value) {
  _internal_set_volmax(value);
  // @@protoc_insertion_point(field_set:DMCinfo.ADModuleInfo.volMax)
}

// float volMin = 5;
inline void ADModuleInfo::clear_volmin() {
  volmin_ = 0;
}
inline float ADModuleInfo::_internal_volmin() const {
  return volmin_;
}
inline float ADModuleInfo::volmin() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ADModuleInfo.volMin)
  return _internal_volmin();
}
inline void ADModuleInfo::_internal_set_volmin(float value) {
  
  volmin_ = value;
}
inline void ADModuleInfo::set_volmin(float value) {
  _internal_set_volmin(value);
  // @@protoc_insertion_point(field_set:DMCinfo.ADModuleInfo.volMin)
}

// float ratedVol = 6;
inline void ADModuleInfo::clear_ratedvol() {
  ratedvol_ = 0;
}
inline float ADModuleInfo::_internal_ratedvol() const {
  return ratedvol_;
}
inline float ADModuleInfo::ratedvol() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ADModuleInfo.ratedVol)
  return _internal_ratedvol();
}
inline void ADModuleInfo::_internal_set_ratedvol(float value) {
  
  ratedvol_ = value;
}
inline void ADModuleInfo::set_ratedvol(float value) {
  _internal_set_ratedvol(value);
  // @@protoc_insertion_point(field_set:DMCinfo.ADModuleInfo.ratedVol)
}

// float ratedPower = 7;
inline void ADModuleInfo::clear_ratedpower() {
  ratedpower_ = 0;
}
inline float ADModuleInfo::_internal_ratedpower() const {
  return ratedpower_;
}
inline float ADModuleInfo::ratedpower() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ADModuleInfo.ratedPower)
  return _internal_ratedpower();
}
inline void ADModuleInfo::_internal_set_ratedpower(float value) {
  
  ratedpower_ = value;
}
inline void ADModuleInfo::set_ratedpower(float value) {
  _internal_set_ratedpower(value);
  // @@protoc_insertion_point(field_set:DMCinfo.ADModuleInfo.ratedPower)
}

// float ratedCurr = 8;
inline void ADModuleInfo::clear_ratedcurr() {
  ratedcurr_ = 0;
}
inline float ADModuleInfo::_internal_ratedcurr() const {
  return ratedcurr_;
}
inline float ADModuleInfo::ratedcurr() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ADModuleInfo.ratedCurr)
  return _internal_ratedcurr();
}
inline void ADModuleInfo::_internal_set_ratedcurr(float value) {
  
  ratedcurr_ = value;
}
inline void ADModuleInfo::set_ratedcurr(float value) {
  _internal_set_ratedcurr(value);
  // @@protoc_insertion_point(field_set:DMCinfo.ADModuleInfo.ratedCurr)
}

// float ratedInputVol = 9;
inline void ADModuleInfo::clear_ratedinputvol() {
  ratedinputvol_ = 0;
}
inline float ADModuleInfo::_internal_ratedinputvol() const {
  return ratedinputvol_;
}
inline float ADModuleInfo::ratedinputvol() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ADModuleInfo.ratedInputVol)
  return _internal_ratedinputvol();
}
inline void ADModuleInfo::_internal_set_ratedinputvol(float value) {
  
  ratedinputvol_ = value;
}
inline void ADModuleInfo::set_ratedinputvol(float value) {
  _internal_set_ratedinputvol(value);
  // @@protoc_insertion_point(field_set:DMCinfo.ADModuleInfo.ratedInputVol)
}

// bytes DCModuleSN = 10;
inline void ADModuleInfo::clear_dcmodulesn() {
  dcmodulesn_.ClearToEmpty();
}
inline const std::string& ADModuleInfo::dcmodulesn() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ADModuleInfo.DCModuleSN)
  return _internal_dcmodulesn();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ADModuleInfo::set_dcmodulesn(ArgT0&& arg0, ArgT... args) {
 
 dcmodulesn_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:DMCinfo.ADModuleInfo.DCModuleSN)
}
inline std::string* ADModuleInfo::mutable_dcmodulesn() {
  std::string* _s = _internal_mutable_dcmodulesn();
  // @@protoc_insertion_point(field_mutable:DMCinfo.ADModuleInfo.DCModuleSN)
  return _s;
}
inline const std::string& ADModuleInfo::_internal_dcmodulesn() const {
  return dcmodulesn_.Get();
}
inline void ADModuleInfo::_internal_set_dcmodulesn(const std::string& value) {
  
  dcmodulesn_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ADModuleInfo::_internal_mutable_dcmodulesn() {
  
  return dcmodulesn_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ADModuleInfo::release_dcmodulesn() {
  // @@protoc_insertion_point(field_release:DMCinfo.ADModuleInfo.DCModuleSN)
  return dcmodulesn_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ADModuleInfo::set_allocated_dcmodulesn(std::string* dcmodulesn) {
  if (dcmodulesn != nullptr) {
    
  } else {
    
  }
  dcmodulesn_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), dcmodulesn,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:DMCinfo.ADModuleInfo.DCModuleSN)
}

// bytes softVersion = 11;
inline void ADModuleInfo::clear_softversion() {
  softversion_.ClearToEmpty();
}
inline const std::string& ADModuleInfo::softversion() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ADModuleInfo.softVersion)
  return _internal_softversion();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ADModuleInfo::set_softversion(ArgT0&& arg0, ArgT... args) {
 
 softversion_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:DMCinfo.ADModuleInfo.softVersion)
}
inline std::string* ADModuleInfo::mutable_softversion() {
  std::string* _s = _internal_mutable_softversion();
  // @@protoc_insertion_point(field_mutable:DMCinfo.ADModuleInfo.softVersion)
  return _s;
}
inline const std::string& ADModuleInfo::_internal_softversion() const {
  return softversion_.Get();
}
inline void ADModuleInfo::_internal_set_softversion(const std::string& value) {
  
  softversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ADModuleInfo::_internal_mutable_softversion() {
  
  return softversion_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ADModuleInfo::release_softversion() {
  // @@protoc_insertion_point(field_release:DMCinfo.ADModuleInfo.softVersion)
  return softversion_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ADModuleInfo::set_allocated_softversion(std::string* softversion) {
  if (softversion != nullptr) {
    
  } else {
    
  }
  softversion_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), softversion,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:DMCinfo.ADModuleInfo.softVersion)
}

// bytes hardVersion = 12;
inline void ADModuleInfo::clear_hardversion() {
  hardversion_.ClearToEmpty();
}
inline const std::string& ADModuleInfo::hardversion() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ADModuleInfo.hardVersion)
  return _internal_hardversion();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ADModuleInfo::set_hardversion(ArgT0&& arg0, ArgT... args) {
 
 hardversion_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:DMCinfo.ADModuleInfo.hardVersion)
}
inline std::string* ADModuleInfo::mutable_hardversion() {
  std::string* _s = _internal_mutable_hardversion();
  // @@protoc_insertion_point(field_mutable:DMCinfo.ADModuleInfo.hardVersion)
  return _s;
}
inline const std::string& ADModuleInfo::_internal_hardversion() const {
  return hardversion_.Get();
}
inline void ADModuleInfo::_internal_set_hardversion(const std::string& value) {
  
  hardversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ADModuleInfo::_internal_mutable_hardversion() {
  
  return hardversion_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ADModuleInfo::release_hardversion() {
  // @@protoc_insertion_point(field_release:DMCinfo.ADModuleInfo.hardVersion)
  return hardversion_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ADModuleInfo::set_allocated_hardversion(std::string* hardversion) {
  if (hardversion != nullptr) {
    
  } else {
    
  }
  hardversion_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), hardversion,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:DMCinfo.ADModuleInfo.hardVersion)
}

// -------------------------------------------------------------------

// PMMInfo

// uint32 MatrixType = 1;
inline void PMMInfo::clear_matrixtype() {
  matrixtype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::_internal_matrixtype() const {
  return matrixtype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::matrixtype() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PMMInfo.MatrixType)
  return _internal_matrixtype();
}
inline void PMMInfo::_internal_set_matrixtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  matrixtype_ = value;
}
inline void PMMInfo::set_matrixtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_matrixtype(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PMMInfo.MatrixType)
}

// uint32 MatrixContactorCurrent = 2;
inline void PMMInfo::clear_matrixcontactorcurrent() {
  matrixcontactorcurrent_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::_internal_matrixcontactorcurrent() const {
  return matrixcontactorcurrent_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::matrixcontactorcurrent() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PMMInfo.MatrixContactorCurrent)
  return _internal_matrixcontactorcurrent();
}
inline void PMMInfo::_internal_set_matrixcontactorcurrent(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  matrixcontactorcurrent_ = value;
}
inline void PMMInfo::set_matrixcontactorcurrent(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_matrixcontactorcurrent(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PMMInfo.MatrixContactorCurrent)
}

// uint32 ContactorAc = 3;
inline void PMMInfo::clear_contactorac() {
  contactorac_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::_internal_contactorac() const {
  return contactorac_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::contactorac() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PMMInfo.ContactorAc)
  return _internal_contactorac();
}
inline void PMMInfo::_internal_set_contactorac(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  contactorac_ = value;
}
inline void PMMInfo::set_contactorac(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_contactorac(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PMMInfo.ContactorAc)
}

// uint32 EmergencyStopType = 4;
inline void PMMInfo::clear_emergencystoptype() {
  emergencystoptype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::_internal_emergencystoptype() const {
  return emergencystoptype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::emergencystoptype() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PMMInfo.EmergencyStopType)
  return _internal_emergencystoptype();
}
inline void PMMInfo::_internal_set_emergencystoptype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  emergencystoptype_ = value;
}
inline void PMMInfo::set_emergencystoptype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_emergencystoptype(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PMMInfo.EmergencyStopType)
}

// uint32 limitPower = 5;
inline void PMMInfo::clear_limitpower() {
  limitpower_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::_internal_limitpower() const {
  return limitpower_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::limitpower() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PMMInfo.limitPower)
  return _internal_limitpower();
}
inline void PMMInfo::_internal_set_limitpower(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  limitpower_ = value;
}
inline void PMMInfo::set_limitpower(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_limitpower(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PMMInfo.limitPower)
}

// .DMCinfo.ADModuleTypeEnum ADModuleType = 6;
inline void PMMInfo::clear_admoduletype() {
  admoduletype_ = 0;
}
inline ::DMCinfo::ADModuleTypeEnum PMMInfo::_internal_admoduletype() const {
  return static_cast< ::DMCinfo::ADModuleTypeEnum >(admoduletype_);
}
inline ::DMCinfo::ADModuleTypeEnum PMMInfo::admoduletype() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PMMInfo.ADModuleType)
  return _internal_admoduletype();
}
inline void PMMInfo::_internal_set_admoduletype(::DMCinfo::ADModuleTypeEnum value) {
  
  admoduletype_ = value;
}
inline void PMMInfo::set_admoduletype(::DMCinfo::ADModuleTypeEnum value) {
  _internal_set_admoduletype(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PMMInfo.ADModuleType)
}

// repeated .DMCinfo.ADModuleInfo ADModule = 7;
inline int PMMInfo::_internal_admodule_size() const {
  return admodule_.size();
}
inline int PMMInfo::admodule_size() const {
  return _internal_admodule_size();
}
inline void PMMInfo::clear_admodule() {
  admodule_.Clear();
}
inline ::DMCinfo::ADModuleInfo* PMMInfo::mutable_admodule(int index) {
  // @@protoc_insertion_point(field_mutable:DMCinfo.PMMInfo.ADModule)
  return admodule_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::ADModuleInfo >*
PMMInfo::mutable_admodule() {
  // @@protoc_insertion_point(field_mutable_list:DMCinfo.PMMInfo.ADModule)
  return &admodule_;
}
inline const ::DMCinfo::ADModuleInfo& PMMInfo::_internal_admodule(int index) const {
  return admodule_.Get(index);
}
inline const ::DMCinfo::ADModuleInfo& PMMInfo::admodule(int index) const {
  // @@protoc_insertion_point(field_get:DMCinfo.PMMInfo.ADModule)
  return _internal_admodule(index);
}
inline ::DMCinfo::ADModuleInfo* PMMInfo::_internal_add_admodule() {
  return admodule_.Add();
}
inline ::DMCinfo::ADModuleInfo* PMMInfo::add_admodule() {
  ::DMCinfo::ADModuleInfo* _add = _internal_add_admodule();
  // @@protoc_insertion_point(field_add:DMCinfo.PMMInfo.ADModule)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::ADModuleInfo >&
PMMInfo::admodule() const {
  // @@protoc_insertion_point(field_list:DMCinfo.PMMInfo.ADModule)
  return admodule_;
}

// uint32 TempEnable = 8;
inline void PMMInfo::clear_tempenable() {
  tempenable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::_internal_tempenable() const {
  return tempenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::tempenable() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PMMInfo.TempEnable)
  return _internal_tempenable();
}
inline void PMMInfo::_internal_set_tempenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  tempenable_ = value;
}
inline void PMMInfo::set_tempenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_tempenable(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PMMInfo.TempEnable)
}

// uint32 ACFanEnable = 9;
inline void PMMInfo::clear_acfanenable() {
  acfanenable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::_internal_acfanenable() const {
  return acfanenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::acfanenable() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PMMInfo.ACFanEnable)
  return _internal_acfanenable();
}
inline void PMMInfo::_internal_set_acfanenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  acfanenable_ = value;
}
inline void PMMInfo::set_acfanenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_acfanenable(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PMMInfo.ACFanEnable)
}

// uint32 SmokeEnable = 10;
inline void PMMInfo::clear_smokeenable() {
  smokeenable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::_internal_smokeenable() const {
  return smokeenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::smokeenable() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PMMInfo.SmokeEnable)
  return _internal_smokeenable();
}
inline void PMMInfo::_internal_set_smokeenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  smokeenable_ = value;
}
inline void PMMInfo::set_smokeenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_smokeenable(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PMMInfo.SmokeEnable)
}

// uint32 ToppleAndFallEnable = 11;
inline void PMMInfo::clear_toppleandfallenable() {
  toppleandfallenable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::_internal_toppleandfallenable() const {
  return toppleandfallenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::toppleandfallenable() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PMMInfo.ToppleAndFallEnable)
  return _internal_toppleandfallenable();
}
inline void PMMInfo::_internal_set_toppleandfallenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  toppleandfallenable_ = value;
}
inline void PMMInfo::set_toppleandfallenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_toppleandfallenable(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PMMInfo.ToppleAndFallEnable)
}

// uint32 WaterEnable = 12;
inline void PMMInfo::clear_waterenable() {
  waterenable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::_internal_waterenable() const {
  return waterenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::waterenable() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PMMInfo.WaterEnable)
  return _internal_waterenable();
}
inline void PMMInfo::_internal_set_waterenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  waterenable_ = value;
}
inline void PMMInfo::set_waterenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_waterenable(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PMMInfo.WaterEnable)
}

// uint32 LightningEnable = 13;
inline void PMMInfo::clear_lightningenable() {
  lightningenable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::_internal_lightningenable() const {
  return lightningenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::lightningenable() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PMMInfo.LightningEnable)
  return _internal_lightningenable();
}
inline void PMMInfo::_internal_set_lightningenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  lightningenable_ = value;
}
inline void PMMInfo::set_lightningenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_lightningenable(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PMMInfo.LightningEnable)
}

// uint32 BreakerEnable = 14;
inline void PMMInfo::clear_breakerenable() {
  breakerenable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::_internal_breakerenable() const {
  return breakerenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::breakerenable() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PMMInfo.BreakerEnable)
  return _internal_breakerenable();
}
inline void PMMInfo::_internal_set_breakerenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  breakerenable_ = value;
}
inline void PMMInfo::set_breakerenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_breakerenable(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PMMInfo.BreakerEnable)
}

// uint32 DustproofEnable = 15;
inline void PMMInfo::clear_dustproofenable() {
  dustproofenable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::_internal_dustproofenable() const {
  return dustproofenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::dustproofenable() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PMMInfo.DustproofEnable)
  return _internal_dustproofenable();
}
inline void PMMInfo::_internal_set_dustproofenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  dustproofenable_ = value;
}
inline void PMMInfo::set_dustproofenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_dustproofenable(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PMMInfo.DustproofEnable)
}

// uint32 ACFanNum = 16;
inline void PMMInfo::clear_acfannum() {
  acfannum_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::_internal_acfannum() const {
  return acfannum_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::acfannum() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PMMInfo.ACFanNum)
  return _internal_acfannum();
}
inline void PMMInfo::_internal_set_acfannum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  acfannum_ = value;
}
inline void PMMInfo::set_acfannum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_acfannum(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PMMInfo.ACFanNum)
}

// .DMCinfo.HvdcSilenceMode ModuleSilenceMode = 17;
inline void PMMInfo::clear_modulesilencemode() {
  modulesilencemode_ = 0;
}
inline ::DMCinfo::HvdcSilenceMode PMMInfo::_internal_modulesilencemode() const {
  return static_cast< ::DMCinfo::HvdcSilenceMode >(modulesilencemode_);
}
inline ::DMCinfo::HvdcSilenceMode PMMInfo::modulesilencemode() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PMMInfo.ModuleSilenceMode)
  return _internal_modulesilencemode();
}
inline void PMMInfo::_internal_set_modulesilencemode(::DMCinfo::HvdcSilenceMode value) {
  
  modulesilencemode_ = value;
}
inline void PMMInfo::set_modulesilencemode(::DMCinfo::HvdcSilenceMode value) {
  _internal_set_modulesilencemode(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PMMInfo.ModuleSilenceMode)
}

// uint32 PduType = 18;
inline void PMMInfo::clear_pdutype() {
  pdutype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::_internal_pdutype() const {
  return pdutype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::pdutype() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PMMInfo.PduType)
  return _internal_pdutype();
}
inline void PMMInfo::_internal_set_pdutype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  pdutype_ = value;
}
inline void PMMInfo::set_pdutype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_pdutype(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PMMInfo.PduType)
}

// uint32 coolingType = 19;
inline void PMMInfo::clear_coolingtype() {
  coolingtype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::_internal_coolingtype() const {
  return coolingtype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::coolingtype() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PMMInfo.coolingType)
  return _internal_coolingtype();
}
inline void PMMInfo::_internal_set_coolingtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  coolingtype_ = value;
}
inline void PMMInfo::set_coolingtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_coolingtype(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PMMInfo.coolingType)
}

// uint32 liquidDeviceType = 20;
inline void PMMInfo::clear_liquiddevicetype() {
  liquiddevicetype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::_internal_liquiddevicetype() const {
  return liquiddevicetype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::liquiddevicetype() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PMMInfo.liquidDeviceType)
  return _internal_liquiddevicetype();
}
inline void PMMInfo::_internal_set_liquiddevicetype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  liquiddevicetype_ = value;
}
inline void PMMInfo::set_liquiddevicetype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_liquiddevicetype(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PMMInfo.liquidDeviceType)
}

// uint32 liquidCoolAddLiquid = 21;
inline void PMMInfo::clear_liquidcooladdliquid() {
  liquidcooladdliquid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::_internal_liquidcooladdliquid() const {
  return liquidcooladdliquid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PMMInfo::liquidcooladdliquid() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PMMInfo.liquidCoolAddLiquid)
  return _internal_liquidcooladdliquid();
}
inline void PMMInfo::_internal_set_liquidcooladdliquid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  liquidcooladdliquid_ = value;
}
inline void PMMInfo::set_liquidcooladdliquid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_liquidcooladdliquid(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PMMInfo.liquidCoolAddLiquid)
}

// -------------------------------------------------------------------

// ServerInfo

// uint32 platform = 1;
inline void ServerInfo::clear_platform() {
  platform_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ServerInfo::_internal_platform() const {
  return platform_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ServerInfo::platform() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ServerInfo.platform)
  return _internal_platform();
}
inline void ServerInfo::_internal_set_platform(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  platform_ = value;
}
inline void ServerInfo::set_platform(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_platform(value);
  // @@protoc_insertion_point(field_set:DMCinfo.ServerInfo.platform)
}

// uint32 qrcode = 2;
inline void ServerInfo::clear_qrcode() {
  qrcode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ServerInfo::_internal_qrcode() const {
  return qrcode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ServerInfo::qrcode() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ServerInfo.qrcode)
  return _internal_qrcode();
}
inline void ServerInfo::_internal_set_qrcode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  qrcode_ = value;
}
inline void ServerInfo::set_qrcode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_qrcode(value);
  // @@protoc_insertion_point(field_set:DMCinfo.ServerInfo.qrcode)
}

// uint32 vin = 3;
inline void ServerInfo::clear_vin() {
  vin_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ServerInfo::_internal_vin() const {
  return vin_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ServerInfo::vin() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ServerInfo.vin)
  return _internal_vin();
}
inline void ServerInfo::_internal_set_vin(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  vin_ = value;
}
inline void ServerInfo::set_vin(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_vin(value);
  // @@protoc_insertion_point(field_set:DMCinfo.ServerInfo.vin)
}

// uint32 card = 4;
inline void ServerInfo::clear_card() {
  card_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ServerInfo::_internal_card() const {
  return card_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ServerInfo::card() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ServerInfo.card)
  return _internal_card();
}
inline void ServerInfo::_internal_set_card(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  card_ = value;
}
inline void ServerInfo::set_card(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_card(value);
  // @@protoc_insertion_point(field_set:DMCinfo.ServerInfo.card)
}

// string platName = 5;
inline void ServerInfo::clear_platname() {
  platname_.ClearToEmpty();
}
inline const std::string& ServerInfo::platname() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ServerInfo.platName)
  return _internal_platname();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ServerInfo::set_platname(ArgT0&& arg0, ArgT... args) {
 
 platname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:DMCinfo.ServerInfo.platName)
}
inline std::string* ServerInfo::mutable_platname() {
  std::string* _s = _internal_mutable_platname();
  // @@protoc_insertion_point(field_mutable:DMCinfo.ServerInfo.platName)
  return _s;
}
inline const std::string& ServerInfo::_internal_platname() const {
  return platname_.Get();
}
inline void ServerInfo::_internal_set_platname(const std::string& value) {
  
  platname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ServerInfo::_internal_mutable_platname() {
  
  return platname_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ServerInfo::release_platname() {
  // @@protoc_insertion_point(field_release:DMCinfo.ServerInfo.platName)
  return platname_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ServerInfo::set_allocated_platname(std::string* platname) {
  if (platname != nullptr) {
    
  } else {
    
  }
  platname_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), platname,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:DMCinfo.ServerInfo.platName)
}

// uint32 wifiofflineChgMode = 6;
inline void ServerInfo::clear_wifiofflinechgmode() {
  wifiofflinechgmode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ServerInfo::_internal_wifiofflinechgmode() const {
  return wifiofflinechgmode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 ServerInfo::wifiofflinechgmode() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ServerInfo.wifiofflineChgMode)
  return _internal_wifiofflinechgmode();
}
inline void ServerInfo::_internal_set_wifiofflinechgmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  wifiofflinechgmode_ = value;
}
inline void ServerInfo::set_wifiofflinechgmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_wifiofflinechgmode(value);
  // @@protoc_insertion_point(field_set:DMCinfo.ServerInfo.wifiofflineChgMode)
}

// string oscProcess = 7;
inline void ServerInfo::clear_oscprocess() {
  oscprocess_.ClearToEmpty();
}
inline const std::string& ServerInfo::oscprocess() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ServerInfo.oscProcess)
  return _internal_oscprocess();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ServerInfo::set_oscprocess(ArgT0&& arg0, ArgT... args) {
 
 oscprocess_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:DMCinfo.ServerInfo.oscProcess)
}
inline std::string* ServerInfo::mutable_oscprocess() {
  std::string* _s = _internal_mutable_oscprocess();
  // @@protoc_insertion_point(field_mutable:DMCinfo.ServerInfo.oscProcess)
  return _s;
}
inline const std::string& ServerInfo::_internal_oscprocess() const {
  return oscprocess_.Get();
}
inline void ServerInfo::_internal_set_oscprocess(const std::string& value) {
  
  oscprocess_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ServerInfo::_internal_mutable_oscprocess() {
  
  return oscprocess_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ServerInfo::release_oscprocess() {
  // @@protoc_insertion_point(field_release:DMCinfo.ServerInfo.oscProcess)
  return oscprocess_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ServerInfo::set_allocated_oscprocess(std::string* oscprocess) {
  if (oscprocess != nullptr) {
    
  } else {
    
  }
  oscprocess_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), oscprocess,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:DMCinfo.ServerInfo.oscProcess)
}

// -------------------------------------------------------------------

// OHPInfo

// uint32 terminalID = 1;
inline void OHPInfo::clear_terminalid() {
  terminalid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::_internal_terminalid() const {
  return terminalid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::terminalid() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.terminalID)
  return _internal_terminalid();
}
inline void OHPInfo::_internal_set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  terminalid_ = value;
}
inline void OHPInfo::set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_terminalid(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OHPInfo.terminalID)
}

// uint32 NetType = 2;
inline void OHPInfo::clear_nettype() {
  nettype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::_internal_nettype() const {
  return nettype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::nettype() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.NetType)
  return _internal_nettype();
}
inline void OHPInfo::_internal_set_nettype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  nettype_ = value;
}
inline void OHPInfo::set_nettype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_nettype(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OHPInfo.NetType)
}

// uint32 Vin = 3;
inline void OHPInfo::clear_vin() {
  vin_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::_internal_vin() const {
  return vin_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::vin() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.Vin)
  return _internal_vin();
}
inline void OHPInfo::_internal_set_vin(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  vin_ = value;
}
inline void OHPInfo::set_vin(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_vin(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OHPInfo.Vin)
}

// uint32 AdminMode = 4;
inline void OHPInfo::clear_adminmode() {
  adminmode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::_internal_adminmode() const {
  return adminmode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::adminmode() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.AdminMode)
  return _internal_adminmode();
}
inline void OHPInfo::_internal_set_adminmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  adminmode_ = value;
}
inline void OHPInfo::set_adminmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_adminmode(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OHPInfo.AdminMode)
}

// uint32 AuxVisiable = 5;
inline void OHPInfo::clear_auxvisiable() {
  auxvisiable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::_internal_auxvisiable() const {
  return auxvisiable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::auxvisiable() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.AuxVisiable)
  return _internal_auxvisiable();
}
inline void OHPInfo::_internal_set_auxvisiable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  auxvisiable_ = value;
}
inline void OHPInfo::set_auxvisiable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_auxvisiable(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OHPInfo.AuxVisiable)
}

// uint32 CooperateMode = 6;
inline void OHPInfo::clear_cooperatemode() {
  cooperatemode_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::_internal_cooperatemode() const {
  return cooperatemode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::cooperatemode() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.CooperateMode)
  return _internal_cooperatemode();
}
inline void OHPInfo::_internal_set_cooperatemode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  cooperatemode_ = value;
}
inline void OHPInfo::set_cooperatemode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_cooperatemode(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OHPInfo.CooperateMode)
}

// bytes MACip = 7;
inline void OHPInfo::clear_macip() {
  macip_.ClearToEmpty();
}
inline const std::string& OHPInfo::macip() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.MACip)
  return _internal_macip();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OHPInfo::set_macip(ArgT0&& arg0, ArgT... args) {
 
 macip_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:DMCinfo.OHPInfo.MACip)
}
inline std::string* OHPInfo::mutable_macip() {
  std::string* _s = _internal_mutable_macip();
  // @@protoc_insertion_point(field_mutable:DMCinfo.OHPInfo.MACip)
  return _s;
}
inline const std::string& OHPInfo::_internal_macip() const {
  return macip_.Get();
}
inline void OHPInfo::_internal_set_macip(const std::string& value) {
  
  macip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* OHPInfo::_internal_mutable_macip() {
  
  return macip_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* OHPInfo::release_macip() {
  // @@protoc_insertion_point(field_release:DMCinfo.OHPInfo.MACip)
  return macip_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void OHPInfo::set_allocated_macip(std::string* macip) {
  if (macip != nullptr) {
    
  } else {
    
  }
  macip_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), macip,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:DMCinfo.OHPInfo.MACip)
}

// bytes WiFiip = 8;
inline void OHPInfo::clear_wifiip() {
  wifiip_.ClearToEmpty();
}
inline const std::string& OHPInfo::wifiip() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.WiFiip)
  return _internal_wifiip();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OHPInfo::set_wifiip(ArgT0&& arg0, ArgT... args) {
 
 wifiip_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:DMCinfo.OHPInfo.WiFiip)
}
inline std::string* OHPInfo::mutable_wifiip() {
  std::string* _s = _internal_mutable_wifiip();
  // @@protoc_insertion_point(field_mutable:DMCinfo.OHPInfo.WiFiip)
  return _s;
}
inline const std::string& OHPInfo::_internal_wifiip() const {
  return wifiip_.Get();
}
inline void OHPInfo::_internal_set_wifiip(const std::string& value) {
  
  wifiip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* OHPInfo::_internal_mutable_wifiip() {
  
  return wifiip_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* OHPInfo::release_wifiip() {
  // @@protoc_insertion_point(field_release:DMCinfo.OHPInfo.WiFiip)
  return wifiip_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void OHPInfo::set_allocated_wifiip(std::string* wifiip) {
  if (wifiip != nullptr) {
    
  } else {
    
  }
  wifiip_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), wifiip,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:DMCinfo.OHPInfo.WiFiip)
}

// repeated .DMCinfo.ServerInfo servers = 9;
inline int OHPInfo::_internal_servers_size() const {
  return servers_.size();
}
inline int OHPInfo::servers_size() const {
  return _internal_servers_size();
}
inline void OHPInfo::clear_servers() {
  servers_.Clear();
}
inline ::DMCinfo::ServerInfo* OHPInfo::mutable_servers(int index) {
  // @@protoc_insertion_point(field_mutable:DMCinfo.OHPInfo.servers)
  return servers_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::ServerInfo >*
OHPInfo::mutable_servers() {
  // @@protoc_insertion_point(field_mutable_list:DMCinfo.OHPInfo.servers)
  return &servers_;
}
inline const ::DMCinfo::ServerInfo& OHPInfo::_internal_servers(int index) const {
  return servers_.Get(index);
}
inline const ::DMCinfo::ServerInfo& OHPInfo::servers(int index) const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.servers)
  return _internal_servers(index);
}
inline ::DMCinfo::ServerInfo* OHPInfo::_internal_add_servers() {
  return servers_.Add();
}
inline ::DMCinfo::ServerInfo* OHPInfo::add_servers() {
  ::DMCinfo::ServerInfo* _add = _internal_add_servers();
  // @@protoc_insertion_point(field_add:DMCinfo.OHPInfo.servers)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::ServerInfo >&
OHPInfo::servers() const {
  // @@protoc_insertion_point(field_list:DMCinfo.OHPInfo.servers)
  return servers_;
}

// uint32 OrderHistory = 10;
inline void OHPInfo::clear_orderhistory() {
  orderhistory_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::_internal_orderhistory() const {
  return orderhistory_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::orderhistory() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.OrderHistory)
  return _internal_orderhistory();
}
inline void OHPInfo::_internal_set_orderhistory(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  orderhistory_ = value;
}
inline void OHPInfo::set_orderhistory(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_orderhistory(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OHPInfo.OrderHistory)
}

// uint32 FaultsHistory = 11;
inline void OHPInfo::clear_faultshistory() {
  faultshistory_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::_internal_faultshistory() const {
  return faultshistory_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::faultshistory() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.FaultsHistory)
  return _internal_faultshistory();
}
inline void OHPInfo::_internal_set_faultshistory(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  faultshistory_ = value;
}
inline void OHPInfo::set_faultshistory(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_faultshistory(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OHPInfo.FaultsHistory)
}

// uint32 stopType = 12;
inline void OHPInfo::clear_stoptype() {
  stoptype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::_internal_stoptype() const {
  return stoptype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::stoptype() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.stopType)
  return _internal_stoptype();
}
inline void OHPInfo::_internal_set_stoptype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  stoptype_ = value;
}
inline void OHPInfo::set_stoptype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_stoptype(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OHPInfo.stopType)
}

// uint32 RateType = 13;
inline void OHPInfo::clear_ratetype() {
  ratetype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::_internal_ratetype() const {
  return ratetype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::ratetype() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.RateType)
  return _internal_ratetype();
}
inline void OHPInfo::_internal_set_ratetype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  ratetype_ = value;
}
inline void OHPInfo::set_ratetype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_ratetype(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OHPInfo.RateType)
}

// uint32 standbylogoType = 14;
inline void OHPInfo::clear_standbylogotype() {
  standbylogotype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::_internal_standbylogotype() const {
  return standbylogotype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::standbylogotype() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.standbylogoType)
  return _internal_standbylogotype();
}
inline void OHPInfo::_internal_set_standbylogotype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  standbylogotype_ = value;
}
inline void OHPInfo::set_standbylogotype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_standbylogotype(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OHPInfo.standbylogoType)
}

// uint32 ledType = 15;
inline void OHPInfo::clear_ledtype() {
  ledtype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::_internal_ledtype() const {
  return ledtype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::ledtype() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.ledType)
  return _internal_ledtype();
}
inline void OHPInfo::_internal_set_ledtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  ledtype_ = value;
}
inline void OHPInfo::set_ledtype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_ledtype(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OHPInfo.ledType)
}

// uint32 stopChgSocType = 16;
inline void OHPInfo::clear_stopchgsoctype() {
  stopchgsoctype_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::_internal_stopchgsoctype() const {
  return stopchgsoctype_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::stopchgsoctype() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.stopChgSocType)
  return _internal_stopchgsoctype();
}
inline void OHPInfo::_internal_set_stopchgsoctype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  stopchgsoctype_ = value;
}
inline void OHPInfo::set_stopchgsoctype(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_stopchgsoctype(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OHPInfo.stopChgSocType)
}

// uint32 hmiConfigEnable = 17;
inline void OHPInfo::clear_hmiconfigenable() {
  hmiconfigenable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::_internal_hmiconfigenable() const {
  return hmiconfigenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::hmiconfigenable() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.hmiConfigEnable)
  return _internal_hmiconfigenable();
}
inline void OHPInfo::_internal_set_hmiconfigenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  hmiconfigenable_ = value;
}
inline void OHPInfo::set_hmiconfigenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_hmiconfigenable(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OHPInfo.hmiConfigEnable)
}

// uint32 netOfflineWifiEnable = 18;
inline void OHPInfo::clear_netofflinewifienable() {
  netofflinewifienable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::_internal_netofflinewifienable() const {
  return netofflinewifienable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::netofflinewifienable() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.netOfflineWifiEnable)
  return _internal_netofflinewifienable();
}
inline void OHPInfo::_internal_set_netofflinewifienable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  netofflinewifienable_ = value;
}
inline void OHPInfo::set_netofflinewifienable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_netofflinewifienable(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OHPInfo.netOfflineWifiEnable)
}

// uint32 VLPREnable = 19;
inline void OHPInfo::clear_vlprenable() {
  vlprenable_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::_internal_vlprenable() const {
  return vlprenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::vlprenable() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.VLPREnable)
  return _internal_vlprenable();
}
inline void OHPInfo::_internal_set_vlprenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  vlprenable_ = value;
}
inline void OHPInfo::set_vlprenable(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_vlprenable(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OHPInfo.VLPREnable)
}

// repeated .DMCinfo.GUNInfo gunInfo = 20;
inline int OHPInfo::_internal_guninfo_size() const {
  return guninfo_.size();
}
inline int OHPInfo::guninfo_size() const {
  return _internal_guninfo_size();
}
inline void OHPInfo::clear_guninfo() {
  guninfo_.Clear();
}
inline ::DMCinfo::GUNInfo* OHPInfo::mutable_guninfo(int index) {
  // @@protoc_insertion_point(field_mutable:DMCinfo.OHPInfo.gunInfo)
  return guninfo_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::GUNInfo >*
OHPInfo::mutable_guninfo() {
  // @@protoc_insertion_point(field_mutable_list:DMCinfo.OHPInfo.gunInfo)
  return &guninfo_;
}
inline const ::DMCinfo::GUNInfo& OHPInfo::_internal_guninfo(int index) const {
  return guninfo_.Get(index);
}
inline const ::DMCinfo::GUNInfo& OHPInfo::guninfo(int index) const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.gunInfo)
  return _internal_guninfo(index);
}
inline ::DMCinfo::GUNInfo* OHPInfo::_internal_add_guninfo() {
  return guninfo_.Add();
}
inline ::DMCinfo::GUNInfo* OHPInfo::add_guninfo() {
  ::DMCinfo::GUNInfo* _add = _internal_add_guninfo();
  // @@protoc_insertion_point(field_add:DMCinfo.OHPInfo.gunInfo)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::GUNInfo >&
OHPInfo::guninfo() const {
  // @@protoc_insertion_point(field_list:DMCinfo.OHPInfo.gunInfo)
  return guninfo_;
}

// uint32 ratedPower = 21;
inline void OHPInfo::clear_ratedpower() {
  ratedpower_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::_internal_ratedpower() const {
  return ratedpower_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::ratedpower() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.ratedPower)
  return _internal_ratedpower();
}
inline void OHPInfo::_internal_set_ratedpower(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  ratedpower_ = value;
}
inline void OHPInfo::set_ratedpower(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_ratedpower(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OHPInfo.ratedPower)
}

// string productModel = 22;
inline void OHPInfo::clear_productmodel() {
  productmodel_.ClearToEmpty();
}
inline const std::string& OHPInfo::productmodel() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.productModel)
  return _internal_productmodel();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OHPInfo::set_productmodel(ArgT0&& arg0, ArgT... args) {
 
 productmodel_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:DMCinfo.OHPInfo.productModel)
}
inline std::string* OHPInfo::mutable_productmodel() {
  std::string* _s = _internal_mutable_productmodel();
  // @@protoc_insertion_point(field_mutable:DMCinfo.OHPInfo.productModel)
  return _s;
}
inline const std::string& OHPInfo::_internal_productmodel() const {
  return productmodel_.Get();
}
inline void OHPInfo::_internal_set_productmodel(const std::string& value) {
  
  productmodel_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* OHPInfo::_internal_mutable_productmodel() {
  
  return productmodel_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* OHPInfo::release_productmodel() {
  // @@protoc_insertion_point(field_release:DMCinfo.OHPInfo.productModel)
  return productmodel_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void OHPInfo::set_allocated_productmodel(std::string* productmodel) {
  if (productmodel != nullptr) {
    
  } else {
    
  }
  productmodel_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), productmodel,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:DMCinfo.OHPInfo.productModel)
}

// string ccid = 23;
inline void OHPInfo::clear_ccid() {
  ccid_.ClearToEmpty();
}
inline const std::string& OHPInfo::ccid() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.ccid)
  return _internal_ccid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OHPInfo::set_ccid(ArgT0&& arg0, ArgT... args) {
 
 ccid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:DMCinfo.OHPInfo.ccid)
}
inline std::string* OHPInfo::mutable_ccid() {
  std::string* _s = _internal_mutable_ccid();
  // @@protoc_insertion_point(field_mutable:DMCinfo.OHPInfo.ccid)
  return _s;
}
inline const std::string& OHPInfo::_internal_ccid() const {
  return ccid_.Get();
}
inline void OHPInfo::_internal_set_ccid(const std::string& value) {
  
  ccid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* OHPInfo::_internal_mutable_ccid() {
  
  return ccid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* OHPInfo::release_ccid() {
  // @@protoc_insertion_point(field_release:DMCinfo.OHPInfo.ccid)
  return ccid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void OHPInfo::set_allocated_ccid(std::string* ccid) {
  if (ccid != nullptr) {
    
  } else {
    
  }
  ccid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ccid,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:DMCinfo.OHPInfo.ccid)
}

// uint32 maxCurrent = 24;
inline void OHPInfo::clear_maxcurrent() {
  maxcurrent_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::_internal_maxcurrent() const {
  return maxcurrent_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OHPInfo::maxcurrent() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OHPInfo.maxCurrent)
  return _internal_maxcurrent();
}
inline void OHPInfo::_internal_set_maxcurrent(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  maxcurrent_ = value;
}
inline void OHPInfo::set_maxcurrent(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_maxcurrent(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OHPInfo.maxCurrent)
}

// -------------------------------------------------------------------

// HmcChargingStrategyM

// .DMCinfo.HmcChargeStrategyEnum chargeStrategy = 1;
inline void HmcChargingStrategyM::clear_chargestrategy() {
  chargestrategy_ = 0;
}
inline ::DMCinfo::HmcChargeStrategyEnum HmcChargingStrategyM::_internal_chargestrategy() const {
  return static_cast< ::DMCinfo::HmcChargeStrategyEnum >(chargestrategy_);
}
inline ::DMCinfo::HmcChargeStrategyEnum HmcChargingStrategyM::chargestrategy() const {
  // @@protoc_insertion_point(field_get:DMCinfo.HmcChargingStrategyM.chargeStrategy)
  return _internal_chargestrategy();
}
inline void HmcChargingStrategyM::_internal_set_chargestrategy(::DMCinfo::HmcChargeStrategyEnum value) {
  
  chargestrategy_ = value;
}
inline void HmcChargingStrategyM::set_chargestrategy(::DMCinfo::HmcChargeStrategyEnum value) {
  _internal_set_chargestrategy(value);
  // @@protoc_insertion_point(field_set:DMCinfo.HmcChargingStrategyM.chargeStrategy)
}

// .DMCinfo.HmcChargeModeEnum chargeStar = 2;
inline void HmcChargingStrategyM::clear_chargestar() {
  chargestar_ = 0;
}
inline ::DMCinfo::HmcChargeModeEnum HmcChargingStrategyM::_internal_chargestar() const {
  return static_cast< ::DMCinfo::HmcChargeModeEnum >(chargestar_);
}
inline ::DMCinfo::HmcChargeModeEnum HmcChargingStrategyM::chargestar() const {
  // @@protoc_insertion_point(field_get:DMCinfo.HmcChargingStrategyM.chargeStar)
  return _internal_chargestar();
}
inline void HmcChargingStrategyM::_internal_set_chargestar(::DMCinfo::HmcChargeModeEnum value) {
  
  chargestar_ = value;
}
inline void HmcChargingStrategyM::set_chargestar(::DMCinfo::HmcChargeModeEnum value) {
  _internal_set_chargestar(value);
  // @@protoc_insertion_point(field_set:DMCinfo.HmcChargingStrategyM.chargeStar)
}

// uint32 startTime = 3;
inline void HmcChargingStrategyM::clear_starttime() {
  starttime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HmcChargingStrategyM::_internal_starttime() const {
  return starttime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HmcChargingStrategyM::starttime() const {
  // @@protoc_insertion_point(field_get:DMCinfo.HmcChargingStrategyM.startTime)
  return _internal_starttime();
}
inline void HmcChargingStrategyM::_internal_set_starttime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  starttime_ = value;
}
inline void HmcChargingStrategyM::set_starttime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_starttime(value);
  // @@protoc_insertion_point(field_set:DMCinfo.HmcChargingStrategyM.startTime)
}

// uint32 endTime = 4;
inline void HmcChargingStrategyM::clear_endtime() {
  endtime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HmcChargingStrategyM::_internal_endtime() const {
  return endtime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HmcChargingStrategyM::endtime() const {
  // @@protoc_insertion_point(field_get:DMCinfo.HmcChargingStrategyM.endTime)
  return _internal_endtime();
}
inline void HmcChargingStrategyM::_internal_set_endtime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  endtime_ = value;
}
inline void HmcChargingStrategyM::set_endtime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_endtime(value);
  // @@protoc_insertion_point(field_set:DMCinfo.HmcChargingStrategyM.endTime)
}

// -------------------------------------------------------------------

// HMCInfo

// uint32 terminalID = 1;
inline void HMCInfo::clear_terminalid() {
  terminalid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HMCInfo::_internal_terminalid() const {
  return terminalid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HMCInfo::terminalid() const {
  // @@protoc_insertion_point(field_get:DMCinfo.HMCInfo.terminalID)
  return _internal_terminalid();
}
inline void HMCInfo::_internal_set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  terminalid_ = value;
}
inline void HMCInfo::set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_terminalid(value);
  // @@protoc_insertion_point(field_set:DMCinfo.HMCInfo.terminalID)
}

// .DMCinfo.HmcLcrEnum lcrType = 2;
inline void HMCInfo::clear_lcrtype() {
  lcrtype_ = 0;
}
inline ::DMCinfo::HmcLcrEnum HMCInfo::_internal_lcrtype() const {
  return static_cast< ::DMCinfo::HmcLcrEnum >(lcrtype_);
}
inline ::DMCinfo::HmcLcrEnum HMCInfo::lcrtype() const {
  // @@protoc_insertion_point(field_get:DMCinfo.HMCInfo.lcrType)
  return _internal_lcrtype();
}
inline void HMCInfo::_internal_set_lcrtype(::DMCinfo::HmcLcrEnum value) {
  
  lcrtype_ = value;
}
inline void HMCInfo::set_lcrtype(::DMCinfo::HmcLcrEnum value) {
  _internal_set_lcrtype(value);
  // @@protoc_insertion_point(field_set:DMCinfo.HMCInfo.lcrType)
}

// string terminalCode = 3;
inline void HMCInfo::clear_terminalcode() {
  terminalcode_.ClearToEmpty();
}
inline const std::string& HMCInfo::terminalcode() const {
  // @@protoc_insertion_point(field_get:DMCinfo.HMCInfo.terminalCode)
  return _internal_terminalcode();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HMCInfo::set_terminalcode(ArgT0&& arg0, ArgT... args) {
 
 terminalcode_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:DMCinfo.HMCInfo.terminalCode)
}
inline std::string* HMCInfo::mutable_terminalcode() {
  std::string* _s = _internal_mutable_terminalcode();
  // @@protoc_insertion_point(field_mutable:DMCinfo.HMCInfo.terminalCode)
  return _s;
}
inline const std::string& HMCInfo::_internal_terminalcode() const {
  return terminalcode_.Get();
}
inline void HMCInfo::_internal_set_terminalcode(const std::string& value) {
  
  terminalcode_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* HMCInfo::_internal_mutable_terminalcode() {
  
  return terminalcode_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* HMCInfo::release_terminalcode() {
  // @@protoc_insertion_point(field_release:DMCinfo.HMCInfo.terminalCode)
  return terminalcode_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void HMCInfo::set_allocated_terminalcode(std::string* terminalcode) {
  if (terminalcode != nullptr) {
    
  } else {
    
  }
  terminalcode_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), terminalcode,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:DMCinfo.HMCInfo.terminalCode)
}

// uint32 soc = 4;
inline void HMCInfo::clear_soc() {
  soc_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HMCInfo::_internal_soc() const {
  return soc_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HMCInfo::soc() const {
  // @@protoc_insertion_point(field_get:DMCinfo.HMCInfo.soc)
  return _internal_soc();
}
inline void HMCInfo::_internal_set_soc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  soc_ = value;
}
inline void HMCInfo::set_soc(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_soc(value);
  // @@protoc_insertion_point(field_set:DMCinfo.HMCInfo.soc)
}

// repeated .DMCinfo.ServerInfo platInfo = 5;
inline int HMCInfo::_internal_platinfo_size() const {
  return platinfo_.size();
}
inline int HMCInfo::platinfo_size() const {
  return _internal_platinfo_size();
}
inline void HMCInfo::clear_platinfo() {
  platinfo_.Clear();
}
inline ::DMCinfo::ServerInfo* HMCInfo::mutable_platinfo(int index) {
  // @@protoc_insertion_point(field_mutable:DMCinfo.HMCInfo.platInfo)
  return platinfo_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::ServerInfo >*
HMCInfo::mutable_platinfo() {
  // @@protoc_insertion_point(field_mutable_list:DMCinfo.HMCInfo.platInfo)
  return &platinfo_;
}
inline const ::DMCinfo::ServerInfo& HMCInfo::_internal_platinfo(int index) const {
  return platinfo_.Get(index);
}
inline const ::DMCinfo::ServerInfo& HMCInfo::platinfo(int index) const {
  // @@protoc_insertion_point(field_get:DMCinfo.HMCInfo.platInfo)
  return _internal_platinfo(index);
}
inline ::DMCinfo::ServerInfo* HMCInfo::_internal_add_platinfo() {
  return platinfo_.Add();
}
inline ::DMCinfo::ServerInfo* HMCInfo::add_platinfo() {
  ::DMCinfo::ServerInfo* _add = _internal_add_platinfo();
  // @@protoc_insertion_point(field_add:DMCinfo.HMCInfo.platInfo)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::ServerInfo >&
HMCInfo::platinfo() const {
  // @@protoc_insertion_point(field_list:DMCinfo.HMCInfo.platInfo)
  return platinfo_;
}

// repeated .DMCinfo.HmcChargingStrategyM strategy = 6;
inline int HMCInfo::_internal_strategy_size() const {
  return strategy_.size();
}
inline int HMCInfo::strategy_size() const {
  return _internal_strategy_size();
}
inline void HMCInfo::clear_strategy() {
  strategy_.Clear();
}
inline ::DMCinfo::HmcChargingStrategyM* HMCInfo::mutable_strategy(int index) {
  // @@protoc_insertion_point(field_mutable:DMCinfo.HMCInfo.strategy)
  return strategy_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::HmcChargingStrategyM >*
HMCInfo::mutable_strategy() {
  // @@protoc_insertion_point(field_mutable_list:DMCinfo.HMCInfo.strategy)
  return &strategy_;
}
inline const ::DMCinfo::HmcChargingStrategyM& HMCInfo::_internal_strategy(int index) const {
  return strategy_.Get(index);
}
inline const ::DMCinfo::HmcChargingStrategyM& HMCInfo::strategy(int index) const {
  // @@protoc_insertion_point(field_get:DMCinfo.HMCInfo.strategy)
  return _internal_strategy(index);
}
inline ::DMCinfo::HmcChargingStrategyM* HMCInfo::_internal_add_strategy() {
  return strategy_.Add();
}
inline ::DMCinfo::HmcChargingStrategyM* HMCInfo::add_strategy() {
  ::DMCinfo::HmcChargingStrategyM* _add = _internal_add_strategy();
  // @@protoc_insertion_point(field_add:DMCinfo.HMCInfo.strategy)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::DMCinfo::HmcChargingStrategyM >&
HMCInfo::strategy() const {
  // @@protoc_insertion_point(field_list:DMCinfo.HMCInfo.strategy)
  return strategy_;
}

// -------------------------------------------------------------------

// GunLoadConstraint

// uint32 terminalID = 1;
inline void GunLoadConstraint::clear_terminalid() {
  terminalid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunLoadConstraint::_internal_terminalid() const {
  return terminalid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunLoadConstraint::terminalid() const {
  // @@protoc_insertion_point(field_get:DMCinfo.GunLoadConstraint.terminalID)
  return _internal_terminalid();
}
inline void GunLoadConstraint::_internal_set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  terminalid_ = value;
}
inline void GunLoadConstraint::set_terminalid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_terminalid(value);
  // @@protoc_insertion_point(field_set:DMCinfo.GunLoadConstraint.terminalID)
}

// uint32 gunID = 2;
inline void GunLoadConstraint::clear_gunid() {
  gunid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunLoadConstraint::_internal_gunid() const {
  return gunid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunLoadConstraint::gunid() const {
  // @@protoc_insertion_point(field_get:DMCinfo.GunLoadConstraint.gunID)
  return _internal_gunid();
}
inline void GunLoadConstraint::_internal_set_gunid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  gunid_ = value;
}
inline void GunLoadConstraint::set_gunid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_gunid(value);
  // @@protoc_insertion_point(field_set:DMCinfo.GunLoadConstraint.gunID)
}

// repeated uint32 gunNum = 3;
inline int GunLoadConstraint::_internal_gunnum_size() const {
  return gunnum_.size();
}
inline int GunLoadConstraint::gunnum_size() const {
  return _internal_gunnum_size();
}
inline void GunLoadConstraint::clear_gunnum() {
  gunnum_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunLoadConstraint::_internal_gunnum(int index) const {
  return gunnum_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunLoadConstraint::gunnum(int index) const {
  // @@protoc_insertion_point(field_get:DMCinfo.GunLoadConstraint.gunNum)
  return _internal_gunnum(index);
}
inline void GunLoadConstraint::set_gunnum(int index, ::PROTOBUF_NAMESPACE_ID::uint32 value) {
  gunnum_.Set(index, value);
  // @@protoc_insertion_point(field_set:DMCinfo.GunLoadConstraint.gunNum)
}
inline void GunLoadConstraint::_internal_add_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  gunnum_.Add(value);
}
inline void GunLoadConstraint::add_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_add_gunnum(value);
  // @@protoc_insertion_point(field_add:DMCinfo.GunLoadConstraint.gunNum)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >&
GunLoadConstraint::_internal_gunnum() const {
  return gunnum_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >&
GunLoadConstraint::gunnum() const {
  // @@protoc_insertion_point(field_list:DMCinfo.GunLoadConstraint.gunNum)
  return _internal_gunnum();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >*
GunLoadConstraint::_internal_mutable_gunnum() {
  return &gunnum_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >*
GunLoadConstraint::mutable_gunnum() {
  // @@protoc_insertion_point(field_mutable_list:DMCinfo.GunLoadConstraint.gunNum)
  return _internal_mutable_gunnum();
}

// uint32 GunlimitPower = 4;
inline void GunLoadConstraint::clear_gunlimitpower() {
  gunlimitpower_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunLoadConstraint::_internal_gunlimitpower() const {
  return gunlimitpower_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunLoadConstraint::gunlimitpower() const {
  // @@protoc_insertion_point(field_get:DMCinfo.GunLoadConstraint.GunlimitPower)
  return _internal_gunlimitpower();
}
inline void GunLoadConstraint::_internal_set_gunlimitpower(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  gunlimitpower_ = value;
}
inline void GunLoadConstraint::set_gunlimitpower(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_gunlimitpower(value);
  // @@protoc_insertion_point(field_set:DMCinfo.GunLoadConstraint.GunlimitPower)
}

// -------------------------------------------------------------------

// GunMatchInfo

// uint32 gunID = 1;
inline void GunMatchInfo::clear_gunid() {
  gunid_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunMatchInfo::_internal_gunid() const {
  return gunid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunMatchInfo::gunid() const {
  // @@protoc_insertion_point(field_get:DMCinfo.GunMatchInfo.gunID)
  return _internal_gunid();
}
inline void GunMatchInfo::_internal_set_gunid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  gunid_ = value;
}
inline void GunMatchInfo::set_gunid(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_gunid(value);
  // @@protoc_insertion_point(field_set:DMCinfo.GunMatchInfo.gunID)
}

// repeated uint32 gunNum = 2;
inline int GunMatchInfo::_internal_gunnum_size() const {
  return gunnum_.size();
}
inline int GunMatchInfo::gunnum_size() const {
  return _internal_gunnum_size();
}
inline void GunMatchInfo::clear_gunnum() {
  gunnum_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunMatchInfo::_internal_gunnum(int index) const {
  return gunnum_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunMatchInfo::gunnum(int index) const {
  // @@protoc_insertion_point(field_get:DMCinfo.GunMatchInfo.gunNum)
  return _internal_gunnum(index);
}
inline void GunMatchInfo::set_gunnum(int index, ::PROTOBUF_NAMESPACE_ID::uint32 value) {
  gunnum_.Set(index, value);
  // @@protoc_insertion_point(field_set:DMCinfo.GunMatchInfo.gunNum)
}
inline void GunMatchInfo::_internal_add_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  gunnum_.Add(value);
}
inline void GunMatchInfo::add_gunnum(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_add_gunnum(value);
  // @@protoc_insertion_point(field_add:DMCinfo.GunMatchInfo.gunNum)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >&
GunMatchInfo::_internal_gunnum() const {
  return gunnum_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >&
GunMatchInfo::gunnum() const {
  // @@protoc_insertion_point(field_list:DMCinfo.GunMatchInfo.gunNum)
  return _internal_gunnum();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >*
GunMatchInfo::_internal_mutable_gunnum() {
  return &gunnum_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >*
GunMatchInfo::mutable_gunnum() {
  // @@protoc_insertion_point(field_mutable_list:DMCinfo.GunMatchInfo.gunNum)
  return _internal_mutable_gunnum();
}

// uint32 isAdjacent = 3;
inline void GunMatchInfo::clear_isadjacent() {
  isadjacent_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunMatchInfo::_internal_isadjacent() const {
  return isadjacent_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 GunMatchInfo::isadjacent() const {
  // @@protoc_insertion_point(field_get:DMCinfo.GunMatchInfo.isAdjacent)
  return _internal_isadjacent();
}
inline void GunMatchInfo::_internal_set_isadjacent(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  isadjacent_ = value;
}
inline void GunMatchInfo::set_isadjacent(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_isadjacent(value);
  // @@protoc_insertion_point(field_set:DMCinfo.GunMatchInfo.isAdjacent)
}

// -------------------------------------------------------------------

// CurrentBalance

// float EqualChgCurrCoef = 1;
inline void CurrentBalance::clear_equalchgcurrcoef() {
  equalchgcurrcoef_ = 0;
}
inline float CurrentBalance::_internal_equalchgcurrcoef() const {
  return equalchgcurrcoef_;
}
inline float CurrentBalance::equalchgcurrcoef() const {
  // @@protoc_insertion_point(field_get:DMCinfo.CurrentBalance.EqualChgCurrCoef)
  return _internal_equalchgcurrcoef();
}
inline void CurrentBalance::_internal_set_equalchgcurrcoef(float value) {
  
  equalchgcurrcoef_ = value;
}
inline void CurrentBalance::set_equalchgcurrcoef(float value) {
  _internal_set_equalchgcurrcoef(value);
  // @@protoc_insertion_point(field_set:DMCinfo.CurrentBalance.EqualChgCurrCoef)
}

// uint32 AllowEqualChgTime = 2;
inline void CurrentBalance::clear_allowequalchgtime() {
  allowequalchgtime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CurrentBalance::_internal_allowequalchgtime() const {
  return allowequalchgtime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CurrentBalance::allowequalchgtime() const {
  // @@protoc_insertion_point(field_get:DMCinfo.CurrentBalance.AllowEqualChgTime)
  return _internal_allowequalchgtime();
}
inline void CurrentBalance::_internal_set_allowequalchgtime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  allowequalchgtime_ = value;
}
inline void CurrentBalance::set_allowequalchgtime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_allowequalchgtime(value);
  // @@protoc_insertion_point(field_set:DMCinfo.CurrentBalance.AllowEqualChgTime)
}

// -------------------------------------------------------------------

// HotRunaway

// float HotRunThreshold = 1;
inline void HotRunaway::clear_hotrunthreshold() {
  hotrunthreshold_ = 0;
}
inline float HotRunaway::_internal_hotrunthreshold() const {
  return hotrunthreshold_;
}
inline float HotRunaway::hotrunthreshold() const {
  // @@protoc_insertion_point(field_get:DMCinfo.HotRunaway.HotRunThreshold)
  return _internal_hotrunthreshold();
}
inline void HotRunaway::_internal_set_hotrunthreshold(float value) {
  
  hotrunthreshold_ = value;
}
inline void HotRunaway::set_hotrunthreshold(float value) {
  _internal_set_hotrunthreshold(value);
  // @@protoc_insertion_point(field_set:DMCinfo.HotRunaway.HotRunThreshold)
}

// uint32 HotRunConfireTime = 2;
inline void HotRunaway::clear_hotrunconfiretime() {
  hotrunconfiretime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HotRunaway::_internal_hotrunconfiretime() const {
  return hotrunconfiretime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HotRunaway::hotrunconfiretime() const {
  // @@protoc_insertion_point(field_get:DMCinfo.HotRunaway.HotRunConfireTime)
  return _internal_hotrunconfiretime();
}
inline void HotRunaway::_internal_set_hotrunconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  hotrunconfiretime_ = value;
}
inline void HotRunaway::set_hotrunconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_hotrunconfiretime(value);
  // @@protoc_insertion_point(field_set:DMCinfo.HotRunaway.HotRunConfireTime)
}

// uint32 HotRunProtDisabled = 3;
inline void HotRunaway::clear_hotrunprotdisabled() {
  hotrunprotdisabled_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HotRunaway::_internal_hotrunprotdisabled() const {
  return hotrunprotdisabled_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 HotRunaway::hotrunprotdisabled() const {
  // @@protoc_insertion_point(field_get:DMCinfo.HotRunaway.HotRunProtDisabled)
  return _internal_hotrunprotdisabled();
}
inline void HotRunaway::_internal_set_hotrunprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  hotrunprotdisabled_ = value;
}
inline void HotRunaway::set_hotrunprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_hotrunprotdisabled(value);
  // @@protoc_insertion_point(field_set:DMCinfo.HotRunaway.HotRunProtDisabled)
}

// -------------------------------------------------------------------

// CellOverVolt

// float LiFePO4CellAllowedChgVoltMax = 1;
inline void CellOverVolt::clear_lifepo4cellallowedchgvoltmax() {
  lifepo4cellallowedchgvoltmax_ = 0;
}
inline float CellOverVolt::_internal_lifepo4cellallowedchgvoltmax() const {
  return lifepo4cellallowedchgvoltmax_;
}
inline float CellOverVolt::lifepo4cellallowedchgvoltmax() const {
  // @@protoc_insertion_point(field_get:DMCinfo.CellOverVolt.LiFePO4CellAllowedChgVoltMax)
  return _internal_lifepo4cellallowedchgvoltmax();
}
inline void CellOverVolt::_internal_set_lifepo4cellallowedchgvoltmax(float value) {
  
  lifepo4cellallowedchgvoltmax_ = value;
}
inline void CellOverVolt::set_lifepo4cellallowedchgvoltmax(float value) {
  _internal_set_lifepo4cellallowedchgvoltmax(value);
  // @@protoc_insertion_point(field_set:DMCinfo.CellOverVolt.LiFePO4CellAllowedChgVoltMax)
}

// float LiMnNiCoCellAllowedChgVoltMax = 2;
inline void CellOverVolt::clear_limnnicocellallowedchgvoltmax() {
  limnnicocellallowedchgvoltmax_ = 0;
}
inline float CellOverVolt::_internal_limnnicocellallowedchgvoltmax() const {
  return limnnicocellallowedchgvoltmax_;
}
inline float CellOverVolt::limnnicocellallowedchgvoltmax() const {
  // @@protoc_insertion_point(field_get:DMCinfo.CellOverVolt.LiMnNiCoCellAllowedChgVoltMax)
  return _internal_limnnicocellallowedchgvoltmax();
}
inline void CellOverVolt::_internal_set_limnnicocellallowedchgvoltmax(float value) {
  
  limnnicocellallowedchgvoltmax_ = value;
}
inline void CellOverVolt::set_limnnicocellallowedchgvoltmax(float value) {
  _internal_set_limnnicocellallowedchgvoltmax(value);
  // @@protoc_insertion_point(field_set:DMCinfo.CellOverVolt.LiMnNiCoCellAllowedChgVoltMax)
}

// float LiTitanateCellAllowedChgVoltMax = 3;
inline void CellOverVolt::clear_lititanatecellallowedchgvoltmax() {
  lititanatecellallowedchgvoltmax_ = 0;
}
inline float CellOverVolt::_internal_lititanatecellallowedchgvoltmax() const {
  return lititanatecellallowedchgvoltmax_;
}
inline float CellOverVolt::lititanatecellallowedchgvoltmax() const {
  // @@protoc_insertion_point(field_get:DMCinfo.CellOverVolt.LiTitanateCellAllowedChgVoltMax)
  return _internal_lititanatecellallowedchgvoltmax();
}
inline void CellOverVolt::_internal_set_lititanatecellallowedchgvoltmax(float value) {
  
  lititanatecellallowedchgvoltmax_ = value;
}
inline void CellOverVolt::set_lititanatecellallowedchgvoltmax(float value) {
  _internal_set_lititanatecellallowedchgvoltmax(value);
  // @@protoc_insertion_point(field_set:DMCinfo.CellOverVolt.LiTitanateCellAllowedChgVoltMax)
}

// float LiManganateCellAllowedChgVoltMax = 4;
inline void CellOverVolt::clear_limanganatecellallowedchgvoltmax() {
  limanganatecellallowedchgvoltmax_ = 0;
}
inline float CellOverVolt::_internal_limanganatecellallowedchgvoltmax() const {
  return limanganatecellallowedchgvoltmax_;
}
inline float CellOverVolt::limanganatecellallowedchgvoltmax() const {
  // @@protoc_insertion_point(field_get:DMCinfo.CellOverVolt.LiManganateCellAllowedChgVoltMax)
  return _internal_limanganatecellallowedchgvoltmax();
}
inline void CellOverVolt::_internal_set_limanganatecellallowedchgvoltmax(float value) {
  
  limanganatecellallowedchgvoltmax_ = value;
}
inline void CellOverVolt::set_limanganatecellallowedchgvoltmax(float value) {
  _internal_set_limanganatecellallowedchgvoltmax(value);
  // @@protoc_insertion_point(field_set:DMCinfo.CellOverVolt.LiManganateCellAllowedChgVoltMax)
}

// uint32 CellOverVoltConfireTime = 5;
inline void CellOverVolt::clear_cellovervoltconfiretime() {
  cellovervoltconfiretime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CellOverVolt::_internal_cellovervoltconfiretime() const {
  return cellovervoltconfiretime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CellOverVolt::cellovervoltconfiretime() const {
  // @@protoc_insertion_point(field_get:DMCinfo.CellOverVolt.CellOverVoltConfireTime)
  return _internal_cellovervoltconfiretime();
}
inline void CellOverVolt::_internal_set_cellovervoltconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  cellovervoltconfiretime_ = value;
}
inline void CellOverVolt::set_cellovervoltconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_cellovervoltconfiretime(value);
  // @@protoc_insertion_point(field_set:DMCinfo.CellOverVolt.CellOverVoltConfireTime)
}

// uint32 CellOverVoltProtDisabled = 6;
inline void CellOverVolt::clear_cellovervoltprotdisabled() {
  cellovervoltprotdisabled_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CellOverVolt::_internal_cellovervoltprotdisabled() const {
  return cellovervoltprotdisabled_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CellOverVolt::cellovervoltprotdisabled() const {
  // @@protoc_insertion_point(field_get:DMCinfo.CellOverVolt.CellOverVoltProtDisabled)
  return _internal_cellovervoltprotdisabled();
}
inline void CellOverVolt::_internal_set_cellovervoltprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  cellovervoltprotdisabled_ = value;
}
inline void CellOverVolt::set_cellovervoltprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_cellovervoltprotdisabled(value);
  // @@protoc_insertion_point(field_set:DMCinfo.CellOverVolt.CellOverVoltProtDisabled)
}

// -------------------------------------------------------------------

// PackOverVolt

// float PackAllowedChgVoltMax = 1;
inline void PackOverVolt::clear_packallowedchgvoltmax() {
  packallowedchgvoltmax_ = 0;
}
inline float PackOverVolt::_internal_packallowedchgvoltmax() const {
  return packallowedchgvoltmax_;
}
inline float PackOverVolt::packallowedchgvoltmax() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PackOverVolt.PackAllowedChgVoltMax)
  return _internal_packallowedchgvoltmax();
}
inline void PackOverVolt::_internal_set_packallowedchgvoltmax(float value) {
  
  packallowedchgvoltmax_ = value;
}
inline void PackOverVolt::set_packallowedchgvoltmax(float value) {
  _internal_set_packallowedchgvoltmax(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PackOverVolt.PackAllowedChgVoltMax)
}

// uint32 PackOverVoltConfireTime = 2;
inline void PackOverVolt::clear_packovervoltconfiretime() {
  packovervoltconfiretime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PackOverVolt::_internal_packovervoltconfiretime() const {
  return packovervoltconfiretime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PackOverVolt::packovervoltconfiretime() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PackOverVolt.PackOverVoltConfireTime)
  return _internal_packovervoltconfiretime();
}
inline void PackOverVolt::_internal_set_packovervoltconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  packovervoltconfiretime_ = value;
}
inline void PackOverVolt::set_packovervoltconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_packovervoltconfiretime(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PackOverVolt.PackOverVoltConfireTime)
}

// uint32 PackOverVoltProtDisabled = 3;
inline void PackOverVolt::clear_packovervoltprotdisabled() {
  packovervoltprotdisabled_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PackOverVolt::_internal_packovervoltprotdisabled() const {
  return packovervoltprotdisabled_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 PackOverVolt::packovervoltprotdisabled() const {
  // @@protoc_insertion_point(field_get:DMCinfo.PackOverVolt.PackOverVoltProtDisabled)
  return _internal_packovervoltprotdisabled();
}
inline void PackOverVolt::_internal_set_packovervoltprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  packovervoltprotdisabled_ = value;
}
inline void PackOverVolt::set_packovervoltprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_packovervoltprotdisabled(value);
  // @@protoc_insertion_point(field_set:DMCinfo.PackOverVolt.PackOverVoltProtDisabled)
}

// -------------------------------------------------------------------

// OverCurrent

// float OverCurrThreshold = 1;
inline void OverCurrent::clear_overcurrthreshold() {
  overcurrthreshold_ = 0;
}
inline float OverCurrent::_internal_overcurrthreshold() const {
  return overcurrthreshold_;
}
inline float OverCurrent::overcurrthreshold() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OverCurrent.OverCurrThreshold)
  return _internal_overcurrthreshold();
}
inline void OverCurrent::_internal_set_overcurrthreshold(float value) {
  
  overcurrthreshold_ = value;
}
inline void OverCurrent::set_overcurrthreshold(float value) {
  _internal_set_overcurrthreshold(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OverCurrent.OverCurrThreshold)
}

// uint32 OverCurrConfireTime = 2;
inline void OverCurrent::clear_overcurrconfiretime() {
  overcurrconfiretime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OverCurrent::_internal_overcurrconfiretime() const {
  return overcurrconfiretime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OverCurrent::overcurrconfiretime() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OverCurrent.OverCurrConfireTime)
  return _internal_overcurrconfiretime();
}
inline void OverCurrent::_internal_set_overcurrconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  overcurrconfiretime_ = value;
}
inline void OverCurrent::set_overcurrconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_overcurrconfiretime(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OverCurrent.OverCurrConfireTime)
}

// uint32 OverCurrProtDisabled = 3;
inline void OverCurrent::clear_overcurrprotdisabled() {
  overcurrprotdisabled_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OverCurrent::_internal_overcurrprotdisabled() const {
  return overcurrprotdisabled_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OverCurrent::overcurrprotdisabled() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OverCurrent.OverCurrProtDisabled)
  return _internal_overcurrprotdisabled();
}
inline void OverCurrent::_internal_set_overcurrprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  overcurrprotdisabled_ = value;
}
inline void OverCurrent::set_overcurrprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_overcurrprotdisabled(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OverCurrent.OverCurrProtDisabled)
}

// -------------------------------------------------------------------

// CellOverTemp

// float LiFePO4OverTempThreshold = 1;
inline void CellOverTemp::clear_lifepo4overtempthreshold() {
  lifepo4overtempthreshold_ = 0;
}
inline float CellOverTemp::_internal_lifepo4overtempthreshold() const {
  return lifepo4overtempthreshold_;
}
inline float CellOverTemp::lifepo4overtempthreshold() const {
  // @@protoc_insertion_point(field_get:DMCinfo.CellOverTemp.LiFePO4OverTempThreshold)
  return _internal_lifepo4overtempthreshold();
}
inline void CellOverTemp::_internal_set_lifepo4overtempthreshold(float value) {
  
  lifepo4overtempthreshold_ = value;
}
inline void CellOverTemp::set_lifepo4overtempthreshold(float value) {
  _internal_set_lifepo4overtempthreshold(value);
  // @@protoc_insertion_point(field_set:DMCinfo.CellOverTemp.LiFePO4OverTempThreshold)
}

// float LiMnNiCoOverTempThreshold = 2;
inline void CellOverTemp::clear_limnnicoovertempthreshold() {
  limnnicoovertempthreshold_ = 0;
}
inline float CellOverTemp::_internal_limnnicoovertempthreshold() const {
  return limnnicoovertempthreshold_;
}
inline float CellOverTemp::limnnicoovertempthreshold() const {
  // @@protoc_insertion_point(field_get:DMCinfo.CellOverTemp.LiMnNiCoOverTempThreshold)
  return _internal_limnnicoovertempthreshold();
}
inline void CellOverTemp::_internal_set_limnnicoovertempthreshold(float value) {
  
  limnnicoovertempthreshold_ = value;
}
inline void CellOverTemp::set_limnnicoovertempthreshold(float value) {
  _internal_set_limnnicoovertempthreshold(value);
  // @@protoc_insertion_point(field_set:DMCinfo.CellOverTemp.LiMnNiCoOverTempThreshold)
}

// float LiTitanateOverTempThreshold = 3;
inline void CellOverTemp::clear_lititanateovertempthreshold() {
  lititanateovertempthreshold_ = 0;
}
inline float CellOverTemp::_internal_lititanateovertempthreshold() const {
  return lititanateovertempthreshold_;
}
inline float CellOverTemp::lititanateovertempthreshold() const {
  // @@protoc_insertion_point(field_get:DMCinfo.CellOverTemp.LiTitanateOverTempThreshold)
  return _internal_lititanateovertempthreshold();
}
inline void CellOverTemp::_internal_set_lititanateovertempthreshold(float value) {
  
  lititanateovertempthreshold_ = value;
}
inline void CellOverTemp::set_lititanateovertempthreshold(float value) {
  _internal_set_lititanateovertempthreshold(value);
  // @@protoc_insertion_point(field_set:DMCinfo.CellOverTemp.LiTitanateOverTempThreshold)
}

// float LiManganateOverTempThreshold = 4;
inline void CellOverTemp::clear_limanganateovertempthreshold() {
  limanganateovertempthreshold_ = 0;
}
inline float CellOverTemp::_internal_limanganateovertempthreshold() const {
  return limanganateovertempthreshold_;
}
inline float CellOverTemp::limanganateovertempthreshold() const {
  // @@protoc_insertion_point(field_get:DMCinfo.CellOverTemp.LiManganateOverTempThreshold)
  return _internal_limanganateovertempthreshold();
}
inline void CellOverTemp::_internal_set_limanganateovertempthreshold(float value) {
  
  limanganateovertempthreshold_ = value;
}
inline void CellOverTemp::set_limanganateovertempthreshold(float value) {
  _internal_set_limanganateovertempthreshold(value);
  // @@protoc_insertion_point(field_set:DMCinfo.CellOverTemp.LiManganateOverTempThreshold)
}

// uint32 OverTempConfireTime = 5;
inline void CellOverTemp::clear_overtempconfiretime() {
  overtempconfiretime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CellOverTemp::_internal_overtempconfiretime() const {
  return overtempconfiretime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CellOverTemp::overtempconfiretime() const {
  // @@protoc_insertion_point(field_get:DMCinfo.CellOverTemp.OverTempConfireTime)
  return _internal_overtempconfiretime();
}
inline void CellOverTemp::_internal_set_overtempconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  overtempconfiretime_ = value;
}
inline void CellOverTemp::set_overtempconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_overtempconfiretime(value);
  // @@protoc_insertion_point(field_set:DMCinfo.CellOverTemp.OverTempConfireTime)
}

// uint32 OverTempProtDisabled = 6;
inline void CellOverTemp::clear_overtempprotdisabled() {
  overtempprotdisabled_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CellOverTemp::_internal_overtempprotdisabled() const {
  return overtempprotdisabled_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 CellOverTemp::overtempprotdisabled() const {
  // @@protoc_insertion_point(field_get:DMCinfo.CellOverTemp.OverTempProtDisabled)
  return _internal_overtempprotdisabled();
}
inline void CellOverTemp::_internal_set_overtempprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  overtempprotdisabled_ = value;
}
inline void CellOverTemp::set_overtempprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_overtempprotdisabled(value);
  // @@protoc_insertion_point(field_set:DMCinfo.CellOverTemp.OverTempProtDisabled)
}

// -------------------------------------------------------------------

// LowTemp

// float LowTempThreshold = 1;
inline void LowTemp::clear_lowtempthreshold() {
  lowtempthreshold_ = 0;
}
inline float LowTemp::_internal_lowtempthreshold() const {
  return lowtempthreshold_;
}
inline float LowTemp::lowtempthreshold() const {
  // @@protoc_insertion_point(field_get:DMCinfo.LowTemp.LowTempThreshold)
  return _internal_lowtempthreshold();
}
inline void LowTemp::_internal_set_lowtempthreshold(float value) {
  
  lowtempthreshold_ = value;
}
inline void LowTemp::set_lowtempthreshold(float value) {
  _internal_set_lowtempthreshold(value);
  // @@protoc_insertion_point(field_set:DMCinfo.LowTemp.LowTempThreshold)
}

// uint32 LowTempConfireTime = 2;
inline void LowTemp::clear_lowtempconfiretime() {
  lowtempconfiretime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 LowTemp::_internal_lowtempconfiretime() const {
  return lowtempconfiretime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 LowTemp::lowtempconfiretime() const {
  // @@protoc_insertion_point(field_get:DMCinfo.LowTemp.LowTempConfireTime)
  return _internal_lowtempconfiretime();
}
inline void LowTemp::_internal_set_lowtempconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  lowtempconfiretime_ = value;
}
inline void LowTemp::set_lowtempconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_lowtempconfiretime(value);
  // @@protoc_insertion_point(field_set:DMCinfo.LowTemp.LowTempConfireTime)
}

// uint32 LowTempProtDisabled = 3;
inline void LowTemp::clear_lowtempprotdisabled() {
  lowtempprotdisabled_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 LowTemp::_internal_lowtempprotdisabled() const {
  return lowtempprotdisabled_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 LowTemp::lowtempprotdisabled() const {
  // @@protoc_insertion_point(field_get:DMCinfo.LowTemp.LowTempProtDisabled)
  return _internal_lowtempprotdisabled();
}
inline void LowTemp::_internal_set_lowtempprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  lowtempprotdisabled_ = value;
}
inline void LowTemp::set_lowtempprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_lowtempprotdisabled(value);
  // @@protoc_insertion_point(field_set:DMCinfo.LowTemp.LowTempProtDisabled)
}

// -------------------------------------------------------------------

// BMSRlyStick

// float BMSRlyStickVoltThreshold = 1;
inline void BMSRlyStick::clear_bmsrlystickvoltthreshold() {
  bmsrlystickvoltthreshold_ = 0;
}
inline float BMSRlyStick::_internal_bmsrlystickvoltthreshold() const {
  return bmsrlystickvoltthreshold_;
}
inline float BMSRlyStick::bmsrlystickvoltthreshold() const {
  // @@protoc_insertion_point(field_get:DMCinfo.BMSRlyStick.BMSRlyStickVoltThreshold)
  return _internal_bmsrlystickvoltthreshold();
}
inline void BMSRlyStick::_internal_set_bmsrlystickvoltthreshold(float value) {
  
  bmsrlystickvoltthreshold_ = value;
}
inline void BMSRlyStick::set_bmsrlystickvoltthreshold(float value) {
  _internal_set_bmsrlystickvoltthreshold(value);
  // @@protoc_insertion_point(field_set:DMCinfo.BMSRlyStick.BMSRlyStickVoltThreshold)
}

// uint32 BMSRlyStickConfireTime = 2;
inline void BMSRlyStick::clear_bmsrlystickconfiretime() {
  bmsrlystickconfiretime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSRlyStick::_internal_bmsrlystickconfiretime() const {
  return bmsrlystickconfiretime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSRlyStick::bmsrlystickconfiretime() const {
  // @@protoc_insertion_point(field_get:DMCinfo.BMSRlyStick.BMSRlyStickConfireTime)
  return _internal_bmsrlystickconfiretime();
}
inline void BMSRlyStick::_internal_set_bmsrlystickconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsrlystickconfiretime_ = value;
}
inline void BMSRlyStick::set_bmsrlystickconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsrlystickconfiretime(value);
  // @@protoc_insertion_point(field_set:DMCinfo.BMSRlyStick.BMSRlyStickConfireTime)
}

// uint32 BMSRlyStickDisabled = 3;
inline void BMSRlyStick::clear_bmsrlystickdisabled() {
  bmsrlystickdisabled_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSRlyStick::_internal_bmsrlystickdisabled() const {
  return bmsrlystickdisabled_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSRlyStick::bmsrlystickdisabled() const {
  // @@protoc_insertion_point(field_get:DMCinfo.BMSRlyStick.BMSRlyStickDisabled)
  return _internal_bmsrlystickdisabled();
}
inline void BMSRlyStick::_internal_set_bmsrlystickdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsrlystickdisabled_ = value;
}
inline void BMSRlyStick::set_bmsrlystickdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsrlystickdisabled(value);
  // @@protoc_insertion_point(field_set:DMCinfo.BMSRlyStick.BMSRlyStickDisabled)
}

// -------------------------------------------------------------------

// BMSRlyOC

// float BMSRlyOCVoltThreshold = 1;
inline void BMSRlyOC::clear_bmsrlyocvoltthreshold() {
  bmsrlyocvoltthreshold_ = 0;
}
inline float BMSRlyOC::_internal_bmsrlyocvoltthreshold() const {
  return bmsrlyocvoltthreshold_;
}
inline float BMSRlyOC::bmsrlyocvoltthreshold() const {
  // @@protoc_insertion_point(field_get:DMCinfo.BMSRlyOC.BMSRlyOCVoltThreshold)
  return _internal_bmsrlyocvoltthreshold();
}
inline void BMSRlyOC::_internal_set_bmsrlyocvoltthreshold(float value) {
  
  bmsrlyocvoltthreshold_ = value;
}
inline void BMSRlyOC::set_bmsrlyocvoltthreshold(float value) {
  _internal_set_bmsrlyocvoltthreshold(value);
  // @@protoc_insertion_point(field_set:DMCinfo.BMSRlyOC.BMSRlyOCVoltThreshold)
}

// float BMSRlyOCCurrentThreshold = 2;
inline void BMSRlyOC::clear_bmsrlyoccurrentthreshold() {
  bmsrlyoccurrentthreshold_ = 0;
}
inline float BMSRlyOC::_internal_bmsrlyoccurrentthreshold() const {
  return bmsrlyoccurrentthreshold_;
}
inline float BMSRlyOC::bmsrlyoccurrentthreshold() const {
  // @@protoc_insertion_point(field_get:DMCinfo.BMSRlyOC.BMSRlyOCCurrentThreshold)
  return _internal_bmsrlyoccurrentthreshold();
}
inline void BMSRlyOC::_internal_set_bmsrlyoccurrentthreshold(float value) {
  
  bmsrlyoccurrentthreshold_ = value;
}
inline void BMSRlyOC::set_bmsrlyoccurrentthreshold(float value) {
  _internal_set_bmsrlyoccurrentthreshold(value);
  // @@protoc_insertion_point(field_set:DMCinfo.BMSRlyOC.BMSRlyOCCurrentThreshold)
}

// uint32 BMSRlyOCConfireTime = 3;
inline void BMSRlyOC::clear_bmsrlyocconfiretime() {
  bmsrlyocconfiretime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSRlyOC::_internal_bmsrlyocconfiretime() const {
  return bmsrlyocconfiretime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSRlyOC::bmsrlyocconfiretime() const {
  // @@protoc_insertion_point(field_get:DMCinfo.BMSRlyOC.BMSRlyOCConfireTime)
  return _internal_bmsrlyocconfiretime();
}
inline void BMSRlyOC::_internal_set_bmsrlyocconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsrlyocconfiretime_ = value;
}
inline void BMSRlyOC::set_bmsrlyocconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsrlyocconfiretime(value);
  // @@protoc_insertion_point(field_set:DMCinfo.BMSRlyOC.BMSRlyOCConfireTime)
}

// uint32 BMSRlyOCDisabled = 4;
inline void BMSRlyOC::clear_bmsrlyocdisabled() {
  bmsrlyocdisabled_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSRlyOC::_internal_bmsrlyocdisabled() const {
  return bmsrlyocdisabled_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSRlyOC::bmsrlyocdisabled() const {
  // @@protoc_insertion_point(field_get:DMCinfo.BMSRlyOC.BMSRlyOCDisabled)
  return _internal_bmsrlyocdisabled();
}
inline void BMSRlyOC::_internal_set_bmsrlyocdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsrlyocdisabled_ = value;
}
inline void BMSRlyOC::set_bmsrlyocdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsrlyocdisabled(value);
  // @@protoc_insertion_point(field_set:DMCinfo.BMSRlyOC.BMSRlyOCDisabled)
}

// -------------------------------------------------------------------

// OverCharge

// float OverChgCoef = 1;
inline void OverCharge::clear_overchgcoef() {
  overchgcoef_ = 0;
}
inline float OverCharge::_internal_overchgcoef() const {
  return overchgcoef_;
}
inline float OverCharge::overchgcoef() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OverCharge.OverChgCoef)
  return _internal_overchgcoef();
}
inline void OverCharge::_internal_set_overchgcoef(float value) {
  
  overchgcoef_ = value;
}
inline void OverCharge::set_overchgcoef(float value) {
  _internal_set_overchgcoef(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OverCharge.OverChgCoef)
}

// float OverChgAH = 2;
inline void OverCharge::clear_overchgah() {
  overchgah_ = 0;
}
inline float OverCharge::_internal_overchgah() const {
  return overchgah_;
}
inline float OverCharge::overchgah() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OverCharge.OverChgAH)
  return _internal_overchgah();
}
inline void OverCharge::_internal_set_overchgah(float value) {
  
  overchgah_ = value;
}
inline void OverCharge::set_overchgah(float value) {
  _internal_set_overchgah(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OverCharge.OverChgAH)
}

// uint32 OverChgConfireTime = 3;
inline void OverCharge::clear_overchgconfiretime() {
  overchgconfiretime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OverCharge::_internal_overchgconfiretime() const {
  return overchgconfiretime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OverCharge::overchgconfiretime() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OverCharge.OverChgConfireTime)
  return _internal_overchgconfiretime();
}
inline void OverCharge::_internal_set_overchgconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  overchgconfiretime_ = value;
}
inline void OverCharge::set_overchgconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_overchgconfiretime(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OverCharge.OverChgConfireTime)
}

// uint32 OverChgProtDisabled = 4;
inline void OverCharge::clear_overchgprotdisabled() {
  overchgprotdisabled_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OverCharge::_internal_overchgprotdisabled() const {
  return overchgprotdisabled_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 OverCharge::overchgprotdisabled() const {
  // @@protoc_insertion_point(field_get:DMCinfo.OverCharge.OverChgProtDisabled)
  return _internal_overchgprotdisabled();
}
inline void OverCharge::_internal_set_overchgprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  overchgprotdisabled_ = value;
}
inline void OverCharge::set_overchgprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_overchgprotdisabled(value);
  // @@protoc_insertion_point(field_set:DMCinfo.OverCharge.OverChgProtDisabled)
}

// -------------------------------------------------------------------

// BMSDataRepeat

// uint32 BMSDataRepeatConfireTime = 1;
inline void BMSDataRepeat::clear_bmsdatarepeatconfiretime() {
  bmsdatarepeatconfiretime_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSDataRepeat::_internal_bmsdatarepeatconfiretime() const {
  return bmsdatarepeatconfiretime_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSDataRepeat::bmsdatarepeatconfiretime() const {
  // @@protoc_insertion_point(field_get:DMCinfo.BMSDataRepeat.BMSDataRepeatConfireTime)
  return _internal_bmsdatarepeatconfiretime();
}
inline void BMSDataRepeat::_internal_set_bmsdatarepeatconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsdatarepeatconfiretime_ = value;
}
inline void BMSDataRepeat::set_bmsdatarepeatconfiretime(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsdatarepeatconfiretime(value);
  // @@protoc_insertion_point(field_set:DMCinfo.BMSDataRepeat.BMSDataRepeatConfireTime)
}

// uint32 BMBMSDataErrorProtDisabled = 2;
inline void BMSDataRepeat::clear_bmbmsdataerrorprotdisabled() {
  bmbmsdataerrorprotdisabled_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSDataRepeat::_internal_bmbmsdataerrorprotdisabled() const {
  return bmbmsdataerrorprotdisabled_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSDataRepeat::bmbmsdataerrorprotdisabled() const {
  // @@protoc_insertion_point(field_get:DMCinfo.BMSDataRepeat.BMBMSDataErrorProtDisabled)
  return _internal_bmbmsdataerrorprotdisabled();
}
inline void BMSDataRepeat::_internal_set_bmbmsdataerrorprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmbmsdataerrorprotdisabled_ = value;
}
inline void BMSDataRepeat::set_bmbmsdataerrorprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmbmsdataerrorprotdisabled(value);
  // @@protoc_insertion_point(field_set:DMCinfo.BMSDataRepeat.BMBMSDataErrorProtDisabled)
}

// uint32 BMSDataErrorProtDisabled = 3;
inline void BMSDataRepeat::clear_bmsdataerrorprotdisabled() {
  bmsdataerrorprotdisabled_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSDataRepeat::_internal_bmsdataerrorprotdisabled() const {
  return bmsdataerrorprotdisabled_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 BMSDataRepeat::bmsdataerrorprotdisabled() const {
  // @@protoc_insertion_point(field_get:DMCinfo.BMSDataRepeat.BMSDataErrorProtDisabled)
  return _internal_bmsdataerrorprotdisabled();
}
inline void BMSDataRepeat::_internal_set_bmsdataerrorprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bmsdataerrorprotdisabled_ = value;
}
inline void BMSDataRepeat::set_bmsdataerrorprotdisabled(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_bmsdataerrorprotdisabled(value);
  // @@protoc_insertion_point(field_set:DMCinfo.BMSDataRepeat.BMSDataErrorProtDisabled)
}

// -------------------------------------------------------------------

// ActiveProtectParam

// .DMCinfo.CurrentBalance currBalance = 1;
inline bool ActiveProtectParam::_internal_has_currbalance() const {
  return this != internal_default_instance() && currbalance_ != nullptr;
}
inline bool ActiveProtectParam::has_currbalance() const {
  return _internal_has_currbalance();
}
inline void ActiveProtectParam::clear_currbalance() {
  if (GetArenaForAllocation() == nullptr && currbalance_ != nullptr) {
    delete currbalance_;
  }
  currbalance_ = nullptr;
}
inline const ::DMCinfo::CurrentBalance& ActiveProtectParam::_internal_currbalance() const {
  const ::DMCinfo::CurrentBalance* p = currbalance_;
  return p != nullptr ? *p : reinterpret_cast<const ::DMCinfo::CurrentBalance&>(
      ::DMCinfo::_CurrentBalance_default_instance_);
}
inline const ::DMCinfo::CurrentBalance& ActiveProtectParam::currbalance() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ActiveProtectParam.currBalance)
  return _internal_currbalance();
}
inline void ActiveProtectParam::unsafe_arena_set_allocated_currbalance(
    ::DMCinfo::CurrentBalance* currbalance) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(currbalance_);
  }
  currbalance_ = currbalance;
  if (currbalance) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:DMCinfo.ActiveProtectParam.currBalance)
}
inline ::DMCinfo::CurrentBalance* ActiveProtectParam::release_currbalance() {
  
  ::DMCinfo::CurrentBalance* temp = currbalance_;
  currbalance_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::DMCinfo::CurrentBalance* ActiveProtectParam::unsafe_arena_release_currbalance() {
  // @@protoc_insertion_point(field_release:DMCinfo.ActiveProtectParam.currBalance)
  
  ::DMCinfo::CurrentBalance* temp = currbalance_;
  currbalance_ = nullptr;
  return temp;
}
inline ::DMCinfo::CurrentBalance* ActiveProtectParam::_internal_mutable_currbalance() {
  
  if (currbalance_ == nullptr) {
    auto* p = CreateMaybeMessage<::DMCinfo::CurrentBalance>(GetArenaForAllocation());
    currbalance_ = p;
  }
  return currbalance_;
}
inline ::DMCinfo::CurrentBalance* ActiveProtectParam::mutable_currbalance() {
  ::DMCinfo::CurrentBalance* _msg = _internal_mutable_currbalance();
  // @@protoc_insertion_point(field_mutable:DMCinfo.ActiveProtectParam.currBalance)
  return _msg;
}
inline void ActiveProtectParam::set_allocated_currbalance(::DMCinfo::CurrentBalance* currbalance) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete currbalance_;
  }
  if (currbalance) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::DMCinfo::CurrentBalance>::GetOwningArena(currbalance);
    if (message_arena != submessage_arena) {
      currbalance = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, currbalance, submessage_arena);
    }
    
  } else {
    
  }
  currbalance_ = currbalance;
  // @@protoc_insertion_point(field_set_allocated:DMCinfo.ActiveProtectParam.currBalance)
}

// .DMCinfo.HotRunaway hotRunaway = 2;
inline bool ActiveProtectParam::_internal_has_hotrunaway() const {
  return this != internal_default_instance() && hotrunaway_ != nullptr;
}
inline bool ActiveProtectParam::has_hotrunaway() const {
  return _internal_has_hotrunaway();
}
inline void ActiveProtectParam::clear_hotrunaway() {
  if (GetArenaForAllocation() == nullptr && hotrunaway_ != nullptr) {
    delete hotrunaway_;
  }
  hotrunaway_ = nullptr;
}
inline const ::DMCinfo::HotRunaway& ActiveProtectParam::_internal_hotrunaway() const {
  const ::DMCinfo::HotRunaway* p = hotrunaway_;
  return p != nullptr ? *p : reinterpret_cast<const ::DMCinfo::HotRunaway&>(
      ::DMCinfo::_HotRunaway_default_instance_);
}
inline const ::DMCinfo::HotRunaway& ActiveProtectParam::hotrunaway() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ActiveProtectParam.hotRunaway)
  return _internal_hotrunaway();
}
inline void ActiveProtectParam::unsafe_arena_set_allocated_hotrunaway(
    ::DMCinfo::HotRunaway* hotrunaway) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(hotrunaway_);
  }
  hotrunaway_ = hotrunaway;
  if (hotrunaway) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:DMCinfo.ActiveProtectParam.hotRunaway)
}
inline ::DMCinfo::HotRunaway* ActiveProtectParam::release_hotrunaway() {
  
  ::DMCinfo::HotRunaway* temp = hotrunaway_;
  hotrunaway_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::DMCinfo::HotRunaway* ActiveProtectParam::unsafe_arena_release_hotrunaway() {
  // @@protoc_insertion_point(field_release:DMCinfo.ActiveProtectParam.hotRunaway)
  
  ::DMCinfo::HotRunaway* temp = hotrunaway_;
  hotrunaway_ = nullptr;
  return temp;
}
inline ::DMCinfo::HotRunaway* ActiveProtectParam::_internal_mutable_hotrunaway() {
  
  if (hotrunaway_ == nullptr) {
    auto* p = CreateMaybeMessage<::DMCinfo::HotRunaway>(GetArenaForAllocation());
    hotrunaway_ = p;
  }
  return hotrunaway_;
}
inline ::DMCinfo::HotRunaway* ActiveProtectParam::mutable_hotrunaway() {
  ::DMCinfo::HotRunaway* _msg = _internal_mutable_hotrunaway();
  // @@protoc_insertion_point(field_mutable:DMCinfo.ActiveProtectParam.hotRunaway)
  return _msg;
}
inline void ActiveProtectParam::set_allocated_hotrunaway(::DMCinfo::HotRunaway* hotrunaway) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete hotrunaway_;
  }
  if (hotrunaway) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::DMCinfo::HotRunaway>::GetOwningArena(hotrunaway);
    if (message_arena != submessage_arena) {
      hotrunaway = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, hotrunaway, submessage_arena);
    }
    
  } else {
    
  }
  hotrunaway_ = hotrunaway;
  // @@protoc_insertion_point(field_set_allocated:DMCinfo.ActiveProtectParam.hotRunaway)
}

// .DMCinfo.CellOverVolt cellOverVolt = 3;
inline bool ActiveProtectParam::_internal_has_cellovervolt() const {
  return this != internal_default_instance() && cellovervolt_ != nullptr;
}
inline bool ActiveProtectParam::has_cellovervolt() const {
  return _internal_has_cellovervolt();
}
inline void ActiveProtectParam::clear_cellovervolt() {
  if (GetArenaForAllocation() == nullptr && cellovervolt_ != nullptr) {
    delete cellovervolt_;
  }
  cellovervolt_ = nullptr;
}
inline const ::DMCinfo::CellOverVolt& ActiveProtectParam::_internal_cellovervolt() const {
  const ::DMCinfo::CellOverVolt* p = cellovervolt_;
  return p != nullptr ? *p : reinterpret_cast<const ::DMCinfo::CellOverVolt&>(
      ::DMCinfo::_CellOverVolt_default_instance_);
}
inline const ::DMCinfo::CellOverVolt& ActiveProtectParam::cellovervolt() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ActiveProtectParam.cellOverVolt)
  return _internal_cellovervolt();
}
inline void ActiveProtectParam::unsafe_arena_set_allocated_cellovervolt(
    ::DMCinfo::CellOverVolt* cellovervolt) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(cellovervolt_);
  }
  cellovervolt_ = cellovervolt;
  if (cellovervolt) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:DMCinfo.ActiveProtectParam.cellOverVolt)
}
inline ::DMCinfo::CellOverVolt* ActiveProtectParam::release_cellovervolt() {
  
  ::DMCinfo::CellOverVolt* temp = cellovervolt_;
  cellovervolt_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::DMCinfo::CellOverVolt* ActiveProtectParam::unsafe_arena_release_cellovervolt() {
  // @@protoc_insertion_point(field_release:DMCinfo.ActiveProtectParam.cellOverVolt)
  
  ::DMCinfo::CellOverVolt* temp = cellovervolt_;
  cellovervolt_ = nullptr;
  return temp;
}
inline ::DMCinfo::CellOverVolt* ActiveProtectParam::_internal_mutable_cellovervolt() {
  
  if (cellovervolt_ == nullptr) {
    auto* p = CreateMaybeMessage<::DMCinfo::CellOverVolt>(GetArenaForAllocation());
    cellovervolt_ = p;
  }
  return cellovervolt_;
}
inline ::DMCinfo::CellOverVolt* ActiveProtectParam::mutable_cellovervolt() {
  ::DMCinfo::CellOverVolt* _msg = _internal_mutable_cellovervolt();
  // @@protoc_insertion_point(field_mutable:DMCinfo.ActiveProtectParam.cellOverVolt)
  return _msg;
}
inline void ActiveProtectParam::set_allocated_cellovervolt(::DMCinfo::CellOverVolt* cellovervolt) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete cellovervolt_;
  }
  if (cellovervolt) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::DMCinfo::CellOverVolt>::GetOwningArena(cellovervolt);
    if (message_arena != submessage_arena) {
      cellovervolt = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, cellovervolt, submessage_arena);
    }
    
  } else {
    
  }
  cellovervolt_ = cellovervolt;
  // @@protoc_insertion_point(field_set_allocated:DMCinfo.ActiveProtectParam.cellOverVolt)
}

// .DMCinfo.PackOverVolt packOverVolt = 4;
inline bool ActiveProtectParam::_internal_has_packovervolt() const {
  return this != internal_default_instance() && packovervolt_ != nullptr;
}
inline bool ActiveProtectParam::has_packovervolt() const {
  return _internal_has_packovervolt();
}
inline void ActiveProtectParam::clear_packovervolt() {
  if (GetArenaForAllocation() == nullptr && packovervolt_ != nullptr) {
    delete packovervolt_;
  }
  packovervolt_ = nullptr;
}
inline const ::DMCinfo::PackOverVolt& ActiveProtectParam::_internal_packovervolt() const {
  const ::DMCinfo::PackOverVolt* p = packovervolt_;
  return p != nullptr ? *p : reinterpret_cast<const ::DMCinfo::PackOverVolt&>(
      ::DMCinfo::_PackOverVolt_default_instance_);
}
inline const ::DMCinfo::PackOverVolt& ActiveProtectParam::packovervolt() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ActiveProtectParam.packOverVolt)
  return _internal_packovervolt();
}
inline void ActiveProtectParam::unsafe_arena_set_allocated_packovervolt(
    ::DMCinfo::PackOverVolt* packovervolt) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(packovervolt_);
  }
  packovervolt_ = packovervolt;
  if (packovervolt) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:DMCinfo.ActiveProtectParam.packOverVolt)
}
inline ::DMCinfo::PackOverVolt* ActiveProtectParam::release_packovervolt() {
  
  ::DMCinfo::PackOverVolt* temp = packovervolt_;
  packovervolt_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::DMCinfo::PackOverVolt* ActiveProtectParam::unsafe_arena_release_packovervolt() {
  // @@protoc_insertion_point(field_release:DMCinfo.ActiveProtectParam.packOverVolt)
  
  ::DMCinfo::PackOverVolt* temp = packovervolt_;
  packovervolt_ = nullptr;
  return temp;
}
inline ::DMCinfo::PackOverVolt* ActiveProtectParam::_internal_mutable_packovervolt() {
  
  if (packovervolt_ == nullptr) {
    auto* p = CreateMaybeMessage<::DMCinfo::PackOverVolt>(GetArenaForAllocation());
    packovervolt_ = p;
  }
  return packovervolt_;
}
inline ::DMCinfo::PackOverVolt* ActiveProtectParam::mutable_packovervolt() {
  ::DMCinfo::PackOverVolt* _msg = _internal_mutable_packovervolt();
  // @@protoc_insertion_point(field_mutable:DMCinfo.ActiveProtectParam.packOverVolt)
  return _msg;
}
inline void ActiveProtectParam::set_allocated_packovervolt(::DMCinfo::PackOverVolt* packovervolt) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete packovervolt_;
  }
  if (packovervolt) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::DMCinfo::PackOverVolt>::GetOwningArena(packovervolt);
    if (message_arena != submessage_arena) {
      packovervolt = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, packovervolt, submessage_arena);
    }
    
  } else {
    
  }
  packovervolt_ = packovervolt;
  // @@protoc_insertion_point(field_set_allocated:DMCinfo.ActiveProtectParam.packOverVolt)
}

// .DMCinfo.OverCurrent overCurrent = 5;
inline bool ActiveProtectParam::_internal_has_overcurrent() const {
  return this != internal_default_instance() && overcurrent_ != nullptr;
}
inline bool ActiveProtectParam::has_overcurrent() const {
  return _internal_has_overcurrent();
}
inline void ActiveProtectParam::clear_overcurrent() {
  if (GetArenaForAllocation() == nullptr && overcurrent_ != nullptr) {
    delete overcurrent_;
  }
  overcurrent_ = nullptr;
}
inline const ::DMCinfo::OverCurrent& ActiveProtectParam::_internal_overcurrent() const {
  const ::DMCinfo::OverCurrent* p = overcurrent_;
  return p != nullptr ? *p : reinterpret_cast<const ::DMCinfo::OverCurrent&>(
      ::DMCinfo::_OverCurrent_default_instance_);
}
inline const ::DMCinfo::OverCurrent& ActiveProtectParam::overcurrent() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ActiveProtectParam.overCurrent)
  return _internal_overcurrent();
}
inline void ActiveProtectParam::unsafe_arena_set_allocated_overcurrent(
    ::DMCinfo::OverCurrent* overcurrent) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(overcurrent_);
  }
  overcurrent_ = overcurrent;
  if (overcurrent) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:DMCinfo.ActiveProtectParam.overCurrent)
}
inline ::DMCinfo::OverCurrent* ActiveProtectParam::release_overcurrent() {
  
  ::DMCinfo::OverCurrent* temp = overcurrent_;
  overcurrent_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::DMCinfo::OverCurrent* ActiveProtectParam::unsafe_arena_release_overcurrent() {
  // @@protoc_insertion_point(field_release:DMCinfo.ActiveProtectParam.overCurrent)
  
  ::DMCinfo::OverCurrent* temp = overcurrent_;
  overcurrent_ = nullptr;
  return temp;
}
inline ::DMCinfo::OverCurrent* ActiveProtectParam::_internal_mutable_overcurrent() {
  
  if (overcurrent_ == nullptr) {
    auto* p = CreateMaybeMessage<::DMCinfo::OverCurrent>(GetArenaForAllocation());
    overcurrent_ = p;
  }
  return overcurrent_;
}
inline ::DMCinfo::OverCurrent* ActiveProtectParam::mutable_overcurrent() {
  ::DMCinfo::OverCurrent* _msg = _internal_mutable_overcurrent();
  // @@protoc_insertion_point(field_mutable:DMCinfo.ActiveProtectParam.overCurrent)
  return _msg;
}
inline void ActiveProtectParam::set_allocated_overcurrent(::DMCinfo::OverCurrent* overcurrent) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete overcurrent_;
  }
  if (overcurrent) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::DMCinfo::OverCurrent>::GetOwningArena(overcurrent);
    if (message_arena != submessage_arena) {
      overcurrent = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, overcurrent, submessage_arena);
    }
    
  } else {
    
  }
  overcurrent_ = overcurrent;
  // @@protoc_insertion_point(field_set_allocated:DMCinfo.ActiveProtectParam.overCurrent)
}

// .DMCinfo.CellOverTemp cellOverTemp = 6;
inline bool ActiveProtectParam::_internal_has_cellovertemp() const {
  return this != internal_default_instance() && cellovertemp_ != nullptr;
}
inline bool ActiveProtectParam::has_cellovertemp() const {
  return _internal_has_cellovertemp();
}
inline void ActiveProtectParam::clear_cellovertemp() {
  if (GetArenaForAllocation() == nullptr && cellovertemp_ != nullptr) {
    delete cellovertemp_;
  }
  cellovertemp_ = nullptr;
}
inline const ::DMCinfo::CellOverTemp& ActiveProtectParam::_internal_cellovertemp() const {
  const ::DMCinfo::CellOverTemp* p = cellovertemp_;
  return p != nullptr ? *p : reinterpret_cast<const ::DMCinfo::CellOverTemp&>(
      ::DMCinfo::_CellOverTemp_default_instance_);
}
inline const ::DMCinfo::CellOverTemp& ActiveProtectParam::cellovertemp() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ActiveProtectParam.cellOverTemp)
  return _internal_cellovertemp();
}
inline void ActiveProtectParam::unsafe_arena_set_allocated_cellovertemp(
    ::DMCinfo::CellOverTemp* cellovertemp) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(cellovertemp_);
  }
  cellovertemp_ = cellovertemp;
  if (cellovertemp) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:DMCinfo.ActiveProtectParam.cellOverTemp)
}
inline ::DMCinfo::CellOverTemp* ActiveProtectParam::release_cellovertemp() {
  
  ::DMCinfo::CellOverTemp* temp = cellovertemp_;
  cellovertemp_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::DMCinfo::CellOverTemp* ActiveProtectParam::unsafe_arena_release_cellovertemp() {
  // @@protoc_insertion_point(field_release:DMCinfo.ActiveProtectParam.cellOverTemp)
  
  ::DMCinfo::CellOverTemp* temp = cellovertemp_;
  cellovertemp_ = nullptr;
  return temp;
}
inline ::DMCinfo::CellOverTemp* ActiveProtectParam::_internal_mutable_cellovertemp() {
  
  if (cellovertemp_ == nullptr) {
    auto* p = CreateMaybeMessage<::DMCinfo::CellOverTemp>(GetArenaForAllocation());
    cellovertemp_ = p;
  }
  return cellovertemp_;
}
inline ::DMCinfo::CellOverTemp* ActiveProtectParam::mutable_cellovertemp() {
  ::DMCinfo::CellOverTemp* _msg = _internal_mutable_cellovertemp();
  // @@protoc_insertion_point(field_mutable:DMCinfo.ActiveProtectParam.cellOverTemp)
  return _msg;
}
inline void ActiveProtectParam::set_allocated_cellovertemp(::DMCinfo::CellOverTemp* cellovertemp) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete cellovertemp_;
  }
  if (cellovertemp) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::DMCinfo::CellOverTemp>::GetOwningArena(cellovertemp);
    if (message_arena != submessage_arena) {
      cellovertemp = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, cellovertemp, submessage_arena);
    }
    
  } else {
    
  }
  cellovertemp_ = cellovertemp;
  // @@protoc_insertion_point(field_set_allocated:DMCinfo.ActiveProtectParam.cellOverTemp)
}

// .DMCinfo.LowTemp lowTemp = 7;
inline bool ActiveProtectParam::_internal_has_lowtemp() const {
  return this != internal_default_instance() && lowtemp_ != nullptr;
}
inline bool ActiveProtectParam::has_lowtemp() const {
  return _internal_has_lowtemp();
}
inline void ActiveProtectParam::clear_lowtemp() {
  if (GetArenaForAllocation() == nullptr && lowtemp_ != nullptr) {
    delete lowtemp_;
  }
  lowtemp_ = nullptr;
}
inline const ::DMCinfo::LowTemp& ActiveProtectParam::_internal_lowtemp() const {
  const ::DMCinfo::LowTemp* p = lowtemp_;
  return p != nullptr ? *p : reinterpret_cast<const ::DMCinfo::LowTemp&>(
      ::DMCinfo::_LowTemp_default_instance_);
}
inline const ::DMCinfo::LowTemp& ActiveProtectParam::lowtemp() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ActiveProtectParam.lowTemp)
  return _internal_lowtemp();
}
inline void ActiveProtectParam::unsafe_arena_set_allocated_lowtemp(
    ::DMCinfo::LowTemp* lowtemp) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(lowtemp_);
  }
  lowtemp_ = lowtemp;
  if (lowtemp) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:DMCinfo.ActiveProtectParam.lowTemp)
}
inline ::DMCinfo::LowTemp* ActiveProtectParam::release_lowtemp() {
  
  ::DMCinfo::LowTemp* temp = lowtemp_;
  lowtemp_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::DMCinfo::LowTemp* ActiveProtectParam::unsafe_arena_release_lowtemp() {
  // @@protoc_insertion_point(field_release:DMCinfo.ActiveProtectParam.lowTemp)
  
  ::DMCinfo::LowTemp* temp = lowtemp_;
  lowtemp_ = nullptr;
  return temp;
}
inline ::DMCinfo::LowTemp* ActiveProtectParam::_internal_mutable_lowtemp() {
  
  if (lowtemp_ == nullptr) {
    auto* p = CreateMaybeMessage<::DMCinfo::LowTemp>(GetArenaForAllocation());
    lowtemp_ = p;
  }
  return lowtemp_;
}
inline ::DMCinfo::LowTemp* ActiveProtectParam::mutable_lowtemp() {
  ::DMCinfo::LowTemp* _msg = _internal_mutable_lowtemp();
  // @@protoc_insertion_point(field_mutable:DMCinfo.ActiveProtectParam.lowTemp)
  return _msg;
}
inline void ActiveProtectParam::set_allocated_lowtemp(::DMCinfo::LowTemp* lowtemp) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete lowtemp_;
  }
  if (lowtemp) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::DMCinfo::LowTemp>::GetOwningArena(lowtemp);
    if (message_arena != submessage_arena) {
      lowtemp = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, lowtemp, submessage_arena);
    }
    
  } else {
    
  }
  lowtemp_ = lowtemp;
  // @@protoc_insertion_point(field_set_allocated:DMCinfo.ActiveProtectParam.lowTemp)
}

// .DMCinfo.BMSRlyStick bmsRlyStick = 8;
inline bool ActiveProtectParam::_internal_has_bmsrlystick() const {
  return this != internal_default_instance() && bmsrlystick_ != nullptr;
}
inline bool ActiveProtectParam::has_bmsrlystick() const {
  return _internal_has_bmsrlystick();
}
inline void ActiveProtectParam::clear_bmsrlystick() {
  if (GetArenaForAllocation() == nullptr && bmsrlystick_ != nullptr) {
    delete bmsrlystick_;
  }
  bmsrlystick_ = nullptr;
}
inline const ::DMCinfo::BMSRlyStick& ActiveProtectParam::_internal_bmsrlystick() const {
  const ::DMCinfo::BMSRlyStick* p = bmsrlystick_;
  return p != nullptr ? *p : reinterpret_cast<const ::DMCinfo::BMSRlyStick&>(
      ::DMCinfo::_BMSRlyStick_default_instance_);
}
inline const ::DMCinfo::BMSRlyStick& ActiveProtectParam::bmsrlystick() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ActiveProtectParam.bmsRlyStick)
  return _internal_bmsrlystick();
}
inline void ActiveProtectParam::unsafe_arena_set_allocated_bmsrlystick(
    ::DMCinfo::BMSRlyStick* bmsrlystick) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmsrlystick_);
  }
  bmsrlystick_ = bmsrlystick;
  if (bmsrlystick) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:DMCinfo.ActiveProtectParam.bmsRlyStick)
}
inline ::DMCinfo::BMSRlyStick* ActiveProtectParam::release_bmsrlystick() {
  
  ::DMCinfo::BMSRlyStick* temp = bmsrlystick_;
  bmsrlystick_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::DMCinfo::BMSRlyStick* ActiveProtectParam::unsafe_arena_release_bmsrlystick() {
  // @@protoc_insertion_point(field_release:DMCinfo.ActiveProtectParam.bmsRlyStick)
  
  ::DMCinfo::BMSRlyStick* temp = bmsrlystick_;
  bmsrlystick_ = nullptr;
  return temp;
}
inline ::DMCinfo::BMSRlyStick* ActiveProtectParam::_internal_mutable_bmsrlystick() {
  
  if (bmsrlystick_ == nullptr) {
    auto* p = CreateMaybeMessage<::DMCinfo::BMSRlyStick>(GetArenaForAllocation());
    bmsrlystick_ = p;
  }
  return bmsrlystick_;
}
inline ::DMCinfo::BMSRlyStick* ActiveProtectParam::mutable_bmsrlystick() {
  ::DMCinfo::BMSRlyStick* _msg = _internal_mutable_bmsrlystick();
  // @@protoc_insertion_point(field_mutable:DMCinfo.ActiveProtectParam.bmsRlyStick)
  return _msg;
}
inline void ActiveProtectParam::set_allocated_bmsrlystick(::DMCinfo::BMSRlyStick* bmsrlystick) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete bmsrlystick_;
  }
  if (bmsrlystick) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::DMCinfo::BMSRlyStick>::GetOwningArena(bmsrlystick);
    if (message_arena != submessage_arena) {
      bmsrlystick = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bmsrlystick, submessage_arena);
    }
    
  } else {
    
  }
  bmsrlystick_ = bmsrlystick;
  // @@protoc_insertion_point(field_set_allocated:DMCinfo.ActiveProtectParam.bmsRlyStick)
}

// .DMCinfo.BMSRlyOC bmsRlyOC = 9;
inline bool ActiveProtectParam::_internal_has_bmsrlyoc() const {
  return this != internal_default_instance() && bmsrlyoc_ != nullptr;
}
inline bool ActiveProtectParam::has_bmsrlyoc() const {
  return _internal_has_bmsrlyoc();
}
inline void ActiveProtectParam::clear_bmsrlyoc() {
  if (GetArenaForAllocation() == nullptr && bmsrlyoc_ != nullptr) {
    delete bmsrlyoc_;
  }
  bmsrlyoc_ = nullptr;
}
inline const ::DMCinfo::BMSRlyOC& ActiveProtectParam::_internal_bmsrlyoc() const {
  const ::DMCinfo::BMSRlyOC* p = bmsrlyoc_;
  return p != nullptr ? *p : reinterpret_cast<const ::DMCinfo::BMSRlyOC&>(
      ::DMCinfo::_BMSRlyOC_default_instance_);
}
inline const ::DMCinfo::BMSRlyOC& ActiveProtectParam::bmsrlyoc() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ActiveProtectParam.bmsRlyOC)
  return _internal_bmsrlyoc();
}
inline void ActiveProtectParam::unsafe_arena_set_allocated_bmsrlyoc(
    ::DMCinfo::BMSRlyOC* bmsrlyoc) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmsrlyoc_);
  }
  bmsrlyoc_ = bmsrlyoc;
  if (bmsrlyoc) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:DMCinfo.ActiveProtectParam.bmsRlyOC)
}
inline ::DMCinfo::BMSRlyOC* ActiveProtectParam::release_bmsrlyoc() {
  
  ::DMCinfo::BMSRlyOC* temp = bmsrlyoc_;
  bmsrlyoc_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::DMCinfo::BMSRlyOC* ActiveProtectParam::unsafe_arena_release_bmsrlyoc() {
  // @@protoc_insertion_point(field_release:DMCinfo.ActiveProtectParam.bmsRlyOC)
  
  ::DMCinfo::BMSRlyOC* temp = bmsrlyoc_;
  bmsrlyoc_ = nullptr;
  return temp;
}
inline ::DMCinfo::BMSRlyOC* ActiveProtectParam::_internal_mutable_bmsrlyoc() {
  
  if (bmsrlyoc_ == nullptr) {
    auto* p = CreateMaybeMessage<::DMCinfo::BMSRlyOC>(GetArenaForAllocation());
    bmsrlyoc_ = p;
  }
  return bmsrlyoc_;
}
inline ::DMCinfo::BMSRlyOC* ActiveProtectParam::mutable_bmsrlyoc() {
  ::DMCinfo::BMSRlyOC* _msg = _internal_mutable_bmsrlyoc();
  // @@protoc_insertion_point(field_mutable:DMCinfo.ActiveProtectParam.bmsRlyOC)
  return _msg;
}
inline void ActiveProtectParam::set_allocated_bmsrlyoc(::DMCinfo::BMSRlyOC* bmsrlyoc) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete bmsrlyoc_;
  }
  if (bmsrlyoc) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::DMCinfo::BMSRlyOC>::GetOwningArena(bmsrlyoc);
    if (message_arena != submessage_arena) {
      bmsrlyoc = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bmsrlyoc, submessage_arena);
    }
    
  } else {
    
  }
  bmsrlyoc_ = bmsrlyoc;
  // @@protoc_insertion_point(field_set_allocated:DMCinfo.ActiveProtectParam.bmsRlyOC)
}

// .DMCinfo.OverCharge overCharge = 10;
inline bool ActiveProtectParam::_internal_has_overcharge() const {
  return this != internal_default_instance() && overcharge_ != nullptr;
}
inline bool ActiveProtectParam::has_overcharge() const {
  return _internal_has_overcharge();
}
inline void ActiveProtectParam::clear_overcharge() {
  if (GetArenaForAllocation() == nullptr && overcharge_ != nullptr) {
    delete overcharge_;
  }
  overcharge_ = nullptr;
}
inline const ::DMCinfo::OverCharge& ActiveProtectParam::_internal_overcharge() const {
  const ::DMCinfo::OverCharge* p = overcharge_;
  return p != nullptr ? *p : reinterpret_cast<const ::DMCinfo::OverCharge&>(
      ::DMCinfo::_OverCharge_default_instance_);
}
inline const ::DMCinfo::OverCharge& ActiveProtectParam::overcharge() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ActiveProtectParam.overCharge)
  return _internal_overcharge();
}
inline void ActiveProtectParam::unsafe_arena_set_allocated_overcharge(
    ::DMCinfo::OverCharge* overcharge) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(overcharge_);
  }
  overcharge_ = overcharge;
  if (overcharge) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:DMCinfo.ActiveProtectParam.overCharge)
}
inline ::DMCinfo::OverCharge* ActiveProtectParam::release_overcharge() {
  
  ::DMCinfo::OverCharge* temp = overcharge_;
  overcharge_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::DMCinfo::OverCharge* ActiveProtectParam::unsafe_arena_release_overcharge() {
  // @@protoc_insertion_point(field_release:DMCinfo.ActiveProtectParam.overCharge)
  
  ::DMCinfo::OverCharge* temp = overcharge_;
  overcharge_ = nullptr;
  return temp;
}
inline ::DMCinfo::OverCharge* ActiveProtectParam::_internal_mutable_overcharge() {
  
  if (overcharge_ == nullptr) {
    auto* p = CreateMaybeMessage<::DMCinfo::OverCharge>(GetArenaForAllocation());
    overcharge_ = p;
  }
  return overcharge_;
}
inline ::DMCinfo::OverCharge* ActiveProtectParam::mutable_overcharge() {
  ::DMCinfo::OverCharge* _msg = _internal_mutable_overcharge();
  // @@protoc_insertion_point(field_mutable:DMCinfo.ActiveProtectParam.overCharge)
  return _msg;
}
inline void ActiveProtectParam::set_allocated_overcharge(::DMCinfo::OverCharge* overcharge) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete overcharge_;
  }
  if (overcharge) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::DMCinfo::OverCharge>::GetOwningArena(overcharge);
    if (message_arena != submessage_arena) {
      overcharge = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, overcharge, submessage_arena);
    }
    
  } else {
    
  }
  overcharge_ = overcharge;
  // @@protoc_insertion_point(field_set_allocated:DMCinfo.ActiveProtectParam.overCharge)
}

// .DMCinfo.BMSDataRepeat bmsDataRepeat = 11;
inline bool ActiveProtectParam::_internal_has_bmsdatarepeat() const {
  return this != internal_default_instance() && bmsdatarepeat_ != nullptr;
}
inline bool ActiveProtectParam::has_bmsdatarepeat() const {
  return _internal_has_bmsdatarepeat();
}
inline void ActiveProtectParam::clear_bmsdatarepeat() {
  if (GetArenaForAllocation() == nullptr && bmsdatarepeat_ != nullptr) {
    delete bmsdatarepeat_;
  }
  bmsdatarepeat_ = nullptr;
}
inline const ::DMCinfo::BMSDataRepeat& ActiveProtectParam::_internal_bmsdatarepeat() const {
  const ::DMCinfo::BMSDataRepeat* p = bmsdatarepeat_;
  return p != nullptr ? *p : reinterpret_cast<const ::DMCinfo::BMSDataRepeat&>(
      ::DMCinfo::_BMSDataRepeat_default_instance_);
}
inline const ::DMCinfo::BMSDataRepeat& ActiveProtectParam::bmsdatarepeat() const {
  // @@protoc_insertion_point(field_get:DMCinfo.ActiveProtectParam.bmsDataRepeat)
  return _internal_bmsdatarepeat();
}
inline void ActiveProtectParam::unsafe_arena_set_allocated_bmsdatarepeat(
    ::DMCinfo::BMSDataRepeat* bmsdatarepeat) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bmsdatarepeat_);
  }
  bmsdatarepeat_ = bmsdatarepeat;
  if (bmsdatarepeat) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:DMCinfo.ActiveProtectParam.bmsDataRepeat)
}
inline ::DMCinfo::BMSDataRepeat* ActiveProtectParam::release_bmsdatarepeat() {
  
  ::DMCinfo::BMSDataRepeat* temp = bmsdatarepeat_;
  bmsdatarepeat_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::DMCinfo::BMSDataRepeat* ActiveProtectParam::unsafe_arena_release_bmsdatarepeat() {
  // @@protoc_insertion_point(field_release:DMCinfo.ActiveProtectParam.bmsDataRepeat)
  
  ::DMCinfo::BMSDataRepeat* temp = bmsdatarepeat_;
  bmsdatarepeat_ = nullptr;
  return temp;
}
inline ::DMCinfo::BMSDataRepeat* ActiveProtectParam::_internal_mutable_bmsdatarepeat() {
  
  if (bmsdatarepeat_ == nullptr) {
    auto* p = CreateMaybeMessage<::DMCinfo::BMSDataRepeat>(GetArenaForAllocation());
    bmsdatarepeat_ = p;
  }
  return bmsdatarepeat_;
}
inline ::DMCinfo::BMSDataRepeat* ActiveProtectParam::mutable_bmsdatarepeat() {
  ::DMCinfo::BMSDataRepeat* _msg = _internal_mutable_bmsdatarepeat();
  // @@protoc_insertion_point(field_mutable:DMCinfo.ActiveProtectParam.bmsDataRepeat)
  return _msg;
}
inline void ActiveProtectParam::set_allocated_bmsdatarepeat(::DMCinfo::BMSDataRepeat* bmsdatarepeat) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete bmsdatarepeat_;
  }
  if (bmsdatarepeat) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::DMCinfo::BMSDataRepeat>::GetOwningArena(bmsdatarepeat);
    if (message_arena != submessage_arena) {
      bmsdatarepeat = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bmsdatarepeat, submessage_arena);
    }
    
  } else {
    
  }
  bmsdatarepeat_ = bmsdatarepeat;
  // @@protoc_insertion_point(field_set_allocated:DMCinfo.ActiveProtectParam.bmsDataRepeat)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace DMCinfo

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::DMCinfo::GunProEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::DMCinfo::GunProEnum>() {
  return ::DMCinfo::GunProEnum_descriptor();
}
template <> struct is_proto_enum< ::DMCinfo::RunModeEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::DMCinfo::RunModeEnum>() {
  return ::DMCinfo::RunModeEnum_descriptor();
}
template <> struct is_proto_enum< ::DMCinfo::GunTypeEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::DMCinfo::GunTypeEnum>() {
  return ::DMCinfo::GunTypeEnum_descriptor();
}
template <> struct is_proto_enum< ::DMCinfo::ADModuleTypeEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::DMCinfo::ADModuleTypeEnum>() {
  return ::DMCinfo::ADModuleTypeEnum_descriptor();
}
template <> struct is_proto_enum< ::DMCinfo::HmcChargeStrategyEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::DMCinfo::HmcChargeStrategyEnum>() {
  return ::DMCinfo::HmcChargeStrategyEnum_descriptor();
}
template <> struct is_proto_enum< ::DMCinfo::HmcChargeModeEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::DMCinfo::HmcChargeModeEnum>() {
  return ::DMCinfo::HmcChargeModeEnum_descriptor();
}
template <> struct is_proto_enum< ::DMCinfo::HmcLcrEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::DMCinfo::HmcLcrEnum>() {
  return ::DMCinfo::HmcLcrEnum_descriptor();
}
template <> struct is_proto_enum< ::DMCinfo::HvdcSilenceMode> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::DMCinfo::HvdcSilenceMode>() {
  return ::DMCinfo::HvdcSilenceMode_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_GCU_5fDMC_5fINFO_2eproto
