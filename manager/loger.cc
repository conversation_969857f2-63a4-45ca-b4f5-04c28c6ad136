/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class loger Definition here.
 */
#include <loger.h>
#include "common/evs_config.h"
#include "common/evs_common.h"

#pragma pack(1)

struct sockaddr_in sockaddrLoger;
#define SDELAYED 50000
#pragma pack()
static int socketLoger = -1 ;
bool g_debug_ = false;

//using namespace evs;
int32_t LogerSocketInit(void)
{
    Status ret = EVS_SUCCESS;
    int port = PORT_LOGER;

    socketLoger = socket(AF_INET, SOCK_DGRAM, 0);
    if (socketLoger < 0) {
        std::cout <<"Creat socket Loger Failed!"<< std::endl;
        return EVS_FAILED;
    }
    sockaddrLoger.sin_family = AF_INET;
    sockaddrLoger.sin_port = htons(port);
    sockaddrLoger.sin_addr.s_addr = inet_addr(LOCOL_IP.c_str());
    std::cout <<"EVS socket Loger Done.socket ID:  "<< socketLoger<<std::endl;
    return ret;
}

int32_t LogerSocketExit(void)
{
    Status ret = EVS_SUCCESS;
    if (socketLoger > 0) {
        shutdown(socketLoger,SHUT_RDWR);
        usleep(SDELAYED);
        close(socketLoger);
    }
    return ret;
}

static Status SendLoger(uint8_t *dataBuf, size_t bufSize)
{
    Status ret = EVS_SUCCESS;
    if (dataBuf == nullptr || bufSize <= 0) {
        std::cout <<"socketLoger dataBuf is NULL or bufSize is 0!"<< std::endl;
        return EVS_FAILED;
    }

    socklen_t lenSockaddr = sizeof(struct sockaddr);
    ret = sendto(socketLoger, dataBuf, bufSize, 0,
                reinterpret_cast<struct sockaddr*>(&sockaddrLoger), lenSockaddr);
    if (ret <= 0) {
        return EVS_FAILED;
    }
    return EVS_SUCCESS;
}

static Status LogPackaging(uint32_t module, const char *content, size_t length)
{

    size_t totalSize = length + LOG_HEADSIZE + TAILSIZE;
    uint8_t pro_array_buff[totalSize];
    memset(pro_array_buff, 0x00, totalSize);
    ProtocolLog_t sendFrame;
    
    sendFrame.head = 0xFA;
    sendFrame.len = length;
    sendFrame.cmd = LOG_RECORD_INFO_CMD;
    sendFrame.mode = (uint8_t)module;
    sendFrame.resv[0] = 0xFF;
    sendFrame.resv[1] = 0xFF;
    // head
    memcpy(pro_array_buff, ((uint8_t*)(&sendFrame)), LOG_HEADSIZE);
    // data
    memcpy(&(pro_array_buff[LOG_HEADSIZE]), content, length);
    // tail
    memcpy(&(pro_array_buff[length + LOG_HEADSIZE]), TAIL, TAILSIZE);
    // send
    SendLoger((uint8_t*)&pro_array_buff,totalSize);
    return EVS_SUCCESS;
}

void Loger(int module, const char * format, ...)
{
    struct timeval currTime;
    gettimeofday(&currTime, nullptr);
    struct ::tm timeInfo;
    time_t rawTime = currTime.tv_sec;
    localtime_r(&rawTime, &timeInfo);

    char buffer[LOG_UFFER_SIZE]; 
    char data[LOG_UFFER_SIZE];
    int logModule = 0;
    uint16_t leve = 1; // 暂时写死 leve1
    va_list arg;
    logModule = module;
    memset(buffer,0,sizeof(buffer)); 
    memset(data,0,sizeof(data));
    va_start (arg, format);
    vsnprintf(buffer, LOG_UFFER_SIZE, format, arg);
    va_end (arg);
	if(module != CAN_LOG){
        sprintf(data, "<%d>[%d-%02d-%d %02d:%02d:%02d.%06lu] " , leve, (timeInfo.tm_year + 1900), (timeInfo.tm_mon+1), timeInfo.tm_mday, \
        timeInfo.tm_hour, timeInfo.tm_min, timeInfo.tm_sec, currTime.tv_usec);
        std::string result = data;
        result.append(buffer);
        strcpy(data, result.c_str());
    } else {
        memcpy(data, buffer, LOG_UFFER_SIZE);
    }
    LogPackaging(logModule, data, strlen(data));

    if(module != CAN_LOG && true == g_debug_){ // Debug 前台打印
        printf("%s\n", data);
    }
}


void Loger(int leve, int module, const char * format, ...)
{
    struct timeval currTime;
    gettimeofday(&currTime, nullptr);
    struct ::tm timeInfo;
    time_t rawTime = currTime.tv_sec;
    localtime_r(&rawTime, &timeInfo);
    char buffer[LOG_UFFER_SIZE];
    char data[LOG_UFFER_SIZE];
    va_list arg;
    memset(buffer,0,sizeof(buffer));
    memset(data,0,sizeof(data));
    va_start (arg, format);
    vsnprintf(buffer, LOG_UFFER_SIZE, format, arg);
    va_end (arg);
    if(leve == 1){
        char data1[LOG_UFFER_SIZE] = LOG_RED;
        strcat(data1, buffer);
        strcat(data1, LOG_CLEAR);
        memcpy(data, data1, LOG_UFFER_SIZE);
    }else if(leve == 2){
        char data2[LOG_UFFER_SIZE] = LOG_YELLOW;
        strcat(data2, buffer);
        strcat(data2, LOG_CLEAR);
        memcpy(data, data2, LOG_UFFER_SIZE);
    } else{
        char data3[LOG_UFFER_SIZE] = LOG_GREEN;
        strcat(data3, buffer);
        strcat(data3, LOG_CLEAR);
        memcpy(data, data3, LOG_UFFER_SIZE);
    }
    printf("<1>[%d-%02d-%d %02d:%02d:%02d.%06lu]" , (timeInfo.tm_year + 1900), (timeInfo.tm_mon+1), timeInfo.tm_mday, \
        timeInfo.tm_hour, timeInfo.tm_min, timeInfo.tm_sec, currTime.tv_usec); 
    printf(" %s\n", data);
}