/***
 * @description: 
 * @version: V00R00C00
 * @author: name
 * @date: Do not edit
 * @lastEditTime: Do not edit 
 * @lastEditors: name 
 * @Copyright: 2021-2023 Xi'an wanma Novel Energy Co., Ltd.
 */
/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class Loger Declaration here.
 */
#ifndef _LOGER_H
#define _LOGER_H
#include <iostream>
#include <sys/socket.h>
#include <sys/time.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <cstring>
#include <unistd.h>
#include <string>
#include <cstdlib>
#include <errno.h>
#include <cstdarg>

#include "google/protobuf/message_lite.h"
#include "proto/GCU_LCR_ALL.pb.h"

#pragma pack(1)
typedef struct _ProtocolLog_t {
    uint8_t head;       // 0xFA
    uint16_t len;       // data len (exclude header)
    uint8_t cmd;        // command
    uint8_t mode;
    uint8_t resv[2];
    uint8_t data[0];    // data  
} ProtocolLog_t;
#pragma pack()

#define  NORMAL  5
#define  CAN_LOG  7
#define LOG_UFFER_SIZE 4096
#define LOG_RED     "\e[1;31m"
#define LOG_YELLOW  "\e[1;33m"
#define LOG_GREEN   "\e[1;32m"
#define LOG_CLEAR   "\e[0m"

class LogEmployee;

int32_t LogerSocketInit(void);
int32_t LogerSocketExit(void);

void Loger(int level, const char * format, ...);
void Loger(int leve, int module, const char * format, ...);


#endif  //_LOGER_H