/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class loger Definition here.
 */
#include "loger.h"
#include "evs_manager.h"
#include "mt_timer.h"
#include "evs_gpio.h"

static void GpioCb(void *)// GPIO
{
    //EVSGpio *gpio = reinterpret_cast<EVSGpio *>(pGpio);
    GpioProcess();
}
static void AdcCb(void *pAdc)// AD 
{
    EVSAdc *adc = reinterpret_cast<EVSAdc *>(pAdc);
    adc->AdcProcess();
}


static struct itimerspec SetTime(uint32_t start, uint32_t cycle){// us
    struct itimerspec itimer;
    itimer.it_value.tv_sec = start/1000000;
    itimer.it_value.tv_nsec = (start%1000000)*1000;

    itimer.it_interval.tv_sec = cycle/1000000;
    itimer.it_interval.tv_nsec = (cycle%1000000)*1000;
    return itimer;
}


TIMER_CREATE(service);
void EVSManager::StartTimer(void){
    
    struct itimerspec itimespec;
    
    TIMER_INIT(service, 10);
    GpioInit();
    itimespec = SetTime(21151,37817);
    TimerFd_.gpio = TIMER_ADD(service, &itimespec, -1, GpioCb, NULL, NULL);
    itimespec = SetTime(31217,4973);
    TimerFd_.adc = TIMER_ADD(service, &itimespec, -1, AdcCb, (void*)(&ADC_), NULL);
}

int32_t EVSManager::EndTimer(int32_t fd){
    return TIMER_DEL(service, fd);
}