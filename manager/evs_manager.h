/***
 * @description: 
 * @version: V00R00C00
 * @author: name
 * @date: Do not edit
 * @lastEditTime: Do not edit 
 * @lastEditors: name 
 * @Copyright: 2021-2023 Xi'an wanma Novel Energy Co., Ltd.
 */
/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class Loger Declaration here.
 */
#ifndef _EVS_MANAGER_H
#define _EVS_MANAGER_H
#include <string>
#include "evs_car.h"
#include "evs_proto.h"
#include "evs_pmm.h"
#include "evs_meter.h"
#include "evs_gpio.h"
#include "evs_adc.h"
typedef struct{
    int32_t proto;
    int32_t pmm;
    int32_t meter;
    int32_t adc;
    int32_t gpio;
}TimerFdDef;
class EVSManager {
public:
    EVSManager();
    ~EVSManager(){}
public:
    EVSCar *EvsCar_ = nullptr;
    //EVSProto EvsProto_;
    EVSPmm EvsPmm_;
    EVSMeter EvsMeter_;
    EVSAdc ADC_;
    //EVSGpio GPIO_;
    std::string serverIP = EVS_SOCKET_IP;

private:
    TimerFdDef TimerFd_;
    void StartTimer(void);
    int32_t EndTimer(int32_t fd);
private:
    uint32_t OfflineCnt_[6];
    uint32_t TimeoutCnt_[6];
    int32_t OfflineDetect(void);
public:
    void EVS_Init(void);
    int32_t MainProcess(void);
};


#endif  //_EVS_MANAGER_H