/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class loger Definition here.
 */
#include "loger.h"
#include "evs_manager.h"
#include "evs_car.h"
#include "evs_config.h"
#include "evs_proto.h"
#include "evs_pmm.h"
#include "evs_meter.h"
#include "mt_timer.h"
#include <thread>
#include "evs_common.h"

extern uint8_t g_evsType;

static void *CreatCharger(void *pcharger)
{
    EVSCar *charger = reinterpret_cast<EVSCar *>(pcharger);
    Loger(NORMAL,"EVS charger is Born ID!");
    charger->CarProcess();
    delete charger;
    Loger(NORMAL,"EVS charger is Dead !");
    return nullptr;
}
static void *CreateMeter(void *pmeter) //meter
{
    EVSMeter *meter = reinterpret_cast<EVSMeter *>(pmeter);
    meter->MeterProcess();
    return nullptr;
}
static void *CreateProto(void *comm) // proto communicate
{
    EVSProto *proto = reinterpret_cast<EVSProto *>(comm);
    proto->ProtoProcess();
    return nullptr;
}
static void *CreatePmm(void *ppmm) // HVDC ctrl
{
    EVSPmm *pmm = reinterpret_cast<EVSPmm *>(ppmm);
    pmm->PmmProcess();
    return nullptr;
}
static int32_t SetThreadAttr(pthread_attr_t &attr,uint32_t stack,
            int32_t policy,int32_t priority){
    struct sched_param cParam;
    pthread_attr_init(&attr);
    pthread_attr_setstacksize(&attr, PTHREAD_STACK_MIN * stack);
    pthread_attr_setinheritsched(&attr, PTHREAD_EXPLICIT_SCHED);
    pthread_attr_setschedpolicy(&attr, policy);
    cParam.sched_priority = priority;
    pthread_attr_setschedparam(&attr, &cParam);
    return 0;
}
EVSManager::EVSManager(){
    memset(&OfflineCnt_,0,sizeof(OfflineCnt_));
    memset(&TimeoutCnt_,0,sizeof(TimeoutCnt_));
}
void EVSManager::EVS_Init(void){
    ADC_.AdcInit();
}

int32_t EVSManager::OfflineDetect(void){
    if(EvsCar_ != nullptr){
        if(OfflineCnt_[0] != EvsCar_->OnLineCnt_){
            TimeoutCnt_[0] = 0;
            OfflineCnt_[0] = EvsCar_->OnLineCnt_;
        }else{
            ++TimeoutCnt_[0];
        }
        if(TimeoutCnt_[0] > 5){
            Loger(NORMAL,"EVS Thread offline , cnt = %d\n",EvsCar_->OnLineCnt_);
        }
    }  
    return 0;
}

int32_t EVSManager::MainProcess(){
    int32_t ret = -1;
    uint32_t cnt = 0;

    pthread_t chargerTid;
    pthread_attr_t cAttr;
    EvsCar_ = new EVSCar(this);
    if(EvsCar_ == nullptr)
    {
        Loger(NORMAL,"EVS car is null!");
        return -1;
    }

    SetThreadAttr(cAttr,10,SCHED_RR,CHARGER_PRIORITY);
    ret = pthread_create(&chargerTid, &cAttr, CreatCharger, EvsCar_);
    if (ret)
    {
        Loger(NORMAL,"evs_car pthread_create error with code:%d", ret);
    }
    ret = pthread_detach(chargerTid);
    pthread_t protoTid;
    SetThreadAttr(cAttr,2,SCHED_OTHER,NORMAL_PRIORITY);
    ret = pthread_create(&protoTid, &cAttr, CreateProto, &EvsCar_->eVSProto);
    if (ret)
    {
        Loger(NORMAL,"evs_car pthread_create error with code:%d", ret);
    }
    ret = pthread_detach(protoTid);
    if(g_evsType == 0) {
        pthread_t meterTid;
        SetThreadAttr(cAttr,2,SCHED_OTHER,NORMAL_PRIORITY);
        ret = pthread_create(&meterTid, &cAttr, CreateMeter, &EvsMeter_);
        if (ret)
        {
            Loger(NORMAL,"evs_car pthread_create error with code:%d", ret);
        }
        ret = pthread_detach(meterTid);
        pthread_t pmmTid;
        SetThreadAttr(cAttr,2,SCHED_OTHER,NORMAL_PRIORITY);
        ret = pthread_create(&pmmTid, &cAttr, CreatePmm, &EvsPmm_);
        if (ret)
        {
            Loger(NORMAL,"evs_car pthread_create error with code:%d", ret);
        }
        ret = pthread_detach(pmmTid);
        EVS_Init();
        StartTimer();
    }
    while(1){
        if((++cnt % 10) == 0){
            Loger(NORMAL, "manager cnt = %d", cnt);
        }
        OfflineDetect();
        std::this_thread::sleep_for(std::chrono::milliseconds(5000));
    }

    return ret;
}