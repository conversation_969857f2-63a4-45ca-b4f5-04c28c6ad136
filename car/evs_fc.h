#ifndef _EVS_FC_H
#define _EVS_FC_H
#include "can_data.h"
#include "can_ex.h"
#include "FailManager.h"

typedef enum : uint8_t
{
    INIT,    // 初始化
    WAITACK, // 等待车辆反馈
    WAITACK1,
    WAITACK2,
    PHASECONFIRMATION // 阶段确认
} FunctionmodStatus;
typedef enum : uint8_t
{
    FUN_INIT = 0,   // 初始化
    FUN_WAIT = 1,    // 等待电桩报文
    FUN_WAIT2 = 2,
    FUN_WAIT3 = 3,
    FUN_TIMEOUT = 4,
    FUN_END = 5
} EvFunStatus;

typedef enum{
    EVS_OFF = 0,
    EVS_ON = 1
}EvsStateEnum;

typedef enum : uint8_t
{
    PROTO_NEGO = 0x00,              // 版本协商
    FUNCTION_NEG = 0x1,             // 功能协商
    PARAMETER_CONFIG = 0x2,         // 参数配置
    AUTHENTICATION = 0x3,           // 鉴权
    APPOINTMENT = 0x4,              // 预约
    SELF_CHECK = 0x5,               // 自检
    POWER_SUPPLY = 0x6,             // 供电模式
    PRE_CHARGE = 0x7,               // 预充
    ENERGY_TRANSFER = 0x8,          // 能量传输
    CHG_END = 0x9,                  // 结束
    CHG_RECONNECT = 0xA,            // 重连
    CHG_DESTORY = 0xB               // 线程退出
} ChargingStatus;

typedef enum : uint8_t
{
    CAUTHENUNFINISHED = 0x00, // 鉴权未完成
    CAUTHENFINISHED = 0xAA    // 鉴权完成
} CAuthenStatusType;

typedef enum : uint8_t
{
    RESULTFAILE = 0x00, // 鉴权失败
    RESULTOK = 0xAA     // 鉴权成功
} AuthenResultType;

typedef enum{
    NO_STOP = 0,
    USER_STOP = 1,
    EV_STOP = 2,
    SE_STOP = 3
}StopTrigerDef;

typedef enum{
    NEGO_CONTINUE = 0x00, // 继续协商
    NEGO_SUCCESS = 0x01, // 协商成功
    NEGO_FAIL = 0x02 // 协商失败
}ProtoNegoResultType;

typedef enum{
    X0_00H = 0,  // 版本协商报文
    X2_02H = 1,  // 车辆确认结果报文
    X4_04H = 2,  // 车辆中止报文
    X6_06H = 3,  // 车辆充电回路接触器状态报文
    X9_09H = 4,  // 车辆唤醒报文
    B2_12H = 5,  // 车辆功能协商确认结果报文
    C2_22H = 6,  // 车辆充电参数报文（FDC = 1）
    C4_24H = 7,  // 车辆充放电参数报文（FDC = 2）
    D2_32H = 8,  // 车辆鉴权等待报文（FDC = 1）
    D4_34H = 9,  // 车辆鉴权参数报文（FDC = 2）
    D6_36H = 10, // 重新鉴权请求报文（FDC = 2）
    D7_37H = 11, // 车辆鉴权参数报文（FDC = 3）
    D9_39H = 12, // 鉴权结果报文（FDC = 3）
    D10_3AH = 13,// 重新鉴权请求报文（FDC = 3）
    E2_42H = 14, // 车辆预约充电信息报文（FDC = 1)
    E4_44H = 15, // 车辆预约充电协商报文（FDC = 1）
    F2_52H = 16, // 检测确认报文（FDC = 1）
    G2_62H = 17, // 车辆供电状态报文（FDC = 1）
    G3_63H = 18, // 车辆供电需求报文（FDC = 1）
    G5_65H = 19, // 车辆供电完成报文
    H2_72H = 20, // 车辆就绪状态报文（FDC = 1）
    H3_73H = 21, // 车辆充电需求报文（FDC = 1）
    H4_74H = 22, // 车辆充电基本信息报文（FDC = 1）
    H7_77H = 23, // 车辆充电电池基本信息（FDC = 1）
    H9_79H = 24, // 车辆暂停报文（FDC = 1）
    H11_82H = 25, // 车辆就绪状态报文（FDC = 2）
    H13_84H = 26, // 车辆动态输出能力报文（FDC = 2）
    H14_85H = 27, // 车辆充电需求报文（FDC = 2）
    H16_87H = 28, // 车辆充放电基本信息报文（fDC = 2）
    H18_89H = 29, // 车辆充放电电池基本信息（FDC = 2）
    H20_8BH = 30, // 车辆暂停报文（FDC = 2）
    I1_91H = 31,  // 车辆粘连检测报文（FDC = 1）
    I4_94H = 32,  // 车辆统计报文（FDC = 1）
    CAR_MSG_NUM
}CarMsgIndexDef;
//#pragma pack(1)
typedef struct{
    int32_t stage;
    int32_t cycle;
    int32_t msgRecv;
    int32_t pause;
    int32_t common;
}FCTimerDef;

//报文状态设置
typedef union{ 
    uint32_t msg[CAR_MSG_NUM];
    struct{ 
        uint32_t x2_0x02_State; //  = 1;  // 车辆确认结果报文
        uint32_t x4_0x04_State; //   = 2;  // 车辆中止报文
        uint32_t x6_0x06_State; //   = 3;  // 车辆充电回路接触器状态报文
        uint32_t x9_0x09_State; //   = 4;  // 车辆唤醒报文
        uint32_t b2_0x12_State; //   = 5;  // 车辆功能协商确认结果报文
        uint32_t c2_0x22_State; //   = 6;  // 车辆充电参数报文（FDC = 1）
        uint32_t c4_0x24_State; //   = 7;  // 车辆充放电参数报文（FDC = 2）
        uint32_t d2_0x32_State; //   = 8;  // 车辆鉴权等待报文（FDC = 1）
        uint32_t d4_0x34_State; //   = 9;  // 车辆鉴权参数报文（FDC = 2）
        uint32_t d6_0x36_State; //   = 10; // 重新鉴权请求报文（FDC = 2）
        uint32_t d7_0x37_State; //   = 11; // 车辆鉴权参数报文（FDC = 3）
        uint32_t d9_0x39_State; //   = 12; // 鉴权结果报文（FDC = 3）
        uint32_t d10_0x3A_State; //   = 13;// 重新鉴权请求报文（FDC = 3）
        uint32_t e2_0x42_State; //   = 14; // 车辆预约充电信息报文（FDC = 1)
        uint32_t e4_0x44_State; //   = 15; // 车辆预约充电协商报文（FDC = 1）
        uint32_t f2_0x52_State; //   = 16; // 检测确认报文（FDC = 1）
        uint32_t g2_0x62_State; //   = 17; // 车辆供电状态报文（FDC = 1）
        uint32_t g3_0x63_State; //   = 18; // 车辆供电需求报文（FDC = 1）
        uint32_t g5_0x65_State; //   = 19; // 车辆供电完成报文
        uint32_t h2_0x72_State; //   = 20; // 车辆就绪状态报文（FDC = 1）
        uint32_t h3_0x73_State; //   = 21; // 车辆充电需求报文（FDC = 1）
        uint32_t h4_0x74_State; //   = 22; // 车辆充电基本信息报文（FDC = 1）
        uint32_t h7_0x77_State; //   = 23; // 车辆充电电池基本信息（FDC = 1）
        uint32_t h9_0x79_State; //   = 24; // 车辆暂停报文（FDC = 1）
        uint32_t h11_0x82_State; //   = 25; // 车辆就绪状态报文（FDC = 2）
        uint32_t h13_0x84_State; //   = 26; // 车辆动态输出能力报文（FDC = 2）
        uint32_t h14_0x85_State; //   = 27; // 车辆充电需求报文（FDC = 2）
        uint32_t h16_0x87_State; //   = 28; // 车辆充放电基本信息报文（fDC = 2）
        uint32_t h18_0x89_State; //   = 29; // 车辆充放电电池基本信息（FDC = 2）
        uint32_t h20_0x8B_State; //   = 30; // 车辆暂停报文（FDC = 2）
        uint32_t i1_0x91_State; //   = 31;  // 车辆粘连检测报文（FDC = 1）
        uint32_t i4_0x94_State; //   = 32;  // 车辆统计报文（FDC = 1）
    }part; 
}EVMsgCtrlDef; 

//PhaseACK-阶段确认
typedef struct {  //（0x00:确认失败， 0xAA:确认成功, 0x55:不回复）(默认0xAA)
    uint32_t funConferAck; // = 1;   //功能协商功能确认结果
    uint32_t configAck; // = 2;      //配置功能确认结果 
    uint32_t authenAck; // = 3;      //鉴权功能确认结果
    uint32_t appointAck; // = 4;     //预约功能确认结果
    uint32_t selfCheckAck; // = 5;   //输出回路检测功能确认结果
    uint32_t powerSupplyAck; // = 6; //供电模式功能确认结果
    uint32_t energyTransferAck; // = 7;//预充及能量传输功能确认结果 
    uint32_t endAck; // = 8;         //结束功能确认结果
}EVFunConferAckMsgDef;

typedef struct{
    GB2015P::FDCResultDef fdcResult;
}MsgToExDef;

typedef struct{
    uint32_t workCmd;   // 0x55: OFF 0xAA:ON
    uint32_t carReady;  // 车辆充电准备就绪状态：0x00未就绪；0xAA就绪 
    uint16_t batVol;       // 电池电压
    uint32_t cc1State;   // cc1 state
    int32_t meterVol; // 电表电压 0.1
    int32_t meterCur; // 电表电流 0.1 
    int32_t chgEnergy; // 电表能量 0.1
    uint32_t k5Cmd;    // 车辆高压接触器C5状态 0：open  1 close
    uint32_t k6Cmd;    // 车辆高压接触器C6状态 0：open  1 close
    uint32_t k5Fb;    // 车辆高压接触器C5状态 0：open  1 close
    uint32_t k6Fb;    // 车辆高压接触器C6状态 0：open  1 close
    uint32_t k5Stick;    // 车辆高压接触器C5状态 0：open  1 close
    uint32_t k6Stick;    // 车辆高压接触器C6状态 0：open  1 close
    uint32_t offlinMode;  // 0x00 离线模式  0xAA 联网模式(默认)
    uint32_t evPause; // 车辆暂停 0xAA 暂停，0x00 恢复

}FcCaredParaDef;

typedef struct{
    uint8_t endCode; // to bms
    uint8_t endReasonL;
    uint8_t endReasonH;
    uint32_t stopCode;//中止码 to user
}StopMsgDef;

typedef struct{
    ChargingStatus evsState;
    GB2015P::FCType FC;
    GB2015P::FDCType FDC;
}FCStateDef;

typedef struct{
    uint32_t workMode; // 0: OFF 1: ON
    uint32_t chgPause; // charger暂停
    uint32_t stickEnable;
    StopMsgDef stopMsg;
    FCStateDef FCState;
    uint32_t reConnect;
    uint32_t outContactorDesire; // 0x00 open 0x01 close
    uint32_t seReady;  //0x00 not ready 0x01 ready
    ProtoNegoResultType protoNegoRlt;
    uint32_t chargerRlyState;// 0x00 open 0x1 close 
}FcOutDataDef;

typedef struct{
    uint8_t nowState;
    uint8_t nextState;
    uint8_t nowFC;
    uint8_t nowFDC;
    uint8_t nextFC;
    uint8_t nextFDC;
}FCStateExDef;

typedef struct{
   uint32_t enable;
   uint32_t cycle;
   uint32_t maxTime;
}MsgParaDef;

typedef struct{
    EvsStateEnum OnOff;
    FCStateDef fcState;
    EvFunStatus funState;
    ProtoNegoResultType negoResult;
    uint16_t sendCnt;
}ThisState;


typedef struct{
    uint32_t D6Cnt;
    uint32_t G6Cnt;
}MsgCntDef;

//#pragma pack()  
class EvsFc : public CanEx
{
    public:
        FailManager *pFault_;
        EvsFc(FailManager *fail);
        ~EvsFc() {}
    private:// set para from fsm
        EVFunConferAckMsgDef FunConferAck_;
        MsgParaDef MsgPara_[CAR_MSG_NUM];
    private:
        FCTimerDef FcTimer_;
        void FCTimer(void); //10ms
        void FCTimerOff(void);
    private:
        // 版本协商
        uint8_t RecvProtoNegoMsg(void);
        EvFunStatus ProtoNegotiation(void);
        // 功能协商
        EvFunStatus FunNegotiation(void);
        // 参数配置
        EvFunStatus ParaConfigFDC1(void);
        bool ParaConfigFDC1Result(void);
        // 鉴权
        EvFunStatus Authentication(void);
        EvFunStatus AuthenticationFDC1_SCAN(void);
        EvFunStatus AuthenticationFDC2_VIM(void);
        EvFunStatus AuthenticationFDC3_CLOUD(void);
        //预约 
        EvFunStatus Appointment(void);
        //self check
        EvFunStatus SelfCheck(void);
        // 供电
        bool SNeedFlg_ = false;
        EvFunStatus PowerSup(void);
        // energy transfer
        EvFunStatus PreCharge(void);
        EvFunStatus EnergyTransferFDC1(void);
        // end
        EvFunStatus EndMod(void);
        uint8_t SeEnd(void);
        uint8_t StopResonFresh(void);
        int32_t StatusEx(void);
        // common msg
        FCStateDef SEdesireFCState_;
        uint32_t SEexFlg_;
        uint8_t GetUserAck(uint8_t fc);
        EvFunStatus CommonMsg(void);
        uint8_t IsStateExLegal(uint8_t fc, uint8_t fdc);
        // process
        int32_t FCSwitch(void);
        int32_t InputUpdate(void);
        int32_t FCOnOffManage(void);
    private: // to Ex
        //功能协商
        uint8_t FunNegoResult_[FC_NUM];
        bool FunNegoResult(void);// 功能协商结果
        int32_t CarMsgUpdate(GB2015P::PIDType type);
        int32_t SendCarMsg(GB2015P::PIDType type);

    private:
        // inner state
        ThisState This;
        ThisState ThisBak_;
        MsgToExDef MsgToEx_;
        FcCaredParaDef FcCaredPara_;
        FcCaredParaDef FcCaredParaBak_;
        FcOutDataDef   FcOutData_;
        MsgCntDef MsgCnt_;
        uint32_t cc1Cnt = 0;
        
        // msg from User
        GB2015P::CarMsgDef CarMsgFromUser_;
        GB2015P::CarMsgDef CarMsgToEx_;
    public:
        int32_t UserSetBmsData(GB2015P::CarMsgDef data){CarMsgFromUser_ = data;return 0;};
        int32_t SetSysData(FcCaredParaDef data){FcCaredPara_ = data;return 0;};
        int32_t UserSetMsgPara(MsgParaDef para,CarMsgIndexDef index);
        void UserSetNegoAck(EVFunConferAckMsgDef ack);

        FcOutDataDef GetOutData(void);
        int32_t FCProcess(void);
  
};

#endif //_EVS_FC_H