/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class evs_charger Declaration here.
 */
#ifndef _EVS_CAR_H
#define _EVS_CAR_H
#include "can_data.h"
//#include "public_msg.h"
#include "timer.h"
//#include "can_ex.h"
#include "evs_fc.h"
#include "FailManager.h"
#include "evs_proto.h"
#include "switch_ctrl.h"
#include "evs_pmm.h"
#include "InsulationTest.h"

#define  CONVERT_WEIGHTS_NUM_TEN 10
#define  CONVERT_WEIGHTS_NUM_ONE_HUNDRED 100
#define  TEMPERATURE_OFFSET 50
#define  HEART_TIME_OUT   10

typedef struct {
    uint32_t id;
    std::string str;
    uint64_t time;
    uint8_t *ptr;
    uint8_t vin[5];
}MsgFromUserDef;

class EVSCar {
public:
    EVSCar(void *ctx);
    ~EVSCar();
private:
    void *_m_ctx;
    Timer Timer_;


public:
    EVSProto eVSProto;
    InsulationTest Insult_;

private:
    StorageParaDef UserSetData_;
    void EvsSleep(struct timespec enter_time);
    //proto
    uint8_t registMsgInitFlag = 1;
    int32_t ProtoInputUpdate(void);
    int32_t ProtoOutputUpdate(EvsFrameMessageEnum FramType);
    int32_t ProtoProcess(void);
    int32_t ProtoSchedule(void);
    void  setProtoEVSLoginData(void);
    void  setProtoEVSHeartData(void);
private:
    // FC 
    FcOutDataDef  DataFromFC_;
    FcOutDataDef  DataFromFCBak_;
    int32_t FcInputUpdate(void);
    int32_t FcSchedule(void);

    // Printf
private: //data form proto
    GB2015P::CarMsgDef CarMsgFromUser_;
    MsgParaDef CarMsgCtrlPara[CAR_MSG_NUM];
    EVFunConferAckMsgDef EvFunNegoAck_; 
    void UserDataInit(void);

private: // data to FC 
    FcCaredParaDef DataToFC_;
public:
    FailManager Fault_;
    EvsFc EvsFC_ = {&Fault_};
private:
    InsulationTest Insulation_;

private: //Battery
    int32_t batCnt_;
    int32_t batPauseCnt_ = 0;
    PmmCaredMsgDef  PmmCaredMsg_; //  evs-->Pmm
    PmmStateInfoDef PmmStateInfo_; // pmm -->evs
    int32_t BatteryReady(void);
    void CmdToPmm(void);
    void StateFromPmm(void);
    void BatteryManage(void);
    void evPausePmm(void);
    
private: //insult Resistor switch
    uint32_t SwitchR;  // switch R
    uint32_t evPause_; // 0:待机态 1：暂停 2：暂停恢复

private: // rly manage
    uint32_t RlyCmd_[RLY_NUM];
    uint32_t RlyCmdBak_[RLY_NUM];
    uint32_t RlyFb_[RLY_NUM];

    uint32_t RlyDiscnt_[RLY_NUM];// fault_discnt
    uint32_t RlyStick_[RLY_NUM]; // fault_stick

    uint32_t DiscntCnt_[RLY_NUM];// cnt_discnt
    uint32_t StickCnt_[RLY_NUM];// cnt_discnt
    
    void RlyDesireState(void);
    void RlyCtrl(void);//ctrl && fault
    void RlyFaultDetect(void);
    void RlyManage(void);//ctrl && fault
    void ElectControl(void);
private: //para save
    MsgFromUserDef UserMsg_;
    int32_t UserMsgSave(void);

public:
    uint32_t OnLineCnt_;//在线计数器
    int32_t CarInit(void);
    int32_t ethInit(void); //网络初始化
    int32_t CarProcess(void);
};


#endif  //_EVS_CAR_H