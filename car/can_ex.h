/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class evs_charger Declaration here.
 */
#ifndef _CAN_EX_H
#define _CAN_EX_H
#include "gb2015p_msgtype.h"
#include "can_data.h"

typedef enum {
    FC_PROTO_CONFER = 0,    // 版本协商
    FC_FUN_CONFER = 1,      // 功能协商
    FC_PARA_CONFIG = 2,     // 参数配置
    FC_AUTHEN = 3,          // 鉴权功能
    FC_APPOINT  = 4,        // 预约充电
    FC_SELF_CHECK = 5,      // 输出回路检测
    FC_POWER_SUPPLY = 6,    // 供电模式
    FC_ENERGY_TRANSFER = 7, // 能量传输
    FC_TRANSFER_END = 8,    // 充电结束
    FC_NUM
}FCTypeDef;
// typedef enum 
// {
//     FDC1 = 0,
//     FDC2 = 1,
//     FDC3 = 2,
//     FDC4 = 3,
//     FDC5 = 4,
//     FDC6 = 5,
//     FDC7 = 6,
//     FDC8 = 7,
//     FDC_NUM = 8
// }FDCTypeDef;
typedef struct{
    uint32_t enable;            // 是否允许发送 0：不允许，1 允许
    uint32_t cycle;             // 发送周期 单位ms
    uint32_t maxTime;           // 用户设置发送时间
}MsgCtrlParaDef;
typedef struct
{
    uint32_t PGI;        // PGI
    MsgCtrlParaDef ctrl; // ctrl para      
    Msg_Parameters msg;  // msg data
} BmsMsgInfoDef;

typedef struct{
    uint8_t nowRecv;    // 实时收到
    uint8_t firstRecv;  // 首次收到
    uint8_t alreadyRecv;// 已经收到
}RecvTypeDef;

typedef struct
{
    uint32_t PGI; 
    uint32_t recv;
    uint8_t  msglen;      
    uint8_t  *msg;  
} MsgInfoDef;

class CanEx {
    public:
        CanEx();
        ~CanEx();
    protected:// send && recv
        CanSocket CanSocket_;
        MsgSend CanSend_ = {&CanSocket_};
        MsgRecv CanRecv_ = {&CanSend_,&CanSocket_};

    protected: // can data pool
        GB2015P::CarMsgDef CarMsg_;
        GB2015P::SeMsgDef  SeMsg_;
        std::unordered_map<GB2015P::PIDType, BmsMsgInfoDef> CarMsgMap_;
        std::unordered_map<GB2015P::PIDType, MsgInfoDef> SEmsgMap_;

        // from FC msg
        GB2015P::CarMsgDef CarMsgFromFC_;
        std::unordered_map<GB2015P::PIDType, BmsMsgInfoDef> CarMsgFromFCMap_;
        int32_t UserMsgInit();
        
    public:
      
    public: // send msg
        
        int32_t SetCarData(GB2015P::PIDType type,uint8_t *data,uint32_t data_len);
        int32_t SetCtrlInfo(GB2015P::PIDType type,MsgCtrlParaDef ctrl);
        int32_t SetCarData(GB2015P::CarMsgDef msg){CarMsg_ = msg;return 0;};
        
        
        
        int32_t ClearMsg(GB2015P::PIDType pgi);
        int32_t ClearCarMsg(void);
        int32_t ClearSeMsg(void);
    public: //recv msg
        int32_t GetCarMsg(GB2015P::PIDType type,uint8_t *data,uint32_t data_len);
        uint8_t GetSeMsg(GB2015P::PIDType type);
        GB2015P::CarMsgDef GetCarMsg(void){return CarMsg_;}
        GB2015P::SeMsgDef GetSeMsg(void){return SeMsg_;}
        uint32_t GetRecvFlg(GB2015P::PIDType type);
        int32_t ClearRecvFlg(GB2015P::PIDType type);
    public:
        void CanExInit(void);
        void FastSchedule(void);// 3ms
        void NormalSchedule(void); //10ms
};
 

#endif  //_CAN_EX_H