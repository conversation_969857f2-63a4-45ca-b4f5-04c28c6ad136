/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class loger Definition here.
 */
#include "loger.h"
#include "evs_car.h"
#include "evs_storage.h"

typedef struct{
    uint8_t index;
    uint8_t PGI;
    MsgParaDef para;
}BmsMsgParaDef;

const BmsMsgParaDef C_BmsMsgPara[]={
        // idx-------PGI ------enable-cycle--maxTime
/*0x00*/{X0_00H,    GB2015P::X0, 1,    0,      15000},
/*0x02*/{X2_02H,    GB2015P::X2, 1,    0,      5000},
/*0x04*/{X4_04H,    GB2015P::X4, 1,    0,      5000},
/*0x06*/{X6_06H,    GB2015P::X6, 1,    1000,   5000},
/*0x09*/{X9_09H,    GB2015P::X9, 1,    0,      5000},
/*0x12*/{B2_12H,    GB2015P::B2, 1,    0,      5000},
/*0x22*/{C2_22H,    GB2015P::C2, 1,    0,      5000},
/*0x24*/{C4_24H,    GB2015P::C4, 1,    0,      5000},
/*0x32*/{D2_32H,    GB2015P::D2, 1,    0,      5000},
/*0x34*/{D4_34H,    GB2015P::D4, 1,    0,      5000},
/*0x36*/{D6_36H,    GB2015P::D6, 1,    0,      5000},
/*0x37*/{D7_37H,    GB2015P::D7, 1,    0,      5000},
/*0x39*/{D9_39H,    GB2015P::D9, 1,    0,      5000},
/*0x3A*/{D10_3AH,   GB2015P::D10,1,    0,      5000},
/*0x42*/{E2_42H,    GB2015P::E2, 1,    0,      5000},
/*0x44*/{E4_44H,    GB2015P::E4, 1,    0,      5000},
/*0x52*/{F2_52H,    GB2015P::F2, 1,    0,      5000},
/*0x62*/{G2_62H,    GB2015P::G2, 1,    0,      5000},
/*0x63*/{G3_63H,    GB2015P::G3, 1,    1000,   5000},
/*0x65*/{G5_65H,    GB2015P::G5, 1,    1000,   5000},
/*0x72*/{H2_72H,    GB2015P::H2, 1,    0,      5000},
/*0x73*/{H3_73H,    GB2015P::H3, 1,    1000,   5000},
/*0x74*/{H4_74H,    GB2015P::H4, 1,    1000,   5000},
/*0x77*/{H7_77H,    GB2015P::H7, 1,    1000,   5000},
/*0x79*/{H9_79H,    GB2015P::H9, 1,    0,      5000},
/*0x82*/{H11_82H,   GB2015P::H11,1,    0,      5000},
/*0x84*/{H13_84H,   GB2015P::H13,1,    250,    5000},
/*0x85*/{H14_85H,   GB2015P::H14,1,    1000,   5000},
/*0x87*/{H16_87H,   GB2015P::H16,1,    1000,   5000},
/*0x89*/{H18_89H,   GB2015P::H18,1,    1000,   5000},
/*0x8B*/{H20_8BH,   GB2015P::H20,1,    0,      5000},
/*0x91*/{I1_91H,    GB2015P::I1, 1,    250,    5000},
/*0x94*/{I4_94H,    GB2015P::I4, 1,    0,      5000}
};

static void BMSMsgInit(GB2015P::CarMsgDef &msg){
    StorageParaDef user_data = GetUserData();
    // 0x00 版本协商报文
    msg.protoConferM.canType = user_data.protoConferM.canType;            // CAN类型CAN2.0B：0x00， CANFD：0x01，CANXL：0x02
    msg.protoConferM.versionResult = 0x01;      // 协商结果 0x00:继续协商， 0x01：协商成功， 0x02：协商失败
    // 协议版本号 BYTE1:主版本号，BYTE2：次版本号，BYTE3：临时版本号
    string tempstr =  user_data.protoConferM.gbVersion;
    uint32_t strlen = user_data.protoConferM.gbVersion.find('.');
    if(strlen >= 1){
        msg.protoConferM.protocolVersion[0] = atoi(tempstr.substr(strlen-1, strlen).c_str());
    }
    string tempstr1 = tempstr.substr(strlen+1, tempstr.length());
    strlen = tempstr.substr(strlen+1, tempstr.length()).find('.');
    msg.protoConferM.protocolVersion[1] =  atoi(tempstr1.substr(0, strlen).c_str());

    strlen = tempstr.rfind('.');
    msg.protoConferM.protocolVersion[2] =  atoi(tempstr.substr(strlen+1, strlen+2).c_str());

    // msg.protoConferM.protocolVersion[0] = 2; 
    // msg.protoConferM.protocolVersion[1] = 0;
    // msg.protoConferM.protocolVersion[2] = 0;

    msg.protoConferM.CPVersion = user_data.protoConferM.guidanceVersion;          // 控制导引版本 0x01：2023A控制导引，0x02：2015+控制导引
    msg.protoConferM.TLVersion = user_data.protoConferM.transportVersion;          // 传输层版本 2015+ B
    msg.protoConferM.res = 0xff;
    // 0x02车辆阶段确认报文
    msg.phaseAckM.pid = GB2015P::X2; 
    msg.phaseAckM.phaseACK = 0xAA; 
    // 0x04车辆中止报文 
    msg.carEndM.pid = GB2015P::X4;
    msg.carEndM.endCode = 0;
    msg.carEndM.endReason1 = 0;
    msg.carEndM.endReason2 = 0;
    msg.carEndM.repeat = 0; // 请求充电 0x00：不请求重连，0xAA请求重连，0xFF无效 
    // 0x06接触器状态报文
    msg.contactStatusM.pid = GB2015P::X6;
    msg.contactStatusM.contactStatus1 = 0; // K1状态  0x00:断开， 0xAA:闭合 0xFF：不可信
    msg.contactStatusM.contactStatus2 = 0; // K2状态
    // 0x09唤醒报文
    msg.wakeupM.pid = GB2015P::X9;
    msg.wakeupM.wakeup = 0xAA; // 唤醒标识 0x00:无效， 0xAA唤醒 
    // 0x12车辆功能协商确认结果报文   
    msg.FDCResultM.pid = GB2015P::B2;
   
    msg.FDCResultM.FDCNegoResult2 = user_data.funConferM.configFDC; // 参数配置      支持则填充FDC值，不支持填充个0x00
    msg.FDCResultM.FDCNegoResult3 = user_data.funConferM.authenFDC; // 鉴权
    msg.FDCResultM.FDCNegoResult4 = user_data.funConferM.appointFDC; // 预约
    msg.FDCResultM.FDCNegoResult5 = user_data.funConferM.selfCheckFDC; // 输出回路检测
    msg.FDCResultM.FDCNegoResult6 = user_data.funConferM.powerSupplyFDC; // 供电模式
    msg.FDCResultM.FDCNegoResult7 = user_data.funConferM.energyTransferFDC; // 预充及能量传输
    msg.FDCResultM.FDCNegoResult8 = user_data.funConferM.endFDC; // 结束 

    // 0x22车辆充电参数报文
    msg.carParaM.pid = GB2015P::C2;
    msg.carParaM.currMAX = user_data.paraConfigM.currAllowMAX * 10;        // 车辆最大允许充电电流 0.1A/位  范围：0A - 6500.0A
    msg.carParaM.voltMAX = user_data.paraConfigM.voltAllowMAX * 10;        // 车辆最高允许充电电压 0.1V/位  范围：0V - 6500.0V
    msg.carParaM.inputEnergyMAX = user_data.paraConfigM.energyAllowMAX * 10; // 车辆最高允许输入总能量 0.1kWh/位 0Kwh - 6500.0Kwh 0xFFFF:参数无效 0xFFFE：参数异常
    msg.carParaM.SOC = user_data.paraConfigM.nowSOC * 10;            // 车辆荷电状态 0.1%位 0% - 100.0%位
    msg.carParaM.battVoltageMAX = user_data.paraConfigM.cellVoltAllowMAX * 100; // 电池最小并联单元最高允许电压 0.01V/位 0V - 650.00V 0xFFFF:参数无效 0xFFFE：参数异常
    msg.carParaM.battTempMAX = user_data.paraConfigM.batTempAllowMAX+50;     // 电池单体最高允许温度 1℃/位 -50℃ ~ 200℃  0xFF：参数无效 0xFE：参数异常
    msg.carParaM.restarNum = user_data.paraConfigM.restarNum;       // 重新启动次数 0 - 200次 0xFE：次数不限， 0xFF：参数无效
    //msg.carChgDischgParaM;   // 0x24车辆充放电参数报文
    // 0x32车辆鉴权等待报文（FDC = 1）
    msg.carAuthenWaitM.pid = GB2015P::D2;
    msg.carAuthenWaitM.authenState = 0xAA; // 是否等待 0xAA：继续等待 0x00 不同意等待
    // 0x34车辆鉴权参数报文（FDC = 2）
    msg.EvinM.pid = GB2015P::D4;       
   // char vin[18] = "XIANWANMAPRODUCE7";
    memcpy(msg.EvinM.EVIN,user_data.authenM.eVIN.c_str(),sizeof(msg.EvinM.EVIN));
    //0x36重新鉴权请求
    msg.eVINagainAuthenM.pid = GB2015P::D6;
    msg.eVINagainAuthenM.FDC = 1; // 即将进入本功能模块的新FDC
    // 0x37车辆鉴权参数报文（FDC = 3）
    msg.cloudAuthenM.pid = GB2015P::D7;
    char fac[10] = "XIANWANMA";
    memcpy(msg.cloudAuthenM.manufacturerIDT,fac,sizeof(msg.cloudAuthenM.manufacturerIDT));// 车辆生产商编码
    // 0x3A重新鉴权请求
    msg.cloudAgainAuthenM.pid = GB2015P::D10;
    msg.cloudAgainAuthenM.FDC = 1; // 即将进入本功能模块的新FDC
    // 0x42车辆预约信息报文
    msg.carAppoInfoM.pid = GB2015P::E2;
    msg.carAppoInfoM.desireStartTime = 2;     // 车辆期望多久后开始充电 1min/位 0xFFFF无效
    msg.carAppoInfoM.desireGoTime = 60;        // 车辆期望多久后出发 1min/位 0xFFFF无效
    // 0x44车辆预约协商结果
    msg.carAppoConfirmM.pid = GB2015P::E4;
    msg.carAppoConfirmM.result = 0xAA;       // 车辆预约协商结果 0xAA:协商成功 0x00：协商失败
    msg.carAppoConfirmM.immediateChg = 0xAA; // 车辆进入立即充电标志位 0x00:不支持 0xAA：支持 0xFF：车辆预约成功时发送
    // 0x52车辆检测确认报文；
    msg.selfcheckAckM.pid = GB2015P::F2;
    msg.selfcheckAckM.ack = 0x00; // 确认检测状态 0xAA确认检测完成
    // 0x62车辆充电状态报文
    msg.CarPowerSupStateM.pid = GB2015P::G2;
    msg.CarPowerSupStateM.ready = 0xAA; // 车辆准备就绪状态 0x00:未就绪， 0xAA:就绪
    // 0x63车辆供电需求报文
    msg.CarPowerSupNeedM.pid = GB2015P::G3;
    msg.CarPowerSupNeedM.needVol = 2000;    // 车辆充电电压需求 0.1V/位 0 - 6500.0
    msg.CarPowerSupNeedM.needCurr = 200;   // 车辆充电电流需求 0.1A/位 0 - 6500.0
    // 0x65车辆供电完成报文
    msg.CarPowerEndM.pid = GB2015P::G5;
    msg.CarPowerEndM.PowerSupplyEnd = 0x00; // 供电完成状态 0x00:未完成 0xAA:完成
    // 0x72车辆就绪状态报文
    msg.carReqdyM.pid = GB2015P::H2;
    msg.carReqdyM.ready = 0xAA;    // 车辆准备就绪状态 0x00:未就绪， 0xAA:就绪
    msg.carReqdyM.batVol = 3000; // 整车充电系统当前电压 0.1V/位 0 - 6500.0
    // 0x73车辆充电需求报文
    msg.carRequireM.pid = GB2015P::H3;
    msg.carRequireM.needVol = user_data.chargingM.volDemand * 10;    // 车辆充电电压需求 0.1V/位 0 - 6500.0
    msg.carRequireM.needCurr = user_data.chargingM.curDemand * 10;   // 车辆充电电流需求 0.1A/位 0 - 6500.0
    msg.carRequireM.chargeMode = user_data.chargingM.chargeMode;  // 充电模式 0x01:恒流 0x02：恒压
    // 0x74车辆充电基本信息
    msg.carBasicInfoM.pid = GB2015P::H4;
    msg.carBasicInfoM.soc = user_data.chargingM.socNow * 10;           // 当前荷电状态 0.1%/位 0 - 100%
    msg.carBasicInfoM.residueTimt = 30;   // 剩余估算时间 1min/位
    // 0x77车辆充电电池基本信息报文
    msg.carBatBasicInfoM.pid = GB2015P::H7;
    msg.carBatBasicInfoM.cellVoltageMAX = user_data.chargingM.cellBatVolMax * 100;      // 电池最小并联单元最高电压 0.01V/位 0xFFFF:参数无效 0xFFFE：数据异常
    msg.carBatBasicInfoM.cellVoltageMIN = user_data.chargingM.cellBatVolMin * 100;      // 电池最小并联单元最低电压
    msg.carBatBasicInfoM.cellTempMAX = user_data.chargingM.celltempMax+50;          // 电池单体最高温度 1℃/位 0xFF：参数无效 0xFE：数据异常
    msg.carBatBasicInfoM.cellTempMIN = user_data.chargingM.celltempMin+50;          // 电池单体最低温度
    // 0x79车辆暂停报文
    msg.carPauseM.pid = GB2015P::H9;
    msg.carPauseM.pause = 0x00; // 暂停状态 0xAA：暂停 0x00:恢复
    // 0x91车辆粘连检测报文
    msg.caStickCheckM.pid = GB2015P::I1;
    msg.caStickCheckM.checkState = 0x00; // 粘连检测状态信息 0x00:待检测 0x01:检测中 0x02:异常中止（无法完成检测）0x03:检测通过（未粘连）0x04:检测失败（粘连） 0xFF：本次不检测
    // 0x94车辆统计报文
    msg.carStatisM.pid = GB2015P::I4;
    msg.carStatisM.soc = user_data.chargingEndM.endCode;  // 车辆中止时的荷电量 0.1% 0 - 100%；
   
}

static  void CtrlParaInit(MsgParaDef *para ,uint32_t len){
    if(len != CAR_MSG_NUM){
        return ;
    }
    for(uint8_t idx = 0; idx < CAR_MSG_NUM; ++idx){
        para[idx] = C_BmsMsgPara[idx].para;
    }
}

static  void FunAckInit(EVFunConferAckMsgDef &ack){
    ack.funConferAck = 0x66; //功能协商功能确认结果
    ack.configAck = 0x66; //配置功能确认结果 
    ack.authenAck = 0x66; //鉴权功能确认结果
    ack.appointAck = 0x66; //预约功能确认结果
    ack.selfCheckAck = 0x66; //输出回路检测功能确认结果
    ack.powerSupplyAck = 0x66; //供电模式功能确认结果
    ack.energyTransferAck = 0x66; //预充及能量传输功能确认结果 
    ack.endAck = 0x66; //结束功能确认结果
}

/***************************************************************************
函数名:UserDataInit
功能描述:用户数据初始化
作者:
日期:
****************************************************************************/
void EVSCar::UserDataInit(void){
    BMSMsgInit(CarMsgFromUser_);
    CtrlParaInit(CarMsgCtrlPara,(sizeof(CarMsgCtrlPara)/sizeof(CarMsgCtrlPara[0])));
    FunAckInit(EvFunNegoAck_);
}