/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class loger Definition here.
 */
#include "loger.h"
#include "evs_car.h"
#include "evs_manager.h"
#include "mt_timer.h"
#include "adc.h"
#include "gb2015p_msgtype.h"
#include "evs_proto.h"
#include "evs_database.h"
#include "evs_storage.h"

#define CYCLE_TIME_NS (2950000)
#define NSEC_PER_SEC (1000 * 1000 * 1000)

EVSCar::EVSCar(void *ctx):_m_ctx(ctx) {
    
}

EVSCar::~EVSCar() {}
/***************************************************************************
函数名:EvsSleep
功能描述:
作者:
日期:
****************************************************************************/
static uint32_t cycleMaxTime = 0;
static uint64_t cycleCnt = 0;
void EVSCar::EvsSleep(struct timespec enter_time){
    struct timespec clockTime;
    clock_gettime(CLOCK_MONOTONIC, &clockTime);
    int64_t CycleTime = (clockTime.tv_sec - enter_time.tv_sec)*1000000000 + clockTime.tv_nsec - enter_time.tv_nsec;
    cycleMaxTime = (cycleMaxTime < CycleTime)?(CycleTime):(cycleMaxTime);

    if(CycleTime < CYCLE_TIME_NS){
        clockTime = enter_time;
        clockTime.tv_nsec += CYCLE_TIME_NS;
    }else{
        clockTime.tv_nsec += 2000000;
        Loger(NORMAL,"Car cycle= %u---- %d",++cycleCnt,CycleTime);
    }
    clock_nanosleep(CLOCK_MONOTONIC, TIMER_ABSTIME, &clockTime, NULL);
}
/***************************************************************************
函数名:FcInputUpdate
功能描述:
作者:
日期:
****************************************************************************/
int32_t EVSCar::FcInputUpdate(void){
    
    EvsFC_.UserSetBmsData(CarMsgFromUser_);

    FcCaredParaDef to_fc_data;
    to_fc_data.workCmd = DataToFC_.workCmd;
    to_fc_data.offlinMode = 0;
    to_fc_data.batVol = F_AD_VALUE[0][E_CH1_GUNA_VF];
    to_fc_data.cc1State = F_AD_VALUE[0][E_CH0_GUNA_CC1];   // cc1 state
    to_fc_data.carReady = (BatteryReady()==ACK)?1:0;
    to_fc_data.k5Cmd = RlyCmd_[RLY_BAT_P] | RlyCmd_[RLY_LOAD_P];
    to_fc_data.k6Cmd = RlyCmd_[RLY_BAT_N] | RlyCmd_[RLY_LOAD_P];
    to_fc_data.k5Fb = RlyFb_[RLY_BAT_P];
    to_fc_data.k6Fb = RlyFb_[RLY_BAT_N];
    to_fc_data.k5Stick = RlyStick_[RLY_BAT_P];
    to_fc_data.k6Stick = RlyStick_[RLY_BAT_N];
    to_fc_data.meterVol = ((EVSManager*)_m_ctx)->EvsMeter_.GetVol() * 10;
    to_fc_data.meterCur = ((EVSManager*)_m_ctx)->EvsMeter_.GetCurr() * 10;
    to_fc_data.chgEnergy = ((EVSManager*)_m_ctx)->EvsMeter_.GetPower() * 10;
    to_fc_data.evPause = eVSProto.eVSManageData.electriCtrlManageBuff.electricCtrl.evPause;
    
    EvsFC_.SetSysData(to_fc_data);
    for(uint32_t i = X0_00H; i< CAR_MSG_NUM;++i){
        EvsFC_.UserSetMsgPara(CarMsgCtrlPara[i],(CarMsgIndexDef)i);
    }
    EvsFC_.UserSetNegoAck(EvFunNegoAck_);
    return 0;
}

/***************************************************************************
函数名:CarProcess
功能描述:
作者:
日期:
****************************************************************************/
int32_t EVSCar::CarInit(void){
    OnLineCnt_ = 0;
    batCnt_ = 0;
    memset(&DataToFC_,0,sizeof(DataToFC_));
    DataToFC_.workCmd = 0xAA;
    
    SwitchR = 0;    // switch R
    evPause_ = 0;
    std::cout<< "UserDataInit !!!!" << std::endl;
    JsonInit();
    UserSetData_ = GetUserData();
    // std::cout<< "UserSetData_ = "<<UserSetData_.chargingM.cellBatVolMax << std::endl;
    // std::cout<< "UserSetData_.serverIP = "<<UserSetData_.ipConfig.serverIP << std::endl;
    ethInit();   //网络初始化
    UserDataInit();
    eVSProto.ProtoInit();
    RlyCmd_[RLY_ADC_SWITCH] = RLY_CLOSE;

    Insulation_.DatInit(0, {0, 250, 250}); // 绝缘初始化
    return 0;


}
/***************************************************************************
函数名:ethInit
功能描述:网络初始化
作者:
日期:
****************************************************************************/
int32_t EVSCar::ethInit(void)
{
    eVSProto.serverIP = UserSetData_.ipConfig.serverIP;
    eVSProto.serverPort = UserSetData_.ipConfig.serverPort;
    eVSProto.socket_initok = true;
    if(UserSetData_.ipConfig.locolIP != getlocalIP("eth0"))
    {
        std::string _strcmd = "ifconfig eth0 "+UserSetData_.ipConfig.locolIP;
        system(_strcmd.c_str());
        Loger(NORMAL,"local ip addr reset: %s",UserSetData_.ipConfig.locolIP);
    }
    return 0;
}
/***************************************************************************
函数名:CarProcess
功能描述:
作者:
日期:
****************************************************************************/
int32_t EVSCar::CarProcess(void){
    int32_t ret = -1;
    uint32_t cnt = 0;
    EVSManager *pManager = (EVSManager*)_m_ctx;
    struct timespec enterTime;
    CarInit();

    while(1){

        clock_gettime(CLOCK_MONOTONIC, &enterTime);
        Timer_.TimerBase();

        ProtoSchedule(); // 报文入口
        FcSchedule(); // 交互入口
        if(Timer_.Flg_.f100ms){
            BatteryManage(); // 高压源控制
            RlyManage(); // 接触器控制
            // Insulation_.InsultForTest(); // 绝缘检测
        }
        if(Timer_.Flg_.f1s){
            ++OnLineCnt_;
            int32_t bat_vol = pManager->EvsMeter_.GetVol();
            if((++cnt)%10 == 0){
                Loger(NORMAL,"Car HB:: cycle = %lld,bat = %d,cc1 = %d,cycleMax = %d",
                cycleCnt,bat_vol,F_AD_VALUE[0][ADC_CC1],cycleMaxTime);
                cycleMaxTime = 0;
            }
        }
        EvsSleep(enterTime);
    }
    return ret;
}
