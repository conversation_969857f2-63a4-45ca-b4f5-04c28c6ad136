
#include "loger.h"
#include "evs_fc.h"

int32_t EvsFc::FCOnOffManage(void){
    switch (This.OnOff){
        case EVS_OFF:
            if(FcCaredPara_.workCmd == 0xAA && (pFault_->GetStopFault() == false)){
                This.OnOff = EVS_ON;
                Loger(NORMAL, "FC on !");
            }
        break;
        case EVS_ON:
            if(FcCaredPara_.workCmd == 0x55 || pFault_->GetStopFault()){
                This.OnOff = EVS_OFF;
            }
        break;
        default :
        break;
    }
    return 0;
}
int32_t EvsFc::InputUpdate(void){
    if(This.OnOff == EVS_ON){
        if(FcCaredPara_.workCmd == 0x55){
            pFault_->FailReport(FAIL_EV_MANUAL,FAIL_ACK);
        }
    }
    return 0;
}
int32_t EvsFc::UserSetMsgPara(MsgParaDef para,CarMsgIndexDef index){
    if(index >= CAR_MSG_NUM){
        return -1;
    }
    MsgPara_[index] = para;
    return 0;
}
void EvsFc::UserSetNegoAck(EVFunConferAckMsgDef ack){
    FunConferAck_ = ack;
}
FcOutDataDef EvsFc::GetOutData(void){
    FcOutData_.workMode = This.OnOff;
    FcOutData_.FCState = This.fcState;
    if(This.fcState.FC == GB2015P::FCType::FC7 && 
            This.fcState.FDC == GB2015P::FDCType::FDC1){
        FcOutData_.seReady = GetSeMsg().seReadyM.Ready;
    }else{
        FcOutData_.seReady = 0;
    }
    if(GetSeMsg().contactStateM.contactStatus1 == 0xAA && \
        GetSeMsg().contactStateM.contactStatus2 == 0xAA) {
        FcOutData_.chargerRlyState = 0x01;
    }
    return FcOutData_;
}