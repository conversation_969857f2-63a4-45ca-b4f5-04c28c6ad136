/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class loger Definition here.
 */
#include "loger.h"
#include "can_ex.h"
#include "evs_common.h"

uint8_t *u8null = nullptr;
const BmsMsgInfoDef BmsMsgInfo[]={
        // PGI ------enable-cycle-maxTime---type-----timeout------SA---------------PS--------msgType----P-------datalen--------------------------*data
/*0x00*/{GB2015P::X0, 1,  0, 15000,   NoConfirm,    1000,  GB2015P::EVCC,    GB2015P::SECC,  CONFER_M,  3,  sizeof(GB2015P::ProtoConferMsgDef),      u8null},
/*0x02*/{GB2015P::X2, 1,  0, 5000,    YesConfirm,   1000,  GB2015P::EVCC,    GB2015P::SECC,  SMPF,      6,  sizeof(GB2015P::PhaseAckMsgDef),         u8null},
/*0x04*/{GB2015P::X4, 1,  0, 5000,    YesConfirm,   1000,  GB2015P::EVCC,    GB2015P::SECC,  SMPF,      6,  sizeof(GB2015P::CarEndMsgDef),           u8null},
/*0x06*/{GB2015P::X6, 1,1000, 5000,   NoConfirm,    0,     GB2015P::EVCC,    GB2015P::SECC,  SMURMPF,   3,  sizeof(GB2015P::ContactStateDef),        u8null},
/*0x09*/{GB2015P::X9, 1,  0, 5000,    YesConfirm,   1000,  GB2015P::EVCC,    GB2015P::SECC,  SMPF,      6,  sizeof(GB2015P::WakeupDef),              u8null},
/*0x12*/{GB2015P::B2, 1,  0, 5000,    YesConfirm,   1000,  GB2015P::EVCC,    GB2015P::SECC,  SMPF,      6,  sizeof(GB2015P::FDCResultDef),           u8null},
/*0x22*/{GB2015P::C2, 1,  0, 5000,    LongFram,     5000,  GB2015P::EVCC,    GB2015P::SECC,  LMPF,      6,  sizeof(GB2015P::CarParaMsgDef),          u8null},
/*0x24*/{GB2015P::C4, 0,  0, 5000,    LongFram,     5000,  GB2015P::EVCC,    GB2015P::SECC,  LMPF,      6,  sizeof(GB2015P::CarChgDischgParaMsgDef), u8null},
/*0x32*/{GB2015P::D2, 1,  0, 5000,    NoConfirm,    0,     GB2015P::EVCC,    GB2015P::SECC,  SMURMPF,   3,  sizeof(GB2015P::CarAuthenWaitMsgDef),    u8null},
/*0x34*/{GB2015P::D4, 1,  0, 5000,    LongFram,     1000,  GB2015P::EVCC,    GB2015P::SECC,  LMPF,      6,  sizeof(GB2015P::EvinMsgDef),             u8null},
/*0x36*/{GB2015P::D6, 1,  0, 5000,    YesConfirm,   1000,  GB2015P::EVCC,    GB2015P::SECC,  SMPF,      6,  sizeof(GB2015P::EvinAgainAutheMsgDef),   u8null},
/*0x37*/{GB2015P::D7, 1,  0, 5000,    LongFram,     1000,  GB2015P::EVCC,    GB2015P::SECC,  LMPF,      6,  sizeof(GB2015P::CarCloudAuthenMsgDef),   u8null},
/*0x3A*/{GB2015P::D10,1,  0, 5000,    YesConfirm,   1000,  GB2015P::EVCC,    GB2015P::SECC,  SMPF,      6,  sizeof(GB2015P::CloudAgainAuthenDef),    u8null},
/*0x42*/{GB2015P::E2, 1,  0, 5000,    YesConfirm,   1000,  GB2015P::EVCC,    GB2015P::SECC,  SMPF,      6,  sizeof(GB2015P::CarAppoInfoDef),         u8null},
/*0x44*/{GB2015P::E4, 1,  0, 5000,    YesConfirm,   1000,  GB2015P::EVCC,    GB2015P::SECC,  SMPF,      6,  sizeof(GB2015P::CarAppoResultDef),       u8null},
/*0x52*/{GB2015P::F2, 1,  0, 5000,    YesConfirm,   1000,  GB2015P::EVCC,    GB2015P::SECC,  SMPF,      6,  sizeof(GB2015P::CarSelfCheckAckDef),     u8null},
/*0x62*/{GB2015P::G2, 0,  0, 5000,    NoConfirm,    0,     GB2015P::EVCC,    GB2015P::SECC,  SMURMPF,   3,  sizeof(GB2015P::CarPowerSupStateDef),    u8null},
/*0x63*/{GB2015P::G3, 0,1000, 5000,   NoConfirm,    5000,  GB2015P::EVCC,    GB2015P::SECC,  SMURMPF,   3,  sizeof(GB2015P::CarPowerSupNeedDef),     u8null},
/*0x65*/{GB2015P::G5, 0,1000, 5000,   YesConfirm,   0,     GB2015P::EVCC,    GB2015P::SECC,  SMPF,      6,  sizeof(GB2015P::CarPowerEndDef),         u8null},
/*0x72*/{GB2015P::H2, 1,  0, 5000,    NoConfirm,    0,     GB2015P::EVCC,    GB2015P::SECC,  SMURMPF,   3,  sizeof(GB2015P::CarReadyMsgDef),         u8null},
/*0x73*/{GB2015P::H3, 1,1000, 5000,   NoConfirm,    5000,  GB2015P::EVCC,    GB2015P::SECC,  SMURMPF,   3,  sizeof(GB2015P::CarRequireMsgDef),       u8null},
/*0x74*/{GB2015P::H4, 1,1000, 5000,   NoConfirm,    0,     GB2015P::EVCC,    GB2015P::SECC,  SMURMPF,   3,  sizeof(GB2015P::CarBasicInfoDef),        u8null},
/*0x77*/{GB2015P::H7, 1,1000, 5000,   NoConfirm,    0,     GB2015P::EVCC,    GB2015P::SECC,  SMURMPF,   3,  sizeof(GB2015P::CarBattBasicInfoDef),    u8null},
/*0x79*/{GB2015P::H9, 1,  0, 5000,    YesConfirm,   5000,  GB2015P::EVCC,    GB2015P::SECC,  SMPF,      6,  sizeof(GB2015P::CarPauseDef),            u8null},
/*0x82*/{GB2015P::H11,0, 0, 5000,     NoConfirm,     0,    GB2015P::EVCC,    GB2015P::SECC,  SMURMPF,   3,  sizeof(GB2015P::CarDisChgReadyDef),      u8null},
/*0x84*/{GB2015P::H13,0,250, 5000,    NoConfirm,    5000,  GB2015P::EVCC,    GB2015P::SECC,  SMURMPF,   3,  sizeof(GB2015P::CarDynamicOutDef),       u8null},
/*0x85*/{GB2015P::H14,0,1000, 5000,   NoConfirm,    5000,  GB2015P::EVCC,    GB2015P::SECC,  SMURMPF,   3,  sizeof(GB2015P::CarDisChgRequireDef),    u8null},
/*0x87*/{GB2015P::H16,0,1000, 5000,   NoConfirm,    0,     GB2015P::EVCC,    GB2015P::SECC,  SMURMPF,   3,  sizeof(GB2015P::CarDisChgInfoDef),       u8null},
/*0x89*/{GB2015P::H18,0,1000, 5000,   NoConfirm,    0,     GB2015P::EVCC,    GB2015P::SECC,  SMURMPF,   3,  sizeof(GB2015P::CarDisChgBattInfoDef),   u8null},
/*0x8B*/{GB2015P::H20,0,  0, 5000,    YesConfirm,   5000,  GB2015P::EVCC,    GB2015P::SECC,  SMPF,      6,  sizeof(GB2015P::CarDisChgPauseDef),      u8null},
/*0x91*/{GB2015P::I1, 1, 250, 5000,   NoConfirm,    0,     GB2015P::EVCC,    GB2015P::SECC,  SMURMPF,   3,  sizeof(GB2015P::CarStickCheckDef),       u8null},
/*0x94*/{GB2015P::I4, 1,  0, 5000,    YesConfirm,   1000,  GB2015P::EVCC,    GB2015P::SECC,  SMPF,      6,  sizeof(GB2015P::CarStatisMsgDef),        u8null}
};

const MsgInfoDef SeMsg[] ={
        // PGI -----------------msglen--------------------------msg
    {GB2015P::X0, 0,    sizeof(GB2015P::ProtoConferMsgDef)    ,u8null},// 0x00 版本协商
    {GB2015P::X1, 0,     sizeof(GB2015P::PhaseRequestMsgDef)   ,u8null},// 0x01阶段请求报文
    {GB2015P::X3, 0,     sizeof(GB2015P::SeEndMsgDef)          ,u8null},// 0x03充电机中止报文
    {GB2015P::X5, 0,     sizeof(GB2015P::ContactStateDef)      ,u8null},// 0x05接触器状态报文
    {GB2015P::X7, 0,     sizeof(GB2015P::ElockStateDef)        ,u8null},// 0x07电子锁状态报文
    {GB2015P::X8, 0,     sizeof(GB2015P::WakeupDef)            ,u8null},// 0x08唤醒报文
    {GB2015P::B1, 0,     sizeof(GB2015P::SeSupportFDCmsgDef)   ,u8null},// 0x11充电机支持功能报文
    {GB2015P::C1, 0,     sizeof(GB2015P::SeParaMsgDef)         ,u8null},// 0x21充电机充电参数报文
    {GB2015P::C3, 0,     sizeof(GB2015P::SeChgDischgParaMsgDef),u8null},// 0x23充电机充放电参数报文
    {GB2015P::D1, 0,     sizeof(GB2015P::SeAuthenParaMsgDef)   ,u8null},// 0x31充电机鉴权参数报文（FDC = 1）
    {GB2015P::D3, 0,     sizeof(GB2015P::AuthenResultMsgDef)   ,u8null},// 0x33鉴权结果报文
    {GB2015P::D5, 0,     sizeof(GB2015P::EvinAuthenResultDef)  ,u8null},// 0x35鉴权结果报文
    {GB2015P::D8, 0,     sizeof(GB2015P::SeCloudAuthenInfoDef) ,u8null},// 0x38充电机鉴权参数报文（FDC = 3）
    {GB2015P::D9, 0,     sizeof(GB2015P::CloudAuthenResultDef) ,u8null},// 0x39鉴权结果报文
    {GB2015P::E1, 0,     sizeof(GB2015P::SeAppoInfoDef)        ,u8null},// 0x41充电机预约充电信息报文
    {GB2015P::E3, 0,     sizeof(GB2015P::SeAppoConfirmDef)     ,u8null},// 0x43充电机预约确认报文
    {GB2015P::F1, 0,     sizeof(GB2015P::SeSelfCheckInfoMsg)   ,u8null},// 0x51充电机检测报文；
    {GB2015P::G1, 0,     sizeof(GB2015P::SePowerSupStateDef)   ,u8null},// 0x61充电机供电状态报文
    {GB2015P::G4, 0,     sizeof(GB2015P::SePowerDynamicOutDef) ,u8null},// 0x64充电机动态输出能力报文
    {GB2015P::G6, 0,     sizeof(GB2015P::SePowerInfoDef)       ,u8null},// 0x66充电机供电基本信息
    {GB2015P::H1, 0,     sizeof(GB2015P::SeReadyMsgDef)        ,u8null},// 0x71充电机就绪状态报文(FDC = 1)
    {GB2015P::H5, 0,     sizeof(GB2015P::SeDynamicOutMsgDef)   ,u8null},// 0x75充电机动态输出报文
    {GB2015P::H6, 0,     sizeof(GB2015P::SeOutInfoDef)         ,u8null},// 0x76充电机充电基本信息
    {GB2015P::H8, 0,     sizeof(GB2015P::SePauseMsgDef)        ,u8null},// 0x78充电机暂停报文
    {GB2015P::H10, 0,     sizeof(GB2015P::SeDisChgReadyDef)    ,u8null},// 0x81充电机就绪状态报文(FDC = 2)
    {GB2015P::H12, 0,     sizeof(GB2015P::SeDisChgNeedDef)     ,u8null},// 0x83充电机放电需求报文
    {GB2015P::H15, 0,     sizeof(GB2015P::SeDynamicOutDef)     ,u8null},// 0x86充电机动态输出能力报文
    {GB2015P::H17, 0,     sizeof(GB2015P::SeDisChgInfoDef)     ,u8null},// 0x88充电机充放电基本信息
    {GB2015P::H19, 0,     sizeof(GB2015P::SeDisChgPauseMsgDef) ,u8null},// 0x8A充电机充放电暂停报文
    {GB2015P::I2, 0,     sizeof(GB2015P::SeCheckEnableDef)     ,u8null},// 0x92充电机允许粘连检测报文
    {GB2015P::I3, 0,     sizeof(GB2015P::SeStatisMsgDef)       ,u8null} // 0x93充电机统计报文     
};

CanEx::CanEx() {
    uint32_t len = sizeof(BmsMsgInfo)/sizeof(BmsMsgInfo[0]);
    CarMsgMap_.clear();
    CarMsgMap_.reserve(len);
    for(uint32_t idx = 0; idx < len; ++idx){
        //CarMsgMap_.insert(std::make_pair((GB2015P::PIDType)BmsMsgInfo[idx].PGI,BmsMsgInfo[idx]));
        CarMsgMap_[(GB2015P::PIDType)BmsMsgInfo[idx].PGI] = BmsMsgInfo[idx];
    }
    CarMsgMap_[GB2015P::X0].msg.data = (uint8_t*)&CarMsg_.protoConferM;         // 0x00 版本协商报文
    CarMsg_.phaseAckM.pid = GB2015P::X2;
    CarMsgMap_[GB2015P::X2].msg.data = (uint8_t*)&CarMsg_.phaseAckM;            // 0x02车辆阶段确认报文
    CarMsg_.carEndM.pid = GB2015P::X4;
    CarMsgMap_[GB2015P::X4].msg.data = (uint8_t*)&CarMsg_.carEndM;              // 0x04车辆中止报文
    CarMsg_.contactStatusM.pid = GB2015P::X6;
    CarMsgMap_[GB2015P::X6].msg.data = (uint8_t*)&CarMsg_.contactStatusM;       // 0x06接触器状态报文
    CarMsg_.wakeupM.pid = GB2015P::X9;
    CarMsgMap_[GB2015P::X9].msg.data = (uint8_t*)&CarMsg_.wakeupM;              // 0x09唤醒报文
    CarMsg_.FDCResultM.pid = GB2015P::B2;
    CarMsgMap_[GB2015P::B2].msg.data = (uint8_t*)&CarMsg_.FDCResultM;           // 0x12车辆功能协商确认结果报文
    CarMsg_.carParaM.pid = GB2015P::C2;
    CarMsgMap_[GB2015P::C2].msg.data = (uint8_t*)&CarMsg_.carParaM;             // 0x22车辆充电参数报文
    CarMsg_.carChgDischgParaM.pid = GB2015P::C4;
    CarMsgMap_[GB2015P::C4].msg.data = (uint8_t*)&CarMsg_.carChgDischgParaM;    // 0x24车辆充放电参数报文
    CarMsg_.carAuthenWaitM.pid = GB2015P::D2;
    CarMsgMap_[GB2015P::D2].msg.data = (uint8_t*)&CarMsg_.carAuthenWaitM;       // 0x32车辆鉴权等待报文（FDC = 1）
    CarMsg_.EvinM.pid = GB2015P::D4;
    CarMsgMap_[GB2015P::D4].msg.data = (uint8_t*)&CarMsg_.EvinM;                // 0x34车辆鉴权参数报文（FDC = 2）
    CarMsg_.eVINagainAuthenM.pid = GB2015P::D6;
    CarMsgMap_[GB2015P::D6].msg.data = (uint8_t*)&CarMsg_.eVINagainAuthenM;     // 0x36重新鉴权请求
    CarMsg_.cloudAuthenM.pid = GB2015P::D7;
    CarMsgMap_[GB2015P::D7].msg.data = (uint8_t*)&CarMsg_.cloudAuthenM;         // 0x37车辆鉴权参数报文（FDC = 3）
    CarMsg_.cloudAgainAuthenM.pid = GB2015P::D10;
    CarMsgMap_[GB2015P::D10].msg.data = (uint8_t*)&CarMsg_.cloudAgainAuthenM;   // 0x3A重新鉴权请求
    CarMsg_.carAppoInfoM.pid = GB2015P::E2;
    CarMsgMap_[GB2015P::E2].msg.data = (uint8_t*)&CarMsg_.carAppoInfoM;         // 0x42车辆预约信息报文
    CarMsg_.carAppoConfirmM.pid = GB2015P::E4;
    CarMsgMap_[GB2015P::E4].msg.data = (uint8_t*)&CarMsg_.carAppoConfirmM;      // 0x44车辆预约协商结果
    CarMsg_.selfcheckAckM.pid = GB2015P::F2;
    CarMsgMap_[GB2015P::F2].msg.data = (uint8_t*)&CarMsg_.selfcheckAckM;        // 0x52车辆检测确认报文；
    CarMsg_.CarPowerSupStateM.pid = GB2015P::G2;
    CarMsgMap_[GB2015P::G2].msg.data = (uint8_t*)&CarMsg_.CarPowerSupStateM;    // 0x62车辆供电状态报文
    CarMsg_.CarPowerSupNeedM.pid = GB2015P::G3;
    CarMsgMap_[GB2015P::G3].msg.data = (uint8_t*)&CarMsg_.CarPowerSupNeedM;     // 0x63车辆供电需求报文
    CarMsg_.CarPowerEndM.pid = GB2015P::G5;
    CarMsgMap_[GB2015P::G5].msg.data = (uint8_t*)&CarMsg_.CarPowerEndM;         // 0x65车辆供电完成报文
    CarMsg_.carReqdyM.pid = GB2015P::H2;
    CarMsgMap_[GB2015P::H2].msg.data = (uint8_t*)&CarMsg_.carReqdyM;            // 0x72车辆就绪状态报文（FDC = 1）
    CarMsg_.carRequireM.pid = GB2015P::H3;
    CarMsgMap_[GB2015P::H3].msg.data = (uint8_t*)&CarMsg_.carRequireM;          // 0x73车辆充电需求报文
    CarMsg_.carBasicInfoM.pid = GB2015P::H4;
    CarMsgMap_[GB2015P::H4].msg.data = (uint8_t*)&CarMsg_.carBasicInfoM;        // 0x74车辆充电基本信息
    CarMsg_.carBatBasicInfoM.pid = GB2015P::H7;
    CarMsgMap_[GB2015P::H7].msg.data = (uint8_t*)&CarMsg_.carBatBasicInfoM;     // 0x77车辆充电电池基本信息报文
    CarMsg_.carPauseM.pid = GB2015P::H9;
    CarMsgMap_[GB2015P::H9].msg.data = (uint8_t*)&CarMsg_.carPauseM;            // 0x79车辆暂停报文
    CarMsg_.CarDisChgReadyM.pid = GB2015P::H11;
    CarMsgMap_[GB2015P::H11].msg.data = (uint8_t*)&CarMsg_.CarDisChgReadyM;     // 0x82车辆就绪状态报文（FDC = 2）
    CarMsg_.CarDynamicOutM.pid = GB2015P::H13;
    CarMsgMap_[GB2015P::H13].msg.data = (uint8_t*)&CarMsg_.CarDynamicOutM;      // 0x84车辆动态输出报文
    CarMsg_.CarDisChgRequireM.pid = GB2015P::H14;
    CarMsgMap_[GB2015P::H14].msg.data = (uint8_t*)&CarMsg_.CarDisChgRequireM;   // 0x85车辆充电需求报文
    CarMsg_.CarDisChgInfoM.pid = GB2015P::H16;
    CarMsgMap_[GB2015P::H16].msg.data = (uint8_t*)&CarMsg_.CarDisChgInfoM;      // 0x87车辆充放电基本信息
    CarMsg_.CarDisChgBattInfoM.pid = GB2015P::H18;
    CarMsgMap_[GB2015P::H18].msg.data = (uint8_t*)&CarMsg_.CarDisChgBattInfoM;  // 0x89车辆充放电电池基本信息报文
    CarMsg_.CarDisChgPauseM.pid = GB2015P::H20;
    CarMsgMap_[GB2015P::H20].msg.data = (uint8_t*)&CarMsg_.CarDisChgPauseM;     // 0x8A车辆充放电暂停报文
    CarMsg_.caStickCheckM.pid = GB2015P::I1;
    CarMsgMap_[GB2015P::I1].msg.data = (uint8_t*)&CarMsg_.caStickCheckM;        // 0x84车辆粘连检测报文
    CarMsg_.carStatisM.pid = GB2015P::I4;
    CarMsgMap_[GB2015P::I4].msg.data = (uint8_t*)&CarMsg_.carStatisM;           // 0x85车辆统计报文
    
    SEmsgMap_.clear();
    SEmsgMap_.reserve(sizeof(SeMsg)/sizeof(SeMsg[0]));
    len = sizeof(SeMsg)/sizeof(SeMsg[0]);
    for(uint32_t idx = 0; idx < len; ++idx){
        SEmsgMap_.insert(std::make_pair((GB2015P::PIDType)SeMsg[idx].PGI,SeMsg[idx]));
    }
    SEmsgMap_[GB2015P::X0].msg = (uint8_t*)(&SeMsg_.protoConferM);
    SEmsgMap_[GB2015P::X1].msg = (uint8_t*)(&SeMsg_.phaseRequestM);
    SEmsgMap_[GB2015P::X3].msg = (uint8_t*)(&SeMsg_.seEndMsgM);
    SEmsgMap_[GB2015P::X5].msg = (uint8_t*)(&SeMsg_.contactStateM);
    SEmsgMap_[GB2015P::X7].msg = (uint8_t*)(&SeMsg_.elockStateM);
    SEmsgMap_[GB2015P::X8].msg = (uint8_t*)(&SeMsg_.wakeUpM);
    SEmsgMap_[GB2015P::B1].msg = (uint8_t*)(&SeMsg_.seSuppFdcM);
    SEmsgMap_[GB2015P::C1].msg = (uint8_t*)(&SeMsg_.seParaM);
    SEmsgMap_[GB2015P::C3].msg = (uint8_t*)(&SeMsg_.seChgDisChgParaM);
    SEmsgMap_[GB2015P::D1].msg = (uint8_t*)(&SeMsg_.seAuthenParaM);
    SEmsgMap_[GB2015P::D3].msg = (uint8_t*)(&SeMsg_.authenResultM);
    SEmsgMap_[GB2015P::D5].msg = (uint8_t*)(&SeMsg_.evinAuthenResultM);
    SEmsgMap_[GB2015P::D8].msg = (uint8_t*)(&SeMsg_.seCloudAuthenInfoM);
    SEmsgMap_[GB2015P::D9].msg = (uint8_t*)(&SeMsg_.cloudAuthenResultM);
    SEmsgMap_[GB2015P::E1].msg = (uint8_t*)(&SeMsg_.seAppoInfoM);
    SEmsgMap_[GB2015P::E3].msg = (uint8_t*)(&SeMsg_.seAppoConfirmM);
    SEmsgMap_[GB2015P::F1].msg = (uint8_t*)(&SeMsg_.seSelfCheckInfoM);
    SEmsgMap_[GB2015P::G1].msg = (uint8_t*)(&SeMsg_.SePowerSupStateM);
    SEmsgMap_[GB2015P::G4].msg = (uint8_t*)(&SeMsg_.SePowerDynamicOutM);
    SEmsgMap_[GB2015P::G6].msg = (uint8_t*)(&SeMsg_.SePowerInfoM);
    SEmsgMap_[GB2015P::H1].msg = (uint8_t*)(&SeMsg_.seReadyM);
    SEmsgMap_[GB2015P::H5].msg = (uint8_t*)(&SeMsg_.seDynamicOutM);
    SEmsgMap_[GB2015P::H6].msg = (uint8_t*)(&SeMsg_.seoutInfoM);
    SEmsgMap_[GB2015P::H8].msg = (uint8_t*)(&SeMsg_.sePauseM);
    SEmsgMap_[GB2015P::H10].msg = (uint8_t*)(&SeMsg_.SeDisChgReadyM);
    SEmsgMap_[GB2015P::H12].msg = (uint8_t*)(&SeMsg_.SeDisChgNeedM);
    SEmsgMap_[GB2015P::H15].msg = (uint8_t*)(&SeMsg_.SeDynamicOutM);
    SEmsgMap_[GB2015P::H17].msg = (uint8_t*)(&SeMsg_.SeDisChgInfoM);
    SEmsgMap_[GB2015P::H19].msg = (uint8_t*)(&SeMsg_.SeDisChgPauseM);
    SEmsgMap_[GB2015P::I2].msg = (uint8_t*)(&SeMsg_.seCheckEnableM);
    SEmsgMap_[GB2015P::I3].msg = (uint8_t*)(&SeMsg_.seStatisM);

    memset(&CarMsg_,0,sizeof(CarMsg_));
    memset(&SeMsg_,0,sizeof(SeMsg_));
    memset(&CarMsgFromFC_,0,sizeof(CarMsgFromFC_));
    Loger(NORMAL,"CanEx() :: init!");
}

CanEx::~CanEx() {}
/***************************************************************************
函数名:SetCarMsg
功能描述:
作者:
日期:
****************************************************************************/
int32_t CanEx::SetCarData(GB2015P::PIDType type,uint8_t *data,uint32_t data_len){
    if(data == nullptr){
        Loger(NORMAL,"SetCarData data = nullptr");
        return -1;
    }
    if(CarMsgMap_.find(type) == CarMsgMap_.end()){
        Loger(NORMAL,"SetCarData type = %d err",type);
        return -1;
    }

    if(CarMsgMap_[type].msg.datalen != data_len){
        Loger(NORMAL,"CarMsgMap_[%d] datalen err, maplen = %d,datalen = %d",
            type,CarMsgMap_[type].msg.datalen,data_len);
        return -1;
    }

    memcpy(CarMsgMap_[type].msg.data,data,data_len);
    return 0;
}
/***************************************************************************
函数名:SetCarMsg
功能描述:
作者:
日期:
****************************************************************************/
int32_t CanEx::SetCtrlInfo(GB2015P::PIDType type,MsgCtrlParaDef ctrl){
    if(CarMsgMap_.find(type) != CarMsgMap_.end()){
        CarMsgMap_[type].ctrl = ctrl;
        return 0;
    }else{
        return -1;
    }
    
}
/***************************************************************************
函数名:GetCarMsg
功能描述:
作者:
日期:
****************************************************************************/
int32_t CanEx::GetCarMsg(GB2015P::PIDType type,uint8_t *data,uint32_t data_len){
    if(data == nullptr){
        Loger(NORMAL,"GetCarMsg data = nullptr");
        return -1;
    }

    if(CarMsgMap_[type].msg.datalen != data_len){
        Loger(NORMAL,"GetCarMsg[%d] datalen err, maplen = %d,datalen = %d",
             type,CarMsgMap_[type].msg.datalen,data_len);
        return -1;
    }
    memcpy(data,CarMsgMap_[type].msg.data,data_len);
    return 0;
}
/***************************************************************************
函数名:ClearMsg
功能描述:
作者:
日期:
****************************************************************************/
int32_t CanEx::ClearCarMsg(void){

    memset(&CarMsg_,0,sizeof(CarMsg_));
    return -1;
}
/***************************************************************************
函数名:ClearMsg
功能描述:
作者:
日期:
****************************************************************************/
int32_t CanEx::ClearSeMsg(void){
    memset(&SeMsg_,0,sizeof(SeMsg_));
    return -1;
}
/***************************************************************************
函数名:ClearMsg
功能描述:
作者:
日期:
****************************************************************************/
int32_t CanEx::ClearMsg(GB2015P::PIDType pgi){
    memset(CarMsgMap_[pgi].msg.data,0,CarMsgMap_[pgi].msg.datalen);
    return -1;
}

//  /***************************************************************************
// 函数名:ClearMsg
// 功能描述:
// 作者:
// 日期:
// ****************************************************************************/
// uint8_t CanEx::UpdateRecvFlg(GB2015P::PIDType type){
//     if(SEmsgMap_.find(type) == SEmsgMap_.end()){
//         Loger(NORMAL,"GetSeMsg type = %d out range",type);
//         return false;
//     }
//     RecvFlg_[type].nowRecv = 1;
//     return ret;
// } 
/***************************************************************************
函数名:ClearMsg
功能描述:
作者:
日期:
****************************************************************************/
uint8_t CanEx::GetSeMsg(GB2015P::PIDType type){
    if(SEmsgMap_.find(type) == SEmsgMap_.end()){
        Loger(NORMAL,"GetSeMsg type = %d out range",type);
        return false;
    }
    int32_t ret = CanRecv_.GetMsgMap(type,SEmsgMap_[type].msg,SEmsgMap_[type].msglen);
    if(ret == true){
        SEmsgMap_[type].recv = 1;
    }
    return ret;
}
uint32_t CanEx::GetRecvFlg(GB2015P::PIDType type){
    if(SEmsgMap_.find(type) == SEmsgMap_.end()){
        Loger(NORMAL,"GetSeMsg type = %d out range",type);
        return 0;
    }
    return SEmsgMap_[type].recv;
}

int32_t CanEx::ClearRecvFlg(GB2015P::PIDType type){
    if(SEmsgMap_.find(type) == SEmsgMap_.end()){
        Loger(NORMAL,"GetSeMsg type = %d out range",type);
        return -1;
    }
    SEmsgMap_[type].recv = 0;
    return 0;
}
/***************************************************************************
函数名:ClearMsg
功能描述:
作者:
日期:
****************************************************************************/
int32_t CanEx::UserMsgInit(){
    uint32_t len = sizeof(BmsMsgInfo)/sizeof(BmsMsgInfo[0]);
    CarMsgFromFCMap_.clear();
    CarMsgFromFCMap_.reserve(len);
    for(uint32_t idx = 0; idx < len; ++idx){
        CarMsgFromFCMap_.insert(std::make_pair((GB2015P::PIDType)BmsMsgInfo[idx].PGI,BmsMsgInfo[idx]));
    }
    CarMsgFromFCMap_[GB2015P::X0].msg.data = (uint8_t*)&CarMsgFromFC_.protoConferM;         // 0x00 版本协商报文
    CarMsgFromFCMap_[GB2015P::X2].msg.data = (uint8_t*)&CarMsgFromFC_.phaseAckM;            // 0x02车辆阶段确认报文
    CarMsgFromFCMap_[GB2015P::X4].msg.data = (uint8_t*)&CarMsgFromFC_.carEndM;              // 0x04车辆中止报文
    CarMsgFromFCMap_[GB2015P::X6].msg.data = (uint8_t*)&CarMsgFromFC_.contactStatusM;       // 0x06接触器状态报文
    CarMsgFromFCMap_[GB2015P::X9].msg.data = (uint8_t*)&CarMsgFromFC_.wakeupM;              // 0x09唤醒报文
    CarMsgFromFCMap_[GB2015P::B2].msg.data = (uint8_t*)&CarMsgFromFC_.FDCResultM;           // 0x12车辆功能协商确认结果报文
    CarMsgFromFCMap_[GB2015P::C2].msg.data = (uint8_t*)&CarMsgFromFC_.carParaM;             // 0x22车辆充电参数报文
    CarMsgFromFCMap_[GB2015P::C4].msg.data = (uint8_t*)&CarMsgFromFC_.carChgDischgParaM;    // 0x24车辆充放电参数报文
    CarMsgFromFCMap_[GB2015P::D2].msg.data = (uint8_t*)&CarMsgFromFC_.carAuthenWaitM;       // 0x32车辆鉴权等待报文（FDC = 1）
    CarMsgFromFCMap_[GB2015P::D4].msg.data = (uint8_t*)&CarMsgFromFC_.EvinM;                // 0x34车辆鉴权参数报文（FDC = 2）
    CarMsgFromFCMap_[GB2015P::D6].msg.data = (uint8_t*)&CarMsgFromFC_.eVINagainAuthenM;     // 0x36重新鉴权请求
    CarMsgFromFCMap_[GB2015P::D7].msg.data = (uint8_t*)&CarMsgFromFC_.cloudAuthenM;         // 0x37车辆鉴权参数报文（FDC = 3）
    CarMsgFromFCMap_[GB2015P::D10].msg.data = (uint8_t*)&CarMsgFromFC_.cloudAgainAuthenM;   // 0x3A重新鉴权请求
    CarMsgFromFCMap_[GB2015P::E2].msg.data = (uint8_t*)&CarMsgFromFC_.carAppoInfoM;         // 0x42车辆预约信息报文
    CarMsgFromFCMap_[GB2015P::E4].msg.data = (uint8_t*)&CarMsgFromFC_.carAppoConfirmM;      // 0x44车辆预约协商结果
    CarMsgFromFCMap_[GB2015P::F2].msg.data = (uint8_t*)&CarMsgFromFC_.selfcheckAckM;        // 0x52车辆检测确认报文；
    CarMsgFromFCMap_[GB2015P::G2].msg.data = (uint8_t*)&CarMsgFromFC_.CarPowerSupStateM;    // 0x62车辆供电状态报文
    CarMsgFromFCMap_[GB2015P::G3].msg.data = (uint8_t*)&CarMsgFromFC_.CarPowerSupNeedM;     // 0x63车辆供电需求报文
    CarMsgFromFCMap_[GB2015P::G5].msg.data = (uint8_t*)&CarMsgFromFC_.CarPowerEndM;         // 0x65车辆供电完成报文
    CarMsgFromFCMap_[GB2015P::H2].msg.data = (uint8_t*)&CarMsgFromFC_.carReqdyM;            // 0x72车辆就绪状态报文(FDC = 1)
    CarMsgFromFCMap_[GB2015P::H3].msg.data = (uint8_t*)&CarMsgFromFC_.carRequireM;          // 0x73车辆充电需求报文
    CarMsgFromFCMap_[GB2015P::H4].msg.data = (uint8_t*)&CarMsgFromFC_.carBasicInfoM;        // 0x74车辆充电基本信息
    CarMsgFromFCMap_[GB2015P::H7].msg.data = (uint8_t*)&CarMsgFromFC_.carBatBasicInfoM;     // 0x77车辆充电电池基本信息报文
    CarMsgFromFCMap_[GB2015P::H9].msg.data = (uint8_t*)&CarMsgFromFC_.carPauseM;            // 0x79车辆暂停报文
    CarMsgFromFCMap_[GB2015P::H11].msg.data = (uint8_t*)&CarMsgFromFC_.CarDisChgReadyM;     // 0x82车辆就绪状态报文（FDC = 2）
    CarMsgFromFCMap_[GB2015P::H13].msg.data = (uint8_t*)&CarMsgFromFC_.CarDynamicOutM;      // 0x84车辆动态输出能力报文
    CarMsgFromFCMap_[GB2015P::H14].msg.data = (uint8_t*)&CarMsgFromFC_.CarDisChgRequireM;   // 0x85车辆充电需求报文
    CarMsgFromFCMap_[GB2015P::H16].msg.data = (uint8_t*)&CarMsgFromFC_.CarDisChgInfoM;      // 0x87车辆充放电基本信息报文
    CarMsgFromFCMap_[GB2015P::H18].msg.data = (uint8_t*)&CarMsgFromFC_.CarDisChgBattInfoM;  // 0x89车辆充放电电池基本信息报文
    CarMsgFromFCMap_[GB2015P::H20].msg.data = (uint8_t*)&CarMsgFromFC_.CarDisChgPauseM;     // 0x8B车辆充放电暂停报文
    CarMsgFromFCMap_[GB2015P::I1].msg.data = (uint8_t*)&CarMsgFromFC_.caStickCheckM;        // 0x84车辆粘连检测报文
    CarMsgFromFCMap_[GB2015P::I4].msg.data = (uint8_t*)&CarMsgFromFC_.carStatisM;           // 0x85车辆统计报文
    return 0;
}
/***************************************************************************
函数名:ClearMsg
功能描述:
作者:
日期:
****************************************************************************/
void CanEx::CanExInit(void){
    CanSocket_.CanInit("can0");
    UserMsgInit();
}
/***************************************************************************
函数名:ClearMsg
功能描述:
作者:
日期:
****************************************************************************/
void CanEx::FastSchedule(void){// 3ms
    CanSend_.sendframe();// 3ms
}
/***************************************************************************
函数名:ClearMsg
功能描述:
作者:
日期:
****************************************************************************/
void CanEx::NormalSchedule(void){ //10ms
    CanSend_.sendLogic();//10ms
    CanRecv_.recvMsg();
}