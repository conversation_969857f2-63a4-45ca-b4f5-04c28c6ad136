#include "can_data.h"
#include <stdio.h>
#include <string.h>
#include <linux/can.h>
#include <unistd.h>
#include "can_socket.h"
#include "cstring"
#include "loger.h"

MsgSend::MsgSend(CanSocket *socket)
{
    // 长消息canid
    memset(&LMcanid, 0, sizeof(LMcanid));
    LMcanid.P = PRIORITY6;
    LMcanid.PF = LMPF;
    LMcanid.EFF = 1;

    for (uint16_t i = 0; i < SMRMQUANTITY; i++)
    {
        // 短消息canid
        memset(&SMRMcanid[i], 0, sizeof(SMRMcanid[i]));
        SMRMcanid[i].P = PRIORITY4;
        SMRMcanid[i].PF = SMPF;
        SMRMcanid[i].EFF = 1;
    }
    memset(recv_smpid, 0xFF, SMRMQUANTITY);
    for(int32_t idx = 0; idx < SMRMQUANTITY; ++idx){
        SMS_T1[idx] = timeoff;
        SMS_T2[idx] = timeoff;
    }
    memset(send_smpid, 0xFF, SMRMQUANTITY);
    memset(send_smflag, false, SMRMQUANTITY);

    // 不需要确认消息canid
    memset(&SMURMcanid, 0, sizeof(SMURMcanid));
    SMURMcanid.P = PRIORITY6;
    SMURMcanid.PF = SMURMPF;
    SMURMcanid.EFF = 1;

    // 确认反馈canid
    memset(&ACKcanid, 0, sizeof(ACKcanid));
    ACKcanid.P = PRIORITY3;
    ACKcanid.PF = ACKPF;
    ACKcanid.EFF = 1;

    sendmsg_flag[(uint8_t)GB2015P::X2] = NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::X4] =NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::X6] = NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::X9] = NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::B2] = NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::C2] =  NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::C4] =  NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::D2] =  NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::D4] =  NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::D6] =  NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::D7] =  NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::D10] =  NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::E2] = NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::E4] =  NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::F2] = NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::G2] = NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::G3] = NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::G5] = NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::H2] = NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::H3] = NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::H4] = NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::H7] = NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::H9] = NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::H11] = NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::H13] = NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::H14] = NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::H16] = NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::H18] = NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::H20] = NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::I1] = NOTSEND;
    sendmsg_flag[(uint8_t)GB2015P::I4] = NOTSEND;

    // LMSendoff = {timeoff, timeoff, timeoff, 0, 0, false, LM_UNK, 0, 0, 0, 0, 0, 0};
    // memcpy(& &LMSendoff, sizeof(_LMSendParameter));
    pCanSocket_ = socket;
}

/***************************************************************************
函数名:GetSendStatus
功能描述:获取报文发送状态
作者:
日期:
****************************************************************************/
SendFlag MsgSend::GetSendStatus(GB2015P::PIDType PGI)
{
    if (sendmsg_flag.find((uint8_t)PGI) != sendmsg_flag.end())
    {
        return sendmsg_flag[(uint8_t)PGI];
    }
    return SENDFAILED;
}

/***************************************************************************
函数名:sendLogic
功能描述:发送报文逻辑函数
作者:
日期:
****************************************************************************/
void MsgSend::sendLogic()
{
    LMWaitACK();
    SM_RMWaitACK();
}
/***************************************************************************
函数名:sendMsg
功能描述:数据发送对外的唯一接口
作者:
日期:
****************************************************************************/
void MsgSend::sendMsg(const Msg_Parameters *pApdu)
{
    if (nullptr ==  pApdu)
    {
        Loger(NORMAL,"error:sendMsg PApdu NULL faile");
        return ;
    }

    switch (pApdu->Confirm)
    {
    case YesConfirm:
    case LongFram:
        if (pApdu->datalen > 8)
        {
            LMInit(pApdu);
        }
        else
        {
            SM_RMsend(pApdu);
        }
        break;

    case NoConfirm:
        SM_URMsend(pApdu);
        break;

    case Respoonse:
        ACKsend(pApdu);
        break;
    default:
        Loger(NORMAL,"error:PApdu.Confirm default");
        break;
    }
}

/***************************************************************************
函数名:LMInit
功能描述:长消息发送初始化
作者:
日期:
****************************************************************************/
void MsgSend::LMInit(const Msg_Parameters *pApdu)
{
    if (nullptr == pApdu)
    {
        Loger(NORMAL,"LMInit pApdu NULL failed");
        return;
    }
    if (!send_lmflag) // 一次只能建立一个长连接
    {
        // 拆包
        LMUnpack(pApdu);
        // 建立链接
        lmsenddata.push(LMcanpdu.at(0));
        send_lmflag = true; // 发送方长连接建立完成
        err_cnt = 0;
        LMS_T2 = time100ms;
        LMS_T3 = pApdu->Time;
        sendmsg_flag[LMcanpdu.at(1).PackedMsg.MsgData[1]] = SENDING;
    }
    else
    {
        sendmsg_flag[pApdu->data[0]] = SENDFAILED;
        Loger(NORMAL,"error:Only one persistent connection can be established");
    }
}

/***************************************************************************
函数名:LMUnpack
功能描述:长消息拆包
作者:
日期:
****************************************************************************/
void MsgSend::LMUnpack(const Msg_Parameters *pApdu)
{
    if (nullptr == pApdu)
    {
        Loger(NORMAL,"error:LMUnpack pApdu NULL failed");
    }
    CANPDU frame = {0};
    uint16_t k = 0;
    LMcanpdu.clear();
    lm_datalen = pApdu->datalen;
    lm_tfra = lm_datalen / 7;
    if (lm_datalen % 7 > 0)
    {
        lm_tfra += 1;
    }
    LMcanid.SA = pApdu->SA;
    LMcanid.PS = pApdu->PS;
    LMcanid.PF = pApdu->PF;
    LMcanid.P = pApdu->priority;
    memcpy(&frame.PackedMsg.ExtId, &LMcanid, sizeof(LMcanid));
    frame.PackedMsg.DLC = 8;
    memset(frame.PackedMsg.MsgData,0xFF,sizeof(frame.PackedMsg.MsgData));
    frame.PackedMsg.MsgData[0] = 0;
    frame.PackedMsg.MsgData[1] = lm_tfra;
    frame.PackedMsg.MsgData[2] = lm_datalen&0xff;
    frame.PackedMsg.MsgData[3] = (lm_datalen>>8)&0xff;

    LMcanpdu.push_back(frame);
    for (uint32_t i = 1; i <= lm_tfra; i++)
    {
        frame.PackedMsg.MsgData[0] = i;
        for (uint16_t j = 1; j < 8; j++)
        {
            if (k < lm_datalen)
            {
                frame.PackedMsg.MsgData[j] = pApdu->data[k];
                ++k;
            }
            else
            {
                frame.PackedMsg.MsgData[j] = 0xFF;
                ++k;
            }
        }
        LMcanpdu.push_back(frame);
    }
}

/***************************************************************************
函数名:LMWaitACK
功能描述:长消息等反馈 10ms
作者:
日期:
****************************************************************************/
void MsgSend::LMWaitACK()
{
    if (send_lmflag)
    {
        LMTimeoutjudg();
        switch (ReceiveACK)
        {
        case LM_UNK:
        break;
        case LM_ACK:
            err_cnt = 0;
            send_cnt = 0;
            LMS_T2 = timeoff;
            ReceiveACK = LM_UNK;
            LMsending();
            break;
        case LM_NACK:
            LMS_T2 = timeoff;
            LMS_T3 = timeoff;
            err_cnt = 0;
            send_cnt = 0;
            send_lmflag = false;
            ReceiveACK = LM_UNK;
            sendmsg_flag[LMcanpdu.at(1).PackedMsg.MsgData[1]] = SENDFAILED;
            break;
        case LM_EndofACK:
            if (recv_n + send_cnt >= lm_tfra)
            {
                LMS_T2 = timeoff;
                LMS_T3 = timeoff;
                err_cnt = 0;
                send_cnt = 0;
                send_lmflag = false;
                ReceiveACK = LM_UNK;
                sendmsg_flag[LMcanpdu.at(1).PackedMsg.MsgData[1]] = SENDSUCCESS;
                Loger(NORMAL,"LM send finish: pgn = %d", LMcanpdu.at(1).PackedMsg.MsgData[1]);
            }
            else
            {
                err_cnt = 0;
                send_cnt = 0;
                LMS_T2 = timeoff;
                ReceiveACK = LM_UNK;
                LMsending();   
            }
            break;
        default:
            Loger(NORMAL,"Obtained unknown parameter  ReceiveACK:%d", ReceiveACK);
            break;
        }
    }
}

/***************************************************************************
函数名:LMsending
功能描述:长消息根据反馈发送报文
作者:
日期:
****************************************************************************/
void MsgSend::LMsending()
{
    Loger(NORMAL,"LMsending :send_cnt = %d,recv_k = %d,recv_n = %d,lm_tfra = %d", send_cnt,recv_k,recv_n,lm_tfra);
    for (uint16_t i = 0; i < recv_k; i++)
    {
        if (send_cnt < recv_k - 1 && recv_n + send_cnt < lm_tfra) // 非请求的最后一帧，且长消息未发送完成
        {
            lmsenddata.push(LMcanpdu.at(recv_n + send_cnt));
            send_cnt += 1;
        }
        else if (send_cnt >= recv_k - 1 && recv_n + send_cnt < lm_tfra) // 请求的最后一帧，但长消息未发送完成
        {
            lmsenddata.push(LMcanpdu.at(recv_n + send_cnt));
            send_cnt += 1;
            LMS_T2 = time100ms;
        }
        else if (recv_n + send_cnt >= lm_tfra) // 长消息发送完成
        {
            lmsenddata.push(LMcanpdu.at(lm_tfra));
            send_cnt += 1;
            LMS_T2 = time100ms;
        }
    }
}

/***************************************************************************
函数名:LMTimeoutjudg
功能描述:长消息超时判断  t2
作者:
日期:
****************************************************************************/
void MsgSend::LMTimeoutjudg()
{
    // 同一包连续两次以上接收反馈超时才判断错误
    if (LMS_T2 <= 0 && LMS_T2 != timeoff)
    {
        if (err_cnt < 2)
        {
            err_cnt++;
            if (recv_n + recv_k - 1 <= 0) // 链接建立超时
            {
                lmsenddata.push(LMcanpdu.at(0)); // 重新发起连链接
            }
            else if (recv_n + recv_k - 1 > 0 && recv_n + recv_k - 1 < lm_tfra) //
            {
                lmsenddata.push(LMcanpdu.at(recv_n + recv_k - 1));
            }
            else if (recv_n + recv_k - 1 >= lm_tfra)
            {
                lmsenddata.push(LMcanpdu.at(lm_tfra));
            }
            LMS_T2 = time100ms;
        }
        else  // 两次以上超时中止长链接
        {
            LMsendNACK();
        }
    }
    if (LMS_T3 <= 0 && LMS_T3 != timeoff)
    {
        LMsendNACK();
    }
}

/***************************************************************************
函数名:LMSsendNACK()
功能描述:发送方发送NACK函数
作者:
日期:
****************************************************************************/
void MsgSend::LMsendNACK()
{
    CANPDU NACKframe = {0};
    memset(NACKframe.PackedMsg.MsgData, 0xFF, 8);
    memcpy(&NACKframe.PackedMsg.ExtId, &LMcanid, sizeof(LMcanid));
    NACKframe.PackedMsg.DLC = 8;
    NACKframe.PackedMsg.MsgData[0] = LM_NACK;
    lmsenddata.push(NACKframe);
    LMS_T2 = timeoff;
    LMS_T3 = timeoff;
    err_cnt = 0;
    send_cnt = 0;
    send_lmflag = false;
}

/***************************************************************************
函数名:LMS_SMS_T
功能描述:发送方超时定时器动作函数 10ms
作者:
日期:
****************************************************************************/
void MsgSend::LMS_SMS_T()
{
    if (LMS_T2 != timeoff)
    {
        LMS_T2 -= time10ms;
    }
    if (LMS_T3 != timeoff)
    {
        LMS_T3 -= 10;
    }
    for (uint16_t i = 0; i < SMRMQUANTITY; i++)
    {
        if (SMS_T1[i] != timeoff)
        {
            SMS_T1[i] -= time10ms;
        }
        if (SMS_T2[i] != timeoff)
        {
            SMS_T2[i] -= time10ms;
        }
    }
}
/***************************************************************************
函数名:SM_RMsend
功能描述:需要确认短消息动作逻辑函数
作者:
日期:
****************************************************************************/
void MsgSend::SM_RMsend(const Msg_Parameters *pApdu)
{
    uint32_t smrmflgcnt = 0;
    uint16_t i = 0;
    if (nullptr == pApdu)
    {
        Loger(NORMAL,"error:SM_RMsend pApdu NULL failed");
    }
    for(uint16_t j = 0; j < SMRMQUANTITY; j++)
    {
        if(pApdu->data[0] == SMRMcanpdu[j].PackedMsg.MsgData[0])
        {
            i = j;
            send_smflag[i] = false;
            break;
        }
    }
    for (; i < SMRMQUANTITY; i++)
    {
        smrmflgcnt += send_smflag[i];
        if (!send_smflag[i])
        {
            // 数据填充
            SMS_T1[i] = pApdu->Time;
            SMS_T2[i] = time50ms;
            SMRMcanpdu[i] = {0};
            memcpy(&SMRMcanpdu[i].PackedMsg.ExtId, &SMRMcanid[i], sizeof(SMRMcanid[i]));

            SMRMcanpdu[i].PackedMsg.ExtId.SA = pApdu->SA;
            SMRMcanpdu[i].PackedMsg.ExtId.PS = pApdu->PS;
            SMRMcanpdu[i].PackedMsg.ExtId.PF = pApdu->PF;
            SMRMcanpdu[i].PackedMsg.ExtId.P = pApdu->priority;
            SMRMcanpdu[i].PackedMsg.DLC = pApdu->datalen;
            memset(SMRMcanpdu[i].PackedMsg.MsgData, 0xFF, sizeof(SMRMcanpdu[i].PackedMsg.MsgData));
            send_smpid[i] = pApdu->data[0];

            for (uint32_t j = 0; j < pApdu->datalen; j++)
            {
                SMRMcanpdu[i].PackedMsg.MsgData[j] = pApdu->data[j];
            }
            // 发送
            senddata.push(SMRMcanpdu[i]);
            sendmsg_flag[SMRMcanpdu[i].PackedMsg.MsgData[0]] = SENDING;
            send_smflag[i] = true;
            break;
        }
    }
    if(smrmflgcnt == SMRMQUANTITY)
    {
        Loger(NORMAL,"error : SMRMflg ALL Occupied");
    }
}

/***************************************************************************
函数名:SM_RMWaitACK
功能描述:短消息等反馈 根据反馈判断是否重发10ms
作者:
日期:
****************************************************************************/
void MsgSend::SM_RMWaitACK()
{
    for (uint16_t i = 0; i < SMRMQUANTITY; i++)
    {
        if (send_smflag[i])
        {
            for(uint16_t j = 0; j < SMRMQUANTITY; j++)
            {
                if (send_smpid[i] == recv_smpid[j]) // 接收到的PGI为发送的PGI // 接收反馈成功
                {
                    SMS_T1[i] = timeoff;
                    SMS_T2[i] = timeoff;
                    send_smpid[i] = 0xFF;
                    recv_smpid[j] = 0xFF;
                    send_smflag[i] = false;
                    sendmsg_flag[SMRMcanpdu[i].PackedMsg.MsgData[0]] = SENDSUCCESS;
                }
            }
            if (SMS_T1[i] <= 0 && SMS_T1[i] != -1) // 失败发送超时
            {
                SMS_T1[i] = timeoff;
                SMS_T2[i] = timeoff;
                send_smflag[i] = false;
                sendmsg_flag[SMRMcanpdu[i].PackedMsg.MsgData[0]] = SENDFAILED;
            }
            else
            {
                if (SMS_T2[i] <= 0 && SMS_T2[i] != -1)
                {
                    senddata.push(SMRMcanpdu[i]);
                    SMS_T2[i] = time50ms;
                }
            }
        }
    }
}

/***************************************************************************
函数名:SM_URMsend
功能描述:不需要确认动作逻辑函数
作者:
日期:
****************************************************************************/
void MsgSend::SM_URMsend(const Msg_Parameters *pApdu) // 由应用层定义发送周期
{
    if (nullptr == pApdu)
    {
        Loger(NORMAL,"error:SM_URMsend pApdu NULL failed");
    }
    SMURMcanpdu = {0};
    memset(SMURMcanpdu.PackedMsg.MsgData, 0xFF, 8);
    memcpy(&SMURMcanpdu.PackedMsg.ExtId, &SMURMcanid, sizeof(CANID));
    SMURMcanpdu.PackedMsg.ExtId.SA = pApdu->SA;
    SMURMcanpdu.PackedMsg.ExtId.PS = pApdu->PS;
    SMURMcanpdu.PackedMsg.ExtId.PF = pApdu->PF;
    SMURMcanpdu.PackedMsg.ExtId.P = pApdu->priority;
    SMURMcanpdu.PackedMsg.DLC = pApdu->datalen;

    for (uint32_t i = 0; i < pApdu->datalen; i++)
    {
        SMURMcanpdu.PackedMsg.MsgData[i] = pApdu->data[i];
    }
    // printf("URM can msg SA-PS-len: %d-%d-%d",SMURMcanpdu.PackedMsg.ExtId.SA,
    //     SMURMcanpdu.PackedMsg.ExtId.PS,SMURMcanpdu.PackedMsg.DLC);
    // printf("URM can msg data: %d-%d-%d-%d-%d-%d-%d-%d",SMURMcanpdu.PackedMsg.MsgData[0],SMURMcanpdu.PackedMsg.MsgData[1],
    //     SMURMcanpdu.PackedMsg.MsgData[2],SMURMcanpdu.PackedMsg.MsgData[3],SMURMcanpdu.PackedMsg.MsgData[4],
    //     SMURMcanpdu.PackedMsg.MsgData[5],SMURMcanpdu.PackedMsg.MsgData[6],SMURMcanpdu.PackedMsg.MsgData[7]);
    senddata.push(SMURMcanpdu);

}

/***************************************************************************
函数名:ACKsend
功能描述:应答确认动作逻辑函数
作者:
日期:
****************************************************************************/
void MsgSend::ACKsend(const Msg_Parameters *pApdu)
{
    if (nullptr == pApdu)
    {
        Loger(NORMAL,"error:ACKsend pApdu NULL failed");
        return ;
    }
    ACKcanpdu = {0};
    ACKcanid.SA = pApdu->SA;
    ACKcanid.PS = pApdu->PS;
    // ACKcanid.PF = pApdu->PF;
    // ACKcanid.P = pApdu->priority;

    memcpy(&ACKcanpdu.PackedMsg.ExtId, &ACKcanid, sizeof(CANID));
    ACKcanpdu.PackedMsg.DLC = pApdu->datalen;

    for (uint32_t i = 0; i < 8; i++)
    {
        ACKcanpdu.PackedMsg.MsgData[i] = pApdu->data[i];
    }

    senddata.push(ACKcanpdu);
}

/***************************************************************************
函数名:sendframe
功能描述:报文发送统一函数接口 3ms
作者:
日期:
****************************************************************************/
void MsgSend::sendframe()
{
    if (!lmsenddata.empty())
    {
        CANPDU frame = {0};
        // if(3ms == 1) // 3ms定时判断
        {
            //memcpy(&frame, &lmsenddata.front(), sizeof(frame));
            frame = lmsenddata.front();
            //uint16_t ret = write(fdw, &frame, sizeof(frame));
            uint16_t ret = pCanSocket_->CanSend(&frame, sizeof(frame));
            if (ret == sizeof(frame))
            {
                lmsenddata.pop();
            }
        }
    }
    uint32_t senddatacnt = 0;
    while (senddatacnt++ < 10)
    {
        if (!senddata.empty())
        {
            CANPDU frame = {0};
            frame = senddata.front();
            uint16_t ret = pCanSocket_->CanSend(&frame, sizeof(CANPDU));
            if (ret == sizeof(frame))
            {
                senddata.pop();
            }
        }
        else
        {
            break; // * 计数
        }
    }
}

MsgRecv::MsgRecv(MsgSend *classMagSend,CanSocket *socket)
{
    msg_map[(uint8_t)GB2015P::X0] = {new GB2015P::ProtoConferMsgDef, sizeof(GB2015P::ProtoConferMsgDef)};
    msg_map[(uint8_t)GB2015P::X1] = {new GB2015P::PhaseRequestMsgDef, sizeof(GB2015P::PhaseRequestMsgDef)};
    msg_map[(uint8_t)GB2015P::X3] = {new GB2015P::SeEndMsgDef, sizeof(GB2015P::SeEndMsgDef)};
    msg_map[(uint8_t)GB2015P::X5] = {new GB2015P::ContactStateDef, sizeof(GB2015P::ContactStateDef)};
    msg_map[(uint8_t)GB2015P::X7] = {new GB2015P::ElockStateDef, sizeof(GB2015P::ElockStateDef)};
    msg_map[(uint8_t)GB2015P::X8] = {new GB2015P::WakeupDef, sizeof(GB2015P::WakeupDef)};
    msg_map[(uint8_t)GB2015P::B1] = {new GB2015P::SeSupportFDCmsgDef, sizeof(GB2015P::SeSupportFDCmsgDef)};
    msg_map[(uint8_t)GB2015P::C1] = {new GB2015P::SeParaMsgDef, sizeof(GB2015P::SeParaMsgDef)};
    msg_map[(uint8_t)GB2015P::C3] = {new GB2015P::SeChgDischgParaMsgDef, sizeof(GB2015P::SeChgDischgParaMsgDef)};
    msg_map[(uint8_t)GB2015P::D1] = {new GB2015P::SeAuthenParaMsgDef, sizeof(GB2015P::SeAuthenParaMsgDef)};
    msg_map[(uint8_t)GB2015P::D3] = {new GB2015P::AuthenResultMsgDef, sizeof(GB2015P::AuthenResultMsgDef)};
    msg_map[(uint8_t)GB2015P::D5] = {new GB2015P::EvinAuthenResultDef, sizeof(GB2015P::EvinAuthenResultDef)};
    msg_map[(uint8_t)GB2015P::D8] = {new GB2015P::SeCloudAuthenInfoDef, sizeof(GB2015P::SeCloudAuthenInfoDef)};
    msg_map[(uint8_t)GB2015P::D9] = {new GB2015P::CloudAuthenResultDef, sizeof(GB2015P::CloudAuthenResultDef)};
    msg_map[(uint8_t)GB2015P::E1] = {new GB2015P::SeAppoInfoDef, sizeof(GB2015P::SeAppoInfoDef)};
    msg_map[(uint8_t)GB2015P::E3] = {new GB2015P::SeAppoConfirmDef, sizeof(GB2015P::SeAppoConfirmDef)};
    msg_map[(uint8_t)GB2015P::F1] = {new GB2015P::SeSelfCheckInfoMsg, sizeof(GB2015P::SeSelfCheckInfoMsg)};
    msg_map[(uint8_t)GB2015P::G1] = {new GB2015P::SePowerSupStateDef, sizeof(GB2015P::SePowerSupStateDef)};
    msg_map[(uint8_t)GB2015P::G4] = {new GB2015P::SePowerDynamicOutDef, sizeof(GB2015P::SePowerDynamicOutDef)};
    msg_map[(uint8_t)GB2015P::G6] = {new GB2015P::SePowerInfoDef, sizeof(GB2015P::SePowerInfoDef)};
    msg_map[(uint8_t)GB2015P::H1] = {new GB2015P::SeReadyMsgDef, sizeof(GB2015P::SeReadyMsgDef)};
    msg_map[(uint8_t)GB2015P::H5] = {new GB2015P::SeDynamicOutMsgDef, sizeof(GB2015P::SeDynamicOutMsgDef)};
    msg_map[(uint8_t)GB2015P::H6] = {new GB2015P::SeOutInfoDef, sizeof(GB2015P::SeOutInfoDef)};
    msg_map[(uint8_t)GB2015P::H8] = {new GB2015P::SePauseMsgDef, sizeof(GB2015P::SePauseMsgDef)};
    msg_map[(uint8_t)GB2015P::H10] = {new GB2015P::SeDisChgReadyDef, sizeof(GB2015P::SeDisChgReadyDef)};
    msg_map[(uint8_t)GB2015P::H12] = {new GB2015P::SeDisChgNeedDef, sizeof(GB2015P::SeDisChgNeedDef)};
    msg_map[(uint8_t)GB2015P::H15] = {new GB2015P::SeDynamicOutDef, sizeof(GB2015P::SeDynamicOutDef)};
    msg_map[(uint8_t)GB2015P::H17] = {new GB2015P::SeDisChgInfoDef, sizeof(GB2015P::SeDisChgInfoDef)};
    msg_map[(uint8_t)GB2015P::H19] = {new GB2015P::SeDisChgPauseMsgDef, sizeof(GB2015P::SeDisChgPauseMsgDef)};
    msg_map[(uint8_t)GB2015P::I2] = {new GB2015P::SeCheckEnableDef, sizeof(GB2015P::SeCheckEnableDef)};
    msg_map[(uint8_t)GB2015P::I3] = {new GB2015P::SeStatisMsgDef, sizeof(GB2015P::SeStatisMsgDef)};

    msg_flag[(uint8_t)GB2015P::X0] =  false;
    msg_flag[(uint8_t)GB2015P::X1] =  false;
    msg_flag[(uint8_t)GB2015P::X3] = false;
    msg_flag[(uint8_t)GB2015P::X5] = false;
    msg_flag[(uint8_t)GB2015P::X7] = false;
    msg_flag[(uint8_t)GB2015P::X8] =false;
    msg_flag[(uint8_t)GB2015P::B1] = false;
    msg_flag[(uint8_t)GB2015P::C1] = false;
    msg_flag[(uint8_t)GB2015P::C3] =  false;
    msg_flag[(uint8_t)GB2015P::D1] =  false;
    msg_flag[(uint8_t)GB2015P::D3] = false;
    msg_flag[(uint8_t)GB2015P::D5] =  false;
    msg_flag[(uint8_t)GB2015P::D8] =  false;
    msg_flag[(uint8_t)GB2015P::D9] =  false;
    msg_flag[(uint8_t)GB2015P::E1] = false;
    msg_flag[(uint8_t)GB2015P::E3] = false;
    msg_flag[(uint8_t)GB2015P::F1] = false;
    msg_flag[(uint8_t)GB2015P::G1] = false;
    msg_flag[(uint8_t)GB2015P::G4] = false;
    msg_flag[(uint8_t)GB2015P::G6] = false;
    msg_flag[(uint8_t)GB2015P::H1] = false;
    msg_flag[(uint8_t)GB2015P::H5] = false;
    msg_flag[(uint8_t)GB2015P::H6] = false;
    msg_flag[(uint8_t)GB2015P::H8] = false;
    msg_flag[(uint8_t)GB2015P::H10] = false;
    msg_flag[(uint8_t)GB2015P::H12] = false;
    msg_flag[(uint8_t)GB2015P::H15] = false;
    msg_flag[(uint8_t)GB2015P::H17] = false;
    msg_flag[(uint8_t)GB2015P::H19] = false;
    msg_flag[(uint8_t)GB2015P::I2] = false;
    msg_flag[(uint8_t)GB2015P::I3] = false;

    CANMsgSend = classMagSend;
    pCanSocket_ = socket;
}

MsgRecv::~MsgRecv()
{
    for (auto it = msg_map.begin(); it != msg_map.end(); ++it)
    {
        delete (uint8_t *)it->second.p;
    }
    msg_map.clear();
    msg_flag.clear();
}

/***************************************************************************
函数名:recvMsg
功能描述:传输层接收对应用层唯一接口
作者:
日期:
****************************************************************************/
uint32_t MsgRecv::recvMsg()
{
    LMS_T();
    CANPDU RecvCANframe = {0};
    uint16_t ackcnt = 0;
    //while(read(fdr, &(RecvCANframe.Frame), sizeof(RecvCANframe)) > 0)
    while(pCanSocket_->CanRecv(&(RecvCANframe.Frame), sizeof(RecvCANframe)) > 0)
    {
        // if (RecvCANframe.PackedMsg.ExtId.DP != 0x00 || RecvCANframe.PackedMsg.ExtId.EDP != 0x00)
        // {
        //     printf("error:DP:%d to EDP:%d faile\n", RecvCANframe.PackedMsg.ExtId.DP, RecvCANframe.PackedMsg.ExtId.EDP);
        //     //return -1;
        // }
        switch (RecvCANframe.PackedMsg.ExtId.PF)
        {
        case LMPF: // 长消息
            // if (RecvCANframe.PackedMsg.ExtId.P != PRIORITY6)
            // {
            //     break;
            // }
            LM_Feedback_ACK(&RecvCANframe); // 长消息应答
            break;
        case SMPF: // 短消息
            // if (RecvCANframe.PackedMsg.ExtId.P != PRIORITY4)
            // {
            //     break;
            // }
            SM_Feedback_ACK(&RecvCANframe); // 短消息应答确认
            break;
        case CHAR_CONFER_M:
        case CONFER_M:
        case SMURMPF: // 不需要确认消息
            // if (RecvCANframe.PackedMsg.ExtId.P != PRIORITY6)
            // {
            //     break;
            // }
            SMURM_Recv(&RecvCANframe);
            break;
        case ACKPF: // 应答确认消息 注意 在接收到啊LM_NACK后 断开长连接将 将lmflag = 0
            // if (RecvCANframe.PackedMsg.ExtId.P != PRIORITY3)
            // {
            //     break;
            // }
            ACK_Receipt(&RecvCANframe, ackcnt);
            ackcnt++;
            break;
        default:
            break;
        }

        DataParsing();
    }

    return 0;
}

/***************************************************************************
函数名:LMS_T
功能描述:传输层接受长消息超时逻辑函数
作者:
日期:
****************************************************************************/
void MsgRecv::LMS_T()
{
    if (recv_lmflag == 1) // 接收方长连接超时判断
    {
        // if(10ms == 1) 10ms进入一次
        {
            if (LMS_T2 != timeoff)
            {
                LMS_T2 -= 10;
            }
            if (LMS_T3 != timeoff)
            {
                LMS_T3 -= 10;
            }
        }
        if (LMS_T2 <= 0 && LMS_T2 != -1)
        {
            if (err_cnt < 2)
            {
                LMS_T2 = time100ms;
                err_cnt += 1;
                send_n = recv_no + 1;
                send_k = lm_tfra - recv_tfra;
                LM_Feedback_send(LM_ACK, send_n, send_k);
            }
            else if (err_cnt >= 2)
            {
                LM_Feedback_send(LM_NACK);
                LMS_T2 = timeoff;
                LMS_T3 = timeoff;
                recv_no = 0;
                recv_num = 0;
                err_cnt = 0;
                recv_lmflag = 0;
                LMcurrentState = Unkown_;
            }
        }
        if (LMS_T3 <= 0 && LMS_T3 != -1)
        {
            LM_Feedback_send(LM_NACK);
            LMS_T3 = timeoff;
            LMS_T2 = timeoff;
            recv_no = 0;
            recv_num = 0;
            err_cnt = 0;
            recv_lmflag = 0;
            LMcurrentState = Unkown_;
        }
    }
}

/***************************************************************************
函数名:SM_Feedback_ACK
功能描述:接收方短消息确认反馈
作者:
日期:
****************************************************************************/
void MsgRecv::SM_Feedback_ACK(const CANPDU *recvframe)
{
    if(nullptr == recvframe)
    {
        Loger(NORMAL,"error: SM_Feedback_ACK recvframe is null failed");
        return ;
    }
    Msg_Parameters SendCANframe;
    uint8_t data[8] = {0};
    SendCANframe.data = data;
    memset(SendCANframe.data, 0xFF, 8);
    SendCANframe.Confirm = Respoonse;
    SendCANframe.PS = (GB2015P::AddressType)recvframe->PackedMsg.ExtId.SA;
    SendCANframe.SA = (GB2015P::AddressType)recvframe->PackedMsg.ExtId.PS;
    SendCANframe.Time = 0;
    SendCANframe.data[0] = 0x00;
    SendCANframe.data[1] = 0x01;
    SendCANframe.data[2] = recvframe->PackedMsg.MsgData[0]; // 填充pid
    SendCANframe.datalen = 8;
    CANMsgSend->sendMsg(&SendCANframe);
    memcpy(SMcandata, recvframe->PackedMsg.MsgData, 8);
    SMrecvokflag = 1;
}

/***************************************************************************
函数名:LM_Feedback_ACK
功能描述:接收方长消息确认反馈逻辑
作者:
日期:
****************************************************************************/
void MsgRecv::LM_Feedback_ACK(const CANPDU *recvframe)
{
    if(nullptr == recvframe)
    {
        Loger(NORMAL,"error : LM_Feedbgack_ACK recvframe is null failed");
        return ;
    }
    uint8_t FrameNum = recvframe->PackedMsg.MsgData[0]; // 当前帧帧号
    uint8_t data[8] = {0};

    if (FrameNum == 0 && recv_lmflag == 0) // 当前为长消息链接帧并且当前没有长消息链接
    {
        LMcurrentState = Establish_;
    }
    else if (FrameNum >= 0 && recv_lmflag == 1) // 帧号不是链接帧且长连接已经成功
    {
        LMcurrentState = Datarecv_;
    }

    switch (LMcurrentState)
    {
    case Establish_:
        lm_tfra = recvframe->PackedMsg.MsgData[1];
        lm_datalen = (static_cast<uint16_t>(recvframe->PackedMsg.MsgData[3]) << 8 | recvframe->PackedMsg.MsgData[2]);
        LMcandata.clear();
        recv_no = 0;
        recv_num = 0;
        recv_tfra = 0;
        err_cnt = 0;
        lmsend_datalen = 0;

        LMS_T2 = time100ms;
        LMS_T3 = time10s;
        LMSendCANframe.data = data;
        memset(LMSendCANframe.data, 0xFF, 8);
        LMSendCANframe.PS = (GB2015P::AddressType)recvframe->PackedMsg.ExtId.SA;
        LMSendCANframe.SA = (GB2015P::AddressType)recvframe->PackedMsg.ExtId.PS;
        LMSendCANframe.datalen = 8;
        LMSendCANframe.Confirm = Respoonse;

        send_k = lm_tfra;
        send_n = 1;
        LM_Feedback_send(LM_ACK, send_n, send_k); // 建立链接
        recv_lmflag = 1;
        LMcurrentState = Unkown_;

        break;
    case Datarecv_:

        if (FrameNum == 0) // 再次接收到长消息链接帧LM(0) 重新链接
        {
            send_k = lm_tfra;
            send_n = 1;
            LM_Feedback_send(LM_ACK, send_n, send_k);
            LMS_T2 = time100ms;
            recv_no = 0;
            recv_num = 0;
            recv_tfra = 0;
        }
        else
        {
            if (FrameNum < recv_no + 1) // 收到重复帧
            {
                break; // 接收到重复帧不做任何操作继续等待
            }
            else if (FrameNum > recv_no + 1 && recv_tfra < lm_tfra) // 漏收报文
            {
                send_k = lm_tfra - recv_no;
                send_n = recv_no + 1;
                LM_Feedback_send(LM_ACK, send_n, send_k);
                recv_num = 0;
            }
            else if (FrameNum == recv_no + 1 && recv_num < send_k - 1 && recv_tfra + 1 < lm_tfra) // 未接收完成
            {
                for (uint16_t j = 1; j < 8; j++)
                {
                    lmsend_datalen++;
                    LMcandata.push_back(recvframe->PackedMsg.MsgData[j]);
                }
                err_cnt = 0;
                recv_no = FrameNum;
                recv_num += 1;
                recv_tfra += 1;
            }
            else if (FrameNum == recv_no + 1 && recv_num >= send_k - 1) // 当前包接收完成
            {
                if (recv_tfra + 1 == lm_tfra) // 是长消息的最后一帧
                {
                    recv_tfra = lm_tfra;
                    send_n = recv_tfra;
                    send_k = recv_num;
                    for (uint16_t j = 1; j < 8; j++)
                    {
                        if(lmsend_datalen >= lm_datalen)
                        {
                            break;
                        }
                        lmsend_datalen++;
                        LMcandata.push_back(recvframe->PackedMsg.MsgData[j]);
                    }
                    LM_Feedback_send(LM_EndofACK, send_n, lmsend_datalen);
                    LMrecvokflag = 1;
                    recv_no = 0;
                    recv_num = 0;
                    err_cnt = 0;
                    LMS_T2 = timeoff;
                    LMS_T3 = timeoff;
                    LMcurrentState = Unkown_;
                    recv_lmflag = 0;
                }
                else if (recv_tfra + 1 < lm_tfra) // 非长消息的最后一帧
                {
                    LMS_T2 = time100ms;
                    err_cnt = 0;
                    recv_no = FrameNum;
                    send_k = lm_tfra - recv_no;
                    send_n = recv_no + 1;
                    LM_Feedback_send(LM_ACK, send_n, send_k);
                    for (uint16_t j = 1; j < 8; j++)
                    {
                        lmsend_datalen++;
                        LMcandata.push_back(recvframe->PackedMsg.MsgData[j]);
                    }
                    recv_num = 0;
                    recv_tfra += 1;
                }
            }
        }
        LMcurrentState = Unkown_;
        break;
    default:
        break;
    }
}

/***************************************************************************
函数名:LM_Feedback_send
功能描述:接收方长消息确认反馈函数
作者:
日期:
****************************************************************************/
void MsgRecv::LM_Feedback_send(ACKType snedACK, uint8_t n, uint16_t k)
{

    switch (snedACK)
    {
    case LM_ACK:
        memset(LMSendCANframe.data, 0xFF, 8);
        LMSendCANframe.data[0] = LM_ACK; // LM_ACK
        LMSendCANframe.data[1] = n;
        LMSendCANframe.data[2] = (uint8_t)k;
        CANMsgSend->sendMsg(&LMSendCANframe);
        break;
    case LM_NACK:
        memset(LMSendCANframe.data, 0xFF, 8);
        LMSendCANframe.data[0] = LM_NACK; // LM_NAK;
        CANMsgSend->sendMsg(&LMSendCANframe);
        break;
    case LM_EndofACK:
        memset(LMSendCANframe.data, 0xFF, 8);
        LMSendCANframe.data[0] = LM_EndofACK; // LM_EndofACK
        LMSendCANframe.data[1] = n;
        LMSendCANframe.data[2] = (k & 0xff);
        LMSendCANframe.data[3] = (k >> 8)&0xff;
        CANMsgSend->sendMsg(&LMSendCANframe);
        break;
    default:
        break;
    }
}

/***************************************************************************
函数名:SMURM_Recv
功能描述:接收方不需要确认报文接收
作者:
日期:
****************************************************************************/
void MsgRecv::SMURM_Recv(const CANPDU *recvframe)
{
    if(nullptr == recvframe)
    {
        Loger(NORMAL,"error : SMURM_Rec recvframe is null fialed");
        return ;
    }
    for (uint16_t i = 0; i < 8; i++)
    {
        SMURMcandata[i] = recvframe->PackedMsg.MsgData[i];
    }
    SMRMrecvokflag = 1;
}

/***************************************************************************
函数名:ACK_Receipt
功能描述:接收方应答确认报文逻辑报文
作者:
日期:
****************************************************************************/
void MsgRecv::ACK_Receipt(const CANPDU *recvframe, uint16_t cnt)
{
    if(nullptr == recvframe)
    {
        Loger(NORMAL,"error : ACK_Receipt recvframe is null failed");
        return ;
    }
    if (CANMsgSend->send_lmflag == true) // 当前为发送方长消息链接且为等待接受状态
    {
        if (recvframe->PackedMsg.MsgData[0] == 1) // LM_ACK
        {
            CANMsgSend->recv_n = recvframe->PackedMsg.MsgData[1];
            CANMsgSend->recv_k = recvframe->PackedMsg.MsgData[2];
            CANMsgSend->ReceiveACK = LM_ACK;
        }
        else if (recvframe->PackedMsg.MsgData[0] == 2) // LM_NACK
        {
            CANMsgSend->ReceiveACK = LM_NACK;
        }
        else if (recvframe->PackedMsg.MsgData[0] == 3) // LM_EndofACK
        {
            CANMsgSend->ReceiveACK = LM_EndofACK;
            CANMsgSend->recv_lmftra = recvframe->PackedMsg.MsgData[1];
            CANMsgSend->recv_lmlen = (static_cast<uint16_t>(recvframe->PackedMsg.MsgData[2]) << 8 | recvframe->PackedMsg.MsgData[3]);
        }
    }
    if (recv_lmflag == 1) // 当前为接收方长消息链接
    {
        if (recvframe->PackedMsg.MsgData[0] == 2)
        {
        }
    }
    if (recvframe->PackedMsg.MsgData[0] == 0 && recvframe->PackedMsg.MsgData[1] == 1) // 发送方短消息反馈
    {
        CANMsgSend->recv_smpid[cnt] = recvframe->PackedMsg.MsgData[2];
    }
}

/***************************************************************************
函数名:DataParsing
功能描述:接收方接受数据到报文的映射
作者:
日期:
****************************************************************************/
void MsgRecv::DataParsing()
{
    uint8_t PGI = 0;
    if (LMrecvokflag == 1)
    {
        PGI = LMcandata.front();
        if (msg_map.find(PGI) != msg_map.end() && msg_flag.find(PGI) != msg_flag.end())
        {
            uint8_t *data_ptr = (uint8_t *)msg_map[PGI].p;
            for (uint16_t i = 0; i < LMcandata.size(); i++)
            {
                data_ptr[i] = LMcandata.at(i);
                if(msg_map[PGI].len == i)
                {
                    break;
                }
            }
            LMcandata.clear();
            msg_flag[PGI] = true;
        }
        LMrecvokflag = 0;
    }
    if (SMrecvokflag == 1)
    {
        PGI = SMcandata[0];
        if (msg_map.find(PGI) != msg_map.end() && msg_flag.find(PGI) != msg_flag.end())
        {
            uint8_t *data_ptr = (uint8_t *)msg_map[PGI].p;
            memcpy(data_ptr, SMcandata, msg_map[PGI].len);
            msg_flag[PGI] = true;
            // if(PGI == GB2015P::H1){
            //     printf("Recv H1 msg SM\n");
            // }
        }
        SMrecvokflag = 0;
    }
    if (SMRMrecvokflag == 1)
    {
        PGI = SMURMcandata[0];
        if (msg_map.find(PGI) != msg_map.end() && msg_flag.find(PGI) != msg_flag.end())
        {
            uint8_t *data_ptr = (uint8_t *)msg_map[PGI].p;
            memcpy(data_ptr, SMURMcandata, msg_map[PGI].len);
            msg_flag[PGI] = true;
            // if(PGI == GB2015P::H1){
            //     printf("Recv H1 msg SMURM\n");
            // }
        }
        SMRMrecvokflag = 0;
    }
}

/***************************************************************************
函数名:GetMsgMap
功能描述:获取对应报文数据 数据从传输层到应用层的映射
作者:
日期:
****************************************************************************/
uint8_t MsgRecv::GetMsgMap(GB2015P::PIDType PGI, uint8_t *data, uint16_t datalen)
{
    if (msg_flag.find((uint8_t)PGI) != msg_flag.end() && msg_map.find((uint8_t) PGI) != msg_map.end())
    {
        if (msg_flag[(uint8_t)PGI])
        {
            memcpy(data, msg_map[(uint8_t)PGI].p, datalen);
            msg_flag[(uint8_t)PGI] = false;
            return true;
        }
        else
        {
            //printf("Receive unsuccessful\n");
            return false;
        }
    }
    //printf("PGI Faile PGI : %d\n", PGI);
    return false;
}


/***************************************************************************
函数名:RecvMsgClean
功能描述:清除接收数据 防止有数据残留
作者:
日期:
****************************************************************************/
void MsgRecv::RecvMsgClean()
{
    for(auto &p : msg_flag)
    {
        // if(p.first == GB2015P::X3)
        // {
        //     continue;;
        // }
        p.second = false;
    }
    Loger(NORMAL,"clean recv msg !");
}