#ifndef __GB2015P_MSGTYPE_H
#define __GB2015P_MSGTYPE_H

#include <cstdint>
#include <linux/can.h>
#include <vector>
#include <queue>
#include <unordered_map>

#define timeoff -1
#define timeout 0
#define time10ms 10
#define time50ms 50
#define time100ms 100
#define time250ms 250
#define time1s 1000
#define time5s 5000
#define time10s 10000
#define time15s 15000
#define time20s 20000
#define time30s 30000
#define time60s 60000
#define time10min 600000
#define time30min 1800000


namespace GB2015P
{

    typedef enum : uint8_t
    {
        X0 = 0x00,  // 版本协商报文
        X1 = 0x01,  // 阶段请求报文
        X2 = 0x02,  // 车辆确认结果报文
        X3 = 0x03,  // 充电机中止报文
        X4 = 0x04,  // 车辆中止报文
        X5 = 0x05,  // 直流供电回路接触器状态报文
        X6 = 0x06,  // 车辆充电回路接触器状态报文
        X7 = 0x07,  // 充电机电子锁状态报文
        X8 = 0x08,  // 充电机唤醒报文
        X9 = 0x09,  // 车辆唤醒报文
        B1 = 0x11,  // 充电机支持功能报文
        B2 = 0x12,  // 车辆功能协商确认结果报文
        C1 = 0x21,  // 充电机充电参数报文（FDC = 1）
        C2 = 0x22,  // 车辆充电参数报文（FDC = 1）
        C3 = 0x23,  // 充电机充放电参数报文（FDC = 2）
        C4 = 0x24,  // 车辆充放电参数报文（FDC = 2）
        D1 = 0x31,  // 充电机鉴权参数报文（FDC = 1）
        D2 = 0x32,  // 车辆鉴权等待报文（FDC = 1）
        D3 = 0x33,  // 鉴权结果报文（FDC = 1）
        D4 = 0x34,  // 车辆鉴权参数报文（FDC = 2）
        D5 = 0x35,  // 鉴权结果报文（FDC = 2）
        D6 = 0x36,  // 重新鉴权请求报文（FDC = 2）
        D7 = 0x37,  // 车辆鉴权参数报文（FDC = 3）
        D8 = 0x38,  // 充电机鉴权参数报文（FDC = 3）
        D9 = 0x39,  // 鉴权结果报文（FDC = 3）
        D10 = 0x3A, // 重新鉴权请求报文（FDC = 3）
        E1 = 0x41,  // 充电机预约充电信息报文（FDC = 1）
        E2 = 0x42,  // 车辆预约充电信息报文（FDC = 1)
        E3 = 0x43,  // 充电机预约充电确认报文（FDC = 1）
        E4 = 0x44,  // 车辆预约充电协商报文（FDC = 1）
        F1 = 0x51,  // 充电机检测信息报文（FDC = 1）
        F2 = 0x52,  // 检测确认报文（FDC = 1）
        G1 = 0x61,  // 充电机供电状态报文（FDC = 1）
        G2 = 0x62,  // 车辆供电状态报文（FDC = 1）
        G3 = 0x63,  // 车辆供电需求报文（FDC = 1）
        G4 = 0x64,  // 充电机动态输出能力报文（FDC = 1）
        G5 = 0x65,  // 车辆供电完成报文
        G6 = 0x66,  // 充电机供电基本信息报文（FDC = 1）
        H1 = 0x71,  // 充电机就绪状态报文（FDC = 1）
        H2 = 0x72,  // 车辆就绪状态报文（FDC = 1）
        H3 = 0x73,  // 车辆充电需求报文（FDC = 1）
        H4 = 0x74,  // 车辆充电基本信息报文（FDC = 1）
        H5 = 0x75,  // 充电机动态输出能力报文（FDC = 1）
        H6 = 0x76,  // 充电机充电基本信息（FDC = 1）
        H7 = 0x77,  // 车辆充电电池基本信息（FDC = 1）
        H8 = 0x78,  // 充电机暂停报文（FDC = 1）
        H9 = 0x79,  // 车辆暂停报文（FDC = 1）
        H10 = 0x81, // 充电机就绪状态报文（FDC = 2）
        H11 = 0x82, // 车辆就绪状态报文（FDC = 2）
        H12 = 0x83, // 充电机放电需求报文（FDC = 2）
        H13 = 0x84, // 车辆动态输出能力报文（FDC = 2）
        H14 = 0x85, // 车辆充电需求报文（FDC = 2）
        H15 = 0x86, // 充电机动态输出能力报文（FDC = 2）
        H16 = 0x87, // 车辆充放电基本信息报文（fDC = 2）
        H17 = 0x88, // 充电机充放电基本信息报文（FDC = 2）
        H18 = 0x89, // 车辆充放电电池基本信息（FDC = 2）
        H19 = 0x8A, // 充电机暂停报文（FDC = 2）
        H20 = 0x8B, // 车辆暂停报文（FDC = 2）
        I1 = 0x91,  // 车辆粘连检测报文（FDC = 1）
        I2 = 0x92,  // 充电机允许粘连检测报文（FDC = 1）
        I3 = 0x93,  // 充电机统计报文（FDC = 1）
        I4 = 0x94,  // 车辆统计报文（FDC = 1）
        max
    } PIDType;

    typedef enum : uint8_t
    {
        FC0 = 0x0,  // 版本协商
        FC1 = 0x10, // 功能协商
        FC2 = 0x20, // 参数配置
        FC3 = 0x30, // 鉴权
        FC4 = 0x40, // 预约
        FC5 = 0x50, // 输出回路检测
        FC6 = 0x60, // 供电模式
        FC7 = 0x70, // 预充及能量传输
        FC8 = 0x80, // 结束
    } FCType;       // 功能模块类型

    typedef enum : uint8_t
    {
        FDC0 = 0,
        FDC1 = 1,
        FDC2 = 2,
        FDC3 = 3,
        FDC4 = 4,
        FDC5 = 5,
        FDC6 = 6,
        FDC7 = 7,
        FDC8 = 8,
        FDC_NUM
    } FDCType;

    typedef enum
    {
        SECC = 0x56, // 桩地址
        EVCC = 0xF4, // 车地址
    } AddressType;   // 地址类型

    typedef enum class CarEndReason : uint8_t
    {
        //* 中止充电
        EV_CONDITION,                       // 达到充电机设定的条件中止
        EV_MANUAL,                          // 人工中止
        EV_APPO_CHANGE,                     // 预约计划变更
        EV_INSULT_FAULT,                    // 绝缘故障
        EV_DISCONNECT,                      // 车辆接口断开
        EV_PE_DISCONNECT,                   // 车辆PE触头断路
        EV_CC2_ANOMAL,                      // CC2电压异常
        EV_CC3_ANOMAL,                      // CC3电压异常
        EV_ELOCK_ANOMAL,                    // 车辆插头电子锁异常
        EV_OTHERS,                          // 其他故障（故障）
        EV_OTHERS_SOS,                      // 其他紧急故障
        EV_S1_OPEN,                         // S1断开
        EV_RECV_END_MSG,                    // 接受到车辆中止报文
        //* 功能模块信息交互超时（只有供电模式与能量传输阶段才会有报文超时 请他模块模块中报文超时为预留）
        EV_TM_FUN_CONFER,                   // 功能协商阶段超时中止
        EV_TM_CONFIG,                       // 参数配置阶段超时中止
        EV_TM_AUTHEN,                       // 鉴权阶段超时
        EV_TM_AUTHEN_MSG,                   // 鉴权阶段报文超时（预留）
        EV_TM_APPO,                         // 预约阶段超时
        EV_TM_APPO_MSG,                     // 预约阶段报文超时（预留）
        EV_TM_CHECK,                        // 输出回路检测阶段超时
        EV_TM_CHECK_MSG,                    // 输出回路阶段报文超时（预留）
        EV_TM_SUPPLY,                       // 供电模式阶段超时
        EV_TM_SUPPLY_MSG,                   // 供电模式阶段报文超时
        EV_TM_ERERGY,                       // 预充及能量传输阶段超时
        EV_TM_ENERGY_MSG,                   // 预充及能量传输阶段报文超时
        EV_TM_EMD,                          // 结束阶段超时
        EV_TM_END_MSG,                      // 结束阶段报文超时（预留）
        //* 功能模块执行失败
        EV_FUN_FAIL,                        // 功能协商必须项协商失败
        EV_OTHER_FUN_FAIL,                  // 其他功能模块协商不成功
        EV_PARA_DISMATC,                    // 参数不匹配
        EV_AUTHEN_FAIL,                     // 鉴权失败
        EV_APPO_WAKEUP_FAIL,                // 预约不允许|唤醒不成功
        EV_SE_SELFCHECK_FAIL,               // 充电机输出回路加测执行失败
        EV_SUPPLY_VOL_HIGH,                 // 供电模式电压过高
        EV_SUPPLY_CURR_BIG,                 // 供电模式电流过大
        EV_SUPPLY_VOL_ERR,                  // 供电电压异常
        EV_SUPPLY_CURR_ERR,                 // 供电电流异常
        EV_SUPPLY_MOD_FAIL,                 // 供电模块投切失败 
        
        EV_CHG_VOL_HIGH,                    // 充电中电压过高
        EV_CHG_CURR_BIG,                    // 充电中电流过大
        EV_CHG_VOL_ERR,                     // 充电中电压异常
        EV_CHG_CURR_ERR,                    // 充电中电流异常
        EV_OVER_TEMP,                       // 充电中车辆插座过温
        EV_UNABLE,                          // 充电中无法结束（正常结束中止时充电机无法停止）
        EV_PAUSE_TIMEOUT,                   // 充电中暂停总时长超限
        EV_PAUSE_CNT_BIG,                   // 充电中暂停次数超限
        EV_PSUSE_CONFLICT,                  // 暂停冲突
        EV_CHG_OTHER_FAULT,                 // 充电中其他故障
        EV_CHG_OTHER_SOS,                   // 充电中其他紧急故障
        //* 阶段确认故障
        EV_PHASE_CONFER_FAIL,               // 阶段确认失败
        EV_PHASE_CONFER_TM,                 // 阶段确认超时
    } CarEndReason;


    typedef enum class ChargerEndReason : uint8_t
    {
        //* 中止充电
        SE_CONDITION,                         // 达到充电机设定的条件中止（正常）
        SE_MANUAL,                            // 人工中止（正常）
        SE_APPO_CHANGE,                       // 预约计划变更（正常）
        SE_APPO_TIME_NO,                      // 已预约未达到预约启动时间（正常）
        SE_SOS,                               // 急停（紧急）
        SE_LEAKAGE_CURR,                      // 漏电（紧急）
        SE_DISCONNECT,                        // 车辆接口断开（紧急）
        SE_CC1_ANOMA,                         // CC1电压异常（紧急）
        SE_LOCK_ANOMA,                        // 电子锁异常（紧急）
        SE_OTHERS,                            // 其他故障（故障）
        SE_OTHERS_SOS,                        // 其他紧急故障（紧急）
        SE_S2_OPEN,                           // S2断开（紧急）
        SE_RECV_STOP_MSG,                     // 接受到车辆中止报文（跟车走（默认：正常））
        //* 功能模块信息交互超时（只有供电模式与能量传输阶段才会有报文超时 其他模块中报文超时为预留）
        SE_TM_FUN_CONFER,                   // 功能协商阶段超时中止
        SE_TM_CONFIG,                       // 参数配置阶段超时中止
        SE_TM_AUTHEN,                       // 鉴权阶段超时
        SE_TM_AUTHEN_MSG,                   // 鉴权阶段报文超时（预留）
        SE_TM_APPO,                         // 预约阶段超时
        SE_TM_APPO_MSG,                     // 预约阶段报文超时（预留）
        SE_TM_CHECK,                        // 输出回路检测阶段超时
        SE_TM_CHECK_MSG,                    // 输出回路阶段报文超时（预留）
        SE_TM_SUPPLY,                       // 供电模式阶段超时
        SE_TM_SUPPLY_MSG,                   // 供电模式阶段报文超时
        SE_TM_ERERGY,                       // 预充及能量传输阶段超时
        SE_TM_ENERGY_MSG,                   // 预充及能量传输阶段报文超时
        SE_TM_EMD,                          // 结束阶段超时
        SE_TM_END_MSG,                      // 结束阶段报文超时（预留）
        //* 功能模块执行失败
        SE_FUN_FAIL,                        // 功能协商必须项协商失败（故障）
        SE_AUTHEN_CONFER_FAIL,              // 鉴权协商不成功（故障）
        SE_PARA_DISMATCH,                   // 参数配置失败，参数不匹配（故障）
        SE_AUTHEN_FAIL,                     // 鉴权失败（故障）
        SE_APPO_NOT_ALLOW,                  // 预约不允许（故障）
        SE_WAKEUP_FAILE,                    // 唤醒不成功（故障）
        SE_VOLT_CHECK_FAIL,                 // 自检充电口电压检测失败（故障）
        SE_STICK_CHECK_FAIL,                // 自检粘连检测失败（故障）
        SE_SHORT_CIRCUIT,                   // 自检短路检测失败（故障）
        SE_INSULT_FAIL,                     // 自检绝缘检测失败（故障）
        SE_DISCHG_FAIL,                     // 自检泄放失败（故障）
        SE_SUPPLY_VOLT_HIGH,                // 供电模式电压过高（紧急）
        SE_SUPPLY_CURR_HIGH,                // 供电模式电流过大（紧急）
        SE_SUPPLY_VOLT_ERR,                 // 供电电压不匹配（故障）
        SE_SUPPLY_CURR_ERR,                 // 供电电流异常（故障）
        SE_SUPPLY_CAR_ERR,                  // 供电车辆逻辑错误（故障）
        SE_SUPPLY_INSULT_FAIL,              // 供电模式绝缘故障（故障）
        SE_SUPPLY_POWER_FAIL,               // 供电模式车辆不行应功率调节（故障）
        SE_CHG_VOLT_HIGH,                   // 充电中电压过高（紧急）
        SE_CHG_CURR_HIGH,                   // 充电中电流过大（紧急）
        SE_PRE_CHG_VOL_DISMATCH,            // 预充电压不匹配（故障）
        SE_CHG_VOL_FAULT,                   // 充电中电压异常（故障）
        SE_CHG_CURR_FAULT,                  // 充电中电流异常（故障）
        SE_CHG_OVER_TEMP,                   // 充电中电缆过温（故障）
        SE_CHG_CAR_ERR,                     // 充电中车辆逻辑错误（故障）
        SE_PAUSETIMEOUT,                    // 充电中暂停总时长超限（故障）
        SE_PAUSE_TIMEOUT,                   // 充电中暂停次数超限（故障）
        SE_PAUSE_CNT_BIG,                   // 暂停冲突（故障）
        SV_CHG_OTHER_FAULT,                 // 充电中其他故障
        SV_CHG_OTHER_SOS,                   // 充电中其他紧急故障
        //* 阶段确认故障
        SV_PHASE_CONFER_FAIL,               // 阶段确认失败
        SV_PHASE_CONFER_TM,                 // 阶段确认超时
    } ChargerEndReason;

    typedef enum : uint8_t
    {
        SuspendCharg = 0x01,           // 中止充电
        FDCTimeout = 0x02,             // 功能模块交互超时
        FDCFaile = 0x03,               // 功能模块执行失败
        PhaseConfirmationFailed = 0x04 // 阶段确认故障
    } EndCodeType;                     // 中止类型

    typedef enum : uint8_t
    {
        NormalEnd = 0x01,  // 正常中止
        ErrorEnd = 0x02,   // 故障中止
        OpponentEnd = 0x03 // 对方主动中止
    } SuspendChargType;

    typedef enum : uint8_t
    {
        // 正常中止
        ConditionEnd = 0x01, // 达到设定条件中止（正常）
        ManualEnd = 0x02,    // 人工中止（正常）
        ChangeEnd = 0x03,    // 预约计划变更（正常）
        SchedulEnd = 0x04    // 未到预约启动时间中止（正常）
    } NormalEndType;

    typedef enum : uint8_t
    {
        // 故障中止
        StopEnd = 0x01,        // 急停（紧急）
        LeakageCurrent = 0x02, // 漏电（紧急）
        DisconnectEnd = 0x03,  // 接口断开（紧急）
        CC1AnomaEnd = 0x04,    // CC1电压异常（紧急）
        LockAnomaEnd = 0x05,   // 电子锁异常（紧急）
        OthersEnd = 0xFE,      // 其他故障（故障停机）
        OthersUrgentEnd = 0xFF // 其他故障（紧急停机）
    } ChargerErrorEndType;

    typedef enum : uint8_t
    {
        InsulationfaultEnd = 0x01, // 绝缘故障（故障）
        CarDisconnectEnd = 0x02,   // 车辆接口断开（紧急）
        PEcircuitbreakEnd = 0x03,  // PE触头断路（紧急）
        CC2AnomaEnd = 0x04,        // CC2电压异常（故障）
        CC3AnomaEnd = 0x05,        // CC3电压异常（紧急）
        CarLockAnomaEnd = 0x06,    // 车辆插头电子锁状态异常（紧急）
        CarotherEnd = 0xFE,        // 其他故障（故障）
        CarOthersUrgentEnd = 0xFF  // 其他故障（紧急）
    } CarErrorEndType;

    typedef enum : uint8_t
    {
        SopenEnd = 0x01,  // S2断开（紧急）S1断开
        RecvEndMsg = 0x02 // 接收到中止报文
    } OpponentEndType;

    typedef enum : uint8_t
    {
        FunctionNegotiaTimeout = 0x01,    // 功能协商超时（故障）
        ParameterConfiguraTimeout = 0x02, // 参数配置超时（故障）
        AuthenticaTimeout = 0x03,         // 鉴权超时（故障）
        ReservationTimeout = 0x04,        // 预约超时（故障）
        SelfCheckTimeout = 0x05,          // 输出回路超时（故障）
        PowerSupplyTimeout = 0x06,        // 供电超时（故障）
        EnergyTransferTimeout = 0x07,     // 预约及能量传输阶段超时（故障）
        EndTimeoutTimeout = 0x08          // 结束超时（故障）
    } FDCTimeoutType;

    typedef enum : uint8_t
    {
        FunctionNegotiaFaile = 0x01,    // 功能协商执行失败
        ParameterConfiguraFaile = 0x02, // 参数配置失败 ------0x01 参数不匹配（故障）
        AuthenticaFaile = 0x03,         // 鉴权失败    ------0x01 鉴权失败（故障）
        ReservationFaile = 0x04,        // 预约失败
        SelfCheckFaile = 0x05,          // 输出回路检测失败
        PowerSupplyFaile = 0x06,        // 供电模式执行失败
        EnergyTransferFaile = 0x07      // 预约及能量传输阶段失败
    } FDCFaileType;

    typedef enum : uint8_t
    {
        MandatoryFaile = 0x01,        // 必须项协商失败（故障）
        AuthenticaNegotiaFaile = 0x02 // 鉴权协商失败（故障）
    } FunctionNegotiaFaileType;

    typedef enum : uint8_t
    {
        ResercationNotAllowed = 0x01, // 预约不允许（故障）
        WakeupFaile = 0x02            // 唤醒不成功（故障）
    } ReservationFaileType;

    typedef enum : uint8_t
    {
        VoltagDetectionFaile = 0x01,       // 充电口电压检测失败（故障）
        AdhesionDetectionFaile = 0x02,     // 粘连检测失败（故障）
        ShortCircuitDetectionFaile = 0x03, // 短路检测失败（故障）
        InsulationTestingFaile = 0x04,     // 绝缘检测失败（故障）
        LeakageFaile = 0x05                // 泄放失败（故障）
    } SelfCheckFaileType;

    typedef enum : uint8_t
    {
        VoltageOverload = 0x01,          // 电压过高（紧急）
        CurrentOverload = 0x02,          // 电流过大（紧急）
        PrechargeVoltageMismatch = 0x03, // 预充电压不匹配（故障）
        VoltageAbnormality = 0x04,       // 电压异常（故障）
        CurrentAbnormality = 0x05,       // 电流异常（故障）
        CableOverheating = 0x06,         // 电缆过温（故障）
        CarLogicalError = 0x07,          // 车辆逻辑错误（故障）
        PauseTimeout = 0x08,             // 暂停超时（故障）
        PauseExceededLimit = 0x09,       // 暂停次数超限（故障）
        PsuseConflict = 0x0A,            // 暂停冲突（故障）
        OtherMalfunction1 = 0xFE,        // 其他故障 （故障）
        OtherMalfunction2 = 0xFF         // 其他故障（紧急）
    } EnergyTransferFaileType;

    typedef enum : uint8_t
    {
        Normalshutdown,   // 正常停机
        Faultyshutdown,   // 故障停机
        Emergencyshutdown // 紧急停机
    } ShutdownmethodType; // 停机类型

    typedef enum : uint8_t
    {
        NOT_ALLOW_ADHESION = 0, // 不允许粘连检测
        ALLOW_ADHESION = 1     // 允许粘连检测
    } AdhesionStatusType;     // 是否允许进行粘连检测

    typedef enum : uint8_t
    {
        NOT_ALLOW = 0,     // 不允许重启重连
        ALLOW = 1         // 允许重启重连
    } ReastartStatusType; // 是否允许重连/重启

    typedef enum : uint8_t
    {
        PHASE_ACK_FAILURE = 0x00, // 阶段确认失败
        PHASE_ACK_SUCCESS = 0x01 // 阶段确认成功
    } PhaseACKType; // 阶段确认类型

    typedef enum : uint8_t{
        WAIT_TESTING = 0x00, // 待检测
        TESTING = 0x01,     // 检测中
        TESTING_OK = 0x02,   // 检测成功
        TESTING_FAIL = 0x03  // 检测失败
    } CheckType; // 自检状态

    typedef enum : uint8_t{
        READY_NOT = 0x00, // 未就绪
        READY_OK = 0x01   // 就绪
    } ReadyType;

    typedef enum : uint8_t{
        WAKEUP_INVAILD = 0x00, // 无效 （用于未被完全唤醒时填充使用）
        WAKEUP = 0xAA, // 唤醒
    }WakeupType; // 唤醒标识

    typedef enum : uint8_t{
        NOT_LOCKED = 0x00, // 未锁止
        LOCKED = 0xAA, // 锁止
        LOCKED_UNKNOWN = 0xFF, // 不可信
    }LatchingStatusType; // 电子锁状态

    typedef enum : uint8_t{
        CONTINUE_WAITING = 0x00, // 继续等待
        DISAGREE_WAIT = 0xDD, // 不同意等待
    }VAuthenStatusType; // 鉴权是否等待

    typedef enum : uint8_t{
        AUTHEN_FAIL = 0x00,
        AUTHEN_SUCCESS = 0xAA
    }AuthenResultType; // 鉴权结果

    typedef enum : uint8_t{
        NEGSUCCSEE = 0xAA, // 协商成功
        NEGFAIL = 0xFF // 协商失败
    }ScheNegoType; // 预约协商结果

    typedef enum : uint8_t{
        NOT_CHARGE_IMMEDIATELY = 0x00,  // 不进行立即充电
        CHARGE_IMMEDIATELY = 0xAA,      // 进行立即充电
        APPOINT_SUCCESSSEND = 0xFF      // 预约成功时发送，对方不对该值做判断
    }SupportChargerType;

    typedef enum : uint8_t{
        SCHEACKT_SUCCESS = 0xAA, // 预约充电确认成功
        SCHEACK_FAIL = 0xFF      // 预约充电确认失败
    }ScheACKType; // 充电机预约充电确认

    typedef enum : uint8_t{
        ACK_CHECK = 0xAA // 确认检测完成
    }ACKCheckType; // 车辆确认检测状态

    typedef enum : uint8_t{
        NOT_ENABLE = 0x00,  // 不允许
        ENABLE = 0x01       // 允许 
    }EnableType; // 是否允许进行粘连检测状态

    typedef enum : uint8_t{
        CAR_WAITESTIMNG = 0x00,     // 待检测
        CAR_TESTING = 0x01,         // 检测中
        CAR_DETECTANOMALY = 0x02,   // 异常中止（无法完成检测）
        CAR_TESTINGOK = 0x03,       // 检测通过（未粘连）
        CAR_TESTINGFAIL = 0x04,     // 检测失败（粘连）
        CAR_NODETECTION = 0xFF      // 本次不检测（不需要检测 | 有不可检测故障）
    }CheckType1; // 车辆粘连检测状态

    typedef enum : uint8_t {
        RESUME = 0x00,  // 暂停恢复
        PAUSE = 0xAA    // 暂停
    }PauseStatusType; // 暂停状态
    

#pragma pack(1)

    //*******************************************版本协商*******************************************

    typedef struct
    {
        uint8_t canType;            // CAN类型CAN2.0B：0x00， CANFD：0x01，CANXL：0x02
        uint8_t versionResult;      // 协商结果 0x00:继续协商， 0x01：协商成功， 0x02：协商失败
        uint8_t protocolVersion[3]; // 协议版本号 BYTE1:主版本号，BYTE2：次版本号，BYTE3：临时版本号
        uint8_t CPVersion;          // 控制导引版本 0x01：2023A控制导引，0x02：2015+控制导引
        uint8_t TLVersion;          // 传输层版本
        uint8_t res;
    } ProtoConferMsgDef;       // 版本协商

    //*******************************************阶段确认*******************************************

    typedef struct
    {
        uint8_t pid;
        uint8_t phaseFC;  // 阶段信息功能代码FC
        uint8_t phaseFDC; // 阶段信息功能描述码FDC
    } PhaseRequestMsgDef;   // 阶段请求报文

    typedef struct
    {
        uint8_t pid;
        uint8_t phaseACK; // 阶段确认 0x00：确认失败， 0xAA：确认成功
    } PhaseAckMsgDef;       // 车辆阶段确认报文

    //*******************************************中止报文*******************************************

    typedef struct
    {
        uint8_t pid;
        uint8_t endCode;    // 中止类型
        uint8_t endReason1; // 中止原因1
        uint8_t endReason2; // 中止原因2
        uint8_t repeat;     // 请求重连 0x00：不请求重连，0xAA请求重连，0xFF无效
    } SeEndMsgDef;      // 充电机中止报文

    typedef struct
    {
        uint8_t pid;
        uint8_t endCode;
        uint8_t endReason1;
        uint8_t endReason2;
        uint8_t repeat; // 请求充电 0x00：不请求重连，0xAA请求重连，0xFF无效
    } CarEndMsgDef;      // 车辆中止报文

    //*******************************************接触器状态*******************************************

    typedef struct
    {
        uint8_t pid;
        uint8_t contactStatus1; // K1状态  0x00:断开， 0xAA:闭合 0xFF：不可信
        uint8_t contactStatus2; // K2状态
    } ContactStateDef;        // 接触器状态报文

    //*******************************************电子锁状态*******************************************

    typedef struct
    {
        uint8_t pid;
        uint8_t elockState; // 电子锁状态 0x00:未锁止， 0xAA：锁止， 0xFF：不可信
    } ElockStateDef;       // 电子锁状态报文

    //*******************************************唤醒报文*******************************************

    typedef struct
    {
        uint8_t pid;
        uint8_t wakeup; // 唤醒标识 0x00:无效， 0xAA唤醒
    } WakeupDef;       // 唤醒报文

    //*******************************************功能协商*******************************************

    typedef struct
    {
        uint8_t pid;
        uint8_t FDCSupport1[8]; // 参数配置    功能模式是否支持第1 - 8个FDC 非0x00:支持 0x00：不支持
        uint8_t FDCSupport2[8]; // 鉴权
        uint8_t FDCSupport3[8]; // 预约
        uint8_t FDCSupport4[8]; // 输出回路检测
        uint8_t FDCSupport5[8]; // 供电模式
        uint8_t FDCSupport6[8]; // 预充及能量传输
        uint8_t FDCSupport7[8]; // 结束
    } SeSupportFDCmsgDef;           // 充电机支持功能报文

    typedef struct
    {
        uint8_t pid;
        uint8_t FDCNegoResult2; // 参数配置      支持则填充FDC值，不支持填充个0x00
        uint8_t FDCNegoResult3; // 鉴权
        uint8_t FDCNegoResult4; // 预约
        uint8_t FDCNegoResult5; // 输出回路检测
        uint8_t FDCNegoResult6; // 供电模式
        uint8_t FDCNegoResult7; // 预充及能量传输
        uint8_t FDCNegoResult8; // 结束
    } FDCResultDef;        // 车辆功能协商确认结果报文


    //*******************************************参数配置（FDC = 1）*******************************************

    typedef struct
    {
        uint8_t pid;
        uint16_t voltMAX;       // 最高输出电压 0.1V/位  范围：0V - 6500.0V
        uint16_t voltMIN;       // 最低输出电压 0.1V/位  范围：0V - 6500.0V
        uint16_t currMAX;       // 最大输出电流 0.1A/位  范围：0A - 6500.0A
        uint16_t currentMIN;    // 最小输出电流 0.1A/位  范围：0A - 6500.0A
        uint8_t restarNum;      // 重新启动次数 0 - 200次 0xFE：次数不限， 0xFF：参数无效
    } SeParaMsgDef; // 充电机充电参数报文

    typedef struct
    {
        uint8_t pid;
        uint16_t currMAX;        // 车辆最大允许充电电流 0.1A/位  范围：0A - 6500.0A
        uint16_t voltMAX;        // 车辆最高允许充电电压 0.1V/位  范围：0V - 6500.0V
        uint16_t inputEnergyMAX; // 车辆最高允许输入总能量 0.1kWh/位 0Kwh - 6500.0Kwh 0xFFFF:参数无效 0xFFFE：参数异常
        uint16_t SOC;            // 车辆荷电状态 0.1%位 0% - 100.0%位
        uint16_t battVoltageMAX; // 电池最小并联单元最高允许电压 0.01V/位 0V - 650.00V 0xFFFF:参数无效 0xFFFE：参数异常
        uint8_t battTempMAX;     // 电池单体最高允许温度 1℃/位 -50℃ ~ 200℃  0xFF：参数无效 0xFE：参数异常
        uint8_t battType;        // 动力蓄电池类型 0x01：铅酸， 0x02：镍氢， 0x03：磷酸铁锂， 0x04：锰酸锂， 0x05: 钴酸锂， 0x06: 三元材料， 0x07：聚合物锂离子，0x08：钛酸锂， 0xFF：其他
        uint8_t restarNum;       // 重新启动次数 0 - 200次 0xFE：次数不限， 0xFF：参数无效
    } CarParaMsgDef;      // 车辆充电参数报文

    //*******************************************参数配置（FDC = 2）*******************************************

    typedef struct
    {
        uint8_t pid;
        uint16_t chgVoltMAX;          // 最高充电电压 0.1 0 - 6500
        uint16_t chargVoltMIN;        // 最低充电电压 0.1 0 - 6500
        uint16_t chargCurrMAX;        // 最大充电电流 0.1 0 - 6500
        uint16_t chargCurrMIN;        // 最小充电电流 0.1 0 - 6500
        uint16_t dischgVoltMAX;       // 最高放电电压 0.1 0 - 6500
        uint16_t dischgVoltMIN;       // 最低放电电压 0.1 0 - 6500
        uint16_t dischgCurrMAX;       // 最大放电电流 0.1 0 - 6500
        uint16_t dischgCurrMIN;       // 最小放电电流 0.1 0 - 6500
        uint8_t restarNum;                 // 重新启动次数 0 - 200次 0xFE：次数不限， 0xFF：参数无效
    } SeChgDischgParaMsgDef; // 充电机充放电参数报文

    typedef struct
    {
        uint8_t pid;
        uint16_t chgCurrMAX;           // 最大充电电流 0.1 0 - 6500
        uint16_t chgVoltMAX;           // 最高充电电压 0.1 0 - 6500
        uint16_t inputEnergyMAX;       // 车辆最高允许输入总能量 0.1kWh/位 0Kwh - 6500.0Kwh 0xFFFF:参数无效 0xFFFE：参数异常
        uint16_t SOC;                  // 车辆荷电状态 0.1%位 0% - 100.0%位
        uint16_t dischgCurrMAX;        // 最大允许放电电流 0.1 0 - 6500
        uint16_t dischgCurrMIN;        // 最小允许放电电流 0.1 0 - 6500
        uint16_t dischgVoltMIN;        // 最低允许放电电压 0.1 0 - 6500
        uint16_t battDischgVoltMAX;    // 电池最小并联单位最高放电电压 0.01 0 - 650.00 0xFFFF:参数无效， 0xFFFE：参数异常
        uint16_t battDischgVolteMIN;   // 电池最小并联单位最低放电电压 0.01 0 - 650.00 0xFFFF:参数无效， 0xFFFE：参数异常
        uint8_t  battTempMAX;          // 电池单体最高允许温度 1℃/位 -50℃ ~ 200℃  0xFF：参数无效 0xFE：参数异常
        uint16_t totalBattCycleNum;    // 已进行的充放电循环次数 0.1次/位 0 - 6500 0xFFFF：参数无效
        uint16_t allowedBatteryCycleNum; // 本次充放电可用的剩余充放电循环次数 0.1次/位 0 - 6500 0xFFFF：参数无效
        uint16_t desireResidueRange;   // 本次充放电结束期望的剩余续航里程 0.1Kw 0 - 6500 0xFFFF：数据无效
        uint16_t allowSOCMIN;          // 车辆最低允许放电电荷状态 0.1%位 0% - 100.0%位
        uint8_t battType;        // 动力蓄电池类型 0x01：铅酸， 0x02：镍氢， 0x03：磷酸铁锂， 0x04：锰酸锂， 0x05: 钴酸锂， 0x06: 三元材料， 0x07：聚合物锂离子，0x08：钛酸锂， 0xFF：其他
        uint8_t restarNum;             // 重新启动次数 0 - 200次 0xFE：次数不限， 0xFF：参数无效
    } CarChgDischgParaMsgDef;      // 车辆充放电参数报文

    //*******************************************鉴权（扫码/刷卡 FDC = 1）*******************************************
    typedef struct
    {
        uint8_t pid;
        uint8_t authenState;     // 扫码/刷卡状态 0x00:未完成 0xAA：完成
        uint8_t waitTime;        // 鉴权等待时间 1min/位 0 - 30
    } SeAuthenParaMsgDef; // 充电机鉴权参数报文（FDC = 1）

    typedef struct
    {
        uint8_t pid;
        uint8_t authenState; // 是否等待 0xAA：继续等待 0x00 不同意等待
    } CarAuthenWaitMsgDef;   // 车辆鉴权等待报文（FDC = 1）

    typedef struct
    {
        uint8_t pid;
        uint8_t authenResult; // 鉴权结果 0x00:鉴权失败， 0xAA：鉴权成功
        uint8_t authenFDC;   // 成功鉴权的FDC 0x01:FDC1 0x02：FDC2 0x03：FDC3 0x00:鉴权失败
    } AuthenResultMsgDef;     // 鉴权结果报文

    //*******************************************鉴权（EVIN FDC = 2）*******************************************
    typedef struct
    {
        uint8_t pid;
        uint8_t EVIN[17];
    } EvinMsgDef; // 车辆鉴权参数报文（FDC = 2）

    typedef struct
    {
        uint8_t pid;
        uint8_t authenResult; // 鉴权结果 0x00:鉴权失败， 0xAA：鉴权成功
        uint8_t authenFDC;   // 成功鉴权的FDC 0x01:FDC1 0x02：FDC2 0x03：FDC3 0x00:鉴权失败
    } EvinAuthenResultDef;     // EVIN鉴权结果报文

    typedef struct
    {
        uint8_t pid;
        uint8_t FDC; // 即将进入本功能模块的新FDC
    } EvinAgainAutheMsgDef;

    //*******************************************鉴权（云端 FDC = 3）*******************************************

    typedef struct
    {
        uint8_t pid;
        uint8_t manufacturerIDT[9]; // 车辆生产商编码
    } CarCloudAuthenMsgDef;  // 车辆鉴权参数报文（FDC = 3）

    typedef struct
    {
        uint8_t pid;
        uint8_t operatorID[9];     // 充电运营商码
        uint8_t equipmentID[26];   // 充电设备接口编码
    } SeCloudAuthenInfoDef; // 充电机鉴权参数报文（FDC = 3）

    typedef struct
    {
        uint8_t pid;
        uint8_t authenResult; // 鉴权结果 0x00:鉴权失败， 0xAA：鉴权成功
        uint8_t authenFDC;   // 成功鉴权的FDC 0x01:FDC1 0x02：FDC2 0x03：FDC3 0x00:鉴权失败
    } CloudAuthenResultDef;     // 鉴权结果报文

    typedef struct
    {
        uint8_t pid;
        uint8_t FDC; // 即将进入本功能模块的新FDC
    } CloudAgainAuthenDef;

    //*******************************************预约*******************************************

    typedef struct
    {
        uint8_t pid;
        uint16_t powerMAX;          // 当前最大输出功率  0.1kW/h/位
        uint8_t powerProInt;        // 时刻编码
        uint8_t powerPro[192];      // 连续24h的输出功率百分比
    } SeAppoInfoDef; // 充电机预约充电信息报文

    typedef struct
    {
        uint8_t pid;
        uint16_t desireStartTime;     // 车辆期望多久后开始充电 1min/位 0xFFFF无效
        uint16_t desireGoTime;        // 车辆期望多久后出发 1min/位 0xFFFF无效
    } CarAppoInfoDef; // 车辆预约冲带你信息报文

    typedef struct
    {
        uint8_t pid;
        uint8_t ack;               // 充电机预约充电确认 0xAA：确认成功 0x00：确认失败
        uint8_t immediateChg;      // 充电机支持立即充电标识位 0x00:不支持 0xAA：支持 0xFF：充电机预约成功时发送
    } SeAppoConfirmDef; // 充电机预约确认报文

    typedef struct
    {
        uint8_t pid;
        uint8_t result;       // 车辆预约协商结果 0xAA:协商成功 0x00：协商失败
        uint8_t immediateChg; // 车辆进入立即充电标志位 0x00:不支持 0xAA：支持 0xFF：车辆预约成功时发送
    } CarAppoResultDef;

    //*******************************************输出回路检测*******************************************

    typedef struct
    {
        uint8_t pid;
        uint8_t stick;          // 粘连 0x00:待检测(泄放) 0x01:检测（泄放）中 0x02：检测（泄放）成功 0x03：检测（泄放）失败
        uint8_t shortCircuit;   // 短路
        uint8_t insult;         // 绝缘
        uint8_t discharge;      // 泄放
    } SeSelfCheckInfoMsg;

    typedef struct
    {
        uint8_t pid;
        uint8_t ack; // 确认检测状态 0xAA确认检测完成
    } CarSelfCheckAckDef;       // 车辆检测确认报文；

    //****************************************** 供电模式 *******************************************

    typedef struct {
        uint8_t pid = G1;
        uint8_t ready; // 0x00 未准备 0xAA 就绪
    } SePowerSupStateDef; // 充电机供电状态报文 G1

    typedef struct {
        uint8_t pid = G2;
        uint8_t ready; // 0x00 未准备 0xAA 就绪
    } CarPowerSupStateDef; // 车辆供电状态报文 G2
    
    typedef struct {
        uint8_t pid = G3;
        uint16_t needVol; // 供电需求电压 0.1 0 - 6550
        uint16_t needCurr; // 供电需求电流 0. 1 - 6550
    } CarPowerSupNeedDef; // 车辆供电需求报文 G3
    
    typedef struct {
        uint8_t pid = G4;
        uint16_t AvailCurr; // 可用的最大电流 0.1 0 - 6550
        uint8_t Reason; // 输出能力变化原因 0x00:无变化 0x01：电网原因 0x02：充电机原因 0xFF：其他
    } SePowerDynamicOutDef; // 充电机动态输出能力报文 G4

    typedef struct {
        uint8_t pid = G5;
        uint8_t PowerSupplyEnd; // 车辆结束供电请求 0xAA:结束
    } CarPowerEndDef; // 车辆供电完成报文 G5

    typedef struct {
        uint8_t pid = G6;
        uint16_t outVol; // 供电电压 0.1
        uint16_t outCurr; // 供电电流 0.1
    } SePowerInfoDef; // 充电机供电基本信息 G6

    //*******************************************预充及能量传输（FDC = 1）*******************************************

    typedef struct
    {
        uint8_t pid;
        uint8_t Ready;   // 充电机就绪状态 0x00:未就绪 0xAA:就绪
    } SeReadyMsgDef; // 充电机就绪状态报文；

    typedef struct
    {
        uint8_t pid;
        uint8_t ready;    // 车辆准备就绪状态 0x00:未就绪， 0xAA:就绪
        uint16_t batVol; // 整车充电系统当前电压 0.1V/位 0 - 6500.0
    } CarReadyMsgDef;      // 车辆就绪状态报文

    typedef struct
    {
        uint8_t pid;
        uint16_t needVol;    // 车辆充电电压需求 0.1V/位 0 - 6500.0
        uint16_t needCurr;   // 车辆充电电流需求 0.1A/位 0 - 6500.0
        uint8_t chargeMode;  // 充电模式 0x01:恒流 0x02：恒压
    } CarRequireMsgDef;      // 车辆充电需求报文

    typedef struct
    {
        uint8_t pid;
        uint16_t soc;           // 当前荷电状态 0.1%/位 0 - 100%
        uint16_t residueTimt;   // 剩余估算时间 1min/位
    } CarBasicInfoDef; // 车辆充电基本信息

    typedef struct
    {
        uint8_t pid;
        uint16_t currMax;         // 充电机当前最大输出电流 0.1A/位 0 - 6500.0
        uint8_t reason;           // 充电机当前输出能力变化原因 0x00:无变化 0x01:电网原因 0x02:充电机原因 0xFF：其他原因
    } SeDynamicOutMsgDef; // 充电机动态输出报文

    typedef struct
    {
        uint8_t pid;
        uint16_t outVol;      // 充电机当前输出电压 0.1V
        uint16_t outCurr;     // 充电机当前输出电流 0.1A
    } SeOutInfoDef; // 充电机充电基本信息

    typedef struct
    {
        uint8_t pid;
        uint16_t cellVoltageMAX;      // 电池最小并联单元最高电压 0.01V/位 0xFFFF:参数无效 0xFFFE：数据异常
        uint16_t cellVoltageMIN;      // 电池最小并联单元最低电压
        uint8_t cellTempMAX;          // 电池单体最高温度 1℃/位 0xFF：参数无效 0xFE：数据异常
        uint8_t cellTempMIN;          // 电池单体最低温度
    } CarBattBasicInfoDef; // 车辆充电电池基本信息报文；

    typedef struct
    {
        uint8_t pid;
        uint8_t pause;   // 暂停状态 0xAA: 暂停 0x00:恢复
    } SePauseMsgDef;     // 充电机暂停报文

    typedef struct
    {
        uint8_t pid;
        uint8_t pause; // 暂停状态 0xAA：暂停 0x00:恢复
    } CarPauseDef;         // 车辆暂停报文

    //*******************************************预充及能量传输（FDC = 2）(充放电)*******************************************

    typedef struct {
        uint8_t pid = H10;
        uint8_t ready;   // 充电机就绪状态 0x00:未就绪 0xAA:就绪
        uint8_t PowerDirection; // 充放电初始模式 0x00：车辆充电 0x01：车辆放电
    } SeDisChgReadyDef; // 充电机就绪状态报文（FDC = 2）； H10

    typedef struct {
        uint8_t pid = H11;
        uint8_t ready;    // 车辆准备就绪状态 0x00:未就绪， 0xAA:就绪
        uint16_t batVol; // 整车充电系统当前电压 0.1V/位 0 - 6500.0
        uint16_t Capacity; // 整车当前动力蓄电池总能量 0.1Kw 0xFFFF 无效
        uint16_t DrivingRange; // 车辆当前续航里程 0.1Km 0xFFFF 无效
    } CarDisChgReadyDef; // 车辆就绪状态报文（FDC = 2） H11

    typedef struct {
        uint8_t pid = H12;
        uint16_t needCurr; // 充电机当前需求电流 0.1A
    } SeDisChgNeedDef; // 充电机放电需求报文(FDC = 2) H12

    typedef struct
    {
        uint8_t pid = H13;
        uint16_t currMax;         // 车辆当前最大输出电流 0.1A/位 0 - 6500.0
        uint8_t reason;           // 车辆当前输出能力变化原因 0x00：无变化 0x01：电量不足 0x02：达到设置输出值 0xFF：其他
    } CarDynamicOutDef; // 车辆动态输出报文（FDC = 2） H13

    typedef struct
    {
        uint8_t pid = H14;
        uint16_t needVol;   // 车辆充电电压需求 0.1V/位 0 - 6500.0
        uint16_t needCurr;   // 车辆充电电流需求 0.1A/位 0 - 6500.0
        uint8_t chargeMode; // 充电模式 0x01:恒流 0x02：恒压
    } CarDisChgRequireDef; // 车辆充电需求报文（FDC = 2） H14

    typedef struct
    {
        uint8_t pid = H15;
        uint16_t currMax;   // 充电机当前最大输出电流 0.1A/位 0 - 6500.0
        uint8_t reason;     // 充电机当前输出能力变化原因 0x00：无变化 0x01：电网原因 0x02：充电机原因 0xFF：其他
    } SeDynamicOutDef; // 充电机动态输出能力报文（FDC = 2） H15

    typedef struct {
        uint8_t pid = H16;
        uint16_t SOC; // 当前荷电状态 0.1%
        uint16_t MTime; // 充/放电剩余估算时间 1min
        uint16_t DrivingRange; // 里程 0.1Km
    } CarDisChgInfoDef; // 车辆充放电基本信息（FDC = 2） H16;

    typedef struct {
        uint8_t pid = H17;
        uint16_t outVol; // 充电机当前输出电压 0.1
        uint16_t outCurr; // 充电机当前输出电流 0.1A
        uint16_t DisChgCurr; // 当前放电电流 0.1A
    } SeDisChgInfoDef; // 充电机充放电基本信息（FDC = 2） H17;

    typedef struct
    {
        uint8_t pid = H18;
        uint16_t cellVoltageMAX;      // 电池最小并联单元最高电压 0.01V/位 0xFFFF:参数无效 0xFFFE：数据异常
        uint16_t cellVoltageMIN;      // 电池最小并联单元最低电压
        uint8_t cellTempMAX;          // 电池单体最高温度 1℃/位 0xFF：参数无效 0xFE：数据异常
        uint8_t cellTempMIN;          // 电池单体最低温度
    } CarDisChgBattInfoDef; // 车辆充放电电池基本信息报文（FDC = 2）； H18

    typedef struct
    {
        uint8_t pid = H19;
        uint8_t pause; // 暂停状态 0xAA: 暂停 0x00:恢复
    } SeDisChgPauseMsgDef; // 充电机充放电暂停报文（FDC = 2） H19

    typedef struct
    {
        uint8_t pid = H20;
        uint8_t pause; // 暂停状态 0xAA：暂停 0x00:恢复
    } CarDisChgPauseDef; // 车辆充放电暂停报文（FDC = 2） H20

    //*******************************************结束*******************************************
    typedef struct
    {
        uint8_t pid;
        uint8_t checkState;        // 粘连检测状态信息 0x00:待检测 0x01:检测中 0x02:异常中止（无法完成检测）0x03:检测通过（未粘连）0x04:检测失败（粘连） 0xFF：本次不检测
    } CarStickCheckDef; // 车辆粘连检测报文

    typedef struct
    {
        uint8_t pid;
        uint8_t enable;         // 充电机允许粘连检测 0x00:不允许 0xAA:允许
    } SeCheckEnableDef; // 充电机允许粘连检测报文

    typedef struct
    {
        uint8_t pid;
        uint16_t chgEnergy; // 本次总充电电量 0.1kWh/位
        uint16_t dischgEnergy; // 本次总放电电量
    } SeStatisMsgDef;   // 充电机统计报文

    typedef struct
    {
        uint8_t pid;
        uint16_t soc;  // 车辆中止时的荷电量 0.1% 0 - 100%；
    } CarStatisMsgDef; // 车辆统计报文

    typedef struct{
        ProtoConferMsgDef protoConferM; // 0x00 版本协商报文
        PhaseAckMsgDef phaseAckM;       // 0x02车辆阶段确认报文
        CarEndMsgDef carEndM;           // 0x04车辆中止报文
        ContactStateDef contactStatusM; // 0x06接触器状态报文
        WakeupDef wakeupM;              // 0x09唤醒报文
        FDCResultDef FDCResultM;        // 0x12车辆功能协商确认结果报文
        CarParaMsgDef carParaM;         // 0x22车辆充电参数报文
        CarChgDischgParaMsgDef carChgDischgParaM;   // 0x24车辆充放电参数报文
        CarAuthenWaitMsgDef carAuthenWaitM;         // 0x32车辆鉴权等待报文（FDC = 1）
        EvinMsgDef EvinM;                           // 0x34车辆鉴权参数报文（FDC = 2）
        EvinAgainAutheMsgDef eVINagainAuthenM;  // 0x36重新鉴权请求
        CarCloudAuthenMsgDef cloudAuthenM;      // 0x37车辆鉴权参数报文（FDC = 3）
        CloudAgainAuthenDef cloudAgainAuthenM;  // 0x3A重新鉴权请求
        CarAppoInfoDef carAppoInfoM;            // 0x42车辆预约信息报文
        CarAppoResultDef carAppoConfirmM;       // 0x44车辆预约协商结果
        CarSelfCheckAckDef selfcheckAckM;       // 0x52车辆检测确认报文；
        CarPowerSupStateDef CarPowerSupStateM;  // 0x62车辆供电状态报文
        CarPowerSupNeedDef CarPowerSupNeedM;    // 0x63车辆供电需求报文
        CarPowerEndDef CarPowerEndM;            // 0x65车辆供电完成报文
        CarReadyMsgDef carReqdyM;               // 0x72车辆就绪状态报文（FDC = 1）
        CarRequireMsgDef carRequireM;           // 0x73车辆充电需求报文
        CarBasicInfoDef carBasicInfoM;          // 0x74车辆充电基本信息
        CarBattBasicInfoDef carBatBasicInfoM;   // 0x77车辆充电电池基本信息报文
        CarPauseDef carPauseM;                  // 0x79车辆暂停报文
        CarDisChgReadyDef CarDisChgReadyM;      // 0x82车辆就绪状态报文（FDC = 2）
        CarDynamicOutDef CarDynamicOutM;        // 0x84车辆动态输出能力报文
        CarDisChgRequireDef CarDisChgRequireM;  // 0x85车辆充电需求报文
        CarDisChgInfoDef CarDisChgInfoM;        // 0x87车辆充放电基本信息报文
        CarDisChgBattInfoDef CarDisChgBattInfoM;// 0x89车辆充放电电池基本信息报文
        CarDisChgPauseDef CarDisChgPauseM;      // 0x8B车辆充放电暂停报文
        CarStickCheckDef caStickCheckM;         // 0x91车辆粘连检测报文
        CarStatisMsgDef carStatisM;             // 0x94车辆统计报文
    }CarMsgDef;

    typedef struct{
       ProtoConferMsgDef protoConferM;      // 0x00 版本协商报文
       PhaseRequestMsgDef phaseRequestM;    // 0x01阶段请求报文
       SeEndMsgDef seEndMsgM;               // 0x03充电机中止报文
       ContactStateDef contactStateM;       // 0x05 接触器状态报文
       ElockStateDef elockStateM;           // 0x07电子锁状态报文
       WakeupDef wakeUpM;                   // 0x08唤醒报文报文
       SeSupportFDCmsgDef seSuppFdcM;       // 0x11充电机支持功能报文
       SeParaMsgDef seParaM;                // 0x21充电机充电参数报文
       SeChgDischgParaMsgDef seChgDisChgParaM;  // 0x23充电机充放电参数报文
       SeAuthenParaMsgDef seAuthenParaM;        // 0x31充电机鉴权参数报文（FDC = 1）
       AuthenResultMsgDef authenResultM;        // 0x33鉴权结果报文
       EvinAuthenResultDef evinAuthenResultM;   // 0x35鉴权结果报文
       SeCloudAuthenInfoDef seCloudAuthenInfoM; // 0x38充电机鉴权参数报文（FDC = 3）
       CloudAuthenResultDef cloudAuthenResultM; // 0x39鉴权结果报文
       SeAppoInfoDef seAppoInfoM;               // 0x41充电机预约充电信息报文
       SeAppoConfirmDef seAppoConfirmM;         // 0x43充电机预约确认报文
       SeSelfCheckInfoMsg seSelfCheckInfoM;     // 0x51充电机检测报文；
       SePowerSupStateDef SePowerSupStateM;     // 0x61充电机供电状态报文
       SePowerDynamicOutDef SePowerDynamicOutM; // 0x64充电机动态输出能力报文
       SePowerInfoDef SePowerInfoM;             // 0x66充电机供电基本信息
       SeReadyMsgDef seReadyM;                  // 0x71充电机就绪状态报文；（FDC = 1）
       SeDynamicOutMsgDef seDynamicOutM;        // 0x75充电机动态输出报文
       SeOutInfoDef seoutInfoM;                 // 0x76充电机充电基本信息
       SePauseMsgDef sePauseM;                  // 0x78充电机暂停报文
       SeDisChgReadyDef SeDisChgReadyM;         // 0x81充电机就绪状态报文（FDC = 2）
       SeDisChgNeedDef SeDisChgNeedM;           // 0x83充电机放电需求报文
       SeDynamicOutDef SeDynamicOutM;           // 0x86充电机动态输出能力报文
       SeDisChgInfoDef SeDisChgInfoM;           // 0x88充电机放电基本信息
       SeDisChgPauseMsgDef SeDisChgPauseM;      // 0x8A充电机充放电暂停报文
       SeCheckEnableDef seCheckEnableM;         // 0x92充电机允许粘连检测报文
       SeStatisMsgDef seStatisM;                // 0x93充电机统计报文
    }SeMsgDef;
};

#pragma pack()

#endif