#include "evs_fc.h"
#include "evs_common.h"
#include <cstring>
#include "loger.h"

/***************************************************************************
函数名:ClearMsg
功能描述:
作者:
日期:
****************************************************************************/
int32_t EvsFc::SendCarMsg(GB2015P::PIDType type){
    CarMsgUpdate(type);
    CanSend_.sendMsg(&(CarMsgMap_[type].msg));

    // Msg_Parameters msg = CarMsgMap_[type].msg;
    if(This.fcState.evsState != ENERGY_TRANSFER && This.funState != FUN_WAIT2){
        // Loger(NORMAL,"SendCarMsg: type = %x,data= %d-%d-%d-%d-%d-%d-%d-%d",type,msg.data[0],msg.data[1],msg.data[2],msg.data[3],
        //     msg.data[4],msg.data[5],msg.data[6],msg.data[7]);
    }
    return 0;
}
/***************************************************************************
函数名:CarMsgUpdate
功能描述:
作者:
日期:
****************************************************************************/
int32_t EvsFc::CarMsgUpdate(GB2015P::PIDType type){
    CarMsgFromFC_ = CarMsgToEx_;
    switch(type){
        case GB2015P::X0:
            CarMsg_.protoConferM = CarMsgFromFC_.protoConferM;
        break;
        case GB2015P::X2:
            CarMsg_.phaseAckM.phaseACK = CarMsgFromFC_.phaseAckM.phaseACK;
            CarMsg_.phaseAckM.pid = type;
        break;
        case GB2015P::X4:
            CarMsg_.carEndM = CarMsgFromFC_.carEndM;
            CarMsg_.carEndM.pid = type;
        break;
        case GB2015P::X6:
            CarMsg_.contactStatusM = CarMsgFromFC_.contactStatusM;
            CarMsg_.contactStatusM.pid = type;
        case GB2015P::X9:
            CarMsg_.wakeupM = CarMsgFromFC_.wakeupM;
            CarMsg_.wakeupM.pid = type;
        break;
        case GB2015P::B2:
            CarMsg_.FDCResultM = CarMsgFromFC_.FDCResultM;
            CarMsg_.FDCResultM.pid = type;
            Loger(NORMAL,"CarMsg_.FDCResultM: %d-%d-%d-%d-%d-%d-%d-%d",CarMsg_.FDCResultM.pid,CarMsg_.FDCResultM.FDCNegoResult2,
                CarMsg_.FDCResultM.FDCNegoResult3,CarMsg_.FDCResultM.FDCNegoResult4,CarMsg_.FDCResultM.FDCNegoResult5,
                CarMsg_.FDCResultM.FDCNegoResult6,
                CarMsg_.FDCResultM.FDCNegoResult7,CarMsg_.FDCResultM.FDCNegoResult8);
        break;
        case GB2015P::C2:
            CarMsg_.carParaM = CarMsgFromFC_.carParaM;
            CarMsg_.carParaM.pid = type;
        break;
        case GB2015P::D2:
            CarMsg_.carAuthenWaitM.authenState = CarMsgFromFC_.carAuthenWaitM.authenState;
            CarMsg_.carAuthenWaitM.pid = type;
        break;
        case GB2015P::D4:
            memcpy(CarMsg_.EvinM.EVIN,CarMsgFromFC_.EvinM.EVIN,sizeof(CarMsg_.EvinM.EVIN));
            CarMsg_.EvinM.pid = type;
        break;
        case GB2015P::D6:
            if(U8BIT_GET(FunNegoResult_[FC_AUTHEN],0)){ //scan authention
                CarMsg_.eVINagainAuthenM.FDC = 1;
            }else if(U8BIT_GET(FunNegoResult_[FC_AUTHEN],2)){ //cloud authention
                CarMsg_.eVINagainAuthenM.FDC = 3;
            }
            CarMsg_.eVINagainAuthenM.pid = type;
        break;
        case GB2015P::D7:
            memcpy(CarMsg_.cloudAuthenM.manufacturerIDT,CarMsgFromFC_.cloudAuthenM.manufacturerIDT,
                    sizeof(CarMsg_.cloudAuthenM.manufacturerIDT));
            CarMsg_.cloudAuthenM.pid = type;
        break;
        case GB2015P::D10:
            if(U8BIT_GET(FunNegoResult_[FC_APPOINT],0)){ //scan authention
                CarMsg_.eVINagainAuthenM.FDC = 1;
            }
            CarMsg_.eVINagainAuthenM.pid = type;
            // else if(U8BIT_GET(FunNegoResult_[FC_APPOINT],1)){ //vin authention
            //     CarMsg_.eVINagainAuthenM.FDC = 2;
            // }
        break;
        case GB2015P::E2:
            CarMsg_.carAppoInfoM.desireStartTime = CarMsgFromFC_.carAppoInfoM.desireStartTime;
            CarMsg_.carAppoInfoM.desireGoTime = CarMsgFromFC_.carAppoInfoM.desireGoTime;
            CarMsg_.carAppoInfoM.pid = type;
        break;
        case GB2015P::E4:
            CarMsg_.carAppoConfirmM = CarMsgFromFC_.carAppoConfirmM;
            if(CarMsg_.carAppoConfirmM.result == 0xAA) {
                CarMsg_.carAppoConfirmM.immediateChg = 0XFF;
            } else {
                CarMsg_.carAppoConfirmM.result = 0x00;
                if(CarMsg_.carAppoConfirmM.immediateChg == 0xFF) {
                    CarMsg_.carAppoConfirmM.immediateChg = 0xAA;
                }
            }
            CarMsg_.carAppoConfirmM.pid = type;
        break;
        case GB2015P::F2:
            CarMsg_.selfcheckAckM = CarMsgFromFC_.selfcheckAckM;
            CarMsg_.selfcheckAckM.pid = type;
        break;
        case GB2015P::G2:
            CarMsg_.CarPowerSupStateM = CarMsgFromFC_.CarPowerSupStateM;
            CarMsg_.CarPowerSupStateM.pid = type;
        break;
        case GB2015P::G3:
            CarMsg_.CarPowerSupNeedM = CarMsgFromFC_.CarPowerSupNeedM;
            CarMsg_.CarPowerSupNeedM.pid = type;
        break;
        case GB2015P::G5:
            CarMsg_.CarPowerEndM = CarMsgFromFC_.CarPowerEndM;
            CarMsg_.CarPowerEndM.pid = type;
        break;
        case GB2015P::H2:
            CarMsg_.carReqdyM = CarMsgFromFC_.carReqdyM;
            // CarMsg_.carReqdyM.ready = FcCaredPara_.carReady;
            // CarMsg_.carReqdyM.batVol = FcCaredPara_.batVol;
            CarMsg_.carReqdyM.pid = type;
        break;
         case GB2015P::H3: 
            CarMsg_.carRequireM = CarMsgFromFC_.carRequireM;
            CarMsg_.carRequireM.pid = type;
        break;
        case GB2015P::H4: 
            CarMsg_.carBasicInfoM = CarMsgFromFC_.carBasicInfoM;
            CarMsg_.carBasicInfoM.pid = type;
        break;
        case GB2015P::H7: 
            CarMsg_.carBatBasicInfoM = CarMsgFromFC_.carBatBasicInfoM;
            CarMsg_.carBatBasicInfoM.pid = type;
        break;
        case GB2015P::H9:  
            CarMsg_.carPauseM = CarMsgFromFC_.carPauseM;
            CarMsg_.carPauseM.pid = type;
        break;
        case GB2015P::I1: 
            CarMsg_.caStickCheckM = CarMsgFromFC_.caStickCheckM;
            CarMsg_.caStickCheckM.pid = type;
        break;
        case GB2015P::I4: 
            CarMsg_.carStatisM = CarMsgFromFC_.carStatisM;
            CarMsg_.carStatisM.pid = type;
        break;
        
        default :
        break;
    }
    return 0;
}
/***************************************************************************
函数名:FunNegoResult
功能描述:判断功能协商是否成功
作者:
日期:
****************************************************************************/
bool EvsFc::FunNegoResult()
{
    memset(FunNegoResult_,0,sizeof(FunNegoResult_));
    // get charger result
    for(uint32_t i = 0; i < 8;++i){ //fdc = 0~8
        if(SeMsg_.seSuppFdcM.FDCSupport1[i] != 0){
            U8BIT_SET(FunNegoResult_[FC_PARA_CONFIG],i);
        }
        if(SeMsg_.seSuppFdcM.FDCSupport2[i] != 0){
           U8BIT_SET(FunNegoResult_[FC_AUTHEN],i);
        }
        if(SeMsg_.seSuppFdcM.FDCSupport3[i] != 0){
            U8BIT_SET(FunNegoResult_[FC_APPOINT],i);
        }
        if(SeMsg_.seSuppFdcM.FDCSupport4[i] != 0){
            U8BIT_SET(FunNegoResult_[FC_SELF_CHECK],i);
        }
        if(SeMsg_.seSuppFdcM.FDCSupport5[i] != 0){
            U8BIT_SET(FunNegoResult_[FC_POWER_SUPPLY],i);
        }
        if(SeMsg_.seSuppFdcM.FDCSupport6[i] != 0){
            U8BIT_SET(FunNegoResult_[FC_ENERGY_TRANSFER],i);
        }
        if(SeMsg_.seSuppFdcM.FDCSupport7[i] != 0){
            U8BIT_SET(FunNegoResult_[FC_TRANSFER_END],i);
        }
    }
    // record nego result
    FunNegoResult_[FC_PARA_CONFIG] &= CarMsgFromUser_.FDCResultM.FDCNegoResult2;
    FunNegoResult_[FC_AUTHEN] &= CarMsgFromUser_.FDCResultM.FDCNegoResult3;
    FunNegoResult_[FC_APPOINT] &= CarMsgFromUser_.FDCResultM.FDCNegoResult4;
    FunNegoResult_[FC_SELF_CHECK] &= CarMsgFromUser_.FDCResultM.FDCNegoResult5;
    FunNegoResult_[FC_POWER_SUPPLY] &= CarMsgFromUser_.FDCResultM.FDCNegoResult6;
    FunNegoResult_[FC_ENERGY_TRANSFER] &= CarMsgFromUser_.FDCResultM.FDCNegoResult7;
    FunNegoResult_[FC_TRANSFER_END] &= CarMsgFromUser_.FDCResultM.FDCNegoResult8;
    Loger(NORMAL,"FunNegoResult_:: F2 = %d, F3 = %d, F4 = %d, F5 = %d, F6 = %d, F7 = %d, F8 = %d",
        FunNegoResult_[2],FunNegoResult_[3],FunNegoResult_[4],FunNegoResult_[5],FunNegoResult_[6]
        ,FunNegoResult_[7],FunNegoResult_[8]);
    // fill msg to charger
    int8_t fdc = 0;
    GB2015P::FDCResultDef rlt;
    memset(&rlt,0,sizeof(rlt));
    for(fdc = GB2015P::FDC0; fdc < GB2015P::FDC_NUM-1; ++fdc){
        if(rlt.FDCNegoResult2 == 0){
            if(U8BIT_GET(FunNegoResult_[FC_PARA_CONFIG],fdc)){
                rlt.FDCNegoResult2 = fdc+1;
            }
        }
        if(U8BIT_GET(FunNegoResult_[FC_AUTHEN],fdc)){
            rlt.FDCNegoResult3 = fdc+1;
        }

        if(rlt.FDCNegoResult4 == 0){
           if(U8BIT_GET(FunNegoResult_[FC_APPOINT],fdc)){
                rlt.FDCNegoResult4 = fdc+1;
            }
        }
        if(rlt.FDCNegoResult5 == 0){
            if(U8BIT_GET(FunNegoResult_[FC_SELF_CHECK],fdc)){
                rlt.FDCNegoResult5 = fdc+1;
            }
        }
        if(rlt.FDCNegoResult6 == 0){
            if(U8BIT_GET(FunNegoResult_[FC_POWER_SUPPLY],fdc)){
                rlt.FDCNegoResult6 = fdc+1;
            }
        }
        if(rlt.FDCNegoResult7 == 0){
           if(U8BIT_GET(FunNegoResult_[FC_ENERGY_TRANSFER],fdc)){
                rlt.FDCNegoResult7 = fdc+1;
            }
        }
        if(rlt.FDCNegoResult8 == 0){
            if(U8BIT_GET(FunNegoResult_[FC_TRANSFER_END],fdc)){
                rlt.FDCNegoResult8 = fdc+1;
            }
        } 
    }
    CarMsgToEx_.FDCResultM = rlt;
    return true;
}