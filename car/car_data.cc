/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class loger Definition here.
 */
#include "loger.h"
#include "evs_car.h"
#include "evs_manager.h"
#include "mt_timer.h"
#include "adc.h"
#include "gb2015p_msgtype.h"
#include "evs_proto.h"
#include "evs_database.h"
#include "evs_storage.h"

void  EVSCar::setProtoEVSLoginData()
{ 

    // if(registMsgInitFlag)
    // {
        //第一次注册
       uint64_t u64_ip = htonl(inet_addr(getlocalIP("eth0").c_str()));
        eVSProto.eVSManageData.evsModuleRegisterManageBuff.evsID = u64_ip;
        eVSProto.eVSManageData.evsModuleRegisterManageBuff.interval = 1000;
        eVSProto.eVSManageData.MsgInfoUpdate();
        // eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.protoConferM = UserSetData_.protoConferM;  // 版本协商信息
        // eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.funConferM = UserSetData_.funConferM;      // 功能协商
        // eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.paraConfigM = UserSetData_.paraConfigM; // 车辆充电参数配置
        // eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.authenM = UserSetData_.authenM;            // 鉴权信息
        // eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.reserveM = UserSetData_.reserveM;          // 预约充电信息
        // eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.powerSupplyM = UserSetData_.powerSupplyM;  // 供电模式
        // eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.chargingM = UserSetData_.chargingM;        // 充电阶段信息
        // eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.chargingEndM = UserSetData_.chargingEndM;  // 充电结束信息

        // eVSProto.eVSManageData.electriCtrlManageBuff.ipConfig = UserSetData_.ipConfig;         //IP配置信息

    // }
    // else
    // {
    //     eVSProto.eVSManageData.setEvsModuleRegisterManageData();   //非第一次注册
    // }

}

void  EVSCar::setProtoEVSHeartData(void)
{
    eVSProto.eVSManageData.controlCmdRspManageBuff.evsState.batVol = ((EVSManager*)_m_ctx)->EvsMeter_.GetVol();
    eVSProto.eVSManageData.controlCmdRspManageBuff.evsState.batCur = ((EVSManager*)_m_ctx)->EvsMeter_.GetCurr();
    eVSProto.eVSManageData.controlCmdRspManageBuff.evsState.nowSOC = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.chargingM.socNow;
    eVSProto.eVSManageData.controlCmdRspManageBuff.evsState.nowFC = DataFromFC_.FCState.FC;
    eVSProto.eVSManageData.controlCmdRspManageBuff.evsState.nowFDC =  DataFromFC_.FCState.FDC;
    eVSProto.eVSManageData.controlCmdRspManageBuff.evsState.chgMode = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.chargingM.chargeMode;
    eVSProto.eVSManageData.controlCmdRspManageBuff.evsState.insultPosR = eVSProto.eVSManageData.electriCtrlManageBuff.insultCtrl.insultPosRes;
    eVSProto.eVSManageData.controlCmdRspManageBuff.evsState.insultNegR = eVSProto.eVSManageData.electriCtrlManageBuff.insultCtrl.insultNegRes;
    eVSProto.eVSManageData.controlCmdRspManageBuff.evsState.cc1Volt = (float)F_AD_VALUE[0][ADC_CC1] / 10;
    eVSProto.eVSManageData.controlCmdRspManageBuff.evsState.cc2Volt = (float)F_AD_VALUE[1][ADC_CC1] / 10;
    eVSProto.eVSManageData.controlCmdRspManageBuff.evsState.workMode = 1;   //等待后续模块完善，重新设置
    eVSProto.eVSManageData.controlCmdRspManageBuff.evsState.stopCode = DataFromFC_.stopMsg.stopCode;
}

/***************************************************************************
函数名:ProtoInputUpdate
功能描述:
作者:
日期:
****************************************************************************/
int32_t EVSCar::ProtoInputUpdate()
{
    //EVS心跳数据(0x1) 数据
    setProtoEVSHeartData();
    return 0;
}

/***************************************************************************
函数名:ProtoOutputUpdate
功能描述:
作者:
日期:
****************************************************************************/
int32_t EVSCar::ProtoOutputUpdate(EvsFrameMessageEnum FramType)
{

     switch (FramType){
        case TYPE_VEHICLE_SIDE_MSG_FILL:
        {
            //0x02数据更新
            StorageParaDef  save_msg;
            save_msg = GetUserData();   //读取现有数据，然后更新，避免IP信息被覆盖
            save_msg.protoConferM = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.protoConferM;  // 版本协商信息
            save_msg.funConferM = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.funConferM;      // 功能协商
            save_msg.paraConfigM = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.paraConfigM; // 车辆充电参数配置
            save_msg.authenM = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.authenM;            // 鉴权信息
            save_msg.reserveM = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.reserveM;          // 预约充电信息
            save_msg.powerSupplyM = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.powerSupplyM;  // 供电模式
            save_msg.chargingM = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.chargingM;        // 充电阶段信息
            save_msg.chargingEndM = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.chargingEndM;  // 充电结束信息
            StorgeUserData(save_msg);

            //ProtoConferMsgDef
            CarMsgFromUser_.protoConferM.canType = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.protoConferM.canType;

            //字符串分割
            string tempstr =  eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.protoConferM.gbVersion;
            uint32_t strlen = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.protoConferM.gbVersion.find('.');
            if(strlen >= 1){
                CarMsgFromUser_.protoConferM.protocolVersion[0] = atoi(tempstr.substr(strlen-1, strlen).c_str());
            }
            string tempstr1 = tempstr.substr(strlen+1, tempstr.length());
            strlen = tempstr.substr(strlen+1, tempstr.length()).find('.');
            CarMsgFromUser_.protoConferM.protocolVersion[1] =  atoi(tempstr1.substr(0, strlen).c_str());

            strlen = tempstr.rfind('.');
            CarMsgFromUser_.protoConferM.protocolVersion[2] =  atoi(tempstr.substr(strlen+1, strlen+2).c_str());

            Loger(NORMAL, "0x02数据更新 ver = %d-%d-%d -%s\r\n",CarMsgFromUser_.protoConferM.protocolVersion[0]
            ,CarMsgFromUser_.protoConferM.protocolVersion[1],CarMsgFromUser_.protoConferM.protocolVersion[2],tempstr.c_str());
            CarMsgFromUser_.protoConferM.CPVersion = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.protoConferM.guidanceVersion;
            CarMsgFromUser_.protoConferM.TLVersion = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.protoConferM.transportVersion;
            CarMsgFromUser_.protoConferM.res = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.protoConferM.conferRes;
            //CarMsgFromUser_.protoConferM.versionResult = 0x01 ;   //暂时赋值

            //PhaseAckMsgDef
            //CarMsgFromUser_.phaseAckM.phaseACK ;
        
            //CarEndMsgDef
            
            CarMsgFromUser_.carEndM.endCode = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.chargingEndM.endCode;
            CarMsgFromUser_.carEndM.endReason1 = (uint8_t)eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.chargingEndM.endReason;
            CarMsgFromUser_.carEndM.endReason2 = (uint8_t) ((uint16_t)eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.chargingEndM.endReason >> 8);
            CarMsgFromUser_.carEndM.repeat    = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.chargingEndM.repeat;

            //ContactStateDef
            //CarMsgFromUser_.contactStatusM.contactStatus1;
            //CarMsgFromUser_.contactStatusM.contactStatus2;
            
            //FDCResultDef 
            CarMsgFromUser_.FDCResultM.FDCNegoResult2 = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.funConferM.configFDC;
            CarMsgFromUser_.FDCResultM.FDCNegoResult3 = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.funConferM.authenFDC;
            CarMsgFromUser_.FDCResultM.FDCNegoResult4 = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.funConferM.appointFDC;
            CarMsgFromUser_.FDCResultM.FDCNegoResult5 = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.funConferM.selfCheckFDC;
            CarMsgFromUser_.FDCResultM.FDCNegoResult6 = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.funConferM.powerSupplyFDC;
            CarMsgFromUser_.FDCResultM.FDCNegoResult7 = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.funConferM.energyTransferFDC;
            CarMsgFromUser_.FDCResultM.FDCNegoResult8 = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.funConferM.endFDC;

            //CarParaMsgDef
            CarMsgFromUser_.carParaM.currMAX = (uint16_t)(eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.paraConfigM.currAllowMAX * CONVERT_WEIGHTS_NUM_TEN);
            CarMsgFromUser_.carParaM.voltMAX = (uint16_t)(eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.paraConfigM.voltAllowMAX * CONVERT_WEIGHTS_NUM_TEN);
            CarMsgFromUser_.carParaM.inputEnergyMAX = (uint16_t)(eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.paraConfigM.energyAllowMAX * CONVERT_WEIGHTS_NUM_TEN);
            CarMsgFromUser_.carParaM.SOC = (uint16_t)(eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.paraConfigM.nowSOC * CONVERT_WEIGHTS_NUM_TEN);
            CarMsgFromUser_.carParaM.battVoltageMAX = (uint16_t)(eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.paraConfigM.cellVoltAllowMAX * CONVERT_WEIGHTS_NUM_ONE_HUNDRED);
            CarMsgFromUser_.carParaM.battTempMAX = (uint16_t)(eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.paraConfigM.batTempAllowMAX + TEMPERATURE_OFFSET);
            CarMsgFromUser_.carParaM.battType = 0xFF; //!  proto 无该字段 暂时写死
            CarMsgFromUser_.carParaM.restarNum = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.paraConfigM.restarNum;

            //CarChgDischgParaMsgDef  
            CarMsgFromUser_.carChgDischgParaM.chgCurrMAX = (uint16_t)(eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.paraConfigM.currAllowMAX * CONVERT_WEIGHTS_NUM_TEN);
            CarMsgFromUser_.carChgDischgParaM.chgVoltMAX = (uint16_t)(eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.paraConfigM.voltAllowMAX * CONVERT_WEIGHTS_NUM_TEN);
            CarMsgFromUser_.carChgDischgParaM.inputEnergyMAX = (uint16_t)(eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.paraConfigM.energyAllowMAX * CONVERT_WEIGHTS_NUM_TEN);
            CarMsgFromUser_.carChgDischgParaM.SOC =  (uint16_t)(eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.paraConfigM.nowSOC * CONVERT_WEIGHTS_NUM_TEN);
            CarMsgFromUser_.carChgDischgParaM.battTempMAX = (uint16_t)(eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.chargingM.celltempMax + TEMPERATURE_OFFSET);
                // CarMsgFromUser_.carChgDischgParaM.dischgCurrMAX
                // CarMsgFromUser_.carChgDischgParaM.dischgCurrMIN
                // CarMsgFromUser_.carChgDischgParaM.dischgVoltMIN
                // CarMsgFromUser_.carChgDischgParaM.battDischgVoltMAX
                // CarMsgFromUser_.carChgDischgParaM.battDischgVolteMIN
                // CarMsgFromUser_.carChgDischgParaM.totalBattCycleNum
                // CarMsgFromUser_.carChgDischgParaM.allowedBatteryCycleNum
                // CarMsgFromUser_.carChgDischgParaM.desireResidueRange
                // CarMsgFromUser_.carChgDischgParaM.allowSOCMIN
                // CarMsgFromUser_.carChgDischgParaM.battType
                // CarMsgFromUser_.carChgDischgParaM.restarNum


            //EvinMsgDef
            for(size_t i = 0; i<sizeof(CarMsgFromUser_.EvinM.EVIN) / sizeof(CarMsgFromUser_.EvinM.EVIN[0]);i++)
            {
                CarMsgFromUser_.EvinM.EVIN[i] = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.authenM.eVIN[i];
            }
        
            //EvinAgainAutheMsgDef
            CarMsgFromUser_.eVINagainAuthenM.FDC = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.authenM.nextFDC;

            //CarCloudAuthenMsgDef  
            for(size_t i = 0; i<sizeof(CarMsgFromUser_.cloudAuthenM.manufacturerIDT) / sizeof(CarMsgFromUser_.cloudAuthenM.manufacturerIDT[0]);i++)
            {
                CarMsgFromUser_.cloudAuthenM.manufacturerIDT[i] = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.authenM.eVIN[i];
            }
            // CloudAgainAuthenDef 
            CarMsgFromUser_.cloudAgainAuthenM.FDC = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.authenM.nextFDC;


            //CarAppoInfoDef
            CarMsgFromUser_.carAppoInfoM.desireStartTime = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.reserveM.bmsDesireStartTime;
            CarMsgFromUser_.carAppoInfoM.desireGoTime = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.reserveM.bmsDesireLeaveTime;
            

            //CarAppoResultDef
            CarMsgFromUser_.carAppoConfirmM.result = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.reserveM.reserveResult;
            CarMsgFromUser_.carAppoConfirmM.immediateChg = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.reserveM.immediateChargeSupport;

            //PowerSupDef
            CarMsgFromUser_.CarPowerSupStateM.ready = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.powerSupplyM.supplyState == 0x01 ? 0xAA : 0x00;
            CarMsgFromUser_.CarPowerSupNeedM.needCurr = (uint16_t)(eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.powerSupplyM.supplyCurrDesire * CONVERT_WEIGHTS_NUM_TEN);
            CarMsgFromUser_.CarPowerSupNeedM.needVol = (uint16_t)(eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.powerSupplyM.supplyVolDesire * CONVERT_WEIGHTS_NUM_TEN);
            CarMsgFromUser_.CarPowerEndM.PowerSupplyEnd = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.powerSupplyM.supplyEnd;


            //CarRequireMsgDef
            CarMsgFromUser_.carRequireM.needVol = (uint16_t)(eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.chargingM.volDemand * CONVERT_WEIGHTS_NUM_TEN);
            CarMsgFromUser_.carRequireM.needCurr = (uint16_t)(eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.chargingM.curDemand * CONVERT_WEIGHTS_NUM_TEN);
            CarMsgFromUser_.carRequireM.chargeMode = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.chargingM.chargeMode;

            //CarBasicInfoDef
            CarMsgFromUser_.carBasicInfoM.soc = (uint16_t)(eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.chargingM.socNow * CONVERT_WEIGHTS_NUM_TEN);
            CarMsgFromUser_.carBasicInfoM.residueTimt = eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.chargingM.resChgTime;

            //CarBattBasicInfoDef
            CarMsgFromUser_.carBatBasicInfoM.cellVoltageMAX = (uint16_t)(eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.chargingM.cellBatVolMax * CONVERT_WEIGHTS_NUM_ONE_HUNDRED);
            CarMsgFromUser_.carBatBasicInfoM.cellVoltageMIN = (uint16_t)(eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.chargingM.cellBatVolMin * CONVERT_WEIGHTS_NUM_ONE_HUNDRED);
            CarMsgFromUser_.carBatBasicInfoM.cellTempMAX = (uint16_t)(eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.chargingM.celltempMax + TEMPERATURE_OFFSET);
            CarMsgFromUser_.carBatBasicInfoM.cellTempMIN = (uint16_t)(eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.chargingM.celltempMin + TEMPERATURE_OFFSET);

            //CarStatisMsgDef carStatisM
            CarMsgFromUser_.carStatisM.soc = (uint16_t)(eVSProto.eVSManageData.vehicleSideMsgFillManageBuff.paraConfigM.nowSOC * CONVERT_WEIGHTS_NUM_TEN);
            }break;
        //0x03数据更新
        case TYPE_MSG_ATTR:
            {
            Loger(NORMAL, "0x03数据更新\r\n");
            // CarAuthenWaitMsgDef
            CarMsgFromUser_.carAuthenWaitM.authenState = eVSProto.eVSManageData.msgCtrlInfoManageBuff.msgCtrl.d2_0x32_State;
            
            //CarSelfCheckAckDef
            CarMsgFromUser_.selfcheckAckM.ack = eVSProto.eVSManageData.msgCtrlInfoManageBuff.msgCtrl.f2_0x52_State;

            //CarReadyMsgDef
            CarMsgFromUser_.carReqdyM.ready = eVSProto.eVSManageData.msgCtrlInfoManageBuff.msgCtrl.h2_0x72_State == 0x01 ? 0xAA : 0x00;
            //CarMsgFromUser_.carReqdyM.batVol

            //CarPauseDef
            CarMsgFromUser_.carPauseM.pause = eVSProto.eVSManageData.msgCtrlInfoManageBuff.msgCtrl.h9_0x79_State;

            //CarStickCheckDef
            CarMsgFromUser_.caStickCheckM.checkState = eVSProto.eVSManageData.msgCtrlInfoManageBuff.msgCtrl.i1_0x91_State;
            
            //CarMsgCtrlPara  updata
            for(int i=1; i<CAR_MSG_NUM; i++)
            {
                CarMsgCtrlPara[i].enable = *((uint32_t *)&eVSProto.eVSManageData.msgCtrlInfoManageBuff.msgCtrl + i -1);
                CarMsgCtrlPara[i].cycle = *((uint32_t *)&eVSProto.eVSManageData.msgCtrlInfoManageBuff.msgCycleTime + i - 1);
                CarMsgCtrlPara[i].maxTime = *((uint32_t *)&eVSProto.eVSManageData.msgCtrlInfoManageBuff.msgMaxSendTIme + i - 1);
            } 

            // EvFunNegoAck_  updata
            EvFunNegoAck_.funConferAck = eVSProto.eVSManageData.msgCtrlInfoManageBuff.funConferAck.funConferAck;
            EvFunNegoAck_.configAck = eVSProto.eVSManageData.msgCtrlInfoManageBuff.funConferAck.configAck;
            EvFunNegoAck_.authenAck = eVSProto.eVSManageData.msgCtrlInfoManageBuff.funConferAck.authenAck;
            EvFunNegoAck_.appointAck = eVSProto.eVSManageData.msgCtrlInfoManageBuff.funConferAck.appointAck;
            EvFunNegoAck_.selfCheckAck = eVSProto.eVSManageData.msgCtrlInfoManageBuff.funConferAck.selfCheckAck;
            EvFunNegoAck_.powerSupplyAck = eVSProto.eVSManageData.msgCtrlInfoManageBuff.funConferAck.powerSupplyAck;
            EvFunNegoAck_.energyTransferAck = eVSProto.eVSManageData.msgCtrlInfoManageBuff.funConferAck.energyTransferAck;
            EvFunNegoAck_.endAck = eVSProto.eVSManageData.msgCtrlInfoManageBuff.funConferAck.endAck;
            }break;
        //0x04数据更新
        case TYPE_ELECTRI_CTRL:{
            //CarReadyMsgDef
            CarMsgFromUser_.carReqdyM.batVol = (uint16_t)(eVSProto.eVSManageData.electriCtrlManageBuff.insultCtrl.batVol *CONVERT_WEIGHTS_NUM_TEN);
            StorageParaDef  preSaveMsg;
            preSaveMsg = GetUserData();   //读取现有数据，然后更新，避免02帧数据被覆盖
            if((preSaveMsg.ipConfig.locolIP != eVSProto.eVSManageData.electriCtrlManageBuff.ipConfig.locolIP)||
            (preSaveMsg.ipConfig.serverIP != eVSProto.eVSManageData.electriCtrlManageBuff.ipConfig.serverIP))
            {
                    preSaveMsg.ipConfig = eVSProto.eVSManageData.electriCtrlManageBuff.ipConfig;  // ip配置信息保存
                    StorgeUserData(preSaveMsg);
                    Loger(NORMAL,"ip配置变化，程序重启");
                    exit(0);
            }

            }break;
        default:
        break;
     }
    return 0;
}


/***************************************************************************
函数名:ProtoProcess
功能描述:
作者:
日期:
****************************************************************************/
int32_t EVSCar::ProtoProcess()
{
    DeviceTypeEnum  _dev;
    // 发送注册消息，注册态
    for(int i=0;i<MAX_SERVER_NUM;i++){
    if(!eVSProto.protocolWorkState[i])
    {
        setProtoEVSLoginData();
        eVSProto.Send_MessageIntface((DeviceTypeEnum)i,TYPE_EVSMODULE_REGISTER);
        eVSProto.ReceiveMsg(&_dev);
        if(Timer_.Flg_.f1s){
            Loger(NORMAL,"send reg msg to service %d",i);
        }
        // 收到回复帧
        if(eVSProto.eVSManageData.isAckMsgUpdata(TYPE_EVSMODULE_REGISTER_ACK))
        {
            eVSProto.eVSManageData.updataEvsId();
            eVSProto.eVSManageData.updataHeartBeatData(0,0);   //第一次设置心跳的数据(部分数据)
            registMsgInitFlag = 0;
        }
    }
    else  //正常态
    {
        eVSProto.ReceiveMsg(&_dev);

        //0x02
        if(eVSProto.eVSManageData.isAckMsgUpdata(TYPE_VEHICLE_SIDE_MSG_FILL))
        {
           Loger(NORMAL,"TYPE_VEHICLE_SIDE_MSG_FILL update,dev=%d\r\n",_dev);
           
            eVSProto.eVSManageData.setAckMsgData(TYPE_VEHICLE_SIDE_MSG_FILL);   // 返回自己的ID
            //需要获取设置结果，结果进行判断
            eVSProto.Send_MessageIntface(_dev,TYPE_MSG_FILL_ACK);
            //更新数据
            ProtoOutputUpdate(TYPE_VEHICLE_SIDE_MSG_FILL);
            //清除标志
             eVSProto.eVSManageData.upDateFlag &=  ~(0x1 << eVSProto.eVSManageData.upDateFlagMap.at(TYPE_VEHICLE_SIDE_MSG_FILL));
        }

        //0x03
        if(eVSProto.eVSManageData.isAckMsgUpdata(TYPE_MSG_ATTR))
        {
            Loger(NORMAL,"TYPE_MSG_ATTR update,dev=%d\r\n",_dev);
            eVSProto.eVSManageData.setAckMsgData(TYPE_MSG_ATTR);
            eVSProto.Send_MessageIntface(_dev,TYPE_MSG_FILL_ACK);
            //更新数据
            ProtoOutputUpdate(TYPE_MSG_ATTR);
            //清除标志
             eVSProto.eVSManageData.upDateFlag &=  ~(0x1 << eVSProto.eVSManageData.upDateFlagMap.at(TYPE_MSG_ATTR));
        }

        //0x04
        if(eVSProto.eVSManageData.isAckMsgUpdata(TYPE_ELECTRI_CTRL))
        {
            Loger(NORMAL,"TYPE_ELECTRI_CTRL update,dev=%d\r\n",_dev);
            eVSProto.eVSManageData.setAckMsgData(TYPE_ELECTRI_CTRL);
            eVSProto.Send_MessageIntface(_dev,TYPE_MSG_FILL_ACK);
            //更新数据
            ProtoOutputUpdate(TYPE_ELECTRI_CTRL);
            //清除标志
             eVSProto.eVSManageData.upDateFlag &=  ~(0x1 << eVSProto.eVSManageData.upDateFlagMap.at(TYPE_ELECTRI_CTRL));
        
        }

        //0x05
        if(eVSProto.eVSManageData.isAckMsgUpdata(TYPE_STEP_INFO))
        {
             Loger(NORMAL,"TYPE_STEP_INFO update,dev=%d\r\n",_dev);
            eVSProto.eVSManageData.setAckMsgData(TYPE_STEP_INFO);
            eVSProto.Send_MessageIntface(_dev,TYPE_MSG_FILL_ACK);
            //清除标志
             eVSProto.eVSManageData.upDateFlag &=  ~(0x1 << eVSProto.eVSManageData.upDateFlagMap.at(TYPE_STEP_INFO));
        }

        //0x1E
        if(eVSProto.eVSManageData.isAckMsgUpdata(TYPE_GET_MSG))
        {
           Loger(NORMAL,"TYPE_GET_MSG update,dev=%d\r\n",_dev);
           
            switch (eVSProto.eVSManageData.getMsgInfoManageBuff.MsgID)
            {
                //查询 0x02
                case TYPE_VEHICLE_SIDE_MSG_FILL:
                    eVSProto.Send_VehicleMsgReport(_dev);
                break;
                //查询 0x03
                case TYPE_MSG_ATTR:
                    eVSProto.Send_MsgAttrReport(_dev);
                break;
                //查询 0x04
                case TYPE_ELECTRI_CTRL:
                    eVSProto.Send_ElecContrlReport(_dev);
                break;
                //查询 0x05
                case TYPE_STEP_INFO:
                    eVSProto.Send_StepInfoReport(_dev);
                break;

                default:
                break;
            }
            eVSProto.eVSManageData.upDateFlag &=  ~(0x1 << eVSProto.eVSManageData.upDateFlagMap.at(TYPE_GET_MSG));
        }

                //心跳send
        if(Timer_.Flg_.f1s)
        {
            if(eVSProto.eVSManageData.isAckMsgUpdata(TYPE_CONTROL_CMD))
            {
                eVSProto.eVSManageData.updataHeartBeatData();
            } 
            eVSProto.Send_MessageIntface((DeviceTypeEnum)i,TYPE_CONTROL_CMD_RSP);
            eVSProto.protocolCounter[i]++;
            if(eVSProto.protocolCounter[i] > HEART_TIME_OUT)  //10秒没有收到回复，重新注册
            {
                Loger(NORMAL,"HB[%d] timeout,re-regist\r\n",_dev);
                eVSProto.protocolWorkState[i] = 0;
                eVSProto.protocolCounter[i] = 0;
            }
        }
    }
    }
    return 0;
}

/***************************************************************************
函数名:ProtoSchedule
功能描述:
作者:
日期:
****************************************************************************/
int32_t EVSCar::ProtoSchedule()
{
    if((Timer_.Flg_.f100ms)&&(eVSProto.socket_ready))
    {
        ProtoInputUpdate();
        ProtoProcess();
    }
    return 0;
}



/***************************************************************************
函数名:FcSchedule
功能描述:
作者:
日期:
****************************************************************************/
int32_t EVSCar::FcSchedule(){

    EvsFC_.FastSchedule();// 3ms
    if(Timer_.Flg_.f10ms){
        // input set
        FcInputUpdate();
        // process
        EvsFC_.FCProcess(); 
        // out data
        DataFromFC_ = EvsFC_.GetOutData();
        // release end fail fault
        if(DataFromFCBak_.FCState.evsState != DataFromFC_.FCState.evsState){
            if(DataFromFC_.FCState.evsState == PROTO_NEGO){
                Fault_.ChgEndFailRelase();
            }
            DataFromFCBak_.FCState.evsState = DataFromFC_.FCState.evsState;
        }

        // if(DataFromFC_.FCState.evsState == PROTO_NEGO){
            
        //     Fault_.GunPullOutFailRelase();
        // }
    }
    if(Timer_.Flg_.f1s){
        if(DataFromFC_.workMode == 0 && Fault_.GetStopFault() == true){
            // FaultMsgDef fault;
            // Fault_.GetStopFault(fault);
            // Loger(NORMAL,"Fault = %d", fault.index);
            Fault_.GunPullOutFailRelase();
        }
    }
    
    return 0;
}
/***************************************************************************
函数名:UserMsgSave
功能描述:
作者:
日期:
****************************************************************************/
int32_t EVSCar::UserMsgSave(void){
    // MsgFromUserDef user;
    // user.id = 5;
    // user.str = 'you are right!';
    // user.time = GetLocolTimeMs();;
    // user.ptr = nullptr;
    // user.vin[0] = 'X';
    // user.vin[1] = 'A';
    // user.vin[2] = 'W';
    // user.vin[3] = 'A';
    // user.vin[4] = 'M';

    // EVSDatabase evs_db;
    // evs_db.DataBaseInit();

    // // EvsDataDef evs_data = {2,0,0};
    // // evs_data.date = GetLocolTimeMs();
    // // uint8_t say[] = "Hi man ,what's up!";
    // // evs_data.len = sizeof(say);
    // // evs_data.data = say;
    // evs_db.DataSave(evs_data);

    // uint8_t  evs_return[128];
    // evs_db.GetData(evs_return,1);
    // //printf("evs_db.GetData len = %d, data = %s",evs_return.length(),evs_return.c_str());
    // if(memcmp(evs_return,say,sizeof(say)-1) == 0){
    //     std::cout<< "evs_return, match !!!!!!!!!!!!!!! "<<std::endl;
    // }else{
    //     std::cout<< "evs_return,not match  "<<std::endl;
    // }
    
    // Loger(NORMAL,"Car Process Begin. evs_db.time = %lld",sizeof(say),evs_data.date);
    return 0;
}