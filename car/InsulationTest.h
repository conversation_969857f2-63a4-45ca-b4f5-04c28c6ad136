/* Copyright 2021, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class VCIGunHolder and VCIGunCharging Declaration here.
 */
#ifndef	_INSULATION_TEST_H
#define _INSULATION_TEST_H

#include "FailManager.h"
#include "evs_common.h"
#include <unistd.h>
#include <vector>


#define	INSULTWARRNVALUE	40
#define	INSULTLVOLTAGEVALUE	430		//Ry = 2M,Rx = 40k
#define	INSULTHVOLTAGEVALUE	4660		//Rx = 2M,Ry = 40k
#define INSULT_SWITCH_RES   50//250     //R3 = 200K
#define SECOND_INSULT_TIME 5 // 500ms
typedef enum
{
	UNKNOW = 0,
    PASS = 1,
    FAIL = 2,
    WARN = 3,
	UNTRUST = 4
}_InsultResultDef;

typedef enum
{
	E_TEST_IDLE = 0,
	E_NEED_TEST = 1,
    E_TEST_READY1 = 2,
    E_TEST_READY2 = 3,
    E_R1R2FIX = 4,
	E_TESTING = 5,
	E_TESTING_STAGE1 = 6,
	E_TESTING_STAGE2= 7,
	E_TEST_END = 8,
}_GunInsulatedStateDef;

typedef struct
{
	uint16_t bInsulResult	:2;
	uint16_t bTestStage		:4;
	uint16_t bRse			:10;
}_InsultStateDef;
typedef enum
{
	INSULT_GND_RELAY = 0,
	INSULTPOS_RELAY = 1,
	INSULTNEG_RELAY = 2,

}RelayIndexDef;

typedef enum
{
	E_NO_CONNECT = 0,
	E_CONNECTING,
	E_CONNECT_OK,
	E_CONNECT_ABORMAL,
}_GunConnectStateDef;

typedef struct 
{
	uint8_t  R1R2FixFlag;
	float 	 f32InsultR1;
	float    f32InsultR2;
}_InsultFixParaDef;

typedef struct 
{
	int16_t hvdcVolMax; //模块最大输出电压
	int16_t hvdcVolMin; //模块最小输出电压
	int16_t batAllowedVolMax; //电池最大允许电压
	int16_t testCmd;   // 开始检测指令: 结束检测 0,开始检测1
	int16_t meterVol;  // 电表电压0.1V
	int16_t m_i16VoutVol; // 0.1V
	int16_t m_i16BattVol; // 0.1V
	int16_t m_i16VolPos;  // 0.1V
	uint32_t OutConnectorFb;
}_InsultInputParaDef;

typedef struct 
{
	int16_t insultVol;         //绝缘电压
	bool  outputContactorDesire;//输出接触器期望
	_InsultStateDef insultState;//绝缘检测过程状态和结果
	_InsultFixParaDef fixPara;//绝缘检测电路硬件参数
	_FaultIndexDef  faultCode;//故障码
	float f32InsultRx;        //DC+对PE电阻
	float f32InsultRy;        //DC-对PE电阻
}_InsultOutputMsgDef;

typedef struct 
{
	float vPos[3];
	float vOut[3];
	float R0;
	float Ra;
	float Rb;
}InsulTestVariableDef;

typedef struct _InsulationR1R2 {
    float f32InsultR1;
    float f32InsultR2;
    bool FileState;
}InsulationR1R2;

template <typename T>
double GetMean(const std::vector<T>& vec) {
    if (vec.size() <= 2) {
        return 0.0;
    }
    // 初始化最大值和最小值
    T maxVal = std::numeric_limits<T>::lowest();
    T minVal = std::numeric_limits<T>::max();
    // 计算均值
    double sum = 0.0;
    // 查找最大值和最小值
    for (const T& value : vec) {
        if (value > maxVal) maxVal = value;
        if (value < minVal) minVal = value;
        sum += value;
    }
    // 移除最大值和最小值
    sum -= (maxVal+minVal);
    return vec.size() > 2 ? sum / (vec.size()-2) : 0.0;
}

class InsulationTest
{
	private:
		_InsultInputParaDef Input_;
		_InsultOutputMsgDef Output_;

		uint32_t InsultState;
		InsulTestVariableDef InsultPara_;
		InsulationR1R2 InsulationR1R2_;
		std::vector<InsulationR1R2> ListInsulR1R2_;

	public:
		uint32_t thisID;

	private:
		int16_t s_i16TestDlyCnt = 0,s_i16TimeCnt1 = 0,s_i16TimeCnt2 = 0,s_Time_base = 0;
		int16_t	 IndexCnt_ = 0;
		int32_t	 VolN_ = 0,VolP_ = 0,VolNp_ = 0,VolPp_ = 0;
		int16_t  TestCnt_ = 0;//检测次数
		int16_t	 TestResult_ = 0;//每次检测结果
		int32_t VolPpBak_=0, VolNpBak_ = 0;
		int8_t TestMode_=0;
		uint8_t ChgOnBak_ = CHARGE_OFF;
		int16_t InsultVol_ = 0; // 绝缘检测电压 取模块电压与电池电压最小值
		_GunConnectStateDef GunStateBak_;
		uint16_t InsultBak = 0xff;
	
	private://待加工
		void ADCSample(void){};//电压采样
		void InsultDatatxt(void);
		//外部静态函数，用于操作IO
		//void Relay_Open(RelayIndexDef relay){};
		//void Relay_Close(RelayIndexDef relay){};
	public:
		void InsultDataReport(void);
		int32_t InsultAltCnt_ = 0;
		float V[7] = {0.0,0.0,0.0},Vp[7] = {0.0,0.0,0.0};
		void InsultAltResultReport(void);
	public:
		void Alg_InsulTest(void);
		void Alg_InsulationTest(void);
		void InsultForTest(void);
		void SetInputPara(_InsultInputParaDef input){Input_ = input;};
		_InsultOutputMsgDef GetOutputPara(void){return Output_;};
		void DatInit(uint32_t gunid,_InsultFixParaDef fixPara);
	private:

};
#endif




