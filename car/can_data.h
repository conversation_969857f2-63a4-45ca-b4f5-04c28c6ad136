#ifndef __CAN_DATA_H
#define __CAN_DATA_H

#include <cstdint>
#include <linux/can.h>
#include <vector>
#include <queue>
#include <unordered_map>
#include "gb2015p_msgtype.h"
#include "can_socket.h"

#define SMRMQUANTITY 10

//#pragma pack(1)

//**********************************can报文传输层**************************************************************

typedef struct
{
    uint32_t SA : 8;  // 源地址
    uint32_t PS : 8;  // 目的地址
    uint32_t PF : 8;  // PUD消息格式类型
    uint32_t DP : 1;  // 数据页
    uint32_t EDP : 1; // 扩展数据页
    uint32_t P : 3;   // 优先级
    uint32_t ERR : 1; // 扩展帧
    uint32_t RTR : 1; // 扩展帧
    uint32_t EFF : 1; // 扩展帧
} CANID;

typedef union
{
    struct can_frame Frame;
    struct
    {
        CANID ExtId;
        uint8_t DLC;
        uint8_t Resv[3];
        uint8_t MsgData[8];
    } PackedMsg;
} CANPDU;

typedef enum : uint8_t
{
    PRIORITY0 = 0,
    PRIORITY1,
    PRIORITY2,
    PRIORITY3,
    PRIORITY4,
    PRIORITY5,
    PRIORITY6,
    PRIORITY7
} PType; // 优先权

typedef enum : uint8_t
{
    LMPF = 0x34,
    SMPF = 0x35,
    SMURMPF = 0x36,
    ACKPF = 0x37,
    CHAR_CONFER_M = 0x38, // 充电机协商报文
    CONFER_M = 0x39   //车辆版本协商报文 
} PFType; // PDU消息格式类型

typedef enum : uint8_t
{
    YesConfirm = 0x00, // 需要确认帧
    NoConfirm = 0x55,  // 不需要确认帧
    Respoonse = 0xAA,   // 应答确认帧
    LongFram = 0xBB     // 长帧
} ConfirmType;

typedef struct
{
    ConfirmType Confirm;       // 当前报文类型
    uint16_t Time;             // 若为需要确认报文则该时间为报文超时时间;
    GB2015P::AddressType SA; // 发送的源地址
    GB2015P::AddressType PS; // 发送的目的地址
    PFType PF;                 // PF
    uint8_t priority;          // 优先级
    uint16_t datalen;          // 数据长度
    uint8_t *data;             // 数据域  判空
} Msg_Parameters;

typedef enum : uint8_t
{
    LM_UNK = 0, // 状态未知
    LM_ACK = 1,
    LM_NACK = 2,
    LM_EndofACK = 3
} ACKType;

typedef enum : uint8_t
{
    Establish_, // 连接建立状态
    Datarecv_,  // 数据接收状态
    Unkown_     // 未知
} LMRecvState;

typedef enum : uint8_t
{
    TRFailed, // 发送失败
    TRok,     // 发送完成
    TRWait,   // 发送等待
    TRTimeout // 等待超时
} TRState;    // 发送状态

typedef enum : uint8_t
{
    NOTSEND = 0x00,    // 未发送
    SENDING = 0x55,    // 发送中
    SENDFAILED = 0x88, // 发送失败
    SENDSUCCESS = 0xAA // 发送成功
} SendFlag;

// typedef struct
// {
//     int16_t LMS_T1 = timeoff;    // 长消息帧间隔定时器 -1为关闭状态
//     int16_t LMS_T2 = timeoff;    // 长消息接受信息超时定时器
//     int16_t LMS_T3 = timeoff;    // 长消息总定时器
//     uint16_t recv_n = 0;         // 长消息待接收起始帧
//     uint16_t recv_k = 0;         // 长消息待接收总帧数
//     bool send_lmflag = false;    // 长消息链接标志位 // 0：未建立长连接 1：长连接建立成功、
//     ACKType ReceiveACK = LM_UNK; // 长消息接受确认标志位
//     uint8_t recv_lmftra = 0;     // 长消息接收方接收的总帧数
//     uint16_t recv_lmlen = 0;     // 长消息接收方接收的总字节数
//     uint16_t err_cnt = 0;        // 长消息接收超时计时器
//     uint8_t lm_tfra = 0;         // 长消息发送总帧数
//     uint16_t lm_datalen = 0;     // 长消息总字节数
//     uint16_t send_cnt = 0;       // 长消息发送帧数计数器
// } _LMSendParameter;
//#pragma pack()

class MsgSend
{
private:
    CANID LMcanid;                 // 长消息canid
    CANID SMRMcanid[SMRMQUANTITY]; // 短消息canid
    CANID SMURMcanid;              // 不需要确认消息canid
    CANID ACKcanid;                // 应答确认canid

    std::vector<CANPDU> LMcanpdu;
    CANPDU SMRMcanpdu[SMRMQUANTITY];
    CANPDU SMURMcanpdu;
    CANPDU ACKcanpdu;

    std::queue<CANPDU> senddata;
    std::queue<CANPDU> lmsenddata;

    int16_t SMS_T1[SMRMQUANTITY] = {timeoff, timeoff, timeoff}; // 短消息接受超时定时器  -1为关闭态
    int16_t SMS_T2[SMRMQUANTITY] = {timeoff, timeoff, timeoff}; // 短消息发送间隔定时器 50ms
    uint8_t send_smpid[SMRMQUANTITY] = {0xFF,0xFF,0xFF};                       // 短消息发送保存PID 0xFF为未知态
    bool send_smflag[SMRMQUANTITY] = {false, false, false};     // 短消息发送标志位

    uint16_t err_cnt = 0;     // 长消息接收超时计时器
    uint8_t lm_tfra = 0;      // 长消息发送总帧数
    uint16_t lm_datalen = 0;  // 长消息总字节数
    uint16_t send_cnt = 0;    // 长消息发送帧数计数器
    int16_t LMS_T1 = timeoff; // 长消息帧间隔定时器 -1为关闭状态
    int16_t LMS_T2 = timeoff; // 长消息接受信息超时定时器
    int16_t LMS_T3 = timeoff; // 长消息总定时器

public:
    CanSocket *pCanSocket_;
    uint16_t recv_n = 0;         // 长消息待接收起始帧
    uint16_t recv_k = 0;         // 长消息待接收总帧数
    bool send_lmflag = false;    // 长消息链接标志位 // 0：未建立长连接 1：长连接建立成功、

    ACKType ReceiveACK = LM_UNK; // 长消息接受确认标志位
    uint8_t recv_lmftra = 0;     // 长消息接收方接收的总帧数
    uint16_t recv_lmlen = 0;     // 长消息接收方接收的总字节数
    uint8_t recv_smpid[SMRMQUANTITY] = {0xFF};   // 短消息发送反馈 0xFF为未接收到反馈态

    uint8_t sm_flag = 0; // 短消息发送标志位
    uint8_t lm_flag = 0; // 长消息发送标志位

public:
    MsgSend(CanSocket *socket);
    void sendMsg(const Msg_Parameters *pApdu);
    void sendLogic(void);//10ms
    void sendframe(void);// 3ms
    SendFlag GetSendStatus(GB2015P::PIDType PGI);
    // Time------------------------定时器
    void LMS_SMS_T(void); // 发送方发送定时器函数 10ms

private:
    void LMWaitACK(void);
    void SM_RMWaitACK(void);
    // LM-------------长消息发送函数
    void LMInit(const Msg_Parameters *pApdu);
    void LMUnpack(const Msg_Parameters *pApdu);
    void LMsending(void);
    void LMTimeoutjudg(void);
    void LMsendNACK(void); // 发送方NACK发送函数
    // SM_RM--------------短消息发送函数
    void SM_RMsend(const Msg_Parameters *pApdu);
    // SM_URM--------------不需要确认报文发送函数
    void SM_URMsend(const Msg_Parameters *pApdu);
    // ACK-----------------应答确认报文发送函数
    void ACKsend(const Msg_Parameters *pApdu);

    std::unordered_map<uint8_t, SendFlag> sendmsg_flag; // 0x00 未发送 0x55 发送中 0x88 发送失败 0xAA 发送成功
};

typedef struct
{
    void *p;
    uint16_t len;
}_CanSpcae;


class MsgRecv
{
private:
    uint16_t err_cnt = 0;                 // 接受超时计数
    uint8_t recv_no = 0;                  // 上次接受的帧号
    uint16_t recv_num = 0;                // 当前有效信息帧帧数
    uint16_t recv_tfra = 0;               // 接收到了多少帧
    uint8_t lm_tfra = 0;                  // 长消息总帧数
    uint16_t lm_datalen = 0;              // 长消息总字节数
    int16_t LMS_T2 = timeoff;             // 帧超时计时器 -1为关闭态
    int16_t LMS_T3 = timeoff;             // 传输超时计时器
    uint8_t recv_lmflag = 0;              // 长消息链接建立标志位 0未链接 1链接
    uint16_t send_k = 0;                  // 发送的待接收帧数 动态变化 正常情况下为总帧数
    uint8_t send_n = 0;                   // 接收方发送待接收起始帧序号
    uint16_t lmsend_datalen = 0;          //
    LMRecvState LMcurrentState = Unkown_; // 长消息接收状态
    Msg_Parameters LMSendCANframe;
    std::vector<uint8_t> LMcandata;
    uint8_t SMcandata[8] = {0};
    uint8_t SMURMcandata[8] = {0};
    CANPDU SMRMcanpdu;
    CANPDU SMURMcanpdu;
    uint8_t LMrecvokflag = 0; // 1为接收完成
    uint8_t SMrecvokflag = 0;
    uint8_t SMRMrecvokflag = 0;
    MsgSend *CANMsgSend;

public:
    std::unordered_map<uint8_t, _CanSpcae> msg_map;
    std::unordered_map<uint8_t, bool> msg_flag;
    CanSocket *pCanSocket_;

public:
    MsgRecv(MsgSend *classMagSend,CanSocket *socket);
    ~MsgRecv();
    uint32_t recvMsg(void); //10ms
    uint8_t GetMsgMap(GB2015P::PIDType PGI, uint8_t *data, uint16_t datalen);
    void RecvMsgClean(void);

private:
    void LMS_T(void);
    void SM_Feedback_ACK(const CANPDU *recvframe);
    void LM_Feedback_ACK(const CANPDU *recvframe);
    void SMURM_Recv(const CANPDU *recvframe);
    void LM_Feedback_send(ACKType snedACK, uint8_t n = 0, uint16_t k = 0);
    void ACK_Receipt(const CANPDU *recvframe, uint16_t cnt);
    void DataParsing(void);
};

#endif