/* Copyright 2021, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class VCIGunHolder and VCIGunCharging Declaration here.
 */
#include <algorithm>
#include <cstring>
#include "InsulationTest.h"
#include "json.hpp"
#include <net/if.h>
#include <sys/ioctl.h>
#include <limits>
#include "evs_gpio.h"
#include "loger.h"

const int16_t INSLT_VOL_MIN = 3500;  //0.1V  

void InsulationTest::DatInit(uint32_t gunid,_InsultFixParaDef fixPara)
{
	thisID = gunid;
	Output_.insultState.bInsulResult = UNKNOW;
	Output_.insultVol = 0;
	Output_.insultState.bTestStage = E_TEST_IDLE;
	//绝缘检测值,单位K欧,X对正，Y对负
	Output_.f32InsultRx = 0.0;
	Output_.f32InsultRy = 0.0;
	Output_.fixPara = fixPara;
	InsultState = 0;
	Output_.outputContactorDesire = 0;//输出接触器期望
	Output_.faultCode = FAIL_NULL;//故障码

	memset(&Input_,0,sizeof(Input_));
	memset(&InsultPara_,0,sizeof(InsultPara_));
	memset(&GunStateBak_,0,sizeof(GunStateBak_));
}
/***************************************************************************
函数名:swap
功能描述:交换指针
输入参数:
作者:
日期:
****************************************************************************/
uint32_t thisCnt = 0;
void InsulationTest::InsultDataReport(void){

    std::string addr = "/tmp/insult";
    addr += '0'+ thisID;
    addr += ".log";
    //FILE *fd = fopen(addr.c_str(),"ab+");
    FILE *fd = fopen(addr.c_str(),"w");
    if(fd == nullptr){
        std::cout<<addr<<"open fail!"<<std::endl;
        return;
    }
	nlohmann::json jout;
	jout["id"] = thisID;
	jout["cnt"] = thisCnt;++thisCnt;
	jout["Rpos"] = (int)Output_.f32InsultRx;
	jout["Rneg"] = (int)Output_.f32InsultRy;
	std::string str = jout.dump();
	Loger(NORMAL,"Insult Data ::%s",str.c_str());
	fwrite(str.c_str(),str.length(),1,fd);
	fclose(fd);
}

/***************************************************************************
函数名:InsultDatatxt
功能描述:R1 R2 内阻计算
输入参数:
作者:
日期:
****************************************************************************/
void InsulationTest::InsultDatatxt(void) {
	if(false == InsulationR1R2_.FileState){
		if((abs(Output_.fixPara.f32InsultR1 - InsulationR1R2_.f32InsultR1) <= InsulationR1R2_.f32InsultR1 * 0.2) && \
			(abs(Output_.fixPara.f32InsultR2 - InsulationR1R2_.f32InsultR2) <= InsulationR1R2_.f32InsultR2 * 0.2) && \
			abs(InsulationR1R2_.f32InsultR1 - InsulationR1R2_.f32InsultR2) <= 50  && \
			InsulationR1R2_.f32InsultR1 > 50 && InsulationR1R2_.f32InsultR2 > 50){
			InsulationR1R2_.FileState = true;
		} else if(Output_.fixPara.f32InsultR1 > 50 && Output_.fixPara.f32InsultR2 > 50){
			InsulationR1R2 data = {Output_.fixPara.f32InsultR1, Output_.fixPara.f32InsultR2, false};
			ListInsulR1R2_.push_back(data);
			uint32_t size = ListInsulR1R2_.size();
			Loger(NORMAL,"gun %d size %d push ListInsulR1R2_ %6.2f--%6.2f",thisID, size, \
			ListInsulR1R2_.at(size - 1).f32InsultR1, ListInsulR1R2_.at(size - 1).f32InsultR2);
			if(size >= 10) {
				std::vector<std::vector<float>> floatVector;
				floatVector.resize(2);
				for (const auto& insulation : ListInsulR1R2_) {
					floatVector.at(0).push_back(insulation.f32InsultR1);
					floatVector.at(1).push_back(insulation.f32InsultR2);
				}
				InsulationR1R2_.f32InsultR1 = GetMean(floatVector.at(0));
				InsulationR1R2_.f32InsultR2 = GetMean(floatVector.at(1));
				if(abs(InsulationR1R2_.f32InsultR1 - InsulationR1R2_.f32InsultR2) >= 50 ) {
					InsulationR1R2_ = {0, 0, false};
					ListInsulR1R2_.clear();
					return ;
				}
				InsulationR1R2_.FileState = true;
				//InsultWriteFile(thisID, InsulationR1R2_);
			}
		}
	}
}

void InsulationTest::InsultForTest(void)
{
	_FaultIndexDef faultCode = FAIL_NULL;
	float f_VoltageNeg = 0,f_VoltagePos = 0,f_VoltageNegPre = 0,f_VoltagePosPre = 0;
	float f32InsultRa,f32InsultRb;//,f32InsultRtemp;
	int16_t tempVout = Input_.m_i16VoutVol; //输出接触器前级电压

	if (Input_.testCmd == CHARGE_OFF && ChgOnBak_ == CHARGE_ON)
	{
		if(Output_.insultState.bTestStage  < E_TESTING_STAGE1)
		{
			s_i16TimeCnt1 = 0;
			s_i16TimeCnt2 = 0;
			s_i16TestDlyCnt = 0; 
			Output_.insultVol = 0;
			Output_.outputContactorDesire = 0;
			Output_.insultState.bTestStage = E_TESTING_STAGE1;
			GpioClr(GPIO_INSULT_KPE);
			GpioClr(GPIO_INSULT_KP);
			GpioClr(GPIO_INSULT_KN);
		}		   
	}
	ChgOnBak_ = Input_.testCmd;
	switch (Output_.insultState.bTestStage)
	{
		case E_TEST_IDLE:
			
			if(Input_.testCmd == CHARGE_ON)
			{
				//这这获取绝缘检测电压。。获取到后不在获取。
				InsultVol_ = tempVout;
				if(InsultVol_ >= Input_.hvdcVolMin)
				{
					//上报绝缘检测电压
					Output_.insultState.bTestStage = E_R1R2FIX;
				}
				// VCI_TIME_LOG("Insult Para:: %d--%d--%d--%d\n",thisID,InsultVol_,Input_.hvdcVolMax,Input_.batAllowedVolMax);
				Loger(NORMAL,"Insult Para:: %d--%d--%d--%d",thisID,InsultVol_,Input_.hvdcVolMax,Input_.batAllowedVolMax);
			}
			// cout<<thisID<<"->testCmd = "<< Input_.testCmd<<endl;
			Loger(NORMAL,"%u->testCmd = %d",thisID,Input_.testCmd);
			
			break;
		case E_R1R2FIX:
			
			if(Output_.fixPara.R1R2FixFlag == 0)
			{
				IndexCnt_++;			
				if(IndexCnt_==15)
				{
					VolPp_ = Input_.m_i16VolPos;
					//如果采样小于1V，则电压为1V,防止除10后变量为0的情况
					DownLimit16(VolPp_,10);
					VolNp_ = tempVout - Input_.m_i16VolPos;
					DownLimit16(VolNp_,10);
		
					GpioSet(GPIO_INSULT_KP);
					GpioClr(GPIO_INSULT_KN);

					V[0] = tempVout;
					Vp[0] = Input_.m_i16VolPos;

				}else if(IndexCnt_==30)
				{
					GpioClr(GPIO_INSULT_KP );
					GpioSet(GPIO_INSULT_KN );
					
					V[1] = tempVout;
					Vp[1] = Input_.m_i16VolPos;

					VolP_ = Input_.m_i16VolPos;
					DownLimit16(VolP_,10);
					VolN_ = tempVout - Input_.m_i16VolPos;
					DownLimit16(VolN_,10);

					f_VoltagePosPre=(float)VolPp_/10.0;
					f_VoltageNegPre=(float)VolNp_/10.0;
					f_VoltagePos=(float)VolP_/10.0;
					f_VoltageNeg=(float)VolN_/10.0;

					Output_.fixPara.f32InsultR1=(f_VoltageNeg*f_VoltagePosPre/(f_VoltageNegPre*f_VoltagePos))*INSULT_SWITCH_RES-INSULT_SWITCH_RES; //没有判定除数是否大于0
					Output_.fixPara.f32InsultR2=f_VoltageNegPre/f_VoltagePosPre*Output_.fixPara.f32InsultR1; //没有判定除数是否大于0
					
					Loger(NORMAL,"insult fix msg%d ::%6.2f--%6.2f--%d--%d--%d--%d",thisID,Output_.fixPara.f32InsultR1,Output_.fixPara.f32InsultR2,VolPp_,VolNp_,VolP_,VolN_);
				}
				else if(IndexCnt_==45)
				{
					GpioClr(GPIO_INSULT_KP );
					GpioClr(GPIO_INSULT_KN );

					V[2] = tempVout;
					Vp[2] = Input_.m_i16VolPos;

					IndexCnt_ =0;
					Output_.fixPara.R1R2FixFlag = 1;
					GpioSet(GPIO_INSULT_KPE );
					Output_.insultState.bTestStage = E_TESTING;
				}
			}
			else
			{
				IndexCnt_ = 0;
				GpioSet(GPIO_INSULT_KPE );
				Output_.insultState.bTestStage = E_TESTING;
			}

	        break;
		case E_TESTING:
			
			//绝缘状态检测
			IndexCnt_++;
			if (IndexCnt_ == 15)
			{
				VolPp_ = Input_.m_i16VolPos;
				VolNp_ = tempVout - Input_.m_i16VolPos;
				VolPpBak_ = VolPp_;
				VolNpBak_ = VolNp_;	
				
				if(VolPp_ > VolNp_)
				{
					GpioSet(GPIO_INSULT_KP );
					TestMode_ = 1;
				}
				else
				{
					GpioSet(GPIO_INSULT_KN );
					TestMode_ =2;
				}

				V[3] = tempVout;
				Vp[3] = Input_.m_i16VolPos;
			}
			else if (IndexCnt_ == 30 )
			{
				VolPp_ = Input_.m_i16VolPos;
				DownLimit16(VolPp_,10);
				VolNp_ = tempVout - Input_.m_i16VolPos;
				DownLimit16(VolNp_,10);
				GpioSet(GPIO_INSULT_KP);
				GpioSet(GPIO_INSULT_KN );

				if(TestMode_ == 1){
					V[4] = tempVout;
					Vp[4] = Input_.m_i16VolPos;
				}else{
					V[5] = tempVout;
					Vp[5] = Input_.m_i16VolPos;
				}
			}
			else if(IndexCnt_== 45)
			{
				VolP_ = Input_.m_i16VolPos;
				DownLimit16(VolP_,10);
				VolN_=tempVout-Input_.m_i16VolPos;
				DownLimit16(VolN_,10);
				V[6] = tempVout;
				Vp[6] = Input_.m_i16VolPos;

				if(TestMode_ == 1){
					GpioClr(GPIO_INSULT_KP);
					GpioSet(GPIO_INSULT_KN);
				}else{
					GpioSet(GPIO_INSULT_KP);
					GpioClr(GPIO_INSULT_KN);
				}
			}
			else if(IndexCnt_== 60)
			{
				GpioClr(GPIO_INSULT_KP);
				GpioClr(GPIO_INSULT_KN);

				if(TestMode_ == 1){
					V[5] = tempVout;
					Vp[5] = Input_.m_i16VolPos;
				}else{
					V[4] = tempVout;
					Vp[4] = Input_.m_i16VolPos;
				}
			}
			else if (IndexCnt_ == 61)
			{
				f_VoltagePosPre = (float)VolPp_/10.0;
				f_VoltageNegPre = (float)VolNp_/10.0;
				f_VoltagePos = (float)VolP_/10.0;
				f_VoltageNeg = (float)VolN_/10.0;

				if(TestMode_ == 1)
				{
					f32InsultRb = f_VoltagePos*f_VoltageNegPre/(f_VoltagePosPre*f_VoltageNeg)*INSULT_SWITCH_RES-INSULT_SWITCH_RES;
					f32InsultRa = INSULT_SWITCH_RES*(f_VoltagePosPre*f32InsultRb)/(INSULT_SWITCH_RES*f_VoltageNegPre - f32InsultRb*f_VoltagePosPre);
					Output_.f32InsultRx = (f32InsultRa*Output_.fixPara.f32InsultR1)/(abs(Output_.fixPara.f32InsultR1 - f32InsultRa));
					Output_.f32InsultRy = (f32InsultRb*Output_.fixPara.f32InsultR2)/(abs(Output_.fixPara.f32InsultR2 - f32InsultRb));
				}
				else if(TestMode_ == 2)
				{
					f32InsultRa = f_VoltageNeg*f_VoltagePosPre/(f_VoltageNegPre*f_VoltagePos)*INSULT_SWITCH_RES-INSULT_SWITCH_RES;
					f32InsultRb = INSULT_SWITCH_RES*(f_VoltageNegPre*f32InsultRa)/(INSULT_SWITCH_RES*f_VoltagePosPre - f32InsultRa*f_VoltageNegPre);
					Output_.f32InsultRx = (f32InsultRa*Output_.fixPara.f32InsultR1)/(abs(Output_.fixPara.f32InsultR1 - f32InsultRa));
					Output_.f32InsultRy = (f32InsultRb*Output_.fixPara.f32InsultR2)/(abs(Output_.fixPara.f32InsultR2 - f32InsultRb));
				}
				Loger(NORMAL,"insult simple msg%d ::%6.2f--%6.2f--%6.2f--%6.2f--%d--%d--%d--%d--%d--%d--%d",\
							thisID,Output_.f32InsultRx,Output_.f32InsultRy,\
							Output_.fixPara.f32InsultR1,Output_.fixPara.f32InsultR2,\
							Output_.insultVol,VolPp_,VolNp_,VolP_,VolN_,TestMode_,VolPpBak_);

				//如果正向单元电压小于43V 或者大于466V，不会再检测，直接报绝缘检测故障
				//绝缘检测阈值100V/欧，Rx,Ry小于100K时，报绝缘故障，假设无穷大R= 1M，则未投切电阻时，Vx < Vout/11时认为绝缘异常
				if((VolPpBak_ < INSULTLVOLTAGEVALUE || VolNpBak_ <INSULTLVOLTAGEVALUE) &&
					(VolPpBak_ < (VolPpBak_+VolNpBak_)/11|| 
					VolNpBak_ < (VolPpBak_+VolNpBak_)/11))
				{
					TestResult_ = UNTRUST;
				}
				//绝缘检测按每伏100欧，绝缘检测阈值(K欧)=充电机最大电压(0.1V的分辨率)*(100欧/V)/1000=(VMax/10)*(100欧/V)/1000 = VMax/100
				else if((Output_.f32InsultRy < Input_.hvdcVolMax/100 && Output_.f32InsultRy > 0)
					||(Output_.f32InsultRx < Input_.hvdcVolMax/100 && Output_.f32InsultRx > 0))
				{
					TestResult_ = FAIL;
				}
				//绝缘检测告警按照每V 500欧;绝缘检测阈值(K欧)=充电机最大电压(0.1V的分辨率)*(500欧/V)/1000=(VMax/10)*(500欧/V)/1000 = VMax/20
				else if((Output_.f32InsultRy < Input_.hvdcVolMax/20 && Output_.f32InsultRy > 0)
					||(Output_.f32InsultRx < Input_.hvdcVolMax/20 && Output_.f32InsultRx > 0))
				{
					TestResult_ = WARN;
				}
				else
				{
					TestResult_ = PASS;
				}

				TestCnt_++;

				if (TestResult_ == PASS || TestResult_ == WARN)
				{
					IndexCnt_ = 61;
				}
				else if (TestResult_ == FAIL || TestResult_ == UNTRUST)
				{
					if (TestCnt_ >= 1)//如果检测连续3次都是失败，则检测失败
					{
						IndexCnt_ = 61;
					}
					else
					{	
						IndexCnt_ = 0;
					}
				}
			}

			if (IndexCnt_ == 61)
			{
				//Relay_Open(INSULT_GND_RELAY);	
				GpioClr(GPIO_INSULT_KPE);
			}
			else if (IndexCnt_ == 62)
			{
				Output_.insultState.bTestStage = E_TESTING_STAGE1;

				if (TestResult_ == PASS)
				{
					Output_.insultState.bInsulResult = PASS;
				}
				else if (TestResult_ == WARN)
				{
					Output_.insultState.bInsulResult = PASS;
					faultCode = FAIL_INSULT_WARN;
				}
				else if (TestResult_ == UNTRUST)
				{
					Output_.insultState.bInsulResult = FAIL;
					faultCode = FAIL_INSULT_SAMP_ERR;
				}
				else if (TestResult_ == FAIL)
				{
					Output_.insultState.bInsulResult = FAIL;
					faultCode = FAIL_INSULT_ABNORMAL;
				}
						
				IndexCnt_ = 0;
				VolPp_ = 0;
				VolN_ = 0;
				VolPp_ = 0;
				VolNp_ = 0;
			}

		   break;
		case E_TESTING_STAGE1:

			Output_.insultState.bTestStage = E_TEST_END;
			break;

		case E_TEST_END:

			if (Input_.testCmd == CHARGE_OFF)
			{
				Output_.insultState.bTestStage = E_TEST_IDLE;
			}

		break;
	}
	Output_.faultCode = faultCode;

	if(memcmp(&InsultBak,(&Output_.insultState),sizeof(InsultBak)))
	{
		memcpy(&InsultBak,(&Output_.insultState),sizeof(InsultBak));
		Loger(NORMAL,"insult state %d ::%d--%d--%d--%d--%d--%d--%d--%d--%d",thisID,\
						(uint16_t)Output_.insultState.bTestStage,(uint16_t)Output_.insultState.bInsulResult,\
						Output_.faultCode,Output_.outputContactorDesire,InsultVol_,Input_.batAllowedVolMax,\
						Input_.m_i16VoutVol,Input_.meterVol,Input_.m_i16BattVol);
	}
}

/***************************************************************************
函数名:swap
功能描述:交换指针
输入参数:
作者:
日期:
****************************************************************************/
void InsulationTest::InsultAltResultReport(void){

    std::string addr = "/tmp/insultAlt";
    addr += '0'+ thisID;
    addr += ".log";
    //FILE *fd = fopen(addr.c_str(),"ab+");
    FILE *fd = fopen(addr.c_str(),"a+");
    if(fd == nullptr){
		Loger(NORMAL,"InsultAltResultReport open fail!");
        return;
    }
	
	nlohmann::json jout;
	jout["id"] = thisID;
	jout["cnt"] = InsultAltCnt_;++InsultAltCnt_;
	jout["Rpos"] = (int)Output_.f32InsultRx;
	jout["Rneg"] = (int)Output_.f32InsultRy;
	jout["R1"] = (int)Output_.fixPara.f32InsultR1;
	jout["R2"] = (int)Output_.fixPara.f32InsultR2;
	jout["V0"] = V[0];
	jout["Vp0"] = Vp[0];
	jout["V1"] = V[1];
	jout["Vp1"] = Vp[1];
	jout["V2"] = V[2];
	jout["Vp2"] = Vp[2];
	jout["V3"] = V[3];
	jout["Vp3"] = Vp[3];
	jout["V4"] = V[4];
	jout["Vp4"] = Vp[4];
	jout["V5"] = V[5];
	jout["Vp5"] = Vp[5];
	jout["V6"] = V[6];
	jout["Vp6"] = Vp[6];
	std::string str = jout.dump();
	Loger(NORMAL,"Insult Alt ::%s",str.c_str());
	fwrite(str.c_str(),str.length(),1,fd);
	fclose(fd);
}
