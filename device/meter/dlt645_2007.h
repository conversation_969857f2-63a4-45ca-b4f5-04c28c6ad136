/*****************************************************************************
*  @file     dlt645_2007.h                                                   *
*  @brief    645-2007协议电表实现                                              *
*                                                                            *
*  <AUTHOR>                                                            *
*  @version  *******(版本号)                                                  *
*----------------------------------------------------------------------------*
*  Remark         : Description                                              *
*----------------------------------------------------------------------------*
*  Change History :                                                          *
*  <Date>     | <Version> | <Author>        | <Description>                  *
*----------------------------------------------------------------------------*
*  2021/11/06 | *******   | guch            | Create file                    *
*****************************************************************************/
#ifndef _DLT645_2007_H
#define _DLT645_2007_H

#include <cstdint>
#include <thread>
#include <mutex>
#include <math.h>
#include <ios>
#include <iostream>
#include "dlt_common.h"
#include "meter_comm.h"
#include "loger.h"

namespace dlt {

typedef enum meterType{
    METER_STAND = 0,
    METER_RELLIN_ENCRYPTION = 1,
    METER_RELLIN_NORMAL = 2,
    METER_645_TYPE_NUM
}_Dlt645TypeDef;
typedef struct {
    _Dlt645TypeDef meter_type;  //电表类型
    std::string currData;
    uint8_t currCode[4];        //电流控制码
    std::string voltData;
    uint8_t voltCode[4];        //电压控制码
    std::string energyData;
    uint8_t energyCode[4];      //电能控制码
}_MeterCmdCodeDef;
    class Dlt645_2007{

        public:
            Dlt645_2007(MeterComm *com,uint32_t meter_type,uint32_t id);
            ~Dlt645_2007();

            /**
             * @brief 打印电表指令8位含义
             * @param ctrl_code 待打印的指令
             */
            void ShowCtrlCode(uint8_t ctrl_code);
            /**
             * @brief 检测电表是否离线
             */
            int32_t CheckMeterStatus();
            /**
             * @brief获取电表SN
             */
            int32_t _GetSN();

            int32_t GetSN(uint8_t sn[DLT645_2007_SN_SIZE]);
            
            int32_t GetSNRefresh(uint8_t sn[DLT645_2007_SN_SIZE]);

            /**
             * @brief 获取电表电压
             * @param value 待获取的电压
             */
            int32_t GetVol(float *value, bool write_log = false);

            /**
             * @brief 获取电表电流
             * @param value 待获取的电流
             */
            int32_t GetCur(float *value, bool write_log = false);

            /**
             * @brief 获取电表正向有功总电能
             * @param value 待获取的电能
             */
            int32_t GetForwardActivePower(float *value, bool write_log = false);

            /**
             * @brief 获取电表反向有功总电能
             * @param value 待获取的电能
             */
            int32_t GetReverseActivePower(float *value);

        private:
            //Dlt645_2007();
          
        private:
            std::mutex _m_mutex; 
            uint32_t _m_meter_type = METER_STAND;
            uint8_t _m_sn[DLT645_2007_SN_SIZE];             // 电表SN号
            uint32_t _m_id;
            MeterComm * _m_meter_comm;
    };
}; // namespace dlt

#endif /* _DLT645_2007_H */
