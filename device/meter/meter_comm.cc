#include "meter_comm.h"

 MeterComm::MeterComm(){
}
int32_t MeterComm::Init(uint32_t id, uint32_t type)
{
    _m_id = id;
    _m_type = type;
    
    Loger(NORMAL, "MeterComm init:: id = %d type = %d ", id, _m_type);
    if(GUNA == _m_id) {
        rs485_ = new rs485("/dev/ttyAS4", " ", 38400, ParityEven, DataBits8, StopOne, FlowNone);
    } else if(GUNB == _m_id) {
        rs485_ = new rs485("/dev/ttyAS2", " ", 9600, ParityEven, DataBits8, StopOne, FlowNone);
    }
    Start(10);
    return 0;
}
MeterComm::~MeterComm(){ 
    Stop();
    delete rs485_;
}
int32_t MeterComm::Start(int32_t try_times){
    int32_t open_ret = -1;
    if(rs485_ == nullptr){
        Loger(NORMAL, "meterComm start rs485_ = nullptr!");
        return -1;
    }
    rs485_->openRs485();
    return open_ret;
}
int32_t MeterComm::Stop(){
    if (rs485_ != nullptr) {
        rs485_->closeRs485();
        delete rs485_;
        return 0;
    }
    return -1;
}

int32_t MeterComm::Read(uint8_t *cmd, int32_t cmd_len, uint8_t *out, 
                int32_t out_len,int32_t reply_len){
    if(rs485_ == nullptr){
        return -1;
    }
    std::lock_guard<std::mutex> lock(_m_mutex);
    if(_m_type == METER_JSY_MK224){//jsy_MK224
        return ModbusRead(cmd, cmd_len, out, out_len,reply_len);
    }else{
        return DltRead(cmd, cmd_len, out, out_len,reply_len);
    }
}

int32_t MeterComm::DltRead(uint8_t *cmd, int32_t cmd_len, uint8_t *out, 
                int32_t out_len,int32_t reply_len){
    char tmp[512] = {0};
    //clear recv buffer
    rs485_->syncRead(tmp, sizeof(tmp));
    
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    //send write cmd
    rs485_->syncWrite((char*)cmd, cmd_len);
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    int32_t total_read = 0, read_len = 0, max_count = 6, count = max_count, flag = 0;
    std::memset(out, 0, out_len);
    do {
        read_len = rs485_->syncRead((char*)out+total_read, out_len-total_read);
        if (count <= 0 || read_len < 0) {
            break;
        } else if (read_len == 0) {
            --count;
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            continue;
        }

        count = max_count;
        total_read += read_len;
        // parse data
        flag = ParseRecvRawData(out, total_read);
        if (flag > 0) {
            break;
        }

        if (flag == 0) { // 继续接收
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            continue;
        } else if (flag < 0) { // 清空缓冲区，继续接收
            total_read = 0;
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            continue;
        }
    } while (1);
    return flag;
}

int32_t MeterComm::ModbusRead(uint8_t *cmd, int32_t cmd_len, uint8_t *out, 
                int32_t out_len,int32_t reply_len){
    char tmp[100] = {0};
    //clear recv buffer
    rs485_->syncRead(tmp, sizeof(tmp)); 
    // send read cmd
    rs485_->syncWrite((char*)cmd, cmd_len);
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    int32_t total_read = 0, read_len = 0, max_count = 5, count = max_count,timeoutCnt = 0;
    std::memset(out, 0, out_len);
    // recv data form slave
    do {
        read_len = rs485_->syncRead((char*)out+total_read, reply_len-total_read);
        Loger(NORMAL, "count = %d, read_len = %d, total = %d, data  = %s", \
            count, read_len, total_read, out);
        if (count <= 0 || read_len < 0) {
            break;
        } else if (read_len == 0) {
            --count;
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
            continue;
        }

        count = max_count;
        total_read += read_len;
        
        if(total_read >= reply_len){
            break;
        }else{
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
            continue;
        }

        if(++timeoutCnt > 10){
            break;
        }
    } while (1);
    return  total_read;
}

int32_t MeterComm::ParseRecvRawData(uint8_t *data, int32_t length)
{
    int32_t parse_len = 0;
    Dlt645Frame_t *dlt645 = {0};
    int32_t data_len = length - FRAME_MIN_LEN - 2, real_len = 0;
    uint8_t real_cs = 0, end = 0;
    if (data_len < 0) {
        return parse_len;   // 数据长度不够，需要继续接收
    }

    for (int32_t i = 0; i < length; i++) {
        if (data[i] != FRAME_HEAD) {    // 找到帧头
            continue;
        }
        dlt645 = (Dlt645Frame_t*)&data[i];
        if (dlt645->head2 != FRAME_HEAD) { // 校验第二帧头
            continue;
        }
        for (int32_t j = i-1; j >= 0; j--) {
            if (data[j] == FRAME_LEAD_ADDR) {
                --data_len;
            }
        }
        if (dlt645->data_len > data_len) { // 数据长度不够
            return parse_len;
        }

        real_len = FRAME_MIN_LEN+dlt645->data_len;
        real_cs = data[i+real_len];
        uint8_t cs = CheckSum(data+i, real_len);
        if (real_cs != cs) {
            Loger(NORMAL, "checksum failed = %X, %X", real_cs, cs);
          continue;  // 校验位错误
        }

        end = data[i+FRAME_MIN_LEN+dlt645->data_len+1];
        if (end != FRAME_END) {
            Loger(NORMAL, "end failed = %02X ", end);
            continue; // 结束符不对
        }
        parse_len = FRAME_MIN_LEN + dlt645->data_len + sizeof(uint8_t) * 2;
        std::memmove(data,data+i,parse_len);
        return parse_len;
    }

    return -1;
}
