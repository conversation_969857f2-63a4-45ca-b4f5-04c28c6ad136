#include "dlt_common.h"

int32_t Translate2Long(uint8_t *read_data, uint16_t len)
{
    //权值
    uint8_t number_weight = 0;
    //当前数组下标索引
    uint8_t current_index = 0;
    //当前解析字节位
    uint8_t current_byte_part = 0;
    //当前整数值
    uint64_t i_value = 0;

    if (len % 2 == 1) {
        i_value += ((read_data[current_index] - 0x33) & 0x0f) * pow(10, number_weight++);
        i_value += (((read_data[current_index] - 0x33)>>4) & 0x0f) * pow(10, number_weight++);
        ++current_index;
        --len;
    }

    while (len--)
    {
        current_byte_part = 'L';
        do
        {
            switch (current_byte_part)
            {
            //当前处理字节低位
            case 'L':
                i_value += ((read_data[current_index] - 0x33) & 0x0f) * pow(10, number_weight);
                number_weight++;
                current_byte_part = 'H';
                break;
            //当前处理字节高位
            case 'H':
                i_value += ((read_data[current_index] - 0x33) >> 4) * pow(10, number_weight);
                number_weight++;
                current_byte_part = 0;
                break;
            }
        } while (current_byte_part != 0);
        current_index++;
    }
    return i_value;
}

int32_t Translate2Int(uint8_t *read_data, uint16_t len)
{
    //权值
    uint8_t number_weight = 0;
    //当前数组下标索引
    uint8_t current_index = 0;
    //当前解析字节位
    uint8_t current_byte_part = 0;
    //当前整数值
    int32_t i_value = 0;

    if (len == 1) {
        i_value += (((read_data[current_index] - 0x33)>>6) & 0x03) * pow(10, number_weight++);
        i_value += (((read_data[current_index] - 0x33)>>4) & 0x03) * pow(10, number_weight++);
        return i_value;
    }

    while (len--)
    {
        current_byte_part = 'L';
        do
        {
            switch (current_byte_part)
            {
            //当前处理字节低位
            case 'L':
                i_value += ((read_data[current_index] - 0x33) & 0x0f) * pow(10, number_weight);
                number_weight++;
                current_byte_part = 'H';
                break;
            //当前处理字节高位
            case 'H':
                i_value += ((read_data[current_index] - 0x33) >> 4) * pow(10, number_weight);
                number_weight++;
                current_byte_part = 0;
                break;
            }
        } while (current_byte_part != 0);
        current_index++;
    }
    return i_value;
}

int32_t GetDecimalValue(uint8_t *read_data, 
    uint16_t read_len, const char *data_format, uint8_t *store_address)
{
    //权值
    int32_t num_weight = 0;
    int64_t ival = Translate2Long(read_data, read_len);

    for (int32_t i = 0; i < (int32_t)std::strlen(data_format); i++)
    {
        if (*(data_format + i) == '.')
        {
            num_weight = std::strlen(data_format) - i - 1;
            if (num_weight < 0)
            {
                return -1;
            }
            break;
        }
    }
    float fval = ival / pow(10, num_weight);
    std::memcpy(store_address, &fval, 4);
    return 0;
}

int32_t CheckSignBit(uint8_t b)
{
    return b&0x80;
}

bool GetCtrlType(uint8_t ctrl_code)
{
    return ((ctrl_code >> 7) & 0x01);
}

bool GetCtrlMeterRespFlag(uint8_t ctrl_code)
{
    return !((ctrl_code >> 6) & 0x01);
}

bool GetCtrlAddtionDataFlag(uint8_t ctrl_code)
{
    return ((ctrl_code >> 5) & 0x01);
}

bool DataCompare(uint8_t * d1,uint8_t *d2,uint32_t len){
    if(memcpy(d1,d2,len)== 0){
        return 1;
    }
    return 0;
}