/*****************************************************************************
*  @file     meter_comm.h                                                     *
*  @brief    电表串口通信                                      *
*                                                                            *
*  <AUTHOR>                                                            *
*  @version  *******(版本号)                                                  *
*----------------------------------------------------------------------------*
*  Remark         : Description                                              *
*----------------------------------------------------------------------------*
*  Change History :                                                          *
*  <Date>     | <Version> | <Author>        | <Description>                  *
*----------------------------------------------------------------------------*
*  2023/08/14 | *******   | Daniel            | Create file                    *
*****************************************************************************/
#ifndef _METER_COMM_H
#define _METER_COMM_H

#include <thread>
#include <cstring>
#include <mutex>
#include <memory>
#include "dlt_common.h"
#include "uart.h"
#include "meter_comm.h"
#include "loger.h"
#include "rs485.h"

typedef enum meterType{
    METER_645_STAND = 0,
    METER_645_RELLIN_ENCRYPTION = 1,
    METER_JSY_MK224 = 2,
    METER_698 = 3,
    METER_645_RELLIN_NORMAL = 4,
    METER_TYPE_NUM
}_MeterTypeDef;

class MeterComm{
    public:
        MeterComm();
        ~MeterComm();
        int32_t Init(uint32_t id, uint32_t type);
    private:
        std::mutex _m_mutex; 
        std::string _m_dev;
        uint32_t _m_baud;
        uint32_t _m_id;   // 电表 id
        uint32_t _m_type;

    // Uart *Uart_ = nullptr;
    rs485 *rs485_ = nullptr;
    private:
        int32_t ParseRecvRawData(uint8_t *data, int32_t length);
        int32_t DltRead(uint8_t *cmd, int32_t cmd_len, uint8_t *out, 
                int32_t out_len,int32_t reply_len);
        int32_t ModbusRead(uint8_t *cmd, int32_t cmd_len, uint8_t *out, 
                int32_t out_len,int32_t reply_len);
    public:
        int32_t Start(int32_t try_times = -1);
        int32_t Stop();
        int32_t Read(uint8_t *cmd, int32_t cmd_len, uint8_t *out, 
                int32_t out_len,int32_t reply_len = 0);
};
 
#endif /* _METER_COMM_H */
