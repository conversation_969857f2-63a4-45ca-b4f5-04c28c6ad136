/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * 电表的电压和电流管理
 */
#ifndef _EVS_METER_H
#define _EVS_METER_H
#include "uart.h"
#include "loger.h"
#include "meter_comm.h"
#include "dlt645_2007.h"
#include "rs485.h"
class EVSMeter {
public:
    EVSMeter();
    ~EVSMeter();
private:
    int32_t _m_meter_vol = 0;
    int32_t _m_meter_curr = 0;
    int32_t _m_power = 0;
    dlt::Dlt645_2007 *dlt_;
    MeterComm MeterComm_;
public:
    int32_t MeterInit(void);
    int32_t MeterProcess(void);
    int32_t GetVol(void){return _m_meter_vol;};
    int32_t GetCurr(void){return _m_meter_curr;};
    int32_t GetPower(void){return _m_power;};

};


#endif  //_EVS_METER_H