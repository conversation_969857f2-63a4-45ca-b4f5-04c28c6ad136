/*****************************************************************************
*  @file     dlt_comm.h                                                     *
*  @brief    电表串口通信                                      *
*                                                                            *
*  <AUTHOR>                                                            *
*  @version  *******(版本号)                                                  *
*----------------------------------------------------------------------------*
*  Remark         : Description                                              *
*----------------------------------------------------------------------------*
*  Change History :                                                          *
*  <Date>     | <Version> | <Author>        | <Description>                  *
*----------------------------------------------------------------------------*
*  2023/08/14 | *******   | Daniel            | Create file                    *
*****************************************************************************/
#ifndef _DLT_COMMON_H
#define _DLT_COMMON_H

#include <memory>
#include <thread>
#include <cstring>
#include <cmath>

#define FRAME_HEAD          0x68
#define FRAME_END           0x16
#define FRAME_MIN_LEN       10
#define FRAME_DATA_SUB      0x33
#define FRAME_READ_DELAY    1    // ms
#define FRAME_LEAD_ADDR   0xFE

#define DLT645_2007_SN_SIZE  6

typedef struct _Dlt645Frame_t {
    uint8_t head1;                          // 0x68 头
    uint8_t sn[DLT645_2007_SN_SIZE];        // 电表SN
    uint8_t head2;                          // 0x68 头
    uint8_t ctrl;                           // 控制指令
    uint8_t data_len;                       // 数据长度
    uint8_t data[0];                        // 数据
} Dlt645Frame_t;

/**
 * @brief:   将接收到的dlt645数据包中的数据转化为整数
 * @read_data: 数据首地址
 * @len:       数据长度
 * @return:  转化后的整数
 */
int32_t Translate2Long(uint8_t *read_data, uint16_t len);
int32_t Translate2Int(uint8_t *read_data, uint16_t len);

/**
 * @brief:   根据数据格式将645协议读取的数据转换为真实数据并存储
 *          真实数据为浮点数据，需要注意的是无论读取数据长度是多少，存储数据长度都应是4字节
 * @read_data:     645协议读取的数据
 * @read_len:      读取数据的长度
 * @data_format:   转换的数据格式，如 XX.XX,XX.XXX
 * @return:  转换成功返回0，失败返回-1
 */
int32_t GetDecimalValue(uint8_t *read_data, uint16_t read_len, 
    const char *data_format, uint8_t *store_address);

/**
 * @brief 判断符号位
 */
int32_t CheckSignBit(uint8_t b);

/**
 * @brief 获取电表指令类型 D7
 * @param ctrl_code 待打印的指令
 */
bool GetCtrlType(uint8_t ctrl_code);

/**
 * @brief 获取电表指令响应成功或失败标志 D6
 * @param ctrl_code 待打印的指令
 */
bool GetCtrlMeterRespFlag(uint8_t ctrl_code);

/**
 * @brief 获取电表指令有无附加数据帧 D6
 * @param ctrl_code 待打印的指令
 */
bool GetCtrlAddtionDataFlag(uint8_t ctrl_code);

bool DataCompare(uint8_t * d1,uint8_t *d2,uint32_t len);

 
#endif /* _DLT_COMMON_H */
