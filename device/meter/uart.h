/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * 
 */
#ifndef UART_H
#define UART_H

#include <stdio.h>   /* 标准输入/输出定义 */
#include <string.h>  /* 字符串函数定义 */
#include <unistd.h>  /* UNIX标准函数定义 */
#include <fcntl.h>   /* 文件控制定义 */
#include <termios.h> /* POSIX终端控制定义 */
#include "loger.h"
#include "evs_common.h"

typedef enum{
    UART_A = 0,
    UART_B = 1,
    UART_NUM
}UartIndex;

typedef struct  {
    uint8_t name;        // A~F
    const char dev485[32];      // /dev/ttyS7
    uint32_t baud;              // 9600
    const char gpio[16];        // gpio PA12
    int32_t status;             // meter status: -1 is uninit, 0 is ok, >0 err
} SerialMsgDef;
 
class Uart {

public :
  ~Uart();
  Uart(UartIndex uart, int nSpeed = 9600, int nBits = 8, char nEvent = 'E', int nStop  = 1);

  int uartWrite(char *data, int num);
  int uartRead(char *data, int num);
  void uartClose();
private:

  int Fd_;
  int uartInit(int nSpeed, int nBits, char nEvent, int nStop);
  int uartOpen(int port, int flag);

};
 
#endif // UART_H