#include"rs485.h"
#include <fcntl.h>
#include <sys/ioctl.h>
#include <termios.h>
#include <unistd.h>
#include <string.h>
#include <thread>

#define REST_BASE      10

rs485::rs485()
:SysCmd(""),m_readBufferSize(0),m_fd(0),m_cfg_tios_flg(false)
{
}
rs485::rs485(const char *dev,const char *gpio,uint32_t baud,Parity parity,
                        DataBits databit,StopBits stopbit,FlowControl flowControl)
:m_portName(dev),m_rs485_gpio_Dev(gpio),m_baudRate(baud),m_parity(parity),
m_dataBits(databit),m_stopbits(stopbit),m_flowControl(flowControl)
{
}
rs485::~rs485()
{
}
/************************************************************************************
函数名称： openRs485
功能描述： 打开Rs485设备
************************************************************************************/
uint32_t rs485::openRs485(void)
{
    m_fd = open(m_portName.c_str(), O_RDWR|O_NOCTTY|O_NDELAY);
    if(m_fd == -1){
        Loger(NORMAL,"openRs485 error:%s-%d",m_portName.c_str(),m_fd);
        return 0;
    }
    //配置串口信息
    if(configRs485(m_fd, m_baudRate, m_parity, m_dataBits, m_stopbits, m_flowControl)){
        m_cfg_tios_flg = true;
    }
    else{
        m_cfg_tios_flg = false;
        return 0;
    }
    return 1;
}
/************************************************************************************
函数名称： isOpenRs485
功能描述： 是否打开Rs485设备
************************************************************************************/
bool rs485::isOpenRs485(void)
{
    bool ret = (m_fd > 0)?true:false;
    return ret;
}
/************************************************************************************
函数名称： closeRs485
功能描述： 关闭Rs485设备
************************************************************************************/
void rs485::closeRs485(void)
{
    struct termios cfg_tios;
    if(m_fd != -1){
        memset(&cfg_tios, 0 ,sizeof(cfg_tios));
        //获取终端属性
        if (tcgetattr(m_fd, &cfg_tios) < 0){
            Loger(NORMAL,"closeRs485 tcgetattr error");
            return;
        }
        cfg_tios.c_cc[VTIME] = 1;
        m_cfg_tios_flg = false;
        //清空输入缓冲区
        tcflush(m_fd, TCIOFLUSH);
        //激活配置
        if (tcsetattr(m_fd, TCSANOW, &cfg_tios) < 0){
            Loger(NORMAL,"tcsetattr failed");
            return;
        }
        close(m_fd);
        m_fd = -1;
    }
    Loger(NORMAL,"closeRs485 Succed!");
}
/************************************************************************************
函数名称： configRs485
功能描述： 配置Rs485串口设备
传入参数：fd                    文件描述符                                 
                      baudRate     波特率
                      parity             检验位
                      dataBits        数据位
                      stopbits         停止位
                      flowControl 流控位
************************************************************************************/
int32_t rs485::configRs485(int fd,
                                int baudRate,
                                Parity parity,
                                DataBits dataBits,
                                StopBits stopbits,
                                FlowControl flowControl)
{
    memset(&cfg_tios, 0 ,sizeof(cfg_tios));
    //获取终端属性
    if (tcgetattr(fd, &cfg_tios) < 0){
        Loger(NORMAL,"configRs485 tcgetattr error");
        return -1;
    }

    //设置输入输出波特率
    int baud = 0;
    baud = rateToConstant(baudRate);

    if (0 != baud){
        cfsetispeed(&cfg_tios, baud);
        cfsetospeed(&cfg_tios, baud);
    }
    else{
        // TODO: custom baudrate
        Loger(NORMAL,"Unkown baudrate!\n");
        return -1;
    }

//设置校验位
    switch (parity)
    {
        /*无奇偶校验位*/
        case ParityNone:
        case ParityNoneN:
            cfg_tios.c_cflag &= ~PARENB; // PARENB：产生奇偶位，执行奇偶校验
            cfg_tios.c_cflag &= ~INPCK;  // INPCK：使奇偶校验起作用
            break;
        /*设置奇校验*/
        case ParityOdd:
            cfg_tios.c_cflag |= PARENB; // PARENB：产生奇偶位，执行奇偶校验
            cfg_tios.c_cflag |= PARODD; // PARODD：若设置则为奇校验,否则为偶校验
            cfg_tios.c_cflag |= INPCK;  // INPCK：使奇偶校验起作用
            cfg_tios.c_cflag |= ISTRIP; // ISTRIP：若设置则有效输入数字被剥离7个字节，否则保留全部8位
            break;
        /*设置偶校验*/
        case ParityEven:
            cfg_tios.c_cflag |= PARENB;  // PARENB：产生奇偶位，执行奇偶校验
            cfg_tios.c_cflag &= ~PARODD; // PARODD：若设置则为奇校验,否则为偶校验
            cfg_tios.c_cflag |= INPCK;   // INPCK：使奇偶校验起作用
            cfg_tios.c_cflag |= ISTRIP; // ISTRIP：若设置则有效输入数字被剥离7个字节，否则保留全部8位
            break;
            /*设为空格,即停止位为2位*/
        case ParitySpace:
            cfg_tios.c_cflag &= ~PARENB; // PARENB：产生奇偶位，执行奇偶校验
            cfg_tios.c_cflag &= ~CSTOPB; // CSTOPB：使用两位停止位
            break;
        default:
           Loger(NORMAL,"Unkown parity!\n");
            return -1;
    }

    //设置数据位
    switch (dataBits)
    {
        case DataBits5:
            cfg_tios.c_cflag &= ~CSIZE; //屏蔽其它标志位
            cfg_tios.c_cflag |= CS5;
            break;
        case DataBits6:
            cfg_tios.c_cflag &= ~CSIZE; //屏蔽其它标志位
            cfg_tios.c_cflag |= CS6;
            break;
        case DataBits7:
            cfg_tios.c_cflag &= ~CSIZE; //屏蔽其它标志位
            cfg_tios.c_cflag |= CS7;
            break;
        case DataBits8:
            cfg_tios.c_cflag &= ~CSIZE; //屏蔽其它标志位
            cfg_tios.c_cflag |= CS8;
            break;
        default:
            Loger(NORMAL,"Unkown bits!\n");
            return -1;
    }

    //停止位
    switch (stopbits)
    {
        case StopOne:
            cfg_tios.c_cflag &= ~CSTOPB; // CSTOPB：使用两位停止位
            break;
        case StopOneAndHalf:
            Loger(NORMAL, "POSIX does not support 1.5 stop bits!\n");
            return -1;
        case StopTwo:
            cfg_tios.c_cflag |= CSTOPB; // CSTOPB：使用两位停止位
            break;
        default:
            Loger(NORMAL,"Unkown stop!\n");
            return -1;
    }

    //控制模式
    cfg_tios.c_cflag |= CLOCAL; //保证程序不占用串口
    cfg_tios.c_cflag |= CREAD;  //保证程序可以从串口中读取数据

    //流控制
    switch (flowControl)
    {
        case FlowNone: ///< No flow control 无流控制
            cfg_tios.c_cflag &= ~CRTSCTS;
            break;
        case FlowHardware: ///< Hardware(RTS / CTS) flow control 硬件流控制
            cfg_tios.c_cflag |= CRTSCTS;
            break;
        case FlowSoftware: ///< Software(XON / XOFF) flow control 软件流控制
            cfg_tios.c_cflag |= IXON | IXOFF | IXANY;
            break;
        default:
            Loger(NORMAL,"Unkown c_flow!\n");
            return -1;
    }
    //设置输出模式为原始输出
    cfg_tios.c_oflag &= ~OPOST; // OPOST：若设置则按定义的输出处理，否则所有c_oflag失效

    //设置本地模式为原始模式
    cfg_tios.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);
    /*
     *ICANON：允许规范模式进行输入处理
     *ECHO：允许输入字符的本地回显
     *ECHOE：在接收EPASE时执行Backspace,Space,Backspace组合
     *ISIG：允许信号
     */
    cfg_tios.c_iflag &= ~(BRKINT | ICRNL | INPCK | ISTRIP | IXON);
    /*
     *BRKINT：如果设置了IGNBRK，BREAK键输入将被忽略
     *ICRNL：将输入的回车转化成换行（如果IGNCR未设置的情况下）(0x0d => 0x0a)
     *INPCK：允许输入奇偶校验
     *ISTRIP：去除字符的第8个比特
     *IXON：允许输出时对XON/XOFF流进行控制 (0x11 0x13)
     */
    //设置等待时间和最小接受字符
    cfg_tios.c_cc[VTIME] = 0; //可以在close中设置
    cfg_tios.c_cc[VMIN] = 1;  //最少读取一个字符
    //清空输入缓冲区
    tcflush(fd, TCIOFLUSH);
    //激活配置
    if (tcsetattr(fd, TCSANOW, &cfg_tios) < 0)
    {
        Loger(NORMAL,"tcsetattr failed");
        return -1;
    }
    return 1;
}
/************************************************************************************
函数名称： syncRead
功能描述： 读取Rs485数据
传入参数：数据缓存指针data, 数据长度len                          
************************************************************************************/
ssize_t rs485::syncRead(char *data, int32_t len)
{
    ssize_t ret = -1;
    int readbytes = 0;
    std::this_thread::sleep_for(std::chrono::milliseconds(REST_BASE*10));
    if(m_cfg_tios_flg && isOpenRs485()){
        // read前获取可读的字节数,不区分阻塞和非阻塞
        ioctl(m_fd, FIONREAD, &readbytes);
        if (readbytes > len) {
            readbytes = len;
        }

        ret = read(m_fd,data,readbytes);
        //Loger(NORMAL,"readpackag:0x%x 0x%x 0x%x 0x%x 0x%x 0x%x 0x%x",data[0],data[1],data[2],data[3],data[4],data[5],data[6]);
        std::this_thread::sleep_for(std::chrono::milliseconds(REST_BASE));
        tcflush(m_fd,TCIFLUSH);
    }
    return ret;
}
/************************************************************************************
函数名称： syncWrite
功能描述： 发送Rs485数据
传入参数： 数据缓存指针data, 数据长度len                                      
************************************************************************************/
ssize_t rs485::syncWrite(const char *data, int32_t len)
{
    ssize_t ret = -1;
    char cleanbuf[128]={0};
    if(m_cfg_tios_flg && isOpenRs485()){
        read(m_fd,cleanbuf,sizeof(cleanbuf));
       //使能串口发送数据 
        SysCmd.append("echo 1 >");
        SysCmd.append(m_rs485_gpio_Dev);
       //system(SysCmd.c_str());
       std::this_thread::sleep_for(std::chrono::milliseconds(REST_BASE*6));
        //Loger(NORMAL,"Rs485 SysCmd:%s",SysCmd.c_str());
        SysCmd.clear();
       ret = write(m_fd,data,len);
       std::this_thread::sleep_for(std::chrono::milliseconds(REST_BASE));
       tcdrain(m_fd);
       tcflush(m_fd,TCOFLUSH);
        SysCmd.append("echo 0 >");
        SysCmd.append(m_rs485_gpio_Dev);  
        //system(SysCmd.c_str());
        std::this_thread::sleep_for(std::chrono::milliseconds(REST_BASE));
        //Loger(NORMAL,"Rs485 SysCmd:%s",SysCmd.c_str());
        SysCmd.clear();
    }
    return ret;
}
/************************************************************************************
函数名称： rateToConstant
功能描述： 设置Rs485串口设备波特率
传入参数： 波特率大小                                
************************************************************************************/
int rs485::rateToConstant(int baudrate)
{
    // https://jim.sh/ftx/files/linux-custom-baudrate.c

#define B(x) \
    case x:  \
        return B##x

    switch (baudrate)
    {
#ifdef B50
        B(50);
#endif
#ifdef B75
        B(75);
#endif
#ifdef B110
        B(110);
#endif
#ifdef B134
        B(134);
#endif
#ifdef B150
        B(150);
#endif
#ifdef B200
        B(200);
#endif
#ifdef B300
        B(300);
#endif
#ifdef B600
        B(600);
#endif
#ifdef B1200
        B(1200);
#endif
#ifdef B1800
        B(1800);
#endif
#ifdef B2400
        B(2400);
#endif
#ifdef B4800
        B(4800);
#endif
#ifdef B9600
        B(9600);
#endif
#ifdef B19200
        B(19200);
#endif
#ifdef B38400
        B(38400);
#endif
#ifdef B57600
        B(57600);
#endif
#ifdef B115200
        B(115200);
#endif
#ifdef B230400
        B(230400);
#endif
#ifdef B460800
        B(460800);
#endif
#ifdef B500000
        B(500000);
#endif
#ifdef B576000
        B(576000);
#endif
#ifdef B921600
        B(921600);
#endif
#ifdef B1000000
        B(1000000);
#endif
#ifdef B1152000
        B(1152000);
#endif
#ifdef B1500000
        B(1500000);
#endif
#ifdef B2000000
        B(2000000);
#endif
#ifdef B2500000
        B(2500000);
#endif
#ifdef B3000000
        B(3000000);
#endif
#ifdef B3500000
        B(3500000);
#endif
#ifdef B4000000
        B(4000000);
#endif

        default:
            return 0;
    }

#undef B
}