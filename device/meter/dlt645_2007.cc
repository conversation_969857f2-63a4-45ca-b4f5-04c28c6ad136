#include "dlt645_2007.h"

#ifndef VLOG_DEBUG_645METER
#define VLOG_DEBUG_645METER 99
#endif 

#ifdef SAMA_A7
#define DEV_GPIO            "PB1"
#endif 


const dlt::_MeterCmdCodeDef c_meterCmd[dlt::METER_645_TYPE_NUM] ={
    {dlt::METER_STAND               ,"XXXX.XX",{0x33,0x34,0x44,0x35}, "XXXX.XX",{0x33,0x34,0x43,0x35},"XXXXXX.XXXX",{0x33,0x33,0x93,0x33}},
    {dlt::METER_RELLIN_ENCRYPTION   ,"XXX.XXX",{0x33,0x34,0x35,0x35}, "XXXXX.X",{0x33,0x34,0x37,0x17},"XXXXXX.XXXX",{0x33,0x33,0x93,0x33}},
    {dlt::METER_RELLIN_NORMAL       ,"XXX.XXX",{0x33,0x34,0x35,0x35}, "XXXXX.X",{0x33,0x34,0x37,0x17},"XXXXXX.XXXX",{0x33,0x33,0x93,0x33}}
};


dlt::Dlt645_2007::Dlt645_2007(MeterComm *com,uint32_t meter_type,uint32_t id) 
    : _m_mutex(), _m_meter_type(METER_STAND), _m_sn{0},_m_id(id),_m_meter_comm(nullptr)
{ 
    _m_meter_comm = com;

    if(meter_type < METER_TYPE_NUM ){
       dlt::_Dlt645TypeDef dlt_type = dlt::METER_STAND;
        switch(meter_type){
            case METER_645_STAND:
                dlt_type = dlt::METER_STAND;
            break;
            case METER_645_RELLIN_ENCRYPTION:
                dlt_type = dlt::METER_RELLIN_ENCRYPTION;
            break;
            case METER_645_RELLIN_NORMAL:
                dlt_type = dlt::METER_RELLIN_NORMAL;
            break;
            default :
            break;
        }
        _m_meter_type =  dlt_type;
    }
}

dlt::Dlt645_2007::~Dlt645_2007() 
{}


int32_t dlt::Dlt645_2007::GetSN(uint8_t sn[DLT645_2007_SN_SIZE])
{
    if(_m_meter_comm == nullptr){
        return -1;
    }
    std::memcpy(sn, _m_sn, DLT645_2007_SN_SIZE);
    return 0;
}

/**
 * 
 * @brief 解析控制码
 * @note D7  D6  D5  D4  D3  D2  D1  D0     (8位)
 * D7 : 传送方向[ 0为主站发出的命令帧 | 1为从站发出的应答帧 ]
 * D6 : 从站应答标志[ 0为从站正确应答 | 1为从站异常应答 ]
 * D5 : 后续帧标志[ 0为无后续帧 | 1为有后续帧 ]
 * D4 ~ D0: 功能码
 *      00000 保留
 *      01000 广播校时
 *      10001 读数据
 *      10010 读后续数据
 *      10011 读通讯地址
 */
void dlt::Dlt645_2007::ShowCtrlCode(uint8_t ctrl_code)
{
    uint8_t mark = 0x01;
    uint8_t tmp[8] = {0};
    
    for (uint8_t i = 0; i < 8; ++i) {
        tmp[sizeof(tmp)-1-i] = (ctrl_code >> i) & mark;
    }
    Loger(NORMAL, "ctrl_code: 0x%X ->%d, %d, %d, %d, %d, %d, %d, %d ",\
        (uint32_t)ctrl_code, (uint32_t)tmp[0], (uint32_t)tmp[1], (uint32_t)tmp[2],\
        (uint32_t)tmp[3], (uint32_t)tmp[4], (uint32_t)tmp[5], (uint32_t)tmp[6], (uint32_t)tmp[7]);

    // D7
    if (tmp[0] == 0) {
        Loger(NORMAL, "D7:%.2X , 主站发出的命令帧", (uint32_t)tmp[0]);
    } else {
        Loger(NORMAL, "D7:%.2X, 从站发出的应答帧", (uint32_t)tmp[0]); 
    }

    // D6
    if (tmp[1] == 0) {
        Loger(NORMAL, "D6:%.2X, 从站正确应答", (uint32_t)tmp[1]);
    } else {
        Loger(NORMAL, "D6:%.2X, 从站异常应答", (uint32_t)tmp[1]);
    }

    // D5
    if (tmp[2] == 0) {
        Loger(NORMAL, "D5:%.2X, 无后续帧", (uint32_t)tmp[2]); 
    } else {
        Loger(NORMAL, "D5:%.2X, 有后续帧", (uint32_t)tmp[2]);
    }

    uint8_t mark2[5][5] = {
        {0x00, 0x00, 0x00, 0x00, 0x00},
        {0x00, 0x01, 0x00, 0x00, 0x00},
        {0x01, 0x00, 0x00, 0x00, 0x01},
        {0x01, 0x00, 0x00, 0x01, 0x00},
        {0x01, 0x00, 0x00, 0x01, 0x01},
    };
    // D4 ~ D0
    if (std::memcmp(&tmp[3], mark2[0], sizeof(mark2[0])) == 0) {
        Loger(NORMAL, "D4 ~ D0: 0x%.2X%.2X%.2X%.2X%.2X, 保留", \
            (uint32_t)tmp[3], (uint32_t)tmp[4], \
            (uint32_t)tmp[5], (uint32_t)tmp[6], (uint32_t)tmp[7]);
    } else if (std::memcmp(&tmp[3], mark2[1], sizeof(mark2[1])) == 0) {
        Loger(NORMAL, "D4 ~ D0: 0x%.2X%.2X%.2X%.2X%.2X, 广播校时", \
            (uint32_t)tmp[3], (uint32_t)tmp[4], \
            (uint32_t)tmp[5], (uint32_t)tmp[6], (uint32_t)tmp[7]);
    } else if (std::memcmp(&tmp[3], mark2[2], sizeof(mark2[2])) == 0) {
        Loger(NORMAL, "D4 ~ D0: 0x%.2X%.2X%.2X%.2X%.2X, 读数据", \
            (uint32_t)tmp[3], (uint32_t)tmp[4], \
            (uint32_t)tmp[5], (uint32_t)tmp[6], (uint32_t)tmp[7]);
    } else if (std::memcmp(&tmp[3], mark2[3], sizeof(mark2[3])) == 0) {
        Loger(NORMAL, "D4 ~ D0: 0x%.2X%.2X%.2X%.2X%.2X, 读后续数据", \
            (uint32_t)tmp[3], (uint32_t)tmp[4], \
            (uint32_t)tmp[5], (uint32_t)tmp[6], (uint32_t)tmp[7]);
    } else if (std::memcmp(&tmp[3], mark2[4], sizeof(mark2[4])) == 0) {
        Loger(NORMAL, "D4 ~ D0: 0x%.2X%.2X%.2X%.2X%.2X, 读通讯地址", \
            (uint32_t)tmp[3], (uint32_t)tmp[4], \
            (uint32_t)tmp[5], (uint32_t)tmp[6], (uint32_t)tmp[7]);
    } else {
        Loger(NORMAL, "D4 ~ D0: 0x%.2X%.2X%.2X%.2X%.2X, unknown", \
            (uint32_t)tmp[3], (uint32_t)tmp[4], \
            (uint32_t)tmp[5], (uint32_t)tmp[6], (uint32_t)tmp[7]);
    }
}
int32_t dlt::Dlt645_2007::CheckMeterStatus()
{
    std::lock_guard<std::mutex> guard(_m_mutex);
    uint8_t addr[] = {
        0xFE, 0xFE, 0xFE, 0xFE,             // 开头
        0x68,                               // 帧起始符
        0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, // 地址
        0x68,                               // 帧起始符
        0x13, 0x00,                         // 控制码、数据长度
        0xDF,                               // 校验码
        0x16                                // 结束符
    };

    // ShowCtrlCode(addr[12]);
    // uint8_t *parse_idx = nullptr;
    uint8_t buf[128];
    int32_t parse_len = _m_meter_comm->Read(addr, sizeof(addr), buf, sizeof(buf));
    if (parse_len <= 0) {
        Loger(NORMAL, "meter[%d] check status fail parse_len = %d", _m_id, parse_len);
        return -1;
    }
    return 0;
}

int32_t dlt::Dlt645_2007::GetSNRefresh(uint8_t sn[DLT645_2007_SN_SIZE])
{
    int32_t ret = _GetSN();
    if(ret != -1){
        GetSN(sn);
    }
    return ret;
}

int32_t dlt::Dlt645_2007::_GetSN()
{
    std::lock_guard<std::mutex> guard(_m_mutex);
    uint8_t addr[] = {
        0xFE, 0xFE, 0xFE, 0xFE,             // 开头
        0x68,                               // 帧起始符
        0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, // 地址
        0x68,                               // 帧起始符
        0x13, 0x00,                         // 控制码、数据长度
        0xDF,                               // 校验码
        0x16                                // 结束符
    };
    // ShowCtrlCode(addr[12]);
    uint8_t *parse_idx = nullptr;
    int32_t parse_start = 0;
    uint8_t buf[128];
    int32_t parse_len = _m_meter_comm->Read(addr, sizeof(addr), buf, sizeof(buf));
    if (parse_len <= 0) {
        Loger(NORMAL, "meter[%d] getSN parse_len = %d", _m_id,parse_len);
        return -1;
    }
    parse_idx = buf + parse_start;
    // Loger(NORMAL, "meter[%d] getSN parse_len = %d, data = %s",_m_id, parse_len,
    //     BytesToHexString((const char*)parse_idx, parse_len).c_str());

    Dlt645Frame_t *dlt645 = (Dlt645Frame_t *)parse_idx;
    std::memcpy(_m_sn, dlt645->sn, sizeof(_m_sn));
    return 0;
}

int32_t dlt::Dlt645_2007::GetVol(float *value, bool write_log)
{
    if(_m_meter_type >= dlt::METER_645_TYPE_NUM){
        
    Loger(NORMAL, "_m_meter_type error = = %d",_m_meter_type);
        return -1;
    }
    std::lock_guard<std::mutex> guard(_m_mutex);
    *value = 0.0;
    uint8_t vol[] = {
        0xFE, 0xFE, 0xFE, 0xFE,             // 开头
        0x68,                               // 帧起始符
        0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, // 地址
        0x68,                               // 帧起始符
        0x11, 0x04,                         // 控制码、数据长度
        0x00, 0x00, 0x00, 0x00,             // A-B-C相电压
        0xDF,                               // 校验码
        0x16                                // 结束符
    };
    // ctrl code 
    std::memcpy(vol+14, c_meterCmd[_m_meter_type].voltCode, sizeof(uint8_t)*4);
    // meter SN
    std::memcpy(vol+5, _m_sn, sizeof(_m_sn));
    // crc
    uint8_t cs = CheckSum(vol+4, sizeof(vol)-4-2);
    vol[sizeof(vol)-2] = cs;

    uint8_t *parse_idx = nullptr;
    int32_t parse_start = 0;
    uint8_t buf[128];
    int32_t total_len = 0;
    int32_t parse_len = _m_meter_comm->Read(vol, sizeof(vol), buf, sizeof(buf));
    if (write_log) {
        Loger(NORMAL, "meter[%d] vol req = %d, %s, res = %d, %s",_m_id, sizeof(vol), 
                BytesToHexString((const char *)vol, sizeof(vol)).c_str(), 
                total_len, BytesToHexString((const char *)buf, total_len).c_str());
    }

    if (parse_len <= 0) { 
        Loger(NORMAL, "meter[%d] vol return data_len is 0",_m_id);
        return -1;
    }
    parse_idx = buf + parse_start;
    // Loger(NORMAL, "meter[%d] vol_ parse_len = %d, data = %s", _m_id, parse_len,
    //                  BytesToHexString((const char *)parse_idx, parse_len).c_str());
    // check response status
    if (!GetCtrlMeterRespFlag(parse_idx[8])) {
        Loger(NORMAL, "get vol_ response failed");
        return -1;
    }

    Dlt645Frame_t* dlt = (Dlt645Frame_t*) parse_idx;
    if(memcmp(dlt->data,&(vol[14]),4) != 0){ //数据寄存器是否一致:返回值已将帧头 FE去除
    Loger(NORMAL, "meter[%d] vol_ data not match parse_len = %d, data = %s", _m_id, parse_len,
        BytesToHexString((const char *)parse_idx, parse_len).c_str());
        return -1;
    }

    uint8_t read_buf[4];

    bool sign_bit = CheckSignBit(parse_idx[parse_len - 3]);
    if (sign_bit > 0) {
        parse_idx[parse_len - 3] = parse_idx[parse_len - 3] & 0x7F;
    }
    uint16_t data_len = c_meterCmd[_m_meter_type].voltData.length()/2;
    GetDecimalValue(&parse_idx[parse_len - data_len -2], 
                data_len, 
                c_meterCmd[_m_meter_type].voltData.c_str(), read_buf);

    float v = 0.0;
    if (sign_bit > 0) {
        v = -*(float*)read_buf;
    } else {
        v = *(float*)read_buf;
    }
    // Loger(NORMAL, "vol_ = %f", v);
    *value = v;
    return 0;
}

int32_t dlt::Dlt645_2007::GetCur(float *value, bool write_log)
{
    if(_m_meter_type >= dlt::METER_645_TYPE_NUM){
        return -1;
    }
    std::lock_guard<std::mutex> guard(_m_mutex);
    *value = 0.0;
    
    uint8_t cur[] = {
        0xFE, 0xFE, 0xFE, 0xFE,             // 开头
        0x68,                               // 帧起始符
        0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, // 地址
        0x68,                               // 帧起始符
        0x11, 0x04,                         // 控制码、数据长度
        0x00, 0x00, 0x00, 0x00,             // A-B-C相电压
        0xDF,                               // 校验码
        0x16                                // 结束符
    };
    std::memcpy(cur+14, c_meterCmd[_m_meter_type].currCode, sizeof(uint8_t)*4);
    
    std::memcpy(cur+5, _m_sn, sizeof(_m_sn));
    uint8_t cs = CheckSum(cur+4, sizeof(cur)-4-2);
    cur[sizeof(cur)-2] = cs;

    uint8_t *parse_idx = nullptr;
    int32_t parse_start = 0;
    uint8_t buf[128];
    int32_t parse_len = _m_meter_comm->Read(cur, sizeof(cur), buf, sizeof(buf));
    if (write_log) {
        Loger(NORMAL, "meter[%d] current req = %d, %s, res = %d, %s", _m_id,sizeof(cur), 
            BytesToHexString((const char *)cur, sizeof(cur)).c_str(), 
            parse_len, BytesToHexString((const char *)buf, parse_len).c_str()); 
    }

    if (parse_len <= 0) { 
        Loger(NORMAL, "meter[%d] current return data_len is 0",_m_id);
        return -1;
    }

    parse_idx = buf + parse_start;
    // Loger(NORMAL, "meter[%d] cur_, parse_len = %d, data = %s", _m_id, parse_len,
    //     BytesToHexString((const char *)parse_idx, parse_len).c_str());
    // check response status
    if (!GetCtrlMeterRespFlag(parse_idx[8])) {
        Loger(NORMAL, "get current_, response failed");
        return -1;
    }

    Dlt645Frame_t* dlt = (Dlt645Frame_t*) parse_idx;
    if(memcmp(dlt->data,&(cur[14]),4) != 0){ //数据寄存器是否一致:返回值已将帧头 FE去除
    // Loger(NORMAL, "meter[%d] curr_ data not match parse_len = %d, data = %s", _m_id, parse_len,
    //     BytesToHexString((const char *)parse_idx, parse_len).c_str());
        return -1;
    }
    uint8_t read_buf[4];

    bool sign_bit = CheckSignBit(parse_idx[parse_len - 3]);
    if (sign_bit > 0) {
        parse_idx[parse_len - 3] = parse_idx[parse_len - 3] & 0x7F;
    }
    uint16_t data_len = c_meterCmd[_m_meter_type].currData.length()/2;
    GetDecimalValue(&parse_idx[parse_len - data_len-2], 
                data_len, c_meterCmd[_m_meter_type].currData.c_str(), read_buf);

    float v = 0.0;
    if (sign_bit > 0) {
        v = -*(float*)read_buf;
    } else {
        v = *(float*)read_buf;
    }
    // Loger(NORMAL, "cur_ = %f", v);
    *value = v;
    // Dlt645Frame_t *dlt645 = (Dlt645Frame_t *)parse_idx;
    return 0;
}

int32_t dlt::Dlt645_2007::GetForwardActivePower(float *value, bool write_log)
{
    std::lock_guard<std::mutex> guard(_m_mutex);
    *value = 0.0;
    uint8_t power[] = {
        0xFE, 0xFE, 0xFE, 0xFE,             // 开头
        0x68,                               // 帧起始符
        0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, // 地址
        0x68,                               // 帧起始符
        0x11, 0x04,                         // 控制码、数据长度
        0x33, 0x33, 0x93, 0x33,             // 正向有功总电能(小数4位,最后2位小数用一个字节扩展)
                                            // 33, 33, 43, 33 是低精度(小数2位)
        0xDF,                               // 校验码
        0x16                                // 结束符
    };

    std::memcpy(power+14, c_meterCmd[_m_meter_type].energyCode, sizeof(uint8_t)*4);
    std::memcpy(power+5, _m_sn, sizeof(_m_sn));
    uint8_t cs = CheckSum(power+4, sizeof(power)-4-2);
    power[sizeof(power)-2] = cs;

    uint8_t *parse_idx = nullptr;
    int32_t parse_start = 0;
    uint8_t buf[128];
    int32_t parse_len = _m_meter_comm->Read(power, sizeof(power), buf, sizeof(buf));
    if (write_log) {
        Loger(NORMAL, "meter[%d] forward_active_power req = %d, %s, res = %d, %s", _m_id, sizeof(power), 
            BytesToHexString((const char *)power, sizeof(power)).c_str(), 
            parse_len, BytesToHexString((const char *)buf, parse_len).c_str());
    }

    if (parse_len <= 0) { 
        Loger(NORMAL, "meter[%d] forward_active_power return data_len ", _m_id);
        return -1;
    }
    parse_idx = buf + parse_start;
    // Loger(NORMAL, "meter[%d] forward_active_power parse_len = %d, data = %s", _m_id, parse_len,
    //     BytesToHexString((const char *)parse_idx, parse_len).c_str());
    // check response status
    if (!GetCtrlMeterRespFlag(parse_idx[8])) {
        Loger(NORMAL, "get forward_active_power response failed");
        return -1;
    }
    Dlt645Frame_t* dlt = (Dlt645Frame_t*) parse_idx;
    if(memcmp(dlt->data,&(power[14]),4) != 0){ //数据寄存器是否一致:返回值已将帧头 FE去除
    Loger(NORMAL, "meter[%d] power data not match parse_len = %d, data = %s", _m_id, parse_len,
        BytesToHexString((const char *)parse_idx, parse_len).c_str());
        return -1;
    }

    uint8_t read_buf[4];
    uint16_t data_len = c_meterCmd[_m_meter_type].energyData.length()/2;
    GetDecimalValue(&parse_idx[parse_len - data_len-2], 
                data_len, 
                c_meterCmd[_m_meter_type].energyData.c_str(), read_buf);
    float v = *(float*)read_buf;
    // Loger(NORMAL, "forward_active_power = %f", v);
    *value = v;
    if (v >= 0.000000 && v < 0.000005) {
    // Loger(NORMAL, "forward_active_power = %f, send_buf = %s, recv_buf = %s", v, 
    //     BytesToHexString((const char*)power, sizeof(power)).c_str(), 
    //     BytesToHexString((const char *)parse_idx, parse_len).c_str());
    }
    // Dlt645Frame_t *dlt645 = (Dlt645Frame_t *)parse_idx;
    return 0;
}

int32_t dlt::Dlt645_2007::GetReverseActivePower(float *value)
{
    *value = 0.0;
    uint8_t power[] = {
        0xFE, 0xFE, 0xFE, 0xFE,             // 开头
        0x68,                               // 帧起始符
        0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, // 地址
        0x68,                               // 帧起始符
        0x11, 0x04,                         // 控制码、数据长度
        0x33, 0x33, 0x35, 0x33,             // 反向有功总电能
        0xDF,                               // 校验码
        0x16                                // 结束符
    };

    std::memcpy(power+5, _m_sn, sizeof(_m_sn));
    uint8_t cs = CheckSum(power+4, sizeof(power)-4-2);
    power[sizeof(power)-2] = cs;

    uint8_t *parse_idx = nullptr;
    int32_t parse_start = 0;
    uint8_t buf[128];
    int32_t parse_len = _m_meter_comm->Read(power, sizeof(power), buf, sizeof(buf));
    if (parse_len <= 0) {
        Loger(NORMAL, "reverse active power parse_len = %d, parse failed", parse_len);
        return -1;
    }
    parse_idx = buf + parse_start;
    //Loger(NORMAL, "reverse_active_power parse_len = %d, data = %s", parse_len,
                    //  BytesToHexString((const char *)parse_idx, parse_len).c_str());
    // check response status
    if (!GetCtrlMeterRespFlag(parse_idx[8])) {
        Loger(NORMAL, "get reverse_active_power response failed");
        return -1;
    }
    uint8_t read_buf[4];
    GetDecimalValue(&parse_idx[parse_len - 6], 
        sizeof(uint8_t) * 4, "XXXXXX.XX", read_buf);
    float v = *(float*)read_buf;
    //Loger(NORMAL, "reverse_active_power = %f", v);
    *value = v;
    return 0;
}