/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class loger Definition here.
 */
#include "evs_meter.h"

EVSMeter::EVSMeter(){
     
}

EVSMeter::~EVSMeter() {}

int32_t EVSMeter::MeterInit(void){
    dlt_ = new dlt::Dlt645_2007(&MeterComm_, METER_645_RELLIN_NORMAL, GUNA);
    return 0;
}

int32_t EVSMeter::MeterProcess(void){
    int32_t ret = -1;
    float vol = 0;
    float cur = 0;
    float elem = 0;
    float neelem = 0;
    uint8_t sn[DLT645_2007_SN_SIZE] = {0};
    MeterInit();
    MeterComm_.Init(GUNA, METER_645_RELLIN_NORMAL);
    if(-1 == dlt_->GetSNRefresh(sn)) {
        dlt_->GetSNRefresh(sn);
    } 
    Loger(2,NORMAL, "sn : %02X-%02X-%02X-%02X-%02X-%02X", \
        sn[0], sn[1], sn[2], sn[3], sn[4], sn[5]);

    while(1){
        dlt_->GetVol(&vol);
        dlt_->GetCur(&cur);
        dlt_->GetForwardActivePower(&elem);
        dlt_->GetReverseActivePower(&neelem);
        if(-1 == dlt_->CheckMeterStatus()) {
            if(++ret > 10) {
                Loger(NORMAL, "meter offline");
            }
        }
        _m_meter_vol = vol;
        _m_meter_curr = cur;
        _m_power = elem;
        Loger(1, NORMAL, "vol %f cur %f elem %f neelem %f", vol, cur, elem, neelem);
    }
    return ret;
}

