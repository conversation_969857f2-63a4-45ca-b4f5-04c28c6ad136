/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class loger Definition here.
 */
#include "uart.h"

const SerialMsgDef c_SerialMsg[] = {
    {UART_A, "/dev/ttyAS4",   9600,       "---",     -1},
    {UART_B, "/dev/ttyAS2",   9600,       "---",     -1}
};

Uart::Uart(UartIndex uart, int nSpeed, int nBits, char nEvent, int nStop){
    Fd_ = uartOpen(uart,O_RDWR |O_NONBLOCK | O_NOCTTY | O_NDELAY);
    if(Fd_ > 0){
        uartInit(nSpeed,nBits,nEvent,nStop);
    } else {
        Loger(NORMAL, "open uart failed");
    }
}
 

int Uart::uartOpen(int port, int flag)
{
    if(port < int(sizeof(c_SerialMsg)/sizeof(c_SerialMsg[0])))
    {
        if(Fd_ > 0){
            close(Fd_);
        }
        Fd_ = open(c_SerialMsg[port].dev485, flag);
    }
    return Fd_;
}

int Uart::uartInit(int nSpeed, int nBits, char nEvent, int nStop)  
{  
    struct termios newtio;
    bzero( &newtio, sizeof( newtio ) );  
    newtio.c_cflag  |=  CLOCAL | CREAD; //CLOCAL:忽略modem控制线  CREAD：打开接受者  
    newtio.c_cflag &= ~CSIZE; //字符长度掩码。取值为：CS5，CS6，CS7或CS8  

    switch( nBits )  
    {  
    case 7:  
        newtio.c_cflag |= CS7;  
        break;  
    case 8:  
        newtio.c_cflag |= CS8;  
        break;  
    }  

    switch( nEvent )  
    {  
    case 'O':  
        newtio.c_cflag |= PARENB; //允许输出产生奇偶信息以及输入到奇偶校验  
        newtio.c_cflag |= PARODD;  //输入和输出是奇及校验  
        newtio.c_iflag |= (INPCK | ISTRIP); // INPACK:启用输入奇偶检测；ISTRIP：去掉第八位  
        break;  
    case 'E':  
        newtio.c_iflag |= (INPCK | ISTRIP);  
        newtio.c_cflag |= PARENB;  
        newtio.c_cflag &= ~PARODD;  
        break;  
    case 'N':   
        newtio.c_cflag &= ~PARENB;  
        break;  
    }  

    switch( nSpeed )  
    {  
    case 2400:  
        cfsetispeed(&newtio, B2400);  
        cfsetospeed(&newtio, B2400);  
        break;  
    case 4800:  
        cfsetispeed(&newtio, B4800);  
        cfsetospeed(&newtio, B4800);  
        break;  
    case 9600:  
        cfsetispeed(&newtio, B9600);  
        cfsetospeed(&newtio, B9600);  
        break;  
    case 115200:  
        cfsetispeed(&newtio, B115200);  
        cfsetospeed(&newtio, B115200);  
        break;  
    case 460800:  
        cfsetispeed(&newtio, B460800);  
        cfsetospeed(&newtio, B460800);  
        break;  
    default:  
        cfsetispeed(&newtio, B9600);  
        cfsetospeed(&newtio, B9600);  
        break;  
    }  

    if( nStop == 1 )  
        newtio.c_cflag &=  ~CSTOPB; //CSTOPB:设置两个停止位，而不是一个  
    else if ( nStop == 2 )  
        newtio.c_cflag |=  CSTOPB;  

    newtio.c_cc[VTIME]  = 0; //VTIME:非cannoical模式读时的延时，以十分之一秒位单位  
    newtio.c_cc[VMIN] = 0; //VMIN:非canonical模式读到最小字符数  
    tcflush(Fd_,TCIFLUSH); // 改变在所有写入 fd 引用的对象的输出都被传输后生效，所有已接受但未读入的输入都在改变发生前丢弃。  
    if((tcsetattr(Fd_,TCSANOW,&newtio))!=0) //TCSANOW:改变立即发生  
    {  
        perror("com set error");  
        return -1;  
    }  
    printf("set done!\n\r");  
    return 0;  
}  

 
int Uart::uartWrite(char *data , int num)
{    
  int ret  = -1;
  ret = write(Fd_, data, num); 
  return ret;
}
 
int  Uart::uartRead( char *data, int num)
{
   int ret = -1;
   int i=0;
   char buf[1024] = {0};
   if(num > 1024)
   {
     ret = read(Fd_, buf, 1024);
   }
   else
   {
      ret = read(Fd_, buf, num);
   }
 
   for (i=0;i<num;i++)
    {
       data[i]=buf[i];
    }
 
   return ret;
}
void Uart::uartClose()
{
   if(Fd_ > 0)
      close(Fd_);
}
 
Uart::~Uart()
{
 
}




 