/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class loger Definition here.
 */
#include "loger.h"
#include "evs_pmm.h"

EVSPmm::EVSPmm() {
    memset(&PmmCaredMsg_,0, sizeof(PmmCaredMsg_));
    memset(&PmmCaredMsgBak_,0, sizeof(PmmCaredMsgBak_));
    memset(&PmmStateInfo_,0, sizeof(PmmStateInfo_));
}

EVSPmm::~EVSPmm() {}
int32_t EVSPmm::PmmInit(void){
    Loger(2,NORMAL, "PmmInit!");
    DCsoure_ = new fcm::dev::DCsource("/dev/ttyAS2", 9600);
    Loger(2,NORMAL, "PmmInit Uart init!");
    DCsoure_->Start(10); // init uart
    Loger(2,NORMAL, "Pmm Start!");
    DCState_ = E_DC_OFF;
    DCStateBak_ = E_DC_IDLE;
    return 0;
}

int32_t EVSPmm::SetPmmCaredMsg(PmmCaredMsgDef msg){
    PmmCaredMsg_ = msg;
    return 0;
}
static uint32_t cnt = 0;
int32_t EVSPmm::PmmProcess(void){
    int32_t ret = -1;
    fcm::dev::RegMsgDef dcMsg = {0};
    fcm::dev::RegMsgDef dcCmd = {0};
    PmmInit();
    while(1){

        if(memcmp(&PmmCaredMsgBak_, &PmmCaredMsg_, sizeof(PmmCaredMsgDef)) != 0) {
            memcpy(&PmmCaredMsgBak_, &PmmCaredMsg_, sizeof(PmmCaredMsgDef));

            dcCmd.setI = (PmmCaredMsg_.curr)*10000;
            dcCmd.setV = (PmmCaredMsg_.vol)*10 + 50;//二极管压降5V
            
            if(dcCmd.runStop !=  PmmCaredMsg_.cmd){
                if(PmmCaredMsg_.cmd == BAT_OFF){
                    DCState_ = E_DC_OFF;
                }
                dcCmd.runStop = PmmCaredMsg_.cmd;
            }
        }
        switch(DCState_){
            case E_DC_OFF:

                //if(dcMsg.runStop != BAT_OFF){
                    DCsoure_->set_DC_Run_Stop(0);
                    DCsoure_->read_byFrame03(&(dcMsg.runStop), fcm::dev::E_REG_RUN_STOP);
                ///}else{

                    DCsoure_->write_byFrame10(0, fcm::dev::E_REG_SET_U);
                    DCsoure_->write_byFrame10(0, fcm::dev::E_REG_SET_I);
                    DCsoure_->read_byFrame03(&(dcMsg.vol), fcm::dev::E_REG_SHOW_U);
                    if(dcMsg.vol < 50){// 5v
                       DCState_ = E_DC_IDLE;
                    }
                ///}

            break;

             case E_DC_IDLE:

                if(dcCmd.runStop == BAT_ON){
                    DCState_ = E_DC_SET_PARA;
                }

            break;

            case E_DC_SET_PARA:

                DCsoure_->write_byFrame10(dcCmd.setV, fcm::dev::E_REG_SET_U);
                DCsoure_->write_byFrame10(dcCmd.setI, fcm::dev::E_REG_SET_I);
                DCsoure_->read_byFrame03(&(dcMsg.setV), fcm::dev::E_REG_SET_U);
               // DCsoure_->read_byFrame03(&(dcMsg.setI), fcm::dev::E_REG_SET_I);

                if(dcMsg.setV == dcCmd.setV){
                    DCState_ = E_DC_ON;
                }
                
            break;
            case E_DC_ON:
                
                if(dcMsg.runStop != dcCmd.runStop){
                    DCsoure_->set_DC_Run_Stop(dcCmd.runStop);
                    DCsoure_->read_byFrame03(&(dcMsg.runStop), fcm::dev::E_REG_RUN_STOP);
                }else {
                    DCState_ = E_DC_RUNING;
                }

            break;
            case E_DC_RUNING:
                    DCsoure_->read_byFrame03(&(dcMsg.vol), fcm::dev::E_REG_SET_U);
                    DCsoure_->read_byFrame03(&(dcMsg.runStop), fcm::dev::E_REG_RUN_STOP);
                    
                    if(dcMsg.setV != dcCmd.setV ){
                        DCsoure_->write_byFrame10(dcCmd.setV, fcm::dev::E_REG_SET_U);
                    }
                    if(dcMsg.setI != dcCmd.setI ){
                        DCsoure_->write_byFrame10(dcCmd.setI, fcm::dev::E_REG_SET_I);
                        DCsoure_->read_byFrame03(&(dcMsg.setI), fcm::dev::E_REG_SET_I);
                    }
                    if(dcMsg.runStop != dcCmd.runStop){
                        DCsoure_->set_DC_Run_Stop(dcCmd.runStop);
                    }
            break;
            default:break;
        }

        PmmStateInfo_.currFb = dcMsg.curr; PmmStateInfo_.currFb /= 1000;
        PmmStateInfo_.onOffState = dcMsg.runStop;
        PmmStateInfo_.volFb = dcMsg.vol; PmmStateInfo_.volFb /= 10;

        if((++cnt)%10 == 0){
           Loger(2,NORMAL, "PMM runing state = %d,cmd = %d, vol = %.d,curr = %d, onstop = %d,volfb = %d,currfb = %d,setV = %d", \
                DCState_,dcCmd.runStop,dcCmd.setV,dcCmd.setI,dcMsg.runStop,\
                dcMsg.vol,dcMsg.curr,dcMsg.setV);
        }
        if(DCState_ != DCStateBak_){
            Loger(2,NORMAL, "PMM runing state = %d,cmd = %d, vol = %.d,curr = %d, onstop = %d,volfb = %d,currfb = %d,setV = %d", \
                DCState_,dcCmd.runStop,dcCmd.setV,dcCmd.setI,dcMsg.runStop,\
                dcMsg.vol,dcMsg.curr,dcMsg.setV);

            DCStateBak_ = DCState_;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    return ret;
}