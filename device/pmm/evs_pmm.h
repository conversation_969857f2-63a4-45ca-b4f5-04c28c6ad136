/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class evs_charger Declaration here.
 */
#ifndef _EVS_PMM_H
#define _EVS_PMM_H
#include "fcm_dev_DCsource.h"
#include "timer.h"
typedef struct{
    uint32_t cmd;
    float vol;
    float curr;
}PmmCaredMsgDef;

typedef struct{
    uint32_t onOffState;
    float volFb;
    float currFb;
    uint32_t faultCode;
}PmmStateInfoDef;

typedef enum{
    E_DC_OFF = 0,
    E_DC_IDLE = 1,
    E_DC_SET_PARA = 2,
    E_DC_ON = 3,
    E_DC_RUNING =4,
    E_DC_ERR= 5
}DCStateDef;

class EVSPmm {
public:
    EVSPmm();
    ~EVSPmm();
private:

    int32_t thisId = 0;
    fcm::dev::DCsource *DCsoure_;
    Timer timer_;
    PmmCaredMsgDef PmmCaredMsg_; // evs to DCconfig
    PmmCaredMsgDef PmmCaredMsgBak_;
    PmmStateInfoDef PmmStateInfo_; // pmm state to evs
private:
    DCStateDef DCState_;
    DCStateDef DCStateBak_;
public:
    int32_t PmmInit(void);
    int32_t PmmProcess(void);
    int32_t SetPmmCaredMsg(PmmCaredMsgDef msg);
    PmmStateInfoDef GetPmmStateInfo(void){return PmmStateInfo_;};
};


#endif  //_EVS_PMM_H