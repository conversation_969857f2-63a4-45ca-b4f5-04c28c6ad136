/*****************************************************************************
*  @file       fcm_dev_<PERSON>source.cc                                                   *
*  @brief    直流电压源                                                             *
*                                                                                                      *
*  <AUTHOR>
*  @version  *******(版本号)                                                   *
*----------------------------------------------------------------------------*
*  Remark         : Description                                              *
*----------------------------------------------------------------------------*
*  Change History :                                                          *
*  <Date>     | <Version> | <Author>        | <Description>                  *
*----------------------------------------------------------------------------*
*  2022/12/22 | *******   |             | Create file                    *
*****************************************************************************/
#include  "fcm_dev_DCsource.h"

#define DC_FRAME_READ_DELAY    (20)   // ms
#define DC_SLAVE_ADDR               (0x00)
#define DC_FRAME_MIN_LEN            (4)

#pragma pack(1)
//03多寄存器读命令帧数据结构
typedef struct _DCsourceFrame03_t {
    uint8_t slaveAddr;                             // 从机地址
    uint8_t funcCode;                             // 功能码
    uint16_t startAddr;                          // 寄存器起始地址
    uint16_t NumOfReg;                       //  寄存器数量
    uint16_t u16_crc;                              //  crc校验
} DCsourceFrame03_t;
//03多寄存器从机返回数据结构
typedef struct _DCsourceFrame03Ans_t {
    uint8_t slaveAddr;                             // 从机地址
    uint8_t funcCode;                             // 功能码
    uint8_t ByteCount;                        //  数据长度 2*N
    uint8_t  *dataPtr;                             //   数据域指针 N*2 bytes
    uint16_t u16_crc;                              //  crc校验
} DCsourceFrame03Ans_t;

//10多寄存器写入命令帧数据结构
typedef struct _DCsourceFrame10_t {
    uint8_t slaveAddr;                             // 从机地址
    uint8_t funcCode;                             // 功能码
    uint16_t startAddr;                          // 寄存器起始地址
    uint16_t NumOfReg;                      //  寄存器数量 N
    uint8_t  byteCount;                         //  字节长度 2*N
    uint8_t  data[2];                                //  数据域指针 N*2 bytes
    uint16_t u16_crc;                             //  crc校验
}DCsourceFrame10_t;
//10多寄存器从机返回数据结构
typedef struct _DCsourceFrame10Ans_t {
    uint8_t slaveAddr;                             // 从机地址
    uint8_t funcCode;                             // 功能码
    uint16_t startAddr;                          // 寄存器起始地址
    uint16_t NumOfReg;                       // 寄存器数量 N
    uint16_t u16_crc;                              //  crc校验
} DCsourceFrame10Ans_t;
//从机异常响应帧
typedef struct _DCsourceFrameErrorAns_t{
    uint8_t  slaveAddr;                            // 从机地址
    uint8_t   errCode;                              //  错误码
    uint8_t   errType;                              //   错误类型
    uint16_t  u16_crc;                             //   crc 校验
}DCsourceFrameErrorAns_t;
#pragma pack()

/**
* @brief 构造函数
*/
fcm::dev::DCsource::DCsource(const char *dev, uint32_t baud) 
: _m_mutex(), _m_baud(baud), _m_dev(dev), _m_sp(nullptr)
{ 
    _m_sp.reset(new rs485(_m_dev.c_str(), " ", _m_baud, ParityNone, DataBits8, StopTwo, FlowNone));
    Loger(NORMAL, "DCsource class created");
}
/**
* @brief 析构函数
*/
fcm::dev::DCsource::~DCsource() 
{ 
    if (_m_sp != nullptr) {
        _m_sp->closeRs485();
    }
    _m_sp = nullptr;
}
 /**
 * @brief 获取模拟器波特率
 */
uint32_t fcm::dev::DCsource::GetBaud()
{
    return _m_baud;
}
/**
 * @brief 获取设备路径
 */
const char* fcm::dev::DCsource::GetDev()
{ 
    return _m_dev.c_str();
}
/**
 * @brief 启动直流电压源 操作
 */
int32_t  fcm::dev::DCsource::Start(int32_t try_times)
{
    int32_t open_ret = -1;

    if (try_times < 0) 
    { // loop forever
        while (_m_sp->openRs485() != 0) 
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        }
    } 
    else 
    {
        while (try_times--) 
        {
            open_ret = _m_sp->openRs485();
            if (open_ret == 0) {
                break;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        }
        if (open_ret != 0) {
            return -1;
        }
    }

    return 0;
}
/**
 * @brief 停止直流电压源 操作
 */
void fcm::dev::DCsource::Stop()
{
    if (_m_sp != nullptr) {
        _m_sp->closeRs485();
    }
}
/**
 * @brief  crc16计算
 * @param uint8_t *data , uint8_t len
 * @return uint16
 */
uint16_t fcm::dev::DCsource::Crc16(uint8_t *data , uint8_t len)
{
	uint8_t i = 0;            
	uint16_t crc = 0xffff;
	while(len--)
	{
        crc ^= *data++;
		for(i = 0; i < 8; i++)
		{
			if(crc & 0x01)  
			{
                crc = (crc >> 1) ^ 0xa001; 	  // Polynomial: x^16 + x^15 + x^2 + 1 (0xa001)
			}
			else
			{
				crc = crc >> 1;
			}
		}
	}
	return crc;
}
/**
 * @brief   直流电压源读取函数
 * @param  
 *        uint8_t *cmd :   读命令
 *        in32_t  length:  读命令长度
 *        uint8_t *out:   读结果指针
 *        int32_t out_len: 读结果长度
 *        int32_t  *start:  
 * @return   int32_t
 */
int32_t fcm::dev::DCsource::Read(uint8_t *cmd, int32_t length, uint8_t *out, int32_t out_len,int32_t *start)
{
    char tmp[100] = {0};
    //clear recv buffer
    _m_sp->syncRead(tmp, sizeof(tmp));
    _m_sp->syncWrite((char*)cmd, length);
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    //Loger(NORMAL, "send cmd == %s", BytesToHexString((const char *)cmd, length));
    int32_t total_read = 0, read_len = 0, max_count = 10, count = max_count, flag = 0;
    std::memset(out, 0, out_len);
    do {
        read_len = _m_sp->syncRead((char*)out+total_read, out_len-total_read);
        
        if (count <= 0 || read_len < 0) {
            break;
        } else if (read_len == 0) {
            --count;
            std::this_thread::sleep_for(std::chrono::milliseconds(DC_FRAME_READ_DELAY));
            continue;
        }

        count = max_count;
        total_read += read_len;
        /*
        // Loger(NORMAL, "Daniel read count = %d, read_len = %d, total = %d, out_len = %d,data = %s", \
        //     count, read_len, total_read,out_len, BytesToHexString((const char *)out, total_read));
        // parse data
        */
        flag = ParseRecvRawData(out, total_read, start);
        if (flag > 0) {
            //Loger(NORMAL, "total_len = %d, data = %s", BytesToHexString((const char *)out, read_len));
            break;
        }
        /*
        // else
        // {
        //     // if (flag < 0) { // 清空缓冲区，继续接收
        //     // total_read = 0;
        //     // }
        //     std::this_thread::sleep_for(std::chrono::milliseconds(DC_FRAME_READ_DELAY));
        //     _m_sp->syncWrite((char*)cmd, length);
        //     Loger(NORMAL, "send cmd == %s", BytesToHexString((const char *)cmd, length));
        //     usleep(25*1000);
        //     continue;
        // }
        */

        if (flag <= 0) { // 继续接收
            std::this_thread::sleep_for(std::chrono::milliseconds(DC_FRAME_READ_DELAY));
            continue;
        }
        /*
        // } else if (flag < 0) { // 清空缓冲区，继续接收
        //     total_read = 0;
        //     std::this_thread::sleep_for(std::chrono::milliseconds(DC_FRAME_READ_DELAY));
        //     continue;
        // }
        */
    } while (1);
    if (flag <= 0) {
        // 解析失败
        return -1;
    }
    return flag;
}

/**
 * @brief 解析直流电压源数据
 * @param start 标识解析数据头索引
 * @return > 0 成功， 0 长度不够, -1 解析失败
 */
int32_t fcm::dev::DCsource::ParseRecvRawData(uint8_t *data, int32_t length, int32_t *start)
{
    int32_t parse_len = 0;
    int32_t data_len = length - DC_FRAME_MIN_LEN, real_len = 0;
    uint16_t real_crc = 0;
    if (data_len < 1) {
        return parse_len;   // 数据长度不够，需要继续接收
    }

    for (int32_t i = 1; i < length; i++) {
        // 找到帧头 从机地址
        if((data[i-1] == DC_SLAVE_ADDR)&&
            ((data[i] == E_DCSC_CODE_03)||(data[i] == E_DCSC_CODE_83)||(data[i] == E_DCSC_CODE_10)))
        {
                if(data[i] == E_DCSC_CODE_03)
                {
                    DCsourceFrame03Ans_t *fram03Ans = nullptr;
                    fram03Ans = (DCsourceFrame03Ans_t*)&data[i-1];
                    if(fram03Ans->ByteCount > data_len-1)
                    {
                        return parse_len;   // 数据长度不够，需要继续接收
                    }
                    real_len = DC_FRAME_MIN_LEN+1+fram03Ans->ByteCount;
                    real_crc = (data[real_len-1] << 8) + data[real_len-2];
                    uint16_t crc = Crc16(data+i-1,real_len-2);
                    if(real_crc != crc)
                    {
                        Loger(NORMAL, "checksum failed = %02x, crc = %02x", real_crc, crc);
                        continue;  // 校验位错误
                    }
                }
                else if(data[i] == E_DCSC_CODE_83)
                {
                    real_len = DC_FRAME_MIN_LEN+1;
                    real_crc = (data[real_len-1]<<8) + data[real_len-2];
                    uint16_t crc = Crc16(data+i-1,real_len-2);
                    if(real_crc != crc)
                    {
                        Loger(NORMAL, "checkcrc failed = %02x, crc = %02x", real_crc, crc);
                        continue;  // 校验位错误
                    }
                }
                else if(data[i] == E_DCSC_CODE_10)
                {
                    real_len = DC_FRAME_MIN_LEN+4;
                    real_crc = (data[real_len-1]<<8) + data[real_len-2];
                    uint16_t crc = Crc16(data+i-1,real_len-2);
                    if(real_crc != crc)
                    {
                        Loger(NORMAL, "checkcrc failed = %02x, crc = %02x", real_crc, crc);
                        continue;  // 校验位错误
                    }
                }
                else break;

                *start = i-1;
                parse_len = real_len;
                return parse_len;
        }
        else continue;
    }

    return -1;
}

 /**
 * @brief  通过10帧写入数据(1个寄存器)
 * @param  uint8_t  set_data,E_DCSC_REG_ADDR e_regAddr
 * @return  int32_t
 */
int32_t  fcm::dev::DCsource::write_byFrame10(uint16_t set_data,E_DCSC_REG_ADDR e_regAddr)
{
    std::lock_guard<std::mutex> guard(_m_mutex);
    if(e_regAddr > E_REG_DCSC_MAX)
    {
        Loger(NORMAL, "set regist not exit");
        return -1;
    }
    uint8_t rBuf[8] = {0};
    uint16_t real_len = DC_FRAME_MIN_LEN+7;
    int32_t parse_start = 0;

    uint8_t wBuf[16] = {0};
    wBuf[0] = DC_SLAVE_ADDR;
    wBuf[1] = E_DCSC_CODE_10;
    wBuf[2] = (e_regAddr >> 8)&0xFF;
    wBuf[3]  = e_regAddr&0xFF;
    wBuf[4]  = 0x00;
    wBuf[5]  = 0x01;
    wBuf[6]  = 0x02;
    wBuf[7]  = (set_data >> 8)&0xFF;
    wBuf[8]  = set_data&0xFF;
    uint16_t crc = Crc16(wBuf,real_len-2);
    wBuf[9]  = crc&0xFF;
    wBuf[10]  = (crc >> 8)&0xFF;

    int32_t parse_len = Read(wBuf, real_len, rBuf, sizeof(rBuf), &parse_start);
    if (parse_len < 0) {
        Loger(NORMAL, "addr parse_len = %d, parse failed", parse_len);
        return -2;
    }
    uint8_t *parse_idx = nullptr;
    parse_idx = rBuf + parse_start;
    //Loger(NORMAL, "addr parse_len = %d, data = %s", parse_len, BytesToHexString((const char*)parse_idx, parse_len));

    uint16_t  readReg = (*(parse_idx+2) << 8)+*(parse_idx+3);
    if((*(parse_idx+1) != E_DCSC_CODE_10)||(readReg != e_regAddr))
    {
        Loger(NORMAL, "write_byFrame10 response failed");
        return -3;
    }
    return 0;
}

/**
 * @brief  通过03帧读寄存器数据
 * @param  uint16_t  *pData,E_DCSC_REG_ADDR e_regAddr
 * @return  int32_t
 */
int32_t fcm::dev::DCsource::read_byFrame03(uint16_t *pData,E_DCSC_REG_ADDR e_regAddr)
{
    std::lock_guard<std::mutex> guard(_m_mutex);
    if(e_regAddr > E_REG_DCSC_MAX)
    {
        Loger(NORMAL, "set regist not exit");
        return -1;
    }
    uint8_t buf[7] = {0};
    uint16_t real_len = DC_FRAME_MIN_LEN+4;
    int32_t parse_start = 0;

    uint8_t wBuf[16] = {0};
    wBuf[0] = DC_SLAVE_ADDR;
    wBuf[1] = E_DCSC_CODE_03;
    wBuf[2] = (e_regAddr >> 8)&0xFF;
    wBuf[3]  = e_regAddr&0xFF;
    wBuf[4]  = 0x00;
    wBuf[5]  = 0x01;
    uint16_t crc = Crc16(wBuf,real_len-2);
    wBuf[6]  =  crc&0xFF;
    wBuf[7]  = (crc >> 8)&0xFF;

    int32_t parse_len = Read(wBuf, real_len, buf, sizeof(buf), &parse_start);
    if (parse_len < 0) {
        Loger(NORMAL, "addr parse_len = %d, parse failed", parse_len);
        return -2;
    }
    uint8_t *parse_idx = nullptr;
    parse_idx = buf + parse_start;
    //Loger(1,NORMAL, "addr parse_len = %d, data = %s", parse_len, BytesToHexString((const char*)parse_idx, parse_len));
    if(*(parse_idx+1) != E_DCSC_CODE_03)
    {
        Loger(1,NORMAL, "read_byFrame03 response failed");
        return -3;
    }
    *pData = buf[parse_start+4] | buf[parse_start+3]<<8;
    //std::memcpy(pData,parse_idx+3,2);
    return 0;
}

/**
 * @bref 直流源输出ON/OFF
 * @param uint8_t  ON:1 OFF:0
 * @return int32_t
*/
int32_t fcm::dev::DCsource::set_DC_Run_Stop(uint16_t cmd)
{
    std::lock_guard<std::mutex> guard(_m_mutex);
    uint8_t rBuf[1024] = {0};
    uint16_t real_len = DC_FRAME_MIN_LEN+7;
    int32_t parse_start = 0;

    uint8_t wBuf[16] = {0};
    wBuf[0] = DC_SLAVE_ADDR;
    wBuf[1] = E_DCSC_CODE_10;
    wBuf[2] = (E_REG_RUN_STOP >> 8)&0xFF;
    wBuf[3]  = E_REG_RUN_STOP&0xFF;
    wBuf[4]  = 0x00;
    wBuf[5]  = 0x01;
    wBuf[6]  = 0x02;
    wBuf[7]  = (cmd >> 8)&0xFF;
    wBuf[8]  = cmd&0xFF;
    uint16_t crc = Crc16(wBuf,real_len-2);
    wBuf[9]  = crc&0xFF;
    wBuf[10]  = (crc >> 8)&0xFF;

    int32_t parse_len = Read(wBuf, real_len, rBuf, sizeof(rBuf), &parse_start);
    if (parse_len < 0) {
        Loger(NORMAL, "addr parse_len = %d, parse failed", parse_len);
        return -2;
    }
    uint8_t *parse_idx = nullptr;
    parse_idx = rBuf + parse_start;
    Loger(NORMAL, "addr parse_len = %d, data = %s", parse_len, BytesToHexString((const char*)parse_idx, parse_len));

    uint16_t  readReg = (*(parse_idx+2) << 8)+*(parse_idx+3);
    if((*(parse_idx+1) != E_DCSC_CODE_10)||(readReg != E_REG_RUN_STOP))
    {
        Loger(NORMAL, "write_byFrame10 response failed");
        return -3;
    }
    return 0;
}





