/*****************************************************************************
*  @file       fcm_dev_<PERSON>source.h                                                   *
*  @brief    直流源功能实现                                              *
*                                                                                                     *
*  <AUTHOR>
*  @version  *******(版本号)                                                  *
*----------------------------------------------------------------------------*
*  Remark         : Description                                              *
*----------------------------------------------------------------------------*
*  Change History :                                                          *
*  <Date>     | <Version> | <Author>        | <Description>                  *
*----------------------------------------------------------------------------*
*  2022/12/22| *******   |             | Create file                    *
*****************************************************************************/
#ifndef _DEV_DC_SOURCE_H
#define _DEV_DC_SOURCE_H

#include <thread>
#include <cstring>
#include <mutex>
#include <memory>
#include "loger.h"
#include "rs485.h"
#include "evs_common.h"

namespace fcm {

namespace dev{
        typedef enum  {
            E_REG_SET_U = 0x1000,
            E_REG_SET_I = 0x1001,
            E_REG_SHOW_U = 0x1002,
            E_REG_SHOW_I = 0x1003,
            E_REG_RUN_STOP = 0x1004,
            E_REG_SET_ADDR =0x1005,
            E_REG_DCSC_MAX,
        }E_DCSC_REG_ADDR;

        typedef enum{
        E_DCSC_CODE_03 = 0x03,
        E_DCSC_CODE_83 = 0x83,
        E_DCSC_CODE_10 = 0x10,
        }E_DCSC_FUNC;
    typedef struct{
        uint16_t setV;//0.1V
        uint16_t setI;// 0.001A
        uint16_t vol;//0.1V
        uint16_t curr;// 0.001A
        uint16_t runStop;// 0 off 1ON 
    }RegMsgDef;
    class DCsource {

        public:
           /**
            * @brief 构造函数
            */
            DCsource(const char *dev, uint32_t baud);
            /**
            * @brief 析构函数
            */
            ~DCsource();

            /**
             * @brief 获取bms模拟器波特率
             */
            uint32_t GetBaud();

            /**
             * @brief 获取设备路径
             */
            const char* GetDev();

            /**
             * @brief 启动bms模拟器操作
             */
            int32_t Start(int32_t try_times = -1);

            /**
             * @brief 停止bms模拟器操作
             */
            void Stop();

            /**
             * @brief  crc16计算
             * @param uint8_t *data , uint8_t len
             * @return uint16
             */
            uint16_t Crc16(uint8_t *data , uint8_t len);

             /**
             * @brief  通过10帧写入数据
             * @param  uint16_t  set_data,E_DCSC_REG_ADDR e_regAddr
             * @return  int32_t
             */
            int32_t write_byFrame10(uint16_t set_data,E_DCSC_REG_ADDR e_regAddr);

            /**
             * @brief  通过03帧读寄存器数据
             * @param  uint16_t  *pData,E_DCSC_REG_ADDR e_regAddr
             * @return  int32_t
             */
            int32_t read_byFrame03(uint16_t *pData,E_DCSC_REG_ADDR e_regAddr);

            /**
             * @bref 直流源输出Run/Stop
             * @param uint8_t  Run:1 Stop:0
             * @return int32_t
            */
           int32_t set_DC_Run_Stop(uint16_t cmd);


        private:
            /**
             * @brief 从串口读取模拟器响应值
             * @param cmd 请求的命令
             * @param length 命令长度
             * @param out 待读取出来的数据指针(外部申请内存)
             * @param out_len 待读取buffer的总长度
             * @return 读取出来的数据长度, >0 成功， <0失败
             */
            int32_t Read(uint8_t *cmd, int32_t length, uint8_t *out, int32_t out_len,int32_t *start);

             /**
             * @brief 解析模拟器数据
             * @param start 标识解析数据头索引
             * @return > 0 成功， 0 长度不够, -1 解析失败
             */
            int32_t ParseRecvRawData(uint8_t *data, int32_t length, int32_t *start);

        private:
            std::mutex _m_mutex; 
            uint32_t    _m_baud;
            std::string  _m_dev;
            std::shared_ptr<rs485> _m_sp;
    };
}; // namespace dev
}; //namespace fcm

#endif /* _DEV_DC_SOURCE*/
