/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class loger Definition here.
 */
#include "loger.h"
#include "evs_car.h"
#include "evs_pmm.h"
#include "evs_manager.h"

/***************************************************************************
函数名:CmdToPmm
功能描述: 100ms/per
1 车桩交互至预充阶段ENERGY_TRANSFER-FC7-FDC1，高压源开机，开机电压为用户设定值
2 收到车辆侧预充ready后 计时50s，关闭高压源和投切接触器
3 重连至预充阶段时，需按照1-2步骤逻辑重新开启高压源
作者:
日期:
****************************************************************************/
void EVSCar::CmdToPmm(void){
    // set cmd to pmm
    PmmCaredMsgDef  pmm_cared = {0,0.0,0.5}; 
    if(DataToFC_.workCmd == 0xAA &&  // ON 
        DataFromFC_.FCState.evsState == ENERGY_TRANSFER && // pre charging
        DataFromFC_.FCState.FC == GB2015P::FCType::FC7 &&
        DataFromFC_.FCState.FDC == GB2015P::FDCType::FDC1 &&
        evPause_ != 2 ){

        if(DataFromFC_.seReady == 1 && 
            (DataFromFC_.chargerRlyState == 0x01)){
           batCnt_ = 500;
        }else{
            ++batCnt_;
            UpLimit16(batCnt_,500);
        }

        if(batCnt_ < 500){
            pmm_cared.cmd = BAT_ON;
        }else{
            pmm_cared.cmd = BAT_OFF;
        }
    }else{
        pmm_cared.cmd = BAT_OFF;
        batCnt_ = 0;
    }
    if(evPause_ == 2) { // 暂停恢复
        ++batPauseCnt_;
        UpLimit16(batPauseCnt_,500);

        if(batPauseCnt_ < 500){
            pmm_cared.cmd = BAT_ON;
        }else{
            pmm_cared.cmd = BAT_OFF;
            evPause_ = 0;
            batPauseCnt_ = 0;
        }
    }
    pmm_cared.curr = 0.1;
    pmm_cared.vol = eVSProto.eVSManageData.electriCtrlManageBuff.insultCtrl.batVol;
    if(memcmp(&PmmCaredMsg_,&pmm_cared,sizeof(pmm_cared))){
        Loger(NORMAL,"Pmm cmd msg::cmd = %d vol = %.2f,cur = %.2f",
                pmm_cared.cmd,pmm_cared.vol,pmm_cared.curr);
        memcpy(&PmmCaredMsg_,&pmm_cared,sizeof(pmm_cared));

        ((EVSManager*)_m_ctx)->EvsPmm_.SetPmmCaredMsg(pmm_cared);
    }
}
void EVSCar::StateFromPmm(void){
    //get pmm msg
    PmmStateInfoDef pmm_state = ((EVSManager*)_m_ctx)->EvsPmm_.GetPmmStateInfo();
    if(memcmp(&PmmStateInfo_,&pmm_state,sizeof(pmm_state))){
        Loger(NORMAL,"Pmm msg::cmdFb = %d fb vol = %.2f,curFb = %.2f,falult num = %d",
                pmm_state.onOffState,pmm_state.volFb,pmm_state.currFb,pmm_state.faultCode);
        memcpy(&PmmStateInfo_,&pmm_state,sizeof(pmm_state));
    }
}
/***************************************************************************
函数名:BatteryReady
功能描述: 100ms/per
1 车桩交互预充阶段
2 高压源开机态
3 高压源到达设定电压
4 高压源无故障
5 电池接触器吸合
作者:
日期:
****************************************************************************/
int32_t EVSCar::BatteryReady(void){
    // precharge
    if(DataToFC_.workCmd == 0xAA &&  // ON 
        DataFromFC_.FCState.evsState == ENERGY_TRANSFER && // pre charging
        DataFromFC_.FCState.FC == GB2015P::FCType::FC7 &&
        DataFromFC_.FCState.FDC == GB2015P::FDCType::FDC1 
    ){
        if(PmmCaredMsg_.cmd == PmmStateInfo_.onOffState
            && abs(PmmCaredMsg_.vol - PmmStateInfo_.volFb) < 3.0 
            && PmmStateInfo_.faultCode == 0
            && RlyFb_[RLY_BAT_P] == RLY_CLOSE
            && RlyFb_[RLY_BAT_N] == RLY_CLOSE){
            return ACK;
        }
    }
    return NACK;
}
/***************************************************************************
函数名:BatteryManage
功能描述: 100ms/per
1 车桩交互至预充阶段ENERGY_TRANSFER-FC7-FDC1，高压源开机，开机电压为用户设定值
2 收到车辆侧预充ready后 计时50s，关闭高压源和投切接触器
3 重连至预充阶段时，需按照1-2步骤逻辑重新开启高压源
作者:
日期:
****************************************************************************/
void EVSCar::BatteryManage(void){
    StateFromPmm();
    evPausePmm();
    CmdToPmm();
}

void EVSCar::evPausePmm(void) 
{
    if(eVSProto.eVSManageData.electriCtrlManageBuff.electricCtrl.evPause == 0xAA && evPause_ == 0) {
        evPause_ = 1;
        Loger(NORMAL, "evPause_ %d", evPause_);
    } else if(eVSProto.eVSManageData.electriCtrlManageBuff.electricCtrl.evPause == 0x00 && evPause_ == 1) {
        evPause_ = 2;
        Loger(NORMAL, "evPause_ %d", evPause_);
    }
}