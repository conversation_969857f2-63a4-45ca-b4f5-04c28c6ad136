/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class evs_charger Declaration here.
 */
#ifndef _SWITCH_CTRL_H
#define _SWITCH_CTRL_H
#include <string>
#include "gpio_ctrl.h"

typedef enum{
    R_OPEN = 0,
    R_59K = 1,
    R_67K = 2,
    R_83K = 3,
    R_100K = 4,
    R_142K = 5,
    R_200K = 6,
    R_500K = 7,
    R_MAX
}RIndexEnum;

typedef struct{
    RIndexEnum indexR;
    uint32_t R;
    uint32_t k5;
    uint32_t k6;
    uint32_t k7;
}SwitchMsgDef;
typedef enum{
    RLY_LOAD_P = 0, //K1
    RLY_LOAD_N = 1, //K2
    RLY_BAT_P = 2, //K3
    RLY_BAT_N = 3, //K4
    RLY_RES_K5 = 4, //K5
    RLY_RES_K6 = 5, //K6
    RLY_RES_K7 = 6, //K7
    RLY_INSULT_KP = 7,
    RLY_INSULT_KN = 8,
    RLY_INSULT_KPE = 9,
    RLY_S2 = 10,
    RLY_S3 = 11,
    RLY_FAN_CTRL = 12,
    RLY_ADC_SWITCH = 13,
    RLY_NUM
}RlyEnumDef;

typedef struct{
    RlyEnumDef rly;
    GpioIndex cmd;
    GpioIndex fb;
    uint32_t fbEnable;
}RlyIoLinkDef;

#endif  //_SWITCH_CTRL_H