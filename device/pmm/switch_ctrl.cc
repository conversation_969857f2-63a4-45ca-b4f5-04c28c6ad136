/* Copyright 2024, Xi<PERSON>an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class loger Definition here.
 */
#include "loger.h"
#include "evs_car.h"
#include "evs_manager.h"
#include "evs_gpio.h"
#include "switch_ctrl.h"

const SwitchMsgDef c_switchMsg[]={
    /* idx    R       100K_K5       200K_K6    500K_K7*/
    {R_OPEN , 0,     RLY_OPEN,     RLY_OPEN,   R<PERSON><PERSON>_OPEN,},
    {R_59K  , 59,    RLY_CLOSE,    RLY_CLOSE,  RLY_CLOSE,},
    {R_67K  , 67,    RLY_CLOSE,    RLY_CLOSE,  RLY_OPEN,},
    {R_83K  , 83,    RLY_CLOSE,    RLY_OPEN,   RLY_CLOSE,},
    {R_100K , 100,   RLY_CLOSE,    RLY_OPEN,   RLY_OP<PERSON>,},
    {R_142K , 142,   <PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>,     RLY_CLOSE,  RLY_CLOSE,},
    {R_200K , 200,   RLY_OPEN,     RLY_CLOSE,  RLY_OPEN,},
    {R_500K , 500,   RLY_OPEN,     RLY_OPEN,   RLY_CLOSE,}
};

const RlyIoLinkDef c_RlyIo[]{
    {RLY_LOAD_P,        GPIO_OUT_K1K2,      GPIO_OUT_K1_FB, 1}, //K1
    {RLY_LOAD_N,        GPIO_OUT_K1K2,      GPIO_OUT_K2_FB, 1}, //K2
    {RLY_BAT_P,         GPIO_BAT_K3K4,      GPIO_BAT_K3_FB, 1}, //K3
    {RLY_BAT_N,         GPIO_BAT_K3K4,      GPIO_BAT_K4_FB, 1}, //K4
    {RLY_RES_K5,        GPIO_RES_K5,        GPIO_RES_K5_FB, 1}, //K5
    {RLY_RES_K6,        GPIO_RES_K6,        GPIO_RES_K6_FB, 1}, //K6
    {RLY_RES_K7,        GPIO_RES_K7,        GPIO_RES_K7_FB, 1}, //K7
    {RLY_INSULT_KP,     GPIO_INSULT_KP,     GPIO_DI_START, 0}, //
    {RLY_INSULT_KN,     GPIO_INSULT_KN,     GPIO_DI_START, 0}, //
    {RLY_INSULT_KPE,    GPIO_INSULT_KPE,    GPIO_DI_START, 0}, //
    {RLY_S2,            GPIO_S2,            GPIO_DI_START, 0}, //
    {RLY_S3,            GPIO_S3,            GPIO_DI_START, 0}, //
    {RLY_FAN_CTRL,      GPIO_FAN_CTRL,      GPIO_DI_START, 0}, //
    {RLY_ADC_SWITCH,    GPIO_DO_RELAY_ADC,  GPIO_DI_START, 0}, //
};
/***************************************************************************
函数名:OutRlyManage
功能描述: 100ms/per
1 高压接触器K5 K6管理
2 连接方式：默认 0x00 正接； 0x01 反接； 0x02 开路
作者:
日期:
****************************************************************************/
void EVSCar::RlyManage(void){
    RlyDesireState();
    ElectControl();
    RlyCtrl();
    RlyFaultDetect();
}
/***************************************************************************
函数名:OutRlyManage
功能描述: 100ms/per
1 高压接触器K5 K6管理
2 连接方式：默认 0x00 正接； 0x01 反接； 0x02 开路
作者:
日期:
****************************************************************************/
uint32_t ins_cnt = 0;
void EVSCar::RlyDesireState(void){
    uint32_t load_cmd = 0;// desire cmd
    uint32_t load_excu = 0;// excute cmd

    // if(++ins_cnt %10 == 0){
    //     if(RlyCmd_[RLY_RES_K5] == RLY_CLOSE){
    //         RlyCmd_[RLY_RES_K5] = RLY_OPEN;
    //     }else{
    //         RlyCmd_[RLY_RES_K5] = RLY_CLOSE;
    //     }
    // }

    //out rly
    if(eVSProto.eVSManageData.electriCtrlManageBuff.electricCtrl.contactK5 == 0xAA &&
        eVSProto.eVSManageData.electriCtrlManageBuff.electricCtrl.contactK6 == 0xAA){
            load_cmd = RLY_CLOSE;
    }else{
        load_cmd = RLY_OPEN;
    }
    // battery Rly
    if(RlyCmd_[RLY_BAT_P] == RLY_OPEN && load_cmd == RLY_CLOSE){
        load_excu = RLY_CLOSE;
    }else{
        load_excu = RLY_OPEN;
    }

    if(RlyCmd_[RLY_LOAD_P] != load_excu || RlyCmd_[RLY_BAT_P] != PmmCaredMsg_.cmd){
        RlyCmd_[RLY_LOAD_P] = load_excu;
        RlyCmd_[RLY_LOAD_N] = load_excu;
        RlyCmd_[RLY_BAT_P] = PmmCaredMsg_.cmd;
        RlyCmd_[RLY_BAT_N] = PmmCaredMsg_.cmd;
        Loger(NORMAL,"Rly cmd:: load_desire = %d, load_excu = %d,bat_rly = %d",load_cmd,load_excu, PmmCaredMsg_.cmd);
    }

    // insult R rly
    uint32_t insultR = 
        eVSProto.eVSManageData.electriCtrlManageBuff.insultCtrl.insultPosRes;
    if(insultR != SwitchR){
        for(uint32_t idx = 0; idx < LERRY_LEN(c_switchMsg); ++idx){
            if(insultR == c_switchMsg[idx].R){
                if(c_switchMsg[idx].k5 == RLY_CLOSE){
                    RlyCmd_[RLY_RES_K5] = RLY_CLOSE;
                }else{
                    RlyCmd_[RLY_RES_K5] = RLY_OPEN;
                }
                // k6
                if(c_switchMsg[idx].k6 == RLY_CLOSE){
                   RlyCmd_[RLY_RES_K6] = RLY_CLOSE;
                }else{
                    RlyCmd_[RLY_RES_K6] = RLY_OPEN;
                }
                // K7
                if(c_switchMsg[idx].k7 == RLY_CLOSE){
                    RlyCmd_[RLY_RES_K7] = RLY_CLOSE;
                }else{
                    RlyCmd_[RLY_RES_K7] = RLY_OPEN;
                }
                break;
            }
        }
        Loger(NORMAL,"SwitchR = %d",insultR);
        SwitchR = insultR;
    }
}
/***************************************************************************
函数名:OutRlyManage
功能描述: 100ms/per
1 高压接触器K5 K6管理
作者:
日期:
****************************************************************************/
void EVSCar::RlyCtrl(void){
    if(memcmp(RlyCmdBak_,RlyCmd_,sizeof(RlyCmdBak_))){
        for(uint32_t idx=0; idx < LERRY_LEN(c_RlyIo);++idx){
            if(RlyCmd_[c_RlyIo[idx].rly] == RLY_CLOSE){
                GpioSet(c_RlyIo[idx].cmd);
            }else{
                GpioClr(c_RlyIo[idx].cmd);
            }
            if(RlyCmd_[idx] != RlyCmdBak_[idx]){
                Loger(NORMAL,"Rly ctrl:: rly = %d,cmd = %d",c_RlyIo[idx].rly,RlyCmd_[c_RlyIo[idx].rly]);
            }
        }
        memcpy(RlyCmdBak_,RlyCmd_,sizeof(RlyCmdBak_));
        
    }  
}
/***************************************************************************
函数名:RlyFaultDetect
功能描述: 100ms/per
作者:
日期:
****************************************************************************/
void EVSCar::RlyFaultDetect(void){
    for(uint32_t idx=0; idx < LERRY_LEN(c_RlyIo);++idx){
        if(c_RlyIo[idx].fbEnable == 0){
            continue;
        }
        RlyFb_[c_RlyIo[idx].rly] = GpioGet(c_RlyIo[idx].fb);

        if(RlyCmd_[idx] == RLY_CLOSE){
            if(RlyFb_[idx] != RLY_CLOSE){
                ++DiscntCnt_[idx];
            }else{
                DiscntCnt_[idx] = 0;
            }
        }
        if(RlyCmd_[idx] == RLY_OPEN){
            if(RlyFb_[idx] != RLY_OPEN){
                ++StickCnt_[idx];
            }else{
                StickCnt_[idx] = 0;
            }
        }

        if(DiscntCnt_[idx] > 30){
            RlyDiscnt_[idx] = 1;
        }else if(DiscntCnt_[idx] == 0){
            RlyDiscnt_[idx] = 0;
        }

        if(++StickCnt_[idx] > 30){
            RlyStick_[idx] = 1;
        }else if(StickCnt_[idx] == 0){
            RlyStick_[idx] = 0;
        }
    }  
}


/***************************************************************************
函数名:ElectControl
功能描述: 100ms/per
1.S2、S3、FAN电气控制
作者:
日期:
****************************************************************************/
void EVSCar::ElectControl()
{
    if(eVSProto.eVSManageData.electriCtrlManageBuff.electricCtrl.CC1_S2 == 0xAA) {
        RlyCmd_[RLY_S2] = RLY_CLOSE;
    } else {
        RlyCmd_[RLY_S2] = RLY_OPEN;
    }

    if(eVSProto.eVSManageData.electriCtrlManageBuff.electricCtrl.CC2_S3 == 0xAA) {
        RlyCmd_[RLY_S3] = RLY_CLOSE;
    } else {
        RlyCmd_[RLY_S3] = RLY_OPEN;
    }

    if(eVSProto.eVSManageData.electriCtrlManageBuff.electricCtrl.sysFan == 0x01) {
        RlyCmd_[RLY_FAN_CTRL] = RLY_CLOSE;
    } else {
        RlyCmd_[RLY_FAN_CTRL] = RLY_OPEN;
    }
}