/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * 设置数据的持久化管理，设置参数数据保存至Flash中
 */
#ifndef _EVS_STORAGE_H
#define _EVS_STORAGE_H
#include <string>
#include "proto/evs_proto.h"

typedef enum{
    T_UINT8 = 0,
    T_UINT16 = 1,
    T_UINT32 = 2,
    T_UINT64 = 3,
    T_INT8 = 4,
    T_INT16 = 5,
    T_INT32 = 6,
    T_INT64 = 7,
    T_FLOAT = 8,
    T_DOUBLE = 9,
    T_STRING = 10,
    T_TYPE_NUM
}FormatType;

typedef struct{
    uint32_t index;
    FormatType type;
    std::string name;
    uint8_t *pinData;
    uint8_t *poutData;
}JsonTypeDef;
extern int32_t JsonInit(void);
extern int32_t UserDataReset(void);
extern StorageParaDef GetUserData(void);
extern int32_t StorgeUserData(StorageParaDef data);


#endif  //_EVS_STORAGE_H