/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class loger Definition here.
 */
#include "loger.h"
#include "evs_storage.h"
#include <cstring>
#include "evs_common.h"
#include "json.hpp"
#include <iostream>
#include <fstream>

using json = nlohmann::ordered_json;//nlohmann::json;
#define JSON_PATH  "/mnt/EVS/evs.json";

static StorageParaDef ST_IN;
static StorageParaDef ST_OUT;

const JsonTypeDef cJsonType[]={
    // 版本协商信息
    {0,T_UINT32,"0 CAN类型",((uint8_t*)&(ST_IN.protoConferM.canType)),((uint8_t*)&(ST_OUT.protoConferM.canType))},
    {1,T_STRING,"1 车辆期望版本号",(uint8_t*)(&ST_IN.protoConferM.gbVersion),(uint8_t*)(&ST_OUT.protoConferM.gbVersion)},
    {2,T_UINT32,"2 车辆控制导引版本",((uint8_t*)&(ST_IN.protoConferM.guidanceVersion)),((uint8_t*)&(ST_OUT.protoConferM.guidanceVersion))},
    {3,T_UINT32,"3 车辆传输层版本",((uint8_t*)&(ST_IN.protoConferM.transportVersion)),((uint8_t*)&(ST_OUT.protoConferM.transportVersion))},
    {4,T_UINT32,"4 版本协商预留",((uint8_t*)&(ST_IN.protoConferM.conferRes)),((uint8_t*)&(ST_OUT.protoConferM.conferRes))},
    // 功能协商
    {5,T_UINT32,"5 配置功能FDC",((uint8_t*)&(ST_IN.funConferM.configFDC)),((uint8_t*)&(ST_OUT.funConferM.configFDC))},
    {6,T_UINT32,"6 鉴权功能FDC",(uint8_t*)(&ST_IN.funConferM.authenFDC),(uint8_t*)(&ST_OUT.funConferM.authenFDC)},
    {7,T_UINT32,"7 预约功能FDC",((uint8_t*)&(ST_IN.funConferM.appointFDC)),((uint8_t*)&(ST_OUT.funConferM.appointFDC))},
    {8,T_UINT32,"8 输出回路检测功能FDC",((uint8_t*)&(ST_IN.funConferM.selfCheckFDC)),((uint8_t*)&(ST_OUT.funConferM.selfCheckFDC))},
    {9,T_UINT32,"9 供电模式功能FDC",((uint8_t*)&(ST_IN.funConferM.powerSupplyFDC)),((uint8_t*)&(ST_OUT.funConferM.powerSupplyFDC))},
    {10,T_UINT32,"10 预充及能量传输功能FDC",((uint8_t*)&(ST_IN.funConferM.energyTransferFDC)),((uint8_t*)&(ST_OUT.funConferM.energyTransferFDC))},
    {11,T_UINT32,"11 结束功能FDC",((uint8_t*)&(ST_IN.funConferM.endFDC)),((uint8_t*)&(ST_OUT.funConferM.endFDC))},
    // 车辆充电参数配置
    {12,T_FLOAT,"12 最大允许充电电流",((uint8_t*)&(ST_IN.paraConfigM.currAllowMAX)),((uint8_t*)&(ST_OUT.paraConfigM.currAllowMAX))},
    {13,T_FLOAT,"13 最高允许充电电压",(uint8_t*)(&ST_IN.paraConfigM.voltAllowMAX),(uint8_t*)(&ST_OUT.paraConfigM.voltAllowMAX)},
    {14,T_FLOAT,"14 最高允许输入总能量",((uint8_t*)&(ST_IN.paraConfigM.energyAllowMAX)),((uint8_t*)&(ST_OUT.paraConfigM.energyAllowMAX))},
    {15,T_FLOAT,"15 车辆荷电状态",((uint8_t*)&(ST_IN.paraConfigM.nowSOC)),((uint8_t*)&(ST_OUT.paraConfigM.nowSOC))},
    {16,T_FLOAT,"16 电池最小并联单元最高允许电压",((uint8_t*)&(ST_IN.paraConfigM.cellVoltAllowMAX)),((uint8_t*)&(ST_OUT.paraConfigM.cellVoltAllowMAX))},
    {17,T_FLOAT,"17 电池单体最高允许温度",((uint8_t*)&(ST_IN.paraConfigM.batTempAllowMAX)),((uint8_t*)&(ST_OUT.paraConfigM.batTempAllowMAX))},
    {18,T_UINT32,"18 重新启动次数",((uint8_t*)&(ST_IN.paraConfigM.restarNum)),((uint8_t*)&(ST_OUT.paraConfigM.restarNum))},
    // 鉴权信息
    {19,T_UINT32,"19 总鉴权等待时间",((uint8_t*)&(ST_IN.authenM.authenWaitTime)),((uint8_t*)&(ST_OUT.authenM.authenWaitTime))},
    {20,T_STRING,"20 车辆识别码EVIN",(uint8_t*)(&ST_IN.authenM.eVIN),(uint8_t*)(&ST_OUT.authenM.eVIN)},
    {21,T_UINT32,"21 重新鉴权的FDC",((uint8_t*)&(ST_IN.authenM.nextFDC)),((uint8_t*)&(ST_OUT.authenM.nextFDC))},
    // 预约充电信息
    {22,T_UINT32,"22 车辆期望开始充电时间",((uint8_t*)&(ST_IN.reserveM.bmsDesireStartTime)),((uint8_t*)&(ST_OUT.reserveM.bmsDesireStartTime))},
    {23,T_UINT32,"23 车辆期望出发时间",(uint8_t*)(&ST_IN.reserveM.bmsDesireLeaveTime),(uint8_t*)(&ST_OUT.reserveM.bmsDesireLeaveTime)},
    {24,T_UINT32,"24 预约协商充电结果",((uint8_t*)&(ST_IN.reserveM.reserveResult)),((uint8_t*)&(ST_OUT.reserveM.reserveResult))},
    {25,T_UINT32,"25 立即充电支持",((uint8_t*)&(ST_IN.reserveM.immediateChargeSupport)),((uint8_t*)&(ST_OUT.reserveM.immediateChargeSupport))},
    // 供电模式
    {26,T_UINT32,"26 车辆供电状态",((uint8_t*)&(ST_IN.powerSupplyM.supplyState)),((uint8_t*)&(ST_OUT.powerSupplyM.supplyState))},
    {27,T_FLOAT,"27 供电电压需求",(uint8_t*)(&ST_IN.powerSupplyM.supplyVolDesire),(uint8_t*)(&ST_OUT.powerSupplyM.supplyVolDesire)},
    {28,T_FLOAT,"28 供电电流需求",((uint8_t*)&(ST_IN.powerSupplyM.supplyCurrDesire)),((uint8_t*)&(ST_OUT.powerSupplyM.supplyCurrDesire))},
    {29,T_UINT32,"29 供电模式结束请求",((uint8_t*)&(ST_IN.powerSupplyM.supplyEnd)),((uint8_t*)&(ST_OUT.powerSupplyM.supplyEnd))},
    // 充电阶段信息
    {30,T_UINT32,"30 BMS就绪状态",(uint8_t*)(&ST_IN.chargingM.bmsReady),(uint8_t*)(&ST_OUT.chargingM.bmsReady)},
    {31,T_FLOAT,"31 需求电压",((uint8_t*)&(ST_IN.chargingM.volDemand)),((uint8_t*)&(ST_OUT.chargingM.volDemand))},
    {32,T_FLOAT,"32 需求电流",((uint8_t*)&(ST_IN.chargingM.curDemand)),((uint8_t*)&(ST_OUT.chargingM.curDemand))},
    {33,T_UINT32,"33 充电模式",(uint8_t*)(&ST_IN.chargingM.chargeMode),(uint8_t*)(&ST_OUT.chargingM.chargeMode)},
    {34,T_FLOAT,"34 当前SOC",((uint8_t*)&(ST_IN.chargingM.socNow)),((uint8_t*)&(ST_OUT.chargingM.socNow))},
    {35,T_UINT32,"35 剩余估算时间",((uint8_t*)&(ST_IN.chargingM.resChgTime)),((uint8_t*)&(ST_OUT.chargingM.resChgTime))},
    {36,T_FLOAT,"36 单体电池最高电压",((uint8_t*)&(ST_IN.chargingM.cellBatVolMax)),((uint8_t*)&(ST_OUT.chargingM.cellBatVolMax))},
    {37,T_FLOAT,"37 单体电池最低电压",(uint8_t*)(&ST_IN.chargingM.cellBatVolMin),(uint8_t*)(&ST_OUT.chargingM.cellBatVolMin)},
    {38,T_FLOAT,"38 电池最高温度",((uint8_t*)&(ST_IN.chargingM.celltempMax)),((uint8_t*)&(ST_OUT.chargingM.celltempMax))},
    {39,T_FLOAT,"39 电池最低温度",((uint8_t*)&(ST_IN.chargingM.celltempMin)),((uint8_t*)&(ST_OUT.chargingM.celltempMin))},
    // 充电结束信息
    {40,T_UINT32,"40 中止类型",(uint8_t*)(&ST_IN.chargingEndM.endType),(uint8_t*)(&ST_OUT.chargingEndM.endType)},
    {41,T_UINT32,"41 中止码",((uint8_t*)&(ST_IN.chargingEndM.endCode)),((uint8_t*)&(ST_OUT.chargingEndM.endCode))},
    {42,T_UINT32,"42 中止原因",((uint8_t*)&(ST_IN.chargingEndM.endReason)),((uint8_t*)&(ST_OUT.chargingEndM.endReason))},
    {43,T_UINT32,"43 请求重连",(uint8_t*)(&ST_IN.chargingEndM.repeat),(uint8_t*)(&ST_OUT.chargingEndM.repeat)},
    {44,T_UINT32,"44 车辆粘连检测状态",((uint8_t*)&(ST_IN.chargingEndM.bmsStickCheckState)),((uint8_t*)&(ST_OUT.chargingEndM.bmsStickCheckState))},
    // IP信息配置
    {45,T_STRING,"45 本地ip地址",(uint8_t*)(&ST_IN.ipConfig.locolIP),(uint8_t*)(&ST_OUT.ipConfig.locolIP)},
    {46,T_UINT32,"46 本地端口号",((uint8_t*)&(ST_IN.ipConfig.locolPort)),((uint8_t*)&(ST_OUT.ipConfig.locolPort))},
    {47,T_STRING,"47 服务器ip地址",(uint8_t*)(&ST_IN.ipConfig.serverIP),(uint8_t*)(&ST_OUT.ipConfig.serverIP)},
    {48,T_UINT32,"48 服务器端口号",((uint8_t*)&(ST_IN.ipConfig.serverPort)),((uint8_t*)&(ST_OUT.ipConfig.serverPort))},

};

 
// 写入 JSON 文件
void write_json(const std::string& file_path, const json& j) {
    std::ofstream o(file_path);
    o << j.dump(4); // dump(4) 表示格式化输出，缩进4个空格
    o.close();
}
 
// 读取 JSON 文件
json read_json(const std::string& file_path) {
    std::ifstream i(file_path);
    json j;
    i >> j; // 从文件流中读取 JSON 数据
    return j;
}
// 存储参数
int32_t SavePara(void){
    json j_save;
    uint32_t idx=0;
    float f_temp;
    double d_temp;
    for(idx = 0; idx < LERRY_LEN(cJsonType); ++idx){
        switch(cJsonType[idx].type){
            case T_UINT8:
                j_save[cJsonType[idx].name] = *((uint8_t*)(cJsonType[idx].pinData));
                break;
            case T_UINT16:
                j_save[cJsonType[idx].name] = *((uint16_t*)(cJsonType[idx].pinData));
                break;
            case T_UINT32:
                j_save[cJsonType[idx].name] = *((uint32_t*)(cJsonType[idx].pinData));
                break;
            case T_INT8:
                j_save[cJsonType[idx].name] = *((int8_t*)(cJsonType[idx].pinData));
                break;
            case T_INT16:
                j_save[cJsonType[idx].name] = *((int16_t*)(cJsonType[idx].pinData));
                break;
            case T_INT32:
                j_save[cJsonType[idx].name] = *((int32_t*)(cJsonType[idx].pinData));
                break;
            case T_UINT64:
                j_save[cJsonType[idx].name] = *((uint64_t*)(cJsonType[idx].pinData));
                break;
            case T_INT64:
                j_save[cJsonType[idx].name] = *((int64_t*)(cJsonType[idx].pinData));
                break;
            case T_FLOAT:
                f_temp = *((float*)(cJsonType[idx].pinData));
                j_save[cJsonType[idx].name] = ((int32_t)((f_temp+0.0005)*1000));
                break;
            case T_DOUBLE:
                d_temp = *((double*)(cJsonType[idx].pinData));
                j_save[cJsonType[idx].name] = ((int64_t)((d_temp+0.0005)*1000));
            break;
            case T_STRING:
                j_save[cJsonType[idx].name] = *((std::string*)(cJsonType[idx].pinData));
            break;
            default: 
            break;
        }
    }
    std::cout<<"jeson_write = "<<j_save.dump(4)<<std::endl;
    // 写入文件
    const std::string file_path = JSON_PATH;
    write_json(file_path, j_save);
    return 0;
}
//读取参数
int32_t GetPara(void){
    // 读取文件
    const std::string file_path = JSON_PATH;
    json j_read = read_json(file_path);
    std::cout << j_read.dump(4) << std::endl; // 输出读取的 JSON 数据，格式化显示
    int64_t tmp = 0;
    for(uint32_t idx = 0; idx < LERRY_LEN(cJsonType); ++idx){
        switch(cJsonType[idx].type){
            case T_UINT8:
                *((uint8_t*)(cJsonType[idx].poutData)) = j_read[cJsonType[idx].name] ;
                break;
            case T_UINT16:
                *((uint16_t*)(cJsonType[idx].poutData)) = j_read[cJsonType[idx].name];
                break;
            case T_UINT32:
                *((uint32_t*)(cJsonType[idx].poutData)) = j_read[cJsonType[idx].name];
            break;
            case T_INT8:
                *((int8_t*)(cJsonType[idx].poutData)) = j_read[cJsonType[idx].name];
                break;
            case T_INT16:
                *((int16_t*)(cJsonType[idx].poutData)) = j_read[cJsonType[idx].name];
                break;
            case T_INT32:
                *((int32_t*)(cJsonType[idx].poutData)) = j_read[cJsonType[idx].name];
            break;
            case T_UINT64:
                *((uint64_t*)(cJsonType[idx].poutData)) = j_read[cJsonType[idx].name];
            break;
            case T_INT64:
                *((int64_t*)(cJsonType[idx].poutData)) = j_read[cJsonType[idx].name];
            break;
            case T_FLOAT:
                tmp = j_read[cJsonType[idx].name];
                *((float*)(cJsonType[idx].poutData)) 
                    = ((float)tmp)/1000;
            break;
            case T_DOUBLE:
                tmp = j_read[cJsonType[idx].name];
                *((double*)(cJsonType[idx].poutData)) 
                    = ((double)tmp)/1000;
            break;
            case T_STRING:
                *((std::string*)(cJsonType[idx].poutData)) = j_read[cJsonType[idx].name];
            break;
            default: break;
        }
    }
    std::cout<<"jeson_read = "<<j_read.dump(4)<<std::endl;
    return 0;
}

StorageParaDef GetUserData(void){
    GetPara();
    return ST_OUT;
}
int32_t StorgeUserData(StorageParaDef data){
    ST_IN = data;
    return SavePara();
}

int32_t UserDataReset(void){
    StorageParaDef indata;
    // EVProtoConferMsg
    indata.protoConferM.canType = 0x00;
    indata.protoConferM.gbVersion = "2.0.0";
    indata.protoConferM.guidanceVersion = 0x01;
    indata.protoConferM.transportVersion = 0x01;
    indata.protoConferM.conferRes = 0xFF;

    // EVFunConferMsg
    indata.funConferM.configFDC = 0x01;
    indata.funConferM.authenFDC = 0x01;
    indata.funConferM.appointFDC = 0x00;
    indata.funConferM.selfCheckFDC= 0x01;
    indata.funConferM.powerSupplyFDC= 0x01;
    indata.funConferM.energyTransferFDC = 0x01;
    indata.funConferM.endFDC= 0x01;

    //EVChgParaConfigMsg
    indata.paraConfigM.currAllowMAX = 100.0;
    indata.paraConfigM.voltAllowMAX = 200.0;
    indata.paraConfigM.energyAllowMAX = 100.0;
    indata.paraConfigM.nowSOC = 49.0;
    indata.paraConfigM.cellVoltAllowMAX = 3.81;
    indata.paraConfigM.batTempAllowMAX = 25;
    indata.paraConfigM. restarNum = 1;

    //EVAuthenMsg
    indata.authenM.authenWaitTime = 600;
    indata.authenM.eVIN = "XIANWANMAPRODUCE7";
    indata.authenM.nextFDC = 0;

    //EVReserveMsg
    indata.reserveM.bmsDesireStartTime = 0;
    indata.reserveM.bmsDesireLeaveTime = 0;
    indata.reserveM.reserveResult = 0xFF;
    indata.reserveM.immediateChargeSupport = 0x00;

    //EVPowerSupplyMsg
    indata.powerSupplyM.supplyState = 0x01;
    indata.powerSupplyM.supplyVolDesire = 300;
    indata.powerSupplyM.supplyCurrDesire = 100;
    indata.powerSupplyM.supplyEnd = 0x00;

    //EVChargingMsg
    indata.chargingM.bmsReady = 0xAA;
    indata.chargingM.volDemand = 450;
    indata.chargingM.curDemand = 200;
    indata.chargingM.chargeMode = 0x01;
    indata.chargingM.socNow = 49;
    indata.chargingM.resChgTime = 100;
    indata.chargingM.cellBatVolMax = 3.15;
    indata.chargingM.cellBatVolMin = 2.93;
    indata.chargingM.celltempMax = 44;
    indata.chargingM.celltempMin = 34;

    // EVChargingEndMsg
    indata.chargingEndM.endType = 0;
    indata.chargingEndM.endCode = 0;
    indata.chargingEndM.endReason = 0;
    indata.chargingEndM.repeat = 0;
    indata.chargingEndM.bmsStickCheckState = 0;

    //IPconfig
    indata.ipConfig.locolIP = EVS_LOCAL_IP;
    indata.ipConfig.locolPort = HMI_SOCKET_PORT;
    indata.ipConfig.serverIP = EVS_SOCKET_IP;
    indata.ipConfig.serverPort = EVS_SOCKET_PORT;
    return StorgeUserData(indata);
}

int32_t JsonInit(void){
    std::string filePath = JSON_PATH;
    std::ifstream file(filePath);
    if (file.good()) {
        std::cout << "json exist!" << std::endl;
    } else {
        std::cout << "json reset!" << std::endl;
        Loger(NORMAL,"UserDataReset by json\r\n");
        return UserDataReset();
    }
    return 0;
}
