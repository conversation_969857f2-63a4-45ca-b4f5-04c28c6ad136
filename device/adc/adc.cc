#include <cstring>
#include <string>
#include <iostream>
#include <fcntl.h>
#include <unistd.h>
#include "adc.h"
#include "loger.h"

#define FILTER(ik,iVar,iMean)    (((((int64_t)(iVar)<<32) - (iMean))>>(ik))+(iMean))
#define BAT_OFFSET  8579  //0.273*1.2/(2.5*65535)

uint16_t RT_AD_DATA[E_SAMPLE_CH_NUM];
FILE *pFd[E_SAMPLE_CH_NUM] = {0}; 
static int32_t  GUN_AD_DATA[AD_GUN_NUM][GUN_AD_TYPE_NUM]; //单枪各个通道采样寄存器值
ADReviseParaDef ADRevisepara[AD_GUN_NUM]; //AD校正参数
int32_t   F_AD_VALUE[AD_GUN_NUM][GUN_AD_TYPE_NUM] = {0};  //单枪各个通道采样真实值

typedef struct
{
    E_AD_Sampile_Index Index;
    char *str;
}ADC_FileStructDef;

const ADC_FileStructDef AD_DataStructer[] ={ 
    {E_CH0_GUNA_CC1,(char*)"/tmp/E_CH0_GUNA_CC1"},
    {E_CH1_GUNA_VF,(char*)"/tmp/E_CH1_GUNA_VF"},
    {E_CH2_GUNA_VOL,(char*)"/tmp/E_CH2_GUNA_VOL"},
    {E_CH3_GUNA_POS,(char*)"/tmp/E_CH3_GUNA_POS"},
    {E_CH4_GUNB_CC1,(char*)"/tmp/E_CH4_GUNB_CC1"},  
    {E_CH5_GUNB_VF,(char*)"/tmp/E_CH5_GUNB_VF"},
    {E_CH6_GUNB_VOL,(char*)"/tmp/E_CH6_GUNB_VOL"},
    {E_CH7_GUNB_POS,(char*)"/tmp/E_CH7_GUNB_POS"}, 
};

typedef struct
{
	int32_t	i32Type;//数据类型
	int32_t	i32ScalFactor;//定标参数
    int32_t	i32FiltCnst;//滤波常量
}_AnalogScaleParaDef;

const _AnalogScaleParaDef aScaleParamTbl[GUN_AD_TYPE_NUM] =
{
    {ADC_CC1,	    135,		1},	// CC1 电压
    {ADC_BAT,		13910,		4},	// 电池电压
    {ADC_POS,		13910,		4}, // 绝缘检测正极电压
    {ADC_VF,		13910,		4}	// 输出接触器前级电压
};
/**
 * @brief AD采样初始化
 * @note  打开每个AD通道的文件
 */
void ADCDevInit(void)
{
    uint8_t i;
    for(i=0; i<E_SAMPLE_CH_NUM; i++)
    {
	    pFd[i] = fopen(AD_DataStructer[i].str, "r");

	    if(pFd[i] == nullptr)
        {
            Loger(NORMAL,"%sfd = %d",AD_DataStructer[i].str,pFd[i]);
        }else{
            fclose(pFd[i]);
        }
    }
    //注意，这里aK[] 和 aOffset[]需要存储到Flash中
    for(uint32_t gun = 0; gun < AD_GUN_NUM; ++gun)
    {
        for (uint16_t i = 0;i < AD_GUN_NUM;i++)
        {
            ADRevisepara[gun].aK[i] = 1024;
            ADRevisepara[gun].aOffset[i] = 0;
        }

        ADRevisepara[gun].aOffset[ADC_VF] = 0;	
        ADRevisepara[gun].aOffset[ADC_BAT] = 0;//-48;
        ADRevisepara[gun].aOffset[ADC_POS] = 0;
    }
}
/**
 * @brief 读取每一个管道的AD采样原始值
 * @note  底层读取每个通道的AD原始值，并做转换
 */
void RT_ReadADValue(uint32_t index){
    uint32_t i = index;
    int ret = 0;
    char tempBuf[64] = {0};
    errno = 0;
    pFd[i] = fopen(AD_DataStructer[i].str, "r");
    if(i > E_SAMPLE_CH_NUM || pFd[i] == nullptr){
        if(pFd[i] == nullptr){
            Loger(NORMAL,"File open failed! errno = %d,strerror = %s",errno,strerror(errno));
        }
        Loger(NORMAL,"FastReadADValue index error %d",i);
        return;
    }
    
    memset(tempBuf,0,sizeof(tempBuf));
    ret = fscanf(pFd[i], "%s",tempBuf); 
    if(ret>0){
        fseek(pFd[i], 0, SEEK_SET);
        RT_AD_DATA[i] = atoi(tempBuf);    
    }
    fclose(pFd[i]);
}

/**
 * @brief 单枪AD数据刷新
 * 
 * @param  
 * @return int8_t 
 */
static UNLONG AD_GUN_VOL[AD_GUN_NUM][GUN_AD_TYPE_NUM] = {0};
// static void RT_GunADDataUpdate(void)
// {
//     for(uint8_t gun = 0; gun< AD_GUN_NUM; ++gun)
//     {
//         GUN_AD_DATA[gun][ADC_VF] = AD_GUN_VOL[gun][ADC_VF].half.hword;
//         GUN_AD_DATA[gun][ADC_BAT] = AD_GUN_VOL[gun][ADC_BAT].half.hword;
//         GUN_AD_DATA[gun][ADC_POS] = AD_GUN_VOL[gun][ADC_POS].half.hword;
//     }
// }
/**
 * @brief 单枪AD数据刷新
 * 
 * @param  
 * @return int8_t 
 */
static void GunADDataUpdate(void)
{
    for(uint8_t gun = 0; gun< AD_GUN_NUM; ++gun)
    {
        GUN_AD_DATA[gun][ADC_CC1] = AD_GUN_VOL[gun][ADC_CC1].half.hword;
        GUN_AD_DATA[gun][ADC_VF] = AD_GUN_VOL[gun][ADC_VF].half.hword;
        GUN_AD_DATA[gun][ADC_BAT] = AD_GUN_VOL[gun][ADC_BAT].half.hword;
        GUN_AD_DATA[gun][ADC_POS] = AD_GUN_VOL[gun][ADC_POS].half.hword;   
    }
}
/**
 * @brief 数据标定：AD寄存器值 -> 真实值
 * 
 * @param  
 * @return int8_t 
 */
static void DataScale(void)
{
    int16_t i;
	int64_t temp = 0;
    for(uint8_t gun = 0; gun< AD_GUN_NUM; ++gun)
    {
        for (i = 0;i < AD_GUN_NUM;i++)
        {
            GUN_AD_DATA[gun][i] = GUN_AD_DATA[gun][i] << 4;
            //定标
            if(i==ADC_BAT)
            {
                int64_t data = GUN_AD_DATA[gun][i];
                temp = (((int64_t)(aScaleParamTbl[i].i32ScalFactor)) * (data - BAT_OFFSET))>>16;
            }
            else
            {
                temp = (((int64_t)(aScaleParamTbl[i].i32ScalFactor)) * GUN_AD_DATA[gun][i])>>16;
            }
            //校正
            temp = (((int64_t)ADRevisepara[gun].aK[i]) * temp)>>10;//比例增益校正
            temp += ADRevisepara[gun].aOffset[i];//零点偏置校正

            //瞬时值
            F_AD_VALUE[gun][i] = temp;
        }
    }
}

static uint32_t cycle = 0;
int32_t FreshGunADData(void)
{
    ++cycle;
    cycle &= 0x7;
    RT_ReadADValue(cycle);
    switch(cycle){
        case 0:
            AD_GUN_VOL[A_GUN0][ADC_CC1].dword =
                 FILTER(1,RT_AD_DATA[E_CH0_GUNA_CC1],AD_GUN_VOL[A_GUN0][ADC_CC1].dword);
        break;
        case 1:
            AD_GUN_VOL[A_GUN0][ADC_VF].dword =
                 FILTER(2,RT_AD_DATA[E_CH1_GUNA_VF],AD_GUN_VOL[A_GUN0][ADC_VF].dword);
        break;
        case 2:
            AD_GUN_VOL[A_GUN0][ADC_BAT].dword =
                 FILTER(2,RT_AD_DATA[E_CH2_GUNA_VOL],AD_GUN_VOL[A_GUN0][ADC_BAT].dword);
        break;
        case 3:
            AD_GUN_VOL[A_GUN0][ADC_POS].dword = 
                FILTER(1,RT_AD_DATA[E_CH3_GUNA_POS],AD_GUN_VOL[A_GUN0][ADC_POS].dword);    
        break;
        case 4:
            AD_GUN_VOL[B_GUN1][ADC_CC1].dword =
                 FILTER(1,RT_AD_DATA[E_CH4_GUNB_CC1],AD_GUN_VOL[B_GUN1][ADC_CC1].dword);
        break;
        case 5:
            AD_GUN_VOL[B_GUN1][ADC_VF].dword =
                 FILTER(2,RT_AD_DATA[E_CH5_GUNB_VF],AD_GUN_VOL[B_GUN1][ADC_VF].dword);
        break; 
        case 6:
            AD_GUN_VOL[B_GUN1][ADC_BAT].dword =
                 FILTER(2,RT_AD_DATA[E_CH6_GUNB_VOL],AD_GUN_VOL[B_GUN1][ADC_BAT].dword);
        break;
        case 7:
            AD_GUN_VOL[B_GUN1][ADC_POS].dword =
                 FILTER(1,RT_AD_DATA[E_CH7_GUNB_POS],AD_GUN_VOL[B_GUN1][ADC_POS].dword);  
        break;
        default:
        break;
    }
    GunADDataUpdate();
    DataScale();
    return 0;
}





