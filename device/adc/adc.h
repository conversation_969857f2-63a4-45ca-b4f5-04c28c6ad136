#ifndef _ADC_H
#define _ADC_H

typedef enum
{  
    E_CH0_GUNA_CC1   = 0,
    E_CH1_GUNA_VF    = 1,
    E_CH2_GUNA_VOL   = 2,
    E_CH3_GUNA_POS   = 3,
    E_CH4_GUNB_CC1   = 4,  
    E_CH5_GUNB_VF    = 5, 
    E_CH6_GUNB_VOL   = 6,
    E_CH7_GUNB_POS   = 7,  
    E_SAMPLE_CH_NUM
}E_AD_Sampile_Index;

typedef enum
{
	ADC_CC1 = 0,
	ADC_BAT = 1,
	ADC_POS = 2,
	ADC_VF = 3,
    GUN_AD_TYPE_NUM
}ADCType;

typedef enum
{
	A_GUN0 = 0,
	B_GUN1 = 1,
    AD_GUN_NUM
}GunType;

typedef union
{
	int64_t	dword;
	struct
	{
		uint32_t lword;
		int32_t hword;
	}half;
}UNLONG;
typedef struct
{
	int16_t    aK[GUN_AD_TYPE_NUM];//校正比例参数
	int16_t    aOffset[GUN_AD_TYPE_NUM];//校正偏置
}ADReviseParaDef;

void ADCDevInit(void);
void RT_ReadADValue(uint32_t index);
int32_t FreshGunADData(void);
extern uint16_t RT_AD_DATA[E_SAMPLE_CH_NUM];
extern int32_t  F_AD_VALUE[AD_GUN_NUM][GUN_AD_TYPE_NUM];

#endif

