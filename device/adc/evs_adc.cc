/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class loger Definition here.
 */
#include "loger.h"
#include "adc.h"
#include "evs_adc.h"

// typedef struct
// {
// 	int32_t	i32Type;//数据类型
// 	int32_t	i32ScalFactor;//定标参数
//     int32_t	i32FiltCnst;//滤波常量
// }_AnalogScaleParaDef;

// const _AnalogScaleParaDef aScaleParamTbl[GUN_AD_TYPE_NUM] =
// {
//     {ADC_CC1,	    135,		1},	// CC1 电压
//     {ADC_BAT,		13910,		4},	// 电池电压
//     {ADC_POS,		13910,		4}, // 绝缘检测正极电压
//     {ADC_VF,		13910,		4}	// 输出接触器前级电压
// };

EVSAdc::EVSAdc(){
  
}

EVSAdc::~EVSAdc() {}
int32_t EVSAdc::AdcInit(void){
    ADCDevInit();
    return 0;
}

int32_t EVSAdc::AdcProcess(void){
    int32_t ret = -1;
    if(++thisCnt%1000 == 0){
        /*
        Loger(NORMAL, "AdcProcess =  %d, %d", F_AD_VALUE[0][E_CH0_GUNA_CC1],F_AD_VALUE[0][E_CH1_GUNA_VF]);
        Loger(NORMAL, "ADC CC1 %d CC2 %d VF %d VOL %d POS %d %d-%d-%d", F_AD_VALUE[0][E_CH0_GUNA_CC1], F_AD_VALUE[1][E_CH0_GUNA_CC1], \
            F_AD_VALUE[0][E_CH1_GUNA_VF], F_AD_VALUE[0][E_CH2_GUNA_VOL], F_AD_VALUE[0][E_CH3_GUNA_POS], \
            F_AD_VALUE[1][E_CH1_GUNA_VF], F_AD_VALUE[1][E_CH2_GUNA_VOL], F_AD_VALUE[1][E_CH3_GUNA_POS]); 
        */
    }
    FreshGunADData();
    return ret;
}