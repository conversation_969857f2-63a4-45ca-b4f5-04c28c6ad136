/* Copyright 2021, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * common functions source files.
 */
#include <net/if.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <linux/can.h>
#include <linux/can/raw.h>
#include <cstring>
#include <iostream>
#include <fcntl.h>
#include "can_socket.h"
#include "evs_common.h"
#include "loger.h"

int32_t CanSocket::CanInit(string can_name){
    
    int32_t flags;
    int cansocket = -1;
    struct sockaddr_can addr;
    struct ifreq ifr;

    if(can_name.empty()) {
         Loger(NORMAL,"Create Comm To BMS Err, can name is null");
         return cansocket;
    }
    CanSocketClose();


    CanName = can_name;
    Loger(NORMAL,"Create Comm To BMS");
    cansocket = socket(PF_CAN, SOCK_RAW, CAN_RAW);//建立 SocketCAN 套接字
    
    strcpy(ifr.ifr_name, can_name.c_str());
    ioctl(cansocket, SIOCGIFINDEX, &ifr);//指定 can 设备
    flags = fcntl(cansocket, F_GETFL, 0);  //获取文件的flags值。
    fcntl(cansocket, F_SETFL, flags | O_NONBLOCK);   //设置成非阻塞模式；
    addr.can_family = AF_CAN;
    addr.can_ifindex = ifr.ifr_ifindex;
    bind(cansocket, (struct sockaddr *)&addr, sizeof(addr)); //将套接字与 can 绑定
    Loger(NORMAL,"cansocket::%d--%s",cansocket,can_name.c_str());
    CANSocket = cansocket;
    CanEnable = 1;
    return cansocket;
}

int32_t CanSocket::CanReset(void){
    int32_t ret = -1;
    char str[25]={0};
    CanEnable = 0;
    // if(CANSocket >0){
    //     close(CANSocket);
    //     Loger(NORMAL,"%s close ,socket = %d",CanName.c_str(),CANSocket);
    // }
    sprintf(str,"ip link set down %s",CanName.c_str());
    system(str);
    Loger(NORMAL,"system cmd: ip link set down %s ,cansocket = %d",CanName.c_str(),CANSocket);
    sprintf(str,"ip link set up %s",CanName.c_str());
    ret = system(str);
    Loger(NORMAL,"system cmd: ip link set up %s ,cansocket = %d",CanName.c_str(),CANSocket);
    CanInit(CanName);
    CanEnable = 1;
    return ret;
}

int32_t CanSocket::CanSocketClose(void){
    int32_t ret = -1;
    CanEnable = 0;
    if(CANSocket > 0){
        ret = close(CANSocket);
        Loger(NORMAL,"%s close success,socket = %d",CanName.c_str(),CANSocket);
        return ret; 
    }
    Loger(NORMAL,"%s close fail,socket = %d",CanName.c_str(),CANSocket);
    return ret;
}

int32_t CanSocket::CanSend(void* data,uint8_t len){
    if(data == nullptr || CANSocket == -1){
        Loger(NORMAL,"%s send fail, socket = %d",CanName.c_str(),CANSocket);
        return -1;
    } 
    if(CanEnable == 0){
        return -1;
    }
    return writen(CANSocket, data, len);
}
int32_t CanSocket::CanRecv(void* data,uint8_t len){
    if(data == nullptr || CANSocket == -1){
        Loger(NORMAL,"%s recv fail, socket = %d",CanName.c_str(),CANSocket);
        return -1;
    } 
    if(CanEnable == 0){
        return -1;
    }
    return readn(CANSocket, data, len);
}

int32_t CanSocket::ClearRecvRegister(void){
    int32_t ret = 0;
    uint32_t cnt = 0;
    can_frame can_data;
    while(readn(CANSocket, &can_data, sizeof(can_data)) > 0){
        if(++cnt > 100){
           ret = -1;
           Loger(NORMAL,"%s clear recv register fail cnt = %d, socket = %d",CanName.c_str(),CANSocket,cnt);
           break;
        }
    }
    return ret;
}