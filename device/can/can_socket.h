/* Copyright 2021, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class 
 */
#ifndef _CAN_SOCKET_H
#define _CAN_SOCKET_H

#include <linux/can.h>

using namespace std;

#define	MAC_ID_BMS					0xF4//244
#define	MAC_ID_CHARGER				0x56//86
#define	MAC_BROADCAST				0xff//86
#define MAC_ID_GUN0                 0xb5
//CAN数据格式转换
typedef struct
{
	uint32_t bSourceAddr	:8;//源地址
	uint32_t bPS			:8;//目的地址
	uint32_t bPF			:8;
	uint32_t bDataPage		:1;
	uint32_t bR				:1;
	uint32_t bPriority		:3;
	uint32_t bERR           :1;//扩展帧
	uint32_t bRTR           :1;//扩展帧
	uint32_t bEFF           :1;//扩展帧
}_BMSExtFrameIdDef;

// CAN Frame
typedef union
{
	struct can_frame Frame;
	struct
	{
		_BMSExtFrameIdDef	ExtId;
		uint8_t 			DLC;
		uint8_t             Resv[3];
		uint8_t				MsgData[8];
	}PackedMsg;
}_CanBMSProtocolDef;

typedef struct
{
	uint16_t  u16PGN;
	uint16_t  u16Priority;
	uint16_t  u16DataLen;
	uint16_t  u16MsgSendPeriod;
	uint16_t  u16DestId;
	uint16_t  u16MACId;
	uint16_t  u16XmitActive;
	//uint16_t  RECVFresh;
	uint8_t*   pDataPtr;
}_CanBMSAppMsgDef;
//BMS 交互
class CanSocket {
    private:
		string  CanName; 
		int32_t CANSocket;
		int32_t CanEnable = 0;
		
    public:

		int32_t CanInit(string can_name);

		int32_t CanReset(void);
		int32_t CanSocketClose(void);
		int32_t ClearRecvRegister(void);//清空 CAN接收队列

		int32_t CanSend(void* data,uint8_t len);
		int32_t CanRecv(void* data,uint8_t len);
};
#endif //_CAN_SOCKET_H