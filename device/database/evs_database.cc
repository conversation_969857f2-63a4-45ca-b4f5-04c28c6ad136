/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class loger Definition here.
 */
#include "loger.h"
#include "evs_database.h"
#include "sqlite3.h"
#include <cstring>
#include "evs_common.h"

EVSDatabase::EVSDatabase(){
  
}

EVSDatabase::~EVSDatabase() {}

int32_t EVSDatabase::DataBaseInit(void){
    sqlite3 *ppdb;
    int32_t ret = sqlite3_open(EVS_DB_NAME,&ppdb);
    if(ret != SQLITE_OK){
        Loger(NORMAL,"sqlite3_open Failed -- %s", sqlite3_errmsg(ppdb));
    }else{
        const char *sqltCmd = "create table evs (id integer, sttime BIGINT, data txt);";
        ret = sqlite3_exec(ppdb, sqltCmd, NULL, NULL, NULL);
        if(ret != SQLITE_OK)
        {
            Loger(NORMAL,"sqlite3_exec1 Failed! -- %s", sqlite3_errmsg(ppdb));
        }
        Loger(NORMAL,"sqlite %s create !", EVS_DB_NAME);
    }
    
    ret = sqlite3_close(ppdb);
	if(ret != SQLITE_OK)
	{
		Loger(NORMAL,"Sqlite Release Failed -- %s",sqlite3_errmsg(ppdb));
	}
    return ret;
}
/***************************************************************************
函数名:DataSave
功能描述:
作者:
日期:
****************************************************************************/
int32_t EVSDatabase::DataSave(EvsDataDef data){
   if(SelectTable(data) == 1){
       return UpdateTable(data);
   }else{
       return InsetTable(data);
   }
}
/***************************************************************************
函数名:SelectTable
功能描述:查询列表 SQLITE_ROW is exist
作者:
日期:
****************************************************************************/
int32_t EVSDatabase::SelectTable(EvsDataDef data){
    sqlite3 *ppdb;
    int32_t ret_exist = 0;
	int ret = sqlite3_open(EVS_DB_NAME,&ppdb);
	if(ret != SQLITE_OK){
		Loger(NORMAL,"sqlite3_open Failed -- %s", sqlite3_errmsg(ppdb));
        return ret;
	}

    sqlite3_stmt *pstmt;
    std::string sql = "select id from evs where id=? limit 1;";

    ret = sqlite3_prepare_v2(ppdb,sql.c_str(),-1,&pstmt,NULL);
    if (ret != SQLITE_OK) {
        // 错误处理
        sqlite3_finalize(pstmt);
        return ret;
    }
    ret = sqlite3_bind_int(pstmt,1,data.id);
    if (ret != SQLITE_OK) {
        // 错误处理
        sqlite3_finalize(pstmt);
        return ret;
    }
    // 执行更新 return line SQLITE_ROW
    if(SQLITE_ROW == sqlite3_step(pstmt)){
        ret_exist = 1;
    }
    sqlite3_reset(pstmt);
    sqlite3_finalize(pstmt);
    ret = sqlite3_close(ppdb);
    if(ret != SQLITE_OK)
    {
        Loger(NORMAL,"Sqlite Release Failed -- %s",sqlite3_errmsg(ppdb));
    }
    return ret_exist;
}
/***************************************************************************
函数名:InsetTable
功能描述:插入列表
作者:
日期:
****************************************************************************/
int32_t EVSDatabase::InsetTable(EvsDataDef data){
    sqlite3 *ppdb;
	int ret = sqlite3_open(EVS_DB_NAME,&ppdb);
	if(ret != SQLITE_OK){
		Loger(NORMAL,"sqlite3_open Failed -- %s", sqlite3_errmsg(ppdb));
        return ret;
	}

    sqlite3_stmt *pstmt;
    std::string sql = "insert into evs (id, sttime, data) values(?,?,?);";
    ret = sqlite3_prepare_v2(ppdb,sql.c_str(),-1,&pstmt,NULL);
    if (ret != SQLITE_OK) {
        // 错误处理
        sqlite3_finalize(pstmt);
        return ret;
    }
    sqlite3_bind_int(pstmt,1,data.id);
    sqlite3_bind_int64(pstmt,2,data.date);
    // uint64_t t = 1730180144947;
     std::cout<<"InsetTable data =" << "::"<<data.date<<std::endl;
    // sqlite3_bind_int64(pstmt,2,t);
    
    ret = sqlite3_bind_text(pstmt,3,(const char*)data.data,data.len,NULL);
    if (ret != SQLITE_OK) {
        // 错误处理
        sqlite3_finalize(pstmt);
        return ret;
    }
    
    sqlite3_step(pstmt);
    //sqlite3_reset(pstmt);
    sqlite3_finalize(pstmt);

    ret = sqlite3_close(ppdb);
	if(ret != SQLITE_OK)
	{
		Loger(NORMAL,"Sqlite Release Failed -- %s",sqlite3_errmsg(ppdb));
	}
    return ret;
}
/***************************************************************************
函数名:UpdateTable
功能描述:刷新列表
作者:
日期:
****************************************************************************/
int32_t EVSDatabase::UpdateTable(EvsDataDef data){
    sqlite3 *ppdb;
	int ret = sqlite3_open(EVS_DB_NAME,&ppdb);
	if(ret != SQLITE_OK){
		Loger(NORMAL,"sqlite3_open Failed -- %s", sqlite3_errmsg(ppdb));
        return ret;
	}
    sqlite3_stmt *pstmt;
    std::string sql = "update evs set data = ? WHERE id = ?;";
    ret = sqlite3_prepare_v2(ppdb,sql.c_str(),-1,&pstmt,NULL);
    if (ret != SQLITE_OK) {
        // 错误处理
        sqlite3_finalize(pstmt);
        return ret;
    }
    ret = sqlite3_bind_text(pstmt,1,(const char*)data.data,data.len,SQLITE_TRANSIENT);
    ret = sqlite3_bind_int(pstmt,2,data.id);
    if (ret != SQLITE_OK) {
        // 错误处理
        sqlite3_finalize(pstmt);
        return ret;
    }
    
    sqlite3_step(pstmt);
    //sqlite3_reset(pstmt);
    sqlite3_finalize(pstmt);

    ret = sqlite3_close(ppdb);
	if(ret != SQLITE_OK)
	{
		Loger(NORMAL,"Sqlite Release Failed -- %s",sqlite3_errmsg(ppdb));
	}
    return ret;
}
//callback
int callback(void *para,int argc,char **argv,char **argv_name){
    if(argc == 1){
        std::cout<<"GetData callback len ="<< sizeof(argv[0])<<",data = "<<argv[0]<<std::endl;
        std::string str = argv[0];
        memcpy(para,str.c_str(),str.length());
        printf("GetData callback len = %d, data = %s",str.length(),str.c_str());
    }
    return 0;
}
int32_t EVSDatabase::GetData(uint8_t* data,uint32_t len){
    sqlite3 *ppdb;
    int32_t ret = sqlite3_open(EVS_DB_NAME,&ppdb);
    if(ret != SQLITE_OK){
        Loger(NORMAL,"sqlite3_open Failed -- %s", sqlite3_errmsg(ppdb));
    }else{
        char sqltCmd[128] ;
        snprintf(sqltCmd,sizeof(sqltCmd),"select data from evs limit 1;");
        ret = sqlite3_exec(ppdb, sqltCmd, callback, data, NULL);
        if(ret != SQLITE_OK)
        {
            Loger(NORMAL,"sqlite3_exec GetData Failed! -- %s", sqlite3_errmsg(ppdb));
        }
        Loger(NORMAL,"sqlite %s GetData ok!", EVS_DB_NAME);
    }
    
    ret = sqlite3_close(ppdb);
	if(ret != SQLITE_OK)
	{
		Loger(NORMAL,"Sqlite Release Failed -- %s",sqlite3_errmsg(ppdb));
	}
    return ret;
}

