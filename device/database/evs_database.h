/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * 设置数据的持久化管理，设置参数数据保存至Flash中
 */
#ifndef _EVS_DATABASE_H
#define _EVS_DATABASE_H
#include <string>


#define EVS_DB_NAME    "evs.db"
// demo
#pragma pack(1)
typedef struct{
    uint32_t id;
    uint64_t date;
    uint32_t len;
    uint8_t* data;
}EvsDataDef;
#pragma pack()

class EVSDatabase {
public:
    EVSDatabase();
    ~EVSDatabase();
private:


public:
    int32_t DataBaseInit(void);
    int32_t SelectTable(EvsDataDef data);
    int32_t InsetTable(EvsDataDef data);
    int32_t UpdateTable(EvsDataDef data);
    int32_t DataSave(EvsDataDef data); // 保存数据
    int32_t GetData(uint8_t* data,uint32_t len); // 获取数据
};


#endif  //_EVS_DATABASE_H