#include "gpio_ctrl.h"
#include <sys/ioctl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <fcntl.h>
#include <unistd.h>
#include <string.h>
#include <dirent.h>
#include <fstream>
#include "loger.h"
#include "evs_common.h"
#define   FINISH                 "finish"
#define   FINISH_SZIE       (strlen(FINISH))
#define   TIMEOUT_MS     1000
#define   OPEN_ERR       "ERROR:open"
#define   WRITE_ERR      "ERROR:write"
#define   READ_ERR       "ERROR:read!=write"
static uint32_t GpioErrCnt = 0;
uint32_t GetGpioErrCnt(void){return GpioErrCnt;};

GpioConfigDef c_GpioConfig[] =
{
    // DO
    {GPIO_OUT_K1K2,      "/sys/class/gpio/gpio371",     "out",  "low"},//  
    {GPIO_BAT_K3K4,      "/sys/class/gpio/gpio372",     "out",  "low"},//  
    {GPIO_RES_K5,        "/sys/class/gpio/gpio373",     "out",  "low"},//  
    {GPIO_RES_K6,        "/sys/class/gpio/gpio370",     "out",  "low"},//  
    {GPIO_RES_K7,        "/sys/class/gpio/gpio369",     "out",  "low"},//  
    {GPIO_INSULT_KP,     "/sys/class/gpio/gpio397",     "out",  "low"},//  INS_A_P
    {GPIO_INSULT_KN,     "/sys/class/gpio/gpio398",     "out",  "low"},//  INS_A_N
    {GPIO_INSULT_KPE,    "/sys/class/gpio/gpio399",     "out",  "low"},//  INS_A_PE
  	{GPIO_S2,            "/sys/class/gpio/gpio400",     "out",  "high"},//  
	{GPIO_S3,            "/sys/class/gpio/gpio402",     "out",  "high"},//  
	{GPIO_FAN_CTRL,      "/sys/class/gpio/gpio409",     "out",  "low"},//  
    {GPIO_DO_RELAY_ADC,  "/sys/class/gpio/gpio395",     "out",  "high"},// 
    //DI
    {GPIO_OUT_K1_FB,      "/sys/class/gpio/gpio110",    "in",  "low"}, //    
    {GPIO_OUT_K2_FB,      "/sys/class/gpio/gpio109",    "in",  "low"},//  
	{GPIO_BAT_K3_FB,      "/sys/class/gpio/gpio139",    "in",  "low"},//  
	{GPIO_BAT_K4_FB,      "/sys/class/gpio/gpio116",    "in",  "low"},//  
	{GPIO_RES_K5_FB,      "/sys/class/gpio/gpio140",    "in",  "low"},//  
    {GPIO_RES_K6_FB,      "/sys/class/gpio/gpio117",    "in",  "low"},//  
    {GPIO_RES_K7_FB,      "/sys/class/gpio/gpio143",    "in",  "low"},//  
};

/**
 * @brief 设置GPIO输出电平
 * 
 * @param GPIO_Index GPIO控制索引
 * @param Value GPIO输出电平
 * @return int8_t -1 设置失败，0 设置成功
 * @note 设置电平Value取值 0，1
 */
int8_t Get_GPIO_direction(GpioIndex index,string &str)
{
    ssize_t ret;
    int32_t gpio_fd;
    string path_buff;
    char value_buff[5] ={0};
    extern int errno;
    errno = 0;
    if(index >= GPIO_INDEX_MAX)
    {
        return -1;
    }
    path_buff = c_GpioConfig[index].GPIO_Name + "/direction";
    gpio_fd = open(path_buff.c_str(),O_RDONLY);
    if(gpio_fd == -1){
        Loger(NORMAL,"open direction filed! errno = %d,strerror = %s",errno,strerror(errno));
        return -1;
    }
    
    ret = readn(gpio_fd,value_buff,3);
    if(ret == -1){
        Loger(NORMAL,"read direction filed! errno = %d,strerror = %s",errno,strerror(errno));
    }
    str = value_buff;
    close(gpio_fd);
    return 0;
}
int8_t GPIO_Init(GpioIndex index)
{
    int64_t ret;
    //int32_t gpio_fd,export_fd,close_fd;
    int32_t gpio_fd,close_fd;
    string path_buff;
    string direction_buff;
    extern int errno;
    errno = 0;
    if(index >= GPIO_INDEX_MAX){
        Loger(NORMAL,"[GPIO_Init] index = %d",index);
        return -1;
    }
    Loger(NORMAL," GPIO_Init index = %d",index);

    //设置GPIO的输入输出方向
    path_buff = c_GpioConfig[index].GPIO_Name + "/direction";
    gpio_fd = open(path_buff.c_str(),O_WRONLY); 
    if(gpio_fd == -1){
        Loger(NORMAL,"%d :open direction file! errno = %d,strerror = %s",index,errno,strerror(errno));
        return -1;
    }
    ret = writen(gpio_fd,c_GpioConfig[index].GPIO_Direction.c_str(),c_GpioConfig[index].GPIO_Direction.length());
    if(ret != static_cast<int64_t>(c_GpioConfig[index].GPIO_Direction.length())){
        Loger(NORMAL,"%swrite direction file!",c_GpioConfig[index].GPIO_Name.c_str());
    }
    close_fd = close(gpio_fd);
    if(close_fd == -1){
        Loger(NORMAL,"close write file!%d-%d",index,gpio_fd);
    }
    return ret;
}
/*
static void WriteBlockLog(uint64_t starttime, uint32_t index, uint32_t level, string errbuf){
    uint32_t len = (150+strlen(c_GpioConfig[index].GPIO_Name.c_str()));
    char tmpBuf[len]={0}; 
    struct timeval ednow;
    struct ::tm timeInfo;
    time_t rawTime;
    uint64_t sec = 0,usec = 0,endTimeStamp = 0, intervalms = 0;
    gettimeofday(&ednow, NULL);
    rawTime = ednow.tv_sec;
    localtime_r(&rawTime, &timeInfo); 
    sec = ednow.tv_sec;
    usec = ednow.tv_usec;
    endTimeStamp = sec * 1000 + usec / 1000;
    intervalms = endTimeStamp - starttime;
    if((intervalms >= TIMEOUT_MS) || (strlen(errbuf.c_str()) > FINISH_SZIE)){
        ofstream writefile("/mnt/logs/GpioWriteBlockLog.txt",ofstream::app);
        if(!writefile.is_open()){
            Loger(NORMAL,"open file GpioWriteBlockLog.txt filed!");
            return;
        }
        sprintf(tmpBuf,"Record time[%d-%d-%d %02d:%02d:%02d.%03lu] interva:%llums Ctrl_Index:%u GPIO_name:%s level:%u %s", \
        (timeInfo.tm_year + 1900), timeInfo.tm_mon+1, timeInfo.tm_mday,timeInfo.tm_hour, timeInfo.tm_min, timeInfo.tm_sec, ednow.tv_usec, \
        intervalms,(uint32_t)index, c_GpioConfig[index].GPIO_Name.c_str(), level, errbuf.c_str());          
        writefile<<tmpBuf;
        writefile<<std::endl;
        writefile.close();
    }
}
*/
/**
 * @brief 设置GPIO输出电平
 * 
 * @param GPIO_Index GPIO控制索引
 * @param Value GPIO输出电平
 * @return int8_t -1 设置失败，0 设置成功
 * @note 设置电平Value取值 0，1
 */
int8_t GpioSet(GpioIndex index,uint8_t value)
{
    int64_t ret;
    int32_t gpio_fd,close_fd;
    string path_buff;
    string value_buff;
    char Rvalue_buff[2] ={0};
    struct timeval stnow;

    if(index >= GPIO_INDEX_MAX)
    {
        return -1;
    }
    extern int errno;errno = 0;
    gettimeofday(&stnow, NULL);
    Loger(NORMAL,"Start Set_GPIO::%u Output_Value::%u",index,value);
    path_buff = c_GpioConfig[index].GPIO_Name + "/value";
    gpio_fd = open(path_buff.c_str(),O_WRONLY);
    if(gpio_fd == -1)
    {
        if(errno == 2){
            ++GpioErrCnt;
        }
        Loger(NORMAL,"GpioSet filed!::%d,errno = %d ,strerror = %s",index,errno,strerror(errno));
        //WriteBlockLog(startTimeStamp,index, value, OPEN_ERR);
        return -1;
    }
    if(value == 0)
    {
        value_buff = "0";
    }
    else
    {
        value_buff = "1";
    }
    ret = writen(gpio_fd,value_buff.c_str(),value_buff.length());
    if(ret != static_cast<int64_t>(c_GpioConfig[index].GPIO_Direction.length()))
    {   
        if(errno == 22){
            ++GpioErrCnt;
        }
        //WriteBlockLog(startTimeStamp,index, value, WRITE_ERR);
        Loger(NORMAL,"write value filed! ret = %d, errno = %d,strerror = %s",ret,errno,strerror(errno));
    }
    close_fd = close(gpio_fd);
    if(close_fd == -1){
        Loger(NORMAL,"close write file!%d-%d-%s",index,errno,strerror(errno));
    }
    gpio_fd = open(path_buff.c_str(),O_RDONLY);
    if(gpio_fd == -1){
        Loger(NORMAL,"GpioSet Read filed!::%d-%d-%s",index,errno,strerror(errno));
    }
    else{
        ret = read(gpio_fd,Rvalue_buff,1);
        if(ret == -1){
            Loger(NORMAL,"GpioSet read GpioGet filed!");
        } 
        Loger(NORMAL,"Set_GPIO::%u Output_Value::%u Read Get_GPIO_Vlue::%d",index,value,atoi(Rvalue_buff));
        close(gpio_fd);
        if(atoi(Rvalue_buff) != value){
            //WriteBlockLog(startTimeStamp,index, value, READ_ERR);
            return -1;
        }
    }
    //WriteBlockLog(startTimeStamp,index, value, FINISH);
    return 0;
}

/**
 * @brief 获取GPIO输入电平
 * 
 * @param GPIO_Index GPIO控制索引
 * @param Value GPIO输入电平
 * @return int8_t -1 设置失败，0 设置成功
 */
int8_t GpioGet(GpioIndex index,uint8_t &value)
{
    ssize_t ret;
    int32_t gpio_fd,close_fd;
    string path_buff;
    char value_buff[2] ={0};
    
    if(index >= GPIO_INDEX_MAX)
    {
        return -1;
    }

    struct timespec g_beginTime;
    struct timespec g_endTime;
    clock_gettime(CLOCK_MONOTONIC, &g_beginTime);
    
    path_buff = c_GpioConfig[index].GPIO_Name + "/value";
    extern int errno;errno = 0;
    gpio_fd = open(path_buff.c_str(),O_RDONLY);
    if(gpio_fd == -1)
    {
        if(errno == 2){
            ++GpioErrCnt;
        }
        Loger(NORMAL,"index:%d open GpioGet filed! errno = %d,strerror = %s",index,errno,strerror(errno));
        return -1;
    }
    ret = readn(gpio_fd,value_buff,1);
    if(ret == -1)
    {
        if(errno == 22){
            ++GpioErrCnt;
        }
        Loger(NORMAL,"read GpioGet filed! errno = %d-%d",errno,gpio_fd);
    }
    value = atoi(value_buff);
    close_fd = close(gpio_fd);
    if(close_fd == -1){
        Loger(NORMAL,"close read file!%d-%d",index,gpio_fd);
    }
    clock_gettime(CLOCK_MONOTONIC, &g_endTime);
    int64_t CycleTime = (g_endTime.tv_sec - g_beginTime.tv_sec)*1000000000 + g_endTime.tv_nsec - g_beginTime.tv_nsec;

    if(CycleTime > 10000000){
        Loger(NORMAL,"GPIO Read  cycle time = %lld ms, gpio = %d",CycleTime/1000,index);
    }
    return 0;
}