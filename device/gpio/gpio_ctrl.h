#ifndef _GPIO_CTRL_H
#define _GPIO_CTRL_H

#include <iostream>
#include <string.h>
#include <iomanip>
using namespace std;

typedef enum
{
    // DO
    GPIO_OUT_K1K2 = 0,
    GPIO_BAT_K3K4 = 1,
    GP<PERSON>_RES_K5 = 2,
    GP<PERSON>_RES_K6 = 3,
    GP<PERSON>_RES_K7 = 4,
    GPIO_INSULT_KP = 5,
    GP<PERSON>_INSULT_KN = 6,
    GPIO_INSULT_KPE = 7,
    GPIO_S2 = 8,
    GPIO_S3 = 9,
    GPIO_FAN_CTRL = 10,
    GPIO_DO_RELAY_ADC = 11,
    GPIO_DO_MAX = 12,
    // DI
    GPIO_DI_START = 12,
    GPIO_OUT_K1_FB = 12,
    GPIO_OUT_K2_FB = 13,
    GPIO_BAT_K3_FB = 14,
    GPIO_BAT_K4_FB = 15,
    GPIO_RES_K5_FB = 16,
    GPIO_RES_K6_FB = 17,
    GPIO_RES_K7_FB = 18,
    GPIO_INDEX_MAX,
}GpioIndex;
#define GPIO_DI_MAX (GPIO_INDEX_MAX - GPIO_DO_MAX)
//#define GPIO_DI_START (GPIO_INDEX_MAX - GPIO_DO_MAX)
typedef struct 
{
    GpioIndex Ctrl_Index;        // GPIO控制索引
    //string GPIO_Number;           // GPIO引脚
    string GPIO_Name;               // GPIO名字
    string GPIO_Direction;          // GPIO流方向 0 in，1 out
    string GPIO_Default_Value;      // GPIO默认电压 0 low, 1 high
}GpioConfigDef;


int8_t GPIO_Init(GpioIndex index);
int8_t GpioSet(GpioIndex index,uint8_t value);
int8_t GpioGet(GpioIndex index,uint8_t &value);
uint32_t GetGpioErrCnt(void);


#endif
