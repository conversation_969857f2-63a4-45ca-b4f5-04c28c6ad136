/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class loger Definition here.
 */
#include "loger.h"
#include "evs_gpio.h"
#include "gpio_ctrl.h"

static uint32_t thisCnt = 0;
static uint32_t DIIdx = 0;
static uint32_t DOIdx = 0;

static uint32_t DOCmd[GPIO_DO_MAX];
static uint32_t DOCmdBak[GPIO_DO_MAX];

static uint32_t DICmd[GPIO_DI_MAX];
static uint32_t DIState[GPIO_DI_MAX];

int32_t GpioSet(GpioIndex gpio);
int32_t GpioClr(GpioIndex gpio);
int32_t GpioGet(GpioIndex gpio);

int32_t GpioInit(void);
int32_t GpioProcess(void);
/***************************************************************************
函数名:EVSGpio::GpioInit
功能描述: 
作者:
日期:
****************************************************************************/
int32_t GpioInit(void){
    thisCnt = 0;
    DIIdx = 0;
    DOIdx = 0;
    memset(DOCmd,0,sizeof(DOCmd));
    memset(DOCmdBak,0,sizeof(DOCmdBak));
    memset(DICmd,0,sizeof(DICmd));
    memset(DIState,0,sizeof(DIState));
    for(uint32_t i = 0; i < GPIO_INDEX_MAX; ++i){
        GPIO_Init((GpioIndex)i);
    }
    return 0;
};

/***************************************************************************
函数名:EVSGpio::Set
功能描述: 
作者:
日期:
****************************************************************************/
int32_t GpioSet(GpioIndex gpio){
    if(gpio >= GPIO_DO_MAX){
        return -1;
    }
    DOCmd[gpio] = 1;
    return 0;
}
/***************************************************************************
函数名:EVSGpio::Clr
功能描述: 
作者:
日期:
****************************************************************************/
int32_t GpioClr(GpioIndex gpio){
    if(gpio >= GPIO_DO_MAX){
        return -1;
    }
    DOCmd[gpio] = 0;
    return 0;
}
/***************************************************************************
函数名:EVSGpio::Get
功能描述: 
作者:
日期:
****************************************************************************/
int32_t GpioGet(GpioIndex gpio){
    if(gpio >= GPIO_INDEX_MAX || gpio < GPIO_DI_START){
        return 0;
    }
   uint32_t idx = gpio - GPIO_DI_START; 
    DICmd[idx] = 1;
    return DIState[idx];
}

/***************************************************************************
函数名:EVSGpio::GpioProcess
功能描述: 
作者:
日期:
****************************************************************************/
int32_t GpioProcess(void){
    int32_t ret = -1;
    uint32_t cnt = 0;
    uint8_t value = 0;
    // DO 
    if(memcmp(DOCmd,DOCmdBak,sizeof(DOCmd)) != 0){
        for(int32_t idx = 0;idx < GPIO_DO_MAX; ++idx){
            DOIdx = (DOIdx)%GPIO_DO_MAX;
            if(DOCmd[DOIdx] != DOCmdBak[DOIdx]){
                if(0 == GpioSet((GpioIndex)DOIdx,DOCmd[DOIdx])){
                    DOCmdBak[DOIdx] = DOCmd[DOIdx];
                }
                ++cnt;
            }
            ++DOIdx;
            if(cnt >2){
                break;
            }
        }
    }
    // DI 
    for(int32_t idx = 0;idx < GPIO_DI_MAX; ++idx){
        DIIdx = (DIIdx)%GPIO_DI_MAX;
        if(DICmd[DIIdx] == 1){
            if(GpioGet((GpioIndex)(DIIdx + GPIO_DI_START),value) == 0){
                DIState[DIIdx] = value;
                DICmd[DIIdx] = 0;
            }
            ++cnt;
        }
        ++DIIdx;
        if(cnt > 3){
            break;
        }
    }
    if(++thisCnt%100 == 0){
       // Loger(2,NORMAL, "GpioProcess =  %d", thisCnt);
    }
    
    return ret;
}