/* Copyright 2021, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class VCIGunHolder and VCIGunCharging Declaration here.
 */
#ifndef	_FAIL_MANAGER_H
#define _FAIL_MANAGER_H
#include <unordered_map>
/******************故障 define*******************************/

#define		FAIL_RELEASE		0//故障消失
#define		FAIL_ACK			1//故障产生

typedef enum {
	FAIL_NULL = 0,
	FAIL_SE_CONDITION = 1,                         // 达到充电机设定的条件中止（正常）
	FAIL_SE_MANUAL = 2,                            // 人工中止（正常）
	FAIL_SE_APPO_CHANGE = 3,                       // 预约计划变更（正常）
	FAIL_SE_APPO_TIME_NO = 4,                      // 已预约未达到预约启动时间（正常）
	FAIL_SE_SOS = 5,                               // 急停（紧急）
	FAIL_SE_LEAKAGE_CURR = 6,                      // 漏电（紧急）
	FAIL_SE_DISCONNECT = 7,                        // 车辆接口断开（紧急）
	FAIL_SE_CC1_ANOMA = 8,                         // CC1电压异常（紧急）
	FAIL_SE_LOCK_ANOMA = 9,                        // 电子锁异常（紧急）
	FAIL_SE_OTHERS = 10,                            // 其他故障（故障）
	FAIL_SE_OTHERS_SOS = 11,                        // 其他紧急故障（紧急）
	FAIL_SE_S2_OPEN = 12,                           // S2断开（紧急）
	FAIL_SE_RECV_STOP_MSG = 13,                     // 接受到车辆中止报文（跟车走（默认：正常））
	FAIL_SE_TM_FUN_CONFER = 14,                   // 功能协商阶段超时中止
	FAIL_SE_TM_CONFIG = 15,                       // 参数配置阶段超时中止
	FAIL_SE_TM_AUTHEN = 16,                       // 鉴权阶段超时
	FAIL_SE_TM_AUTHEN_MSG = 17,                   // 鉴权阶段报文超时（预留）
	FAIL_SE_TM_APPO = 18,                         // 预约阶段超时
	FAIL_SE_TM_APPO_MSG = 19,                     // 预约阶段报文超时（预留）
	FAIL_SE_TM_CHECK = 20,                        // 输出回路检测阶段超时
	FAIL_SE_TM_CHECK_MSG = 21,                    // 输出回路阶段报文超时（预留）
	FAIL_SE_TM_SUPPLY = 22,                       // 供电模式阶段超时
	FAIL_SE_TM_SUPPLY_MSG = 23,                   // 供电模式阶段报文超时
	FAIL_SE_TM_ERERGY = 24,                       // 预充及能量传输阶段超时
	FAIL_SE_TM_ENERGY_MSG = 25,                   // 预充及能量传输阶段报文超时
	FAIL_SE_TM_EMD = 26,                          // 结束阶段超时
	FAIL_SE_TM_END_MSG = 27,                      // 结束阶段报文超时（预留）
	//* 功能模块执行失败
	FAIL_SE_FUN_FAIL = 28,                        // 功能协商必须项协商失败（故障）
	FAIL_SE_AUTHEN_CONFER_FAIL = 29,              // 鉴权协商不成功（故障）
	FAIL_SE_PARA_DISMATCH = 30,                   // 参数配置失败，参数不匹配（故障）
	FAIL_SE_AUTHEN_FAIL = 31,                     // 鉴权失败（故障）
	FAIL_SE_APPO_NOT_ALLOW = 32,                  // 预约不允许（故障）
	FAIL_SE_WAKEUP_FAILE = 33,                    // 唤醒不成功（故障）
	FAIL_SE_VOLT_CHECK_FAIL = 34,                 // 自检充电口电压检测失败（故障）
	FAIL_SE_STICK_CHECK_FAIL = 35,                // 自检粘连检测失败（故障）
	FAIL_SE_SHORT_CIRCUIT = 36,                   // 自检短路检测失败（故障）
	FAIL_SE_INSULT_FAIL = 37,                     // 自检绝缘检测失败（故障）
	FAIL_SE_DISCHG_FAIL = 38,                     // 自检泄放失败（故障）
	FAIL_SE_SUPPLY_VOLT_HIGH = 39,                // 供电模式电压过高（紧急）
	FAIL_SE_SUPPLY_CURR_HIGH = 40,                // 供电模式电流过大（紧急）
	FAIL_SE_SUPPLY_VOLT_ERR = 41,                 // 供电电压不匹配（故障）
	FAIL_SE_SUPPLY_CURR_ERR = 42,                 // 供电电流异常（故障）
	FAIL_SE_SUPPLY_CAR_ERR = 43,                  // 供电车辆逻辑错误（故障）
	FAIL_SE_SUPPLY_INSULT_FAIL = 44,              // 供电模式绝缘故障（故障）
	FAIL_SE_SUPPLY_POWER_FAIL = 45,               // 供电模式车辆不行应功率调节（故障）
	FAIL_SE_CHG_VOLT_HIGH = 46,                   // 充电中电压过高（紧急）
	FAIL_SE_CHG_CURR_HIGH = 47,                   // 充电中电流过大（紧急）
	FAIL_SE_PRE_CHG_VOL_DISMATCH = 48,            // 预充电压不匹配（故障）
	FAIL_SE_CHG_VOL_FAULT = 49,                   // 充电中电压异常（故障）
	FAIL_SE_CHG_CURR_FAULT = 50,                  // 充电中电流异常（故障）
	FAIL_SE_CHG_OVER_TEMP = 51,                   // 充电中电缆过温（故障）
	FAIL_SE_CHG_CAR_ERR = 52,                     // 充电中车辆逻辑错误（故障）
	FAIL_SE_PAUSETIMEOUT = 53,                    // 充电中暂停总时长超限（故障）
	FAIL_SE_PAUSE_TIMEOUT = 54,                   // 充电中暂停次数超限（故障）
	FAIL_SE_PAUSE_CNT_BIG = 55,                   // 暂停冲突（故障）
	FAIL_SE_CHG_OTHER_FAULT = 56,                 // 充电中其他故障
	FAIL_SE_CHG_OTHER_SOS = 57,                   // 充电中其他紧急故障
	//* 阶段确认故障
	FAIL_SE_PHASE_CONFER_FAIL = 58,          	// 阶段确认失败
	FAIL_SE_PHASE_CONFER_TM = 59,               // 阶段确认超时

	FAIL_USER_STOP = 60,                     // user stop
	FAIL_EV_CONDITION = 61,                       // 达到充电机设定的条件中止
	FAIL_EV_MANUAL = 62,                          // 人工中止
	FAIL_EV_APPO_CHANGE = 63,                     // 预约计划变更
	FAIL_EV_INSULT_FAULT = 64,                    // 绝缘故障
	FAIL_EV_DISCONNECT = 65,                      // 车辆接口断开
	FAIL_EV_PE_DISCONNECT = 66,                   // 车辆PE触头断路
	FAIL_EV_CC2_ANOMAL = 67,                      // CC2电压异常
	FAIL_EV_CC3_ANOMAL = 68,                      // CC3电压异常
	FAIL_EV_ELOCK_ANOMAL = 69,                    // 车辆插头电子锁异常
	FAIL_EV_OTHERS = 70,                          // 其他故障（故障）
	FAIL_EV_OTHERS_SOS = 71,                      // 其他紧急故障
	FAIL_EV_S1_OPEN = 72,                         // S1断开
	FAIL_EV_RECV_END_MSG = 73,                    // 接受到车辆中止报文
	//* 功能模块信息交互超时（只有供电模式与能量传输阶段才会有报文超时 请他模块模块中报文超时为预留）
	FAIL_EV_TM_FUN_CONFER = 74,                   // 功能协商阶段超时中止
	FAIL_EV_TM_CONFIG = 75,                       // 参数配置阶段超时中止
	FAIL_EV_TM_AUTHEN = 76,                       // 鉴权阶段超时
	FAIL_EV_TM_AUTHEN_MSG = 77,                   // 鉴权阶段报文超时（预留）
	FAIL_EV_TM_APPO = 78,                         // 预约阶段超时
	FAIL_EV_TM_APPO_MSG = 79,                     // 预约阶段报文超时（预留）
	FAIL_EV_TM_CHECK = 80,                        // 输出回路检测阶段超时
	FAIL_EV_TM_CHECK_MSG = 81,                    // 输出回路阶段报文超时（预留）
	FAIL_EV_TM_SUPPLY = 82,                       // 供电模式阶段超时
	FAIL_EV_TM_SUPPLY_MSG = 83,                   // 供电模式阶段报文超时
	FAIL_EV_TM_ERERGY = 84,                       // 预充及能量传输阶段超时
	FAIL_EV_TM_ENERGY_MSG = 85,                   // 预充及能量传输阶段报文超时
	FAIL_EV_TM_EMD = 86,                          // 结束阶段超时
	FAIL_EV_TM_END_MSG = 87,                      // 结束阶段报文超时（预留）
	//* 功能模块执行失败
	FAIL_EV_FUN_FAIL = 88,                        // 功能协商必须项协商失败
	FAIL_EV_OTHER_FUN_FAIL = 89,                  // 其他功能模块协商不成功
	FAIL_EV_PARA_DISMATC = 90,                    // 参数不匹配
	FAIL_EV_AUTHEN_FAIL = 91,                     // 鉴权失败
	FAIL_EV_APPO_WAKEUP_FAIL = 92,                // 预约不允许|唤醒不成功
	FAIL_EV_SE_SELFCHECK_FAIL = 93,               // 充电机输出回路加测执行失败
	FAIL_EV_SUPPLY_VOL_HIGH = 94,                 // 供电模式电压过高
	FAIL_EV_SUPPLY_CURR_BIG = 95,                 // 供电模式电流过大
	FAIL_EV_SUPPLY_VOL_ERR = 96,                  // 供电电压异常
	FAIL_EV_SUPPLY_CURR_ERR = 97,                 // 供电电流异常
	FAIL_EV_SUPPLY_MOD_FAIL = 98,                 // 供电模块投切失败 
	
	FAIL_EV_CHG_VOL_HIGH = 99,                    // 充电中电压过高
	FAIL_EV_CHG_CURR_BIG = 100,                    // 充电中电流过大
	FAIL_EV_CHG_VOL_ERR = 101,                     // 充电中电压异常
	FAIL_EV_CHG_CURR_ERR = 102,                    // 充电中电流异常
	FAIL_EV_OVER_TEMP = 103,                       // 充电中车辆插座过温
	FAIL_EV_UNABLE = 104,                          // 充电中无法结束（正常结束中止时充电机无法停止）
	FAIL_EV_PAUSE_TIMEOUT = 105,                   // 充电中暂停总时长超限
	FAIL_EV_PAUSE_CNT_BIG = 106,                   // 充电中暂停次数超限
	FAIL_EV_PSUSE_CONFLICT = 107,                  // 暂停冲突
	FAIL_EV_CHG_OTHER_FAULT = 108,                 // 充电中其他故障
	FAIL_EV_CHG_OTHER_SOS = 109,                   // 充电中其他紧急故障
	//* 阶段确认故障
	FAIL_EV_PHASE_CONFER_FAIL = 110,               // 阶段确认失败
	FAIL_EV_PHASE_CONFER_TM = 111,                 // 阶段确认超时
	FAIL_SE_STOP = 112,							   // 设备侧中止
	FAIL_EV_TM_0X64 = 113,						   // 供电模式 充电机动态输出能力报文0x64超时
	FAIL_EV_TM_0X75 = 114,						   // 充电阶段 充电机动态输出能力报文0x75超时
	FAIL_EV_TM_PROTO_NEGO = 115,				   // 版本协商超时
	FAIL_EV_PROTO_NEGO_FAIL = 116,				   // 版本协商失败

	FAIL_INSULT_VOL_HIGH =  117,             // 绝缘检测电压高
	FAIL_INSULT_VOL_LOW = 118,               // 绝缘检测阶段模块电压低
    FAIL_INSULT_WARN =  119,                 // 绝缘检测告
    FAIL_INSULT_ABNORMAL =  120,             // 绝缘检测异常
	FAIL_DIS_CHG_FAIL =  121,                // 泄放失败
	FAIL_INSULT_SAMP_ERR= 122,               // 绝缘结果不可信 （注:绝缘检测采样值正负偏差过大，采样结果不可信）线程 故障 不显示
	FAIL_START_VOL_HIGH = 123,
	FAIL_NUM_MAX
}_FaultIndexDef;

typedef struct{
	_FaultIndexDef index;
	uint8_t isStop;
	uint8_t isShow;
	uint8_t endRelease;
	uint8_t cc1Release;
	uint8_t endType;
	uint8_t endCodeL;
	uint8_t endCodeH;
}FaultMsgDef;
			
/**********************class define****************************/

class FailManager{
	private:
		uint32_t thisID;
		FaultMsgDef StopFault; // the flg to stop the charger
		std::unordered_map<_FaultIndexDef, FaultMsgDef> FaultMap_;
	public:
		bool GetFailState(_FaultIndexDef fault,FaultMsgDef &msg);
		bool GetFailState(_FaultIndexDef fault);
		void FailReport(_FaultIndexDef fault, uint16_t cmd);
		void ChgEndFailRelase(void);
		void GunPullOutFailRelase(void);
	public:
		FailManager();
		~FailManager();
		bool GetStopFault(FaultMsgDef &msg);
		bool GetStopFault();
};
#endif




