/* Copyright 2021, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * common functions source files.
 */
#ifndef _TIMER_H
#define _TIMER_H
#include <cstring>
#include <iostream>

typedef struct _SysBaseTime {
    bool f10ms;
    bool f100ms;
    bool f200ms;
    bool f1s;
} SysBaseTime;

class Timer
{
    private:

    uint64_t  SysClock = 0;
    struct timespec tvBak = {0};
    uint8_t Startflg = 0;
    int64_t TimeStampCnt = 0;

    public:
        SysBaseTime Flg_;
    private:
        void TimerFlagUpdete(bool cycle);
    public:
        void TimerBase(void); 
        void Init(void) {memset(&Flg_,0,sizeof(Flg_));};
};
#endif   //_TIMER_H