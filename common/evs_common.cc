/* Copyright 2021, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * common functions source files.
 */
#include "evs_common.h"
#include <unistd.h>
#include <errno.h>

uint8_t g_evsType = 0; // 模拟器类型 0:完整模拟器 1：单板模拟器
uint64_t GetLocolTimeMs(void);
ssize_t writen(int fd, const void *vptr, size_t n);
ssize_t readn(int fd, void *vptr, size_t n);

/* Write "n" bytes to a descriptor. */
ssize_t writen(int fd, const void *vptr, size_t n)
{
    size_t nleft;
    ssize_t nwritten;
    const char *ptr;

    ptr = (const char *)vptr;
    nleft = n;
    while (nleft > 0) {
        if ( (nwritten = write(fd, ptr, nleft)) <= 0) {
            if (nwritten < 0 && errno == EINTR)
                nwritten = 0;        /* and call write() again */
            else
                return(-1);            /* error */
        }

        nleft -= nwritten;
        ptr   += nwritten;
    }
    return(n);
}
/* end writen */

/* Read "n" bytes from a descriptor. */
ssize_t  readn(int fd, void *vptr, size_t n)
{
    size_t nleft;
    ssize_t nread;
    char *ptr;

    ptr = (char*)vptr;
    nleft = n;
    while (nleft > 0) {
        if ( (nread = read(fd, ptr, nleft)) < 0) {
            if (errno == EINTR)
                nread = 0;        /* and call read() again */
            else
                return(-1);
        } else if (nread == 0)
            break;                /* EOF */

        nleft -= nread;
        ptr   += nread;
    }
    return(n - nleft);        /* return >= 0 */
}

std::string BytesToHexString(const char *bytes, const ssize_t length) {
    std::string buff;
    if (bytes == NULL) {
        return "";
    }
    const char hex[16] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
    // std::string buff;
    const int len = length;
    for (int j = 0; j < len; j++) {
        int32_t tmp = bytes[j];
        buff += hex[((tmp>>4) & 0x0000000F)];
        buff += hex[((tmp) & 0x0000000F)];
    }
    return buff;
}

uint8_t CheckSum(uint8_t *data, uint32_t length)
{
    uint32_t i;
    uint8_t result=0;
    for (i = 0; i < length; i++) {
      result += data[i];
    }
    return (result &0x00FF);
}

uint64_t GetLocolTimeMs(void){
    uint64_t t_s,t_us;
    struct timeval now;
    gettimeofday(&now, NULL);
    t_s = now.tv_sec;
    t_us = now.tv_usec;
    return (t_s*1000+t_us/1000);
}

std::string getlocalIP(std::string ethx)
{
    char command[256];
    char ip_address[256];
    std::memset(command, 0, sizeof(command));
    std::memset(ip_address, 0, sizeof(ip_address));
    std::string _strcmd = "ip addr show "+ethx+"| grep \"inet \" | awk '{print $2}' | cut -d/ -f1";
 
    // 构造ifconfig命令
    snprintf(command, sizeof(command), _strcmd.c_str());
 
    // 执行命令并获取输出
    FILE *fp = popen(command, "r");
    if (fp == nullptr) {
        return "";
    }
    fgets(ip_address, sizeof(ip_address), fp);
    pclose(fp);
 
    // 去除尾部的换行符
    ip_address[strcspn(ip_address, "\n")] = 0;

    std::cout<<"get locolIP=="<< ip_address <<std::endl;

    //std::cout<< "get locolIP==" << ip_address << std::endl;
 
    return std::string(ip_address);
}