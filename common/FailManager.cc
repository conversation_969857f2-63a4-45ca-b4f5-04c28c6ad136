/* Copyright 2021, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class VCIGunHolder and VCIGunCharging Declaration here.
 */
#include <cstring>
#include "FailManager.h"
#include "evs_common.h"
#include "loger.h"

const FaultMsgDef C_FaultInfo[]= {
	// index-------------------stop-----show----endClr-cc1Clr--endType----endCodeL--endCodeH
	{FAIL_NULL,					0,		0,		1,		1,		0x0,		0x0, 		0x0		}, // 
	{FAIL_SE_CONDITION,			1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 达到充电机设定的条件中止（正常）
	{FAIL_SE_MANUAL,			1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 人工中止（正常）
	{FAIL_SE_APPO_CHANGE ,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 预约计划变更（正常）
	{FAIL_SE_APPO_TIME_NO,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 已预约未达到预约启动时间（正常）
	{FAIL_SE_SOS,				1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 急停（紧急）
	{FAIL_SE_LEAKAGE_CURR ,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 漏电（紧急）
	{FAIL_SE_DISCONNECT,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 车辆接口断开（紧急）
	{FAIL_SE_CC1_ANOMA,			1,		1,		1,		1,		0x01,		0x03,		0x02	}, // CC1电压异常（紧急）
	{FAIL_SE_LOCK_ANOMA,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 电子锁异常（紧急）
	{FAIL_SE_OTHERS,			1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 其他故障（故障）
	{FAIL_SE_OTHERS_SOS,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 其他紧急故障（紧急）
	{FAIL_SE_S2_OPEN,			1,		1,		1,		1,		0x01,		0x03,		0x02	}, // S2断开（紧急）
	{FAIL_SE_RECV_STOP_MSG,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 接受到车辆中止报文（跟车走（默认：正常））
	{FAIL_SE_TM_FUN_CONFER,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 功能协商阶段超时中止
	{FAIL_SE_TM_CONFIG,			1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 参数配置阶段超时中止
	{FAIL_SE_TM_AUTHEN,			1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 鉴权阶段超时
	{FAIL_SE_TM_AUTHEN_MSG,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 鉴权阶段报文超时（预留）
	{FAIL_SE_TM_APPO,			1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 预约阶段超时
	{FAIL_SE_TM_APPO_MSG,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 预约阶段报文超时（预留）
	{FAIL_SE_TM_CHECK,			1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 输出回路检测阶段超时
	{FAIL_SE_TM_CHECK_MSG,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 输出回路阶段报文超时（预留）
	{FAIL_SE_TM_SUPPLY,			1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 供电模式阶段超时
	{FAIL_SE_TM_SUPPLY_MSG,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 供电模式阶段报文超时
	{FAIL_SE_TM_ERERGY,			1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 预充及能量传输阶段超时
	{FAIL_SE_TM_ENERGY_MSG,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 预充及能量传输阶段报文超时
	{FAIL_SE_TM_EMD,			1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 结束阶段超时
	{FAIL_SE_TM_END_MSG,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 结束阶段报文超时（预留）
	//* 功能模块执行失败
	{FAIL_SE_FUN_FAIL,			1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 功能协商必须项协商失败（故障）
	{FAIL_SE_AUTHEN_CONFER_FAIL,1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 鉴权协商不成功（故障）
	{FAIL_SE_PARA_DISMATCH,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 参数配置失败，参数不匹配（故障）
	{FAIL_SE_AUTHEN_FAIL,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 鉴权失败（故障）
	{FAIL_SE_APPO_NOT_ALLOW,	1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 预约不允许（故障）
	{FAIL_SE_WAKEUP_FAILE,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 唤醒不成功（故障）
	{FAIL_SE_VOLT_CHECK_FAIL,	1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 自检充电口电压检测失败（故障）
	{FAIL_SE_STICK_CHECK_FAIL,	1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 自检粘连检测失败（故障）
	{FAIL_SE_SHORT_CIRCUIT,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 自检短路检测失败（故障）
	{FAIL_SE_INSULT_FAIL,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 自检绝缘检测失败（故障）
	{FAIL_SE_DISCHG_FAIL,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 自检泄放失败（故障）
	{FAIL_SE_SUPPLY_VOLT_HIGH,	1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 供电模式电压过高（紧急）
	{FAIL_SE_SUPPLY_CURR_HIGH,	1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 供电模式电流过大（紧急）
	{FAIL_SE_SUPPLY_VOLT_ERR,	1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 供电电压不匹配（故障）
	{FAIL_SE_SUPPLY_CURR_ERR,	1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 供电电流异常（故障）
	{FAIL_SE_SUPPLY_CAR_ERR,	1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 供电车辆逻辑错误（故障）
	{FAIL_SE_SUPPLY_INSULT_FAIL,1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 供电模式绝缘故障（故障）
	{FAIL_SE_SUPPLY_POWER_FAIL,	1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 供电模式车辆不行应功率调节（故障）
	{FAIL_SE_CHG_VOLT_HIGH,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 充电中电压过高（紧急）
	{FAIL_SE_CHG_CURR_HIGH,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 充电中电流过大（紧急）
	{FAIL_SE_PRE_CHG_VOL_DISMATCH,1,	1,		1,		1,		0x01,		0x03,		0x02	}, // 预充电压不匹配（故障）
	{FAIL_SE_CHG_VOL_FAULT,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 充电中电压异常（故障）
	{FAIL_SE_CHG_CURR_FAULT,	1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 充电中电流异常（故障）
	{FAIL_SE_CHG_OVER_TEMP,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 充电中电缆过温（故障）
	{FAIL_SE_CHG_CAR_ERR,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 充电中车辆逻辑错误（故障）
	{FAIL_SE_PAUSETIMEOUT,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 充电中暂停总时长超限（故障）
	{FAIL_SE_PAUSE_TIMEOUT,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 充电中暂停次数超限（故障）
	{FAIL_SE_PAUSE_CNT_BIG,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 暂停冲突（故障）
	{FAIL_SE_CHG_OTHER_FAULT,	1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 充电中其他故障
	{FAIL_SE_CHG_OTHER_SOS,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 充电中其他紧急故障
	//* 阶段确认故障
	{FAIL_SE_PHASE_CONFER_FAIL,	1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 阶段确认失败
	{FAIL_SE_PHASE_CONFER_TM,	1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 阶段确认超时
	{FAIL_USER_STOP,			1,		1,		1,		1,		0x01,		0x01,		0x02	}, // 阶段确认超时
	
	{FAIL_EV_CONDITION,			1,		1,		1,		1,		0x01,		0x01,		0x01	}, // 达到充电机设定的条件中止
	{FAIL_EV_MANUAL,			1,		1,		1,		1,		0x01,		0x01,		0x02	}, // 人工中止
	{FAIL_EV_APPO_CHANGE,		1,		1,		1,		1,		0x01,		0x01,		0x03	}, // 预约计划变更
	{FAIL_EV_INSULT_FAULT,		1,		1,		1,		1,		0x01,		0x02,		0x01	}, // 绝缘故障
	{FAIL_EV_DISCONNECT,		1,		1,		1,		1,		0x01,		0x02,		0x02	}, // 车辆接口断开
	{FAIL_EV_PE_DISCONNECT,		1,		1,		1,		1,		0x01,		0x02,		0x03	}, // 车辆PE触头断路
	{FAIL_EV_CC2_ANOMAL,		1,		1,		1,		1,		0x01,		0x02,		0x04	}, // CC2电压异常
	{FAIL_EV_CC3_ANOMAL,		1,		1,		1,		1,		0x01,		0x02,		0x05	}, // CC3电压异常
	{FAIL_EV_ELOCK_ANOMAL,		1,		1,		1,		1,		0x01,		0x02,		0x06	}, // 车辆插头电子锁异常
	{FAIL_EV_OTHERS,			1,		1,		1,		1,		0x01,		0x02,		0xFE	}, // 其他故障（故障）
	{FAIL_EV_OTHERS_SOS,		1,		1,		1,		1,		0x01,		0x02,		0xFF	}, // 其他紧急故障
	{FAIL_EV_S1_OPEN,			1,		1,		1,		1,		0x01,		0x03,		0x01	}, // S1断开
	{FAIL_EV_RECV_END_MSG,		1,		1,		1,		1,		0x01,		0x03,		0x02	}, // 接受到车辆中止报文
	//* 功能模块信息交互超时（只有供电模式与能量传输阶段才会有报文超时 请他模块模块中报文超时为预留）
	{FAIL_EV_TM_FUN_CONFER,		1,		1,		1,		1,		0x02,		0x01,		0x00	}, // 功能协商阶段超时中止
	{FAIL_EV_TM_CONFIG,			1,		1,		1,		1,		0x02,		0x02,		0x00	}, // 参数配置阶段超时中止
	{FAIL_EV_TM_AUTHEN,			1,		1,		1,		1,		0x02,		0x03,		0x00	}, // 鉴权阶段超时
	{FAIL_EV_TM_AUTHEN_MSG,		1,		1,		1,		1,		0x02,		0x03,		0x00	}, // 鉴权阶段报文超时（预留）
	{FAIL_EV_TM_APPO,			1,		1,		1,		1,		0x02,		0x04,		0x00	}, // 预约阶段超时
	{FAIL_EV_TM_APPO_MSG,		1,		1,		1,		1,		0x02,		0x04,		0x00	}, // 预约阶段报文超时（预留）
	{FAIL_EV_TM_CHECK,			1,		1,		1,		1,		0x02,		0x05,		0x00	}, // 输出回路检测阶段超时
	{FAIL_EV_TM_CHECK_MSG,		1,		1,		1,		1,		0x02,		0x05,		0x00	}, // 输出回路阶段报文超时（预留）
	{FAIL_EV_TM_SUPPLY,			1,		1,		1,		1,		0x02,		0x06,		0x00	}, // 供电模式阶段超时
	{FAIL_EV_TM_SUPPLY_MSG,		1,		1,		1,		1,		0x02,		0x06,		0x00	}, // 供电模式阶段报文超时
	{FAIL_EV_TM_ERERGY,			1,		1,		1,		1,		0x02,		0x07,		0x00	}, // 预充及能量传输阶段超时
	{FAIL_EV_TM_ENERGY_MSG,		1,		1,		1,		1,		0x02,		0x07,		0x00	}, // 预充及能量传输阶段报文超时
	{FAIL_EV_TM_EMD,			1,		1,		1,		1,		0x02,		0x08,		0x00	}, // 结束阶段超时
	{FAIL_EV_TM_END_MSG,		1,		1,		1,		1,		0x02,		0x08,		0x00	}, // 结束阶段报文超时（预留）
	//* 功能模块执行失败
	{FAIL_EV_FUN_FAIL,			1,		1,		1,		1,		0x03,		0x01,		0x01	}, // 功能协商必须项协商失败
	{FAIL_EV_OTHER_FUN_FAIL,	1,		1,		1,		1,		0x03,		0x01,		0x02	}, // 其他功能模块协商不成功
	{FAIL_EV_PARA_DISMATC,		1,		1,		1,		1,		0x03,		0x02,		0x01	}, // 参数不匹配
	{FAIL_EV_AUTHEN_FAIL,		1,		1,		1,		1,		0x03,		0x03,		0x01	}, // 鉴权失败
	{FAIL_EV_APPO_WAKEUP_FAIL,	1,		1,		1,		1,		0x03,		0x04,		0x01	}, // 预约不允许|唤醒不成功
	{FAIL_EV_SE_SELFCHECK_FAIL,	1,		1,		1,		1,		0x03,		0x05,		0x01	}, // 充电机输出回路加测执行失败
	{FAIL_EV_SUPPLY_VOL_HIGH,	1,		1,		1,		1,		0x03,		0x06,		0x01	}, // 供电模式电压过高
	{FAIL_EV_SUPPLY_CURR_BIG,	1,		1,		1,		1,		0x03,		0x06,		0x02	}, // 供电模式电流过大
	{FAIL_EV_SUPPLY_VOL_ERR,	1,		1,		1,		1,		0x03,		0x06,		0x03	}, // 供电电压异常
	{FAIL_EV_SUPPLY_CURR_ERR,	1,		1,		1,		1,		0x03,		0x06,		0x04	}, // 供电电流异常
	{FAIL_EV_SUPPLY_MOD_FAIL,	1,		1,		1,		1,		0x03,		0x06,		0x05	}, // 供电模块投切失败 
	
	{FAIL_EV_CHG_VOL_HIGH,		1,		1,		1,		1,		0x03,		0x07,		0x01	}, // 充电中电压过高
	{FAIL_EV_CHG_CURR_BIG,		1,		1,		1,		1,		0x03,		0x07,		0x02	}, // 充电中电流过大
	{FAIL_EV_CHG_VOL_ERR,		1,		1,		1,		1,		0x03,		0x07,		0x03	}, // 充电中电压异常
	{FAIL_EV_CHG_CURR_ERR,		1,		1,		1,		1,		0x03,		0x07,		0x04	}, // 充电中电流异常
	{FAIL_EV_OVER_TEMP,			1,		1,		1,		1,		0x03,		0x07,		0x05	}, // 充电中车辆插座过温
	{FAIL_EV_UNABLE,			1,		1,		1,		1,		0x03,		0x07,		0x06	}, // 充电中无法结束（正常结束中止时充电机无法停止）
	{FAIL_EV_PAUSE_TIMEOUT,		1,		1,		1,		1,		0x03,		0x07,		0x07	}, // 充电中暂停总时长超限
	{FAIL_EV_PAUSE_CNT_BIG,		1,		1,		1,		1,		0x03,		0x07,		0x08	}, // 充电中暂停次数超限
	{FAIL_EV_PSUSE_CONFLICT,	1,		1,		1,		1,		0x03,		0x07,		0x09	}, // 暂停冲突
	{FAIL_EV_CHG_OTHER_FAULT,	1,		1,		1,		1,		0x03,		0x07,		0xFE	}, // 充电中其他故障
	{FAIL_EV_CHG_OTHER_SOS,		1,		1,		1,		1,		0x03,		0x07,		0xFF	}, // 充电中其他紧急故障
	//* 阶段确认故障
	{FAIL_EV_PHASE_CONFER_FAIL,	1,		1,		1,		1,		0x04,		0x01,		0x01	}, // 阶段确认失败
	{FAIL_EV_PHASE_CONFER_TM,	1,		1,		1,		1,		0x04,		0x02,		0x01	}, // 阶段确认超时
	{FAIL_SE_STOP,				1,		1,		1,		1,		0x01,		0x03,		0x02	}, // SE stop
	{FAIL_EV_TM_0X64,			1,		1,		1,		1,		0x02,		0x06,		0x64	}, // SE stop
	{FAIL_EV_TM_0X75,			1,		1,		1,		1,		0x02,		0x07,		0x75	}, // SE stop
	{FAIL_EV_TM_PROTO_NEGO,		1,		1,		1,		1,		0x0,		0x0,		0x0		}, // 版本协商超时
	{FAIL_EV_PROTO_NEGO_FAIL,	1,		1,		1,		1,		0x0,		0x0,		0x0		}, // 版本协商失败
};
/************************************************************************************
函数名：	Class_FailManager()	
功能描述：	
//Author: 	
***********************************************************************************/
FailManager::FailManager(void){
	memset(&StopFault,0,sizeof(StopFault));
	FaultMap_.clear();
}
FailManager::~FailManager(){}
/************************************************************************************
函数名：	App_iFailStateGet()	
功能描述：	//iFailValState为故障是否消失，一般为0或这1，0为故障消失,1为故障消失.
//Author: 	
***********************************************************************************/

void FailManager::FailReport(_FaultIndexDef fault, uint16_t cmd) {
	
	if(fault >= FAIL_NUM_MAX) {
		return ;
	}
	auto id = FaultMap_.find(fault);
	if(id != FaultMap_.end()){
		if(cmd == FAIL_RELEASE){
			FaultMap_.erase(id->first);
		}
	}else{
		if(cmd == FAIL_ACK){
			FaultMap_[fault] = C_FaultInfo[fault];
		}
	}
}
/************************************************************************************
函数名：	App_iFailStateGet()	
功能描述：	Getting the condition of Sepecific Fail
//Author: 	
***********************************************************************************/
bool FailManager::GetFailState(_FaultIndexDef fault,FaultMsgDef &msg) {
	if(FaultMap_.find(fault) != FaultMap_.end()){
		msg = FaultMap_[fault];
		return true;
	}else{
		return false;
	}
}
bool FailManager::GetFailState(_FaultIndexDef fault) {
	if(FaultMap_.find(fault) != FaultMap_.end()){
		return true;
	}else{
		return false;
	}
}
/************************************************************************************
函数名：	App_ChgEndFailRelase()
功能描述：	Failstate handle
//Author:
***********************************************************************************/
void FailManager::ChgEndFailRelase(void) {
	uint32_t cnt = 0;
	Loger(NORMAL,"ChgEndFailRelase::1");
	for (auto it = FaultMap_.begin(); it != FaultMap_.end();) {
		if(it->second.endRelease == 1){
			Loger(NORMAL,"ChgEndFailRelase:: cnt = %d",cnt++);
			it = FaultMap_.erase(it);
		}else{
			++it;
		}
    }
}
/************************************************************************************
函数名：	App_GunPullOutFailRelase()
功能描述：	Failstate handle
//Author:
***********************************************************************************/
void FailManager::GunPullOutFailRelase(void) {
	for (auto it = FaultMap_.begin(); it != FaultMap_.end();) {
		if(it->second.cc1Release == 1){
			it = FaultMap_.erase(it);
		}else{
			++it;
		}
    }
}
bool FailManager::GetStopFault(FaultMsgDef &msg){
	for (const auto& fault : FaultMap_) {
		if(fault.second.isStop == 1){
			msg = fault.second;
			return true;
		}
    }
	return false;
}

bool FailManager::GetStopFault(){
	for (const auto& fault : FaultMap_) {
		if(fault.second.isStop == 1){
			if(StopFault.index != fault.second.index){
				StopFault.index = fault.second.index;
				Loger(NORMAL,"StopFault = %d",(uint16_t)fault.second.index);
			}
			return true;
		}
    }
	return false;
}