/* Copyright 2021, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * common functions source files.
 */
#include "timer.h"
/************************************************************************************
函数名称：	TimerFlagUpdete()
功能描述： 
Notice: 10ms call
************************************************************************************/
void Timer::TimerFlagUpdete(bool cycle) {
    if(cycle) {
        SysClock++;
        Flg_.f10ms = cycle;
        Flg_.f100ms = SysClock%10 ? 0:1;
        Flg_.f200ms = SysClock%20 ? 0:1;
        Flg_.f1s = SysClock%100 ? 0:1;
    } else {
       memset(&Flg_,0,sizeof(Flg_)); 
    }
}
/************************************************************************************
函数名称：	TimerFlagUpdete()
功能描述： 
Notice: 3ms call
************************************************************************************/
void Timer::TimerBase(void) {
    uint64_t nsdiff = 0;
    struct timespec tv = {0};

    if(Startflg == 0) {
        Startflg = 1;
        clock_gettime(CLOCK_MONOTONIC, &tvBak);
    }
    clock_gettime(CLOCK_MONOTONIC, &tv);
    nsdiff = (((tv.tv_sec - tvBak.tv_sec)*1000000000 + tv.tv_nsec -tvBak.tv_nsec));
    TimeStampCnt += nsdiff;
    tvBak = tv;
    if (TimeStampCnt > 8500000) {
        TimeStampCnt -= 10000000;
        TimerFlagUpdete(true);
    } else {
       TimerFlagUpdete(false); 
    }
}