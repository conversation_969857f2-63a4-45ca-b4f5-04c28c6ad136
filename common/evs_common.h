
/* Copyright 2021, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Module head file(Global).
 */
#ifndef _EVS_COMMON_H
#define _EVS_COMMON_H
#include <string>
#include <errno.h>
#include <sstream>
#include <iomanip>
#include <sys/time.h>
#include <time.h>
#include <cstring>
#include <iostream>


const size_t DEFAULTSIZE = 1024;
const size_t HEADSIZE = 4;
const size_t TAILSIZE = 4;
const size_t LOG_HEADSIZE = 7;
const uint8_t TAIL[4] = {0x58, 0x41, 0x57, 0x4D};
const uint8_t LOG_RECORD_INFO_CMD = 0x10;


const int EVS_SUCCESS   (0);
const int EVS_FAILED    (-1);
const int SOCKET_FAILED (-1);

#define IN 
#define OUT 

#define MAX(x,y)                        (((x)>=(y))?(x):(y))
#define MIN(x,y)                        (((x)<=(y))?(x):(y))
#define	DownLimit16(Value,DownValue)    {Value = (Value <= DownValue) ? DownValue : Value;}
#define	UpLimit16(Value,UpValue)        {Value = (Value >= UpValue) ? UpValue : Value;}
#define UpDownLimited(Var,Max,Min)      {(Var) = ((Var)>=(Max))?(Max):(Var);(Var) = ((Var)<=(Min))?(Min):(Var);}
#define U8BIT_SET(x, bit)	                (x = (uint8_t)(x | (((uint8_t)1)<<bit)))
#define U8BIT_CLR(x, bit)	                (x = (uint8_t)(x &(~(((uint8_t)1)<<bit))))
#define U8BIT_GET(x, bit)					((uint8_t)(((uint8_t)x)>>bit & 0x01))

#define U16BIT_SET(x, bit)	                (x = (uint16_t)(x | (((uint16_t)1)<<bit)))
#define U16BIT_CLR(x, bit)	                (x = (uint16_t)(x &(~(((uint16_t)1)<<bit))))
#define U16BIT_GET(x, bit)					((uint16_t)(((uint16_t)x)>>bit & 0x0001))

#define U32BIT_SET(x, bit)					(x = (uint64_t)(x | (((uint64_t)1)<<bit)))
#define U32BIT_CLR(x, bit)					(x = (uint64_t)(x &(~(((uint64_t)1)<<bit))))
#define U32BIT_GET(x, bit)					((uint64_t)(((uint64_t)x)>>bit & 0x0000000000000001))
#define U64BIT_SET(x, bit)					(x = (uint64_t)(x | (((uint64_t)1)<<bit)))
#define U64BIT_CLR(x, bit)					(x = (uint64_t)(x &(~(((uint64_t)1)<<bit))))
#define U64BIT_GET(x, bit)					((uint64_t)(((uint64_t)x)>>bit & 0x0000000000000001))
#define LERRY_LEN(x) (sizeof(x)/sizeof(x[0]))
#define GUNA 0
#define GUNB 1

#define RLY_CLOSE (1)
#define RLY_OPEN  (0)

#define  BAT_ON (1)
#define  BAT_OFF (0)

#define  ACK (0xAA)
#define  NACK (0x00)

#define	CHARGE_ON     	(1)
#define	CHARGE_OFF		(0)

#define	ON_ACK		(2)
#define	OFF_ACK	    (1)
/**
 * @brief proto 协议头
 */
typedef struct _ProtocolBase_t {
    uint8_t head;       // 0xFA
    uint16_t len;       // data len (exclude header)
    uint8_t cmd;        // command
    uint8_t data[0];    // data
} ProtocolBase_t;

ssize_t writen(int fd, const void *vptr, size_t n);
ssize_t readn(int fd, void *vptr, size_t n);
extern std::string BytesToHexString(const char *bytes, const ssize_t length);
extern uint8_t CheckSum(uint8_t *data, uint32_t length);
extern uint64_t GetLocolTimeMs(void);
std::string getlocalIP(std::string ethx);

#endif  //_EVS_COMMON_H