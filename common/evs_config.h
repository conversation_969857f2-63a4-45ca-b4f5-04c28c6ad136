/* Copyright 2021, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Module head file(Global).
 */
#ifndef _EVS_CONFIG_H
#define _EVS_CONFIG_H
#include <string>

//namespace evs {

using Status = int32_t;

const uint32_t CHARGER_PRIORITY = 90;
const uint32_t COMM_PRIORITY = 60;
const uint32_t CLOCK_PRIORITY = 80;
const uint32_t SERVER_PRIORITY = 70;
const uint32_t NORMAL_PRIORITY = 50;

const std::string LOCOL_IP("127.0.0.1");
const int PORT_LOGER = 50602;

const std::string CAN0 ("can0");
const std::string CAN1 ("can1");
const std::string CAN2 ("can2");
const std::string CAN3 ("can3");

//}

#endif  //_EVS_CONFIG_H