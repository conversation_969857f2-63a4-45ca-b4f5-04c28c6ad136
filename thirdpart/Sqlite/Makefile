# TARGET is the executable file
.PHONY: all sqlite3 compile install clean

PRE_FIX = $(PWD)

SQLITE3_DIR = my_sqlite3

CROSS=/home/<USER>/work/t113/chain_t113/host/bin/arm-linux-gnueabihf-
AR=/home/<USER>/work/t113/chain_t113/host/bin/arm-linux-gnueabihf-ar
STRIP=/home/<USER>/work/t113/chain_t113/host/bin/arm-linux-gnueabihf-strip

#--------------sqlite3-------------------------------------------
ifeq ($(SQLITE3_DIR), $(wildcard $(SQLITE3_DIR))) 
	SQLITE3_SRC_UNZIP := 
else 

SQLITE3_BUILD = cd $(SQLITE3_DIR) \
				&& ./configure --host=arm-linux CC=$(CROSS)gcc \
				--prefix=$(PRE_FIX)/_sqlite3_install \
				&& make && make install && cd - 

SQLITE3_SRC_UNZIP := tar -xzf sqlite-autoconf-3460000.tar.gz \
	&& mv sqlite-autoconf-3460000 $(SQLITE3_DIR) \
	&& $(SQLITE3_BUILD)
endif

INSTALL_DIR = build

all: install 
	
sqlite3:
	$(SQLITE3_SRC_UNZIP)

compile: sqlite3

install: compile
	-mkdir -p $(INSTALL_DIR)/

clean: 
	-rm -rf $(INSTALL_DIR)


