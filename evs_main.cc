/* Copyright 2024, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * This software is provide the control of communication between  Electronic Vehicle and Charge Station.
 * Based on GBT27930-2015+.
 * main source file.
 * Note: Do NOT typing "using namesapce std"
 */
#include <cstdlib>
#include <string>
#include <vector>
#include <iostream>
#include <string>
#include "common/evs_config.h"
#include "common/evs_common.h"
#include "loger.h"
#include "evs_manager.h"

extern bool g_debug_;
extern uint8_t g_evsType;

int main(int32_t argc, const char **argv) {
    int ret = 0;
    
    ret = LogerSocketInit();
    if(-1 == ret){
      Loger(NORMAL,"LCR socker init fail!");
    }
    std::cout << "EVS software Version:"<< "" GIT_VERSION""<< std::endl;
    Loger(NORMAL,"EVS software Version:" GIT_VERSION"");
    EVSManager manager;
    for(int32_t i =1; i< argc; i++) {
      if (!strcmp(argv[i], "debug")) { // debug前台打印
        g_debug_ = true;
        Loger(NORMAL,"EVS debug:");
        continue;
      }
      if(!strcmp(argv[i], "sin")) { // 单板模拟器
        Loger(NORMAL, "EVS SIN");
        g_evsType = 1;
        continue;
      }
    }
    ret = manager.MainProcess();
    if (ret != EVS_SUCCESS) {
        Loger(NORMAL,"Manager MainProcess Failed!");
        return ret;
    }
    LogerSocketExit();
    return EVS_SUCCESS;

    return ret;
}